/**
 * Session Configuration Debug Component
 * Shows current session management settings
 */

'use client';

import React from 'react';

interface SessionConfigProps {
  className?: string;
}

export function SessionConfig({ className = '' }: SessionConfigProps) {
  // Get session configuration
  const cookieMaxAge = process.env.SESSION_COOKIE_MAX_AGE 
    ? parseInt(process.env.SESSION_COOKIE_MAX_AGE) 
    : 60 * 60 * 24 * 7; // Default: 7 days
  
  const refreshThreshold = process.env.TOKEN_REFRESH_THRESHOLD 
    ? parseInt(process.env.TOKEN_REFRESH_THRESHOLD) 
    : 300; // Default: 5 minutes
  
  const autoRefresh = process.env.AUTO_REFRESH_TOKENS !== 'false'; // Default: true

  const formatDuration = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    const parts = [];
    if (days > 0) parts.push(`${days}d`);
    if (hours > 0) parts.push(`${hours}h`);
    if (minutes > 0) parts.push(`${minutes}m`);
    
    return parts.join(' ') || `${seconds}s`;
  };

  return (
    <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 ${className}`}>
      <h3 className="text-sm font-semibold text-gray-700 mb-3">Session Configuration</h3>
      
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">Cookie Duration:</span>
          <span className="font-mono text-gray-900">{formatDuration(cookieMaxAge)}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">Refresh Threshold:</span>
          <span className="font-mono text-gray-900">{formatDuration(refreshThreshold)}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">Auto Refresh:</span>
          <span className={`font-mono ${autoRefresh ? 'text-green-600' : 'text-red-600'}`}>
            {autoRefresh ? 'Enabled' : 'Disabled'}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">Periodic Check:</span>
          <span className="font-mono text-gray-900">5 minutes</span>
        </div>
      </div>
      
      <div className="mt-3 pt-3 border-t border-gray-200">
        <p className="text-xs text-gray-500">
          Tokens are automatically refreshed when they expire within the threshold.
          Cookies persist for the full duration to maintain session state.
        </p>
      </div>
    </div>
  );
}
