686d79de7e4fba87341bb115a044a66f
'use client';

/* istanbul ignore next */
import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\UserContext.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_18s9p7qc7a() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\UserContext.tsx";
  var hash = "15e341f7effed584c4c1600ba737374bf4372c66";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\UserContext.tsx",
    statementMap: {
      "0": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 47,
          column: 2
        }
      },
      "1": {
        start: {
          line: 43,
          column: 33
        },
        end: {
          line: 43,
          column: 38
        }
      },
      "2": {
        start: {
          line: 44,
          column: 37
        },
        end: {
          line: 44,
          column: 42
        }
      },
      "3": {
        start: {
          line: 45,
          column: 24
        },
        end: {
          line: 45,
          column: 30
        }
      },
      "4": {
        start: {
          line: 46,
          column: 17
        },
        end: {
          line: 46,
          column: 22
        }
      },
      "5": {
        start: {
          line: 50,
          column: 26
        },
        end: {
          line: 50,
          column: 53
        }
      },
      "6": {
        start: {
          line: 51,
          column: 36
        },
        end: {
          line: 51,
          column: 51
        }
      },
      "7": {
        start: {
          line: 52,
          column: 40
        },
        end: {
          line: 52,
          column: 49
        }
      },
      "8": {
        start: {
          line: 53,
          column: 21
        },
        end: {
          line: 53,
          column: 32
        }
      },
      "9": {
        start: {
          line: 56,
          column: 2
        },
        end: {
          line: 75,
          column: 41
        }
      },
      "10": {
        start: {
          line: 57,
          column: 4
        },
        end: {
          line: 74,
          column: 5
        }
      },
      "11": {
        start: {
          line: 59,
          column: 6
        },
        end: {
          line: 71,
          column: 8
        }
      },
      "12": {
        start: {
          line: 73,
          column: 6
        },
        end: {
          line: 73,
          column: 19
        }
      },
      "13": {
        start: {
          line: 78,
          column: 2
        },
        end: {
          line: 110,
          column: 16
        }
      },
      "14": {
        start: {
          line: 79,
          column: 4
        },
        end: {
          line: 79,
          column: 25
        }
      },
      "15": {
        start: {
          line: 79,
          column: 19
        },
        end: {
          line: 79,
          column: 25
        }
      },
      "16": {
        start: {
          line: 81,
          column: 32
        },
        end: {
          line: 107,
          column: 5
        }
      },
      "17": {
        start: {
          line: 82,
          column: 6
        },
        end: {
          line: 106,
          column: 7
        }
      },
      "18": {
        start: {
          line: 83,
          column: 8
        },
        end: {
          line: 83,
          column: 26
        }
      },
      "19": {
        start: {
          line: 86,
          column: 28
        },
        end: {
          line: 86,
          column: 73
        }
      },
      "20": {
        start: {
          line: 87,
          column: 8
        },
        end: {
          line: 91,
          column: 9
        }
      },
      "21": {
        start: {
          line: 88,
          column: 30
        },
        end: {
          line: 88,
          column: 53
        }
      },
      "22": {
        start: {
          line: 89,
          column: 10
        },
        end: {
          line: 89,
          column: 78
        }
      },
      "23": {
        start: {
          line: 89,
          column: 26
        },
        end: {
          line: 89,
          column: 77
        }
      },
      "24": {
        start: {
          line: 90,
          column: 10
        },
        end: {
          line: 90,
          column: 16
        }
      },
      "25": {
        start: {
          line: 94,
          column: 25
        },
        end: {
          line: 94,
          column: 73
        }
      },
      "26": {
        start: {
          line: 95,
          column: 8
        },
        end: {
          line: 101,
          column: 9
        }
      },
      "27": {
        start: {
          line: 96,
          column: 30
        },
        end: {
          line: 96,
          column: 51
        }
      },
      "28": {
        start: {
          line: 97,
          column: 10
        },
        end: {
          line: 97,
          column: 65
        }
      },
      "29": {
        start: {
          line: 97,
          column: 26
        },
        end: {
          line: 97,
          column: 64
        }
      },
      "30": {
        start: {
          line: 100,
          column: 10
        },
        end: {
          line: 100,
          column: 84
        }
      },
      "31": {
        start: {
          line: 103,
          column: 8
        },
        end: {
          line: 103,
          column: 63
        }
      },
      "32": {
        start: {
          line: 105,
          column: 8
        },
        end: {
          line: 105,
          column: 27
        }
      },
      "33": {
        start: {
          line: 109,
          column: 4
        },
        end: {
          line: 109,
          column: 25
        }
      },
      "34": {
        start: {
          line: 113,
          column: 28
        },
        end: {
          line: 139,
          column: 3
        }
      },
      "35": {
        start: {
          line: 114,
          column: 4
        },
        end: {
          line: 114,
          column: 31
        }
      },
      "36": {
        start: {
          line: 114,
          column: 19
        },
        end: {
          line: 114,
          column: 31
        }
      },
      "37": {
        start: {
          line: 116,
          column: 4
        },
        end: {
          line: 138,
          column: 5
        }
      },
      "38": {
        start: {
          line: 117,
          column: 6
        },
        end: {
          line: 117,
          column: 24
        }
      },
      "39": {
        start: {
          line: 118,
          column: 23
        },
        end: {
          line: 124,
          column: 8
        }
      },
      "40": {
        start: {
          line: 126,
          column: 6
        },
        end: {
          line: 128,
          column: 7
        }
      },
      "41": {
        start: {
          line: 127,
          column: 8
        },
        end: {
          line: 127,
          column: 51
        }
      },
      "42": {
        start: {
          line: 130,
          column: 26
        },
        end: {
          line: 130,
          column: 47
        }
      },
      "43": {
        start: {
          line: 131,
          column: 6
        },
        end: {
          line: 131,
          column: 64
        }
      },
      "44": {
        start: {
          line: 131,
          column: 22
        },
        end: {
          line: 131,
          column: 63
        }
      },
      "45": {
        start: {
          line: 132,
          column: 6
        },
        end: {
          line: 132,
          column: 17
        }
      },
      "46": {
        start: {
          line: 134,
          column: 6
        },
        end: {
          line: 134,
          column: 58
        }
      },
      "47": {
        start: {
          line: 135,
          column: 6
        },
        end: {
          line: 135,
          column: 18
        }
      },
      "48": {
        start: {
          line: 137,
          column: 6
        },
        end: {
          line: 137,
          column: 25
        }
      },
      "49": {
        start: {
          line: 142,
          column: 32
        },
        end: {
          line: 181,
          column: 3
        }
      },
      "50": {
        start: {
          line: 143,
          column: 4
        },
        end: {
          line: 143,
          column: 31
        }
      },
      "51": {
        start: {
          line: 143,
          column: 19
        },
        end: {
          line: 143,
          column: 31
        }
      },
      "52": {
        start: {
          line: 145,
          column: 4
        },
        end: {
          line: 180,
          column: 5
        }
      },
      "53": {
        start: {
          line: 146,
          column: 6
        },
        end: {
          line: 146,
          column: 24
        }
      },
      "54": {
        start: {
          line: 149,
          column: 33
        },
        end: {
          line: 149,
          column: 72
        }
      },
      "55": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 150,
          column: 81
        }
      },
      "56": {
        start: {
          line: 150,
          column: 22
        },
        end: {
          line: 150,
          column: 80
        }
      },
      "57": {
        start: {
          line: 153,
          column: 6
        },
        end: {
          line: 153,
          column: 87
        }
      },
      "58": {
        start: {
          line: 156,
          column: 23
        },
        end: {
          line: 162,
          column: 8
        }
      },
      "59": {
        start: {
          line: 164,
          column: 6
        },
        end: {
          line: 166,
          column: 7
        }
      },
      "60": {
        start: {
          line: 165,
          column: 8
        },
        end: {
          line: 165,
          column: 55
        }
      },
      "61": {
        start: {
          line: 168,
          column: 6
        },
        end: {
          line: 168,
          column: 17
        }
      },
      "62": {
        start: {
          line: 170,
          column: 6
        },
        end: {
          line: 170,
          column: 62
        }
      },
      "63": {
        start: {
          line: 172,
          column: 26
        },
        end: {
          line: 172,
          column: 71
        }
      },
      "64": {
        start: {
          line: 173,
          column: 6
        },
        end: {
          line: 176,
          column: 7
        }
      },
      "65": {
        start: {
          line: 174,
          column: 28
        },
        end: {
          line: 174,
          column: 51
        }
      },
      "66": {
        start: {
          line: 175,
          column: 8
        },
        end: {
          line: 175,
          column: 76
        }
      },
      "67": {
        start: {
          line: 175,
          column: 24
        },
        end: {
          line: 175,
          column: 75
        }
      },
      "68": {
        start: {
          line: 177,
          column: 6
        },
        end: {
          line: 177,
          column: 18
        }
      },
      "69": {
        start: {
          line: 179,
          column: 6
        },
        end: {
          line: 179,
          column: 25
        }
      },
      "70": {
        start: {
          line: 184,
          column: 25
        },
        end: {
          line: 205,
          column: 3
        }
      },
      "71": {
        start: {
          line: 185,
          column: 4
        },
        end: {
          line: 185,
          column: 28
        }
      },
      "72": {
        start: {
          line: 185,
          column: 15
        },
        end: {
          line: 185,
          column: 28
        }
      },
      "73": {
        start: {
          line: 187,
          column: 4
        },
        end: {
          line: 189,
          column: 5
        }
      },
      "74": {
        start: {
          line: 188,
          column: 6
        },
        end: {
          line: 188,
          column: 53
        }
      },
      "75": {
        start: {
          line: 191,
          column: 4
        },
        end: {
          line: 193,
          column: 5
        }
      },
      "76": {
        start: {
          line: 192,
          column: 6
        },
        end: {
          line: 192,
          column: 22
        }
      },
      "77": {
        start: {
          line: 196,
          column: 19
        },
        end: {
          line: 198,
          column: 5
        }
      },
      "78": {
        start: {
          line: 197,
          column: 6
        },
        end: {
          line: 197,
          column: 88
        }
      },
      "79": {
        start: {
          line: 200,
          column: 4
        },
        end: {
          line: 202,
          column: 5
        }
      },
      "80": {
        start: {
          line: 201,
          column: 6
        },
        end: {
          line: 201,
          column: 37
        }
      },
      "81": {
        start: {
          line: 204,
          column: 4
        },
        end: {
          line: 204,
          column: 44
        }
      },
      "82": {
        start: {
          line: 208,
          column: 18
        },
        end: {
          line: 210,
          column: 3
        }
      },
      "83": {
        start: {
          line: 209,
          column: 4
        },
        end: {
          line: 209,
          column: 47
        }
      },
      "84": {
        start: {
          line: 212,
          column: 2
        },
        end: {
          line: 225,
          column: 3
        }
      },
      "85": {
        start: {
          line: 229,
          column: 23
        },
        end: {
          line: 229,
          column: 52
        }
      },
      "86": {
        start: {
          line: 229,
          column: 29
        },
        end: {
          line: 229,
          column: 52
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 43,
            column: 21
          },
          end: {
            line: 43,
            column: 22
          }
        },
        loc: {
          start: {
            line: 43,
            column: 33
          },
          end: {
            line: 43,
            column: 38
          }
        },
        line: 43
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 44,
            column: 25
          },
          end: {
            line: 44,
            column: 26
          }
        },
        loc: {
          start: {
            line: 44,
            column: 37
          },
          end: {
            line: 44,
            column: 42
          }
        },
        line: 44
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 45,
            column: 18
          },
          end: {
            line: 45,
            column: 19
          }
        },
        loc: {
          start: {
            line: 45,
            column: 24
          },
          end: {
            line: 45,
            column: 30
          }
        },
        line: 45
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 46,
            column: 11
          },
          end: {
            line: 46,
            column: 12
          }
        },
        loc: {
          start: {
            line: 46,
            column: 17
          },
          end: {
            line: 46,
            column: 22
          }
        },
        line: 46
      },
      "4": {
        name: "UserProvider",
        decl: {
          start: {
            line: 49,
            column: 16
          },
          end: {
            line: 49,
            column: 28
          }
        },
        loc: {
          start: {
            line: 49,
            column: 68
          },
          end: {
            line: 226,
            column: 1
          }
        },
        line: 49
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 56,
            column: 12
          },
          end: {
            line: 56,
            column: 13
          }
        },
        loc: {
          start: {
            line: 56,
            column: 18
          },
          end: {
            line: 75,
            column: 3
          }
        },
        line: 56
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 78,
            column: 12
          },
          end: {
            line: 78,
            column: 13
          }
        },
        loc: {
          start: {
            line: 78,
            column: 18
          },
          end: {
            line: 110,
            column: 3
          }
        },
        line: 78
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 81,
            column: 32
          },
          end: {
            line: 81,
            column: 33
          }
        },
        loc: {
          start: {
            line: 81,
            column: 44
          },
          end: {
            line: 107,
            column: 5
          }
        },
        line: 81
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 89,
            column: 18
          },
          end: {
            line: 89,
            column: 19
          }
        },
        loc: {
          start: {
            line: 89,
            column: 26
          },
          end: {
            line: 89,
            column: 77
          }
        },
        line: 89
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 97,
            column: 18
          },
          end: {
            line: 97,
            column: 19
          }
        },
        loc: {
          start: {
            line: 97,
            column: 26
          },
          end: {
            line: 97,
            column: 64
          }
        },
        line: 97
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 113,
            column: 28
          },
          end: {
            line: 113,
            column: 29
          }
        },
        loc: {
          start: {
            line: 113,
            column: 77
          },
          end: {
            line: 139,
            column: 3
          }
        },
        line: 113
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 131,
            column: 14
          },
          end: {
            line: 131,
            column: 15
          }
        },
        loc: {
          start: {
            line: 131,
            column: 22
          },
          end: {
            line: 131,
            column: 63
          }
        },
        line: 131
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 142,
            column: 32
          },
          end: {
            line: 142,
            column: 33
          }
        },
        loc: {
          start: {
            line: 142,
            column: 99
          },
          end: {
            line: 181,
            column: 3
          }
        },
        line: 142
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 150,
            column: 14
          },
          end: {
            line: 150,
            column: 15
          }
        },
        loc: {
          start: {
            line: 150,
            column: 22
          },
          end: {
            line: 150,
            column: 80
          }
        },
        line: 150
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 175,
            column: 16
          },
          end: {
            line: 175,
            column: 17
          }
        },
        loc: {
          start: {
            line: 175,
            column: 24
          },
          end: {
            line: 175,
            column: 75
          }
        },
        line: 175
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 184,
            column: 25
          },
          end: {
            line: 184,
            column: 26
          }
        },
        loc: {
          start: {
            line: 184,
            column: 39
          },
          end: {
            line: 205,
            column: 3
          }
        },
        line: 184
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 196,
            column: 19
          },
          end: {
            line: 196,
            column: 20
          }
        },
        loc: {
          start: {
            line: 196,
            column: 36
          },
          end: {
            line: 198,
            column: 5
          }
        },
        line: 196
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 208,
            column: 18
          },
          end: {
            line: 208,
            column: 19
          }
        },
        loc: {
          start: {
            line: 208,
            column: 45
          },
          end: {
            line: 210,
            column: 3
          }
        },
        line: 208
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 229,
            column: 23
          },
          end: {
            line: 229,
            column: 24
          }
        },
        loc: {
          start: {
            line: 229,
            column: 29
          },
          end: {
            line: 229,
            column: 52
          }
        },
        line: 229
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 57,
            column: 4
          },
          end: {
            line: 74,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 57,
            column: 4
          },
          end: {
            line: 74,
            column: 5
          }
        }, {
          start: {
            line: 72,
            column: 11
          },
          end: {
            line: 74,
            column: 5
          }
        }],
        line: 57
      },
      "1": {
        loc: {
          start: {
            line: 57,
            column: 8
          },
          end: {
            line: 57,
            column: 35
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 57,
            column: 8
          },
          end: {
            line: 57,
            column: 23
          }
        }, {
          start: {
            line: 57,
            column: 27
          },
          end: {
            line: 57,
            column: 35
          }
        }],
        line: 57
      },
      "2": {
        loc: {
          start: {
            line: 61,
            column: 18
          },
          end: {
            line: 61,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 61,
            column: 18
          },
          end: {
            line: 61,
            column: 35
          }
        }, {
          start: {
            line: 61,
            column: 39
          },
          end: {
            line: 61,
            column: 49
          }
        }],
        line: 61
      },
      "3": {
        loc: {
          start: {
            line: 62,
            column: 21
          },
          end: {
            line: 70,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 62,
            column: 21
          },
          end: {
            line: 62,
            column: 38
          }
        }, {
          start: {
            line: 62,
            column: 42
          },
          end: {
            line: 70,
            column: 9
          }
        }],
        line: 62
      },
      "4": {
        loc: {
          start: {
            line: 79,
            column: 4
          },
          end: {
            line: 79,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 79,
            column: 4
          },
          end: {
            line: 79,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 79
      },
      "5": {
        loc: {
          start: {
            line: 87,
            column: 8
          },
          end: {
            line: 91,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 87,
            column: 8
          },
          end: {
            line: 91,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 87
      },
      "6": {
        loc: {
          start: {
            line: 89,
            column: 26
          },
          end: {
            line: 89,
            column: 77
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 89,
            column: 33
          },
          end: {
            line: 89,
            column: 70
          }
        }, {
          start: {
            line: 89,
            column: 73
          },
          end: {
            line: 89,
            column: 77
          }
        }],
        line: 89
      },
      "7": {
        loc: {
          start: {
            line: 95,
            column: 8
          },
          end: {
            line: 101,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 95,
            column: 8
          },
          end: {
            line: 101,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 95
      },
      "8": {
        loc: {
          start: {
            line: 97,
            column: 26
          },
          end: {
            line: 97,
            column: 64
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 97,
            column: 33
          },
          end: {
            line: 97,
            column: 57
          }
        }, {
          start: {
            line: 97,
            column: 60
          },
          end: {
            line: 97,
            column: 64
          }
        }],
        line: 97
      },
      "9": {
        loc: {
          start: {
            line: 114,
            column: 4
          },
          end: {
            line: 114,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 114,
            column: 4
          },
          end: {
            line: 114,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 114
      },
      "10": {
        loc: {
          start: {
            line: 126,
            column: 6
          },
          end: {
            line: 128,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 126,
            column: 6
          },
          end: {
            line: 128,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 126
      },
      "11": {
        loc: {
          start: {
            line: 131,
            column: 22
          },
          end: {
            line: 131,
            column: 63
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 131,
            column: 29
          },
          end: {
            line: 131,
            column: 56
          }
        }, {
          start: {
            line: 131,
            column: 59
          },
          end: {
            line: 131,
            column: 63
          }
        }],
        line: 131
      },
      "12": {
        loc: {
          start: {
            line: 143,
            column: 4
          },
          end: {
            line: 143,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 143,
            column: 4
          },
          end: {
            line: 143,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 143
      },
      "13": {
        loc: {
          start: {
            line: 150,
            column: 22
          },
          end: {
            line: 150,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 150,
            column: 29
          },
          end: {
            line: 150,
            column: 73
          }
        }, {
          start: {
            line: 150,
            column: 76
          },
          end: {
            line: 150,
            column: 80
          }
        }],
        line: 150
      },
      "14": {
        loc: {
          start: {
            line: 164,
            column: 6
          },
          end: {
            line: 166,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 164,
            column: 6
          },
          end: {
            line: 166,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 164
      },
      "15": {
        loc: {
          start: {
            line: 173,
            column: 6
          },
          end: {
            line: 176,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 173,
            column: 6
          },
          end: {
            line: 176,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 173
      },
      "16": {
        loc: {
          start: {
            line: 175,
            column: 24
          },
          end: {
            line: 175,
            column: 75
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 175,
            column: 31
          },
          end: {
            line: 175,
            column: 68
          }
        }, {
          start: {
            line: 175,
            column: 71
          },
          end: {
            line: 175,
            column: 75
          }
        }],
        line: 175
      },
      "17": {
        loc: {
          start: {
            line: 185,
            column: 4
          },
          end: {
            line: 185,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 185,
            column: 4
          },
          end: {
            line: 185,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 185
      },
      "18": {
        loc: {
          start: {
            line: 187,
            column: 4
          },
          end: {
            line: 189,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 187,
            column: 4
          },
          end: {
            line: 189,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 187
      },
      "19": {
        loc: {
          start: {
            line: 187,
            column: 8
          },
          end: {
            line: 187,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 187,
            column: 8
          },
          end: {
            line: 187,
            column: 23
          }
        }, {
          start: {
            line: 187,
            column: 27
          },
          end: {
            line: 187,
            column: 43
          }
        }],
        line: 187
      },
      "20": {
        loc: {
          start: {
            line: 191,
            column: 4
          },
          end: {
            line: 193,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 191,
            column: 4
          },
          end: {
            line: 193,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 191
      },
      "21": {
        loc: {
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 202,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 202,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 200
      },
      "22": {
        loc: {
          start: {
            line: 200,
            column: 8
          },
          end: {
            line: 200,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 200,
            column: 8
          },
          end: {
            line: 200,
            column: 18
          }
        }, {
          start: {
            line: 200,
            column: 22
          },
          end: {
            line: 200,
            column: 55
          }
        }],
        line: 200
      },
      "23": {
        loc: {
          start: {
            line: 209,
            column: 11
          },
          end: {
            line: 209,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 209,
            column: 11
          },
          end: {
            line: 209,
            column: 38
          }
        }, {
          start: {
            line: 209,
            column: 42
          },
          end: {
            line: 209,
            column: 47
          }
        }],
        line: 209
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "15e341f7effed584c4c1600ba737374bf4372c66"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_18s9p7qc7a = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_18s9p7qc7a();
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
import { createContext, useState, useEffect, useContext } from 'react';
import { useAuth } from './AuthContext';
import { useClient } from './ClientContext';
// Create context with default values
const UserContext =
/* istanbul ignore next */
(cov_18s9p7qc7a().s[0]++, /*#__PURE__*/createContext({
  user: null,
  isLoading: false,
  updateUserProfile: async () => {
    /* istanbul ignore next */
    cov_18s9p7qc7a().f[0]++;
    cov_18s9p7qc7a().s[1]++;
    return false;
  },
  updateUserPreferences: async () => {
    /* istanbul ignore next */
    cov_18s9p7qc7a().f[1]++;
    cov_18s9p7qc7a().s[2]++;
    return false;
  },
  getDisplayName: () => {
    /* istanbul ignore next */
    cov_18s9p7qc7a().f[2]++;
    cov_18s9p7qc7a().s[3]++;
    return 'User';
  },
  hasRole: () => {
    /* istanbul ignore next */
    cov_18s9p7qc7a().f[3]++;
    cov_18s9p7qc7a().s[4]++;
    return false;
  }
}));
export function UserProvider({
  children
}) {
  /* istanbul ignore next */
  cov_18s9p7qc7a().f[4]++;
  const [user, setUser] =
  /* istanbul ignore next */
  (cov_18s9p7qc7a().s[5]++, useState(null));
  const [isLoading, setIsLoading] =
  /* istanbul ignore next */
  (cov_18s9p7qc7a().s[6]++, useState(false));
  const {
    isAuthenticated,
    authUser
  } =
  /* istanbul ignore next */
  (cov_18s9p7qc7a().s[7]++, useAuth());
  const {
    client
  } =
  /* istanbul ignore next */
  (cov_18s9p7qc7a().s[8]++, useClient());

  // Update user when auth state changes
  /* istanbul ignore next */
  cov_18s9p7qc7a().s[9]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_18s9p7qc7a().f[5]++;
    cov_18s9p7qc7a().s[10]++;
    if (
    /* istanbul ignore next */
    (cov_18s9p7qc7a().b[1][0]++, isAuthenticated) &&
    /* istanbul ignore next */
    (cov_18s9p7qc7a().b[1][1]++, authUser)) {
      /* istanbul ignore next */
      cov_18s9p7qc7a().b[0][0]++;
      cov_18s9p7qc7a().s[11]++;
      // Initialize user from auth data
      setUser(
      /* istanbul ignore next */
      _objectSpread(_objectSpread({}, authUser), {}, {
        clientId:
        /* istanbul ignore next */
        (cov_18s9p7qc7a().b[2][0]++, authUser.clientId) ||
        /* istanbul ignore next */
        (cov_18s9p7qc7a().b[2][1]++, client?.id),
        preferences:
        /* istanbul ignore next */
        (cov_18s9p7qc7a().b[3][0]++, user?.preferences) ||
        /* istanbul ignore next */
        (cov_18s9p7qc7a().b[3][1]++, {
          theme: 'system',
          notifications: {
            email: true,
            push: true,
            sms: false
          },
          displayDensity: 'comfortable'
        })
      }));
    } else {
      /* istanbul ignore next */
      cov_18s9p7qc7a().b[0][1]++;
      cov_18s9p7qc7a().s[12]++;
      setUser(null);
    }
  }, [isAuthenticated, authUser, client]);

  // Load user preferences from API or localStorage
  /* istanbul ignore next */
  cov_18s9p7qc7a().s[13]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_18s9p7qc7a().f[6]++;
    cov_18s9p7qc7a().s[14]++;
    if (!user?.id) {
      /* istanbul ignore next */
      cov_18s9p7qc7a().b[4][0]++;
      cov_18s9p7qc7a().s[15]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_18s9p7qc7a().b[4][1]++;
    }
    cov_18s9p7qc7a().s[16]++;
    const loadUserPreferences = async () => {
      /* istanbul ignore next */
      cov_18s9p7qc7a().f[7]++;
      cov_18s9p7qc7a().s[17]++;
      try {
        /* istanbul ignore next */
        cov_18s9p7qc7a().s[18]++;
        setIsLoading(true);

        // Try to load from localStorage first (for demo/development)
        const storedPrefs =
        /* istanbul ignore next */
        (cov_18s9p7qc7a().s[19]++, localStorage.getItem(`user_prefs_${user.id}`));
        /* istanbul ignore next */
        cov_18s9p7qc7a().s[20]++;
        if (storedPrefs) {
          /* istanbul ignore next */
          cov_18s9p7qc7a().b[5][0]++;
          const parsedPrefs =
          /* istanbul ignore next */
          (cov_18s9p7qc7a().s[21]++, JSON.parse(storedPrefs));
          /* istanbul ignore next */
          cov_18s9p7qc7a().s[22]++;
          setUser(prev => {
            /* istanbul ignore next */
            cov_18s9p7qc7a().f[8]++;
            cov_18s9p7qc7a().s[23]++;
            return prev ?
            /* istanbul ignore next */
            (cov_18s9p7qc7a().b[6][0]++, _objectSpread(_objectSpread({}, prev), {}, {
              preferences: parsedPrefs
            })) :
            /* istanbul ignore next */
            (cov_18s9p7qc7a().b[6][1]++, null);
          });
          /* istanbul ignore next */
          cov_18s9p7qc7a().s[24]++;
          return;
        } else
        /* istanbul ignore next */
        {
          cov_18s9p7qc7a().b[5][1]++;
        }

        // If not in localStorage, fetch from API
        const response =
        /* istanbul ignore next */
        (cov_18s9p7qc7a().s[25]++, await fetch(`/api/users/${user.id}/preferences`));
        /* istanbul ignore next */
        cov_18s9p7qc7a().s[26]++;
        if (response.ok) {
          /* istanbul ignore next */
          cov_18s9p7qc7a().b[7][0]++;
          const preferences =
          /* istanbul ignore next */
          (cov_18s9p7qc7a().s[27]++, await response.json());
          /* istanbul ignore next */
          cov_18s9p7qc7a().s[28]++;
          setUser(prev => {
            /* istanbul ignore next */
            cov_18s9p7qc7a().f[9]++;
            cov_18s9p7qc7a().s[29]++;
            return prev ?
            /* istanbul ignore next */
            (cov_18s9p7qc7a().b[8][0]++, _objectSpread(_objectSpread({}, prev), {}, {
              preferences
            })) :
            /* istanbul ignore next */
            (cov_18s9p7qc7a().b[8][1]++, null);
          });

          // Cache in localStorage
          /* istanbul ignore next */
          cov_18s9p7qc7a().s[30]++;
          localStorage.setItem(`user_prefs_${user.id}`, JSON.stringify(preferences));
        } else
        /* istanbul ignore next */
        {
          cov_18s9p7qc7a().b[7][1]++;
        }
      } catch (error) {
        /* istanbul ignore next */
        cov_18s9p7qc7a().s[31]++;
        console.error('Error loading user preferences:', error);
      } finally {
        /* istanbul ignore next */
        cov_18s9p7qc7a().s[32]++;
        setIsLoading(false);
      }
    };
    /* istanbul ignore next */
    cov_18s9p7qc7a().s[33]++;
    loadUserPreferences();
  }, [user?.id]);

  // Update user profile
  /* istanbul ignore next */
  cov_18s9p7qc7a().s[34]++;
  const updateUserProfile = async data => {
    /* istanbul ignore next */
    cov_18s9p7qc7a().f[10]++;
    cov_18s9p7qc7a().s[35]++;
    if (!user?.id) {
      /* istanbul ignore next */
      cov_18s9p7qc7a().b[9][0]++;
      cov_18s9p7qc7a().s[36]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_18s9p7qc7a().b[9][1]++;
    }
    cov_18s9p7qc7a().s[37]++;
    try {
      /* istanbul ignore next */
      cov_18s9p7qc7a().s[38]++;
      setIsLoading(true);
      const response =
      /* istanbul ignore next */
      (cov_18s9p7qc7a().s[39]++, await fetch(`/api/users/${user.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      }));
      /* istanbul ignore next */
      cov_18s9p7qc7a().s[40]++;
      if (!response.ok) {
        /* istanbul ignore next */
        cov_18s9p7qc7a().b[10][0]++;
        cov_18s9p7qc7a().s[41]++;
        throw new Error('Failed to update profile');
      } else
      /* istanbul ignore next */
      {
        cov_18s9p7qc7a().b[10][1]++;
      }
      const updatedUser =
      /* istanbul ignore next */
      (cov_18s9p7qc7a().s[42]++, await response.json());
      /* istanbul ignore next */
      cov_18s9p7qc7a().s[43]++;
      setUser(prev => {
        /* istanbul ignore next */
        cov_18s9p7qc7a().f[11]++;
        cov_18s9p7qc7a().s[44]++;
        return prev ?
        /* istanbul ignore next */
        (cov_18s9p7qc7a().b[11][0]++, _objectSpread(_objectSpread({}, prev), updatedUser)) :
        /* istanbul ignore next */
        (cov_18s9p7qc7a().b[11][1]++, null);
      });
      /* istanbul ignore next */
      cov_18s9p7qc7a().s[45]++;
      return true;
    } catch (error) {
      /* istanbul ignore next */
      cov_18s9p7qc7a().s[46]++;
      console.error('Error updating user profile:', error);
      /* istanbul ignore next */
      cov_18s9p7qc7a().s[47]++;
      return false;
    } finally {
      /* istanbul ignore next */
      cov_18s9p7qc7a().s[48]++;
      setIsLoading(false);
    }
  };

  // Update user preferences
  /* istanbul ignore next */
  cov_18s9p7qc7a().s[49]++;
  const updateUserPreferences = async preferences => {
    /* istanbul ignore next */
    cov_18s9p7qc7a().f[12]++;
    cov_18s9p7qc7a().s[50]++;
    if (!user?.id) {
      /* istanbul ignore next */
      cov_18s9p7qc7a().b[12][0]++;
      cov_18s9p7qc7a().s[51]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_18s9p7qc7a().b[12][1]++;
    }
    cov_18s9p7qc7a().s[52]++;
    try {
      /* istanbul ignore next */
      cov_18s9p7qc7a().s[53]++;
      setIsLoading(true);

      // Update in state first for immediate feedback
      const updatedPreferences =
      /* istanbul ignore next */
      (cov_18s9p7qc7a().s[54]++, _objectSpread(_objectSpread({}, user.preferences), preferences));
      /* istanbul ignore next */
      cov_18s9p7qc7a().s[55]++;
      setUser(prev => {
        /* istanbul ignore next */
        cov_18s9p7qc7a().f[13]++;
        cov_18s9p7qc7a().s[56]++;
        return prev ?
        /* istanbul ignore next */
        (cov_18s9p7qc7a().b[13][0]++, _objectSpread(_objectSpread({}, prev), {}, {
          preferences: updatedPreferences
        })) :
        /* istanbul ignore next */
        (cov_18s9p7qc7a().b[13][1]++, null);
      });

      // Cache in localStorage
      /* istanbul ignore next */
      cov_18s9p7qc7a().s[57]++;
      localStorage.setItem(`user_prefs_${user.id}`, JSON.stringify(updatedPreferences));

      // Then update on server
      const response =
      /* istanbul ignore next */
      (cov_18s9p7qc7a().s[58]++, await fetch(`/api/users/${user.id}/preferences`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(preferences)
      }));
      /* istanbul ignore next */
      cov_18s9p7qc7a().s[59]++;
      if (!response.ok) {
        /* istanbul ignore next */
        cov_18s9p7qc7a().b[14][0]++;
        cov_18s9p7qc7a().s[60]++;
        throw new Error('Failed to update preferences');
      } else
      /* istanbul ignore next */
      {
        cov_18s9p7qc7a().b[14][1]++;
      }
      cov_18s9p7qc7a().s[61]++;
      return true;
    } catch (error) {
      /* istanbul ignore next */
      cov_18s9p7qc7a().s[62]++;
      console.error('Error updating user preferences:', error);
      // Revert to previous state on error
      const storedPrefs =
      /* istanbul ignore next */
      (cov_18s9p7qc7a().s[63]++, localStorage.getItem(`user_prefs_${user.id}`));
      /* istanbul ignore next */
      cov_18s9p7qc7a().s[64]++;
      if (storedPrefs) {
        /* istanbul ignore next */
        cov_18s9p7qc7a().b[15][0]++;
        const parsedPrefs =
        /* istanbul ignore next */
        (cov_18s9p7qc7a().s[65]++, JSON.parse(storedPrefs));
        /* istanbul ignore next */
        cov_18s9p7qc7a().s[66]++;
        setUser(prev => {
          /* istanbul ignore next */
          cov_18s9p7qc7a().f[14]++;
          cov_18s9p7qc7a().s[67]++;
          return prev ?
          /* istanbul ignore next */
          (cov_18s9p7qc7a().b[16][0]++, _objectSpread(_objectSpread({}, prev), {}, {
            preferences: parsedPrefs
          })) :
          /* istanbul ignore next */
          (cov_18s9p7qc7a().b[16][1]++, null);
        });
      } else
      /* istanbul ignore next */
      {
        cov_18s9p7qc7a().b[15][1]++;
      }
      cov_18s9p7qc7a().s[68]++;
      return false;
    } finally {
      /* istanbul ignore next */
      cov_18s9p7qc7a().s[69]++;
      setIsLoading(false);
    }
  };

  // Get formatted display name
  /* istanbul ignore next */
  cov_18s9p7qc7a().s[70]++;
  const getDisplayName = () => {
    /* istanbul ignore next */
    cov_18s9p7qc7a().f[15]++;
    cov_18s9p7qc7a().s[71]++;
    if (!user) {
      /* istanbul ignore next */
      cov_18s9p7qc7a().b[17][0]++;
      cov_18s9p7qc7a().s[72]++;
      return 'User';
    } else
    /* istanbul ignore next */
    {
      cov_18s9p7qc7a().b[17][1]++;
    }
    cov_18s9p7qc7a().s[73]++;
    if (
    /* istanbul ignore next */
    (cov_18s9p7qc7a().b[19][0]++, user.given_name) &&
    /* istanbul ignore next */
    (cov_18s9p7qc7a().b[19][1]++, user.family_name)) {
      /* istanbul ignore next */
      cov_18s9p7qc7a().b[18][0]++;
      cov_18s9p7qc7a().s[74]++;
      return `${user.given_name} ${user.family_name}`;
    } else
    /* istanbul ignore next */
    {
      cov_18s9p7qc7a().b[18][1]++;
    }
    cov_18s9p7qc7a().s[75]++;
    if (user.name) {
      /* istanbul ignore next */
      cov_18s9p7qc7a().b[20][0]++;
      cov_18s9p7qc7a().s[76]++;
      return user.name;
    } else
    /* istanbul ignore next */
    {
      cov_18s9p7qc7a().b[20][1]++;
    }

    // Check if email looks like a UUID or system-generated value
    cov_18s9p7qc7a().s[77]++;
    const isUUID = str => {
      /* istanbul ignore next */
      cov_18s9p7qc7a().f[16]++;
      cov_18s9p7qc7a().s[78]++;
      return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(str);
    };
    /* istanbul ignore next */
    cov_18s9p7qc7a().s[79]++;
    if (
    /* istanbul ignore next */
    (cov_18s9p7qc7a().b[22][0]++, user.email) &&
    /* istanbul ignore next */
    (cov_18s9p7qc7a().b[22][1]++, !isUUID(user.email.split('@')[0]))) {
      /* istanbul ignore next */
      cov_18s9p7qc7a().b[21][0]++;
      cov_18s9p7qc7a().s[80]++;
      return user.email.split('@')[0];
    } else
    /* istanbul ignore next */
    {
      cov_18s9p7qc7a().b[21][1]++;
    }
    cov_18s9p7qc7a().s[81]++;
    return `User ${user.id.substring(0, 8)}`;
  };

  // Role checking utility
  /* istanbul ignore next */
  cov_18s9p7qc7a().s[82]++;
  const hasRole = role => {
    /* istanbul ignore next */
    cov_18s9p7qc7a().f[17]++;
    cov_18s9p7qc7a().s[83]++;
    return /* istanbul ignore next */(cov_18s9p7qc7a().b[23][0]++, user?.roles?.includes(role)) ||
    /* istanbul ignore next */
    (cov_18s9p7qc7a().b[23][1]++, false);
  };
  /* istanbul ignore next */
  cov_18s9p7qc7a().s[84]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  UserContext.Provider,
  /* istanbul ignore next */
  {
    value: {
      user,
      isLoading,
      updateUserProfile,
      updateUserPreferences,
      getDisplayName,
      hasRole
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 213,
      columnNumber: 5
    }
  }, children);
}

// Custom hook for using user context
/* istanbul ignore next */
cov_18s9p7qc7a().s[85]++;
export const useUser = () => {
  /* istanbul ignore next */
  cov_18s9p7qc7a().f[18]++;
  cov_18s9p7qc7a().s[86]++;
  return useContext(UserContext);
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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