8cc00003e05f1ea4581cc3162b91a87e
/**
 * Dashboard Data Hook
 * 
 * Custom hook for managing dashboard data fetching, caching, and state management.
 * Focused responsibility: Data layer for dashboard components.
 */

'use client';

/* istanbul ignore next */
import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
function cov_120psm3w3w() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\hooks\\useDashboardData.ts";
  var hash = "28d28ed66791866b1bea51849975f5ccf854c11c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\hooks\\useDashboardData.ts",
    statementMap: {
      "0": {
        start: {
          line: 38,
          column: 37
        },
        end: {
          line: 43,
          column: 1
        }
      },
      "1": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 37
        }
      },
      "2": {
        start: {
          line: 48,
          column: 18
        },
        end: {
          line: 48,
          column: 31
        }
      },
      "3": {
        start: {
          line: 49,
          column: 19
        },
        end: {
          line: 52,
          column: 10
        }
      },
      "4": {
        start: {
          line: 56,
          column: 38
        },
        end: {
          line: 56,
          column: 45
        }
      },
      "5": {
        start: {
          line: 58,
          column: 21
        },
        end: {
          line: 58,
          column: 42
        }
      },
      "6": {
        start: {
          line: 59,
          column: 20
        },
        end: {
          line: 59,
          column: 65
        }
      },
      "7": {
        start: {
          line: 59,
          column: 37
        },
        end: {
          line: 59,
          column: 55
        }
      },
      "8": {
        start: {
          line: 61,
          column: 2
        },
        end: {
          line: 74,
          column: 3
        }
      },
      "9": {
        start: {
          line: 62,
          column: 21
        },
        end: {
          line: 67,
          column: 6
        }
      },
      "10": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 69,
          column: 27
        }
      },
      "11": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 70,
          column: 19
        }
      },
      "12": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 72,
          column: 27
        }
      },
      "13": {
        start: {
          line: 73,
          column: 4
        },
        end: {
          line: 73,
          column: 15
        }
      },
      "14": {
        start: {
          line: 78,
          column: 65
        },
        end: {
          line: 78,
          column: 76
        }
      },
      "15": {
        start: {
          line: 80,
          column: 26
        },
        end: {
          line: 84,
          column: 4
        }
      },
      "16": {
        start: {
          line: 86,
          column: 36
        },
        end: {
          line: 86,
          column: 51
        }
      },
      "17": {
        start: {
          line: 87,
          column: 28
        },
        end: {
          line: 87,
          column: 57
        }
      },
      "18": {
        start: {
          line: 90,
          column: 23
        },
        end: {
          line: 90,
          column: 35
        }
      },
      "19": {
        start: {
          line: 91,
          column: 29
        },
        end: {
          line: 91,
          column: 65
        }
      },
      "20": {
        start: {
          line: 94,
          column: 26
        },
        end: {
          line: 94,
          column: 50
        }
      },
      "21": {
        start: {
          line: 97,
          column: 20
        },
        end: {
          line: 103,
          column: 33
        }
      },
      "22": {
        start: {
          line: 98,
          column: 4
        },
        end: {
          line: 98,
          column: 37
        }
      },
      "23": {
        start: {
          line: 98,
          column: 26
        },
        end: {
          line: 98,
          column: 37
        }
      },
      "24": {
        start: {
          line: 99,
          column: 4
        },
        end: {
          line: 102,
          column: 5
        }
      },
      "25": {
        start: {
          line: 106,
          column: 21
        },
        end: {
          line: 138,
          column: 14
        }
      },
      "26": {
        start: {
          line: 107,
          column: 4
        },
        end: {
          line: 107,
          column: 56
        }
      },
      "27": {
        start: {
          line: 107,
          column: 17
        },
        end: {
          line: 107,
          column: 56
        }
      },
      "28": {
        start: {
          line: 109,
          column: 4
        },
        end: {
          line: 109,
          column: 39
        }
      },
      "29": {
        start: {
          line: 111,
          column: 21
        },
        end: {
          line: 111,
          column: 77
        }
      },
      "30": {
        start: {
          line: 112,
          column: 19
        },
        end: {
          line: 112,
          column: 41
        }
      },
      "31": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 116,
          column: 5
        }
      },
      "32": {
        start: {
          line: 114,
          column: 6
        },
        end: {
          line: 114,
          column: 44
        }
      },
      "33": {
        start: {
          line: 115,
          column: 6
        },
        end: {
          line: 115,
          column: 19
        }
      },
      "34": {
        start: {
          line: 118,
          column: 21
        },
        end: {
          line: 118,
          column: 76
        }
      },
      "35": {
        start: {
          line: 120,
          column: 4
        },
        end: {
          line: 122,
          column: 5
        }
      },
      "36": {
        start: {
          line: 121,
          column: 6
        },
        end: {
          line: 121,
          column: 89
        }
      },
      "37": {
        start: {
          line: 124,
          column: 19
        },
        end: {
          line: 124,
          column: 40
        }
      },
      "38": {
        start: {
          line: 126,
          column: 4
        },
        end: {
          line: 137,
          column: 5
        }
      },
      "39": {
        start: {
          line: 127,
          column: 6
        },
        end: {
          line: 127,
          column: 89
        }
      },
      "40": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 44
        }
      },
      "41": {
        start: {
          line: 129,
          column: 6
        },
        end: {
          line: 129,
          column: 24
        }
      },
      "42": {
        start: {
          line: 130,
          column: 11
        },
        end: {
          line: 137,
          column: 5
        }
      },
      "43": {
        start: {
          line: 131,
          column: 6
        },
        end: {
          line: 131,
          column: 35
        }
      },
      "44": {
        start: {
          line: 134,
          column: 6
        },
        end: {
          line: 134,
          column: 84
        }
      },
      "45": {
        start: {
          line: 135,
          column: 6
        },
        end: {
          line: 135,
          column: 44
        }
      },
      "46": {
        start: {
          line: 136,
          column: 6
        },
        end: {
          line: 136,
          column: 19
        }
      },
      "47": {
        start: {
          line: 141,
          column: 24
        },
        end: {
          line: 173,
          column: 14
        }
      },
      "48": {
        start: {
          line: 142,
          column: 4
        },
        end: {
          line: 142,
          column: 56
        }
      },
      "49": {
        start: {
          line: 142,
          column: 17
        },
        end: {
          line: 142,
          column: 56
        }
      },
      "50": {
        start: {
          line: 144,
          column: 4
        },
        end: {
          line: 144,
          column: 42
        }
      },
      "51": {
        start: {
          line: 146,
          column: 21
        },
        end: {
          line: 146,
          column: 80
        }
      },
      "52": {
        start: {
          line: 147,
          column: 19
        },
        end: {
          line: 147,
          column: 41
        }
      },
      "53": {
        start: {
          line: 148,
          column: 4
        },
        end: {
          line: 151,
          column: 5
        }
      },
      "54": {
        start: {
          line: 149,
          column: 6
        },
        end: {
          line: 149,
          column: 47
        }
      },
      "55": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 150,
          column: 19
        }
      },
      "56": {
        start: {
          line: 153,
          column: 21
        },
        end: {
          line: 153,
          column: 79
        }
      },
      "57": {
        start: {
          line: 155,
          column: 4
        },
        end: {
          line: 157,
          column: 5
        }
      },
      "58": {
        start: {
          line: 156,
          column: 6
        },
        end: {
          line: 156,
          column: 92
        }
      },
      "59": {
        start: {
          line: 159,
          column: 19
        },
        end: {
          line: 159,
          column: 40
        }
      },
      "60": {
        start: {
          line: 161,
          column: 4
        },
        end: {
          line: 172,
          column: 5
        }
      },
      "61": {
        start: {
          line: 162,
          column: 6
        },
        end: {
          line: 162,
          column: 92
        }
      },
      "62": {
        start: {
          line: 163,
          column: 6
        },
        end: {
          line: 163,
          column: 47
        }
      },
      "63": {
        start: {
          line: 164,
          column: 6
        },
        end: {
          line: 164,
          column: 24
        }
      },
      "64": {
        start: {
          line: 165,
          column: 11
        },
        end: {
          line: 172,
          column: 5
        }
      },
      "65": {
        start: {
          line: 166,
          column: 6
        },
        end: {
          line: 166,
          column: 35
        }
      },
      "66": {
        start: {
          line: 169,
          column: 6
        },
        end: {
          line: 169,
          column: 87
        }
      },
      "67": {
        start: {
          line: 170,
          column: 6
        },
        end: {
          line: 170,
          column: 47
        }
      },
      "68": {
        start: {
          line: 171,
          column: 6
        },
        end: {
          line: 171,
          column: 19
        }
      },
      "69": {
        start: {
          line: 176,
          column: 29
        },
        end: {
          line: 225,
          column: 41
        }
      },
      "70": {
        start: {
          line: 177,
          column: 4
        },
        end: {
          line: 177,
          column: 48
        }
      },
      "71": {
        start: {
          line: 177,
          column: 42
        },
        end: {
          line: 177,
          column: 48
        }
      },
      "72": {
        start: {
          line: 180,
          column: 4
        },
        end: {
          line: 182,
          column: 5
        }
      },
      "73": {
        start: {
          line: 181,
          column: 6
        },
        end: {
          line: 181,
          column: 40
        }
      },
      "74": {
        start: {
          line: 184,
          column: 4
        },
        end: {
          line: 184,
          column: 54
        }
      },
      "75": {
        start: {
          line: 185,
          column: 19
        },
        end: {
          line: 185,
          column: 52
        }
      },
      "76": {
        start: {
          line: 187,
          column: 4
        },
        end: {
          line: 187,
          column: 22
        }
      },
      "77": {
        start: {
          line: 188,
          column: 4
        },
        end: {
          line: 188,
          column: 18
        }
      },
      "78": {
        start: {
          line: 190,
          column: 4
        },
        end: {
          line: 224,
          column: 5
        }
      },
      "79": {
        start: {
          line: 191,
          column: 40
        },
        end: {
          line: 194,
          column: 8
        }
      },
      "80": {
        start: {
          line: 196,
          column: 6
        },
        end: {
          line: 196,
          column: 39
        }
      },
      "81": {
        start: {
          line: 196,
          column: 33
        },
        end: {
          line: 196,
          column: 39
        }
      },
      "82": {
        start: {
          line: 198,
          column: 37
        },
        end: {
          line: 202,
          column: 7
        }
      },
      "83": {
        start: {
          line: 204,
          column: 6
        },
        end: {
          line: 204,
          column: 22
        }
      },
      "84": {
        start: {
          line: 207,
          column: 6
        },
        end: {
          line: 209,
          column: 7
        }
      },
      "85": {
        start: {
          line: 208,
          column: 8
        },
        end: {
          line: 208,
          column: 65
        }
      },
      "86": {
        start: {
          line: 210,
          column: 6
        },
        end: {
          line: 212,
          column: 7
        }
      },
      "87": {
        start: {
          line: 211,
          column: 8
        },
        end: {
          line: 211,
          column: 71
        }
      },
      "88": {
        start: {
          line: 215,
          column: 6
        },
        end: {
          line: 215,
          column: 39
        }
      },
      "89": {
        start: {
          line: 215,
          column: 33
        },
        end: {
          line: 215,
          column: 39
        }
      },
      "90": {
        start: {
          line: 217,
          column: 27
        },
        end: {
          line: 217,
          column: 96
        }
      },
      "91": {
        start: {
          line: 218,
          column: 6
        },
        end: {
          line: 218,
          column: 28
        }
      },
      "92": {
        start: {
          line: 219,
          column: 6
        },
        end: {
          line: 219,
          column: 55
        }
      },
      "93": {
        start: {
          line: 221,
          column: 6
        },
        end: {
          line: 223,
          column: 7
        }
      },
      "94": {
        start: {
          line: 222,
          column: 8
        },
        end: {
          line: 222,
          column: 27
        }
      },
      "95": {
        start: {
          line: 228,
          column: 23
        },
        end: {
          line: 239,
          column: 26
        }
      },
      "96": {
        start: {
          line: 229,
          column: 4
        },
        end: {
          line: 229,
          column: 23
        }
      },
      "97": {
        start: {
          line: 229,
          column: 17
        },
        end: {
          line: 229,
          column: 23
        }
      },
      "98": {
        start: {
          line: 231,
          column: 4
        },
        end: {
          line: 238,
          column: 5
        }
      },
      "99": {
        start: {
          line: 232,
          column: 20
        },
        end: {
          line: 232,
          column: 38
        }
      },
      "100": {
        start: {
          line: 233,
          column: 6
        },
        end: {
          line: 235,
          column: 7
        }
      },
      "101": {
        start: {
          line: 234,
          column: 8
        },
        end: {
          line: 234,
          column: 45
        }
      },
      "102": {
        start: {
          line: 234,
          column: 25
        },
        end: {
          line: 234,
          column: 43
        }
      },
      "103": {
        start: {
          line: 237,
          column: 6
        },
        end: {
          line: 237,
          column: 52
        }
      },
      "104": {
        start: {
          line: 241,
          column: 26
        },
        end: {
          line: 256,
          column: 29
        }
      },
      "105": {
        start: {
          line: 242,
          column: 4
        },
        end: {
          line: 242,
          column: 23
        }
      },
      "106": {
        start: {
          line: 242,
          column: 17
        },
        end: {
          line: 242,
          column: 23
        }
      },
      "107": {
        start: {
          line: 244,
          column: 4
        },
        end: {
          line: 255,
          column: 5
        }
      },
      "108": {
        start: {
          line: 245,
          column: 23
        },
        end: {
          line: 245,
          column: 44
        }
      },
      "109": {
        start: {
          line: 246,
          column: 6
        },
        end: {
          line: 252,
          column: 7
        }
      },
      "110": {
        start: {
          line: 247,
          column: 8
        },
        end: {
          line: 251,
          column: 11
        }
      },
      "111": {
        start: {
          line: 247,
          column: 25
        },
        end: {
          line: 251,
          column: 9
        }
      },
      "112": {
        start: {
          line: 254,
          column: 6
        },
        end: {
          line: 254,
          column: 55
        }
      },
      "113": {
        start: {
          line: 259,
          column: 2
        },
        end: {
          line: 265,
          column: 62
        }
      },
      "114": {
        start: {
          line: 260,
          column: 4
        },
        end: {
          line: 264,
          column: 5
        }
      },
      "115": {
        start: {
          line: 261,
          column: 6
        },
        end: {
          line: 261,
          column: 26
        }
      },
      "116": {
        start: {
          line: 262,
          column: 11
        },
        end: {
          line: 264,
          column: 5
        }
      },
      "117": {
        start: {
          line: 263,
          column: 6
        },
        end: {
          line: 263,
          column: 27
        }
      },
      "118": {
        start: {
          line: 268,
          column: 2
        },
        end: {
          line: 275,
          column: 8
        }
      },
      "119": {
        start: {
          line: 269,
          column: 4
        },
        end: {
          line: 274,
          column: 5
        }
      },
      "120": {
        start: {
          line: 270,
          column: 6
        },
        end: {
          line: 270,
          column: 34
        }
      },
      "121": {
        start: {
          line: 271,
          column: 6
        },
        end: {
          line: 273,
          column: 7
        }
      },
      "122": {
        start: {
          line: 272,
          column: 8
        },
        end: {
          line: 272,
          column: 42
        }
      },
      "123": {
        start: {
          line: 277,
          column: 2
        },
        end: {
          line: 284,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "fetchWithTimeout",
        decl: {
          start: {
            line: 55,
            column: 15
          },
          end: {
            line: 55,
            column: 31
          }
        },
        loc: {
          start: {
            line: 55,
            column: 92
          },
          end: {
            line: 75,
            column: 1
          }
        },
        line: 55
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 59,
            column: 31
          },
          end: {
            line: 59,
            column: 32
          }
        },
        loc: {
          start: {
            line: 59,
            column: 37
          },
          end: {
            line: 59,
            column: 55
          }
        },
        line: 59
      },
      "2": {
        name: "useDashboardData",
        decl: {
          start: {
            line: 77,
            column: 16
          },
          end: {
            line: 77,
            column: 32
          }
        },
        loc: {
          start: {
            line: 77,
            column: 59
          },
          end: {
            line: 285,
            column: 1
          }
        },
        line: 77
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 97,
            column: 28
          },
          end: {
            line: 97,
            column: 29
          }
        },
        loc: {
          start: {
            line: 97,
            column: 34
          },
          end: {
            line: 103,
            column: 3
          }
        },
        line: 97
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 106,
            column: 33
          },
          end: {
            line: 106,
            column: 34
          }
        },
        loc: {
          start: {
            line: 106,
            column: 96
          },
          end: {
            line: 138,
            column: 3
          }
        },
        line: 106
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 141,
            column: 36
          },
          end: {
            line: 141,
            column: 37
          }
        },
        loc: {
          start: {
            line: 141,
            column: 94
          },
          end: {
            line: 173,
            column: 3
          }
        },
        line: 141
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 176,
            column: 41
          },
          end: {
            line: 176,
            column: 42
          }
        },
        loc: {
          start: {
            line: 176,
            column: 68
          },
          end: {
            line: 225,
            column: 3
          }
        },
        line: 176
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 228,
            column: 35
          },
          end: {
            line: 228,
            column: 36
          }
        },
        loc: {
          start: {
            line: 228,
            column: 62
          },
          end: {
            line: 239,
            column: 3
          }
        },
        line: 228
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 234,
            column: 16
          },
          end: {
            line: 234,
            column: 17
          }
        },
        loc: {
          start: {
            line: 234,
            column: 25
          },
          end: {
            line: 234,
            column: 43
          }
        },
        line: 234
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 241,
            column: 38
          },
          end: {
            line: 241,
            column: 39
          }
        },
        loc: {
          start: {
            line: 241,
            column: 65
          },
          end: {
            line: 256,
            column: 3
          }
        },
        line: 241
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 247,
            column: 16
          },
          end: {
            line: 247,
            column: 17
          }
        },
        loc: {
          start: {
            line: 247,
            column: 25
          },
          end: {
            line: 251,
            column: 9
          }
        },
        line: 247
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 259,
            column: 12
          },
          end: {
            line: 259,
            column: 13
          }
        },
        loc: {
          start: {
            line: 259,
            column: 18
          },
          end: {
            line: 265,
            column: 3
          }
        },
        line: 259
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 268,
            column: 12
          },
          end: {
            line: 268,
            column: 13
          }
        },
        loc: {
          start: {
            line: 268,
            column: 18
          },
          end: {
            line: 275,
            column: 3
          }
        },
        line: 268
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 269,
            column: 11
          },
          end: {
            line: 269,
            column: 12
          }
        },
        loc: {
          start: {
            line: 269,
            column: 17
          },
          end: {
            line: 274,
            column: 5
          }
        },
        line: 269
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 55,
            column: 45
          },
          end: {
            line: 55,
            column: 71
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 55,
            column: 69
          },
          end: {
            line: 55,
            column: 71
          }
        }],
        line: 55
      },
      "1": {
        loc: {
          start: {
            line: 56,
            column: 18
          },
          end: {
            line: 56,
            column: 33
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 56,
            column: 28
          },
          end: {
            line: 56,
            column: 33
          }
        }],
        line: 56
      },
      "2": {
        loc: {
          start: {
            line: 63,
            column: 14
          },
          end: {
            line: 63,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 63,
            column: 14
          },
          end: {
            line: 63,
            column: 20
          }
        }, {
          start: {
            line: 63,
            column: 24
          },
          end: {
            line: 63,
            column: 41
          }
        }],
        line: 63
      },
      "3": {
        loc: {
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 98,
            column: 37
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 98,
            column: 37
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 98
      },
      "4": {
        loc: {
          start: {
            line: 106,
            column: 40
          },
          end: {
            line: 106,
            column: 66
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 106,
            column: 64
          },
          end: {
            line: 106,
            column: 66
          }
        }],
        line: 106
      },
      "5": {
        loc: {
          start: {
            line: 107,
            column: 4
          },
          end: {
            line: 107,
            column: 56
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 107,
            column: 4
          },
          end: {
            line: 107,
            column: 56
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 107
      },
      "6": {
        loc: {
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 116,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 116,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 113
      },
      "7": {
        loc: {
          start: {
            line: 120,
            column: 4
          },
          end: {
            line: 122,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 120,
            column: 4
          },
          end: {
            line: 122,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 120
      },
      "8": {
        loc: {
          start: {
            line: 126,
            column: 4
          },
          end: {
            line: 137,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 126,
            column: 4
          },
          end: {
            line: 137,
            column: 5
          }
        }, {
          start: {
            line: 130,
            column: 11
          },
          end: {
            line: 137,
            column: 5
          }
        }],
        line: 126
      },
      "9": {
        loc: {
          start: {
            line: 130,
            column: 11
          },
          end: {
            line: 137,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 130,
            column: 11
          },
          end: {
            line: 137,
            column: 5
          }
        }, {
          start: {
            line: 132,
            column: 11
          },
          end: {
            line: 137,
            column: 5
          }
        }],
        line: 130
      },
      "10": {
        loc: {
          start: {
            line: 141,
            column: 43
          },
          end: {
            line: 141,
            column: 69
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 141,
            column: 67
          },
          end: {
            line: 141,
            column: 69
          }
        }],
        line: 141
      },
      "11": {
        loc: {
          start: {
            line: 142,
            column: 4
          },
          end: {
            line: 142,
            column: 56
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 142,
            column: 4
          },
          end: {
            line: 142,
            column: 56
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 142
      },
      "12": {
        loc: {
          start: {
            line: 148,
            column: 4
          },
          end: {
            line: 151,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 148,
            column: 4
          },
          end: {
            line: 151,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 148
      },
      "13": {
        loc: {
          start: {
            line: 155,
            column: 4
          },
          end: {
            line: 157,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 155,
            column: 4
          },
          end: {
            line: 157,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 155
      },
      "14": {
        loc: {
          start: {
            line: 161,
            column: 4
          },
          end: {
            line: 172,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 161,
            column: 4
          },
          end: {
            line: 172,
            column: 5
          }
        }, {
          start: {
            line: 165,
            column: 11
          },
          end: {
            line: 172,
            column: 5
          }
        }],
        line: 161
      },
      "15": {
        loc: {
          start: {
            line: 165,
            column: 11
          },
          end: {
            line: 172,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 165,
            column: 11
          },
          end: {
            line: 172,
            column: 5
          }
        }, {
          start: {
            line: 167,
            column: 11
          },
          end: {
            line: 172,
            column: 5
          }
        }],
        line: 165
      },
      "16": {
        loc: {
          start: {
            line: 177,
            column: 4
          },
          end: {
            line: 177,
            column: 48
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 177,
            column: 4
          },
          end: {
            line: 177,
            column: 48
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 177
      },
      "17": {
        loc: {
          start: {
            line: 177,
            column: 8
          },
          end: {
            line: 177,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 177,
            column: 8
          },
          end: {
            line: 177,
            column: 15
          }
        }, {
          start: {
            line: 177,
            column: 19
          },
          end: {
            line: 177,
            column: 40
          }
        }],
        line: 177
      },
      "18": {
        loc: {
          start: {
            line: 180,
            column: 4
          },
          end: {
            line: 182,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 180,
            column: 4
          },
          end: {
            line: 182,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 180
      },
      "19": {
        loc: {
          start: {
            line: 196,
            column: 6
          },
          end: {
            line: 196,
            column: 39
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 196,
            column: 6
          },
          end: {
            line: 196,
            column: 39
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 196
      },
      "20": {
        loc: {
          start: {
            line: 199,
            column: 15
          },
          end: {
            line: 199,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 199,
            column: 50
          },
          end: {
            line: 199,
            column: 65
          }
        }, {
          start: {
            line: 199,
            column: 68
          },
          end: {
            line: 199,
            column: 80
          }
        }],
        line: 199
      },
      "21": {
        loc: {
          start: {
            line: 200,
            column: 24
          },
          end: {
            line: 200,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 200,
            column: 62
          },
          end: {
            line: 200,
            column: 80
          }
        }, {
          start: {
            line: 200,
            column: 83
          },
          end: {
            line: 200,
            column: 98
          }
        }],
        line: 200
      },
      "22": {
        loc: {
          start: {
            line: 201,
            column: 26
          },
          end: {
            line: 201,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 201,
            column: 64
          },
          end: {
            line: 201,
            column: 82
          }
        }, {
          start: {
            line: 201,
            column: 85
          },
          end: {
            line: 201,
            column: 100
          }
        }],
        line: 201
      },
      "23": {
        loc: {
          start: {
            line: 207,
            column: 6
          },
          end: {
            line: 209,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 207,
            column: 6
          },
          end: {
            line: 209,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 207
      },
      "24": {
        loc: {
          start: {
            line: 210,
            column: 6
          },
          end: {
            line: 212,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 210,
            column: 6
          },
          end: {
            line: 212,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 210
      },
      "25": {
        loc: {
          start: {
            line: 215,
            column: 6
          },
          end: {
            line: 215,
            column: 39
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 215,
            column: 6
          },
          end: {
            line: 215,
            column: 39
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 215
      },
      "26": {
        loc: {
          start: {
            line: 217,
            column: 27
          },
          end: {
            line: 217,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 217,
            column: 50
          },
          end: {
            line: 217,
            column: 61
          }
        }, {
          start: {
            line: 217,
            column: 64
          },
          end: {
            line: 217,
            column: 96
          }
        }],
        line: 217
      },
      "27": {
        loc: {
          start: {
            line: 221,
            column: 6
          },
          end: {
            line: 223,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 221,
            column: 6
          },
          end: {
            line: 223,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 221
      },
      "28": {
        loc: {
          start: {
            line: 229,
            column: 4
          },
          end: {
            line: 229,
            column: 23
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 229,
            column: 4
          },
          end: {
            line: 229,
            column: 23
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 229
      },
      "29": {
        loc: {
          start: {
            line: 233,
            column: 6
          },
          end: {
            line: 235,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 233,
            column: 6
          },
          end: {
            line: 235,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 233
      },
      "30": {
        loc: {
          start: {
            line: 242,
            column: 4
          },
          end: {
            line: 242,
            column: 23
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 242,
            column: 4
          },
          end: {
            line: 242,
            column: 23
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 242
      },
      "31": {
        loc: {
          start: {
            line: 246,
            column: 6
          },
          end: {
            line: 252,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 246,
            column: 6
          },
          end: {
            line: 252,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 246
      },
      "32": {
        loc: {
          start: {
            line: 260,
            column: 4
          },
          end: {
            line: 264,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 260,
            column: 4
          },
          end: {
            line: 264,
            column: 5
          }
        }, {
          start: {
            line: 262,
            column: 11
          },
          end: {
            line: 264,
            column: 5
          }
        }],
        line: 260
      },
      "33": {
        loc: {
          start: {
            line: 260,
            column: 8
          },
          end: {
            line: 260,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 260,
            column: 8
          },
          end: {
            line: 260,
            column: 14
          }
        }, {
          start: {
            line: 260,
            column: 18
          },
          end: {
            line: 260,
            column: 32
          }
        }, {
          start: {
            line: 260,
            column: 36
          },
          end: {
            line: 260,
            column: 48
          }
        }],
        line: 260
      },
      "34": {
        loc: {
          start: {
            line: 262,
            column: 11
          },
          end: {
            line: 264,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 262,
            column: 11
          },
          end: {
            line: 264,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 262
      },
      "35": {
        loc: {
          start: {
            line: 271,
            column: 6
          },
          end: {
            line: 273,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 271,
            column: 6
          },
          end: {
            line: 273,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 271
      },
      "36": {
        loc: {
          start: {
            line: 279,
            column: 15
          },
          end: {
            line: 279,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 279,
            column: 15
          },
          end: {
            line: 279,
            column: 24
          }
        }, {
          start: {
            line: 279,
            column: 28
          },
          end: {
            line: 279,
            column: 41
          }
        }],
        line: 279
      },
      "37": {
        loc: {
          start: {
            line: 280,
            column: 11
          },
          end: {
            line: 280,
            column: 31
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 280,
            column: 11
          },
          end: {
            line: 280,
            column: 16
          }
        }, {
          start: {
            line: 280,
            column: 20
          },
          end: {
            line: 280,
            column: 31
          }
        }],
        line: 280
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "28d28ed66791866b1bea51849975f5ccf854c11c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_120psm3w3w = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_120psm3w3w();
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useTenant } from '@/contexts/AppContext';
import { isSuccessResponse, isErrorResponse } from '@/lib/type-utils';
import { apiCache, cacheUtils } from '@/lib/cache';
import { useDebounce, performanceUtils } from '@/lib/performance';
// Default data
const defaultStats =
/* istanbul ignore next */
(cov_120psm3w3w().s[0]++, {
  totalRenewals: 0,
  renewalsDue: 0,
  vendors: 0,
  annualSpend: '$0'
});
const defaultRenewals =
/* istanbul ignore next */
(cov_120psm3w3w().s[1]++, []);

// Cache configuration - now using advanced cache
const CACHE_TTL =
/* istanbul ignore next */
(cov_120psm3w3w().s[2]++, 5 * 60 * 1000); // 5 minutes
const CACHE_TAGS =
/* istanbul ignore next */
(cov_120psm3w3w().s[3]++, {
  STATS: 'dashboard-stats',
  RENEWALS: 'dashboard-renewals'
});

// Fetch with timeout and error handling
async function fetchWithTimeout(url, options =
/* istanbul ignore next */
(cov_120psm3w3w().b[0][0]++, {})) {
  /* istanbul ignore next */
  cov_120psm3w3w().f[0]++;
  const {
    signal,
    timeout =
    /* istanbul ignore next */
    (cov_120psm3w3w().b[1][0]++, 10000)
  } =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[4]++, options);
  const controller =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[5]++, new AbortController());
  const timeoutId =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[6]++, setTimeout(() => {
    /* istanbul ignore next */
    cov_120psm3w3w().f[1]++;
    cov_120psm3w3w().s[7]++;
    return controller.abort();
  }, timeout));
  /* istanbul ignore next */
  cov_120psm3w3w().s[8]++;
  try {
    const response =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[9]++, await fetch(url, {
      signal:
      /* istanbul ignore next */
      (cov_120psm3w3w().b[2][0]++, signal) ||
      /* istanbul ignore next */
      (cov_120psm3w3w().b[2][1]++, controller.signal),
      headers: {
        'Content-Type': 'application/json'
      }
    }));
    /* istanbul ignore next */
    cov_120psm3w3w().s[10]++;
    clearTimeout(timeoutId);
    /* istanbul ignore next */
    cov_120psm3w3w().s[11]++;
    return response;
  } catch (error) {
    /* istanbul ignore next */
    cov_120psm3w3w().s[12]++;
    clearTimeout(timeoutId);
    /* istanbul ignore next */
    cov_120psm3w3w().s[13]++;
    throw error;
  }
}
export function useDashboardData() {
  /* istanbul ignore next */
  cov_120psm3w3w().f[2]++;
  const {
    tenant,
    loading: tenantLoading,
    error: tenantError
  } =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[14]++, useTenant());
  const [data, setData] =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[15]++, useState({
    stats: defaultStats,
    recentRenewals: defaultRenewals,
    upcomingRenewals: defaultRenewals
  }));
  const [isLoading, setIsLoading] =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[16]++, useState(false));
  const [error, setError] =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[17]++, useState(null));

  // Use ref to track if component is mounted
  const isMountedRef =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[18]++, useRef(true));
  const abortControllerRef =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[19]++, useRef(null));

  // Debounce tenant changes to prevent excessive API calls
  const debouncedTenant =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[20]++, useDebounce(tenant, 300));

  // Memoize cache keys to prevent recreation
  const cacheKeys =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[21]++, useMemo(() => {
    /* istanbul ignore next */
    cov_120psm3w3w().f[3]++;
    cov_120psm3w3w().s[22]++;
    if (!debouncedTenant) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[3][0]++;
      cov_120psm3w3w().s[23]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[3][1]++;
    }
    cov_120psm3w3w().s[24]++;
    return {
      stats: cacheUtils.createKey('dashboard-stats', debouncedTenant.tenantId),
      renewals: cacheUtils.createKey('dashboard-renewals', debouncedTenant.tenantId)
    };
  }, [debouncedTenant?.tenantId]));

  // Fetch dashboard stats with advanced caching
  const fetchStats =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[25]++, useCallback(async (options =
  /* istanbul ignore next */
  (cov_120psm3w3w().b[4][0]++, {})) => {
    /* istanbul ignore next */
    cov_120psm3w3w().f[4]++;
    cov_120psm3w3w().s[26]++;
    if (!tenant) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[5][0]++;
      cov_120psm3w3w().s[27]++;
      throw new Error('Tenant not available');
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[5][1]++;
    }
    cov_120psm3w3w().s[28]++;
    performanceUtils.mark('fetchStats');
    const cacheKey =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[29]++, cacheUtils.createKey('dashboard-stats', tenant.tenantId));
    const cached =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[30]++, apiCache.get(cacheKey));
    /* istanbul ignore next */
    cov_120psm3w3w().s[31]++;
    if (cached) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[6][0]++;
      cov_120psm3w3w().s[32]++;
      performanceUtils.measure('fetchStats');
      /* istanbul ignore next */
      cov_120psm3w3w().s[33]++;
      return cached;
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[6][1]++;
    }
    const response =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[34]++, await fetchWithTimeout('/api/dashboard/stats', options));
    /* istanbul ignore next */
    cov_120psm3w3w().s[35]++;
    if (!response.ok) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[7][0]++;
      cov_120psm3w3w().s[36]++;
      throw new Error(`Failed to fetch stats: ${response.status} ${response.statusText}`);
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[7][1]++;
    }
    const result =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[37]++, await response.json());
    /* istanbul ignore next */
    cov_120psm3w3w().s[38]++;
    if (isSuccessResponse(result)) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[8][0]++;
      cov_120psm3w3w().s[39]++;
      apiCache.set(cacheKey, result.data, CACHE_TTL, [CACHE_TAGS.STATS, tenant.tenantId]);
      /* istanbul ignore next */
      cov_120psm3w3w().s[40]++;
      performanceUtils.measure('fetchStats');
      /* istanbul ignore next */
      cov_120psm3w3w().s[41]++;
      return result.data;
    } else {
      /* istanbul ignore next */
      cov_120psm3w3w().b[8][1]++;
      cov_120psm3w3w().s[42]++;
      if (isErrorResponse(result)) {
        /* istanbul ignore next */
        cov_120psm3w3w().b[9][0]++;
        cov_120psm3w3w().s[43]++;
        throw new Error(result.error);
      } else {
        /* istanbul ignore next */
        cov_120psm3w3w().b[9][1]++;
        cov_120psm3w3w().s[44]++;
        // Fallback for legacy API responses
        apiCache.set(cacheKey, result, CACHE_TTL, [CACHE_TAGS.STATS, tenant.tenantId]);
        /* istanbul ignore next */
        cov_120psm3w3w().s[45]++;
        performanceUtils.measure('fetchStats');
        /* istanbul ignore next */
        cov_120psm3w3w().s[46]++;
        return result;
      }
    }
  }, [tenant]));

  // Fetch recent renewals with advanced caching
  const fetchRenewals =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[47]++, useCallback(async (options =
  /* istanbul ignore next */
  (cov_120psm3w3w().b[10][0]++, {})) => {
    /* istanbul ignore next */
    cov_120psm3w3w().f[5]++;
    cov_120psm3w3w().s[48]++;
    if (!tenant) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[11][0]++;
      cov_120psm3w3w().s[49]++;
      throw new Error('Tenant not available');
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[11][1]++;
    }
    cov_120psm3w3w().s[50]++;
    performanceUtils.mark('fetchRenewals');
    const cacheKey =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[51]++, cacheUtils.createKey('dashboard-renewals', tenant.tenantId));
    const cached =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[52]++, apiCache.get(cacheKey));
    /* istanbul ignore next */
    cov_120psm3w3w().s[53]++;
    if (cached) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[12][0]++;
      cov_120psm3w3w().s[54]++;
      performanceUtils.measure('fetchRenewals');
      /* istanbul ignore next */
      cov_120psm3w3w().s[55]++;
      return cached;
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[12][1]++;
    }
    const response =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[56]++, await fetchWithTimeout('/api/dashboard/renewals', options));
    /* istanbul ignore next */
    cov_120psm3w3w().s[57]++;
    if (!response.ok) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[13][0]++;
      cov_120psm3w3w().s[58]++;
      throw new Error(`Failed to fetch renewals: ${response.status} ${response.statusText}`);
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[13][1]++;
    }
    const result =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[59]++, await response.json());
    /* istanbul ignore next */
    cov_120psm3w3w().s[60]++;
    if (isSuccessResponse(result)) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[14][0]++;
      cov_120psm3w3w().s[61]++;
      apiCache.set(cacheKey, result.data, CACHE_TTL, [CACHE_TAGS.RENEWALS, tenant.tenantId]);
      /* istanbul ignore next */
      cov_120psm3w3w().s[62]++;
      performanceUtils.measure('fetchRenewals');
      /* istanbul ignore next */
      cov_120psm3w3w().s[63]++;
      return result.data;
    } else {
      /* istanbul ignore next */
      cov_120psm3w3w().b[14][1]++;
      cov_120psm3w3w().s[64]++;
      if (isErrorResponse(result)) {
        /* istanbul ignore next */
        cov_120psm3w3w().b[15][0]++;
        cov_120psm3w3w().s[65]++;
        throw new Error(result.error);
      } else {
        /* istanbul ignore next */
        cov_120psm3w3w().b[15][1]++;
        cov_120psm3w3w().s[66]++;
        // Fallback for legacy API responses
        apiCache.set(cacheKey, result, CACHE_TTL, [CACHE_TAGS.RENEWALS, tenant.tenantId]);
        /* istanbul ignore next */
        cov_120psm3w3w().s[67]++;
        performanceUtils.measure('fetchRenewals');
        /* istanbul ignore next */
        cov_120psm3w3w().s[68]++;
        return result;
      }
    }
  }, [tenant]));

  // Fetch all dashboard data
  const fetchDashboardData =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[69]++, useCallback(async () => {
    /* istanbul ignore next */
    cov_120psm3w3w().f[6]++;
    cov_120psm3w3w().s[70]++;
    if (
    /* istanbul ignore next */
    (cov_120psm3w3w().b[17][0]++, !tenant) ||
    /* istanbul ignore next */
    (cov_120psm3w3w().b[17][1]++, !isMountedRef.current)) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[16][0]++;
      cov_120psm3w3w().s[71]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[16][1]++;
    }

    // Cancel any existing request
    cov_120psm3w3w().s[72]++;
    if (abortControllerRef.current) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[18][0]++;
      cov_120psm3w3w().s[73]++;
      abortControllerRef.current.abort();
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[18][1]++;
    }
    cov_120psm3w3w().s[74]++;
    abortControllerRef.current = new AbortController();
    const signal =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[75]++, abortControllerRef.current.signal);
    /* istanbul ignore next */
    cov_120psm3w3w().s[76]++;
    setIsLoading(true);
    /* istanbul ignore next */
    cov_120psm3w3w().s[77]++;
    setError(null);
    /* istanbul ignore next */
    cov_120psm3w3w().s[78]++;
    try {
      const [statsData, renewalsData] =
      /* istanbul ignore next */
      (cov_120psm3w3w().s[79]++, await Promise.allSettled([fetchStats({
        signal
      }), fetchRenewals({
        signal
      })]));
      /* istanbul ignore next */
      cov_120psm3w3w().s[80]++;
      if (!isMountedRef.current) {
        /* istanbul ignore next */
        cov_120psm3w3w().b[19][0]++;
        cov_120psm3w3w().s[81]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_120psm3w3w().b[19][1]++;
      }
      const newData =
      /* istanbul ignore next */
      (cov_120psm3w3w().s[82]++, {
        stats: statsData.status === 'fulfilled' ?
        /* istanbul ignore next */
        (cov_120psm3w3w().b[20][0]++, statsData.value) :
        /* istanbul ignore next */
        (cov_120psm3w3w().b[20][1]++, defaultStats),
        recentRenewals: renewalsData.status === 'fulfilled' ?
        /* istanbul ignore next */
        (cov_120psm3w3w().b[21][0]++, renewalsData.value) :
        /* istanbul ignore next */
        (cov_120psm3w3w().b[21][1]++, defaultRenewals),
        upcomingRenewals: renewalsData.status === 'fulfilled' ?
        /* istanbul ignore next */
        (cov_120psm3w3w().b[22][0]++, renewalsData.value) :
        /* istanbul ignore next */
        (cov_120psm3w3w().b[22][1]++, defaultRenewals)
      });
      /* istanbul ignore next */
      cov_120psm3w3w().s[83]++;
      setData(newData);

      // Log any errors but don't fail the entire operation
      /* istanbul ignore next */
      cov_120psm3w3w().s[84]++;
      if (statsData.status === 'rejected') {
        /* istanbul ignore next */
        cov_120psm3w3w().b[23][0]++;
        cov_120psm3w3w().s[85]++;
        console.error('Failed to fetch stats:', statsData.reason);
      } else
      /* istanbul ignore next */
      {
        cov_120psm3w3w().b[23][1]++;
      }
      cov_120psm3w3w().s[86]++;
      if (renewalsData.status === 'rejected') {
        /* istanbul ignore next */
        cov_120psm3w3w().b[24][0]++;
        cov_120psm3w3w().s[87]++;
        console.error('Failed to fetch renewals:', renewalsData.reason);
      } else
      /* istanbul ignore next */
      {
        cov_120psm3w3w().b[24][1]++;
      }
    } catch (err) {
      /* istanbul ignore next */
      cov_120psm3w3w().s[88]++;
      if (!isMountedRef.current) {
        /* istanbul ignore next */
        cov_120psm3w3w().b[25][0]++;
        cov_120psm3w3w().s[89]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_120psm3w3w().b[25][1]++;
      }
      const errorMessage =
      /* istanbul ignore next */
      (cov_120psm3w3w().s[90]++, err instanceof Error ?
      /* istanbul ignore next */
      (cov_120psm3w3w().b[26][0]++, err.message) :
      /* istanbul ignore next */
      (cov_120psm3w3w().b[26][1]++, 'Failed to fetch dashboard data'));
      /* istanbul ignore next */
      cov_120psm3w3w().s[91]++;
      setError(errorMessage);
      /* istanbul ignore next */
      cov_120psm3w3w().s[92]++;
      console.error('Dashboard data fetch error:', err);
    } finally {
      /* istanbul ignore next */
      cov_120psm3w3w().s[93]++;
      if (isMountedRef.current) {
        /* istanbul ignore next */
        cov_120psm3w3w().b[27][0]++;
        cov_120psm3w3w().s[94]++;
        setIsLoading(false);
      } else
      /* istanbul ignore next */
      {
        cov_120psm3w3w().b[27][1]++;
      }
    }
  }, [tenant, fetchStats, fetchRenewals]));

  // Individual refetch functions
  const refetchStats =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[95]++, useCallback(async () => {
    /* istanbul ignore next */
    cov_120psm3w3w().f[7]++;
    cov_120psm3w3w().s[96]++;
    if (!tenant) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[28][0]++;
      cov_120psm3w3w().s[97]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[28][1]++;
    }
    cov_120psm3w3w().s[98]++;
    try {
      const stats =
      /* istanbul ignore next */
      (cov_120psm3w3w().s[99]++, await fetchStats());
      /* istanbul ignore next */
      cov_120psm3w3w().s[100]++;
      if (isMountedRef.current) {
        /* istanbul ignore next */
        cov_120psm3w3w().b[29][0]++;
        cov_120psm3w3w().s[101]++;
        setData(prev => {
          /* istanbul ignore next */
          cov_120psm3w3w().f[8]++;
          cov_120psm3w3w().s[102]++;
          return /* istanbul ignore next */_objectSpread(_objectSpread({}, prev), {}, {
            stats
          });
        });
      } else
      /* istanbul ignore next */
      {
        cov_120psm3w3w().b[29][1]++;
      }
    } catch (err) {
      /* istanbul ignore next */
      cov_120psm3w3w().s[103]++;
      console.error('Failed to refetch stats:', err);
    }
  }, [tenant, fetchStats]));
  const refetchRenewals =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[104]++, useCallback(async () => {
    /* istanbul ignore next */
    cov_120psm3w3w().f[9]++;
    cov_120psm3w3w().s[105]++;
    if (!tenant) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[30][0]++;
      cov_120psm3w3w().s[106]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[30][1]++;
    }
    cov_120psm3w3w().s[107]++;
    try {
      const renewals =
      /* istanbul ignore next */
      (cov_120psm3w3w().s[108]++, await fetchRenewals());
      /* istanbul ignore next */
      cov_120psm3w3w().s[109]++;
      if (isMountedRef.current) {
        /* istanbul ignore next */
        cov_120psm3w3w().b[31][0]++;
        cov_120psm3w3w().s[110]++;
        setData(prev => {
          /* istanbul ignore next */
          cov_120psm3w3w().f[10]++;
          cov_120psm3w3w().s[111]++;
          return /* istanbul ignore next */_objectSpread(_objectSpread({}, prev), {}, {
            recentRenewals: renewals,
            upcomingRenewals: renewals
          });
        });
      } else
      /* istanbul ignore next */
      {
        cov_120psm3w3w().b[31][1]++;
      }
    } catch (err) {
      /* istanbul ignore next */
      cov_120psm3w3w().s[112]++;
      console.error('Failed to refetch renewals:', err);
    }
  }, [tenant, fetchRenewals]));

  // Main effect to fetch data when tenant changes
  /* istanbul ignore next */
  cov_120psm3w3w().s[113]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_120psm3w3w().f[11]++;
    cov_120psm3w3w().s[114]++;
    if (
    /* istanbul ignore next */
    (cov_120psm3w3w().b[33][0]++, tenant) &&
    /* istanbul ignore next */
    (cov_120psm3w3w().b[33][1]++, !tenantLoading) &&
    /* istanbul ignore next */
    (cov_120psm3w3w().b[33][2]++, !tenantError)) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[32][0]++;
      cov_120psm3w3w().s[115]++;
      fetchDashboardData();
    } else {
      /* istanbul ignore next */
      cov_120psm3w3w().b[32][1]++;
      cov_120psm3w3w().s[116]++;
      if (tenantError) {
        /* istanbul ignore next */
        cov_120psm3w3w().b[34][0]++;
        cov_120psm3w3w().s[117]++;
        setError(tenantError);
      } else
      /* istanbul ignore next */
      {
        cov_120psm3w3w().b[34][1]++;
      }
    }
  }, [tenant, tenantLoading, tenantError, fetchDashboardData]);

  // Cleanup effect
  /* istanbul ignore next */
  cov_120psm3w3w().s[118]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_120psm3w3w().f[12]++;
    cov_120psm3w3w().s[119]++;
    return () => {
      /* istanbul ignore next */
      cov_120psm3w3w().f[13]++;
      cov_120psm3w3w().s[120]++;
      isMountedRef.current = false;
      /* istanbul ignore next */
      cov_120psm3w3w().s[121]++;
      if (abortControllerRef.current) {
        /* istanbul ignore next */
        cov_120psm3w3w().b[35][0]++;
        cov_120psm3w3w().s[122]++;
        abortControllerRef.current.abort();
      } else
      /* istanbul ignore next */
      {
        cov_120psm3w3w().b[35][1]++;
      }
    };
  }, []);
  /* istanbul ignore next */
  cov_120psm3w3w().s[123]++;
  return {
    data,
    isLoading:
    /* istanbul ignore next */
    (cov_120psm3w3w().b[36][0]++, isLoading) ||
    /* istanbul ignore next */
    (cov_120psm3w3w().b[36][1]++, tenantLoading),
    error:
    /* istanbul ignore next */
    (cov_120psm3w3w().b[37][0]++, error) ||
    /* istanbul ignore next */
    (cov_120psm3w3w().b[37][1]++, tenantError),
    refetch: fetchDashboardData,
    refetchStats,
    refetchRenewals
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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