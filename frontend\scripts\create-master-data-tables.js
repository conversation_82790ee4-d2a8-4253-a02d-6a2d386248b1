const { Pool } = require('pg');

// Database configuration
const pool = new Pool({
  user: 'postgres',
  host: '127.0.0.1',
  database: 'Renewtrack',
  password: 'postgres',
  port: 5432,
});

async function createMasterDataTables() {
  const client = await pool.connect();
  
  try {
    console.log('Creating master data tables...');
    
    // Enable UUID extension
    await client.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');
    console.log('✓ UUID extension enabled');
    
    // Create enum types
    await client.query(`
      DO $$ BEGIN
          CREATE TYPE metadata.sync_status AS ENUM ('pending', 'matched', 'manual_review', 'rejected');
      EXCEPTION
          WHEN duplicate_object THEN null;
      END $$;
    `);
    console.log('✓ sync_status enum created');
    
    await client.query(`
      DO $$ BEGIN
          CREATE TYPE metadata.entity_status AS ENUM ('active', 'merged', 'deprecated');
      EXCEPTION
          WHEN duplicate_object THEN null;
      END $$;
    `);
    console.log('✓ entity_status enum created');
    
    await client.query(`
      DO $$ BEGIN
          CREATE TYPE metadata.product_type AS ENUM ('physical', 'digital', 'service');
      EXCEPTION
          WHEN duplicate_object THEN null;
      END $$;
    `);
    console.log('✓ product_type enum created');
    
    // Create global_vendors table
    await client.query(`
      CREATE TABLE IF NOT EXISTS metadata.global_vendors (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          canonical_name VARCHAR(255) NOT NULL UNIQUE,
          legal_name VARCHAR(255),
          tax_id VARCHAR(100),
          country_code VARCHAR(2),
          domain VARCHAR(255),
          address_hash VARCHAR(64),
          phone_normalized VARCHAR(50),
          confidence_score DECIMAL(5,4) DEFAULT 0.0000 CHECK (confidence_score >= 0 AND confidence_score <= 1),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          status metadata.entity_status DEFAULT 'active',
          merged_into_id UUID
      )
    `);
    console.log('✓ global_vendors table created');
    
    // Add self-referencing foreign key
    await client.query(`
      ALTER TABLE metadata.global_vendors 
      DROP CONSTRAINT IF EXISTS fk_global_vendors_merged_into
    `);
    await client.query(`
      ALTER TABLE metadata.global_vendors 
      ADD CONSTRAINT fk_global_vendors_merged_into 
      FOREIGN KEY (merged_into_id) REFERENCES metadata.global_vendors(id)
    `);
    console.log('✓ global_vendors foreign key added');
    
    // Create global_products table
    await client.query(`
      CREATE TABLE IF NOT EXISTS metadata.global_products (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          canonical_name VARCHAR(255) NOT NULL,
          product_category VARCHAR(100),
          vendor_id UUID NOT NULL,
          gtin VARCHAR(14),
          manufacturer_sku VARCHAR(100),
          product_type metadata.product_type DEFAULT 'physical',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          status metadata.entity_status DEFAULT 'active',
          merged_into_id UUID
      )
    `);
    console.log('✓ global_products table created');
    
    // Add foreign key constraints for global_products
    await client.query(`
      ALTER TABLE metadata.global_products 
      DROP CONSTRAINT IF EXISTS fk_global_products_vendor
    `);
    await client.query(`
      ALTER TABLE metadata.global_products 
      ADD CONSTRAINT fk_global_products_vendor 
      FOREIGN KEY (vendor_id) REFERENCES metadata.global_vendors(id)
    `);
    
    await client.query(`
      ALTER TABLE metadata.global_products 
      DROP CONSTRAINT IF EXISTS fk_global_products_merged_into
    `);
    await client.query(`
      ALTER TABLE metadata.global_products 
      ADD CONSTRAINT fk_global_products_merged_into 
      FOREIGN KEY (merged_into_id) REFERENCES metadata.global_products(id)
    `);
    console.log('✓ global_products foreign keys added');
    
    // Create global_product_versions table
    await client.query(`
      CREATE TABLE IF NOT EXISTS metadata.global_product_versions (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          product_id UUID NOT NULL,
          version_number VARCHAR(50) NOT NULL,
          release_date DATE,
          end_of_life_date DATE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          status metadata.entity_status DEFAULT 'active',
          merged_into_id UUID
      )
    `);
    console.log('✓ global_product_versions table created');
    
    // Add foreign key constraints for global_product_versions
    await client.query(`
      ALTER TABLE metadata.global_product_versions 
      DROP CONSTRAINT IF EXISTS fk_global_product_versions_product
    `);
    await client.query(`
      ALTER TABLE metadata.global_product_versions 
      ADD CONSTRAINT fk_global_product_versions_product 
      FOREIGN KEY (product_id) REFERENCES metadata.global_products(id)
    `);
    
    await client.query(`
      ALTER TABLE metadata.global_product_versions 
      DROP CONSTRAINT IF EXISTS fk_global_product_versions_merged_into
    `);
    await client.query(`
      ALTER TABLE metadata.global_product_versions 
      ADD CONSTRAINT fk_global_product_versions_merged_into 
      FOREIGN KEY (merged_into_id) REFERENCES metadata.global_product_versions(id)
    `);
    console.log('✓ global_product_versions foreign keys added');
    
    // Add unique constraints
    await client.query(`
      ALTER TABLE metadata.global_products 
      DROP CONSTRAINT IF EXISTS global_products_vendor_name_unique
    `);
    await client.query(`
      ALTER TABLE metadata.global_products 
      ADD CONSTRAINT global_products_vendor_name_unique UNIQUE (vendor_id, canonical_name)
    `);
    
    await client.query(`
      ALTER TABLE metadata.global_product_versions 
      DROP CONSTRAINT IF EXISTS global_product_versions_product_version_unique
    `);
    await client.query(`
      ALTER TABLE metadata.global_product_versions 
      ADD CONSTRAINT global_product_versions_product_version_unique UNIQUE (product_id, version_number)
    `);
    console.log('✓ Unique constraints added');
    
    // Create indexes
    await client.query('CREATE INDEX IF NOT EXISTS idx_global_vendors_canonical_name ON metadata.global_vendors(canonical_name)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_global_vendors_domain ON metadata.global_vendors(domain) WHERE domain IS NOT NULL');
    await client.query('CREATE INDEX IF NOT EXISTS idx_global_vendors_status ON metadata.global_vendors(status)');
    
    await client.query('CREATE INDEX IF NOT EXISTS idx_global_products_vendor_id ON metadata.global_products(vendor_id)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_global_products_canonical_name ON metadata.global_products(canonical_name)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_global_products_category ON metadata.global_products(product_category)');
    
    await client.query('CREATE INDEX IF NOT EXISTS idx_global_product_versions_product_id ON metadata.global_product_versions(product_id)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_global_product_versions_version ON metadata.global_product_versions(version_number)');
    console.log('✓ Indexes created');
    
    console.log('✅ All global master data tables created successfully!');
    
  } catch (error) {
    console.error('❌ Error creating tables:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function createTenantTables() {
  const client = await pool.connect();
  
  try {
    console.log('Creating tenant-specific tables...');
    
    // Create enum type for tenant schema
    await client.query(`
      DO $$ BEGIN
          CREATE TYPE "tenant_0000000000000001".sync_status AS ENUM ('pending', 'matched', 'manual_review', 'rejected');
      EXCEPTION
          WHEN duplicate_object THEN null;
      END $$;
    `);
    console.log('✓ Tenant sync_status enum created');
    
    // Create tenant_vendors table
    await client.query(`
      CREATE TABLE IF NOT EXISTS "tenant_0000000000000001".tenant_vendors (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name VARCHAR(255) NOT NULL,
          display_name VARCHAR(255),
          contact_email VARCHAR(255),
          phone VARCHAR(50),
          address_line1 VARCHAR(255),
          address_line2 VARCHAR(255),
          city VARCHAR(100),
          state VARCHAR(100),
          postal_code VARCHAR(20),
          country VARCHAR(100),
          tax_id VARCHAR(100),
          website VARCHAR(255),
          notes TEXT,
          custom_fields JSONB DEFAULT '{}',
          global_vendor_id UUID,
          sync_status "tenant_0000000000000001".sync_status DEFAULT 'pending',
          sync_confidence DECIMAL(5,4) DEFAULT 0.0000 CHECK (sync_confidence >= 0 AND sync_confidence <= 1),
          last_sync_attempt TIMESTAMP,
          created_by UUID,
          updated_by UUID,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          CONSTRAINT tenant_vendors_name_not_empty CHECK (LENGTH(TRIM(name)) > 0)
      )
    `);
    console.log('✓ tenant_vendors table created');
    
    // Create tenant_products table
    await client.query(`
      CREATE TABLE IF NOT EXISTS "tenant_0000000000000001".tenant_products (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          vendor_id UUID NOT NULL REFERENCES "tenant_0000000000000001".tenant_vendors(id),
          name VARCHAR(255) NOT NULL,
          description TEXT,
          category VARCHAR(100),
          sku VARCHAR(100),
          barcode VARCHAR(50),
          unit_of_measure VARCHAR(50),
          custom_fields JSONB DEFAULT '{}',
          global_product_id UUID,
          sync_status "tenant_0000000000000001".sync_status DEFAULT 'pending',
          sync_confidence DECIMAL(5,4) DEFAULT 0.0000 CHECK (sync_confidence >= 0 AND sync_confidence <= 1),
          last_sync_attempt TIMESTAMP,
          created_by UUID,
          updated_by UUID,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          CONSTRAINT tenant_products_name_not_empty CHECK (LENGTH(TRIM(name)) > 0)
      )
    `);
    console.log('✓ tenant_products table created');
    
    // Create tenant_product_versions table
    await client.query(`
      CREATE TABLE IF NOT EXISTS "tenant_0000000000000001".tenant_product_versions (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          product_id UUID NOT NULL REFERENCES "tenant_0000000000000001".tenant_products(id),
          version VARCHAR(50) NOT NULL,
          release_date DATE,
          notes TEXT,
          is_current BOOLEAN DEFAULT false,
          custom_fields JSONB DEFAULT '{}',
          global_product_version_id UUID,
          sync_status "tenant_0000000000000001".sync_status DEFAULT 'pending',
          sync_confidence DECIMAL(5,4) DEFAULT 0.0000 CHECK (sync_confidence >= 0 AND sync_confidence <= 1),
          last_sync_attempt TIMESTAMP,
          created_by UUID,
          updated_by UUID,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          CONSTRAINT tenant_product_versions_version_not_empty CHECK (LENGTH(TRIM(version)) > 0),
          CONSTRAINT tenant_product_versions_product_version_unique UNIQUE (product_id, version)
      )
    `);
    console.log('✓ tenant_product_versions table created');
    
    // Create indexes for tenant tables
    await client.query('CREATE INDEX IF NOT EXISTS idx_tenant_vendors_name ON "tenant_0000000000000001".tenant_vendors(name)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_tenant_vendors_global_id ON "tenant_0000000000000001".tenant_vendors(global_vendor_id)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_tenant_vendors_sync_status ON "tenant_0000000000000001".tenant_vendors(sync_status)');
    
    await client.query('CREATE INDEX IF NOT EXISTS idx_tenant_products_vendor_id ON "tenant_0000000000000001".tenant_products(vendor_id)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_tenant_products_name ON "tenant_0000000000000001".tenant_products(name)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_tenant_products_global_id ON "tenant_0000000000000001".tenant_products(global_product_id)');
    
    await client.query('CREATE INDEX IF NOT EXISTS idx_tenant_product_versions_product_id ON "tenant_0000000000000001".tenant_product_versions(product_id)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_tenant_product_versions_global_id ON "tenant_0000000000000001".tenant_product_versions(global_product_version_id)');
    console.log('✓ Tenant table indexes created');
    
    console.log('✅ All tenant-specific tables created successfully!');
    
  } catch (error) {
    console.error('❌ Error creating tenant tables:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function main() {
  try {
    await createMasterDataTables();
    await createTenantTables();
    console.log('🎉 All master data management tables created successfully!');
  } catch (error) {
    console.error('💥 Failed to create tables:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

main();
