/**
 * Database Utilities and Query Optimization
 * 
 * This module provides optimized database operations with proper error handling,
 * connection management, and query optimization for the multi-tenant application.
 */

import { Pool, PoolClient, QueryResult } from 'pg';
import { getDbPool } from './db-config';

// Query result interfaces
export interface QueryOptions {
  timeout?: number;
  retries?: number;
  schema?: string;
}

export interface DatabaseError extends Error {
  code?: string;
  detail?: string;
  hint?: string;
  position?: string;
  internalPosition?: string;
  internalQuery?: string;
  where?: string;
  schema?: string;
  table?: string;
  column?: string;
  dataType?: string;
  constraint?: string;
}

// Database operation result
export interface DbResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errorCode?: string;
  rowCount?: number;
}

// Query cache for prepared statements
const queryCache = new Map<string, string>();

/**
 * Execute a query with proper error handling and optimization
 */
export async function executeQuery<T = any>(
  query: string,
  params: any[] = [],
  options: QueryOptions = {}
): Promise<DbResult<T[]>> {
  let client: PoolClient | null = null;
  
  try {
    const pool = await getDbPool();
    client = await pool.connect();
    
    // Set query timeout if specified
    if (options.timeout) {
      await client.query(`SET statement_timeout = ${options.timeout}`);
    }
    
    // Set schema if specified (for multi-tenant support)
    if (options.schema) {
      await client.query(`SET search_path TO "${options.schema}", public`);
    }
    
    const startTime = Date.now();
    const result: QueryResult = await client.query(query, params);
    const duration = Date.now() - startTime;
    
    // Log slow queries (> 1 second)
    if (duration > 1000) {
      console.warn(`Slow query detected (${duration}ms):`, {
        query: query.substring(0, 100) + '...',
        params: params.length,
        schema: options.schema
      });
    }
    
    return {
      success: true,
      data: result.rows,
      rowCount: result.rowCount || 0
    };
    
  } catch (error) {
    const dbError = error as DatabaseError;
    console.error('Database query error:', {
      error: dbError.message,
      code: dbError.code,
      detail: dbError.detail,
      query: query.substring(0, 100) + '...',
      schema: options.schema
    });
    
    return {
      success: false,
      error: getDatabaseErrorMessage(dbError),
      errorCode: dbError.code || 'UNKNOWN_ERROR'
    };
  } finally {
    if (client) {
      client.release();
    }
  }
}

/**
 * Execute a single query and return the first row
 */
export async function executeQuerySingle<T = any>(
  query: string,
  params: any[] = [],
  options: QueryOptions = {}
): Promise<DbResult<T>> {
  const result = await executeQuery<T>(query, params, options);
  
  if (!result.success) {
    return result;
  }
  
  return {
    success: true,
    data: result.data?.[0] || null,
    rowCount: result.rowCount
  };
}

/**
 * Execute a transaction with multiple queries
 */
export async function executeTransaction<T = any>(
  queries: Array<{ query: string; params?: any[] }>,
  options: QueryOptions = {}
): Promise<DbResult<T[]>> {
  let client: PoolClient | null = null;
  
  try {
    const pool = await getDbPool();
    client = await pool.connect();
    
    // Set schema if specified
    if (options.schema) {
      await client.query(`SET search_path TO "${options.schema}", public`);
    }
    
    await client.query('BEGIN');
    
    const results: any[] = [];
    
    for (const { query, params = [] } of queries) {
      const result = await client.query(query, params);
      results.push(result.rows);
    }
    
    await client.query('COMMIT');
    
    return {
      success: true,
      data: results,
      rowCount: results.reduce((sum, rows) => sum + rows.length, 0)
    };
    
  } catch (error) {
    if (client) {
      try {
        await client.query('ROLLBACK');
      } catch (rollbackError) {
        console.error('Error rolling back transaction:', rollbackError);
      }
    }
    
    const dbError = error as DatabaseError;
    console.error('Transaction error:', {
      error: dbError.message,
      code: dbError.code,
      detail: dbError.detail,
      schema: options.schema
    });
    
    return {
      success: false,
      error: getDatabaseErrorMessage(dbError),
      errorCode: dbError.code || 'TRANSACTION_ERROR'
    };
  } finally {
    if (client) {
      client.release();
    }
  }
}

/**
 * Check if a schema exists
 */
export async function schemaExists(schemaName: string): Promise<boolean> {
  const result = await executeQuerySingle<{ exists: boolean }>(
    'SELECT EXISTS(SELECT 1 FROM information_schema.schemata WHERE schema_name = $1) as exists',
    [schemaName]
  );
  
  return result.success && result.data?.exists === true;
}

/**
 * Check if a table exists in a schema
 */
export async function tableExists(tableName: string, schemaName = 'public'): Promise<boolean> {
  const result = await executeQuerySingle<{ exists: boolean }>(
    'SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_schema = $1 AND table_name = $2) as exists',
    [schemaName, tableName]
  );
  
  return result.success && result.data?.exists === true;
}

/**
 * Get database connection health status
 */
export async function getConnectionHealth(): Promise<{
  healthy: boolean;
  totalConnections: number;
  idleConnections: number;
  waitingCount: number;
}> {
  try {
    const pool = await getDbPool();
    
    return {
      healthy: true,
      totalConnections: pool.totalCount,
      idleConnections: pool.idleCount,
      waitingCount: pool.waitingCount
    };
  } catch (error) {
    console.error('Database health check failed:', error);
    return {
      healthy: false,
      totalConnections: 0,
      idleConnections: 0,
      waitingCount: 0
    };
  }
}

/**
 * Close database connections (for graceful shutdown)
 */
export async function closeDatabase(): Promise<void> {
  try {
    const pool = await getDbPool();
    await pool.end();
    console.log('Database connections closed successfully');
  } catch (error) {
    console.error('Error closing database connections:', error);
  }
}

/**
 * Get user-friendly error message from database error
 */
function getDatabaseErrorMessage(error: DatabaseError): string {
  switch (error.code) {
    case '23505': // unique_violation
      return 'A record with this information already exists';
    case '23503': // foreign_key_violation
      return 'Referenced record does not exist';
    case '23502': // not_null_violation
      return 'Required field is missing';
    case '23514': // check_violation
      return 'Invalid data provided';
    case '42P01': // undefined_table
      return 'Database table not found';
    case '42703': // undefined_column
      return 'Database column not found';
    case '42883': // undefined_function
      return 'Database function not found';
    case '28P01': // invalid_password
      return 'Database authentication failed';
    case '3D000': // invalid_catalog_name
      return 'Database not found';
    case '08006': // connection_failure
      return 'Database connection failed';
    case '57P03': // cannot_connect_now
      return 'Database is temporarily unavailable';
    case '53300': // too_many_connections
      return 'Database connection limit reached';
    default:
      return 'A database error occurred';
  }
}

/**
 * Build WHERE clause with proper parameter binding
 */
export function buildWhereClause(
  conditions: Record<string, any>,
  startIndex = 1
): { clause: string; params: any[]; nextIndex: number } {
  const params: any[] = [];
  const clauses: string[] = [];
  let paramIndex = startIndex;
  
  for (const [key, value] of Object.entries(conditions)) {
    if (value !== undefined && value !== null) {
      if (Array.isArray(value)) {
        // Handle IN clause
        const placeholders = value.map(() => `$${paramIndex++}`).join(', ');
        clauses.push(`${key} IN (${placeholders})`);
        params.push(...value);
      } else {
        clauses.push(`${key} = $${paramIndex++}`);
        params.push(value);
      }
    }
  }
  
  return {
    clause: clauses.length > 0 ? `WHERE ${clauses.join(' AND ')}` : '',
    params,
    nextIndex: paramIndex
  };
}

/**
 * Escape identifier for safe SQL construction
 */
export function escapeIdentifier(identifier: string): string {
  return `"${identifier.replace(/"/g, '""')}"`;
}
