2308808c<PERSON>ec14e50cab2c0d8c244274
/* istanbul ignore next */
import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
function cov_2j7xd0qkwm() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\performance.ts";
  var hash = "1e3d05e3fde3f1d96426c8ae149f91eac0c9b9af";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\performance.ts",
    statementMap: {
      "0": {
        start: {
          line: 12,
          column: 46
        },
        end: {
          line: 12,
          column: 64
        }
      },
      "1": {
        start: {
          line: 14,
          column: 2
        },
        end: {
          line: 22,
          column: 20
        }
      },
      "2": {
        start: {
          line: 15,
          column: 20
        },
        end: {
          line: 17,
          column: 13
        }
      },
      "3": {
        start: {
          line: 16,
          column: 6
        },
        end: {
          line: 16,
          column: 30
        }
      },
      "4": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 21,
          column: 5
        }
      },
      "5": {
        start: {
          line: 20,
          column: 6
        },
        end: {
          line: 20,
          column: 27
        }
      },
      "6": {
        start: {
          line: 24,
          column: 2
        },
        end: {
          line: 24,
          column: 23
        }
      },
      "7": {
        start: {
          line: 32,
          column: 18
        },
        end: {
          line: 32,
          column: 36
        }
      },
      "8": {
        start: {
          line: 34,
          column: 2
        },
        end: {
          line: 42,
          column: 3
        }
      },
      "9": {
        start: {
          line: 36,
          column: 6
        },
        end: {
          line: 39,
          column: 7
        }
      },
      "10": {
        start: {
          line: 37,
          column: 8
        },
        end: {
          line: 37,
          column: 25
        }
      },
      "11": {
        start: {
          line: 38,
          column: 8
        },
        end: {
          line: 38,
          column: 36
        }
      },
      "12": {
        start: {
          line: 50,
          column: 2
        },
        end: {
          line: 50,
          column: 36
        }
      },
      "13": {
        start: {
          line: 55,
          column: 14
        },
        end: {
          line: 55,
          column: 64
        }
      },
      "14": {
        start: {
          line: 57,
          column: 2
        },
        end: {
          line: 59,
          column: 3
        }
      },
      "15": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 58,
          column: 44
        }
      },
      "16": {
        start: {
          line: 61,
          column: 2
        },
        end: {
          line: 61,
          column: 26
        }
      },
      "17": {
        start: {
          line: 66,
          column: 2
        },
        end: {
          line: 66,
          column: 41
        }
      },
      "18": {
        start: {
          line: 66,
          column: 29
        },
        end: {
          line: 66,
          column: 41
        }
      },
      "19": {
        start: {
          line: 68,
          column: 2
        },
        end: {
          line: 72,
          column: 3
        }
      },
      "20": {
        start: {
          line: 68,
          column: 15
        },
        end: {
          line: 68,
          column: 16
        }
      },
      "21": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 71,
          column: 5
        }
      },
      "22": {
        start: {
          line: 70,
          column: 6
        },
        end: {
          line: 70,
          column: 18
        }
      },
      "23": {
        start: {
          line: 74,
          column: 2
        },
        end: {
          line: 74,
          column: 13
        }
      },
      "24": {
        start: {
          line: 79,
          column: 20
        },
        end: {
          line: 79,
          column: 36
        }
      },
      "25": {
        start: {
          line: 80,
          column: 22
        },
        end: {
          line: 80,
          column: 31
        }
      },
      "26": {
        start: {
          line: 82,
          column: 2
        },
        end: {
          line: 99,
          column: 4
        }
      },
      "27": {
        start: {
          line: 83,
          column: 4
        },
        end: {
          line: 83,
          column: 28
        }
      },
      "28": {
        start: {
          line: 85,
          column: 4
        },
        end: {
          line: 98,
          column: 5
        }
      },
      "29": {
        start: {
          line: 86,
          column: 6
        },
        end: {
          line: 88,
          column: 7
        }
      },
      "30": {
        start: {
          line: 87,
          column: 8
        },
        end: {
          line: 87,
          column: 45
        }
      },
      "31": {
        start: {
          line: 90,
          column: 22
        },
        end: {
          line: 90,
          column: 39
        }
      },
      "32": {
        start: {
          line: 91,
          column: 23
        },
        end: {
          line: 91,
          column: 50
        }
      },
      "33": {
        start: {
          line: 93,
          column: 6
        },
        end: {
          line: 95,
          column: 7
        }
      },
      "34": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 94,
          column: 114
        }
      },
      "35": {
        start: {
          line: 97,
          column: 6
        },
        end: {
          line: 97,
          column: 33
        }
      },
      "36": {
        start: {
          line: 101,
          column: 2
        },
        end: {
          line: 101,
          column: 45
        }
      },
      "37": {
        start: {
          line: 108,
          column: 46
        },
        end: {
          line: 108,
          column: 61
        }
      },
      "38": {
        start: {
          line: 109,
          column: 14
        },
        end: {
          line: 109,
          column: 39
        }
      },
      "39": {
        start: {
          line: 111,
          column: 2
        },
        end: {
          line: 130,
          column: 15
        }
      },
      "40": {
        start: {
          line: 112,
          column: 20
        },
        end: {
          line: 112,
          column: 31
        }
      },
      "41": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 113,
          column: 24
        }
      },
      "42": {
        start: {
          line: 113,
          column: 18
        },
        end: {
          line: 113,
          column: 24
        }
      },
      "43": {
        start: {
          line: 115,
          column: 21
        },
        end: {
          line: 123,
          column: 5
        }
      },
      "44": {
        start: {
          line: 117,
          column: 8
        },
        end: {
          line: 117,
          column: 47
        }
      },
      "45": {
        start: {
          line: 125,
          column: 4
        },
        end: {
          line: 125,
          column: 29
        }
      },
      "46": {
        start: {
          line: 127,
          column: 4
        },
        end: {
          line: 129,
          column: 5
        }
      },
      "47": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 33
        }
      },
      "48": {
        start: {
          line: 132,
          column: 2
        },
        end: {
          line: 132,
          column: 30
        }
      },
      "49": {
        start: {
          line: 141,
          column: 36
        },
        end: {
          line: 141,
          column: 47
        }
      },
      "50": {
        start: {
          line: 143,
          column: 23
        },
        end: {
          line: 157,
          column: 53
        }
      },
      "51": {
        start: {
          line: 144,
          column: 23
        },
        end: {
          line: 144,
          column: 57
        }
      },
      "52": {
        start: {
          line: 145,
          column: 21
        },
        end: {
          line: 148,
          column: 5
        }
      },
      "53": {
        start: {
          line: 150,
          column: 4
        },
        end: {
          line: 156,
          column: 5
        }
      },
      "54": {
        start: {
          line: 159,
          column: 23
        },
        end: {
          line: 161,
          column: 8
        }
      },
      "55": {
        start: {
          line: 160,
          column: 4
        },
        end: {
          line: 160,
          column: 43
        }
      },
      "56": {
        start: {
          line: 163,
          column: 2
        },
        end: {
          line: 166,
          column: 3
        }
      },
      "57": {
        start: {
          line: 173,
          column: 28
        },
        end: {
          line: 173,
          column: 50
        }
      },
      "58": {
        start: {
          line: 174,
          column: 25
        },
        end: {
          line: 174,
          column: 58
        }
      },
      "59": {
        start: {
          line: 175,
          column: 21
        },
        end: {
          line: 175,
          column: 45
        }
      },
      "60": {
        start: {
          line: 177,
          column: 26
        },
        end: {
          line: 194,
          column: 8
        }
      },
      "61": {
        start: {
          line: 178,
          column: 4
        },
        end: {
          line: 178,
          column: 40
        }
      },
      "62": {
        start: {
          line: 180,
          column: 4
        },
        end: {
          line: 182,
          column: 5
        }
      },
      "63": {
        start: {
          line: 181,
          column: 6
        },
        end: {
          line: 181,
          column: 38
        }
      },
      "64": {
        start: {
          line: 184,
          column: 4
        },
        end: {
          line: 193,
          column: 9
        }
      },
      "65": {
        start: {
          line: 185,
          column: 6
        },
        end: {
          line: 192,
          column: 8
        }
      },
      "66": {
        start: {
          line: 186,
          column: 23
        },
        end: {
          line: 186,
          column: 32
        }
      },
      "67": {
        start: {
          line: 187,
          column: 8
        },
        end: {
          line: 189,
          column: 9
        }
      },
      "68": {
        start: {
          line: 188,
          column: 10
        },
        end: {
          line: 188,
          column: 37
        }
      },
      "69": {
        start: {
          line: 190,
          column: 8
        },
        end: {
          line: 190,
          column: 35
        }
      },
      "70": {
        start: {
          line: 191,
          column: 8
        },
        end: {
          line: 191,
          column: 23
        }
      },
      "71": {
        start: {
          line: 196,
          column: 2
        },
        end: {
          line: 202,
          column: 8
        }
      },
      "72": {
        start: {
          line: 197,
          column: 4
        },
        end: {
          line: 201,
          column: 5
        }
      },
      "73": {
        start: {
          line: 198,
          column: 6
        },
        end: {
          line: 200,
          column: 7
        }
      },
      "74": {
        start: {
          line: 199,
          column: 8
        },
        end: {
          line: 199,
          column: 40
        }
      },
      "75": {
        start: {
          line: 204,
          column: 2
        },
        end: {
          line: 204,
          column: 33
        }
      },
      "76": {
        start: {
          line: 209,
          column: 2
        },
        end: {
          line: 224,
          column: 21
        }
      },
      "77": {
        start: {
          line: 210,
          column: 4
        },
        end: {
          line: 223,
          column: 5
        }
      },
      "78": {
        start: {
          line: 211,
          column: 26
        },
        end: {
          line: 219,
          column: 7
        }
      },
      "79": {
        start: {
          line: 212,
          column: 23
        },
        end: {
          line: 212,
          column: 50
        }
      },
      "80": {
        start: {
          line: 213,
          column: 8
        },
        end: {
          line: 218,
          column: 9
        }
      },
      "81": {
        start: {
          line: 214,
          column: 10
        },
        end: {
          line: 217,
          column: 12
        }
      },
      "82": {
        start: {
          line: 221,
          column: 23
        },
        end: {
          line: 221,
          column: 53
        }
      },
      "83": {
        start: {
          line: 222,
          column: 6
        },
        end: {
          line: 222,
          column: 42
        }
      },
      "84": {
        start: {
          line: 222,
          column: 19
        },
        end: {
          line: 222,
          column: 42
        }
      },
      "85": {
        start: {
          line: 232,
          column: 2
        },
        end: {
          line: 242,
          column: 3
        }
      },
      "86": {
        start: {
          line: 234,
          column: 6
        },
        end: {
          line: 239,
          column: 7
        }
      },
      "87": {
        start: {
          line: 236,
          column: 8
        },
        end: {
          line: 236,
          column: 22
        }
      },
      "88": {
        start: {
          line: 238,
          column: 8
        },
        end: {
          line: 238,
          column: 22
        }
      },
      "89": {
        start: {
          line: 247,
          column: 24
        },
        end: {
          line: 247,
          column: 53
        }
      },
      "90": {
        start: {
          line: 248,
          column: 22
        },
        end: {
          line: 248,
          column: 31
        }
      },
      "91": {
        start: {
          line: 250,
          column: 2
        },
        end: {
          line: 268,
          column: 4
        }
      },
      "92": {
        start: {
          line: 251,
          column: 4
        },
        end: {
          line: 251,
          column: 28
        }
      },
      "93": {
        start: {
          line: 253,
          column: 4
        },
        end: {
          line: 267,
          column: 5
        }
      },
      "94": {
        start: {
          line: 254,
          column: 6
        },
        end: {
          line: 264,
          column: 7
        }
      },
      "95": {
        start: {
          line: 255,
          column: 29
        },
        end: {
          line: 257,
          column: 9
        }
      },
      "96": {
        start: {
          line: 256,
          column: 17
        },
        end: {
          line: 256,
          column: 68
        }
      },
      "97": {
        start: {
          line: 259,
          column: 8
        },
        end: {
          line: 263,
          column: 9
        }
      },
      "98": {
        start: {
          line: 260,
          column: 10
        },
        end: {
          line: 260,
          column: 100
        }
      },
      "99": {
        start: {
          line: 261,
          column: 15
        },
        end: {
          line: 263,
          column: 9
        }
      },
      "100": {
        start: {
          line: 262,
          column: 10
        },
        end: {
          line: 262,
          column: 75
        }
      },
      "101": {
        start: {
          line: 266,
          column: 6
        },
        end: {
          line: 266,
          column: 42
        }
      },
      "102": {
        start: {
          line: 270,
          column: 2
        },
        end: {
          line: 270,
          column: 28
        }
      },
      "103": {
        start: {
          line: 276,
          column: 32
        },
        end: {
          line: 308,
          column: 1
        }
      },
      "104": {
        start: {
          line: 279,
          column: 4
        },
        end: {
          line: 281,
          column: 5
        }
      },
      "105": {
        start: {
          line: 280,
          column: 6
        },
        end: {
          line: 280,
          column: 39
        }
      },
      "106": {
        start: {
          line: 286,
          column: 4
        },
        end: {
          line: 297,
          column: 5
        }
      },
      "107": {
        start: {
          line: 287,
          column: 6
        },
        end: {
          line: 287,
          column: 37
        }
      },
      "108": {
        start: {
          line: 288,
          column: 6
        },
        end: {
          line: 288,
          column: 63
        }
      },
      "109": {
        start: {
          line: 290,
          column: 22
        },
        end: {
          line: 290,
          column: 56
        }
      },
      "110": {
        start: {
          line: 291,
          column: 6
        },
        end: {
          line: 296,
          column: 7
        }
      },
      "111": {
        start: {
          line: 292,
          column: 25
        },
        end: {
          line: 292,
          column: 61
        }
      },
      "112": {
        start: {
          line: 293,
          column: 8
        },
        end: {
          line: 295,
          column: 9
        }
      },
      "113": {
        start: {
          line: 294,
          column: 10
        },
        end: {
          line: 294,
          column: 75
        }
      },
      "114": {
        start: {
          line: 302,
          column: 4
        },
        end: {
          line: 306,
          column: 5
        }
      },
      "115": {
        start: {
          line: 303,
          column: 6
        },
        end: {
          line: 303,
          column: 47
        }
      },
      "116": {
        start: {
          line: 304,
          column: 6
        },
        end: {
          line: 304,
          column: 45
        }
      },
      "117": {
        start: {
          line: 305,
          column: 6
        },
        end: {
          line: 305,
          column: 39
        }
      }
    },
    fnMap: {
      "0": {
        name: "useDebounce",
        decl: {
          start: {
            line: 11,
            column: 16
          },
          end: {
            line: 11,
            column: 27
          }
        },
        loc: {
          start: {
            line: 11,
            column: 59
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 11
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 14,
            column: 12
          },
          end: {
            line: 14,
            column: 13
          }
        },
        loc: {
          start: {
            line: 14,
            column: 18
          },
          end: {
            line: 22,
            column: 3
          }
        },
        line: 14
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 15,
            column: 31
          },
          end: {
            line: 15,
            column: 32
          }
        },
        loc: {
          start: {
            line: 15,
            column: 37
          },
          end: {
            line: 17,
            column: 5
          }
        },
        line: 15
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 19,
            column: 11
          },
          end: {
            line: 19,
            column: 12
          }
        },
        loc: {
          start: {
            line: 19,
            column: 17
          },
          end: {
            line: 21,
            column: 5
          }
        },
        line: 19
      },
      "4": {
        name: "useThrottle",
        decl: {
          start: {
            line: 28,
            column: 16
          },
          end: {
            line: 28,
            column: 27
          }
        },
        loc: {
          start: {
            line: 31,
            column: 5
          },
          end: {
            line: 43,
            column: 1
          }
        },
        line: 31
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 35,
            column: 5
          },
          end: {
            line: 35,
            column: 6
          }
        },
        loc: {
          start: {
            line: 35,
            column: 18
          },
          end: {
            line: 40,
            column: 5
          }
        },
        line: 35
      },
      "6": {
        name: "useOptimizedCallback",
        decl: {
          start: {
            line: 46,
            column: 16
          },
          end: {
            line: 46,
            column: 36
          }
        },
        loc: {
          start: {
            line: 49,
            column: 5
          },
          end: {
            line: 51,
            column: 1
          }
        },
        line: 49
      },
      "7": {
        name: "useDeepMemo",
        decl: {
          start: {
            line: 54,
            column: 16
          },
          end: {
            line: 54,
            column: 27
          }
        },
        loc: {
          start: {
            line: 54,
            column: 80
          },
          end: {
            line: 62,
            column: 1
          }
        },
        line: 54
      },
      "8": {
        name: "areEqual",
        decl: {
          start: {
            line: 65,
            column: 9
          },
          end: {
            line: 65,
            column: 17
          }
        },
        loc: {
          start: {
            line: 65,
            column: 77
          },
          end: {
            line: 75,
            column: 1
          }
        },
        line: 65
      },
      "9": {
        name: "usePerformanceMonitor",
        decl: {
          start: {
            line: 78,
            column: 16
          },
          end: {
            line: 78,
            column: 37
          }
        },
        loc: {
          start: {
            line: 78,
            column: 52
          },
          end: {
            line: 102,
            column: 1
          }
        },
        line: 78
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 82,
            column: 12
          },
          end: {
            line: 82,
            column: 13
          }
        },
        loc: {
          start: {
            line: 82,
            column: 18
          },
          end: {
            line: 99,
            column: 3
          }
        },
        line: 82
      },
      "11": {
        name: "useIntersectionObserver",
        decl: {
          start: {
            line: 105,
            column: 16
          },
          end: {
            line: 105,
            column: 39
          }
        },
        loc: {
          start: {
            line: 107,
            column: 43
          },
          end: {
            line: 133,
            column: 1
          }
        },
        line: 107
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 111,
            column: 12
          },
          end: {
            line: 111,
            column: 13
          }
        },
        loc: {
          start: {
            line: 111,
            column: 18
          },
          end: {
            line: 130,
            column: 3
          }
        },
        line: 111
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 116,
            column: 6
          },
          end: {
            line: 116,
            column: 7
          }
        },
        loc: {
          start: {
            line: 116,
            column: 19
          },
          end: {
            line: 118,
            column: 7
          }
        },
        line: 116
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 127,
            column: 11
          },
          end: {
            line: 127,
            column: 12
          }
        },
        loc: {
          start: {
            line: 127,
            column: 17
          },
          end: {
            line: 129,
            column: 5
          }
        },
        line: 127
      },
      "15": {
        name: "useVirtualScrolling",
        decl: {
          start: {
            line: 136,
            column: 16
          },
          end: {
            line: 136,
            column: 35
          }
        },
        loc: {
          start: {
            line: 140,
            column: 2
          },
          end: {
            line: 167,
            column: 1
          }
        },
        line: 140
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 143,
            column: 31
          },
          end: {
            line: 143,
            column: 32
          }
        },
        loc: {
          start: {
            line: 143,
            column: 37
          },
          end: {
            line: 157,
            column: 3
          }
        },
        line: 143
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 159,
            column: 35
          },
          end: {
            line: 159,
            column: 36
          }
        },
        loc: {
          start: {
            line: 159,
            column: 70
          },
          end: {
            line: 161,
            column: 3
          }
        },
        line: 159
      },
      "18": {
        name: "useBatchedState",
        decl: {
          start: {
            line: 170,
            column: 16
          },
          end: {
            line: 170,
            column: 31
          }
        },
        loc: {
          start: {
            line: 172,
            column: 42
          },
          end: {
            line: 205,
            column: 1
          }
        },
        line: 172
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 177,
            column: 38
          },
          end: {
            line: 177,
            column: 39
          }
        },
        loc: {
          start: {
            line: 177,
            column: 67
          },
          end: {
            line: 194,
            column: 3
          }
        },
        line: 177
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 184,
            column: 36
          },
          end: {
            line: 184,
            column: 37
          }
        },
        loc: {
          start: {
            line: 184,
            column: 42
          },
          end: {
            line: 193,
            column: 5
          }
        },
        line: 184
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 185,
            column: 15
          },
          end: {
            line: 185,
            column: 16
          }
        },
        loc: {
          start: {
            line: 185,
            column: 30
          },
          end: {
            line: 192,
            column: 7
          }
        },
        line: 185
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 196,
            column: 12
          },
          end: {
            line: 196,
            column: 13
          }
        },
        loc: {
          start: {
            line: 196,
            column: 18
          },
          end: {
            line: 202,
            column: 3
          }
        },
        line: 196
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 197,
            column: 11
          },
          end: {
            line: 197,
            column: 12
          }
        },
        loc: {
          start: {
            line: 197,
            column: 17
          },
          end: {
            line: 201,
            column: 5
          }
        },
        line: 197
      },
      "24": {
        name: "useMemoryMonitor",
        decl: {
          start: {
            line: 208,
            column: 16
          },
          end: {
            line: 208,
            column: 32
          }
        },
        loc: {
          start: {
            line: 208,
            column: 56
          },
          end: {
            line: 225,
            column: 1
          }
        },
        line: 208
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 209,
            column: 12
          },
          end: {
            line: 209,
            column: 13
          }
        },
        loc: {
          start: {
            line: 209,
            column: 18
          },
          end: {
            line: 224,
            column: 3
          }
        },
        line: 209
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 211,
            column: 26
          },
          end: {
            line: 211,
            column: 27
          }
        },
        loc: {
          start: {
            line: 211,
            column: 32
          },
          end: {
            line: 219,
            column: 7
          }
        },
        line: 211
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 222,
            column: 13
          },
          end: {
            line: 222,
            column: 14
          }
        },
        loc: {
          start: {
            line: 222,
            column: 19
          },
          end: {
            line: 222,
            column: 42
          }
        },
        line: 222
      },
      "28": {
        name: "createOptimizedEventHandler",
        decl: {
          start: {
            line: 228,
            column: 16
          },
          end: {
            line: 228,
            column: 43
          }
        },
        loc: {
          start: {
            line: 231,
            column: 2
          },
          end: {
            line: 243,
            column: 1
          }
        },
        line: 231
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 233,
            column: 4
          },
          end: {
            line: 233,
            column: 5
          }
        },
        loc: {
          start: {
            line: 233,
            column: 18
          },
          end: {
            line: 240,
            column: 5
          }
        },
        line: 233
      },
      "30": {
        name: "useRenderOptimization",
        decl: {
          start: {
            line: 246,
            column: 16
          },
          end: {
            line: 246,
            column: 37
          }
        },
        loc: {
          start: {
            line: 246,
            column: 89
          },
          end: {
            line: 271,
            column: 1
          }
        },
        line: 246
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 250,
            column: 12
          },
          end: {
            line: 250,
            column: 13
          }
        },
        loc: {
          start: {
            line: 250,
            column: 18
          },
          end: {
            line: 268,
            column: 3
          }
        },
        line: 250
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 256,
            column: 10
          },
          end: {
            line: 256,
            column: 11
          }
        },
        loc: {
          start: {
            line: 256,
            column: 17
          },
          end: {
            line: 256,
            column: 68
          }
        },
        line: 256
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 278,
            column: 8
          },
          end: {
            line: 278,
            column: 9
          }
        },
        loc: {
          start: {
            line: 278,
            column: 26
          },
          end: {
            line: 282,
            column: 3
          }
        },
        line: 278
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 285,
            column: 11
          },
          end: {
            line: 285,
            column: 12
          }
        },
        loc: {
          start: {
            line: 285,
            column: 29
          },
          end: {
            line: 298,
            column: 3
          }
        },
        line: 285
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 301,
            column: 9
          },
          end: {
            line: 301,
            column: 10
          }
        },
        loc: {
          start: {
            line: 301,
            column: 27
          },
          end: {
            line: 307,
            column: 3
          }
        },
        line: 301
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 36,
            column: 6
          },
          end: {
            line: 39,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 36,
            column: 6
          },
          end: {
            line: 39,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 36
      },
      "1": {
        loc: {
          start: {
            line: 57,
            column: 2
          },
          end: {
            line: 59,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 57,
            column: 2
          },
          end: {
            line: 59,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 57
      },
      "2": {
        loc: {
          start: {
            line: 57,
            column: 6
          },
          end: {
            line: 57,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 57,
            column: 6
          },
          end: {
            line: 57,
            column: 18
          }
        }, {
          start: {
            line: 57,
            column: 22
          },
          end: {
            line: 57,
            column: 55
          }
        }],
        line: 57
      },
      "3": {
        loc: {
          start: {
            line: 66,
            column: 2
          },
          end: {
            line: 66,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 66,
            column: 2
          },
          end: {
            line: 66,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 66
      },
      "4": {
        loc: {
          start: {
            line: 69,
            column: 4
          },
          end: {
            line: 71,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 69,
            column: 4
          },
          end: {
            line: 71,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 69
      },
      "5": {
        loc: {
          start: {
            line: 85,
            column: 4
          },
          end: {
            line: 98,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 85,
            column: 4
          },
          end: {
            line: 98,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 85
      },
      "6": {
        loc: {
          start: {
            line: 86,
            column: 6
          },
          end: {
            line: 88,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 86,
            column: 6
          },
          end: {
            line: 88,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 86
      },
      "7": {
        loc: {
          start: {
            line: 93,
            column: 6
          },
          end: {
            line: 95,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 93,
            column: 6
          },
          end: {
            line: 95,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 93
      },
      "8": {
        loc: {
          start: {
            line: 106,
            column: 2
          },
          end: {
            line: 106,
            column: 40
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 106,
            column: 38
          },
          end: {
            line: 106,
            column: 40
          }
        }],
        line: 106
      },
      "9": {
        loc: {
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 113,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 113,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 113
      },
      "10": {
        loc: {
          start: {
            line: 180,
            column: 4
          },
          end: {
            line: 182,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 180,
            column: 4
          },
          end: {
            line: 182,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 180
      },
      "11": {
        loc: {
          start: {
            line: 198,
            column: 6
          },
          end: {
            line: 200,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 198,
            column: 6
          },
          end: {
            line: 200,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 198
      },
      "12": {
        loc: {
          start: {
            line: 210,
            column: 4
          },
          end: {
            line: 223,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 210,
            column: 4
          },
          end: {
            line: 223,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 210
      },
      "13": {
        loc: {
          start: {
            line: 210,
            column: 8
          },
          end: {
            line: 210,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 210,
            column: 8
          },
          end: {
            line: 210,
            column: 46
          }
        }, {
          start: {
            line: 210,
            column: 50
          },
          end: {
            line: 210,
            column: 73
          }
        }],
        line: 210
      },
      "14": {
        loc: {
          start: {
            line: 213,
            column: 8
          },
          end: {
            line: 218,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 213,
            column: 8
          },
          end: {
            line: 218,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 213
      },
      "15": {
        loc: {
          start: {
            line: 230,
            column: 2
          },
          end: {
            line: 230,
            column: 56
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 230,
            column: 54
          },
          end: {
            line: 230,
            column: 56
          }
        }],
        line: 230
      },
      "16": {
        loc: {
          start: {
            line: 234,
            column: 6
          },
          end: {
            line: 239,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 234,
            column: 6
          },
          end: {
            line: 239,
            column: 7
          }
        }, {
          start: {
            line: 237,
            column: 13
          },
          end: {
            line: 239,
            column: 7
          }
        }],
        line: 234
      },
      "17": {
        loc: {
          start: {
            line: 253,
            column: 4
          },
          end: {
            line: 267,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 253,
            column: 4
          },
          end: {
            line: 267,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 253
      },
      "18": {
        loc: {
          start: {
            line: 254,
            column: 6
          },
          end: {
            line: 264,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 254,
            column: 6
          },
          end: {
            line: 264,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 254
      },
      "19": {
        loc: {
          start: {
            line: 259,
            column: 8
          },
          end: {
            line: 263,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 259,
            column: 8
          },
          end: {
            line: 263,
            column: 9
          }
        }, {
          start: {
            line: 261,
            column: 15
          },
          end: {
            line: 263,
            column: 9
          }
        }],
        line: 259
      },
      "20": {
        loc: {
          start: {
            line: 261,
            column: 15
          },
          end: {
            line: 263,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 261,
            column: 15
          },
          end: {
            line: 263,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 261
      },
      "21": {
        loc: {
          start: {
            line: 279,
            column: 4
          },
          end: {
            line: 281,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 279,
            column: 4
          },
          end: {
            line: 281,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 279
      },
      "22": {
        loc: {
          start: {
            line: 279,
            column: 8
          },
          end: {
            line: 279,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 279,
            column: 8
          },
          end: {
            line: 279,
            column: 42
          }
        }, {
          start: {
            line: 279,
            column: 46
          },
          end: {
            line: 279,
            column: 62
          }
        }],
        line: 279
      },
      "23": {
        loc: {
          start: {
            line: 286,
            column: 4
          },
          end: {
            line: 297,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 286,
            column: 4
          },
          end: {
            line: 297,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 286
      },
      "24": {
        loc: {
          start: {
            line: 286,
            column: 8
          },
          end: {
            line: 286,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 286,
            column: 8
          },
          end: {
            line: 286,
            column: 42
          }
        }, {
          start: {
            line: 286,
            column: 46
          },
          end: {
            line: 286,
            column: 62
          }
        }, {
          start: {
            line: 286,
            column: 66
          },
          end: {
            line: 286,
            column: 85
          }
        }],
        line: 286
      },
      "25": {
        loc: {
          start: {
            line: 291,
            column: 6
          },
          end: {
            line: 296,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 291,
            column: 6
          },
          end: {
            line: 296,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 291
      },
      "26": {
        loc: {
          start: {
            line: 293,
            column: 8
          },
          end: {
            line: 295,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 293,
            column: 8
          },
          end: {
            line: 295,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 293
      },
      "27": {
        loc: {
          start: {
            line: 302,
            column: 4
          },
          end: {
            line: 306,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 302,
            column: 4
          },
          end: {
            line: 306,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 302
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1e3d05e3fde3f1d96426c8ae149f91eac0c9b9af"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2j7xd0qkwm = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2j7xd0qkwm();
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Performance Optimization Utilities
 * 
 * This module provides utilities for optimizing React performance including
 * memoization helpers, debouncing, throttling, and performance monitoring.
 */

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

// Debounce hook for expensive operations
export function useDebounce(value, delay) {
  /* istanbul ignore next */
  cov_2j7xd0qkwm().f[0]++;
  const [debouncedValue, setDebouncedValue] =
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().s[0]++, useState(value));
  /* istanbul ignore next */
  cov_2j7xd0qkwm().s[1]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_2j7xd0qkwm().f[1]++;
    const handler =
    /* istanbul ignore next */
    (cov_2j7xd0qkwm().s[2]++, setTimeout(() => {
      /* istanbul ignore next */
      cov_2j7xd0qkwm().f[2]++;
      cov_2j7xd0qkwm().s[3]++;
      setDebouncedValue(value);
    }, delay));
    /* istanbul ignore next */
    cov_2j7xd0qkwm().s[4]++;
    return () => {
      /* istanbul ignore next */
      cov_2j7xd0qkwm().f[3]++;
      cov_2j7xd0qkwm().s[5]++;
      clearTimeout(handler);
    };
  }, [value, delay]);
  /* istanbul ignore next */
  cov_2j7xd0qkwm().s[6]++;
  return debouncedValue;
}

// Throttle hook for frequent events
export function useThrottle(callback, delay) {
  /* istanbul ignore next */
  cov_2j7xd0qkwm().f[4]++;
  const lastRun =
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().s[7]++, useRef(Date.now()));
  /* istanbul ignore next */
  cov_2j7xd0qkwm().s[8]++;
  return useCallback((...args) => {
    /* istanbul ignore next */
    cov_2j7xd0qkwm().f[5]++;
    cov_2j7xd0qkwm().s[9]++;
    if (Date.now() - lastRun.current >= delay) {
      /* istanbul ignore next */
      cov_2j7xd0qkwm().b[0][0]++;
      cov_2j7xd0qkwm().s[10]++;
      callback(...args);
      /* istanbul ignore next */
      cov_2j7xd0qkwm().s[11]++;
      lastRun.current = Date.now();
    } else
    /* istanbul ignore next */
    {
      cov_2j7xd0qkwm().b[0][1]++;
    }
  }, [callback, delay]);
}

// Memoized callback with dependency optimization
export function useOptimizedCallback(callback, deps) {
  /* istanbul ignore next */
  cov_2j7xd0qkwm().f[6]++;
  cov_2j7xd0qkwm().s[12]++;
  return useCallback(callback, deps);
}

// Memoized value with deep comparison
export function useDeepMemo(factory, deps) {
  /* istanbul ignore next */
  cov_2j7xd0qkwm().f[7]++;
  const ref =
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().s[13]++, useRef());
  /* istanbul ignore next */
  cov_2j7xd0qkwm().s[14]++;
  if (
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().b[2][0]++, !ref.current) ||
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().b[2][1]++, !areEqual(ref.current.deps, deps))) {
    /* istanbul ignore next */
    cov_2j7xd0qkwm().b[1][0]++;
    cov_2j7xd0qkwm().s[15]++;
    ref.current = {
      deps,
      value: factory()
    };
  } else
  /* istanbul ignore next */
  {
    cov_2j7xd0qkwm().b[1][1]++;
  }
  cov_2j7xd0qkwm().s[16]++;
  return ref.current.value;
}

// Deep equality check for dependencies
function areEqual(a, b) {
  /* istanbul ignore next */
  cov_2j7xd0qkwm().f[8]++;
  cov_2j7xd0qkwm().s[17]++;
  if (a.length !== b.length) {
    /* istanbul ignore next */
    cov_2j7xd0qkwm().b[3][0]++;
    cov_2j7xd0qkwm().s[18]++;
    return false;
  } else
  /* istanbul ignore next */
  {
    cov_2j7xd0qkwm().b[3][1]++;
  }
  cov_2j7xd0qkwm().s[19]++;
  for (let i =
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().s[20]++, 0); i < a.length; i++) {
    /* istanbul ignore next */
    cov_2j7xd0qkwm().s[21]++;
    if (!Object.is(a[i], b[i])) {
      /* istanbul ignore next */
      cov_2j7xd0qkwm().b[4][0]++;
      cov_2j7xd0qkwm().s[22]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_2j7xd0qkwm().b[4][1]++;
    }
  }
  /* istanbul ignore next */
  cov_2j7xd0qkwm().s[23]++;
  return true;
}

// Performance monitoring hook
export function usePerformanceMonitor(name) {
  /* istanbul ignore next */
  cov_2j7xd0qkwm().f[9]++;
  const startTime =
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().s[24]++, useRef());
  const renderCount =
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().s[25]++, useRef(0));
  /* istanbul ignore next */
  cov_2j7xd0qkwm().s[26]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_2j7xd0qkwm().f[10]++;
    cov_2j7xd0qkwm().s[27]++;
    renderCount.current += 1;
    /* istanbul ignore next */
    cov_2j7xd0qkwm().s[28]++;
    if (process.env.NODE_ENV === 'development') {
      /* istanbul ignore next */
      cov_2j7xd0qkwm().b[5][0]++;
      cov_2j7xd0qkwm().s[29]++;
      if (!startTime.current) {
        /* istanbul ignore next */
        cov_2j7xd0qkwm().b[6][0]++;
        cov_2j7xd0qkwm().s[30]++;
        startTime.current = performance.now();
      } else
      /* istanbul ignore next */
      {
        cov_2j7xd0qkwm().b[6][1]++;
      }
      const endTime =
      /* istanbul ignore next */
      (cov_2j7xd0qkwm().s[31]++, performance.now());
      const duration =
      /* istanbul ignore next */
      (cov_2j7xd0qkwm().s[32]++, endTime - startTime.current);
      /* istanbul ignore next */
      cov_2j7xd0qkwm().s[33]++;
      if (duration > 16) {
        /* istanbul ignore next */
        cov_2j7xd0qkwm().b[7][0]++;
        cov_2j7xd0qkwm().s[34]++;
        // More than one frame (16ms)
        console.warn(`Slow render detected in ${name}: ${duration.toFixed(2)}ms (render #${renderCount.current})`);
      } else
      /* istanbul ignore next */
      {
        cov_2j7xd0qkwm().b[7][1]++;
      }
      cov_2j7xd0qkwm().s[35]++;
      startTime.current = endTime;
    } else
    /* istanbul ignore next */
    {
      cov_2j7xd0qkwm().b[5][1]++;
    }
  });
  /* istanbul ignore next */
  cov_2j7xd0qkwm().s[36]++;
  return {
    renderCount: renderCount.current
  };
}

// Intersection Observer hook for lazy loading
export function useIntersectionObserver(options =
/* istanbul ignore next */
(cov_2j7xd0qkwm().b[8][0]++, {})) {
  /* istanbul ignore next */
  cov_2j7xd0qkwm().f[11]++;
  const [isIntersecting, setIsIntersecting] =
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().s[37]++, useState(false));
  const ref =
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().s[38]++, useRef(null));
  /* istanbul ignore next */
  cov_2j7xd0qkwm().s[39]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_2j7xd0qkwm().f[12]++;
    const element =
    /* istanbul ignore next */
    (cov_2j7xd0qkwm().s[40]++, ref.current);
    /* istanbul ignore next */
    cov_2j7xd0qkwm().s[41]++;
    if (!element) {
      /* istanbul ignore next */
      cov_2j7xd0qkwm().b[9][0]++;
      cov_2j7xd0qkwm().s[42]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_2j7xd0qkwm().b[9][1]++;
    }
    const observer =
    /* istanbul ignore next */
    (cov_2j7xd0qkwm().s[43]++, new IntersectionObserver(([entry]) => {
      /* istanbul ignore next */
      cov_2j7xd0qkwm().f[13]++;
      cov_2j7xd0qkwm().s[44]++;
      setIsIntersecting(entry.isIntersecting);
    },
    /* istanbul ignore next */
    _objectSpread({
      threshold: 0.1
    }, options)));
    /* istanbul ignore next */
    cov_2j7xd0qkwm().s[45]++;
    observer.observe(element);
    /* istanbul ignore next */
    cov_2j7xd0qkwm().s[46]++;
    return () => {
      /* istanbul ignore next */
      cov_2j7xd0qkwm().f[14]++;
      cov_2j7xd0qkwm().s[47]++;
      observer.unobserve(element);
    };
  }, [options]);
  /* istanbul ignore next */
  cov_2j7xd0qkwm().s[48]++;
  return [ref, isIntersecting];
}

// Virtual scrolling hook for large lists
export function useVirtualScrolling(items, itemHeight, containerHeight) {
  /* istanbul ignore next */
  cov_2j7xd0qkwm().f[15]++;
  const [scrollTop, setScrollTop] =
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().s[49]++, useState(0));
  const visibleItems =
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().s[50]++, useMemo(() => {
    /* istanbul ignore next */
    cov_2j7xd0qkwm().f[16]++;
    const startIndex =
    /* istanbul ignore next */
    (cov_2j7xd0qkwm().s[51]++, Math.floor(scrollTop / itemHeight));
    const endIndex =
    /* istanbul ignore next */
    (cov_2j7xd0qkwm().s[52]++, Math.min(startIndex + Math.ceil(containerHeight / itemHeight) + 1, items.length));
    /* istanbul ignore next */
    cov_2j7xd0qkwm().s[53]++;
    return {
      startIndex,
      endIndex,
      items: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight
    };
  }, [items, itemHeight, containerHeight, scrollTop]));
  const handleScroll =
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().s[54]++, useCallback(e => {
    /* istanbul ignore next */
    cov_2j7xd0qkwm().f[17]++;
    cov_2j7xd0qkwm().s[55]++;
    setScrollTop(e.currentTarget.scrollTop);
  }, []));
  /* istanbul ignore next */
  cov_2j7xd0qkwm().s[56]++;
  return /* istanbul ignore next */_objectSpread(_objectSpread({}, visibleItems), {}, {
    handleScroll
  });
}

// Optimized state updater that batches updates
export function useBatchedState(initialState) {
  /* istanbul ignore next */
  cov_2j7xd0qkwm().f[18]++;
  const [state, setState] =
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().s[57]++, useState(initialState));
  const pendingUpdates =
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().s[58]++, useRef([]));
  const timeoutRef =
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().s[59]++, useRef());
  const batchedSetState =
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().s[60]++, useCallback(updater => {
    /* istanbul ignore next */
    cov_2j7xd0qkwm().f[19]++;
    cov_2j7xd0qkwm().s[61]++;
    pendingUpdates.current.push(updater);
    /* istanbul ignore next */
    cov_2j7xd0qkwm().s[62]++;
    if (timeoutRef.current) {
      /* istanbul ignore next */
      cov_2j7xd0qkwm().b[10][0]++;
      cov_2j7xd0qkwm().s[63]++;
      clearTimeout(timeoutRef.current);
    } else
    /* istanbul ignore next */
    {
      cov_2j7xd0qkwm().b[10][1]++;
    }
    cov_2j7xd0qkwm().s[64]++;
    timeoutRef.current = setTimeout(() => {
      /* istanbul ignore next */
      cov_2j7xd0qkwm().f[20]++;
      cov_2j7xd0qkwm().s[65]++;
      setState(prevState => {
        /* istanbul ignore next */
        cov_2j7xd0qkwm().f[21]++;
        let newState =
        /* istanbul ignore next */
        (cov_2j7xd0qkwm().s[66]++, prevState);
        /* istanbul ignore next */
        cov_2j7xd0qkwm().s[67]++;
        for (const update of pendingUpdates.current) {
          /* istanbul ignore next */
          cov_2j7xd0qkwm().s[68]++;
          newState = update(newState);
        }
        /* istanbul ignore next */
        cov_2j7xd0qkwm().s[69]++;
        pendingUpdates.current = [];
        /* istanbul ignore next */
        cov_2j7xd0qkwm().s[70]++;
        return newState;
      });
    }, 0);
  }, []));
  /* istanbul ignore next */
  cov_2j7xd0qkwm().s[71]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_2j7xd0qkwm().f[22]++;
    cov_2j7xd0qkwm().s[72]++;
    return () => {
      /* istanbul ignore next */
      cov_2j7xd0qkwm().f[23]++;
      cov_2j7xd0qkwm().s[73]++;
      if (timeoutRef.current) {
        /* istanbul ignore next */
        cov_2j7xd0qkwm().b[11][0]++;
        cov_2j7xd0qkwm().s[74]++;
        clearTimeout(timeoutRef.current);
      } else
      /* istanbul ignore next */
      {
        cov_2j7xd0qkwm().b[11][1]++;
      }
    };
  }, []);
  /* istanbul ignore next */
  cov_2j7xd0qkwm().s[75]++;
  return [state, batchedSetState];
}

// Memory usage monitoring
export function useMemoryMonitor(componentName) {
  /* istanbul ignore next */
  cov_2j7xd0qkwm().f[24]++;
  cov_2j7xd0qkwm().s[76]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_2j7xd0qkwm().f[25]++;
    cov_2j7xd0qkwm().s[77]++;
    if (
    /* istanbul ignore next */
    (cov_2j7xd0qkwm().b[13][0]++, process.env.NODE_ENV === 'development') &&
    /* istanbul ignore next */
    (cov_2j7xd0qkwm().b[13][1]++, 'memory' in performance)) {
      /* istanbul ignore next */
      cov_2j7xd0qkwm().b[12][0]++;
      cov_2j7xd0qkwm().s[78]++;
      const checkMemory = () => {
        /* istanbul ignore next */
        cov_2j7xd0qkwm().f[26]++;
        const memory =
        /* istanbul ignore next */
        (cov_2j7xd0qkwm().s[79]++, performance.memory);
        /* istanbul ignore next */
        cov_2j7xd0qkwm().s[80]++;
        if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
          /* istanbul ignore next */
          cov_2j7xd0qkwm().b[14][0]++;
          cov_2j7xd0qkwm().s[81]++;
          console.warn(`High memory usage detected in ${componentName}:`, {
            used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
            limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`
          });
        } else
        /* istanbul ignore next */
        {
          cov_2j7xd0qkwm().b[14][1]++;
        }
      };
      const interval =
      /* istanbul ignore next */
      (cov_2j7xd0qkwm().s[82]++, setInterval(checkMemory, 5000));
      /* istanbul ignore next */
      cov_2j7xd0qkwm().s[83]++;
      return () => {
        /* istanbul ignore next */
        cov_2j7xd0qkwm().f[27]++;
        cov_2j7xd0qkwm().s[84]++;
        return clearInterval(interval);
      };
    } else
    /* istanbul ignore next */
    {
      cov_2j7xd0qkwm().b[12][1]++;
    }
  }, [componentName]);
}

// Optimized event handler creator
export function createOptimizedEventHandler(handler, options =
/* istanbul ignore next */
(cov_2j7xd0qkwm().b[15][0]++, {})) {
  /* istanbul ignore next */
  cov_2j7xd0qkwm().f[28]++;
  cov_2j7xd0qkwm().s[85]++;
  return useCallback(event => {
    /* istanbul ignore next */
    cov_2j7xd0qkwm().f[29]++;
    cov_2j7xd0qkwm().s[86]++;
    if (options.passive !== false) {
      /* istanbul ignore next */
      cov_2j7xd0qkwm().b[16][0]++;
      cov_2j7xd0qkwm().s[87]++;
      // For passive events, we can optimize by not calling preventDefault
      handler(event);
    } else {
      /* istanbul ignore next */
      cov_2j7xd0qkwm().b[16][1]++;
      cov_2j7xd0qkwm().s[88]++;
      handler(event);
    }
  }, [handler, options]);
}

// Component render optimization checker
export function useRenderOptimization(componentName, props) {
  /* istanbul ignore next */
  cov_2j7xd0qkwm().f[30]++;
  const previousProps =
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().s[89]++, useRef());
  const renderCount =
  /* istanbul ignore next */
  (cov_2j7xd0qkwm().s[90]++, useRef(0));
  /* istanbul ignore next */
  cov_2j7xd0qkwm().s[91]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_2j7xd0qkwm().f[31]++;
    cov_2j7xd0qkwm().s[92]++;
    renderCount.current += 1;
    /* istanbul ignore next */
    cov_2j7xd0qkwm().s[93]++;
    if (process.env.NODE_ENV === 'development') {
      /* istanbul ignore next */
      cov_2j7xd0qkwm().b[17][0]++;
      cov_2j7xd0qkwm().s[94]++;
      if (previousProps.current) {
        /* istanbul ignore next */
        cov_2j7xd0qkwm().b[18][0]++;
        const changedProps =
        /* istanbul ignore next */
        (cov_2j7xd0qkwm().s[95]++, Object.keys(props).filter(key => {
          /* istanbul ignore next */
          cov_2j7xd0qkwm().f[32]++;
          cov_2j7xd0qkwm().s[96]++;
          return !Object.is(props[key], previousProps.current[key]);
        }));
        /* istanbul ignore next */
        cov_2j7xd0qkwm().s[97]++;
        if (changedProps.length === 0) {
          /* istanbul ignore next */
          cov_2j7xd0qkwm().b[19][0]++;
          cov_2j7xd0qkwm().s[98]++;
          console.warn(`Unnecessary re-render in ${componentName} (render #${renderCount.current})`);
        } else {
          /* istanbul ignore next */
          cov_2j7xd0qkwm().b[19][1]++;
          cov_2j7xd0qkwm().s[99]++;
          if (changedProps.length > 0) {
            /* istanbul ignore next */
            cov_2j7xd0qkwm().b[20][0]++;
            cov_2j7xd0qkwm().s[100]++;
            console.log(`${componentName} re-rendered due to:`, changedProps);
          } else
          /* istanbul ignore next */
          {
            cov_2j7xd0qkwm().b[20][1]++;
          }
        }
      } else
      /* istanbul ignore next */
      {
        cov_2j7xd0qkwm().b[18][1]++;
      }
      cov_2j7xd0qkwm().s[101]++;
      previousProps.current =
      /* istanbul ignore next */
      _objectSpread({}, props);
    } else
    /* istanbul ignore next */
    {
      cov_2j7xd0qkwm().b[17][1]++;
    }
  });
  /* istanbul ignore next */
  cov_2j7xd0qkwm().s[102]++;
  return renderCount.current;
}

// Performance timing utilities
export const performanceUtils =
/* istanbul ignore next */
(cov_2j7xd0qkwm().s[103]++, {
  // Mark the start of a performance measurement
  mark: name => {
    /* istanbul ignore next */
    cov_2j7xd0qkwm().f[33]++;
    cov_2j7xd0qkwm().s[104]++;
    if (
    /* istanbul ignore next */
    (cov_2j7xd0qkwm().b[22][0]++, typeof performance !== 'undefined') &&
    /* istanbul ignore next */
    (cov_2j7xd0qkwm().b[22][1]++, performance.mark)) {
      /* istanbul ignore next */
      cov_2j7xd0qkwm().b[21][0]++;
      cov_2j7xd0qkwm().s[105]++;
      performance.mark(`${name}-start`);
    } else
    /* istanbul ignore next */
    {
      cov_2j7xd0qkwm().b[21][1]++;
    }
  },
  // Mark the end and measure performance
  measure: name => {
    /* istanbul ignore next */
    cov_2j7xd0qkwm().f[34]++;
    cov_2j7xd0qkwm().s[106]++;
    if (
    /* istanbul ignore next */
    (cov_2j7xd0qkwm().b[24][0]++, typeof performance !== 'undefined') &&
    /* istanbul ignore next */
    (cov_2j7xd0qkwm().b[24][1]++, performance.mark) &&
    /* istanbul ignore next */
    (cov_2j7xd0qkwm().b[24][2]++, performance.measure)) {
      /* istanbul ignore next */
      cov_2j7xd0qkwm().b[23][0]++;
      cov_2j7xd0qkwm().s[107]++;
      performance.mark(`${name}-end`);
      /* istanbul ignore next */
      cov_2j7xd0qkwm().s[108]++;
      performance.measure(name, `${name}-start`, `${name}-end`);
      const entries =
      /* istanbul ignore next */
      (cov_2j7xd0qkwm().s[109]++, performance.getEntriesByName(name));
      /* istanbul ignore next */
      cov_2j7xd0qkwm().s[110]++;
      if (entries.length > 0) {
        /* istanbul ignore next */
        cov_2j7xd0qkwm().b[25][0]++;
        const duration =
        /* istanbul ignore next */
        (cov_2j7xd0qkwm().s[111]++, entries[entries.length - 1].duration);
        /* istanbul ignore next */
        cov_2j7xd0qkwm().s[112]++;
        if (duration > 100) {
          /* istanbul ignore next */
          cov_2j7xd0qkwm().b[26][0]++;
          cov_2j7xd0qkwm().s[113]++;
          // Log if operation takes more than 100ms
          console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`);
        } else
        /* istanbul ignore next */
        {
          cov_2j7xd0qkwm().b[26][1]++;
        }
      } else
      /* istanbul ignore next */
      {
        cov_2j7xd0qkwm().b[25][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_2j7xd0qkwm().b[23][1]++;
    }
  },
  // Clear performance marks and measures
  clear: name => {
    /* istanbul ignore next */
    cov_2j7xd0qkwm().f[35]++;
    cov_2j7xd0qkwm().s[114]++;
    if (typeof performance !== 'undefined') {
      /* istanbul ignore next */
      cov_2j7xd0qkwm().b[27][0]++;
      cov_2j7xd0qkwm().s[115]++;
      performance.clearMarks?.(`${name}-start`);
      /* istanbul ignore next */
      cov_2j7xd0qkwm().s[116]++;
      performance.clearMarks?.(`${name}-end`);
      /* istanbul ignore next */
      cov_2j7xd0qkwm().s[117]++;
      performance.clearMeasures?.(name);
    } else
    /* istanbul ignore next */
    {
      cov_2j7xd0qkwm().b[27][1]++;
    }
  }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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