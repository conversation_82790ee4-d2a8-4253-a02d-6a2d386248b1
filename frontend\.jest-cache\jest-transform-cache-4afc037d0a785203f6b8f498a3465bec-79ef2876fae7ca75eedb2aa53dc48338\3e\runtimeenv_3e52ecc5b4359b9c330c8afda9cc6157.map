{"version": 3, "names": ["cov_2c8fkmz668", "actualCoverage", "getRuntimeEnv", "f", "s", "b", "window", "__NEXT_DATA__", "props", "pageProps", "env", "NEXT_PUBLIC_AWS_REGION", "process", "NEXT_PUBLIC_AWS_USER_POOLS_ID", "NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID", "NEXT_PUBLIC_AWS_COGNITO_DOMAIN", "NEXT_PUBLIC_REDIRECT_SIGN_IN", "NEXT_PUBLIC_REDIRECT_SIGN_OUT"], "sources": ["runtime-env.ts"], "sourcesContent": ["// This utility helps load environment variables at runtime\n// Useful for debugging environment variable issues\n\ninterface RuntimeEnv {\n  NEXT_PUBLIC_AWS_REGION?: string;\n  NEXT_PUBLIC_AWS_USER_POOLS_ID?: string;\n  NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID?: string;\n  NEXT_PUBLIC_AWS_COGNITO_DOMAIN?: string;\n  NEXT_PUBLIC_REDIRECT_SIGN_IN?: string;\n  NEXT_PUBLIC_REDIRECT_SIGN_OUT?: string;\n}\n\n// Try to load environment variables from different sources\nexport function getRuntimeEnv(): RuntimeEnv {\n  // First try window.__NEXT_DATA__.props.pageProps.env if it exists (SSR injected)\n  if (typeof window !== 'undefined' && \n      window.__NEXT_DATA__?.props?.pageProps?.env) {\n    return window.__NEXT_DATA__.props.pageProps.env;\n  }\n  \n  // Then try process.env (should work in both server and client contexts)\n  const env: RuntimeEnv = {\n    NEXT_PUBLIC_AWS_REGION: process.env.NEXT_PUBLIC_AWS_REGION,\n    NEXT_PUBLIC_AWS_USER_POOLS_ID: process.env.NEXT_PUBLIC_AWS_USER_POOLS_ID,\n    NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID: process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID,\n    NEXT_PUBLIC_AWS_COGNITO_DOMAIN: process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN,\n    NEXT_PUBLIC_REDIRECT_SIGN_IN: process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN,\n    NEXT_PUBLIC_REDIRECT_SIGN_OUT: process.env.NEXT_PUBLIC_REDIRECT_SIGN_OUT,\n  };\n  \n  return env;\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ;AACA;;AAWA;AACA,OAAO,SAASE,aAAaA,CAAA,EAAe;EAAA;EAAAF,cAAA,GAAAG,CAAA;EAAAH,cAAA,GAAAI,CAAA;EAC1C;EACA;EAAI;EAAA,CAAAJ,cAAA,GAAAK,CAAA,iBAAOC,MAAM,KAAK,WAAW;EAAA;EAAA,CAAAN,cAAA,GAAAK,CAAA,UAC7BC,MAAM,CAACC,aAAa,EAAEC,KAAK,EAAEC,SAAS,EAAEC,GAAG,GAAE;IAAA;IAAAV,cAAA,GAAAK,CAAA;IAAAL,cAAA,GAAAI,CAAA;IAC/C,OAAOE,MAAM,CAACC,aAAa,CAACC,KAAK,CAACC,SAAS,CAACC,GAAG;EACjD,CAAC;EAAA;EAAA;IAAAV,cAAA,GAAAK,CAAA;EAAA;;EAED;EACA,MAAMK,GAAe;EAAA;EAAA,CAAAV,cAAA,GAAAI,CAAA,OAAG;IACtBO,sBAAsB,EAAEC,OAAO,CAACF,GAAG,CAACC,sBAAsB;IAC1DE,6BAA6B,EAAED,OAAO,CAACF,GAAG,CAACG,6BAA6B;IACxEC,wCAAwC,EAAEF,OAAO,CAACF,GAAG,CAACI,wCAAwC;IAC9FC,8BAA8B,EAAEH,OAAO,CAACF,GAAG,CAACK,8BAA8B;IAC1EC,4BAA4B,EAAEJ,OAAO,CAACF,GAAG,CAACM,4BAA4B;IACtEC,6BAA6B,EAAEL,OAAO,CAACF,GAAG,CAACO;EAC7C,CAAC;EAAC;EAAAjB,cAAA,GAAAI,CAAA;EAEF,OAAOM,GAAG;AACZ", "ignoreList": []}