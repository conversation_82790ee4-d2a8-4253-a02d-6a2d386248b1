/**
 * Auth Service - Optimized Authentication Management
 * 
 * Reduces redundant auth verifications by implementing caching and
 * efficient session management with automatic token refresh.
 */

import { cookies } from 'next/headers'
import { NextRequest } from 'next/server'
import jwt from 'jsonwebtoken'
import jwksClient from 'jwks-rsa'
import { amplifyConfig, appConfig } from '@/lib/config/app-config'

// Session cache to reduce redundant verifications
interface CachedSession {
  session: UserSession
  expiresAt: number
}

interface UserSession {
  email: string
  sub: string
  name?: string
  groups?: string[]
  exp: number
  iat: number
}

interface AuthResult {
  success: boolean
  session?: UserSession
  error?: string
  response?: Response
}

class AuthService {
  private static instance: AuthService
  private sessionCache = new Map<string, CachedSession>()
  private jwksClientInstance: jwksClient.JwksClient | null = null
  private cacheCleanupInterval: NodeJS.Timeout | null = null

  private constructor() {
    // Start cache cleanup interval
    this.startCacheCleanup()
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService()
    }
    return AuthService.instance
  }

  /**
   * Get JWKS client (lazy initialization)
   */
  private getJwksClient(): jwksClient.JwksClient {
    if (!this.jwksClientInstance) {
      this.jwksClientInstance = jwksClient({
        jwksUri: `https://cognito-idp.${amplifyConfig.region}.amazonaws.com/${amplifyConfig.userPoolId}/.well-known/jwks.json`,
        cache: true,
        cacheMaxAge: 600000, // 10 minutes
        rateLimit: true,
        jwksRequestsPerMinute: 10
      })
    }
    return this.jwksClientInstance
  }

  /**
   * Verify JWT token with caching
   */
  private async verifyToken(token: string): Promise<UserSession | null> {
    try {
      // Check cache first
      const cached = this.sessionCache.get(token)
      if (cached && cached.expiresAt > Date.now()) {
        return cached.session
      }

      // Decode token header to get key ID
      const decoded = jwt.decode(token, { complete: true })
      if (!decoded || typeof decoded === 'string' || !decoded.header.kid) {
        return null
      }

      // Get signing key
      const client = this.getJwksClient()
      const key = await client.getSigningKey(decoded.header.kid)
      const signingKey = key.getPublicKey()

      // Verify token
      const payload = jwt.verify(token, signingKey, {
        algorithms: ['RS256'],
        issuer: `https://cognito-idp.${amplifyConfig.region}.amazonaws.com/${amplifyConfig.userPoolId}`,
        audience: amplifyConfig.userPoolClientId
      }) as any

      const session: UserSession = {
        email: payload.email,
        sub: payload.sub,
        name: payload.name,
        groups: payload['cognito:groups'] || [],
        exp: payload.exp,
        iat: payload.iat
      }

      // Cache the session (expire 5 minutes before token expires)
      const cacheExpiresAt = (payload.exp * 1000) - (5 * 60 * 1000)
      this.sessionCache.set(token, {
        session,
        expiresAt: cacheExpiresAt
      })

      return session

    } catch (error) {
      if (appConfig.isDevelopment) {
        console.error('Token verification failed:', error)
      }
      return null
    }
  }

  /**
   * Require authentication with caching
   */
  public async requireAuth(request?: NextRequest): Promise<AuthResult> {
    try {
      // Get token from cookies or headers
      let token: string | undefined

      if (request) {
        // From middleware or API route with request object
        token = request.cookies.get('idToken')?.value ||
                request.headers.get('authorization')?.replace('Bearer ', '')
      } else {
        // From API route without request object
        const cookieStore = cookies()
        token = cookieStore.get('idToken')?.value
      }

      if (!token) {
        return {
          success: false,
          error: 'No authentication token found',
          response: new Response(
            JSON.stringify({ error: 'Authentication required' }),
            { 
              status: 401,
              headers: { 'Content-Type': 'application/json' }
            }
          )
        }
      }

      if (appConfig.isDevelopment) {
        console.log('Found auth token in cookie: idToken')
      }

      // Verify token (with caching)
      const session = await this.verifyToken(token)

      if (!session) {
        return {
          success: false,
          error: 'Invalid or expired token',
          response: new Response(
            JSON.stringify({ error: 'Invalid authentication token' }),
            { 
              status: 401,
              headers: { 'Content-Type': 'application/json' }
            }
          )
        }
      }

      if (appConfig.isDevelopment) {
        console.log(`Successfully verified session for user: ${session.email}`)
      }

      return {
        success: true,
        session
      }

    } catch (error) {
      console.error('Authentication error:', error)
      return {
        success: false,
        error: 'Authentication failed',
        response: new Response(
          JSON.stringify({ error: 'Authentication failed' }),
          { 
            status: 500,
            headers: { 'Content-Type': 'application/json' }
          }
        )
      }
    }
  }

  /**
   * Check if user has required permissions
   */
  public hasPermission(session: UserSession, requiredGroups: string[]): boolean {
    if (!requiredGroups.length) return true
    if (!session.groups) return false
    
    return requiredGroups.some(group => session.groups!.includes(group))
  }

  /**
   * Get session from cache (if available)
   */
  public getCachedSession(token: string): UserSession | null {
    const cached = this.sessionCache.get(token)
    if (cached && cached.expiresAt > Date.now()) {
      return cached.session
    }
    return null
  }

  /**
   * Clear session cache
   */
  public clearCache(): void {
    this.sessionCache.clear()
  }

  /**
   * Get cache statistics
   */
  public getCacheStats() {
    const now = Date.now()
    const validEntries = Array.from(this.sessionCache.values())
      .filter(entry => entry.expiresAt > now)

    return {
      totalEntries: this.sessionCache.size,
      validEntries: validEntries.length,
      expiredEntries: this.sessionCache.size - validEntries.length
    }
  }

  /**
   * Start cache cleanup interval
   */
  private startCacheCleanup(): void {
    this.cacheCleanupInterval = setInterval(() => {
      const now = Date.now()
      const expiredKeys: string[] = []

      for (const [key, value] of this.sessionCache.entries()) {
        if (value.expiresAt <= now) {
          expiredKeys.push(key)
        }
      }

      expiredKeys.forEach(key => this.sessionCache.delete(key))

      if (appConfig.isDevelopment && expiredKeys.length > 0) {
        console.log(`Cleaned up ${expiredKeys.length} expired session cache entries`)
      }
    }, 5 * 60 * 1000) // Clean up every 5 minutes
  }

  /**
   * Stop cache cleanup interval
   */
  public destroy(): void {
    if (this.cacheCleanupInterval) {
      clearInterval(this.cacheCleanupInterval)
      this.cacheCleanupInterval = null
    }
    this.clearCache()
  }
}

// Export singleton instance
export const authService = AuthService.getInstance()

// Convenience function for easy import
export async function requireAuth(request?: NextRequest): Promise<AuthResult> {
  return authService.requireAuth(request)
}

// Export types
export type { UserSession, AuthResult }

// Export the class for testing
export { AuthService }
