
# Separate credentials for local development
DB_USER=postgres
DB_PASSWORD=admin

# AWS Configuration
AWS_REGION=ca-central-1


# AWS Amplify Configuration
NEXT_PUBLIC_AWS_REGION=ca-central-1
NEXT_PUBLIC_AWS_USER_POOLS_ID=ca-central-1_uwPuGUhLc
NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID=6fc4ks4poom3mqk5icavr7np1k

NEXT_PUBLIC_AWS_COGNITO_DOMAIN=auth.renewtrack.com
NEXT_PUBLIC_REDIRECT_SIGN_IN=http://localhost:3000/callback
NEXT_PUBLIC_REDIRECT_SIGN_OUT=http://localhost:3000/signout


# Database Configuration
# For production RDS (uncomment when using RDS)
# DATABASE_URL='postgresql://postgres:<EMAIL>:5432/renewtrack'
# DATABASE_SSL='true'

# For local development (currently active)
DATABASE_URL='postgresql://postgres:admin@127.0.0.1:5432/Renewtrack'
DATABASE_SSL='false'

DB_HOST='127.0.0.1'
DB_NAME='Renewtrack'
