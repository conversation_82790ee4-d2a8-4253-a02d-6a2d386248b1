c9930662bbfbc607f0321831093210cb
/* istanbul ignore next */
function cov_2c8mebnvkg() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\amplify-config.ts";
  var hash = "399d252e0eb9de4ddbba3eea73668974dd29a146";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\amplify-config.ts",
    statementMap: {
      "0": {
        start: {
          line: 7,
          column: 2
        },
        end: {
          line: 10,
          column: 3
        }
      },
      "1": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 8,
          column: 59
        }
      },
      "2": {
        start: {
          line: 9,
          column: 4
        },
        end: {
          line: 9,
          column: 11
        }
      },
      "3": {
        start: {
          line: 13,
          column: 24
        },
        end: {
          line: 13,
          column: 36
        }
      },
      "4": {
        start: {
          line: 14,
          column: 17
        },
        end: {
          line: 21,
          column: 3
        }
      },
      "5": {
        start: {
          line: 23,
          column: 2
        },
        end: {
          line: 30,
          column: 5
        }
      },
      "6": {
        start: {
          line: 32,
          column: 2
        },
        end: {
          line: 56,
          column: 3
        }
      },
      "7": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 51,
          column: 7
        }
      },
      "8": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 52,
          column: 92
        }
      },
      "9": {
        start: {
          line: 54,
          column: 4
        },
        end: {
          line: 54,
          column: 55
        }
      },
      "10": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 55,
          column: 16
        }
      },
      "11": {
        start: {
          line: 62,
          column: 2
        },
        end: {
          line: 64,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "configureAmplify",
        decl: {
          start: {
            line: 5,
            column: 16
          },
          end: {
            line: 5,
            column: 32
          }
        },
        loc: {
          start: {
            line: 5,
            column: 35
          },
          end: {
            line: 57,
            column: 1
          }
        },
        line: 5
      },
      "1": {
        name: "getAmplifySSR",
        decl: {
          start: {
            line: 60,
            column: 16
          },
          end: {
            line: 60,
            column: 29
          }
        },
        loc: {
          start: {
            line: 60,
            column: 49
          },
          end: {
            line: 65,
            column: 1
          }
        },
        line: 60
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 7,
            column: 2
          },
          end: {
            line: 10,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 7,
            column: 2
          },
          end: {
            line: 10,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 7
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {
      "0": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "399d252e0eb9de4ddbba3eea73668974dd29a146"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2c8mebnvkg = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2c8mebnvkg();
import { Amplify } from 'aws-amplify';
import { publicConfig } from './config';

// Amplify configuration using centralized configuration
export function configureAmplify() {
  /* istanbul ignore next */
  cov_2c8mebnvkg().f[0]++;
  cov_2c8mebnvkg().s[0]++;
  // Only configure once
  if (Amplify.getConfig().Auth) {
    /* istanbul ignore next */
    cov_2c8mebnvkg().b[0][0]++;
    cov_2c8mebnvkg().s[1]++;
    console.log('Amplify already configured, skipping...');
    /* istanbul ignore next */
    cov_2c8mebnvkg().s[2]++;
    return;
  } else
  /* istanbul ignore next */
  {
    cov_2c8mebnvkg().b[0][1]++;
  }

  // Get configuration from centralized config
  const {
    aws,
    auth
  } =
  /* istanbul ignore next */
  (cov_2c8mebnvkg().s[3]++, publicConfig);
  const config =
  /* istanbul ignore next */
  (cov_2c8mebnvkg().s[4]++, {
    region: aws.region,
    userPoolId: aws.userPoolId,
    userPoolClientId: aws.userPoolClientId,
    cognitoDomain: aws.cognitoDomain,
    redirectSignIn: auth.redirectSignIn,
    redirectSignOut: auth.redirectSignOut
  });
  /* istanbul ignore next */
  cov_2c8mebnvkg().s[5]++;
  console.log('Configuring Amplify with:', {
    region: config.region,
    userPoolId: config.userPoolId,
    userPoolClientId: config.userPoolClientId,
    cognitoDomain: config.cognitoDomain,
    redirectSignIn: config.redirectSignIn,
    redirectSignOut: config.redirectSignOut
  });
  /* istanbul ignore next */
  cov_2c8mebnvkg().s[6]++;
  try {
    /* istanbul ignore next */
    cov_2c8mebnvkg().s[7]++;
    Amplify.configure({
      Auth: {
        Cognito: {
          userPoolId: config.userPoolId,
          userPoolClientId: config.userPoolClientId,
          loginWith: {
            oauth: {
              domain: config.cognitoDomain,
              scopes: ["email", "profile", "openid", "aws.cognito.signin.user.admin"],
              redirectSignIn: [config.redirectSignIn],
              redirectSignOut: [config.redirectSignOut],
              responseType: "code"
            }
          }
        }
      }
    }, {
      ssr: true
    });
    /* istanbul ignore next */
    cov_2c8mebnvkg().s[8]++;
    console.log('Amplify configured successfully with OAuth domain:', config.cognitoDomain);
  } catch (error) {
    /* istanbul ignore next */
    cov_2c8mebnvkg().s[9]++;
    console.error('Error configuring Amplify:', error);
    /* istanbul ignore next */
    cov_2c8mebnvkg().s[10]++;
    throw error;
  }
}

// Export configured instance for server-side operations
export function getAmplifySSR(_request) {
  /* istanbul ignore next */
  cov_2c8mebnvkg().f[1]++;
  cov_2c8mebnvkg().s[11]++;
  // For Amplify v6, we need to create a new instance with SSR context
  return {
    Auth: Amplify.getConfig().Auth
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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