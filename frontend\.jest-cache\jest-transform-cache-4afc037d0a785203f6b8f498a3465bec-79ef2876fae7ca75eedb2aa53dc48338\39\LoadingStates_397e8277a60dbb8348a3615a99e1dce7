49b2ae3643a6fe4034d4a04ad12b8318
/**
 * Loading State Components
 * 
 * Reusable loading components with different styles and animations.
 * Focused responsibility: Consistent loading UI across the application.
 */

'use client';

/* istanbul ignore next */
import _extends from "@babel/runtime/helpers/esm/extends";
import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
const _excluded = ["children", "isLoading", "disabled", "className", "onClick"];
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\LoadingStates.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_257j1uopi2() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\LoadingStates.tsx";
  var hash = "09db94c1c63a3ec7a93051b52a11180f4769231c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\LoadingStates.tsx",
    statementMap: {
      "0": {
        start: {
          line: 44,
          column: 22
        },
        end: {
          line: 49,
          column: 3
        }
      },
      "1": {
        start: {
          line: 51,
          column: 23
        },
        end: {
          line: 55,
          column: 3
        }
      },
      "2": {
        start: {
          line: 57,
          column: 2
        },
        end: {
          line: 88,
          column: 3
        }
      },
      "3": {
        start: {
          line: 100,
          column: 25
        },
        end: {
          line: 100,
          column: 55
        }
      },
      "4": {
        start: {
          line: 102,
          column: 2
        },
        end: {
          line: 112,
          column: 3
        }
      },
      "5": {
        start: {
          line: 103,
          column: 4
        },
        end: {
          line: 111,
          column: 5
        }
      },
      "6": {
        start: {
          line: 114,
          column: 2
        },
        end: {
          line: 129,
          column: 3
        }
      },
      "7": {
        start: {
          line: 117,
          column: 8
        },
        end: {
          line: 126,
          column: 10
        }
      },
      "8": {
        start: {
          line: 140,
          column: 2
        },
        end: {
          line: 158,
          column: 3
        }
      },
      "9": {
        start: {
          line: 168,
          column: 2
        },
        end: {
          line: 182,
          column: 3
        }
      },
      "10": {
        start: {
          line: 201,
          column: 2
        },
        end: {
          line: 217,
          column: 3
        }
      },
      "11": {
        start: {
          line: 232,
          column: 2
        },
        end: {
          line: 232,
          column: 29
        }
      },
      "12": {
        start: {
          line: 232,
          column: 18
        },
        end: {
          line: 232,
          column: 29
        }
      },
      "13": {
        start: {
          line: 234,
          column: 2
        },
        end: {
          line: 246,
          column: 3
        }
      },
      "14": {
        start: {
          line: 261,
          column: 2
        },
        end: {
          line: 278,
          column: 3
        }
      },
      "15": {
        start: {
          line: 264,
          column: 8
        },
        end: {
          line: 275,
          column: 14
        }
      },
      "16": {
        start: {
          line: 295,
          column: 2
        },
        end: {
          line: 325,
          column: 3
        }
      },
      "17": {
        start: {
          line: 302,
          column: 16
        },
        end: {
          line: 304,
          column: 21
        }
      },
      "18": {
        start: {
          line: 311,
          column: 12
        },
        end: {
          line: 320,
          column: 17
        }
      },
      "19": {
        start: {
          line: 313,
          column: 16
        },
        end: {
          line: 318,
          column: 21
        }
      }
    },
    fnMap: {
      "0": {
        name: "LoadingSpinner",
        decl: {
          start: {
            line: 37,
            column: 16
          },
          end: {
            line: 37,
            column: 30
          }
        },
        loc: {
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 89,
            column: 1
          }
        },
        line: 43
      },
      "1": {
        name: "LoadingSkeleton",
        decl: {
          start: {
            line: 92,
            column: 16
          },
          end: {
            line: 92,
            column: 31
          }
        },
        loc: {
          start: {
            line: 99,
            column: 25
          },
          end: {
            line: 130,
            column: 1
          }
        },
        line: 99
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 116,
            column: 37
          },
          end: {
            line: 116,
            column: 38
          }
        },
        loc: {
          start: {
            line: 117,
            column: 8
          },
          end: {
            line: 126,
            column: 10
          }
        },
        line: 117
      },
      "3": {
        name: "LoadingPage",
        decl: {
          start: {
            line: 133,
            column: 16
          },
          end: {
            line: 133,
            column: 27
          }
        },
        loc: {
          start: {
            line: 139,
            column: 21
          },
          end: {
            line: 159,
            column: 1
          }
        },
        line: 139
      },
      "4": {
        name: "LoadingCard",
        decl: {
          start: {
            line: 162,
            column: 16
          },
          end: {
            line: 162,
            column: 27
          }
        },
        loc: {
          start: {
            line: 167,
            column: 21
          },
          end: {
            line: 183,
            column: 1
          }
        },
        line: 167
      },
      "5": {
        name: "LoadingButton",
        decl: {
          start: {
            line: 186,
            column: 16
          },
          end: {
            line: 186,
            column: 29
          }
        },
        loc: {
          start: {
            line: 200,
            column: 3
          },
          end: {
            line: 218,
            column: 1
          }
        },
        line: 200
      },
      "6": {
        name: "LoadingOverlay",
        decl: {
          start: {
            line: 221,
            column: 16
          },
          end: {
            line: 221,
            column: 30
          }
        },
        loc: {
          start: {
            line: 231,
            column: 3
          },
          end: {
            line: 247,
            column: 1
          }
        },
        line: 231
      },
      "7": {
        name: "LoadingList",
        decl: {
          start: {
            line: 250,
            column: 16
          },
          end: {
            line: 250,
            column: 27
          }
        },
        loc: {
          start: {
            line: 260,
            column: 3
          },
          end: {
            line: 279,
            column: 1
          }
        },
        line: 260
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 263,
            column: 37
          },
          end: {
            line: 263,
            column: 38
          }
        },
        loc: {
          start: {
            line: 264,
            column: 8
          },
          end: {
            line: 275,
            column: 14
          }
        },
        line: 264
      },
      "9": {
        name: "LoadingTable",
        decl: {
          start: {
            line: 282,
            column: 16
          },
          end: {
            line: 282,
            column: 28
          }
        },
        loc: {
          start: {
            line: 294,
            column: 3
          },
          end: {
            line: 326,
            column: 1
          }
        },
        line: 294
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 301,
            column: 47
          },
          end: {
            line: 301,
            column: 48
          }
        },
        loc: {
          start: {
            line: 302,
            column: 16
          },
          end: {
            line: 304,
            column: 21
          }
        },
        line: 302
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 310,
            column: 40
          },
          end: {
            line: 310,
            column: 41
          }
        },
        loc: {
          start: {
            line: 311,
            column: 12
          },
          end: {
            line: 320,
            column: 17
          }
        },
        line: 311
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 312,
            column: 47
          },
          end: {
            line: 312,
            column: 48
          }
        },
        loc: {
          start: {
            line: 313,
            column: 16
          },
          end: {
            line: 318,
            column: 21
          }
        },
        line: 313
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 38,
            column: 2
          },
          end: {
            line: 38,
            column: 13
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 38,
            column: 9
          },
          end: {
            line: 38,
            column: 13
          }
        }],
        line: 38
      },
      "1": {
        loc: {
          start: {
            line: 39,
            column: 2
          },
          end: {
            line: 39,
            column: 19
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 39,
            column: 10
          },
          end: {
            line: 39,
            column: 19
          }
        }],
        line: 39
      },
      "2": {
        loc: {
          start: {
            line: 41,
            column: 2
          },
          end: {
            line: 41,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 41,
            column: 14
          },
          end: {
            line: 41,
            column: 16
          }
        }],
        line: 41
      },
      "3": {
        loc: {
          start: {
            line: 62,
            column: 18
          },
          end: {
            line: 62,
            column: 35
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 62,
            column: 18
          },
          end: {
            line: 62,
            column: 22
          }
        }, {
          start: {
            line: 62,
            column: 26
          },
          end: {
            line: 62,
            column: 35
          }
        }],
        line: 62
      },
      "4": {
        loc: {
          start: {
            line: 84,
            column: 7
          },
          end: {
            line: 86,
            column: 7
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 84,
            column: 7
          },
          end: {
            line: 84,
            column: 11
          }
        }, {
          start: {
            line: 85,
            column: 8
          },
          end: {
            line: 85,
            column: 67
          }
        }],
        line: 84
      },
      "5": {
        loc: {
          start: {
            line: 93,
            column: 2
          },
          end: {
            line: 93,
            column: 11
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 93,
            column: 10
          },
          end: {
            line: 93,
            column: 11
          }
        }],
        line: 93
      },
      "6": {
        loc: {
          start: {
            line: 94,
            column: 2
          },
          end: {
            line: 94,
            column: 17
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 94,
            column: 11
          },
          end: {
            line: 94,
            column: 17
          }
        }],
        line: 94
      },
      "7": {
        loc: {
          start: {
            line: 95,
            column: 2
          },
          end: {
            line: 95,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 95,
            column: 10
          },
          end: {
            line: 95,
            column: 16
          }
        }],
        line: 95
      },
      "8": {
        loc: {
          start: {
            line: 96,
            column: 2
          },
          end: {
            line: 96,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 96,
            column: 12
          },
          end: {
            line: 96,
            column: 16
          }
        }],
        line: 96
      },
      "9": {
        loc: {
          start: {
            line: 97,
            column: 2
          },
          end: {
            line: 97,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 97,
            column: 14
          },
          end: {
            line: 97,
            column: 16
          }
        }],
        line: 97
      },
      "10": {
        loc: {
          start: {
            line: 100,
            column: 25
          },
          end: {
            line: 100,
            column: 55
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 100,
            column: 35
          },
          end: {
            line: 100,
            column: 50
          }
        }, {
          start: {
            line: 100,
            column: 53
          },
          end: {
            line: 100,
            column: 55
          }
        }],
        line: 100
      },
      "11": {
        loc: {
          start: {
            line: 102,
            column: 2
          },
          end: {
            line: 112,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 102,
            column: 2
          },
          end: {
            line: 112,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 102
      },
      "12": {
        loc: {
          start: {
            line: 122,
            column: 19
          },
          end: {
            line: 122,
            column: 54
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 122,
            column: 41
          },
          end: {
            line: 122,
            column: 46
          }
        }, {
          start: {
            line: 122,
            column: 49
          },
          end: {
            line: 122,
            column: 54
          }
        }],
        line: 122
      },
      "13": {
        loc: {
          start: {
            line: 134,
            column: 2
          },
          end: {
            line: 134,
            column: 22
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 134,
            column: 10
          },
          end: {
            line: 134,
            column: 22
          }
        }],
        line: 134
      },
      "14": {
        loc: {
          start: {
            line: 136,
            column: 2
          },
          end: {
            line: 136,
            column: 12
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 136,
            column: 9
          },
          end: {
            line: 136,
            column: 12
          }
        }],
        line: 136
      },
      "15": {
        loc: {
          start: {
            line: 137,
            column: 2
          },
          end: {
            line: 137,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 137,
            column: 14
          },
          end: {
            line: 137,
            column: 16
          }
        }],
        line: 137
      },
      "16": {
        loc: {
          start: {
            line: 150,
            column: 9
          },
          end: {
            line: 152,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 150,
            column: 9
          },
          end: {
            line: 150,
            column: 17
          }
        }, {
          start: {
            line: 151,
            column: 10
          },
          end: {
            line: 151,
            column: 63
          }
        }],
        line: 150
      },
      "17": {
        loc: {
          start: {
            line: 164,
            column: 2
          },
          end: {
            line: 164,
            column: 11
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 164,
            column: 10
          },
          end: {
            line: 164,
            column: 11
          }
        }],
        line: 164
      },
      "18": {
        loc: {
          start: {
            line: 165,
            column: 2
          },
          end: {
            line: 165,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 165,
            column: 14
          },
          end: {
            line: 165,
            column: 16
          }
        }],
        line: 165
      },
      "19": {
        loc: {
          start: {
            line: 173,
            column: 7
          },
          end: {
            line: 177,
            column: 7
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 173,
            column: 7
          },
          end: {
            line: 173,
            column: 12
          }
        }, {
          start: {
            line: 174,
            column: 8
          },
          end: {
            line: 176,
            column: 14
          }
        }],
        line: 173
      },
      "20": {
        loc: {
          start: {
            line: 188,
            column: 2
          },
          end: {
            line: 188,
            column: 19
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 188,
            column: 14
          },
          end: {
            line: 188,
            column: 19
          }
        }],
        line: 188
      },
      "21": {
        loc: {
          start: {
            line: 189,
            column: 2
          },
          end: {
            line: 189,
            column: 18
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 189,
            column: 13
          },
          end: {
            line: 189,
            column: 18
          }
        }],
        line: 189
      },
      "22": {
        loc: {
          start: {
            line: 190,
            column: 2
          },
          end: {
            line: 190,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 190,
            column: 14
          },
          end: {
            line: 190,
            column: 16
          }
        }],
        line: 190
      },
      "23": {
        loc: {
          start: {
            line: 203,
            column: 37
          },
          end: {
            line: 203,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 203,
            column: 49
          },
          end: {
            line: 203,
            column: 80
          }
        }, {
          start: {
            line: 203,
            column: 83
          },
          end: {
            line: 203,
            column: 85
          }
        }],
        line: 203
      },
      "24": {
        loc: {
          start: {
            line: 204,
            column: 16
          },
          end: {
            line: 204,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 204,
            column: 16
          },
          end: {
            line: 204,
            column: 24
          }
        }, {
          start: {
            line: 204,
            column: 28
          },
          end: {
            line: 204,
            column: 37
          }
        }],
        line: 204
      },
      "25": {
        loc: {
          start: {
            line: 205,
            column: 15
          },
          end: {
            line: 205,
            column: 46
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 205,
            column: 27
          },
          end: {
            line: 205,
            column: 36
          }
        }, {
          start: {
            line: 205,
            column: 39
          },
          end: {
            line: 205,
            column: 46
          }
        }],
        line: 205
      },
      "26": {
        loc: {
          start: {
            line: 208,
            column: 7
          },
          end: {
            line: 215,
            column: 7
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 209,
            column: 8
          },
          end: {
            line: 212,
            column: 14
          }
        }, {
          start: {
            line: 214,
            column: 8
          },
          end: {
            line: 214,
            column: 16
          }
        }],
        line: 208
      },
      "27": {
        loc: {
          start: {
            line: 222,
            column: 2
          },
          end: {
            line: 222,
            column: 19
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 222,
            column: 14
          },
          end: {
            line: 222,
            column: 19
          }
        }],
        line: 222
      },
      "28": {
        loc: {
          start: {
            line: 223,
            column: 2
          },
          end: {
            line: 223,
            column: 21
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 223,
            column: 9
          },
          end: {
            line: 223,
            column: 21
          }
        }],
        line: 223
      },
      "29": {
        loc: {
          start: {
            line: 224,
            column: 2
          },
          end: {
            line: 224,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 224,
            column: 14
          },
          end: {
            line: 224,
            column: 16
          }
        }],
        line: 224
      },
      "30": {
        loc: {
          start: {
            line: 232,
            column: 2
          },
          end: {
            line: 232,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 232,
            column: 2
          },
          end: {
            line: 232,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 232
      },
      "31": {
        loc: {
          start: {
            line: 251,
            column: 2
          },
          end: {
            line: 251,
            column: 11
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 251,
            column: 10
          },
          end: {
            line: 251,
            column: 11
          }
        }],
        line: 251
      },
      "32": {
        loc: {
          start: {
            line: 252,
            column: 2
          },
          end: {
            line: 252,
            column: 20
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 252,
            column: 15
          },
          end: {
            line: 252,
            column: 20
          }
        }],
        line: 252
      },
      "33": {
        loc: {
          start: {
            line: 253,
            column: 2
          },
          end: {
            line: 253,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 253,
            column: 14
          },
          end: {
            line: 253,
            column: 16
          }
        }],
        line: 253
      },
      "34": {
        loc: {
          start: {
            line: 265,
            column: 11
          },
          end: {
            line: 267,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 265,
            column: 11
          },
          end: {
            line: 265,
            column: 21
          }
        }, {
          start: {
            line: 266,
            column: 12
          },
          end: {
            line: 266,
            column: 62
          }
        }],
        line: 265
      },
      "35": {
        loc: {
          start: {
            line: 283,
            column: 2
          },
          end: {
            line: 283,
            column: 10
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 283,
            column: 9
          },
          end: {
            line: 283,
            column: 10
          }
        }],
        line: 283
      },
      "36": {
        loc: {
          start: {
            line: 284,
            column: 2
          },
          end: {
            line: 284,
            column: 13
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 284,
            column: 12
          },
          end: {
            line: 284,
            column: 13
          }
        }],
        line: 284
      },
      "37": {
        loc: {
          start: {
            line: 285,
            column: 2
          },
          end: {
            line: 285,
            column: 19
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 285,
            column: 15
          },
          end: {
            line: 285,
            column: 19
          }
        }],
        line: 285
      },
      "38": {
        loc: {
          start: {
            line: 286,
            column: 2
          },
          end: {
            line: 286,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 286,
            column: 14
          },
          end: {
            line: 286,
            column: 16
          }
        }],
        line: 286
      },
      "39": {
        loc: {
          start: {
            line: 298,
            column: 9
          },
          end: {
            line: 308,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 298,
            column: 9
          },
          end: {
            line: 298,
            column: 19
          }
        }, {
          start: {
            line: 299,
            column: 10
          },
          end: {
            line: 307,
            column: 18
          }
        }],
        line: 298
      },
      "40": {
        loc: {
          start: {
            line: 315,
            column: 27
          },
          end: {
            line: 315,
            column: 57
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 315,
            column: 44
          },
          end: {
            line: 315,
            column: 49
          }
        }, {
          start: {
            line: 315,
            column: 52
          },
          end: {
            line: 315,
            column: 57
          }
        }],
        line: 315
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0],
      "6": [0],
      "7": [0],
      "8": [0],
      "9": [0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0],
      "14": [0],
      "15": [0],
      "16": [0, 0],
      "17": [0],
      "18": [0],
      "19": [0, 0],
      "20": [0],
      "21": [0],
      "22": [0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0],
      "28": [0],
      "29": [0],
      "30": [0, 0],
      "31": [0],
      "32": [0],
      "33": [0],
      "34": [0, 0],
      "35": [0],
      "36": [0],
      "37": [0],
      "38": [0],
      "39": [0, 0],
      "40": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "09db94c1c63a3ec7a93051b52a11180f4769231c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_257j1uopi2 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_257j1uopi2();
// Loading Spinner Component
export function LoadingSpinner({
  size =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[0][0]++, 'md'),
  color =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[1][0]++, 'primary'),
  text,
  className =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[2][0]++, ''),
  'data-testid': testId
}) {
  /* istanbul ignore next */
  cov_257j1uopi2().f[0]++;
  const sizeClasses =
  /* istanbul ignore next */
  (cov_257j1uopi2().s[0]++, {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  });
  const colorClasses =
  /* istanbul ignore next */
  (cov_257j1uopi2().s[1]++, {
    primary: 'text-blue-600',
    secondary: 'text-gray-600',
    white: 'text-white'
  });
  /* istanbul ignore next */
  cov_257j1uopi2().s[2]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: `flex items-center justify-center ${className}`,
    /* istanbul ignore next */
    "data-testid": testId,
    role: "status",
    /* istanbul ignore next */
    "aria-label":
    /* istanbul ignore next */
    (cov_257j1uopi2().b[3][0]++, text) ||
    /* istanbul ignore next */
    (cov_257j1uopi2().b[3][1]++, 'Loading'),
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 58,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: `animate-spin ${sizeClasses[size]} ${colorClasses[color]}`,
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 64,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "circle",
  /* istanbul ignore next */
  {
    className: "opacity-25",
    cx: "12",
    cy: "12",
    r: "10",
    stroke: "currentColor",
    strokeWidth: "4",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 70,
      columnNumber: 9
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    className: "opacity-75",
    fill: "currentColor",
    d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 78,
      columnNumber: 9
    }
  })),
  /* istanbul ignore next */
  (cov_257j1uopi2().b[4][0]++, text) &&
  /* istanbul ignore next */
  (cov_257j1uopi2().b[4][1]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    className: "ml-2 text-sm text-secondary",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 85,
      columnNumber: 9
    }
  }, text)));
}

// Loading Skeleton Component
export function LoadingSkeleton({
  lines =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[5][0]++, 1),
  height =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[6][0]++, '1rem'),
  width =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[7][0]++, '100%'),
  animate =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[8][0]++, true),
  className =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[9][0]++, ''),
  'data-testid': testId
}) {
  /* istanbul ignore next */
  cov_257j1uopi2().f[1]++;
  const animationClass =
  /* istanbul ignore next */
  (cov_257j1uopi2().s[3]++, animate ?
  /* istanbul ignore next */
  (cov_257j1uopi2().b[10][0]++, 'animate-pulse') :
  /* istanbul ignore next */
  (cov_257j1uopi2().b[10][1]++, ''));
  /* istanbul ignore next */
  cov_257j1uopi2().s[4]++;
  if (lines === 1) {
    /* istanbul ignore next */
    cov_257j1uopi2().b[11][0]++;
    cov_257j1uopi2().s[5]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: `bg-gray-200 rounded ${animationClass} ${className}`,
      style: {
        height,
        width
      },
      /* istanbul ignore next */
      "data-testid": testId,
      role: "status",
      /* istanbul ignore next */
      "aria-label": "Loading content",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 104,
        columnNumber: 7
      }
    });
  } else
  /* istanbul ignore next */
  {
    cov_257j1uopi2().b[11][1]++;
  }
  cov_257j1uopi2().s[6]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: `space-y-2 ${className}`,
    /* istanbul ignore next */
    "data-testid": testId,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 115,
      columnNumber: 5
    }
  }, Array.from({
    length: lines
  }, (_, index) => {
    /* istanbul ignore next */
    cov_257j1uopi2().f[2]++;
    cov_257j1uopi2().s[7]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      key: index,
      className: `bg-gray-200 rounded ${animationClass}`,
      style: {
        height,
        width: index === lines - 1 ?
        /* istanbul ignore next */
        (cov_257j1uopi2().b[12][0]++, '75%') :
        /* istanbul ignore next */
        (cov_257j1uopi2().b[12][1]++, width) // Last line is shorter
      },
      role: "status",
      /* istanbul ignore next */
      "aria-label": `Loading content line ${index + 1}`,
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 117,
        columnNumber: 9
      }
    });
  }));
}

// Full Page Loading Component
export function LoadingPage({
  title =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[13][0]++, 'Loading...'),
  subtitle,
  icon =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[14][0]++, '⏳'),
  className =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[15][0]++, ''),
  'data-testid': testId
}) {
  /* istanbul ignore next */
  cov_257j1uopi2().f[3]++;
  cov_257j1uopi2().s[8]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: `flex items-center justify-center min-h-screen ${className}`,
    /* istanbul ignore next */
    "data-testid": testId,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 141,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-center",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 145,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-6xl mb-4",
    role: "img",
    /* istanbul ignore next */
    "aria-label": "Loading icon",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 146,
      columnNumber: 9
    }
  }, icon),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h1",
  /* istanbul ignore next */
  {
    className: "text-2xl font-semibold mb-2",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 149,
      columnNumber: 9
    }
  }, title),
  /* istanbul ignore next */
  (cov_257j1uopi2().b[16][0]++, subtitle) &&
  /* istanbul ignore next */
  (cov_257j1uopi2().b[16][1]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-secondary max-w-md",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 151,
      columnNumber: 11
    }
  }, subtitle)),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "mt-6",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 153,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(LoadingSpinner,
  /* istanbul ignore next */
  {
    size: "lg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 154,
      columnNumber: 11
    }
  }))));
}

// Loading Card Component
export function LoadingCard({
  title,
  lines =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[17][0]++, 3),
  className =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[18][0]++, ''),
  'data-testid': testId
}) {
  /* istanbul ignore next */
  cov_257j1uopi2().f[4]++;
  cov_257j1uopi2().s[9]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: `card ${className}`,
    /* istanbul ignore next */
    "data-testid": testId,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 169,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  (cov_257j1uopi2().b[19][0]++, title) &&
  /* istanbul ignore next */
  (cov_257j1uopi2().b[19][1]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "card-header",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 174,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(LoadingSkeleton,
  /* istanbul ignore next */
  {
    width: "40%",
    height: "1.5rem",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 175,
      columnNumber: 11
    }
  }))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "card-content",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 178,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(LoadingSkeleton,
  /* istanbul ignore next */
  {
    lines: lines,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 179,
      columnNumber: 9
    }
  })));
}

// Loading Button Component
export function LoadingButton(
/* istanbul ignore next */
_ref) {
  /* istanbul ignore next */
  let {
      children,
      isLoading =
      /* istanbul ignore next */
      (cov_257j1uopi2().b[20][0]++, false),
      disabled =
      /* istanbul ignore next */
      (cov_257j1uopi2().b[21][0]++, false),
      className =
      /* istanbul ignore next */
      (cov_257j1uopi2().b[22][0]++, ''),
      onClick
    } = _ref,
    props = _objectWithoutProperties(_ref, _excluded);
  /* istanbul ignore next */
  cov_257j1uopi2().f[5]++;
  cov_257j1uopi2().s[10]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  _extends({
    className: `btn ${className} ${isLoading ?
    /* istanbul ignore next */
    (cov_257j1uopi2().b[23][0]++, 'opacity-75 cursor-not-allowed') :
    /* istanbul ignore next */
    (cov_257j1uopi2().b[23][1]++, '')}`,
    disabled:
    /* istanbul ignore next */
    (cov_257j1uopi2().b[24][0]++, disabled) ||
    /* istanbul ignore next */
    (cov_257j1uopi2().b[24][1]++, isLoading),
    onClick: isLoading ?
    /* istanbul ignore next */
    (cov_257j1uopi2().b[25][0]++, undefined) :
    /* istanbul ignore next */
    (cov_257j1uopi2().b[25][1]++, onClick)
  }, props, {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 202,
      columnNumber: 5
    }
  }), isLoading ?
  /* istanbul ignore next */
  (cov_257j1uopi2().b[26][0]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex items-center",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 209,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(LoadingSpinner,
  /* istanbul ignore next */
  {
    size: "sm",
    color: "white",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 210,
      columnNumber: 11
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    className: "ml-2",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 211,
      columnNumber: 11
    }
  }, "Loading..."))) :
  /* istanbul ignore next */
  (cov_257j1uopi2().b[26][1]++, children));
}

// Loading Overlay Component
export function LoadingOverlay({
  isVisible =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[27][0]++, false),
  text =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[28][0]++, 'Loading...'),
  className =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[29][0]++, ''),
  'data-testid': testId
}) {
  /* istanbul ignore next */
  cov_257j1uopi2().f[6]++;
  cov_257j1uopi2().s[11]++;
  if (!isVisible) {
    /* istanbul ignore next */
    cov_257j1uopi2().b[30][0]++;
    cov_257j1uopi2().s[12]++;
    return null;
  } else
  /* istanbul ignore next */
  {
    cov_257j1uopi2().b[30][1]++;
  }
  cov_257j1uopi2().s[13]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: `fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${className}`,
    /* istanbul ignore next */
    "data-testid": testId,
    role: "dialog",
    /* istanbul ignore next */
    "aria-modal": "true",
    /* istanbul ignore next */
    "aria-label": "Loading overlay",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 235,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "bg-white rounded-lg p-6 shadow-lg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 242,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(LoadingSpinner,
  /* istanbul ignore next */
  {
    size: "lg",
    text: text,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 243,
      columnNumber: 9
    }
  })));
}

// Loading List Component
export function LoadingList({
  items =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[31][0]++, 5),
  showAvatar =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[32][0]++, false),
  className =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[33][0]++, ''),
  'data-testid': testId
}) {
  /* istanbul ignore next */
  cov_257j1uopi2().f[7]++;
  cov_257j1uopi2().s[14]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: `space-y-4 ${className}`,
    /* istanbul ignore next */
    "data-testid": testId,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 262,
      columnNumber: 5
    }
  }, Array.from({
    length: items
  }, (_, index) => {
    /* istanbul ignore next */
    cov_257j1uopi2().f[8]++;
    cov_257j1uopi2().s[15]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      key: index,
      className: "flex items-center space-x-3 p-3 border rounded-lg",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 264,
        columnNumber: 9
      }
    },
    /* istanbul ignore next */
    (cov_257j1uopi2().b[34][0]++, showAvatar) &&
    /* istanbul ignore next */
    (cov_257j1uopi2().b[34][1]++,
    /* istanbul ignore next */
    __jsx(LoadingSkeleton,
    /* istanbul ignore next */
    {
      width: "2.5rem",
      height: "2.5rem",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 266,
        columnNumber: 13
      }
    })),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "flex-1",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 268,
        columnNumber: 11
      }
    },
    /* istanbul ignore next */
    __jsx(LoadingSkeleton,
    /* istanbul ignore next */
    {
      width: "60%",
      height: "1rem",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 269,
        columnNumber: 13
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "mt-2",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 270,
        columnNumber: 13
      }
    },
    /* istanbul ignore next */
    __jsx(LoadingSkeleton,
    /* istanbul ignore next */
    {
      width: "40%",
      height: "0.75rem",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 271,
        columnNumber: 15
      }
    }))),
    /* istanbul ignore next */
    __jsx(LoadingSkeleton,
    /* istanbul ignore next */
    {
      width: "4rem",
      height: "1.5rem",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 274,
        columnNumber: 11
      }
    }));
  }));
}

// Loading Table Component
export function LoadingTable({
  rows =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[35][0]++, 5),
  columns =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[36][0]++, 4),
  showHeader =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[37][0]++, true),
  className =
  /* istanbul ignore next */
  (cov_257j1uopi2().b[38][0]++, ''),
  'data-testid': testId
}) {
  /* istanbul ignore next */
  cov_257j1uopi2().f[9]++;
  cov_257j1uopi2().s[16]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: `overflow-hidden border rounded-lg ${className}`,
    /* istanbul ignore next */
    "data-testid": testId,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 296,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "table",
  /* istanbul ignore next */
  {
    className: "w-full",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 297,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  (cov_257j1uopi2().b[39][0]++, showHeader) &&
  /* istanbul ignore next */
  (cov_257j1uopi2().b[39][1]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "thead",
  /* istanbul ignore next */
  {
    className: "bg-gray-50",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 299,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "tr",
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 300,
      columnNumber: 13
    }
  }, Array.from({
    length: columns
  }, (_, index) => {
    /* istanbul ignore next */
    cov_257j1uopi2().f[10]++;
    cov_257j1uopi2().s[17]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "th",
    /* istanbul ignore next */
    {
      key: index,
      className: "p-3 text-left",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 302,
        columnNumber: 17
      }
    },
    /* istanbul ignore next */
    __jsx(LoadingSkeleton,
    /* istanbul ignore next */
    {
      width: "80%",
      height: "1rem",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 303,
        columnNumber: 19
      }
    }));
  })))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "tbody",
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 309,
      columnNumber: 9
    }
  }, Array.from({
    length: rows
  }, (_, rowIndex) => {
    /* istanbul ignore next */
    cov_257j1uopi2().f[11]++;
    cov_257j1uopi2().s[18]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "tr",
    /* istanbul ignore next */
    {
      key: rowIndex,
      className: "border-t",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 311,
        columnNumber: 13
      }
    }, Array.from({
      length: columns
    }, (_, colIndex) => {
      /* istanbul ignore next */
      cov_257j1uopi2().f[12]++;
      cov_257j1uopi2().s[19]++;
      return /* istanbul ignore next */__jsx(
      /* istanbul ignore next */
      "td",
      /* istanbul ignore next */
      {
        key: colIndex,
        className: "p-3",
        __self: this,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 313,
          columnNumber: 17
        }
      },
      /* istanbul ignore next */
      __jsx(LoadingSkeleton,
      /* istanbul ignore next */
      {
        width: colIndex === 0 ?
        /* istanbul ignore next */
        (cov_257j1uopi2().b[40][0]++, '90%') :
        /* istanbul ignore next */
        (cov_257j1uopi2().b[40][1]++, '70%'),
        height: "0.875rem",
        __self: this,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 314,
          columnNumber: 19
        }
      }));
    }));
  }))));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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