2c14f96224815e1ca0acfd2b6a1a8c6f
/* istanbul ignore next */
function cov_2c8fkmz668() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\runtime-env.ts";
  var hash = "94d3c10e4fcb17757dd16a9c499e69f15e0f0d77";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\runtime-env.ts",
    statementMap: {
      "0": {
        start: {
          line: 16,
          column: 2
        },
        end: {
          line: 19,
          column: 3
        }
      },
      "1": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 18,
          column: 52
        }
      },
      "2": {
        start: {
          line: 22,
          column: 26
        },
        end: {
          line: 29,
          column: 3
        }
      },
      "3": {
        start: {
          line: 31,
          column: 2
        },
        end: {
          line: 31,
          column: 13
        }
      }
    },
    fnMap: {
      "0": {
        name: "getRuntimeEnv",
        decl: {
          start: {
            line: 14,
            column: 16
          },
          end: {
            line: 14,
            column: 29
          }
        },
        loc: {
          start: {
            line: 14,
            column: 44
          },
          end: {
            line: 32,
            column: 1
          }
        },
        line: 14
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 16,
            column: 2
          },
          end: {
            line: 19,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 2
          },
          end: {
            line: 19,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "1": {
        loc: {
          start: {
            line: 16,
            column: 6
          },
          end: {
            line: 17,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 16,
            column: 6
          },
          end: {
            line: 16,
            column: 35
          }
        }, {
          start: {
            line: 17,
            column: 6
          },
          end: {
            line: 17,
            column: 49
          }
        }],
        line: 16
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "94d3c10e4fcb17757dd16a9c499e69f15e0f0d77"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2c8fkmz668 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2c8fkmz668();
// This utility helps load environment variables at runtime
// Useful for debugging environment variable issues

// Try to load environment variables from different sources
export function getRuntimeEnv() {
  /* istanbul ignore next */
  cov_2c8fkmz668().f[0]++;
  cov_2c8fkmz668().s[0]++;
  // First try window.__NEXT_DATA__.props.pageProps.env if it exists (SSR injected)
  if (
  /* istanbul ignore next */
  (cov_2c8fkmz668().b[1][0]++, typeof window !== 'undefined') &&
  /* istanbul ignore next */
  (cov_2c8fkmz668().b[1][1]++, window.__NEXT_DATA__?.props?.pageProps?.env)) {
    /* istanbul ignore next */
    cov_2c8fkmz668().b[0][0]++;
    cov_2c8fkmz668().s[1]++;
    return window.__NEXT_DATA__.props.pageProps.env;
  } else
  /* istanbul ignore next */
  {
    cov_2c8fkmz668().b[0][1]++;
  }

  // Then try process.env (should work in both server and client contexts)
  const env =
  /* istanbul ignore next */
  (cov_2c8fkmz668().s[2]++, {
    NEXT_PUBLIC_AWS_REGION: process.env.NEXT_PUBLIC_AWS_REGION,
    NEXT_PUBLIC_AWS_USER_POOLS_ID: process.env.NEXT_PUBLIC_AWS_USER_POOLS_ID,
    NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID: process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID,
    NEXT_PUBLIC_AWS_COGNITO_DOMAIN: process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN,
    NEXT_PUBLIC_REDIRECT_SIGN_IN: process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN,
    NEXT_PUBLIC_REDIRECT_SIGN_OUT: process.env.NEXT_PUBLIC_REDIRECT_SIGN_OUT
  });
  /* istanbul ignore next */
  cov_2c8fkmz668().s[3]++;
  return env;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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