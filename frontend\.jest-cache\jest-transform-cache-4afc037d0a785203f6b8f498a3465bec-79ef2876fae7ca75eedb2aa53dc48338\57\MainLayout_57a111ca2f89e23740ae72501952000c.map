{"version": 3, "names": ["_jsxFileName", "React", "__jsx", "createElement", "cov_2g5ovmwduy", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "_coverageSchema", "coverage", "actualCoverage", "useEffect", "useRouter", "useAuth", "Sidebar", "getLoginUrl", "MainLayout", "children", "isAuthenticated", "isLoading", "router", "window", "location", "href", "className", "__self", "__source", "fileName", "lineNumber", "columnNumber"], "sources": ["MainLayout.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useEffect } from 'react'\r\nimport { useRouter } from 'next/navigation'\r\nimport { useAuth } from '@/contexts/AuthContext'\r\nimport Sidebar from './Sidebar'\r\nimport { getLoginUrl } from '@/lib/auth'\r\n\r\nexport default function MainLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode\r\n}) {\r\n  const { isAuthenticated, isLoading } = useAuth()\r\n  const router = useRouter()\r\n\r\n  useEffect(() => {\r\n    if (!isLoading && !isAuthenticated) {\r\n      window.location.href = getLoginUrl()\r\n    }\r\n  }, [isLoading, isAuthenticated])\r\n\r\n  // Show loading state\r\n  if (isLoading) {\r\n    return <div className=\"flex items-center justify-center h-screen\">Loading...</div>\r\n  }\r\n\r\n  // Only render the layout when authenticated\r\n  if (!isAuthenticated) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex h-screen\">\r\n      <Sidebar />\r\n      <main className=\"flex-1 p-6 overflow-auto\">\r\n        {children}\r\n      </main>\r\n    </div>\r\n  )\r\n}\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAAA,YAAY;;AAAA;AAAA,IAAAA,YAAA;AAAA,OAAAC,KAAA;AAAA,IAAAC,KAAA,GAAAD,KAAA,CAAAE,aAAA;AAAA,SAAAC,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,eAAA;IAAArB,IAAA;EAAA;EAAA,IAAAsB,QAAA,GAAArB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAmB,QAAA,CAAAvB,IAAA,KAAAuB,QAAA,CAAAvB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAsB,QAAA,CAAAvB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAmB,cAAA,GAAAD,QAAA,CAAAvB,IAAA;EAAA;IAeA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAyB,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAzB,cAAA;AAbZ,SAAS0B,SAAS,QAAQ,OAAO;AACjC,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,WAAW,QAAQ,YAAY;AAExC,eAAe,SAASC,UAAUA,CAAC;EACjCC;AAGF,CAAC,EAAE;EAAA;EAAAhC,cAAA,GAAAqB,CAAA;EACD,MAAM;IAAEY,eAAe;IAAEC;EAAU,CAAC;EAAA;EAAA,CAAAlC,cAAA,GAAAoB,CAAA,OAAGQ,OAAO,CAAC,CAAC;EAChD,MAAMO,MAAM;EAAA;EAAA,CAAAnC,cAAA,GAAAoB,CAAA,OAAGO,SAAS,CAAC,CAAC;EAAA;EAAA3B,cAAA,GAAAoB,CAAA;EAE1BM,SAAS,CAAC,MAAM;IAAA;IAAA1B,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACd;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAACY,SAAS;IAAA;IAAA,CAAAlC,cAAA,GAAAsB,CAAA,UAAI,CAACW,eAAe,GAAE;MAAA;MAAAjC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAClCgB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGR,WAAW,CAAC,CAAC;IACtC,CAAC;IAAA;IAAA;MAAA9B,cAAA,GAAAsB,CAAA;IAAA;EACH,CAAC,EAAE,CAACY,SAAS,EAAED,eAAe,CAAC,CAAC;;EAEhC;EAAA;EAAAjC,cAAA,GAAAoB,CAAA;EACA,IAAIc,SAAS,EAAE;IAAA;IAAAlC,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACb,OAAO,0BAAAtB,KAAA;IAAA;IAAA;IAAA;IAAA;MAAKyC,SAAS,EAAC,2CAA2C;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAA9C,YAAA;QAAA+C,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,YAAe,CAAC;EACpF,CAAC;EAAA;EAAA;IAAA5C,cAAA,GAAAsB,CAAA;EAAA;;EAED;EAAAtB,cAAA,GAAAoB,CAAA;EACA,IAAI,CAACa,eAAe,EAAE;IAAA;IAAAjC,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACpB,OAAO,IAAI;EACb,CAAC;EAAA;EAAA;IAAApB,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EAED,OACE,0BAAAtB,KAAA;EAAA;EAAA;EAAA;EAAA;IAAKyC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAA9C,YAAA;MAAA+C,UAAA;MAAAC,YAAA;IAAA;EAAA;EAC5B;EAAA9C,KAAA,CAAC+B,OAAO;EAAA;EAAA;IAAAW,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAA9C,YAAA;MAAA+C,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC;EACX;EAAA9C,KAAA;EAAA;EAAA;EAAA;EAAA;IAAMyC,SAAS,EAAC,0BAA0B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAA9C,YAAA;MAAA+C,UAAA;MAAAC,YAAA;IAAA;EAAA,GACvCZ,QACG,CACH,CAAC;AAEV", "ignoreList": []}