bc9b923368a8170b2eec34a16f07f5e5
/* istanbul ignore next */
function cov_2fd3e7m4j() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\database.ts";
  var hash = "0a9d406ddf0ce86a4931119e1f767895da120015";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\database.ts",
    statementMap: {
      "0": {
        start: {
          line: 43,
          column: 19
        },
        end: {
          line: 43,
          column: 44
        }
      },
      "1": {
        start: {
          line: 53,
          column: 34
        },
        end: {
          line: 53,
          column: 38
        }
      },
      "2": {
        start: {
          line: 55,
          column: 2
        },
        end: {
          line: 107,
          column: 3
        }
      },
      "3": {
        start: {
          line: 56,
          column: 17
        },
        end: {
          line: 56,
          column: 34
        }
      },
      "4": {
        start: {
          line: 57,
          column: 4
        },
        end: {
          line: 57,
          column: 34
        }
      },
      "5": {
        start: {
          line: 60,
          column: 4
        },
        end: {
          line: 62,
          column: 5
        }
      },
      "6": {
        start: {
          line: 61,
          column: 6
        },
        end: {
          line: 61,
          column: 71
        }
      },
      "7": {
        start: {
          line: 65,
          column: 4
        },
        end: {
          line: 67,
          column: 5
        }
      },
      "8": {
        start: {
          line: 66,
          column: 6
        },
        end: {
          line: 66,
          column: 75
        }
      },
      "9": {
        start: {
          line: 69,
          column: 22
        },
        end: {
          line: 69,
          column: 32
        }
      },
      "10": {
        start: {
          line: 70,
          column: 32
        },
        end: {
          line: 70,
          column: 65
        }
      },
      "11": {
        start: {
          line: 71,
          column: 21
        },
        end: {
          line: 71,
          column: 43
        }
      },
      "12": {
        start: {
          line: 74,
          column: 4
        },
        end: {
          line: 80,
          column: 5
        }
      },
      "13": {
        start: {
          line: 75,
          column: 6
        },
        end: {
          line: 79,
          column: 9
        }
      },
      "14": {
        start: {
          line: 82,
          column: 4
        },
        end: {
          line: 86,
          column: 6
        }
      },
      "15": {
        start: {
          line: 89,
          column: 20
        },
        end: {
          line: 89,
          column: 42
        }
      },
      "16": {
        start: {
          line: 90,
          column: 4
        },
        end: {
          line: 96,
          column: 7
        }
      },
      "17": {
        start: {
          line: 98,
          column: 4
        },
        end: {
          line: 102,
          column: 6
        }
      },
      "18": {
        start: {
          line: 104,
          column: 4
        },
        end: {
          line: 106,
          column: 5
        }
      },
      "19": {
        start: {
          line: 105,
          column: 6
        },
        end: {
          line: 105,
          column: 23
        }
      },
      "20": {
        start: {
          line: 118,
          column: 17
        },
        end: {
          line: 118,
          column: 62
        }
      },
      "21": {
        start: {
          line: 120,
          column: 2
        },
        end: {
          line: 122,
          column: 3
        }
      },
      "22": {
        start: {
          line: 121,
          column: 4
        },
        end: {
          line: 121,
          column: 18
        }
      },
      "23": {
        start: {
          line: 124,
          column: 2
        },
        end: {
          line: 128,
          column: 4
        }
      },
      "24": {
        start: {
          line: 138,
          column: 34
        },
        end: {
          line: 138,
          column: 38
        }
      },
      "25": {
        start: {
          line: 140,
          column: 2
        },
        end: {
          line: 192,
          column: 3
        }
      },
      "26": {
        start: {
          line: 141,
          column: 17
        },
        end: {
          line: 141,
          column: 34
        }
      },
      "27": {
        start: {
          line: 142,
          column: 4
        },
        end: {
          line: 142,
          column: 34
        }
      },
      "28": {
        start: {
          line: 145,
          column: 4
        },
        end: {
          line: 147,
          column: 5
        }
      },
      "29": {
        start: {
          line: 146,
          column: 6
        },
        end: {
          line: 146,
          column: 75
        }
      },
      "30": {
        start: {
          line: 149,
          column: 4
        },
        end: {
          line: 149,
          column: 32
        }
      },
      "31": {
        start: {
          line: 151,
          column: 27
        },
        end: {
          line: 151,
          column: 29
        }
      },
      "32": {
        start: {
          line: 153,
          column: 4
        },
        end: {
          line: 156,
          column: 5
        }
      },
      "33": {
        start: {
          line: 154,
          column: 21
        },
        end: {
          line: 154,
          column: 54
        }
      },
      "34": {
        start: {
          line: 155,
          column: 6
        },
        end: {
          line: 155,
          column: 32
        }
      },
      "35": {
        start: {
          line: 158,
          column: 4
        },
        end: {
          line: 158,
          column: 33
        }
      },
      "36": {
        start: {
          line: 160,
          column: 4
        },
        end: {
          line: 164,
          column: 6
        }
      },
      "37": {
        start: {
          line: 163,
          column: 46
        },
        end: {
          line: 163,
          column: 63
        }
      },
      "38": {
        start: {
          line: 167,
          column: 4
        },
        end: {
          line: 173,
          column: 5
        }
      },
      "39": {
        start: {
          line: 168,
          column: 6
        },
        end: {
          line: 172,
          column: 7
        }
      },
      "40": {
        start: {
          line: 169,
          column: 8
        },
        end: {
          line: 169,
          column: 39
        }
      },
      "41": {
        start: {
          line: 171,
          column: 8
        },
        end: {
          line: 171,
          column: 72
        }
      },
      "42": {
        start: {
          line: 175,
          column: 20
        },
        end: {
          line: 175,
          column: 42
        }
      },
      "43": {
        start: {
          line: 176,
          column: 4
        },
        end: {
          line: 181,
          column: 7
        }
      },
      "44": {
        start: {
          line: 183,
          column: 4
        },
        end: {
          line: 187,
          column: 6
        }
      },
      "45": {
        start: {
          line: 189,
          column: 4
        },
        end: {
          line: 191,
          column: 5
        }
      },
      "46": {
        start: {
          line: 190,
          column: 6
        },
        end: {
          line: 190,
          column: 23
        }
      },
      "47": {
        start: {
          line: 199,
          column: 17
        },
        end: {
          line: 202,
          column: 3
        }
      },
      "48": {
        start: {
          line: 204,
          column: 2
        },
        end: {
          line: 204,
          column: 56
        }
      },
      "49": {
        start: {
          line: 211,
          column: 17
        },
        end: {
          line: 214,
          column: 3
        }
      },
      "50": {
        start: {
          line: 216,
          column: 2
        },
        end: {
          line: 216,
          column: 56
        }
      },
      "51": {
        start: {
          line: 228,
          column: 2
        },
        end: {
          line: 245,
          column: 3
        }
      },
      "52": {
        start: {
          line: 229,
          column: 17
        },
        end: {
          line: 229,
          column: 34
        }
      },
      "53": {
        start: {
          line: 231,
          column: 4
        },
        end: {
          line: 236,
          column: 6
        }
      },
      "54": {
        start: {
          line: 238,
          column: 4
        },
        end: {
          line: 238,
          column: 58
        }
      },
      "55": {
        start: {
          line: 239,
          column: 4
        },
        end: {
          line: 244,
          column: 6
        }
      },
      "56": {
        start: {
          line: 252,
          column: 2
        },
        end: {
          line: 258,
          column: 3
        }
      },
      "57": {
        start: {
          line: 253,
          column: 17
        },
        end: {
          line: 253,
          column: 34
        }
      },
      "58": {
        start: {
          line: 254,
          column: 4
        },
        end: {
          line: 254,
          column: 21
        }
      },
      "59": {
        start: {
          line: 255,
          column: 4
        },
        end: {
          line: 255,
          column: 60
        }
      },
      "60": {
        start: {
          line: 257,
          column: 4
        },
        end: {
          line: 257,
          column: 64
        }
      },
      "61": {
        start: {
          line: 265,
          column: 2
        },
        end: {
          line: 292,
          column: 3
        }
      },
      "62": {
        start: {
          line: 267,
          column: 6
        },
        end: {
          line: 267,
          column: 61
        }
      },
      "63": {
        start: {
          line: 269,
          column: 6
        },
        end: {
          line: 269,
          column: 48
        }
      },
      "64": {
        start: {
          line: 271,
          column: 6
        },
        end: {
          line: 271,
          column: 41
        }
      },
      "65": {
        start: {
          line: 273,
          column: 6
        },
        end: {
          line: 273,
          column: 37
        }
      },
      "66": {
        start: {
          line: 275,
          column: 6
        },
        end: {
          line: 275,
          column: 40
        }
      },
      "67": {
        start: {
          line: 277,
          column: 6
        },
        end: {
          line: 277,
          column: 41
        }
      },
      "68": {
        start: {
          line: 279,
          column: 6
        },
        end: {
          line: 279,
          column: 43
        }
      },
      "69": {
        start: {
          line: 281,
          column: 6
        },
        end: {
          line: 281,
          column: 46
        }
      },
      "70": {
        start: {
          line: 283,
          column: 6
        },
        end: {
          line: 283,
          column: 34
        }
      },
      "71": {
        start: {
          line: 285,
          column: 6
        },
        end: {
          line: 285,
          column: 42
        }
      },
      "72": {
        start: {
          line: 287,
          column: 6
        },
        end: {
          line: 287,
          column: 51
        }
      },
      "73": {
        start: {
          line: 289,
          column: 6
        },
        end: {
          line: 289,
          column: 49
        }
      },
      "74": {
        start: {
          line: 291,
          column: 6
        },
        end: {
          line: 291,
          column: 41
        }
      },
      "75": {
        start: {
          line: 302,
          column: 24
        },
        end: {
          line: 302,
          column: 26
        }
      },
      "76": {
        start: {
          line: 303,
          column: 28
        },
        end: {
          line: 303,
          column: 30
        }
      },
      "77": {
        start: {
          line: 304,
          column: 19
        },
        end: {
          line: 304,
          column: 29
        }
      },
      "78": {
        start: {
          line: 306,
          column: 2
        },
        end: {
          line: 318,
          column: 3
        }
      },
      "79": {
        start: {
          line: 307,
          column: 4
        },
        end: {
          line: 317,
          column: 5
        }
      },
      "80": {
        start: {
          line: 308,
          column: 6
        },
        end: {
          line: 316,
          column: 7
        }
      },
      "81": {
        start: {
          line: 310,
          column: 29
        },
        end: {
          line: 310,
          column: 75
        }
      },
      "82": {
        start: {
          line: 310,
          column: 45
        },
        end: {
          line: 310,
          column: 63
        }
      },
      "83": {
        start: {
          line: 311,
          column: 8
        },
        end: {
          line: 311,
          column: 52
        }
      },
      "84": {
        start: {
          line: 312,
          column: 8
        },
        end: {
          line: 312,
          column: 30
        }
      },
      "85": {
        start: {
          line: 314,
          column: 8
        },
        end: {
          line: 314,
          column: 50
        }
      },
      "86": {
        start: {
          line: 315,
          column: 8
        },
        end: {
          line: 315,
          column: 27
        }
      },
      "87": {
        start: {
          line: 320,
          column: 2
        },
        end: {
          line: 324,
          column: 4
        }
      },
      "88": {
        start: {
          line: 331,
          column: 2
        },
        end: {
          line: 331,
          column: 47
        }
      }
    },
    fnMap: {
      "0": {
        name: "executeQuery",
        decl: {
          start: {
            line: 48,
            column: 22
          },
          end: {
            line: 48,
            column: 34
          }
        },
        loc: {
          start: {
            line: 52,
            column: 26
          },
          end: {
            line: 108,
            column: 1
          }
        },
        line: 52
      },
      "1": {
        name: "executeQuerySingle",
        decl: {
          start: {
            line: 113,
            column: 22
          },
          end: {
            line: 113,
            column: 40
          }
        },
        loc: {
          start: {
            line: 117,
            column: 24
          },
          end: {
            line: 129,
            column: 1
          }
        },
        line: 117
      },
      "2": {
        name: "executeTransaction",
        decl: {
          start: {
            line: 134,
            column: 22
          },
          end: {
            line: 134,
            column: 40
          }
        },
        loc: {
          start: {
            line: 137,
            column: 26
          },
          end: {
            line: 193,
            column: 1
          }
        },
        line: 137
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 163,
            column: 31
          },
          end: {
            line: 163,
            column: 32
          }
        },
        loc: {
          start: {
            line: 163,
            column: 46
          },
          end: {
            line: 163,
            column: 63
          }
        },
        line: 163
      },
      "4": {
        name: "schemaExists",
        decl: {
          start: {
            line: 198,
            column: 22
          },
          end: {
            line: 198,
            column: 34
          }
        },
        loc: {
          start: {
            line: 198,
            column: 73
          },
          end: {
            line: 205,
            column: 1
          }
        },
        line: 198
      },
      "5": {
        name: "tableExists",
        decl: {
          start: {
            line: 210,
            column: 22
          },
          end: {
            line: 210,
            column: 33
          }
        },
        loc: {
          start: {
            line: 210,
            column: 94
          },
          end: {
            line: 217,
            column: 1
          }
        },
        line: 210
      },
      "6": {
        name: "getConnectionHealth",
        decl: {
          start: {
            line: 222,
            column: 22
          },
          end: {
            line: 222,
            column: 41
          }
        },
        loc: {
          start: {
            line: 227,
            column: 3
          },
          end: {
            line: 246,
            column: 1
          }
        },
        line: 227
      },
      "7": {
        name: "closeDatabase",
        decl: {
          start: {
            line: 251,
            column: 22
          },
          end: {
            line: 251,
            column: 35
          }
        },
        loc: {
          start: {
            line: 251,
            column: 53
          },
          end: {
            line: 259,
            column: 1
          }
        },
        line: 251
      },
      "8": {
        name: "getDatabaseErrorMessage",
        decl: {
          start: {
            line: 264,
            column: 9
          },
          end: {
            line: 264,
            column: 32
          }
        },
        loc: {
          start: {
            line: 264,
            column: 63
          },
          end: {
            line: 293,
            column: 1
          }
        },
        line: 264
      },
      "9": {
        name: "buildWhereClause",
        decl: {
          start: {
            line: 298,
            column: 16
          },
          end: {
            line: 298,
            column: 32
          }
        },
        loc: {
          start: {
            line: 301,
            column: 56
          },
          end: {
            line: 325,
            column: 1
          }
        },
        line: 301
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 310,
            column: 39
          },
          end: {
            line: 310,
            column: 40
          }
        },
        loc: {
          start: {
            line: 310,
            column: 45
          },
          end: {
            line: 310,
            column: 63
          }
        },
        line: 310
      },
      "11": {
        name: "escapeIdentifier",
        decl: {
          start: {
            line: 330,
            column: 16
          },
          end: {
            line: 330,
            column: 32
          }
        },
        loc: {
          start: {
            line: 330,
            column: 61
          },
          end: {
            line: 332,
            column: 1
          }
        },
        line: 330
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 50,
            column: 2
          },
          end: {
            line: 50,
            column: 20
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 50,
            column: 18
          },
          end: {
            line: 50,
            column: 20
          }
        }],
        line: 50
      },
      "1": {
        loc: {
          start: {
            line: 51,
            column: 2
          },
          end: {
            line: 51,
            column: 28
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 51,
            column: 26
          },
          end: {
            line: 51,
            column: 28
          }
        }],
        line: 51
      },
      "2": {
        loc: {
          start: {
            line: 60,
            column: 4
          },
          end: {
            line: 62,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 60,
            column: 4
          },
          end: {
            line: 62,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 60
      },
      "3": {
        loc: {
          start: {
            line: 65,
            column: 4
          },
          end: {
            line: 67,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 65,
            column: 4
          },
          end: {
            line: 67,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 65
      },
      "4": {
        loc: {
          start: {
            line: 74,
            column: 4
          },
          end: {
            line: 80,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 4
          },
          end: {
            line: 80,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 74
      },
      "5": {
        loc: {
          start: {
            line: 85,
            column: 16
          },
          end: {
            line: 85,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 85,
            column: 16
          },
          end: {
            line: 85,
            column: 31
          }
        }, {
          start: {
            line: 85,
            column: 35
          },
          end: {
            line: 85,
            column: 36
          }
        }],
        line: 85
      },
      "6": {
        loc: {
          start: {
            line: 101,
            column: 17
          },
          end: {
            line: 101,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 101,
            column: 17
          },
          end: {
            line: 101,
            column: 29
          }
        }, {
          start: {
            line: 101,
            column: 33
          },
          end: {
            line: 101,
            column: 48
          }
        }],
        line: 101
      },
      "7": {
        loc: {
          start: {
            line: 104,
            column: 4
          },
          end: {
            line: 106,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 104,
            column: 4
          },
          end: {
            line: 106,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 104
      },
      "8": {
        loc: {
          start: {
            line: 115,
            column: 2
          },
          end: {
            line: 115,
            column: 20
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 115,
            column: 18
          },
          end: {
            line: 115,
            column: 20
          }
        }],
        line: 115
      },
      "9": {
        loc: {
          start: {
            line: 116,
            column: 2
          },
          end: {
            line: 116,
            column: 28
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 116,
            column: 26
          },
          end: {
            line: 116,
            column: 28
          }
        }],
        line: 116
      },
      "10": {
        loc: {
          start: {
            line: 120,
            column: 2
          },
          end: {
            line: 122,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 120,
            column: 2
          },
          end: {
            line: 122,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 120
      },
      "11": {
        loc: {
          start: {
            line: 126,
            column: 10
          },
          end: {
            line: 126,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 126,
            column: 10
          },
          end: {
            line: 126,
            column: 26
          }
        }, {
          start: {
            line: 126,
            column: 30
          },
          end: {
            line: 126,
            column: 34
          }
        }],
        line: 126
      },
      "12": {
        loc: {
          start: {
            line: 136,
            column: 2
          },
          end: {
            line: 136,
            column: 28
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 136,
            column: 26
          },
          end: {
            line: 136,
            column: 28
          }
        }],
        line: 136
      },
      "13": {
        loc: {
          start: {
            line: 145,
            column: 4
          },
          end: {
            line: 147,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 145,
            column: 4
          },
          end: {
            line: 147,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 145
      },
      "14": {
        loc: {
          start: {
            line: 153,
            column: 24
          },
          end: {
            line: 153,
            column: 35
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 153,
            column: 33
          },
          end: {
            line: 153,
            column: 35
          }
        }],
        line: 153
      },
      "15": {
        loc: {
          start: {
            line: 167,
            column: 4
          },
          end: {
            line: 173,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 167,
            column: 4
          },
          end: {
            line: 173,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 167
      },
      "16": {
        loc: {
          start: {
            line: 186,
            column: 17
          },
          end: {
            line: 186,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 186,
            column: 17
          },
          end: {
            line: 186,
            column: 29
          }
        }, {
          start: {
            line: 186,
            column: 33
          },
          end: {
            line: 186,
            column: 52
          }
        }],
        line: 186
      },
      "17": {
        loc: {
          start: {
            line: 189,
            column: 4
          },
          end: {
            line: 191,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 189,
            column: 4
          },
          end: {
            line: 191,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 189
      },
      "18": {
        loc: {
          start: {
            line: 204,
            column: 9
          },
          end: {
            line: 204,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 204,
            column: 9
          },
          end: {
            line: 204,
            column: 23
          }
        }, {
          start: {
            line: 204,
            column: 27
          },
          end: {
            line: 204,
            column: 55
          }
        }],
        line: 204
      },
      "19": {
        loc: {
          start: {
            line: 210,
            column: 53
          },
          end: {
            line: 210,
            column: 74
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 210,
            column: 66
          },
          end: {
            line: 210,
            column: 74
          }
        }],
        line: 210
      },
      "20": {
        loc: {
          start: {
            line: 216,
            column: 9
          },
          end: {
            line: 216,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 216,
            column: 9
          },
          end: {
            line: 216,
            column: 23
          }
        }, {
          start: {
            line: 216,
            column: 27
          },
          end: {
            line: 216,
            column: 55
          }
        }],
        line: 216
      },
      "21": {
        loc: {
          start: {
            line: 265,
            column: 2
          },
          end: {
            line: 292,
            column: 3
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 266,
            column: 4
          },
          end: {
            line: 267,
            column: 61
          }
        }, {
          start: {
            line: 268,
            column: 4
          },
          end: {
            line: 269,
            column: 48
          }
        }, {
          start: {
            line: 270,
            column: 4
          },
          end: {
            line: 271,
            column: 41
          }
        }, {
          start: {
            line: 272,
            column: 4
          },
          end: {
            line: 273,
            column: 37
          }
        }, {
          start: {
            line: 274,
            column: 4
          },
          end: {
            line: 275,
            column: 40
          }
        }, {
          start: {
            line: 276,
            column: 4
          },
          end: {
            line: 277,
            column: 41
          }
        }, {
          start: {
            line: 278,
            column: 4
          },
          end: {
            line: 279,
            column: 43
          }
        }, {
          start: {
            line: 280,
            column: 4
          },
          end: {
            line: 281,
            column: 46
          }
        }, {
          start: {
            line: 282,
            column: 4
          },
          end: {
            line: 283,
            column: 34
          }
        }, {
          start: {
            line: 284,
            column: 4
          },
          end: {
            line: 285,
            column: 42
          }
        }, {
          start: {
            line: 286,
            column: 4
          },
          end: {
            line: 287,
            column: 51
          }
        }, {
          start: {
            line: 288,
            column: 4
          },
          end: {
            line: 289,
            column: 49
          }
        }, {
          start: {
            line: 290,
            column: 4
          },
          end: {
            line: 291,
            column: 41
          }
        }],
        line: 265
      },
      "22": {
        loc: {
          start: {
            line: 300,
            column: 2
          },
          end: {
            line: 300,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 300,
            column: 15
          },
          end: {
            line: 300,
            column: 16
          }
        }],
        line: 300
      },
      "23": {
        loc: {
          start: {
            line: 307,
            column: 4
          },
          end: {
            line: 317,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 307,
            column: 4
          },
          end: {
            line: 317,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 307
      },
      "24": {
        loc: {
          start: {
            line: 307,
            column: 8
          },
          end: {
            line: 307,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 307,
            column: 8
          },
          end: {
            line: 307,
            column: 27
          }
        }, {
          start: {
            line: 307,
            column: 31
          },
          end: {
            line: 307,
            column: 45
          }
        }],
        line: 307
      },
      "25": {
        loc: {
          start: {
            line: 308,
            column: 6
          },
          end: {
            line: 316,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 308,
            column: 6
          },
          end: {
            line: 316,
            column: 7
          }
        }, {
          start: {
            line: 313,
            column: 13
          },
          end: {
            line: 316,
            column: 7
          }
        }],
        line: 308
      },
      "26": {
        loc: {
          start: {
            line: 321,
            column: 12
          },
          end: {
            line: 321,
            column: 70
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 321,
            column: 33
          },
          end: {
            line: 321,
            column: 65
          }
        }, {
          start: {
            line: 321,
            column: 68
          },
          end: {
            line: 321,
            column: 70
          }
        }],
        line: 321
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0],
      "9": [0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0],
      "13": [0, 0],
      "14": [0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0],
      "20": [0, 0],
      "21": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "22": [0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0a9d406ddf0ce86a4931119e1f767895da120015"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2fd3e7m4j = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2fd3e7m4j();
/**
 * Database Utilities and Query Optimization
 * 
 * This module provides optimized database operations with proper error handling,
 * connection management, and query optimization for the multi-tenant application.
 */

import { getDbPool } from './db-config';

// Query result interfaces

// Database operation result

// Query cache for prepared statements
const queryCache =
/* istanbul ignore next */
(cov_2fd3e7m4j().s[0]++, new Map());

/**
 * Execute a query with proper error handling and optimization
 */
export async function executeQuery(query, params =
/* istanbul ignore next */
(cov_2fd3e7m4j().b[0][0]++, []), options =
/* istanbul ignore next */
(cov_2fd3e7m4j().b[1][0]++, {})) {
  /* istanbul ignore next */
  cov_2fd3e7m4j().f[0]++;
  let client =
  /* istanbul ignore next */
  (cov_2fd3e7m4j().s[1]++, null);
  /* istanbul ignore next */
  cov_2fd3e7m4j().s[2]++;
  try {
    const pool =
    /* istanbul ignore next */
    (cov_2fd3e7m4j().s[3]++, await getDbPool());
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[4]++;
    client = await pool.connect();

    // Set query timeout if specified
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[5]++;
    if (options.timeout) {
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[2][0]++;
      cov_2fd3e7m4j().s[6]++;
      await client.query(`SET statement_timeout = ${options.timeout}`);
    } else
    /* istanbul ignore next */
    {
      cov_2fd3e7m4j().b[2][1]++;
    }

    // Set schema if specified (for multi-tenant support)
    cov_2fd3e7m4j().s[7]++;
    if (options.schema) {
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[3][0]++;
      cov_2fd3e7m4j().s[8]++;
      await client.query(`SET search_path TO "${options.schema}", public`);
    } else
    /* istanbul ignore next */
    {
      cov_2fd3e7m4j().b[3][1]++;
    }
    const startTime =
    /* istanbul ignore next */
    (cov_2fd3e7m4j().s[9]++, Date.now());
    const result =
    /* istanbul ignore next */
    (cov_2fd3e7m4j().s[10]++, await client.query(query, params));
    const duration =
    /* istanbul ignore next */
    (cov_2fd3e7m4j().s[11]++, Date.now() - startTime);

    // Log slow queries (> 1 second)
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[12]++;
    if (duration > 1000) {
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[4][0]++;
      cov_2fd3e7m4j().s[13]++;
      console.warn(`Slow query detected (${duration}ms):`, {
        query: query.substring(0, 100) + '...',
        params: params.length,
        schema: options.schema
      });
    } else
    /* istanbul ignore next */
    {
      cov_2fd3e7m4j().b[4][1]++;
    }
    cov_2fd3e7m4j().s[14]++;
    return {
      success: true,
      data: result.rows,
      rowCount:
      /* istanbul ignore next */
      (cov_2fd3e7m4j().b[5][0]++, result.rowCount) ||
      /* istanbul ignore next */
      (cov_2fd3e7m4j().b[5][1]++, 0)
    };
  } catch (error) {
    const dbError =
    /* istanbul ignore next */
    (cov_2fd3e7m4j().s[15]++, error);
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[16]++;
    console.error('Database query error:', {
      error: dbError.message,
      code: dbError.code,
      detail: dbError.detail,
      query: query.substring(0, 100) + '...',
      schema: options.schema
    });
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[17]++;
    return {
      success: false,
      error: getDatabaseErrorMessage(dbError),
      errorCode:
      /* istanbul ignore next */
      (cov_2fd3e7m4j().b[6][0]++, dbError.code) ||
      /* istanbul ignore next */
      (cov_2fd3e7m4j().b[6][1]++, 'UNKNOWN_ERROR')
    };
  } finally {
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[18]++;
    if (client) {
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[7][0]++;
      cov_2fd3e7m4j().s[19]++;
      client.release();
    } else
    /* istanbul ignore next */
    {
      cov_2fd3e7m4j().b[7][1]++;
    }
  }
}

/**
 * Execute a single query and return the first row
 */
export async function executeQuerySingle(query, params =
/* istanbul ignore next */
(cov_2fd3e7m4j().b[8][0]++, []), options =
/* istanbul ignore next */
(cov_2fd3e7m4j().b[9][0]++, {})) {
  /* istanbul ignore next */
  cov_2fd3e7m4j().f[1]++;
  const result =
  /* istanbul ignore next */
  (cov_2fd3e7m4j().s[20]++, await executeQuery(query, params, options));
  /* istanbul ignore next */
  cov_2fd3e7m4j().s[21]++;
  if (!result.success) {
    /* istanbul ignore next */
    cov_2fd3e7m4j().b[10][0]++;
    cov_2fd3e7m4j().s[22]++;
    return result;
  } else
  /* istanbul ignore next */
  {
    cov_2fd3e7m4j().b[10][1]++;
  }
  cov_2fd3e7m4j().s[23]++;
  return {
    success: true,
    data:
    /* istanbul ignore next */
    (cov_2fd3e7m4j().b[11][0]++, result.data?.[0]) ||
    /* istanbul ignore next */
    (cov_2fd3e7m4j().b[11][1]++, null),
    rowCount: result.rowCount
  };
}

/**
 * Execute a transaction with multiple queries
 */
export async function executeTransaction(queries, options =
/* istanbul ignore next */
(cov_2fd3e7m4j().b[12][0]++, {})) {
  /* istanbul ignore next */
  cov_2fd3e7m4j().f[2]++;
  let client =
  /* istanbul ignore next */
  (cov_2fd3e7m4j().s[24]++, null);
  /* istanbul ignore next */
  cov_2fd3e7m4j().s[25]++;
  try {
    const pool =
    /* istanbul ignore next */
    (cov_2fd3e7m4j().s[26]++, await getDbPool());
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[27]++;
    client = await pool.connect();

    // Set schema if specified
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[28]++;
    if (options.schema) {
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[13][0]++;
      cov_2fd3e7m4j().s[29]++;
      await client.query(`SET search_path TO "${options.schema}", public`);
    } else
    /* istanbul ignore next */
    {
      cov_2fd3e7m4j().b[13][1]++;
    }
    cov_2fd3e7m4j().s[30]++;
    await client.query('BEGIN');
    const results =
    /* istanbul ignore next */
    (cov_2fd3e7m4j().s[31]++, []);
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[32]++;
    for (const {
      query,
      params =
      /* istanbul ignore next */
      (cov_2fd3e7m4j().b[14][0]++, [])
    } of queries) {
      const result =
      /* istanbul ignore next */
      (cov_2fd3e7m4j().s[33]++, await client.query(query, params));
      /* istanbul ignore next */
      cov_2fd3e7m4j().s[34]++;
      results.push(result.rows);
    }
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[35]++;
    await client.query('COMMIT');
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[36]++;
    return {
      success: true,
      data: results,
      rowCount: results.reduce((sum, rows) => {
        /* istanbul ignore next */
        cov_2fd3e7m4j().f[3]++;
        cov_2fd3e7m4j().s[37]++;
        return sum + rows.length;
      }, 0)
    };
  } catch (error) {
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[38]++;
    if (client) {
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[15][0]++;
      cov_2fd3e7m4j().s[39]++;
      try {
        /* istanbul ignore next */
        cov_2fd3e7m4j().s[40]++;
        await client.query('ROLLBACK');
      } catch (rollbackError) {
        /* istanbul ignore next */
        cov_2fd3e7m4j().s[41]++;
        console.error('Error rolling back transaction:', rollbackError);
      }
    } else
    /* istanbul ignore next */
    {
      cov_2fd3e7m4j().b[15][1]++;
    }
    const dbError =
    /* istanbul ignore next */
    (cov_2fd3e7m4j().s[42]++, error);
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[43]++;
    console.error('Transaction error:', {
      error: dbError.message,
      code: dbError.code,
      detail: dbError.detail,
      schema: options.schema
    });
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[44]++;
    return {
      success: false,
      error: getDatabaseErrorMessage(dbError),
      errorCode:
      /* istanbul ignore next */
      (cov_2fd3e7m4j().b[16][0]++, dbError.code) ||
      /* istanbul ignore next */
      (cov_2fd3e7m4j().b[16][1]++, 'TRANSACTION_ERROR')
    };
  } finally {
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[45]++;
    if (client) {
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[17][0]++;
      cov_2fd3e7m4j().s[46]++;
      client.release();
    } else
    /* istanbul ignore next */
    {
      cov_2fd3e7m4j().b[17][1]++;
    }
  }
}

/**
 * Check if a schema exists
 */
export async function schemaExists(schemaName) {
  /* istanbul ignore next */
  cov_2fd3e7m4j().f[4]++;
  const result =
  /* istanbul ignore next */
  (cov_2fd3e7m4j().s[47]++, await executeQuerySingle('SELECT EXISTS(SELECT 1 FROM information_schema.schemata WHERE schema_name = $1) as exists', [schemaName]));
  /* istanbul ignore next */
  cov_2fd3e7m4j().s[48]++;
  return /* istanbul ignore next */(cov_2fd3e7m4j().b[18][0]++, result.success) &&
  /* istanbul ignore next */
  (cov_2fd3e7m4j().b[18][1]++, result.data?.exists === true);
}

/**
 * Check if a table exists in a schema
 */
export async function tableExists(tableName, schemaName =
/* istanbul ignore next */
(cov_2fd3e7m4j().b[19][0]++, 'public')) {
  /* istanbul ignore next */
  cov_2fd3e7m4j().f[5]++;
  const result =
  /* istanbul ignore next */
  (cov_2fd3e7m4j().s[49]++, await executeQuerySingle('SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_schema = $1 AND table_name = $2) as exists', [schemaName, tableName]));
  /* istanbul ignore next */
  cov_2fd3e7m4j().s[50]++;
  return /* istanbul ignore next */(cov_2fd3e7m4j().b[20][0]++, result.success) &&
  /* istanbul ignore next */
  (cov_2fd3e7m4j().b[20][1]++, result.data?.exists === true);
}

/**
 * Get database connection health status
 */
export async function getConnectionHealth() {
  /* istanbul ignore next */
  cov_2fd3e7m4j().f[6]++;
  cov_2fd3e7m4j().s[51]++;
  try {
    const pool =
    /* istanbul ignore next */
    (cov_2fd3e7m4j().s[52]++, await getDbPool());
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[53]++;
    return {
      healthy: true,
      totalConnections: pool.totalCount,
      idleConnections: pool.idleCount,
      waitingCount: pool.waitingCount
    };
  } catch (error) {
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[54]++;
    console.error('Database health check failed:', error);
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[55]++;
    return {
      healthy: false,
      totalConnections: 0,
      idleConnections: 0,
      waitingCount: 0
    };
  }
}

/**
 * Close database connections (for graceful shutdown)
 */
export async function closeDatabase() {
  /* istanbul ignore next */
  cov_2fd3e7m4j().f[7]++;
  cov_2fd3e7m4j().s[56]++;
  try {
    const pool =
    /* istanbul ignore next */
    (cov_2fd3e7m4j().s[57]++, await getDbPool());
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[58]++;
    await pool.end();
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[59]++;
    console.log('Database connections closed successfully');
  } catch (error) {
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[60]++;
    console.error('Error closing database connections:', error);
  }
}

/**
 * Get user-friendly error message from database error
 */
function getDatabaseErrorMessage(error) {
  /* istanbul ignore next */
  cov_2fd3e7m4j().f[8]++;
  cov_2fd3e7m4j().s[61]++;
  switch (error.code) {
    case '23505':
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[21][0]++;
      cov_2fd3e7m4j().s[62]++;
      // unique_violation
      return 'A record with this information already exists';
    case '23503':
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[21][1]++;
      cov_2fd3e7m4j().s[63]++;
      // foreign_key_violation
      return 'Referenced record does not exist';
    case '23502':
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[21][2]++;
      cov_2fd3e7m4j().s[64]++;
      // not_null_violation
      return 'Required field is missing';
    case '23514':
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[21][3]++;
      cov_2fd3e7m4j().s[65]++;
      // check_violation
      return 'Invalid data provided';
    case '42P01':
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[21][4]++;
      cov_2fd3e7m4j().s[66]++;
      // undefined_table
      return 'Database table not found';
    case '42703':
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[21][5]++;
      cov_2fd3e7m4j().s[67]++;
      // undefined_column
      return 'Database column not found';
    case '42883':
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[21][6]++;
      cov_2fd3e7m4j().s[68]++;
      // undefined_function
      return 'Database function not found';
    case '28P01':
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[21][7]++;
      cov_2fd3e7m4j().s[69]++;
      // invalid_password
      return 'Database authentication failed';
    case '3D000':
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[21][8]++;
      cov_2fd3e7m4j().s[70]++;
      // invalid_catalog_name
      return 'Database not found';
    case '08006':
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[21][9]++;
      cov_2fd3e7m4j().s[71]++;
      // connection_failure
      return 'Database connection failed';
    case '57P03':
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[21][10]++;
      cov_2fd3e7m4j().s[72]++;
      // cannot_connect_now
      return 'Database is temporarily unavailable';
    case '53300':
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[21][11]++;
      cov_2fd3e7m4j().s[73]++;
      // too_many_connections
      return 'Database connection limit reached';
    default:
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[21][12]++;
      cov_2fd3e7m4j().s[74]++;
      return 'A database error occurred';
  }
}

/**
 * Build WHERE clause with proper parameter binding
 */
export function buildWhereClause(conditions, startIndex =
/* istanbul ignore next */
(cov_2fd3e7m4j().b[22][0]++, 1)) {
  /* istanbul ignore next */
  cov_2fd3e7m4j().f[9]++;
  const params =
  /* istanbul ignore next */
  (cov_2fd3e7m4j().s[75]++, []);
  const clauses =
  /* istanbul ignore next */
  (cov_2fd3e7m4j().s[76]++, []);
  let paramIndex =
  /* istanbul ignore next */
  (cov_2fd3e7m4j().s[77]++, startIndex);
  /* istanbul ignore next */
  cov_2fd3e7m4j().s[78]++;
  for (const [key, value] of Object.entries(conditions)) {
    /* istanbul ignore next */
    cov_2fd3e7m4j().s[79]++;
    if (
    /* istanbul ignore next */
    (cov_2fd3e7m4j().b[24][0]++, value !== undefined) &&
    /* istanbul ignore next */
    (cov_2fd3e7m4j().b[24][1]++, value !== null)) {
      /* istanbul ignore next */
      cov_2fd3e7m4j().b[23][0]++;
      cov_2fd3e7m4j().s[80]++;
      if (Array.isArray(value)) {
        /* istanbul ignore next */
        cov_2fd3e7m4j().b[25][0]++;
        // Handle IN clause
        const placeholders =
        /* istanbul ignore next */
        (cov_2fd3e7m4j().s[81]++, value.map(() => {
          /* istanbul ignore next */
          cov_2fd3e7m4j().f[10]++;
          cov_2fd3e7m4j().s[82]++;
          return `$${paramIndex++}`;
        }).join(', '));
        /* istanbul ignore next */
        cov_2fd3e7m4j().s[83]++;
        clauses.push(`${key} IN (${placeholders})`);
        /* istanbul ignore next */
        cov_2fd3e7m4j().s[84]++;
        params.push(...value);
      } else {
        /* istanbul ignore next */
        cov_2fd3e7m4j().b[25][1]++;
        cov_2fd3e7m4j().s[85]++;
        clauses.push(`${key} = $${paramIndex++}`);
        /* istanbul ignore next */
        cov_2fd3e7m4j().s[86]++;
        params.push(value);
      }
    } else
    /* istanbul ignore next */
    {
      cov_2fd3e7m4j().b[23][1]++;
    }
  }
  /* istanbul ignore next */
  cov_2fd3e7m4j().s[87]++;
  return {
    clause: clauses.length > 0 ?
    /* istanbul ignore next */
    (cov_2fd3e7m4j().b[26][0]++, `WHERE ${clauses.join(' AND ')}`) :
    /* istanbul ignore next */
    (cov_2fd3e7m4j().b[26][1]++, ''),
    params,
    nextIndex: paramIndex
  };
}

/**
 * Escape identifier for safe SQL construction
 */
export function escapeIdentifier(identifier) {
  /* istanbul ignore next */
  cov_2fd3e7m4j().f[11]++;
  cov_2fd3e7m4j().s[88]++;
  return `"${identifier.replace(/"/g, '""')}"`;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfMmZkM2U3bTRqIiwiYWN0dWFsQ292ZXJhZ2UiLCJnZXREYlBvb2wiLCJxdWVyeUNhY2hlIiwicyIsIk1hcCIsImV4ZWN1dGVRdWVyeSIsInF1ZXJ5IiwicGFyYW1zIiwiYiIsIm9wdGlvbnMiLCJmIiwiY2xpZW50IiwicG9vbCIsImNvbm5lY3QiLCJ0aW1lb3V0Iiwic2NoZW1hIiwic3RhcnRUaW1lIiwiRGF0ZSIsIm5vdyIsInJlc3VsdCIsImR1cmF0aW9uIiwiY29uc29sZSIsIndhcm4iLCJzdWJzdHJpbmciLCJsZW5ndGgiLCJzdWNjZXNzIiwiZGF0YSIsInJvd3MiLCJyb3dDb3VudCIsImVycm9yIiwiZGJFcnJvciIsIm1lc3NhZ2UiLCJjb2RlIiwiZGV0YWlsIiwiZ2V0RGF0YWJhc2VFcnJvck1lc3NhZ2UiLCJlcnJvckNvZGUiLCJyZWxlYXNlIiwiZXhlY3V0ZVF1ZXJ5U2luZ2xlIiwiZXhlY3V0ZVRyYW5zYWN0aW9uIiwicXVlcmllcyIsInJlc3VsdHMiLCJwdXNoIiwicmVkdWNlIiwic3VtIiwicm9sbGJhY2tFcnJvciIsInNjaGVtYUV4aXN0cyIsInNjaGVtYU5hbWUiLCJleGlzdHMiLCJ0YWJsZUV4aXN0cyIsInRhYmxlTmFtZSIsImdldENvbm5lY3Rpb25IZWFsdGgiLCJoZWFsdGh5IiwidG90YWxDb25uZWN0aW9ucyIsInRvdGFsQ291bnQiLCJpZGxlQ29ubmVjdGlvbnMiLCJpZGxlQ291bnQiLCJ3YWl0aW5nQ291bnQiLCJjbG9zZURhdGFiYXNlIiwiZW5kIiwibG9nIiwiYnVpbGRXaGVyZUNsYXVzZSIsImNvbmRpdGlvbnMiLCJzdGFydEluZGV4IiwiY2xhdXNlcyIsInBhcmFtSW5kZXgiLCJrZXkiLCJ2YWx1ZSIsIk9iamVjdCIsImVudHJpZXMiLCJ1bmRlZmluZWQiLCJBcnJheSIsImlzQXJyYXkiLCJwbGFjZWhvbGRlcnMiLCJtYXAiLCJqb2luIiwiY2xhdXNlIiwibmV4dEluZGV4IiwiZXNjYXBlSWRlbnRpZmllciIsImlkZW50aWZpZXIiLCJyZXBsYWNlIl0sInNvdXJjZXMiOlsiZGF0YWJhc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBEYXRhYmFzZSBVdGlsaXRpZXMgYW5kIFF1ZXJ5IE9wdGltaXphdGlvblxuICogXG4gKiBUaGlzIG1vZHVsZSBwcm92aWRlcyBvcHRpbWl6ZWQgZGF0YWJhc2Ugb3BlcmF0aW9ucyB3aXRoIHByb3BlciBlcnJvciBoYW5kbGluZyxcbiAqIGNvbm5lY3Rpb24gbWFuYWdlbWVudCwgYW5kIHF1ZXJ5IG9wdGltaXphdGlvbiBmb3IgdGhlIG11bHRpLXRlbmFudCBhcHBsaWNhdGlvbi5cbiAqL1xuXG5pbXBvcnQgeyBQb29sLCBQb29sQ2xpZW50LCBRdWVyeVJlc3VsdCB9IGZyb20gJ3BnJztcbmltcG9ydCB7IGdldERiUG9vbCB9IGZyb20gJy4vZGItY29uZmlnJztcblxuLy8gUXVlcnkgcmVzdWx0IGludGVyZmFjZXNcbmV4cG9ydCBpbnRlcmZhY2UgUXVlcnlPcHRpb25zIHtcbiAgdGltZW91dD86IG51bWJlcjtcbiAgcmV0cmllcz86IG51bWJlcjtcbiAgc2NoZW1hPzogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIERhdGFiYXNlRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gIGNvZGU/OiBzdHJpbmc7XG4gIGRldGFpbD86IHN0cmluZztcbiAgaGludD86IHN0cmluZztcbiAgcG9zaXRpb24/OiBzdHJpbmc7XG4gIGludGVybmFsUG9zaXRpb24/OiBzdHJpbmc7XG4gIGludGVybmFsUXVlcnk/OiBzdHJpbmc7XG4gIHdoZXJlPzogc3RyaW5nO1xuICBzY2hlbWE/OiBzdHJpbmc7XG4gIHRhYmxlPzogc3RyaW5nO1xuICBjb2x1bW4/OiBzdHJpbmc7XG4gIGRhdGFUeXBlPzogc3RyaW5nO1xuICBjb25zdHJhaW50Pzogc3RyaW5nO1xufVxuXG4vLyBEYXRhYmFzZSBvcGVyYXRpb24gcmVzdWx0XG5leHBvcnQgaW50ZXJmYWNlIERiUmVzdWx0PFQgPSBhbnk+IHtcbiAgc3VjY2VzczogYm9vbGVhbjtcbiAgZGF0YT86IFQ7XG4gIGVycm9yPzogc3RyaW5nO1xuICBlcnJvckNvZGU/OiBzdHJpbmc7XG4gIHJvd0NvdW50PzogbnVtYmVyO1xufVxuXG4vLyBRdWVyeSBjYWNoZSBmb3IgcHJlcGFyZWQgc3RhdGVtZW50c1xuY29uc3QgcXVlcnlDYWNoZSA9IG5ldyBNYXA8c3RyaW5nLCBzdHJpbmc+KCk7XG5cbi8qKlxuICogRXhlY3V0ZSBhIHF1ZXJ5IHdpdGggcHJvcGVyIGVycm9yIGhhbmRsaW5nIGFuZCBvcHRpbWl6YXRpb25cbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGV4ZWN1dGVRdWVyeTxUID0gYW55PihcbiAgcXVlcnk6IHN0cmluZyxcbiAgcGFyYW1zOiBhbnlbXSA9IFtdLFxuICBvcHRpb25zOiBRdWVyeU9wdGlvbnMgPSB7fVxuKTogUHJvbWlzZTxEYlJlc3VsdDxUW10+PiB7XG4gIGxldCBjbGllbnQ6IFBvb2xDbGllbnQgfCBudWxsID0gbnVsbDtcbiAgXG4gIHRyeSB7XG4gICAgY29uc3QgcG9vbCA9IGF3YWl0IGdldERiUG9vbCgpO1xuICAgIGNsaWVudCA9IGF3YWl0IHBvb2wuY29ubmVjdCgpO1xuICAgIFxuICAgIC8vIFNldCBxdWVyeSB0aW1lb3V0IGlmIHNwZWNpZmllZFxuICAgIGlmIChvcHRpb25zLnRpbWVvdXQpIHtcbiAgICAgIGF3YWl0IGNsaWVudC5xdWVyeShgU0VUIHN0YXRlbWVudF90aW1lb3V0ID0gJHtvcHRpb25zLnRpbWVvdXR9YCk7XG4gICAgfVxuICAgIFxuICAgIC8vIFNldCBzY2hlbWEgaWYgc3BlY2lmaWVkIChmb3IgbXVsdGktdGVuYW50IHN1cHBvcnQpXG4gICAgaWYgKG9wdGlvbnMuc2NoZW1hKSB7XG4gICAgICBhd2FpdCBjbGllbnQucXVlcnkoYFNFVCBzZWFyY2hfcGF0aCBUTyBcIiR7b3B0aW9ucy5zY2hlbWF9XCIsIHB1YmxpY2ApO1xuICAgIH1cbiAgICBcbiAgICBjb25zdCBzdGFydFRpbWUgPSBEYXRlLm5vdygpO1xuICAgIGNvbnN0IHJlc3VsdDogUXVlcnlSZXN1bHQgPSBhd2FpdCBjbGllbnQucXVlcnkocXVlcnksIHBhcmFtcyk7XG4gICAgY29uc3QgZHVyYXRpb24gPSBEYXRlLm5vdygpIC0gc3RhcnRUaW1lO1xuICAgIFxuICAgIC8vIExvZyBzbG93IHF1ZXJpZXMgKD4gMSBzZWNvbmQpXG4gICAgaWYgKGR1cmF0aW9uID4gMTAwMCkge1xuICAgICAgY29uc29sZS53YXJuKGBTbG93IHF1ZXJ5IGRldGVjdGVkICgke2R1cmF0aW9ufW1zKTpgLCB7XG4gICAgICAgIHF1ZXJ5OiBxdWVyeS5zdWJzdHJpbmcoMCwgMTAwKSArICcuLi4nLFxuICAgICAgICBwYXJhbXM6IHBhcmFtcy5sZW5ndGgsXG4gICAgICAgIHNjaGVtYTogb3B0aW9ucy5zY2hlbWFcbiAgICAgIH0pO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4ge1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIGRhdGE6IHJlc3VsdC5yb3dzLFxuICAgICAgcm93Q291bnQ6IHJlc3VsdC5yb3dDb3VudCB8fCAwXG4gICAgfTtcbiAgICBcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zdCBkYkVycm9yID0gZXJyb3IgYXMgRGF0YWJhc2VFcnJvcjtcbiAgICBjb25zb2xlLmVycm9yKCdEYXRhYmFzZSBxdWVyeSBlcnJvcjonLCB7XG4gICAgICBlcnJvcjogZGJFcnJvci5tZXNzYWdlLFxuICAgICAgY29kZTogZGJFcnJvci5jb2RlLFxuICAgICAgZGV0YWlsOiBkYkVycm9yLmRldGFpbCxcbiAgICAgIHF1ZXJ5OiBxdWVyeS5zdWJzdHJpbmcoMCwgMTAwKSArICcuLi4nLFxuICAgICAgc2NoZW1hOiBvcHRpb25zLnNjaGVtYVxuICAgIH0pO1xuICAgIFxuICAgIHJldHVybiB7XG4gICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgIGVycm9yOiBnZXREYXRhYmFzZUVycm9yTWVzc2FnZShkYkVycm9yKSxcbiAgICAgIGVycm9yQ29kZTogZGJFcnJvci5jb2RlIHx8ICdVTktOT1dOX0VSUk9SJ1xuICAgIH07XG4gIH0gZmluYWxseSB7XG4gICAgaWYgKGNsaWVudCkge1xuICAgICAgY2xpZW50LnJlbGVhc2UoKTtcbiAgICB9XG4gIH1cbn1cblxuLyoqXG4gKiBFeGVjdXRlIGEgc2luZ2xlIHF1ZXJ5IGFuZCByZXR1cm4gdGhlIGZpcnN0IHJvd1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZXhlY3V0ZVF1ZXJ5U2luZ2xlPFQgPSBhbnk+KFxuICBxdWVyeTogc3RyaW5nLFxuICBwYXJhbXM6IGFueVtdID0gW10sXG4gIG9wdGlvbnM6IFF1ZXJ5T3B0aW9ucyA9IHt9XG4pOiBQcm9taXNlPERiUmVzdWx0PFQ+PiB7XG4gIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGV4ZWN1dGVRdWVyeTxUPihxdWVyeSwgcGFyYW1zLCBvcHRpb25zKTtcbiAgXG4gIGlmICghcmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICByZXR1cm4gcmVzdWx0O1xuICB9XG4gIFxuICByZXR1cm4ge1xuICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgZGF0YTogcmVzdWx0LmRhdGE/LlswXSB8fCBudWxsLFxuICAgIHJvd0NvdW50OiByZXN1bHQucm93Q291bnRcbiAgfTtcbn1cblxuLyoqXG4gKiBFeGVjdXRlIGEgdHJhbnNhY3Rpb24gd2l0aCBtdWx0aXBsZSBxdWVyaWVzXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBleGVjdXRlVHJhbnNhY3Rpb248VCA9IGFueT4oXG4gIHF1ZXJpZXM6IEFycmF5PHsgcXVlcnk6IHN0cmluZzsgcGFyYW1zPzogYW55W10gfT4sXG4gIG9wdGlvbnM6IFF1ZXJ5T3B0aW9ucyA9IHt9XG4pOiBQcm9taXNlPERiUmVzdWx0PFRbXT4+IHtcbiAgbGV0IGNsaWVudDogUG9vbENsaWVudCB8IG51bGwgPSBudWxsO1xuICBcbiAgdHJ5IHtcbiAgICBjb25zdCBwb29sID0gYXdhaXQgZ2V0RGJQb29sKCk7XG4gICAgY2xpZW50ID0gYXdhaXQgcG9vbC5jb25uZWN0KCk7XG4gICAgXG4gICAgLy8gU2V0IHNjaGVtYSBpZiBzcGVjaWZpZWRcbiAgICBpZiAob3B0aW9ucy5zY2hlbWEpIHtcbiAgICAgIGF3YWl0IGNsaWVudC5xdWVyeShgU0VUIHNlYXJjaF9wYXRoIFRPIFwiJHtvcHRpb25zLnNjaGVtYX1cIiwgcHVibGljYCk7XG4gICAgfVxuICAgIFxuICAgIGF3YWl0IGNsaWVudC5xdWVyeSgnQkVHSU4nKTtcbiAgICBcbiAgICBjb25zdCByZXN1bHRzOiBhbnlbXSA9IFtdO1xuICAgIFxuICAgIGZvciAoY29uc3QgeyBxdWVyeSwgcGFyYW1zID0gW10gfSBvZiBxdWVyaWVzKSB7XG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBjbGllbnQucXVlcnkocXVlcnksIHBhcmFtcyk7XG4gICAgICByZXN1bHRzLnB1c2gocmVzdWx0LnJvd3MpO1xuICAgIH1cbiAgICBcbiAgICBhd2FpdCBjbGllbnQucXVlcnkoJ0NPTU1JVCcpO1xuICAgIFxuICAgIHJldHVybiB7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgZGF0YTogcmVzdWx0cyxcbiAgICAgIHJvd0NvdW50OiByZXN1bHRzLnJlZHVjZSgoc3VtLCByb3dzKSA9PiBzdW0gKyByb3dzLmxlbmd0aCwgMClcbiAgICB9O1xuICAgIFxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGlmIChjbGllbnQpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IGNsaWVudC5xdWVyeSgnUk9MTEJBQ0snKTtcbiAgICAgIH0gY2F0Y2ggKHJvbGxiYWNrRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igcm9sbGluZyBiYWNrIHRyYW5zYWN0aW9uOicsIHJvbGxiYWNrRXJyb3IpO1xuICAgICAgfVxuICAgIH1cbiAgICBcbiAgICBjb25zdCBkYkVycm9yID0gZXJyb3IgYXMgRGF0YWJhc2VFcnJvcjtcbiAgICBjb25zb2xlLmVycm9yKCdUcmFuc2FjdGlvbiBlcnJvcjonLCB7XG4gICAgICBlcnJvcjogZGJFcnJvci5tZXNzYWdlLFxuICAgICAgY29kZTogZGJFcnJvci5jb2RlLFxuICAgICAgZGV0YWlsOiBkYkVycm9yLmRldGFpbCxcbiAgICAgIHNjaGVtYTogb3B0aW9ucy5zY2hlbWFcbiAgICB9KTtcbiAgICBcbiAgICByZXR1cm4ge1xuICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICBlcnJvcjogZ2V0RGF0YWJhc2VFcnJvck1lc3NhZ2UoZGJFcnJvciksXG4gICAgICBlcnJvckNvZGU6IGRiRXJyb3IuY29kZSB8fCAnVFJBTlNBQ1RJT05fRVJST1InXG4gICAgfTtcbiAgfSBmaW5hbGx5IHtcbiAgICBpZiAoY2xpZW50KSB7XG4gICAgICBjbGllbnQucmVsZWFzZSgpO1xuICAgIH1cbiAgfVxufVxuXG4vKipcbiAqIENoZWNrIGlmIGEgc2NoZW1hIGV4aXN0c1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gc2NoZW1hRXhpc3RzKHNjaGVtYU5hbWU6IHN0cmluZyk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICBjb25zdCByZXN1bHQgPSBhd2FpdCBleGVjdXRlUXVlcnlTaW5nbGU8eyBleGlzdHM6IGJvb2xlYW4gfT4oXG4gICAgJ1NFTEVDVCBFWElTVFMoU0VMRUNUIDEgRlJPTSBpbmZvcm1hdGlvbl9zY2hlbWEuc2NoZW1hdGEgV0hFUkUgc2NoZW1hX25hbWUgPSAkMSkgYXMgZXhpc3RzJyxcbiAgICBbc2NoZW1hTmFtZV1cbiAgKTtcbiAgXG4gIHJldHVybiByZXN1bHQuc3VjY2VzcyAmJiByZXN1bHQuZGF0YT8uZXhpc3RzID09PSB0cnVlO1xufVxuXG4vKipcbiAqIENoZWNrIGlmIGEgdGFibGUgZXhpc3RzIGluIGEgc2NoZW1hXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB0YWJsZUV4aXN0cyh0YWJsZU5hbWU6IHN0cmluZywgc2NoZW1hTmFtZSA9ICdwdWJsaWMnKTogUHJvbWlzZTxib29sZWFuPiB7XG4gIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGV4ZWN1dGVRdWVyeVNpbmdsZTx7IGV4aXN0czogYm9vbGVhbiB9PihcbiAgICAnU0VMRUNUIEVYSVNUUyhTRUxFQ1QgMSBGUk9NIGluZm9ybWF0aW9uX3NjaGVtYS50YWJsZXMgV0hFUkUgdGFibGVfc2NoZW1hID0gJDEgQU5EIHRhYmxlX25hbWUgPSAkMikgYXMgZXhpc3RzJyxcbiAgICBbc2NoZW1hTmFtZSwgdGFibGVOYW1lXVxuICApO1xuICBcbiAgcmV0dXJuIHJlc3VsdC5zdWNjZXNzICYmIHJlc3VsdC5kYXRhPy5leGlzdHMgPT09IHRydWU7XG59XG5cbi8qKlxuICogR2V0IGRhdGFiYXNlIGNvbm5lY3Rpb24gaGVhbHRoIHN0YXR1c1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0Q29ubmVjdGlvbkhlYWx0aCgpOiBQcm9taXNlPHtcbiAgaGVhbHRoeTogYm9vbGVhbjtcbiAgdG90YWxDb25uZWN0aW9uczogbnVtYmVyO1xuICBpZGxlQ29ubmVjdGlvbnM6IG51bWJlcjtcbiAgd2FpdGluZ0NvdW50OiBudW1iZXI7XG59PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcG9vbCA9IGF3YWl0IGdldERiUG9vbCgpO1xuICAgIFxuICAgIHJldHVybiB7XG4gICAgICBoZWFsdGh5OiB0cnVlLFxuICAgICAgdG90YWxDb25uZWN0aW9uczogcG9vbC50b3RhbENvdW50LFxuICAgICAgaWRsZUNvbm5lY3Rpb25zOiBwb29sLmlkbGVDb3VudCxcbiAgICAgIHdhaXRpbmdDb3VudDogcG9vbC53YWl0aW5nQ291bnRcbiAgICB9O1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0RhdGFiYXNlIGhlYWx0aCBjaGVjayBmYWlsZWQ6JywgZXJyb3IpO1xuICAgIHJldHVybiB7XG4gICAgICBoZWFsdGh5OiBmYWxzZSxcbiAgICAgIHRvdGFsQ29ubmVjdGlvbnM6IDAsXG4gICAgICBpZGxlQ29ubmVjdGlvbnM6IDAsXG4gICAgICB3YWl0aW5nQ291bnQ6IDBcbiAgICB9O1xuICB9XG59XG5cbi8qKlxuICogQ2xvc2UgZGF0YWJhc2UgY29ubmVjdGlvbnMgKGZvciBncmFjZWZ1bCBzaHV0ZG93bilcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNsb3NlRGF0YWJhc2UoKTogUHJvbWlzZTx2b2lkPiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcG9vbCA9IGF3YWl0IGdldERiUG9vbCgpO1xuICAgIGF3YWl0IHBvb2wuZW5kKCk7XG4gICAgY29uc29sZS5sb2coJ0RhdGFiYXNlIGNvbm5lY3Rpb25zIGNsb3NlZCBzdWNjZXNzZnVsbHknKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjbG9zaW5nIGRhdGFiYXNlIGNvbm5lY3Rpb25zOicsIGVycm9yKTtcbiAgfVxufVxuXG4vKipcbiAqIEdldCB1c2VyLWZyaWVuZGx5IGVycm9yIG1lc3NhZ2UgZnJvbSBkYXRhYmFzZSBlcnJvclxuICovXG5mdW5jdGlvbiBnZXREYXRhYmFzZUVycm9yTWVzc2FnZShlcnJvcjogRGF0YWJhc2VFcnJvcik6IHN0cmluZyB7XG4gIHN3aXRjaCAoZXJyb3IuY29kZSkge1xuICAgIGNhc2UgJzIzNTA1JzogLy8gdW5pcXVlX3Zpb2xhdGlvblxuICAgICAgcmV0dXJuICdBIHJlY29yZCB3aXRoIHRoaXMgaW5mb3JtYXRpb24gYWxyZWFkeSBleGlzdHMnO1xuICAgIGNhc2UgJzIzNTAzJzogLy8gZm9yZWlnbl9rZXlfdmlvbGF0aW9uXG4gICAgICByZXR1cm4gJ1JlZmVyZW5jZWQgcmVjb3JkIGRvZXMgbm90IGV4aXN0JztcbiAgICBjYXNlICcyMzUwMic6IC8vIG5vdF9udWxsX3Zpb2xhdGlvblxuICAgICAgcmV0dXJuICdSZXF1aXJlZCBmaWVsZCBpcyBtaXNzaW5nJztcbiAgICBjYXNlICcyMzUxNCc6IC8vIGNoZWNrX3Zpb2xhdGlvblxuICAgICAgcmV0dXJuICdJbnZhbGlkIGRhdGEgcHJvdmlkZWQnO1xuICAgIGNhc2UgJzQyUDAxJzogLy8gdW5kZWZpbmVkX3RhYmxlXG4gICAgICByZXR1cm4gJ0RhdGFiYXNlIHRhYmxlIG5vdCBmb3VuZCc7XG4gICAgY2FzZSAnNDI3MDMnOiAvLyB1bmRlZmluZWRfY29sdW1uXG4gICAgICByZXR1cm4gJ0RhdGFiYXNlIGNvbHVtbiBub3QgZm91bmQnO1xuICAgIGNhc2UgJzQyODgzJzogLy8gdW5kZWZpbmVkX2Z1bmN0aW9uXG4gICAgICByZXR1cm4gJ0RhdGFiYXNlIGZ1bmN0aW9uIG5vdCBmb3VuZCc7XG4gICAgY2FzZSAnMjhQMDEnOiAvLyBpbnZhbGlkX3Bhc3N3b3JkXG4gICAgICByZXR1cm4gJ0RhdGFiYXNlIGF1dGhlbnRpY2F0aW9uIGZhaWxlZCc7XG4gICAgY2FzZSAnM0QwMDAnOiAvLyBpbnZhbGlkX2NhdGFsb2dfbmFtZVxuICAgICAgcmV0dXJuICdEYXRhYmFzZSBub3QgZm91bmQnO1xuICAgIGNhc2UgJzA4MDA2JzogLy8gY29ubmVjdGlvbl9mYWlsdXJlXG4gICAgICByZXR1cm4gJ0RhdGFiYXNlIGNvbm5lY3Rpb24gZmFpbGVkJztcbiAgICBjYXNlICc1N1AwMyc6IC8vIGNhbm5vdF9jb25uZWN0X25vd1xuICAgICAgcmV0dXJuICdEYXRhYmFzZSBpcyB0ZW1wb3JhcmlseSB1bmF2YWlsYWJsZSc7XG4gICAgY2FzZSAnNTMzMDAnOiAvLyB0b29fbWFueV9jb25uZWN0aW9uc1xuICAgICAgcmV0dXJuICdEYXRhYmFzZSBjb25uZWN0aW9uIGxpbWl0IHJlYWNoZWQnO1xuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gJ0EgZGF0YWJhc2UgZXJyb3Igb2NjdXJyZWQnO1xuICB9XG59XG5cbi8qKlxuICogQnVpbGQgV0hFUkUgY2xhdXNlIHdpdGggcHJvcGVyIHBhcmFtZXRlciBiaW5kaW5nXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBidWlsZFdoZXJlQ2xhdXNlKFxuICBjb25kaXRpb25zOiBSZWNvcmQ8c3RyaW5nLCBhbnk+LFxuICBzdGFydEluZGV4ID0gMVxuKTogeyBjbGF1c2U6IHN0cmluZzsgcGFyYW1zOiBhbnlbXTsgbmV4dEluZGV4OiBudW1iZXIgfSB7XG4gIGNvbnN0IHBhcmFtczogYW55W10gPSBbXTtcbiAgY29uc3QgY2xhdXNlczogc3RyaW5nW10gPSBbXTtcbiAgbGV0IHBhcmFtSW5kZXggPSBzdGFydEluZGV4O1xuICBcbiAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoY29uZGl0aW9ucykpIHtcbiAgICBpZiAodmFsdWUgIT09IHVuZGVmaW5lZCAmJiB2YWx1ZSAhPT0gbnVsbCkge1xuICAgICAgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSB7XG4gICAgICAgIC8vIEhhbmRsZSBJTiBjbGF1c2VcbiAgICAgICAgY29uc3QgcGxhY2Vob2xkZXJzID0gdmFsdWUubWFwKCgpID0+IGAkJHtwYXJhbUluZGV4Kyt9YCkuam9pbignLCAnKTtcbiAgICAgICAgY2xhdXNlcy5wdXNoKGAke2tleX0gSU4gKCR7cGxhY2Vob2xkZXJzfSlgKTtcbiAgICAgICAgcGFyYW1zLnB1c2goLi4udmFsdWUpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY2xhdXNlcy5wdXNoKGAke2tleX0gPSAkJHtwYXJhbUluZGV4Kyt9YCk7XG4gICAgICAgIHBhcmFtcy5wdXNoKHZhbHVlKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgXG4gIHJldHVybiB7XG4gICAgY2xhdXNlOiBjbGF1c2VzLmxlbmd0aCA+IDAgPyBgV0hFUkUgJHtjbGF1c2VzLmpvaW4oJyBBTkQgJyl9YCA6ICcnLFxuICAgIHBhcmFtcyxcbiAgICBuZXh0SW5kZXg6IHBhcmFtSW5kZXhcbiAgfTtcbn1cblxuLyoqXG4gKiBFc2NhcGUgaWRlbnRpZmllciBmb3Igc2FmZSBTUUwgY29uc3RydWN0aW9uXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBlc2NhcGVJZGVudGlmaWVyKGlkZW50aWZpZXI6IHN0cmluZyk6IHN0cmluZyB7XG4gIHJldHVybiBgXCIke2lkZW50aWZpZXIucmVwbGFjZSgvXCIvZywgJ1wiXCInKX1cImA7XG59XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQWVZO0lBQUFBLGFBQUEsWUFBQUEsQ0FBQTtNQUFBLE9BQUFDLGNBQUE7SUFBQTtFQUFBO0VBQUEsT0FBQUEsY0FBQTtBQUFBO0FBQUFELGFBQUE7QUFmWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBR0EsU0FBU0UsU0FBUyxRQUFRLGFBQWE7O0FBRXZDOztBQXNCQTs7QUFTQTtBQUNBLE1BQU1DLFVBQVU7QUFBQTtBQUFBLENBQUFILGFBQUEsR0FBQUksQ0FBQSxPQUFHLElBQUlDLEdBQUcsQ0FBaUIsQ0FBQzs7QUFFNUM7QUFDQTtBQUNBO0FBQ0EsT0FBTyxlQUFlQyxZQUFZQSxDQUNoQ0MsS0FBYSxFQUNiQyxNQUFhO0FBQUE7QUFBQSxDQUFBUixhQUFBLEdBQUFTLENBQUEsVUFBRyxFQUFFLEdBQ2xCQyxPQUFxQjtBQUFBO0FBQUEsQ0FBQVYsYUFBQSxHQUFBUyxDQUFBLFVBQUcsQ0FBQyxDQUFDLEdBQ0Y7RUFBQTtFQUFBVCxhQUFBLEdBQUFXLENBQUE7RUFDeEIsSUFBSUMsTUFBeUI7RUFBQTtFQUFBLENBQUFaLGFBQUEsR0FBQUksQ0FBQSxPQUFHLElBQUk7RUFBQztFQUFBSixhQUFBLEdBQUFJLENBQUE7RUFFckMsSUFBSTtJQUNGLE1BQU1TLElBQUk7SUFBQTtJQUFBLENBQUFiLGFBQUEsR0FBQUksQ0FBQSxPQUFHLE1BQU1GLFNBQVMsQ0FBQyxDQUFDO0lBQUM7SUFBQUYsYUFBQSxHQUFBSSxDQUFBO0lBQy9CUSxNQUFNLEdBQUcsTUFBTUMsSUFBSSxDQUFDQyxPQUFPLENBQUMsQ0FBQzs7SUFFN0I7SUFBQTtJQUFBZCxhQUFBLEdBQUFJLENBQUE7SUFDQSxJQUFJTSxPQUFPLENBQUNLLE9BQU8sRUFBRTtNQUFBO01BQUFmLGFBQUEsR0FBQVMsQ0FBQTtNQUFBVCxhQUFBLEdBQUFJLENBQUE7TUFDbkIsTUFBTVEsTUFBTSxDQUFDTCxLQUFLLENBQUMsMkJBQTJCRyxPQUFPLENBQUNLLE9BQU8sRUFBRSxDQUFDO0lBQ2xFLENBQUM7SUFBQTtJQUFBO01BQUFmLGFBQUEsR0FBQVMsQ0FBQTtJQUFBOztJQUVEO0lBQUFULGFBQUEsR0FBQUksQ0FBQTtJQUNBLElBQUlNLE9BQU8sQ0FBQ00sTUFBTSxFQUFFO01BQUE7TUFBQWhCLGFBQUEsR0FBQVMsQ0FBQTtNQUFBVCxhQUFBLEdBQUFJLENBQUE7TUFDbEIsTUFBTVEsTUFBTSxDQUFDTCxLQUFLLENBQUMsdUJBQXVCRyxPQUFPLENBQUNNLE1BQU0sV0FBVyxDQUFDO0lBQ3RFLENBQUM7SUFBQTtJQUFBO01BQUFoQixhQUFBLEdBQUFTLENBQUE7SUFBQTtJQUVELE1BQU1RLFNBQVM7SUFBQTtJQUFBLENBQUFqQixhQUFBLEdBQUFJLENBQUEsT0FBR2MsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQztJQUM1QixNQUFNQyxNQUFtQjtJQUFBO0lBQUEsQ0FBQXBCLGFBQUEsR0FBQUksQ0FBQSxRQUFHLE1BQU1RLE1BQU0sQ0FBQ0wsS0FBSyxDQUFDQSxLQUFLLEVBQUVDLE1BQU0sQ0FBQztJQUM3RCxNQUFNYSxRQUFRO0lBQUE7SUFBQSxDQUFBckIsYUFBQSxHQUFBSSxDQUFBLFFBQUdjLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUMsR0FBR0YsU0FBUzs7SUFFdkM7SUFBQTtJQUFBakIsYUFBQSxHQUFBSSxDQUFBO0lBQ0EsSUFBSWlCLFFBQVEsR0FBRyxJQUFJLEVBQUU7TUFBQTtNQUFBckIsYUFBQSxHQUFBUyxDQUFBO01BQUFULGFBQUEsR0FBQUksQ0FBQTtNQUNuQmtCLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDLHdCQUF3QkYsUUFBUSxNQUFNLEVBQUU7UUFDbkRkLEtBQUssRUFBRUEsS0FBSyxDQUFDaUIsU0FBUyxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsR0FBRyxLQUFLO1FBQ3RDaEIsTUFBTSxFQUFFQSxNQUFNLENBQUNpQixNQUFNO1FBQ3JCVCxNQUFNLEVBQUVOLE9BQU8sQ0FBQ007TUFDbEIsQ0FBQyxDQUFDO0lBQ0osQ0FBQztJQUFBO0lBQUE7TUFBQWhCLGFBQUEsR0FBQVMsQ0FBQTtJQUFBO0lBQUFULGFBQUEsR0FBQUksQ0FBQTtJQUVELE9BQU87TUFDTHNCLE9BQU8sRUFBRSxJQUFJO01BQ2JDLElBQUksRUFBRVAsTUFBTSxDQUFDUSxJQUFJO01BQ2pCQyxRQUFRO01BQUU7TUFBQSxDQUFBN0IsYUFBQSxHQUFBUyxDQUFBLFVBQUFXLE1BQU0sQ0FBQ1MsUUFBUTtNQUFBO01BQUEsQ0FBQTdCLGFBQUEsR0FBQVMsQ0FBQSxVQUFJLENBQUM7SUFDaEMsQ0FBQztFQUVILENBQUMsQ0FBQyxPQUFPcUIsS0FBSyxFQUFFO0lBQ2QsTUFBTUMsT0FBTztJQUFBO0lBQUEsQ0FBQS9CLGFBQUEsR0FBQUksQ0FBQSxRQUFHMEIsS0FBSyxDQUFpQjtJQUFDO0lBQUE5QixhQUFBLEdBQUFJLENBQUE7SUFDdkNrQixPQUFPLENBQUNRLEtBQUssQ0FBQyx1QkFBdUIsRUFBRTtNQUNyQ0EsS0FBSyxFQUFFQyxPQUFPLENBQUNDLE9BQU87TUFDdEJDLElBQUksRUFBRUYsT0FBTyxDQUFDRSxJQUFJO01BQ2xCQyxNQUFNLEVBQUVILE9BQU8sQ0FBQ0csTUFBTTtNQUN0QjNCLEtBQUssRUFBRUEsS0FBSyxDQUFDaUIsU0FBUyxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsR0FBRyxLQUFLO01BQ3RDUixNQUFNLEVBQUVOLE9BQU8sQ0FBQ007SUFDbEIsQ0FBQyxDQUFDO0lBQUM7SUFBQWhCLGFBQUEsR0FBQUksQ0FBQTtJQUVILE9BQU87TUFDTHNCLE9BQU8sRUFBRSxLQUFLO01BQ2RJLEtBQUssRUFBRUssdUJBQXVCLENBQUNKLE9BQU8sQ0FBQztNQUN2Q0ssU0FBUztNQUFFO01BQUEsQ0FBQXBDLGFBQUEsR0FBQVMsQ0FBQSxVQUFBc0IsT0FBTyxDQUFDRSxJQUFJO01BQUE7TUFBQSxDQUFBakMsYUFBQSxHQUFBUyxDQUFBLFVBQUksZUFBZTtJQUM1QyxDQUFDO0VBQ0gsQ0FBQyxTQUFTO0lBQUE7SUFBQVQsYUFBQSxHQUFBSSxDQUFBO0lBQ1IsSUFBSVEsTUFBTSxFQUFFO01BQUE7TUFBQVosYUFBQSxHQUFBUyxDQUFBO01BQUFULGFBQUEsR0FBQUksQ0FBQTtNQUNWUSxNQUFNLENBQUN5QixPQUFPLENBQUMsQ0FBQztJQUNsQixDQUFDO0lBQUE7SUFBQTtNQUFBckMsYUFBQSxHQUFBUyxDQUFBO0lBQUE7RUFDSDtBQUNGOztBQUVBO0FBQ0E7QUFDQTtBQUNBLE9BQU8sZUFBZTZCLGtCQUFrQkEsQ0FDdEMvQixLQUFhLEVBQ2JDLE1BQWE7QUFBQTtBQUFBLENBQUFSLGFBQUEsR0FBQVMsQ0FBQSxVQUFHLEVBQUUsR0FDbEJDLE9BQXFCO0FBQUE7QUFBQSxDQUFBVixhQUFBLEdBQUFTLENBQUEsVUFBRyxDQUFDLENBQUMsR0FDSjtFQUFBO0VBQUFULGFBQUEsR0FBQVcsQ0FBQTtFQUN0QixNQUFNUyxNQUFNO0VBQUE7RUFBQSxDQUFBcEIsYUFBQSxHQUFBSSxDQUFBLFFBQUcsTUFBTUUsWUFBWSxDQUFJQyxLQUFLLEVBQUVDLE1BQU0sRUFBRUUsT0FBTyxDQUFDO0VBQUM7RUFBQVYsYUFBQSxHQUFBSSxDQUFBO0VBRTdELElBQUksQ0FBQ2dCLE1BQU0sQ0FBQ00sT0FBTyxFQUFFO0lBQUE7SUFBQTFCLGFBQUEsR0FBQVMsQ0FBQTtJQUFBVCxhQUFBLEdBQUFJLENBQUE7SUFDbkIsT0FBT2dCLE1BQU07RUFDZixDQUFDO0VBQUE7RUFBQTtJQUFBcEIsYUFBQSxHQUFBUyxDQUFBO0VBQUE7RUFBQVQsYUFBQSxHQUFBSSxDQUFBO0VBRUQsT0FBTztJQUNMc0IsT0FBTyxFQUFFLElBQUk7SUFDYkMsSUFBSTtJQUFFO0lBQUEsQ0FBQTNCLGFBQUEsR0FBQVMsQ0FBQSxXQUFBVyxNQUFNLENBQUNPLElBQUksR0FBRyxDQUFDLENBQUM7SUFBQTtJQUFBLENBQUEzQixhQUFBLEdBQUFTLENBQUEsV0FBSSxJQUFJO0lBQzlCb0IsUUFBUSxFQUFFVCxNQUFNLENBQUNTO0VBQ25CLENBQUM7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxPQUFPLGVBQWVVLGtCQUFrQkEsQ0FDdENDLE9BQWlELEVBQ2pEOUIsT0FBcUI7QUFBQTtBQUFBLENBQUFWLGFBQUEsR0FBQVMsQ0FBQSxXQUFHLENBQUMsQ0FBQyxHQUNGO0VBQUE7RUFBQVQsYUFBQSxHQUFBVyxDQUFBO0VBQ3hCLElBQUlDLE1BQXlCO0VBQUE7RUFBQSxDQUFBWixhQUFBLEdBQUFJLENBQUEsUUFBRyxJQUFJO0VBQUM7RUFBQUosYUFBQSxHQUFBSSxDQUFBO0VBRXJDLElBQUk7SUFDRixNQUFNUyxJQUFJO0lBQUE7SUFBQSxDQUFBYixhQUFBLEdBQUFJLENBQUEsUUFBRyxNQUFNRixTQUFTLENBQUMsQ0FBQztJQUFDO0lBQUFGLGFBQUEsR0FBQUksQ0FBQTtJQUMvQlEsTUFBTSxHQUFHLE1BQU1DLElBQUksQ0FBQ0MsT0FBTyxDQUFDLENBQUM7O0lBRTdCO0lBQUE7SUFBQWQsYUFBQSxHQUFBSSxDQUFBO0lBQ0EsSUFBSU0sT0FBTyxDQUFDTSxNQUFNLEVBQUU7TUFBQTtNQUFBaEIsYUFBQSxHQUFBUyxDQUFBO01BQUFULGFBQUEsR0FBQUksQ0FBQTtNQUNsQixNQUFNUSxNQUFNLENBQUNMLEtBQUssQ0FBQyx1QkFBdUJHLE9BQU8sQ0FBQ00sTUFBTSxXQUFXLENBQUM7SUFDdEUsQ0FBQztJQUFBO0lBQUE7TUFBQWhCLGFBQUEsR0FBQVMsQ0FBQTtJQUFBO0lBQUFULGFBQUEsR0FBQUksQ0FBQTtJQUVELE1BQU1RLE1BQU0sQ0FBQ0wsS0FBSyxDQUFDLE9BQU8sQ0FBQztJQUUzQixNQUFNa0MsT0FBYztJQUFBO0lBQUEsQ0FBQXpDLGFBQUEsR0FBQUksQ0FBQSxRQUFHLEVBQUU7SUFBQztJQUFBSixhQUFBLEdBQUFJLENBQUE7SUFFMUIsS0FBSyxNQUFNO01BQUVHLEtBQUs7TUFBRUMsTUFBTTtNQUFBO01BQUEsQ0FBQVIsYUFBQSxHQUFBUyxDQUFBLFdBQUcsRUFBRTtJQUFDLENBQUMsSUFBSStCLE9BQU8sRUFBRTtNQUM1QyxNQUFNcEIsTUFBTTtNQUFBO01BQUEsQ0FBQXBCLGFBQUEsR0FBQUksQ0FBQSxRQUFHLE1BQU1RLE1BQU0sQ0FBQ0wsS0FBSyxDQUFDQSxLQUFLLEVBQUVDLE1BQU0sQ0FBQztNQUFDO01BQUFSLGFBQUEsR0FBQUksQ0FBQTtNQUNqRHFDLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDdEIsTUFBTSxDQUFDUSxJQUFJLENBQUM7SUFDM0I7SUFBQztJQUFBNUIsYUFBQSxHQUFBSSxDQUFBO0lBRUQsTUFBTVEsTUFBTSxDQUFDTCxLQUFLLENBQUMsUUFBUSxDQUFDO0lBQUM7SUFBQVAsYUFBQSxHQUFBSSxDQUFBO0lBRTdCLE9BQU87TUFDTHNCLE9BQU8sRUFBRSxJQUFJO01BQ2JDLElBQUksRUFBRWMsT0FBTztNQUNiWixRQUFRLEVBQUVZLE9BQU8sQ0FBQ0UsTUFBTSxDQUFDLENBQUNDLEdBQUcsRUFBRWhCLElBQUksS0FBSztRQUFBO1FBQUE1QixhQUFBLEdBQUFXLENBQUE7UUFBQVgsYUFBQSxHQUFBSSxDQUFBO1FBQUEsT0FBQXdDLEdBQUcsR0FBR2hCLElBQUksQ0FBQ0gsTUFBTTtNQUFELENBQUMsRUFBRSxDQUFDO0lBQzlELENBQUM7RUFFSCxDQUFDLENBQUMsT0FBT0ssS0FBSyxFQUFFO0lBQUE7SUFBQTlCLGFBQUEsR0FBQUksQ0FBQTtJQUNkLElBQUlRLE1BQU0sRUFBRTtNQUFBO01BQUFaLGFBQUEsR0FBQVMsQ0FBQTtNQUFBVCxhQUFBLEdBQUFJLENBQUE7TUFDVixJQUFJO1FBQUE7UUFBQUosYUFBQSxHQUFBSSxDQUFBO1FBQ0YsTUFBTVEsTUFBTSxDQUFDTCxLQUFLLENBQUMsVUFBVSxDQUFDO01BQ2hDLENBQUMsQ0FBQyxPQUFPc0MsYUFBYSxFQUFFO1FBQUE7UUFBQTdDLGFBQUEsR0FBQUksQ0FBQTtRQUN0QmtCLE9BQU8sQ0FBQ1EsS0FBSyxDQUFDLGlDQUFpQyxFQUFFZSxhQUFhLENBQUM7TUFDakU7SUFDRixDQUFDO0lBQUE7SUFBQTtNQUFBN0MsYUFBQSxHQUFBUyxDQUFBO0lBQUE7SUFFRCxNQUFNc0IsT0FBTztJQUFBO0lBQUEsQ0FBQS9CLGFBQUEsR0FBQUksQ0FBQSxRQUFHMEIsS0FBSyxDQUFpQjtJQUFDO0lBQUE5QixhQUFBLEdBQUFJLENBQUE7SUFDdkNrQixPQUFPLENBQUNRLEtBQUssQ0FBQyxvQkFBb0IsRUFBRTtNQUNsQ0EsS0FBSyxFQUFFQyxPQUFPLENBQUNDLE9BQU87TUFDdEJDLElBQUksRUFBRUYsT0FBTyxDQUFDRSxJQUFJO01BQ2xCQyxNQUFNLEVBQUVILE9BQU8sQ0FBQ0csTUFBTTtNQUN0QmxCLE1BQU0sRUFBRU4sT0FBTyxDQUFDTTtJQUNsQixDQUFDLENBQUM7SUFBQztJQUFBaEIsYUFBQSxHQUFBSSxDQUFBO0lBRUgsT0FBTztNQUNMc0IsT0FBTyxFQUFFLEtBQUs7TUFDZEksS0FBSyxFQUFFSyx1QkFBdUIsQ0FBQ0osT0FBTyxDQUFDO01BQ3ZDSyxTQUFTO01BQUU7TUFBQSxDQUFBcEMsYUFBQSxHQUFBUyxDQUFBLFdBQUFzQixPQUFPLENBQUNFLElBQUk7TUFBQTtNQUFBLENBQUFqQyxhQUFBLEdBQUFTLENBQUEsV0FBSSxtQkFBbUI7SUFDaEQsQ0FBQztFQUNILENBQUMsU0FBUztJQUFBO0lBQUFULGFBQUEsR0FBQUksQ0FBQTtJQUNSLElBQUlRLE1BQU0sRUFBRTtNQUFBO01BQUFaLGFBQUEsR0FBQVMsQ0FBQTtNQUFBVCxhQUFBLEdBQUFJLENBQUE7TUFDVlEsTUFBTSxDQUFDeUIsT0FBTyxDQUFDLENBQUM7SUFDbEIsQ0FBQztJQUFBO0lBQUE7TUFBQXJDLGFBQUEsR0FBQVMsQ0FBQTtJQUFBO0VBQ0g7QUFDRjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxPQUFPLGVBQWVxQyxZQUFZQSxDQUFDQyxVQUFrQixFQUFvQjtFQUFBO0VBQUEvQyxhQUFBLEdBQUFXLENBQUE7RUFDdkUsTUFBTVMsTUFBTTtFQUFBO0VBQUEsQ0FBQXBCLGFBQUEsR0FBQUksQ0FBQSxRQUFHLE1BQU1rQyxrQkFBa0IsQ0FDckMsMkZBQTJGLEVBQzNGLENBQUNTLFVBQVUsQ0FDYixDQUFDO0VBQUM7RUFBQS9DLGFBQUEsR0FBQUksQ0FBQTtFQUVGLE9BQU8sMkJBQUFKLGFBQUEsR0FBQVMsQ0FBQSxXQUFBVyxNQUFNLENBQUNNLE9BQU87RUFBQTtFQUFBLENBQUExQixhQUFBLEdBQUFTLENBQUEsV0FBSVcsTUFBTSxDQUFDTyxJQUFJLEVBQUVxQixNQUFNLEtBQUssSUFBSTtBQUN2RDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxPQUFPLGVBQWVDLFdBQVdBLENBQUNDLFNBQWlCLEVBQUVILFVBQVU7QUFBQTtBQUFBLENBQUEvQyxhQUFBLEdBQUFTLENBQUEsV0FBRyxRQUFRLEdBQW9CO0VBQUE7RUFBQVQsYUFBQSxHQUFBVyxDQUFBO0VBQzVGLE1BQU1TLE1BQU07RUFBQTtFQUFBLENBQUFwQixhQUFBLEdBQUFJLENBQUEsUUFBRyxNQUFNa0Msa0JBQWtCLENBQ3JDLDhHQUE4RyxFQUM5RyxDQUFDUyxVQUFVLEVBQUVHLFNBQVMsQ0FDeEIsQ0FBQztFQUFDO0VBQUFsRCxhQUFBLEdBQUFJLENBQUE7RUFFRixPQUFPLDJCQUFBSixhQUFBLEdBQUFTLENBQUEsV0FBQVcsTUFBTSxDQUFDTSxPQUFPO0VBQUE7RUFBQSxDQUFBMUIsYUFBQSxHQUFBUyxDQUFBLFdBQUlXLE1BQU0sQ0FBQ08sSUFBSSxFQUFFcUIsTUFBTSxLQUFLLElBQUk7QUFDdkQ7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsT0FBTyxlQUFlRyxtQkFBbUJBLENBQUEsRUFLdEM7RUFBQTtFQUFBbkQsYUFBQSxHQUFBVyxDQUFBO0VBQUFYLGFBQUEsR0FBQUksQ0FBQTtFQUNELElBQUk7SUFDRixNQUFNUyxJQUFJO0lBQUE7SUFBQSxDQUFBYixhQUFBLEdBQUFJLENBQUEsUUFBRyxNQUFNRixTQUFTLENBQUMsQ0FBQztJQUFDO0lBQUFGLGFBQUEsR0FBQUksQ0FBQTtJQUUvQixPQUFPO01BQ0xnRCxPQUFPLEVBQUUsSUFBSTtNQUNiQyxnQkFBZ0IsRUFBRXhDLElBQUksQ0FBQ3lDLFVBQVU7TUFDakNDLGVBQWUsRUFBRTFDLElBQUksQ0FBQzJDLFNBQVM7TUFDL0JDLFlBQVksRUFBRTVDLElBQUksQ0FBQzRDO0lBQ3JCLENBQUM7RUFDSCxDQUFDLENBQUMsT0FBTzNCLEtBQUssRUFBRTtJQUFBO0lBQUE5QixhQUFBLEdBQUFJLENBQUE7SUFDZGtCLE9BQU8sQ0FBQ1EsS0FBSyxDQUFDLCtCQUErQixFQUFFQSxLQUFLLENBQUM7SUFBQztJQUFBOUIsYUFBQSxHQUFBSSxDQUFBO0lBQ3RELE9BQU87TUFDTGdELE9BQU8sRUFBRSxLQUFLO01BQ2RDLGdCQUFnQixFQUFFLENBQUM7TUFDbkJFLGVBQWUsRUFBRSxDQUFDO01BQ2xCRSxZQUFZLEVBQUU7SUFDaEIsQ0FBQztFQUNIO0FBQ0Y7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsT0FBTyxlQUFlQyxhQUFhQSxDQUFBLEVBQWtCO0VBQUE7RUFBQTFELGFBQUEsR0FBQVcsQ0FBQTtFQUFBWCxhQUFBLEdBQUFJLENBQUE7RUFDbkQsSUFBSTtJQUNGLE1BQU1TLElBQUk7SUFBQTtJQUFBLENBQUFiLGFBQUEsR0FBQUksQ0FBQSxRQUFHLE1BQU1GLFNBQVMsQ0FBQyxDQUFDO0lBQUM7SUFBQUYsYUFBQSxHQUFBSSxDQUFBO0lBQy9CLE1BQU1TLElBQUksQ0FBQzhDLEdBQUcsQ0FBQyxDQUFDO0lBQUM7SUFBQTNELGFBQUEsR0FBQUksQ0FBQTtJQUNqQmtCLE9BQU8sQ0FBQ3NDLEdBQUcsQ0FBQywwQ0FBMEMsQ0FBQztFQUN6RCxDQUFDLENBQUMsT0FBTzlCLEtBQUssRUFBRTtJQUFBO0lBQUE5QixhQUFBLEdBQUFJLENBQUE7SUFDZGtCLE9BQU8sQ0FBQ1EsS0FBSyxDQUFDLHFDQUFxQyxFQUFFQSxLQUFLLENBQUM7RUFDN0Q7QUFDRjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxTQUFTSyx1QkFBdUJBLENBQUNMLEtBQW9CLEVBQVU7RUFBQTtFQUFBOUIsYUFBQSxHQUFBVyxDQUFBO0VBQUFYLGFBQUEsR0FBQUksQ0FBQTtFQUM3RCxRQUFRMEIsS0FBSyxDQUFDRyxJQUFJO0lBQ2hCLEtBQUssT0FBTztNQUFBO01BQUFqQyxhQUFBLEdBQUFTLENBQUE7TUFBQVQsYUFBQSxHQUFBSSxDQUFBO01BQUU7TUFDWixPQUFPLCtDQUErQztJQUN4RCxLQUFLLE9BQU87TUFBQTtNQUFBSixhQUFBLEdBQUFTLENBQUE7TUFBQVQsYUFBQSxHQUFBSSxDQUFBO01BQUU7TUFDWixPQUFPLGtDQUFrQztJQUMzQyxLQUFLLE9BQU87TUFBQTtNQUFBSixhQUFBLEdBQUFTLENBQUE7TUFBQVQsYUFBQSxHQUFBSSxDQUFBO01BQUU7TUFDWixPQUFPLDJCQUEyQjtJQUNwQyxLQUFLLE9BQU87TUFBQTtNQUFBSixhQUFBLEdBQUFTLENBQUE7TUFBQVQsYUFBQSxHQUFBSSxDQUFBO01BQUU7TUFDWixPQUFPLHVCQUF1QjtJQUNoQyxLQUFLLE9BQU87TUFBQTtNQUFBSixhQUFBLEdBQUFTLENBQUE7TUFBQVQsYUFBQSxHQUFBSSxDQUFBO01BQUU7TUFDWixPQUFPLDBCQUEwQjtJQUNuQyxLQUFLLE9BQU87TUFBQTtNQUFBSixhQUFBLEdBQUFTLENBQUE7TUFBQVQsYUFBQSxHQUFBSSxDQUFBO01BQUU7TUFDWixPQUFPLDJCQUEyQjtJQUNwQyxLQUFLLE9BQU87TUFBQTtNQUFBSixhQUFBLEdBQUFTLENBQUE7TUFBQVQsYUFBQSxHQUFBSSxDQUFBO01BQUU7TUFDWixPQUFPLDZCQUE2QjtJQUN0QyxLQUFLLE9BQU87TUFBQTtNQUFBSixhQUFBLEdBQUFTLENBQUE7TUFBQVQsYUFBQSxHQUFBSSxDQUFBO01BQUU7TUFDWixPQUFPLGdDQUFnQztJQUN6QyxLQUFLLE9BQU87TUFBQTtNQUFBSixhQUFBLEdBQUFTLENBQUE7TUFBQVQsYUFBQSxHQUFBSSxDQUFBO01BQUU7TUFDWixPQUFPLG9CQUFvQjtJQUM3QixLQUFLLE9BQU87TUFBQTtNQUFBSixhQUFBLEdBQUFTLENBQUE7TUFBQVQsYUFBQSxHQUFBSSxDQUFBO01BQUU7TUFDWixPQUFPLDRCQUE0QjtJQUNyQyxLQUFLLE9BQU87TUFBQTtNQUFBSixhQUFBLEdBQUFTLENBQUE7TUFBQVQsYUFBQSxHQUFBSSxDQUFBO01BQUU7TUFDWixPQUFPLHFDQUFxQztJQUM5QyxLQUFLLE9BQU87TUFBQTtNQUFBSixhQUFBLEdBQUFTLENBQUE7TUFBQVQsYUFBQSxHQUFBSSxDQUFBO01BQUU7TUFDWixPQUFPLG1DQUFtQztJQUM1QztNQUFBO01BQUFKLGFBQUEsR0FBQVMsQ0FBQTtNQUFBVCxhQUFBLEdBQUFJLENBQUE7TUFDRSxPQUFPLDJCQUEyQjtFQUN0QztBQUNGOztBQUVBO0FBQ0E7QUFDQTtBQUNBLE9BQU8sU0FBU3lELGdCQUFnQkEsQ0FDOUJDLFVBQStCLEVBQy9CQyxVQUFVO0FBQUE7QUFBQSxDQUFBL0QsYUFBQSxHQUFBUyxDQUFBLFdBQUcsQ0FBQyxHQUN3QztFQUFBO0VBQUFULGFBQUEsR0FBQVcsQ0FBQTtFQUN0RCxNQUFNSCxNQUFhO0VBQUE7RUFBQSxDQUFBUixhQUFBLEdBQUFJLENBQUEsUUFBRyxFQUFFO0VBQ3hCLE1BQU00RCxPQUFpQjtFQUFBO0VBQUEsQ0FBQWhFLGFBQUEsR0FBQUksQ0FBQSxRQUFHLEVBQUU7RUFDNUIsSUFBSTZELFVBQVU7RUFBQTtFQUFBLENBQUFqRSxhQUFBLEdBQUFJLENBQUEsUUFBRzJELFVBQVU7RUFBQztFQUFBL0QsYUFBQSxHQUFBSSxDQUFBO0VBRTVCLEtBQUssTUFBTSxDQUFDOEQsR0FBRyxFQUFFQyxLQUFLLENBQUMsSUFBSUMsTUFBTSxDQUFDQyxPQUFPLENBQUNQLFVBQVUsQ0FBQyxFQUFFO0lBQUE7SUFBQTlELGFBQUEsR0FBQUksQ0FBQTtJQUNyRDtJQUFJO0lBQUEsQ0FBQUosYUFBQSxHQUFBUyxDQUFBLFdBQUEwRCxLQUFLLEtBQUtHLFNBQVM7SUFBQTtJQUFBLENBQUF0RSxhQUFBLEdBQUFTLENBQUEsV0FBSTBELEtBQUssS0FBSyxJQUFJLEdBQUU7TUFBQTtNQUFBbkUsYUFBQSxHQUFBUyxDQUFBO01BQUFULGFBQUEsR0FBQUksQ0FBQTtNQUN6QyxJQUFJbUUsS0FBSyxDQUFDQyxPQUFPLENBQUNMLEtBQUssQ0FBQyxFQUFFO1FBQUE7UUFBQW5FLGFBQUEsR0FBQVMsQ0FBQTtRQUN4QjtRQUNBLE1BQU1nRSxZQUFZO1FBQUE7UUFBQSxDQUFBekUsYUFBQSxHQUFBSSxDQUFBLFFBQUcrRCxLQUFLLENBQUNPLEdBQUcsQ0FBQyxNQUFNO1VBQUE7VUFBQTFFLGFBQUEsR0FBQVcsQ0FBQTtVQUFBWCxhQUFBLEdBQUFJLENBQUE7VUFBQSxXQUFJNkQsVUFBVSxFQUFFLEVBQUU7UUFBRCxDQUFDLENBQUMsQ0FBQ1UsSUFBSSxDQUFDLElBQUksQ0FBQztRQUFDO1FBQUEzRSxhQUFBLEdBQUFJLENBQUE7UUFDcEU0RCxPQUFPLENBQUN0QixJQUFJLENBQUMsR0FBR3dCLEdBQUcsUUFBUU8sWUFBWSxHQUFHLENBQUM7UUFBQztRQUFBekUsYUFBQSxHQUFBSSxDQUFBO1FBQzVDSSxNQUFNLENBQUNrQyxJQUFJLENBQUMsR0FBR3lCLEtBQUssQ0FBQztNQUN2QixDQUFDLE1BQU07UUFBQTtRQUFBbkUsYUFBQSxHQUFBUyxDQUFBO1FBQUFULGFBQUEsR0FBQUksQ0FBQTtRQUNMNEQsT0FBTyxDQUFDdEIsSUFBSSxDQUFDLEdBQUd3QixHQUFHLE9BQU9ELFVBQVUsRUFBRSxFQUFFLENBQUM7UUFBQztRQUFBakUsYUFBQSxHQUFBSSxDQUFBO1FBQzFDSSxNQUFNLENBQUNrQyxJQUFJLENBQUN5QixLQUFLLENBQUM7TUFDcEI7SUFDRixDQUFDO0lBQUE7SUFBQTtNQUFBbkUsYUFBQSxHQUFBUyxDQUFBO0lBQUE7RUFDSDtFQUFDO0VBQUFULGFBQUEsR0FBQUksQ0FBQTtFQUVELE9BQU87SUFDTHdFLE1BQU0sRUFBRVosT0FBTyxDQUFDdkMsTUFBTSxHQUFHLENBQUM7SUFBQTtJQUFBLENBQUF6QixhQUFBLEdBQUFTLENBQUEsV0FBRyxTQUFTdUQsT0FBTyxDQUFDVyxJQUFJLENBQUMsT0FBTyxDQUFDLEVBQUU7SUFBQTtJQUFBLENBQUEzRSxhQUFBLEdBQUFTLENBQUEsV0FBRyxFQUFFO0lBQ2xFRCxNQUFNO0lBQ05xRSxTQUFTLEVBQUVaO0VBQ2IsQ0FBQztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBLE9BQU8sU0FBU2EsZ0JBQWdCQSxDQUFDQyxVQUFrQixFQUFVO0VBQUE7RUFBQS9FLGFBQUEsR0FBQVcsQ0FBQTtFQUFBWCxhQUFBLEdBQUFJLENBQUE7RUFDM0QsT0FBTyxJQUFJMkUsVUFBVSxDQUFDQyxPQUFPLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxHQUFHO0FBQzlDIiwiaWdub3JlTGlzdCI6W119