{"version": 3, "names": ["cov_1j8gdkw6xy", "actualCoverage", "cache", "cookies", "validate<PERSON><PERSON><PERSON><PERSON><PERSON>", "publicConfig", "verifySession", "s", "f", "cookieStore", "userPoolClientId", "aws", "possibleTokenCookies", "authToken", "cookieName", "cookie", "get", "value", "b", "console", "log", "allCookies", "getAll", "name", "includes", "payload", "email", "isAuth", "userId", "sub", "roles", "error", "getUser", "session", "id", "hasRole", "requiredRole"], "sources": ["dal.ts"], "sourcesContent": ["import 'server-only'\r\nimport { cache } from 'react'\r\nimport { cookies } from 'next/headers'\r\nimport { validateAuthCookie, CognitoJwtPayload } from './jwt-validator'\r\nimport { publicConfig } from './config'\r\n\r\nexport const verifySession = cache(async (): Promise<{\r\n  isAuth: boolean;\r\n  userId: string;\r\n  email: string;\r\n  roles: string[];\r\n} | null> => {\r\n  try {\r\n    const cookieStore = await cookies()\r\n    const { userPoolClientId } = publicConfig.aws;\r\n\r\n    // Try to get authentication tokens from cookies\r\n    const possibleTokenCookies = [\r\n      'idToken',\r\n      `CognitoIdentityServiceProvider.${userPoolClientId}.LastAuthUser`,\r\n      'amplify-signin-with-hostedUI'\r\n    ];\r\n\r\n    let authToken: string | null = null;\r\n\r\n    // Check for various Amplify cookie patterns\r\n    for (const cookieName of possibleTokenCookies) {\r\n      const cookie = cookieStore.get(cookieName);\r\n      if (cookie?.value) {\r\n        authToken = cookie.value;\r\n        console.log(`Found auth token in cookie: ${cookieName}`);\r\n        break;\r\n      }\r\n    }\r\n\r\n    // Also check for Cognito-specific cookies\r\n    if (!authToken) {\r\n      const allCookies = cookieStore.getAll();\r\n\r\n      for (const cookie of allCookies) {\r\n        if (cookie.name.includes('CognitoIdentityServiceProvider') && cookie.name.includes('idToken')) {\r\n          authToken = cookie.value;\r\n          console.log(`Found Cognito idToken in cookie: ${cookie.name}`);\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    if (!authToken) {\r\n      console.log('No authentication token found in cookies');\r\n      return null;\r\n    }\r\n\r\n    // Use secure JWT validation\r\n    const payload = await validateAuthCookie(authToken);\r\n    if (!payload) {\r\n      console.log('JWT validation failed');\r\n      return null;\r\n    }\r\n\r\n    console.log('Successfully verified session for user:', payload.email);\r\n\r\n    return {\r\n      isAuth: true,\r\n      userId: payload.sub,\r\n      email: payload.email,\r\n      roles: payload['cognito:groups'] || []\r\n    }\r\n  } catch (error) {\r\n    console.error('Auth verification error:', error)\r\n    return null\r\n  }\r\n})\r\n\r\nexport const getUser = cache(async () => {\r\n  const session = await verifySession()\r\n  if (!session) return null\r\n  \r\n  // Get user data from session\r\n  // Add your user fetching logic here\r\n  return { id: session.userId }\r\n})\r\n\r\n// Role-based authorization helper\r\nexport const hasRole = (session: any, requiredRole: string) => {\r\n  if (!session || !session.roles) return false\r\n  return session.roles.includes(requiredRole)\r\n}\r\n\r\n\r\n\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ,OAAO,aAAa;AACpB,SAASE,KAAK,QAAQ,OAAO;AAC7B,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,kBAAkB,QAA2B,iBAAiB;AACvE,SAASC,YAAY,QAAQ,UAAU;AAEvC,OAAO,MAAMC,aAAa;AAAA;AAAA,CAAAN,cAAA,GAAAO,CAAA,OAAGL,KAAK,CAAC,YAKtB;EAAA;EAAAF,cAAA,GAAAQ,CAAA;EAAAR,cAAA,GAAAO,CAAA;EACX,IAAI;IACF,MAAME,WAAW;IAAA;IAAA,CAAAT,cAAA,GAAAO,CAAA,OAAG,MAAMJ,OAAO,CAAC,CAAC;IACnC,MAAM;MAAEO;IAAiB,CAAC;IAAA;IAAA,CAAAV,cAAA,GAAAO,CAAA,OAAGF,YAAY,CAACM,GAAG;;IAE7C;IACA,MAAMC,oBAAoB;IAAA;IAAA,CAAAZ,cAAA,GAAAO,CAAA,OAAG,CAC3B,SAAS,EACT,kCAAkCG,gBAAgB,eAAe,EACjE,8BAA8B,CAC/B;IAED,IAAIG,SAAwB;IAAA;IAAA,CAAAb,cAAA,GAAAO,CAAA,OAAG,IAAI;;IAEnC;IAAA;IAAAP,cAAA,GAAAO,CAAA;IACA,KAAK,MAAMO,UAAU,IAAIF,oBAAoB,EAAE;MAC7C,MAAMG,MAAM;MAAA;MAAA,CAAAf,cAAA,GAAAO,CAAA,OAAGE,WAAW,CAACO,GAAG,CAACF,UAAU,CAAC;MAAC;MAAAd,cAAA,GAAAO,CAAA;MAC3C,IAAIQ,MAAM,EAAEE,KAAK,EAAE;QAAA;QAAAjB,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAO,CAAA;QACjBM,SAAS,GAAGE,MAAM,CAACE,KAAK;QAAC;QAAAjB,cAAA,GAAAO,CAAA;QACzBY,OAAO,CAACC,GAAG,CAAC,+BAA+BN,UAAU,EAAE,CAAC;QAAC;QAAAd,cAAA,GAAAO,CAAA;QACzD;MACF,CAAC;MAAA;MAAA;QAAAP,cAAA,GAAAkB,CAAA;MAAA;IACH;;IAEA;IAAA;IAAAlB,cAAA,GAAAO,CAAA;IACA,IAAI,CAACM,SAAS,EAAE;MAAA;MAAAb,cAAA,GAAAkB,CAAA;MACd,MAAMG,UAAU;MAAA;MAAA,CAAArB,cAAA,GAAAO,CAAA,QAAGE,WAAW,CAACa,MAAM,CAAC,CAAC;MAAC;MAAAtB,cAAA,GAAAO,CAAA;MAExC,KAAK,MAAMQ,MAAM,IAAIM,UAAU,EAAE;QAAA;QAAArB,cAAA,GAAAO,CAAA;QAC/B;QAAI;QAAA,CAAAP,cAAA,GAAAkB,CAAA,UAAAH,MAAM,CAACQ,IAAI,CAACC,QAAQ,CAAC,gCAAgC,CAAC;QAAA;QAAA,CAAAxB,cAAA,GAAAkB,CAAA,UAAIH,MAAM,CAACQ,IAAI,CAACC,QAAQ,CAAC,SAAS,CAAC,GAAE;UAAA;UAAAxB,cAAA,GAAAkB,CAAA;UAAAlB,cAAA,GAAAO,CAAA;UAC7FM,SAAS,GAAGE,MAAM,CAACE,KAAK;UAAC;UAAAjB,cAAA,GAAAO,CAAA;UACzBY,OAAO,CAACC,GAAG,CAAC,oCAAoCL,MAAM,CAACQ,IAAI,EAAE,CAAC;UAAC;UAAAvB,cAAA,GAAAO,CAAA;UAC/D;QACF,CAAC;QAAA;QAAA;UAAAP,cAAA,GAAAkB,CAAA;QAAA;MACH;IACF,CAAC;IAAA;IAAA;MAAAlB,cAAA,GAAAkB,CAAA;IAAA;IAAAlB,cAAA,GAAAO,CAAA;IAED,IAAI,CAACM,SAAS,EAAE;MAAA;MAAAb,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAO,CAAA;MACdY,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MAAC;MAAApB,cAAA,GAAAO,CAAA;MACxD,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAAP,cAAA,GAAAkB,CAAA;IAAA;;IAED;IACA,MAAMO,OAAO;IAAA;IAAA,CAAAzB,cAAA,GAAAO,CAAA,QAAG,MAAMH,kBAAkB,CAACS,SAAS,CAAC;IAAC;IAAAb,cAAA,GAAAO,CAAA;IACpD,IAAI,CAACkB,OAAO,EAAE;MAAA;MAAAzB,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAO,CAAA;MACZY,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MAAC;MAAApB,cAAA,GAAAO,CAAA;MACrC,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAAP,cAAA,GAAAkB,CAAA;IAAA;IAAAlB,cAAA,GAAAO,CAAA;IAEDY,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEK,OAAO,CAACC,KAAK,CAAC;IAAC;IAAA1B,cAAA,GAAAO,CAAA;IAEtE,OAAO;MACLoB,MAAM,EAAE,IAAI;MACZC,MAAM,EAAEH,OAAO,CAACI,GAAG;MACnBH,KAAK,EAAED,OAAO,CAACC,KAAK;MACpBI,KAAK;MAAE;MAAA,CAAA9B,cAAA,GAAAkB,CAAA,UAAAO,OAAO,CAAC,gBAAgB,CAAC;MAAA;MAAA,CAAAzB,cAAA,GAAAkB,CAAA,UAAI,EAAE;IACxC,CAAC;EACH,CAAC,CAAC,OAAOa,KAAK,EAAE;IAAA;IAAA/B,cAAA,GAAAO,CAAA;IACdY,OAAO,CAACY,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAAA;IAAA/B,cAAA,GAAAO,CAAA;IAChD,OAAO,IAAI;EACb;AACF,CAAC,CAAC;AAEF,OAAO,MAAMyB,OAAO;AAAA;AAAA,CAAAhC,cAAA,GAAAO,CAAA,QAAGL,KAAK,CAAC,YAAY;EAAA;EAAAF,cAAA,GAAAQ,CAAA;EACvC,MAAMyB,OAAO;EAAA;EAAA,CAAAjC,cAAA,GAAAO,CAAA,QAAG,MAAMD,aAAa,CAAC,CAAC;EAAA;EAAAN,cAAA,GAAAO,CAAA;EACrC,IAAI,CAAC0B,OAAO,EAAE;IAAA;IAAAjC,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAO,CAAA;IAAA,OAAO,IAAI;EAAD,CAAC;EAAA;EAAA;IAAAP,cAAA,GAAAkB,CAAA;EAAA;;EAEzB;EACA;EAAAlB,cAAA,GAAAO,CAAA;EACA,OAAO;IAAE2B,EAAE,EAAED,OAAO,CAACL;EAAO,CAAC;AAC/B,CAAC,CAAC;;AAEF;AAAA;AAAA5B,cAAA,GAAAO,CAAA;AACA,OAAO,MAAM4B,OAAO,GAAGA,CAACF,OAAY,EAAEG,YAAoB,KAAK;EAAA;EAAApC,cAAA,GAAAQ,CAAA;EAAAR,cAAA,GAAAO,CAAA;EAC7D;EAAI;EAAA,CAAAP,cAAA,GAAAkB,CAAA,WAACe,OAAO;EAAA;EAAA,CAAAjC,cAAA,GAAAkB,CAAA,UAAI,CAACe,OAAO,CAACH,KAAK,GAAE;IAAA;IAAA9B,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAO,CAAA;IAAA,OAAO,KAAK;EAAD,CAAC;EAAA;EAAA;IAAAP,cAAA,GAAAkB,CAAA;EAAA;EAAAlB,cAAA,GAAAO,CAAA;EAC5C,OAAO0B,OAAO,CAACH,KAAK,CAACN,QAAQ,CAACY,YAAY,CAAC;AAC7C,CAAC", "ignoreList": []}