{"version": 3, "names": ["_jsxFileName", "React", "__jsx", "createElement", "cov_1zmgs245lc", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "s", "f", "b", "_coverageSchema", "coverage", "actualCoverage", "useEffect", "useRouter", "signOut", "SignOutPage", "router", "handleSignOut", "push", "error", "console", "className", "__self", "__source", "fileName", "lineNumber", "columnNumber"], "sources": ["page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useEffect } from 'react'\r\nimport { useRouter } from 'next/navigation'\r\nimport { signOut } from '@/lib/auth'\r\n\r\nexport default function SignOutPage() {\r\n  const router = useRouter()\r\n\r\n  useEffect(() => {\r\n    async function handleSignOut() {\r\n      try {\r\n        await signOut()\r\n        router.push('/login')\r\n      } catch (error) {\r\n        console.error('Sign out error:', error)\r\n        // Still redirect to login\r\n        router.push('/login')\r\n      }\r\n    }\r\n\r\n    handleSignOut()\r\n  }, [router])\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-center min-h-screen\">\r\n      <div className=\"text-center\">\r\n        <h1 className=\"text-2xl font-bold mb-4\">Signing Out</h1>\r\n        <p className=\"mb-4\">Please wait while we sign you out...</p>\r\n        <div className=\"w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin mx-auto\"></div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "mappings": "AAAA,YAAY;;AAAA;AAAA,IAAAA,YAAA;AAAA,OAAAC,KAAA;AAAA,IAAAC,KAAA,GAAAD,KAAA,CAAAE,aAAA;AAAA,SAAAC,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;IAAAC,eAAA;IAAAlB,IAAA;EAAA;EAAA,IAAAmB,QAAA,GAAAlB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAgB,QAAA,CAAApB,IAAA,KAAAoB,QAAA,CAAApB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAmB,QAAA,CAAApB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAgB,cAAA,GAAAD,QAAA,CAAApB,IAAA;EAAA;IAeA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAsB,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAtB,cAAA;AAbZ,SAASuB,SAAS,QAAQ,OAAO;AACjC,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,OAAO,QAAQ,YAAY;AAEpC,eAAe,SAASC,WAAWA,CAAA,EAAG;EAAA;EAAA1B,cAAA,GAAAkB,CAAA;EACpC,MAAMS,MAAM;EAAA;EAAA,CAAA3B,cAAA,GAAAiB,CAAA,OAAGO,SAAS,CAAC,CAAC;EAAA;EAAAxB,cAAA,GAAAiB,CAAA;EAE1BM,SAAS,CAAC,MAAM;IAAA;IAAAvB,cAAA,GAAAkB,CAAA;IACd,eAAeU,aAAaA,CAAA,EAAG;MAAA;MAAA5B,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAiB,CAAA;MAC7B,IAAI;QAAA;QAAAjB,cAAA,GAAAiB,CAAA;QACF,MAAMQ,OAAO,CAAC,CAAC;QAAA;QAAAzB,cAAA,GAAAiB,CAAA;QACfU,MAAM,CAACE,IAAI,CAAC,QAAQ,CAAC;MACvB,CAAC,CAAC,OAAOC,KAAK,EAAE;QAAA;QAAA9B,cAAA,GAAAiB,CAAA;QACdc,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;QACvC;QAAA;QAAA9B,cAAA,GAAAiB,CAAA;QACAU,MAAM,CAACE,IAAI,CAAC,QAAQ,CAAC;MACvB;IACF;IAAC;IAAA7B,cAAA,GAAAiB,CAAA;IAEDW,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACD,MAAM,CAAC,CAAC;EAAA;EAAA3B,cAAA,GAAAiB,CAAA;EAEZ,OACE,0BAAAnB,KAAA;EAAA;EAAA;EAAA;EAAA;IAAKkC,SAAS,EAAC,+CAA+C;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAvC,YAAA;MAAAwC,UAAA;MAAAC,YAAA;IAAA;EAAA;EAC5D;EAAAvC,KAAA;EAAA;EAAA;EAAA;EAAA;IAAKkC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAvC,YAAA;MAAAwC,UAAA;MAAAC,YAAA;IAAA;EAAA;EAC1B;EAAAvC,KAAA;EAAA;EAAA;EAAA;EAAA;IAAIkC,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAvC,YAAA;MAAAwC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,aAAe,CAAC;EACxD;EAAAvC,KAAA;EAAA;EAAA;EAAA;EAAA;IAAGkC,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAvC,YAAA;MAAAwC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sCAAuC,CAAC;EAC5D;EAAAvC,KAAA;EAAA;EAAA;EAAA;EAAA;IAAKkC,SAAS,EAAC,qFAAqF;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAvC,YAAA;MAAAwC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAM,CACvG,CACF,CAAC;AAEV", "ignoreList": []}