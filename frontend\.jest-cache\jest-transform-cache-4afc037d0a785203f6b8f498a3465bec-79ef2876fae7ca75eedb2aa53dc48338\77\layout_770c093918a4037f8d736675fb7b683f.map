{"version": 3, "names": ["_jsxFileName", "React", "__jsx", "createElement", "cov_2ga3zjmfwz", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "_coverageSchema", "coverage", "actualCoverage", "useEffect", "useState", "useRouter", "useAuth", "Sidebar", "DashboardLayout", "children", "isAuthenticated", "isLoading", "router", "isRedirecting", "setIsRedirecting", "console", "log", "timestamp", "Date", "toLocaleTimeString", "urlParams", "URLSearchParams", "window", "location", "search", "hasOAuthCode", "has", "push", "cleanUrl", "pathname", "history", "replaceState", "document", "title", "className", "__self", "__source", "fileName", "lineNumber", "columnNumber"], "sources": ["layout.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useEffect, useState } from 'react'\r\nimport { useRouter } from 'next/navigation'\r\nimport { useAuth } from '@/contexts/AppContext'\r\nimport Sidebar from '@/components/layout/Sidebar'\r\n\r\nexport default function DashboardLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode\r\n}) {\r\n  const { isAuthenticated, isLoading } = useAuth()\r\n  const router = useRouter()\r\n  const [isRedirecting, setIsRedirecting] = useState(false)\r\n  \r\n  useEffect(() => {\r\n    console.log('🏠 [DASHBOARD-LAYOUT] Auth state changed:', {\r\n      isLoading,\r\n      isAuthenticated,\r\n      isRedirecting,\r\n      timestamp: new Date().toLocaleTimeString()\r\n    });\r\n\r\n    // Check if this is an OAuth callback (has code parameter)\r\n    const urlParams = new URLSearchParams(window.location.search)\r\n    const hasOAuthCode = urlParams.has('code')\r\n\r\n    if (hasOAuthCode) {\r\n      console.log('🔄 [DASHBOARD-LAYOUT] OAuth callback detected, waiting for Amplify to process...')\r\n      // Give Amplify extra time to process OAuth callback\r\n      return\r\n    }\r\n\r\n    // Only redirect if authentication check is complete and user is not authenticated\r\n    if (!isLoading && !isAuthenticated && !isRedirecting) {\r\n      setIsRedirecting(true)\r\n      console.log('❌ [DASHBOARD-LAYOUT] User not authenticated, redirecting to login')\r\n      router.push('/login')\r\n    } else if (!isLoading && isAuthenticated) {\r\n      console.log('✅ [DASHBOARD-LAYOUT] User is authenticated, showing dashboard')\r\n\r\n      // Clean up URL if it has OAuth parameters\r\n      if (hasOAuthCode) {\r\n        const cleanUrl = window.location.pathname\r\n        window.history.replaceState({}, document.title, cleanUrl)\r\n      }\r\n    }\r\n  }, [isLoading, isAuthenticated, router, isRedirecting])\r\n\r\n  // Show loading state\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-screen\">\r\n        <div className=\"w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin\"></div>\r\n        <p className=\"ml-4 text-lg\">Loading your dashboard...</p>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // Don't render anything while redirecting\r\n  if (!isAuthenticated) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-screen\">\r\n        <div className=\"w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin\"></div>\r\n        <p className=\"ml-4 text-lg\">Redirecting to login...</p>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex h-screen\">\r\n      <Sidebar />\r\n      <main className=\"main-content\">\r\n        {children}\r\n      </main>\r\n    </div>\r\n  )\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAAA,YAAY;;AAAA;AAAA,IAAAA,YAAA;AAAA,OAAAC,KAAA;AAAA,IAAAC,KAAA,GAAAD,KAAA,CAAAE,aAAA;AAAA,SAAAC,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,eAAA;IAAArB,IAAA;EAAA;EAAA,IAAAsB,QAAA,GAAArB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAmB,QAAA,CAAAvB,IAAA,KAAAuB,QAAA,CAAAvB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAsB,QAAA,CAAAvB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAmB,cAAA,GAAAD,QAAA,CAAAvB,IAAA;EAAA;IAeA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAyB,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAzB,cAAA;AAbZ,SAAS0B,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,OAAOC,OAAO,MAAM,6BAA6B;AAEjD,eAAe,SAASC,eAAeA,CAAC;EACtCC;AAGF,CAAC,EAAE;EAAA;EAAAhC,cAAA,GAAAqB,CAAA;EACD,MAAM;IAAEY,eAAe;IAAEC;EAAU,CAAC;EAAA;EAAA,CAAAlC,cAAA,GAAAoB,CAAA,OAAGS,OAAO,CAAC,CAAC;EAChD,MAAMM,MAAM;EAAA;EAAA,CAAAnC,cAAA,GAAAoB,CAAA,OAAGQ,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACQ,aAAa,EAAEC,gBAAgB,CAAC;EAAA;EAAA,CAAArC,cAAA,GAAAoB,CAAA,OAAGO,QAAQ,CAAC,KAAK,CAAC;EAAA;EAAA3B,cAAA,GAAAoB,CAAA;EAEzDM,SAAS,CAAC,MAAM;IAAA;IAAA1B,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACdkB,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;MACvDL,SAAS;MACTD,eAAe;MACfG,aAAa;MACbI,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC;IAC3C,CAAC,CAAC;;IAEF;IACA,MAAMC,SAAS;IAAA;IAAA,CAAA3C,cAAA,GAAAoB,CAAA,OAAG,IAAIwB,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,MAAMC,YAAY;IAAA;IAAA,CAAAhD,cAAA,GAAAoB,CAAA,OAAGuB,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC;IAAA;IAAAjD,cAAA,GAAAoB,CAAA;IAE1C,IAAI4B,YAAY,EAAE;MAAA;MAAAhD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAChBkB,OAAO,CAACC,GAAG,CAAC,kFAAkF,CAAC;MAC/F;MAAA;MAAAvC,cAAA,GAAAoB,CAAA;MACA;IACF,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAACY,SAAS;IAAA;IAAA,CAAAlC,cAAA,GAAAsB,CAAA,UAAI,CAACW,eAAe;IAAA;IAAA,CAAAjC,cAAA,GAAAsB,CAAA,UAAI,CAACc,aAAa,GAAE;MAAA;MAAApC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACpDiB,gBAAgB,CAAC,IAAI,CAAC;MAAA;MAAArC,cAAA,GAAAoB,CAAA;MACtBkB,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC;MAAA;MAAAvC,cAAA,GAAAoB,CAAA;MAChFe,MAAM,CAACe,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC,MAAM;MAAA;MAAAlD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,WAACY,SAAS;MAAA;MAAA,CAAAlC,cAAA,GAAAsB,CAAA,UAAIW,eAAe,GAAE;QAAA;QAAAjC,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACxCkB,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;;QAE5E;QAAA;QAAAvC,cAAA,GAAAoB,CAAA;QACA,IAAI4B,YAAY,EAAE;UAAA;UAAAhD,cAAA,GAAAsB,CAAA;UAChB,MAAM6B,QAAQ;UAAA;UAAA,CAAAnD,cAAA,GAAAoB,CAAA,QAAGyB,MAAM,CAACC,QAAQ,CAACM,QAAQ;UAAA;UAAApD,cAAA,GAAAoB,CAAA;UACzCyB,MAAM,CAACQ,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,EAAEL,QAAQ,CAAC;QAC3D,CAAC;QAAA;QAAA;UAAAnD,cAAA,GAAAsB,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAAtB,cAAA,GAAAsB,CAAA;MAAA;IAAD;EACF,CAAC,EAAE,CAACY,SAAS,EAAED,eAAe,EAAEE,MAAM,EAAEC,aAAa,CAAC,CAAC;;EAEvD;EAAA;EAAApC,cAAA,GAAAoB,CAAA;EACA,IAAIc,SAAS,EAAE;IAAA;IAAAlC,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACb,OACE,0BAAAtB,KAAA;IAAA;IAAA;IAAA;IAAA;MAAK2D,SAAS,EAAC,2CAA2C;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAhE,YAAA;QAAAiE,UAAA;QAAAC,YAAA;MAAA;IAAA;IACxD;IAAAhE,KAAA;IAAA;IAAA;IAAA;IAAA;MAAK2D,SAAS,EAAC,6EAA6E;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAhE,YAAA;QAAAiE,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAM,CAAC;IACnG;IAAAhE,KAAA;IAAA;IAAA;IAAA;IAAA;MAAG2D,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAhE,YAAA;QAAAiE,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,2BAA4B,CACrD,CAAC;EAEV,CAAC;EAAA;EAAA;IAAA9D,cAAA,GAAAsB,CAAA;EAAA;;EAED;EAAAtB,cAAA,GAAAoB,CAAA;EACA,IAAI,CAACa,eAAe,EAAE;IAAA;IAAAjC,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACpB,OACE,0BAAAtB,KAAA;IAAA;IAAA;IAAA;IAAA;MAAK2D,SAAS,EAAC,2CAA2C;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAhE,YAAA;QAAAiE,UAAA;QAAAC,YAAA;MAAA;IAAA;IACxD;IAAAhE,KAAA;IAAA;IAAA;IAAA;IAAA;MAAK2D,SAAS,EAAC,6EAA6E;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAhE,YAAA;QAAAiE,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAM,CAAC;IACnG;IAAAhE,KAAA;IAAA;IAAA;IAAA;IAAA;MAAG2D,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAhE,YAAA;QAAAiE,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,yBAA0B,CACnD,CAAC;EAEV,CAAC;EAAA;EAAA;IAAA9D,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EAED,OACE,0BAAAtB,KAAA;EAAA;EAAA;EAAA;EAAA;IAAK2D,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAhE,YAAA;MAAAiE,UAAA;MAAAC,YAAA;IAAA;EAAA;EAC5B;EAAAhE,KAAA,CAACgC,OAAO;EAAA;EAAA;IAAA4B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAhE,YAAA;MAAAiE,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC;EACX;EAAAhE,KAAA;EAAA;EAAA;EAAA;EAAA;IAAM2D,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAhE,YAAA;MAAAiE,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3B9B,QACG,CACH,CAAC;AAEV", "ignoreList": []}