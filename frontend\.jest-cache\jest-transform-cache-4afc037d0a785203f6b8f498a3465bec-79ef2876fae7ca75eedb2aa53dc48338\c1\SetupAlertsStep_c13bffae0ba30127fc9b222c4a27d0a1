9a21f6f57850ab1c165f055117b78d8e
/**
 * Setup Alerts Step Component
 * 
 * Second step of the Add Renewal modal - configures renewal alerts
 */

'use client';

/* istanbul ignore next */
import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\modals\\steps\\SetupAlertsStep.tsx";
var __jsx = React.createElement;
function cov_17ep0w2s5s() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\modals\\steps\\SetupAlertsStep.tsx";
  var hash = "5e7449caabaf08d5e2b2287fd119f35c1d0dc37b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\modals\\steps\\SetupAlertsStep.tsx",
    statementMap: {
      "0": {
        start: {
          line: 20,
          column: 56
        },
        end: {
          line: 214,
          column: 1
        }
      },
      "1": {
        start: {
          line: 28,
          column: 28
        },
        end: {
          line: 35,
          column: 22
        }
      },
      "2": {
        start: {
          line: 29,
          column: 26
        },
        end: {
          line: 29,
          column: 35
        }
      },
      "3": {
        start: {
          line: 30,
          column: 4
        },
        end: {
          line: 33,
          column: 5
        }
      },
      "4": {
        start: {
          line: 34,
          column: 4
        },
        end: {
          line: 34,
          column: 27
        }
      },
      "5": {
        start: {
          line: 38,
          column: 38
        },
        end: {
          line: 41,
          column: 25
        }
      },
      "6": {
        start: {
          line: 39,
          column: 19
        },
        end: {
          line: 39,
          column: 92
        }
      },
      "7": {
        start: {
          line: 39,
          column: 56
        },
        end: {
          line: 39,
          column: 68
        }
      },
      "8": {
        start: {
          line: 39,
          column: 86
        },
        end: {
          line: 39,
          column: 91
        }
      },
      "9": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 40,
          column: 55
        }
      },
      "10": {
        start: {
          line: 44,
          column: 25
        },
        end: {
          line: 52,
          column: 22
        }
      },
      "11": {
        start: {
          line: 45,
          column: 36
        },
        end: {
          line: 50,
          column: 5
        }
      },
      "12": {
        start: {
          line: 51,
          column: 4
        },
        end: {
          line: 51,
          column: 33
        }
      },
      "13": {
        start: {
          line: 55,
          column: 28
        },
        end: {
          line: 60,
          column: 22
        }
      },
      "14": {
        start: {
          line: 56,
          column: 4
        },
        end: {
          line: 59,
          column: 5
        }
      },
      "15": {
        start: {
          line: 57,
          column: 28
        },
        end: {
          line: 57,
          column: 62
        }
      },
      "16": {
        start: {
          line: 57,
          column: 50
        },
        end: {
          line: 57,
          column: 61
        }
      },
      "17": {
        start: {
          line: 58,
          column: 6
        },
        end: {
          line: 58,
          column: 29
        }
      },
      "18": {
        start: {
          line: 62,
          column: 2
        },
        end: {
          line: 213,
          column: 3
        }
      },
      "19": {
        start: {
          line: 65,
          column: 8
        },
        end: {
          line: 162,
          column: 14
        }
      },
      "20": {
        start: {
          line: 83,
          column: 31
        },
        end: {
          line: 83,
          column: 55
        }
      },
      "21": {
        start: {
          line: 103,
          column: 31
        },
        end: {
          line: 103,
          column: 108
        }
      },
      "22": {
        start: {
          line: 121,
          column: 31
        },
        end: {
          line: 121,
          column: 81
        }
      },
      "23": {
        start: {
          line: 139,
          column: 31
        },
        end: {
          line: 139,
          column: 88
        }
      },
      "24": {
        start: {
          line: 150,
          column: 33
        },
        end: {
          line: 150,
          column: 86
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 20,
            column: 56
          },
          end: {
            line: 20,
            column: 57
          }
        },
        loc: {
          start: {
            line: 26,
            column: 6
          },
          end: {
            line: 214,
            column: 1
          }
        },
        line: 26
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 28,
            column: 40
          },
          end: {
            line: 28,
            column: 41
          }
        },
        loc: {
          start: {
            line: 28,
            column: 99
          },
          end: {
            line: 35,
            column: 3
          }
        },
        line: 28
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 38,
            column: 50
          },
          end: {
            line: 38,
            column: 51
          }
        },
        loc: {
          start: {
            line: 38,
            column: 91
          },
          end: {
            line: 41,
            column: 3
          }
        },
        line: 38
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 39,
            column: 47
          },
          end: {
            line: 39,
            column: 48
          }
        },
        loc: {
          start: {
            line: 39,
            column: 56
          },
          end: {
            line: 39,
            column: 68
          }
        },
        line: 39
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 39,
            column: 77
          },
          end: {
            line: 39,
            column: 78
          }
        },
        loc: {
          start: {
            line: 39,
            column: 86
          },
          end: {
            line: 39,
            column: 91
          }
        },
        line: 39
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 44,
            column: 37
          },
          end: {
            line: 44,
            column: 38
          }
        },
        loc: {
          start: {
            line: 44,
            column: 43
          },
          end: {
            line: 52,
            column: 3
          }
        },
        line: 44
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 55,
            column: 40
          },
          end: {
            line: 55,
            column: 41
          }
        },
        loc: {
          start: {
            line: 55,
            column: 59
          },
          end: {
            line: 60,
            column: 3
          }
        },
        line: 55
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 57,
            column: 40
          },
          end: {
            line: 57,
            column: 41
          }
        },
        loc: {
          start: {
            line: 57,
            column: 50
          },
          end: {
            line: 57,
            column: 61
          }
        },
        line: 57
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 64,
            column: 16
          },
          end: {
            line: 64,
            column: 17
          }
        },
        loc: {
          start: {
            line: 65,
            column: 8
          },
          end: {
            line: 162,
            column: 14
          }
        },
        line: 65
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 83,
            column: 25
          },
          end: {
            line: 83,
            column: 26
          }
        },
        loc: {
          start: {
            line: 83,
            column: 31
          },
          end: {
            line: 83,
            column: 55
          }
        },
        line: 83
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 103,
            column: 24
          },
          end: {
            line: 103,
            column: 25
          }
        },
        loc: {
          start: {
            line: 103,
            column: 31
          },
          end: {
            line: 103,
            column: 108
          }
        },
        line: 103
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 121,
            column: 24
          },
          end: {
            line: 121,
            column: 25
          }
        },
        loc: {
          start: {
            line: 121,
            column: 31
          },
          end: {
            line: 121,
            column: 81
          }
        },
        line: 121
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 139,
            column: 24
          },
          end: {
            line: 139,
            column: 25
          }
        },
        loc: {
          start: {
            line: 139,
            column: 31
          },
          end: {
            line: 139,
            column: 88
          }
        },
        line: 139
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 150,
            column: 26
          },
          end: {
            line: 150,
            column: 27
          }
        },
        loc: {
          start: {
            line: 150,
            column: 33
          },
          end: {
            line: 150,
            column: 86
          }
        },
        line: 150
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 56,
            column: 4
          },
          end: {
            line: 59,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 56,
            column: 4
          },
          end: {
            line: 59,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 56
      },
      "1": {
        loc: {
          start: {
            line: 79,
            column: 13
          },
          end: {
            line: 88,
            column: 13
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 79,
            column: 13
          },
          end: {
            line: 79,
            column: 28
          }
        }, {
          start: {
            line: 80,
            column: 14
          },
          end: {
            line: 87,
            column: 23
          }
        }],
        line: 79
      },
      "2": {
        loc: {
          start: {
            line: 103,
            column: 77
          },
          end: {
            line: 103,
            column: 107
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 103,
            column: 77
          },
          end: {
            line: 103,
            column: 101
          }
        }, {
          start: {
            line: 103,
            column: 105
          },
          end: {
            line: 103,
            column: 107
          }
        }],
        line: 103
      },
      "3": {
        loc: {
          start: {
            line: 166,
            column: 7
          },
          end: {
            line: 184,
            column: 7
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 166,
            column: 7
          },
          end: {
            line: 166,
            column: 22
          }
        }, {
          start: {
            line: 167,
            column: 8
          },
          end: {
            line: 183,
            column: 14
          }
        }],
        line: 166
      },
      "4": {
        loc: {
          start: {
            line: 202,
            column: 11
          },
          end: {
            line: 209,
            column: 11
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 203,
            column: 12
          },
          end: {
            line: 206,
            column: 15
          }
        }, {
          start: {
            line: 208,
            column: 12
          },
          end: {
            line: 208,
            column: 27
          }
        }],
        line: 202
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5e7449caabaf08d5e2b2287fd119f35c1d0dc37b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_17ep0w2s5s = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_17ep0w2s5s();
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
import React, { useCallback } from 'react';
/* istanbul ignore next */
cov_17ep0w2s5s().s[0]++;
const SetupAlertsStep = ({
  data,
  onChange,
  onBack,
  onSubmit,
  isSubmitting
}) => {
  /* istanbul ignore next */
  cov_17ep0w2s5s().f[0]++;
  // Handle alert field changes
  const handleAlertChange =
  /* istanbul ignore next */
  (cov_17ep0w2s5s().s[1]++, useCallback((index, field, value) => {
    /* istanbul ignore next */
    cov_17ep0w2s5s().f[1]++;
    const updatedAlerts =
    /* istanbul ignore next */
    (cov_17ep0w2s5s().s[2]++, [...data]);
    /* istanbul ignore next */
    cov_17ep0w2s5s().s[3]++;
    updatedAlerts[index] =
    /* istanbul ignore next */
    _objectSpread(_objectSpread({}, updatedAlerts[index]), {}, {
      [field]: value
    });
    /* istanbul ignore next */
    cov_17ep0w2s5s().s[4]++;
    onChange(updatedAlerts);
  }, [data, onChange]));

  // Handle email recipients change
  const handleEmailRecipientsChange =
  /* istanbul ignore next */
  (cov_17ep0w2s5s().s[5]++, useCallback((index, emailsString) => {
    /* istanbul ignore next */
    cov_17ep0w2s5s().f[2]++;
    const emails =
    /* istanbul ignore next */
    (cov_17ep0w2s5s().s[6]++, emailsString.split(',').map(email => {
      /* istanbul ignore next */
      cov_17ep0w2s5s().f[3]++;
      cov_17ep0w2s5s().s[7]++;
      return email.trim();
    }).filter(email => {
      /* istanbul ignore next */
      cov_17ep0w2s5s().f[4]++;
      cov_17ep0w2s5s().s[8]++;
      return email;
    }));
    /* istanbul ignore next */
    cov_17ep0w2s5s().s[9]++;
    handleAlertChange(index, 'emailRecipients', emails);
  }, [handleAlertChange]));

  // Add new alert
  const handleAddAlert =
  /* istanbul ignore next */
  (cov_17ep0w2s5s().s[10]++, useCallback(() => {
    /* istanbul ignore next */
    cov_17ep0w2s5s().f[5]++;
    const newAlert =
    /* istanbul ignore next */
    (cov_17ep0w2s5s().s[11]++, {
      daysBeforeRenewal: 30,
      emailRecipients: [],
      customMessage: '',
      enabled: true
    });
    /* istanbul ignore next */
    cov_17ep0w2s5s().s[12]++;
    onChange([...data, newAlert]);
  }, [data, onChange]));

  // Remove alert
  const handleRemoveAlert =
  /* istanbul ignore next */
  (cov_17ep0w2s5s().s[13]++, useCallback(index => {
    /* istanbul ignore next */
    cov_17ep0w2s5s().f[6]++;
    cov_17ep0w2s5s().s[14]++;
    if (data.length > 1) {
      /* istanbul ignore next */
      cov_17ep0w2s5s().b[0][0]++;
      const updatedAlerts =
      /* istanbul ignore next */
      (cov_17ep0w2s5s().s[15]++, data.filter((_, i) => {
        /* istanbul ignore next */
        cov_17ep0w2s5s().f[7]++;
        cov_17ep0w2s5s().s[16]++;
        return i !== index;
      }));
      /* istanbul ignore next */
      cov_17ep0w2s5s().s[17]++;
      onChange(updatedAlerts);
    } else
    /* istanbul ignore next */
    {
      cov_17ep0w2s5s().b[0][1]++;
    }
  }, [data, onChange]));
  /* istanbul ignore next */
  cov_17ep0w2s5s().s[18]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "setup-alerts-step",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 63,
      columnNumber: 5
    }
  }, data.map((alert, index) => {
    /* istanbul ignore next */
    cov_17ep0w2s5s().f[8]++;
    cov_17ep0w2s5s().s[19]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      key: index,
      className: "alert-config",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 65,
        columnNumber: 9
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "alert-header",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 67,
        columnNumber: 11
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "alert-icon",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 68,
        columnNumber: 13
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "svg",
    /* istanbul ignore next */
    {
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      strokeWidth: "2",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 69,
        columnNumber: 15
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "path",
    /* istanbul ignore next */
    {
      d: "M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 70,
        columnNumber: 17
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "line",
    /* istanbul ignore next */
    {
      x1: "12",
      y1: "9",
      x2: "12",
      y2: "13",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 71,
        columnNumber: 17
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "line",
    /* istanbul ignore next */
    {
      x1: "12",
      y1: "17",
      x2: "12.01",
      y2: "17",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 72,
        columnNumber: 17
      }
    }))),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "alert-title",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 75,
        columnNumber: 13
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "h3",
    /* istanbul ignore next */
    {
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 76,
        columnNumber: 15
      }
    }, "Set Up Renewal Alert"),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "p",
    /* istanbul ignore next */
    {
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 77,
        columnNumber: 15
      }
    }, "Create an alert to be notified before the renewal date for test.")),
    /* istanbul ignore next */
    (cov_17ep0w2s5s().b[1][0]++, data.length > 1) &&
    /* istanbul ignore next */
    (cov_17ep0w2s5s().b[1][1]++,
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "button",
    /* istanbul ignore next */
    {
      type: "button",
      className: "btn-remove-alert",
      onClick: () => {
        /* istanbul ignore next */
        cov_17ep0w2s5s().f[9]++;
        cov_17ep0w2s5s().s[20]++;
        return handleRemoveAlert(index);
      },
      /* istanbul ignore next */
      "aria-label": "Remove alert",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 80,
        columnNumber: 15
      }
    }, "\xD7"))),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "form-group",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 92,
        columnNumber: 11
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "label",
    /* istanbul ignore next */
    {
      htmlFor: `daysBeforeRenewal-${index}`,
      className: "form-label",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 93,
        columnNumber: 13
      }
    }, "Days Before Renewal"),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "input",
    /* istanbul ignore next */
    {
      id: `daysBeforeRenewal-${index}`,
      type: "number",
      className: "form-input",
      min: "1",
      max: "365",
      value: alert.daysBeforeRenewal,
      onChange: e => {
        /* istanbul ignore next */
        cov_17ep0w2s5s().f[10]++;
        cov_17ep0w2s5s().s[21]++;
        return handleAlertChange(index, 'daysBeforeRenewal',
        /* istanbul ignore next */
        (cov_17ep0w2s5s().b[2][0]++, parseInt(e.target.value)) ||
        /* istanbul ignore next */
        (cov_17ep0w2s5s().b[2][1]++, 30));
      },
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 96,
        columnNumber: 13
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "p",
    /* istanbul ignore next */
    {
      className: "form-help",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 105,
        columnNumber: 13
      }
    }, "How many days before the renewal date to receive alerts")),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "form-group",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 111,
        columnNumber: 11
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "label",
    /* istanbul ignore next */
    {
      htmlFor: `emailRecipients-${index}`,
      className: "form-label",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 112,
        columnNumber: 13
      }
    }, "Email Recipients"),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "textarea",
    /* istanbul ignore next */
    {
      id: `emailRecipients-${index}`,
      className: "form-textarea",
      placeholder: "Enter email addresses separated by commas",
      rows: 3,
      value: alert.emailRecipients.join(', '),
      onChange: e => {
        /* istanbul ignore next */
        cov_17ep0w2s5s().f[11]++;
        cov_17ep0w2s5s().s[22]++;
        return handleEmailRecipientsChange(index, e.target.value);
      },
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 115,
        columnNumber: 13
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "p",
    /* istanbul ignore next */
    {
      className: "form-help",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 123,
        columnNumber: 13
      }
    }, "Email addresses that should receive alerts (leave empty to use the associated emails)")),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "form-group",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 129,
        columnNumber: 11
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "label",
    /* istanbul ignore next */
    {
      htmlFor: `customMessage-${index}`,
      className: "form-label",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 130,
        columnNumber: 13
      }
    }, "Custom Message (Optional)"),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "textarea",
    /* istanbul ignore next */
    {
      id: `customMessage-${index}`,
      className: "form-textarea",
      placeholder: "Add a custom message to include in the alert",
      rows: 4,
      value: alert.customMessage,
      onChange: e => {
        /* istanbul ignore next */
        cov_17ep0w2s5s().f[12]++;
        cov_17ep0w2s5s().s[23]++;
        return handleAlertChange(index, 'customMessage', e.target.value);
      },
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 133,
        columnNumber: 13
      }
    })),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "form-group checkbox-group",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 144,
        columnNumber: 11
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "label",
    /* istanbul ignore next */
    {
      className: "checkbox-label",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 145,
        columnNumber: 13
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "input",
    /* istanbul ignore next */
    {
      type: "checkbox",
      className: "checkbox-input",
      checked: alert.enabled,
      onChange: e => {
        /* istanbul ignore next */
        cov_17ep0w2s5s().f[13]++;
        cov_17ep0w2s5s().s[24]++;
        return handleAlertChange(index, 'enabled', e.target.checked);
      },
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 146,
        columnNumber: 15
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "span",
    /* istanbul ignore next */
    {
      className: "checkbox-custom",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 152,
        columnNumber: 15
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "span",
    /* istanbul ignore next */
    {
      className: "checkbox-text",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 153,
        columnNumber: 15
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "strong",
    /* istanbul ignore next */
    {
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 154,
        columnNumber: 17
      }
    }, "Enable this alert"),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "br",
    /* istanbul ignore next */
    {
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 155,
        columnNumber: 17
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "span",
    /* istanbul ignore next */
    {
      className: "checkbox-subtext",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 156,
        columnNumber: 17
      }
    }, "Uncheck to create the alert but keep it disabled")))));
  }),
  /* istanbul ignore next */
  (cov_17ep0w2s5s().b[3][0]++, data.length < 5) &&
  /* istanbul ignore next */
  (cov_17ep0w2s5s().b[3][1]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "add-alert-section",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 167,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  {
    type: "button",
    className: "btn btn-outline add-alert-btn",
    onClick: handleAddAlert,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 168,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    width: "16",
    height: "16",
    viewBox: "0 0 24 24",
    fill: "none",
    stroke: "currentColor",
    strokeWidth: "2",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 173,
      columnNumber: 13
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "circle",
  /* istanbul ignore next */
  {
    cx: "12",
    cy: "12",
    r: "10",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 174,
      columnNumber: 15
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "line",
  /* istanbul ignore next */
  {
    x1: "12",
    y1: "8",
    x2: "12",
    y2: "16",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 175,
      columnNumber: 15
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "line",
  /* istanbul ignore next */
  {
    x1: "8",
    y1: "12",
    x2: "16",
    y2: "12",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 176,
      columnNumber: 15
    }
  })), "Add Another Alert"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "add-alert-help",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 180,
      columnNumber: 11
    }
  }, 5 - data.length, " more alerts available"))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "modal-actions",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 187,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  {
    type: "button",
    className: "btn btn-secondary",
    onClick: onBack,
    disabled: isSubmitting,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 188,
      columnNumber: 9
    }
  }, "Back to Details"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  {
    type: "button",
    className: "btn btn-primary",
    onClick: onSubmit,
    disabled: isSubmitting,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 196,
      columnNumber: 9
    }
  }, isSubmitting ?
  /* istanbul ignore next */
  (cov_17ep0w2s5s().b[4][0]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  React.Fragment,
  /* istanbul ignore next */
  null,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    className: "spinner",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 204,
      columnNumber: 15
    }
  }), "Saving...")) :
  /* istanbul ignore next */
  (cov_17ep0w2s5s().b[4][1]++, 'Save & Finish'))));
};
export default SetupAlertsStep;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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