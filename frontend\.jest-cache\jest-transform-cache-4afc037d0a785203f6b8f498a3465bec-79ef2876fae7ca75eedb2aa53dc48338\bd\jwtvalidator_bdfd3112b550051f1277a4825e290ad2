d865547c1a3d50c37c468a84eaf42634
/* istanbul ignore next */
function cov_2k8i4ud2ua() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\jwt-validator.ts";
  var hash = "9c0110e22111ef70c1f9b6dc6654181a21d065e2";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\jwt-validator.ts",
    statementMap: {
      "0": {
        start: {
          line: 30,
          column: 62
        },
        end: {
          line: 30,
          column: 66
        }
      },
      "1": {
        start: {
          line: 36,
          column: 33
        },
        end: {
          line: 36,
          column: 49
        }
      },
      "2": {
        start: {
          line: 37,
          column: 2
        },
        end: {
          line: 37,
          column: 91
        }
      },
      "3": {
        start: {
          line: 44,
          column: 2
        },
        end: {
          line: 47,
          column: 3
        }
      },
      "4": {
        start: {
          line: 45,
          column: 20
        },
        end: {
          line: 45,
          column: 32
        }
      },
      "5": {
        start: {
          line: 46,
          column: 4
        },
        end: {
          line: 46,
          column: 53
        }
      },
      "6": {
        start: {
          line: 48,
          column: 2
        },
        end: {
          line: 48,
          column: 19
        }
      },
      "7": {
        start: {
          line: 55,
          column: 2
        },
        end: {
          line: 93,
          column: 3
        }
      },
      "8": {
        start: {
          line: 56,
          column: 17
        },
        end: {
          line: 56,
          column: 26
        }
      },
      "9": {
        start: {
          line: 57,
          column: 53
        },
        end: {
          line: 57,
          column: 69
        }
      },
      "10": {
        start: {
          line: 60,
          column: 27
        },
        end: {
          line: 60,
          column: 86
        }
      },
      "11": {
        start: {
          line: 63,
          column: 24
        },
        end: {
          line: 66,
          column: 6
        }
      },
      "12": {
        start: {
          line: 69,
          column: 27
        },
        end: {
          line: 69,
          column: 55
        }
      },
      "13": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 75,
          column: 5
        }
      },
      "14": {
        start: {
          line: 73,
          column: 6
        },
        end: {
          line: 73,
          column: 68
        }
      },
      "15": {
        start: {
          line: 74,
          column: 6
        },
        end: {
          line: 74,
          column: 18
        }
      },
      "16": {
        start: {
          line: 78,
          column: 4
        },
        end: {
          line: 81,
          column: 5
        }
      },
      "17": {
        start: {
          line: 79,
          column: 6
        },
        end: {
          line: 79,
          column: 48
        }
      },
      "18": {
        start: {
          line: 80,
          column: 6
        },
        end: {
          line: 80,
          column: 18
        }
      },
      "19": {
        start: {
          line: 84,
          column: 4
        },
        end: {
          line: 87,
          column: 5
        }
      },
      "20": {
        start: {
          line: 85,
          column: 6
        },
        end: {
          line: 85,
          column: 73
        }
      },
      "21": {
        start: {
          line: 89,
          column: 4
        },
        end: {
          line: 89,
          column: 26
        }
      },
      "22": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 91,
          column: 51
        }
      },
      "23": {
        start: {
          line: 92,
          column: 4
        },
        end: {
          line: 92,
          column: 16
        }
      },
      "24": {
        start: {
          line: 100,
          column: 2
        },
        end: {
          line: 102,
          column: 3
        }
      },
      "25": {
        start: {
          line: 101,
          column: 4
        },
        end: {
          line: 101,
          column: 16
        }
      },
      "26": {
        start: {
          line: 105,
          column: 16
        },
        end: {
          line: 105,
          column: 34
        }
      },
      "27": {
        start: {
          line: 108,
          column: 2
        },
        end: {
          line: 111,
          column: 3
        }
      },
      "28": {
        start: {
          line: 109,
          column: 4
        },
        end: {
          line: 109,
          column: 49
        }
      },
      "29": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 110,
          column: 16
        }
      },
      "30": {
        start: {
          line: 113,
          column: 2
        },
        end: {
          line: 113,
          column: 33
        }
      },
      "31": {
        start: {
          line: 121,
          column: 2
        },
        end: {
          line: 123,
          column: 3
        }
      },
      "32": {
        start: {
          line: 122,
          column: 4
        },
        end: {
          line: 122,
          column: 71
        }
      },
      "33": {
        start: {
          line: 125,
          column: 2
        },
        end: {
          line: 144,
          column: 3
        }
      },
      "34": {
        start: {
          line: 126,
          column: 18
        },
        end: {
          line: 126,
          column: 34
        }
      },
      "35": {
        start: {
          line: 127,
          column: 4
        },
        end: {
          line: 129,
          column: 5
        }
      },
      "36": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 18
        }
      },
      "37": {
        start: {
          line: 131,
          column: 20
        },
        end: {
          line: 131,
          column: 74
        }
      },
      "38": {
        start: {
          line: 134,
          column: 24
        },
        end: {
          line: 134,
          column: 53
        }
      },
      "39": {
        start: {
          line: 135,
          column: 4
        },
        end: {
          line: 138,
          column: 5
        }
      },
      "40": {
        start: {
          line: 136,
          column: 6
        },
        end: {
          line: 136,
          column: 40
        }
      },
      "41": {
        start: {
          line: 137,
          column: 6
        },
        end: {
          line: 137,
          column: 18
        }
      },
      "42": {
        start: {
          line: 140,
          column: 4
        },
        end: {
          line: 140,
          column: 40
        }
      },
      "43": {
        start: {
          line: 142,
          column: 4
        },
        end: {
          line: 142,
          column: 49
        }
      },
      "44": {
        start: {
          line: 143,
          column: 4
        },
        end: {
          line: 143,
          column: 16
        }
      },
      "45": {
        start: {
          line: 151,
          column: 22
        },
        end: {
          line: 151,
          column: 51
        }
      },
      "46": {
        start: {
          line: 152,
          column: 2
        },
        end: {
          line: 152,
          column: 35
        }
      },
      "47": {
        start: {
          line: 159,
          column: 2
        },
        end: {
          line: 159,
          column: 28
        }
      },
      "48": {
        start: {
          line: 166,
          column: 25
        },
        end: {
          line: 166,
          column: 56
        }
      },
      "49": {
        start: {
          line: 167,
          column: 22
        },
        end: {
          line: 167,
          column: 56
        }
      },
      "50": {
        start: {
          line: 168,
          column: 2
        },
        end: {
          line: 168,
          column: 39
        }
      }
    },
    fnMap: {
      "0": {
        name: "getJwksUrl",
        decl: {
          start: {
            line: 35,
            column: 9
          },
          end: {
            line: 35,
            column: 19
          }
        },
        loc: {
          start: {
            line: 35,
            column: 30
          },
          end: {
            line: 38,
            column: 1
          }
        },
        line: 35
      },
      "1": {
        name: "getJwks",
        decl: {
          start: {
            line: 43,
            column: 9
          },
          end: {
            line: 43,
            column: 16
          }
        },
        loc: {
          start: {
            line: 43,
            column: 19
          },
          end: {
            line: 49,
            column: 1
          }
        },
        line: 43
      },
      "2": {
        name: "validateJwtToken",
        decl: {
          start: {
            line: 54,
            column: 22
          },
          end: {
            line: 54,
            column: 38
          }
        },
        loc: {
          start: {
            line: 54,
            column: 89
          },
          end: {
            line: 94,
            column: 1
          }
        },
        line: 54
      },
      "3": {
        name: "validateAuthCookie",
        decl: {
          start: {
            line: 99,
            column: 22
          },
          end: {
            line: 99,
            column: 40
          }
        },
        loc: {
          start: {
            line: 99,
            column: 97
          },
          end: {
            line: 114,
            column: 1
          }
        },
        line: 99
      },
      "4": {
        name: "parseJwtUnsafe",
        decl: {
          start: {
            line: 120,
            column: 16
          },
          end: {
            line: 120,
            column: 30
          }
        },
        loc: {
          start: {
            line: 120,
            column: 72
          },
          end: {
            line: 145,
            column: 1
          }
        },
        line: 120
      },
      "5": {
        name: "isTokenExpired",
        decl: {
          start: {
            line: 150,
            column: 16
          },
          end: {
            line: 150,
            column: 30
          }
        },
        loc: {
          start: {
            line: 150,
            column: 68
          },
          end: {
            line: 153,
            column: 1
          }
        },
        line: 150
      },
      "6": {
        name: "getTokenExpirationTime",
        decl: {
          start: {
            line: 158,
            column: 16
          },
          end: {
            line: 158,
            column: 38
          }
        },
        loc: {
          start: {
            line: 158,
            column: 75
          },
          end: {
            line: 160,
            column: 1
          }
        },
        line: 158
      },
      "7": {
        name: "isTokenExpiringWithin",
        decl: {
          start: {
            line: 165,
            column: 16
          },
          end: {
            line: 165,
            column: 37
          }
        },
        loc: {
          start: {
            line: 165,
            column: 92
          },
          end: {
            line: 169,
            column: 1
          }
        },
        line: 165
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 44,
            column: 2
          },
          end: {
            line: 47,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 2
          },
          end: {
            line: 47,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "1": {
        loc: {
          start: {
            line: 72,
            column: 4
          },
          end: {
            line: 75,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 4
          },
          end: {
            line: 75,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "2": {
        loc: {
          start: {
            line: 78,
            column: 4
          },
          end: {
            line: 81,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 78,
            column: 4
          },
          end: {
            line: 81,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 78
      },
      "3": {
        loc: {
          start: {
            line: 84,
            column: 4
          },
          end: {
            line: 87,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 84,
            column: 4
          },
          end: {
            line: 87,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 84
      },
      "4": {
        loc: {
          start: {
            line: 100,
            column: 2
          },
          end: {
            line: 102,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 100,
            column: 2
          },
          end: {
            line: 102,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 100
      },
      "5": {
        loc: {
          start: {
            line: 108,
            column: 2
          },
          end: {
            line: 111,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 108,
            column: 2
          },
          end: {
            line: 111,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 108
      },
      "6": {
        loc: {
          start: {
            line: 108,
            column: 6
          },
          end: {
            line: 108,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 108,
            column: 6
          },
          end: {
            line: 108,
            column: 26
          }
        }, {
          start: {
            line: 108,
            column: 30
          },
          end: {
            line: 108,
            column: 59
          }
        }],
        line: 108
      },
      "7": {
        loc: {
          start: {
            line: 121,
            column: 2
          },
          end: {
            line: 123,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 121,
            column: 2
          },
          end: {
            line: 123,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 121
      },
      "8": {
        loc: {
          start: {
            line: 127,
            column: 4
          },
          end: {
            line: 129,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 127,
            column: 4
          },
          end: {
            line: 129,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 127
      },
      "9": {
        loc: {
          start: {
            line: 135,
            column: 4
          },
          end: {
            line: 138,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 135,
            column: 4
          },
          end: {
            line: 138,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 135
      },
      "10": {
        loc: {
          start: {
            line: 135,
            column: 8
          },
          end: {
            line: 135,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 135,
            column: 8
          },
          end: {
            line: 135,
            column: 19
          }
        }, {
          start: {
            line: 135,
            column: 23
          },
          end: {
            line: 135,
            column: 48
          }
        }],
        line: 135
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "9c0110e22111ef70c1f9b6dc6654181a21d065e2"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2k8i4ud2ua = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2k8i4ud2ua();
/**
 * Secure JWT Token Validation
 * 
 * This module provides secure JWT token validation with proper signature verification
 * using AWS Cognito's JWKS (JSON Web Key Set).
 */

import { jwtVerify, createRemoteJWKSet } from 'jose';
import { publicConfig } from './config';

// Cognito JWT payload interface

// Cache for JWKS
let jwksCache =
/* istanbul ignore next */
(cov_2k8i4ud2ua().s[0]++, null);

/**
 * Get JWKS URL for the Cognito User Pool
 */
function getJwksUrl() {
  /* istanbul ignore next */
  cov_2k8i4ud2ua().f[0]++;
  const {
    region,
    userPoolId
  } =
  /* istanbul ignore next */
  (cov_2k8i4ud2ua().s[1]++, publicConfig.aws);
  /* istanbul ignore next */
  cov_2k8i4ud2ua().s[2]++;
  return `https://cognito-idp.${region}.amazonaws.com/${userPoolId}/.well-known/jwks.json`;
}

/**
 * Get or create JWKS instance
 */
function getJwks() {
  /* istanbul ignore next */
  cov_2k8i4ud2ua().f[1]++;
  cov_2k8i4ud2ua().s[3]++;
  if (!jwksCache) {
    /* istanbul ignore next */
    cov_2k8i4ud2ua().b[0][0]++;
    const jwksUrl =
    /* istanbul ignore next */
    (cov_2k8i4ud2ua().s[4]++, getJwksUrl());
    /* istanbul ignore next */
    cov_2k8i4ud2ua().s[5]++;
    jwksCache = createRemoteJWKSet(new URL(jwksUrl));
  } else
  /* istanbul ignore next */
  {
    cov_2k8i4ud2ua().b[0][1]++;
  }
  cov_2k8i4ud2ua().s[6]++;
  return jwksCache;
}

/**
 * Validate JWT token with proper signature verification
 */
export async function validateJwtToken(token) {
  /* istanbul ignore next */
  cov_2k8i4ud2ua().f[2]++;
  cov_2k8i4ud2ua().s[7]++;
  try {
    const jwks =
    /* istanbul ignore next */
    (cov_2k8i4ud2ua().s[8]++, getJwks());
    const {
      region,
      userPoolId,
      userPoolClientId
    } =
    /* istanbul ignore next */
    (cov_2k8i4ud2ua().s[9]++, publicConfig.aws);

    // Expected issuer
    const expectedIssuer =
    /* istanbul ignore next */
    (cov_2k8i4ud2ua().s[10]++, `https://cognito-idp.${region}.amazonaws.com/${userPoolId}`);

    // Verify the JWT
    const {
      payload
    } =
    /* istanbul ignore next */
    (cov_2k8i4ud2ua().s[11]++, await jwtVerify(token, jwks, {
      issuer: expectedIssuer,
      audience: userPoolClientId
    }));

    // Additional validation
    const cognitoPayload =
    /* istanbul ignore next */
    (cov_2k8i4ud2ua().s[12]++, payload);

    // Validate token type (should be 'id' for ID tokens)
    /* istanbul ignore next */
    cov_2k8i4ud2ua().s[13]++;
    if (cognitoPayload.token_use !== 'id') {
      /* istanbul ignore next */
      cov_2k8i4ud2ua().b[1][0]++;
      cov_2k8i4ud2ua().s[14]++;
      console.warn('Invalid token type:', cognitoPayload.token_use);
      /* istanbul ignore next */
      cov_2k8i4ud2ua().s[15]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_2k8i4ud2ua().b[1][1]++;
    }

    // Validate email is present and verified (if available)
    cov_2k8i4ud2ua().s[16]++;
    if (!cognitoPayload.email) {
      /* istanbul ignore next */
      cov_2k8i4ud2ua().b[2][0]++;
      cov_2k8i4ud2ua().s[17]++;
      console.warn('Token missing email claim');
      /* istanbul ignore next */
      cov_2k8i4ud2ua().s[18]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_2k8i4ud2ua().b[2][1]++;
    }

    // Check if email is verified (optional but recommended)
    cov_2k8i4ud2ua().s[19]++;
    if (cognitoPayload.email_verified === false) {
      /* istanbul ignore next */
      cov_2k8i4ud2ua().b[3][0]++;
      cov_2k8i4ud2ua().s[20]++;
      console.warn('Email not verified for user:', cognitoPayload.email);
      // You might want to reject unverified emails in production
    } else
    /* istanbul ignore next */
    {
      cov_2k8i4ud2ua().b[3][1]++;
    }
    cov_2k8i4ud2ua().s[21]++;
    return cognitoPayload;
  } catch (error) {
    /* istanbul ignore next */
    cov_2k8i4ud2ua().s[22]++;
    console.error('JWT validation failed:', error);
    /* istanbul ignore next */
    cov_2k8i4ud2ua().s[23]++;
    return null;
  }
}

/**
 * Extract and validate JWT from cookie string
 */
export async function validateAuthCookie(cookieValue) {
  /* istanbul ignore next */
  cov_2k8i4ud2ua().f[3]++;
  cov_2k8i4ud2ua().s[24]++;
  if (!cookieValue) {
    /* istanbul ignore next */
    cov_2k8i4ud2ua().b[4][0]++;
    cov_2k8i4ud2ua().s[25]++;
    return null;
  } else
  /* istanbul ignore next */
  {
    cov_2k8i4ud2ua().b[4][1]++;
  }

  // Remove any potential cookie prefix/suffix
  const token =
  /* istanbul ignore next */
  (cov_2k8i4ud2ua().s[26]++, cookieValue.trim());

  // Basic JWT format check
  /* istanbul ignore next */
  cov_2k8i4ud2ua().s[27]++;
  if (
  /* istanbul ignore next */
  (cov_2k8i4ud2ua().b[6][0]++, !token.includes('.')) ||
  /* istanbul ignore next */
  (cov_2k8i4ud2ua().b[6][1]++, token.split('.').length !== 3)) {
    /* istanbul ignore next */
    cov_2k8i4ud2ua().b[5][0]++;
    cov_2k8i4ud2ua().s[28]++;
    console.warn('Invalid JWT format in cookie');
    /* istanbul ignore next */
    cov_2k8i4ud2ua().s[29]++;
    return null;
  } else
  /* istanbul ignore next */
  {
    cov_2k8i4ud2ua().b[5][1]++;
  }
  cov_2k8i4ud2ua().s[30]++;
  return validateJwtToken(token);
}

/**
 * Fallback JWT parsing (INSECURE - only for development/debugging)
 * This should NOT be used in production
 */
export function parseJwtUnsafe(token) {
  /* istanbul ignore next */
  cov_2k8i4ud2ua().f[4]++;
  cov_2k8i4ud2ua().s[31]++;
  if (publicConfig.app.isProduction) {
    /* istanbul ignore next */
    cov_2k8i4ud2ua().b[7][0]++;
    cov_2k8i4ud2ua().s[32]++;
    throw new Error('Unsafe JWT parsing is not allowed in production');
  } else
  /* istanbul ignore next */
  {
    cov_2k8i4ud2ua().b[7][1]++;
  }
  cov_2k8i4ud2ua().s[33]++;
  try {
    const parts =
    /* istanbul ignore next */
    (cov_2k8i4ud2ua().s[34]++, token.split('.'));
    /* istanbul ignore next */
    cov_2k8i4ud2ua().s[35]++;
    if (parts.length !== 3) {
      /* istanbul ignore next */
      cov_2k8i4ud2ua().b[8][0]++;
      cov_2k8i4ud2ua().s[36]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_2k8i4ud2ua().b[8][1]++;
    }
    const payload =
    /* istanbul ignore next */
    (cov_2k8i4ud2ua().s[37]++, JSON.parse(Buffer.from(parts[1], 'base64').toString()));

    // Check expiration
    const currentTime =
    /* istanbul ignore next */
    (cov_2k8i4ud2ua().s[38]++, Math.floor(Date.now() / 1000));
    /* istanbul ignore next */
    cov_2k8i4ud2ua().s[39]++;
    if (
    /* istanbul ignore next */
    (cov_2k8i4ud2ua().b[10][0]++, payload.exp) &&
    /* istanbul ignore next */
    (cov_2k8i4ud2ua().b[10][1]++, payload.exp < currentTime)) {
      /* istanbul ignore next */
      cov_2k8i4ud2ua().b[9][0]++;
      cov_2k8i4ud2ua().s[40]++;
      console.warn('Token has expired');
      /* istanbul ignore next */
      cov_2k8i4ud2ua().s[41]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_2k8i4ud2ua().b[9][1]++;
    }
    cov_2k8i4ud2ua().s[42]++;
    return payload;
  } catch (error) {
    /* istanbul ignore next */
    cov_2k8i4ud2ua().s[43]++;
    console.error('Failed to parse JWT:', error);
    /* istanbul ignore next */
    cov_2k8i4ud2ua().s[44]++;
    return null;
  }
}

/**
 * Validate token expiration
 */
export function isTokenExpired(payload) {
  /* istanbul ignore next */
  cov_2k8i4ud2ua().f[5]++;
  const currentTime =
  /* istanbul ignore next */
  (cov_2k8i4ud2ua().s[45]++, Math.floor(Date.now() / 1000));
  /* istanbul ignore next */
  cov_2k8i4ud2ua().s[46]++;
  return payload.exp < currentTime;
}

/**
 * Get token expiration time in milliseconds
 */
export function getTokenExpirationTime(payload) {
  /* istanbul ignore next */
  cov_2k8i4ud2ua().f[6]++;
  cov_2k8i4ud2ua().s[47]++;
  return payload.exp * 1000;
}

/**
 * Check if token expires within the specified minutes
 */
export function isTokenExpiringWithin(payload, minutes) {
  /* istanbul ignore next */
  cov_2k8i4ud2ua().f[7]++;
  const expirationTime =
  /* istanbul ignore next */
  (cov_2k8i4ud2ua().s[48]++, getTokenExpirationTime(payload));
  const warningTime =
  /* istanbul ignore next */
  (cov_2k8i4ud2ua().s[49]++, Date.now() + minutes * 60 * 1000);
  /* istanbul ignore next */
  cov_2k8i4ud2ua().s[50]++;
  return expirationTime <= warningTime;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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