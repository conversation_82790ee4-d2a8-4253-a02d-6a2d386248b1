import { Signer } from "@aws-sdk/rds-signer";
import { Pool } from 'pg';

let pool: Pool | null = null;

export async function getDbPool() {
  if (pool) return pool;
  
  // For local development, use environment variables
  if (process.env.NODE_ENV !== 'production') {
    console.log('Database config:', {
      user: process.env.DB_USER,
      host: process.env.DB_HOST || '127.0.0.1',
      database: process.env.DB_NAME,
      ssl: process.env.DATABASE_SSL === 'true'
    });

    pool = new Pool({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      host: process.env.DB_HOST || '127.0.0.1',
      port: 5432,
      database: process.env.DB_NAME,
      ssl: process.env.DATABASE_SSL === 'true' ? { rejectUnauthorized: false } : false
    });
    return pool;
  }
  
  // For production, use IAM authentication
  try {
    const dbHost = process.env.DB_HOST || 'renewtrack-prod-ca-central-1-rds.cpy6ukqgy9s3.ca-central-1.rds.amazonaws.com';
    const dbUser = process.env.DB_IAM_USER || 'renewtrack_iam_user';
    const dbName = process.env.DB_NAME || 'renewtrack';

    // Create RDS signer for IAM authentication
    const signer = new Signer({
      hostname: dbHost,
      port: 5432,
      username: dbUser,
      region: process.env.AWS_REGION || process.env.NEXT_PUBLIC_AWS_REGION || 'ca-central-1'
    });

    // Get temporary auth token
    const token = await signer.getAuthToken();

    if (!token) {
      throw new Error('Failed to obtain RDS auth token');
    }
    
    // Create connection pool with IAM token as password
    pool = new Pool({
      user: dbUser,
      password: token,
      host: dbHost,
      port: 5432,
      database: dbName,
      ssl: { rejectUnauthorized: false }
    });
    
    return pool;
  } catch (error) {
    console.error('Error establishing database connection:', error);
    throw error;
  }
}















