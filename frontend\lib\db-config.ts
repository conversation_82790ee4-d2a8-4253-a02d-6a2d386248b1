import { Signer } from "@aws-sdk/rds-signer";
import { Pool, PoolConfig, PoolClient } from 'pg';
import { getDatabaseConfig, publicConfig } from './config';

// Connection pool instance
let pool: Pool | null = null;

// Pool configuration with optimized settings
const getPoolConfig = (dbConfig: ReturnType<typeof getDatabaseConfig>): PoolConfig => {
  const baseConfig: PoolConfig = {
    user: dbConfig.user,
    password: dbConfig.password,
    host: dbConfig.host || '127.0.0.1',
    port: 5432,
    database: dbConfig.name,
    ssl: dbConfig.ssl ? { rejectUnauthorized: false } : false,

    // Connection pool optimization
    max: 20, // Maximum number of clients in the pool
    min: 2,  // Minimum number of clients in the pool
    idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
    connectionTimeoutMillis: 10000, // Return error after 10 seconds if connection could not be established
    maxUses: 7500, // Close (and replace) a connection after it has been used 7500 times

    // Query timeout
    query_timeout: 30000, // 30 seconds
    statement_timeout: 30000, // 30 seconds

    // Keep alive settings
    keepAlive: true,
    keepAliveInitialDelayMillis: 10000,
  };

  return baseConfig;
};

export async function getDbPool(): Promise<Pool> {
  if (pool) return pool;

  const dbConfig = getDatabaseConfig();

  // For local development, use environment variables
  if (!publicConfig.app.isProduction) {
    console.log('Database config:', {
      user: dbConfig.user,
      host: dbConfig.host || '127.0.0.1',
      database: dbConfig.name,
      ssl: dbConfig.ssl
    });

    const poolConfig = getPoolConfig(dbConfig);
    pool = new Pool(poolConfig);

    // Add error handling for the pool
    pool.on('error', (err) => {
      console.error('Unexpected error on idle client', err);
    });

    pool.on('connect', () => {
      console.log('New client connected to database');
    });

    pool.on('remove', () => {
      console.log('Client removed from pool');
    });

    return pool;
  }
  
  // For production, use IAM authentication
  try {
    const dbHost = dbConfig.host || dbConfig.url || 'renewtrack-prod-ca-central-1-rds.cpy6ukqgy9s3.ca-central-1.rds.amazonaws.com';
    const dbUser = process.env.DB_IAM_USER || 'renewtrack_iam_user';
    const dbName = dbConfig.name || 'renewtrack';

    // Create RDS signer for IAM authentication
    const signer = new Signer({
      hostname: dbHost,
      port: 5432,
      username: dbUser,
      region: publicConfig.aws.region
    });

    // Get temporary auth token
    const token = await signer.getAuthToken();

    if (!token) {
      throw new Error('Failed to obtain RDS auth token');
    }
    
    // Create connection pool with IAM token as password
    pool = new Pool({
      user: dbUser,
      password: token,
      host: dbHost,
      port: 5432,
      database: dbName,
      ssl: { rejectUnauthorized: false }
    });
    
    return pool;
  } catch (error) {
    console.error('Error establishing database connection:', error);
    throw error;
  }
}















