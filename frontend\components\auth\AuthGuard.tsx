/**
 * Authentication Guard Component
 * 
 * Provides proper loading states and redirects during authentication checks
 */

'use client';

import React from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useApp } from '@/contexts/AppContext';

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

export function AuthGuard({ 
  children, 
  requireAuth = true, 
  redirectTo = '/login' 
}: AuthGuardProps) {
  const { isAuthenticated, isLoading, user } = useApp();
  const router = useRouter();
  const pathname = usePathname();

  // Define protected routes
  const protectedRoutes = ['/dashboard', '/profile', '/settings', '/clients', '/renewals'];
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname === route || pathname.startsWith(`${route}/`)
  );

  // Show loading state while authentication is being determined
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-lg font-medium text-gray-900 mb-2">
            Checking Authentication...
          </h2>
          <p className="text-sm text-gray-600">
            Please wait while we verify your session.
          </p>
        </div>
      </div>
    );
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !isAuthenticated && isProtectedRoute) {
    // Show redirecting message briefly before redirect
    setTimeout(() => {
      router.push(redirectTo);
    }, 100);

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-pulse">
            <div className="text-4xl mb-4">🔐</div>
          </div>
          <h2 className="text-lg font-medium text-gray-900 mb-2">
            Redirecting to Login...
          </h2>
          <p className="text-sm text-gray-600">
            You need to be authenticated to access this page.
          </p>
        </div>
      </div>
    );
  }

  // If user is authenticated but on an auth page (login/signup), redirect to dashboard
  if (isAuthenticated && (pathname === '/login' || pathname === '/signup')) {
    setTimeout(() => {
      router.push('/dashboard');
    }, 100);

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-pulse">
            <div className="text-4xl mb-4">✅</div>
          </div>
          <h2 className="text-lg font-medium text-gray-900 mb-2">
            Already Authenticated
          </h2>
          <p className="text-sm text-gray-600">
            Redirecting to your dashboard...
          </p>
        </div>
      </div>
    );
  }

  // Render children if authentication requirements are met
  return <>{children}</>;
}

export default AuthGuard;
