{"version": 3, "names": ["cov_xn0pqfcci", "actualCoverage", "getConnectionHealth", "requireAuth", "createSuccessResponse", "createErrorResponse", "ApiErrorCode", "HttpStatus", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GET", "s", "f", "authResult", "success", "b", "response", "health", "healthy", "DATABASE_ERROR", "INTERNAL_SERVER_ERROR", "status", "database", "totalConnections", "idleConnections", "waitingCount", "connectionUtilization", "Math", "round", "timestamp", "Date", "toISOString"], "sources": ["route.ts"], "sourcesContent": ["/**\n * Database Health Check API\n * \n * This endpoint provides database connection health status and metrics\n * for monitoring and debugging purposes.\n */\n\nimport { getConnectionHealth } from '@/lib/database';\nimport { requireAuth } from '@/lib/auth-middleware';\nimport { \n  createSuccessResponse, \n  createErrorResponse, \n  ApiErrorCode, \n  HttpStatus,\n  withErrorHandling \n} from '@/lib/api-response';\n\nexport const GET = withErrorHandling(async () => {\n  // Verify authentication (only authenticated users can check health)\n  const authResult = await requireAuth();\n  if (!authResult.success) {\n    return authResult.response!;\n  }\n\n  // Get database health status\n  const health = await getConnectionHealth();\n  \n  if (!health.healthy) {\n    return createErrorResponse(\n      'Database is unhealthy',\n      ApiErrorCode.DATABASE_ERROR,\n      HttpStatus.INTERNAL_SERVER_ERROR\n    );\n  }\n\n  return createSuccessResponse({\n    status: 'healthy',\n    database: {\n      healthy: health.healthy,\n      totalConnections: health.totalConnections,\n      idleConnections: health.idleConnections,\n      waitingCount: health.waitingCount,\n      connectionUtilization: health.totalConnections > 0 \n        ? Math.round(((health.totalConnections - health.idleConnections) / health.totalConnections) * 100)\n        : 0\n    },\n    timestamp: new Date().toISOString()\n  }, 'Database health check completed successfully');\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAfZ;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASE,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SACEC,qBAAqB,EACrBC,mBAAmB,EACnBC,YAAY,EACZC,UAAU,EACVC,iBAAiB,QACZ,oBAAoB;AAE3B,OAAO,MAAMC,GAAG;AAAA;AAAA,CAAAT,aAAA,GAAAU,CAAA,OAAGF,iBAAiB,CAAC,YAAY;EAAA;EAAAR,aAAA,GAAAW,CAAA;EAC/C;EACA,MAAMC,UAAU;EAAA;EAAA,CAAAZ,aAAA,GAAAU,CAAA,OAAG,MAAMP,WAAW,CAAC,CAAC;EAAC;EAAAH,aAAA,GAAAU,CAAA;EACvC,IAAI,CAACE,UAAU,CAACC,OAAO,EAAE;IAAA;IAAAb,aAAA,GAAAc,CAAA;IAAAd,aAAA,GAAAU,CAAA;IACvB,OAAOE,UAAU,CAACG,QAAQ;EAC5B,CAAC;EAAA;EAAA;IAAAf,aAAA,GAAAc,CAAA;EAAA;;EAED;EACA,MAAME,MAAM;EAAA;EAAA,CAAAhB,aAAA,GAAAU,CAAA,OAAG,MAAMR,mBAAmB,CAAC,CAAC;EAAC;EAAAF,aAAA,GAAAU,CAAA;EAE3C,IAAI,CAACM,MAAM,CAACC,OAAO,EAAE;IAAA;IAAAjB,aAAA,GAAAc,CAAA;IAAAd,aAAA,GAAAU,CAAA;IACnB,OAAOL,mBAAmB,CACxB,uBAAuB,EACvBC,YAAY,CAACY,cAAc,EAC3BX,UAAU,CAACY,qBACb,CAAC;EACH,CAAC;EAAA;EAAA;IAAAnB,aAAA,GAAAc,CAAA;EAAA;EAAAd,aAAA,GAAAU,CAAA;EAED,OAAON,qBAAqB,CAAC;IAC3BgB,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE;MACRJ,OAAO,EAAED,MAAM,CAACC,OAAO;MACvBK,gBAAgB,EAAEN,MAAM,CAACM,gBAAgB;MACzCC,eAAe,EAAEP,MAAM,CAACO,eAAe;MACvCC,YAAY,EAAER,MAAM,CAACQ,YAAY;MACjCC,qBAAqB,EAAET,MAAM,CAACM,gBAAgB,GAAG,CAAC;MAAA;MAAA,CAAAtB,aAAA,GAAAc,CAAA,UAC9CY,IAAI,CAACC,KAAK,CAAE,CAACX,MAAM,CAACM,gBAAgB,GAAGN,MAAM,CAACO,eAAe,IAAIP,MAAM,CAACM,gBAAgB,GAAI,GAAG,CAAC;MAAA;MAAA,CAAAtB,aAAA,GAAAc,CAAA,UAChG,CAAC;IACP,CAAC;IACDc,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EACpC,CAAC,EAAE,8CAA8C,CAAC;AACpD,CAAC,CAAC", "ignoreList": []}