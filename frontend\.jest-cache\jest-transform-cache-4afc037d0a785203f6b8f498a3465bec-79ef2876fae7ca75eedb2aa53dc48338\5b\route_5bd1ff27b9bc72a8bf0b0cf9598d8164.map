{"version": 3, "names": ["cov_1t9xjs7xl8", "actualCoverage", "requireAuth", "createSuccessResponse", "createErrorResponse", "ApiErrorCode", "HttpStatus", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GET", "s", "f", "authResult", "success", "b", "response", "session", "scanResults", "lastScanDate", "results", "error", "console", "DATABASE_ERROR", "INTERNAL_SERVER_ERROR"], "sources": ["route.ts"], "sourcesContent": ["import { requireAuth } from '@/lib/auth-middleware';\nimport {\n  createSuccessResponse,\n  createErrorResponse,\n  ApiErrorCode,\n  HttpStatus,\n  withErrorHandling\n} from '@/lib/api-response';\n\n// Scan result interface\ninterface ScanResult {\n  id: string;\n  type: 'software' | 'license' | 'renewal';\n  name: string;\n  status: 'found' | 'missing' | 'expired' | 'warning';\n  lastSeen: string;\n  details?: string;\n}\n\nexport const GET = withErrorHandling(async () => {\n  // Verify authentication and get session\n  const authResult = await requireAuth();\n  if (!authResult.success) {\n    return authResult.response!;\n  }\n\n  const session = authResult.session!;\n\n  try {\n    // TODO: Replace with actual database query\n    // For now, return empty results as placeholder\n    const scanResults: ScanResult[] = [];\n    const lastScanDate = null;\n\n    return createSuccessResponse({\n      results: scanResults,\n      lastScanDate: lastScanDate\n    }, 'Scan results retrieved successfully');\n\n  } catch (error) {\n    console.error('Error fetching scan results:', error);\n    return createErrorResponse(\n      'Failed to fetch scan results',\n      ApiErrorCode.DATABASE_ERROR,\n      HttpStatus.INTERNAL_SERVER_ERROR\n    );\n  }\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ,SAASE,WAAW,QAAQ,uBAAuB;AACnD,SACEC,qBAAqB,EACrBC,mBAAmB,EACnBC,YAAY,EACZC,UAAU,EACVC,iBAAiB,QACZ,oBAAoB;;AAE3B;;AAUA,OAAO,MAAMC,GAAG;AAAA;AAAA,CAAAR,cAAA,GAAAS,CAAA,OAAGF,iBAAiB,CAAC,YAAY;EAAA;EAAAP,cAAA,GAAAU,CAAA;EAC/C;EACA,MAAMC,UAAU;EAAA;EAAA,CAAAX,cAAA,GAAAS,CAAA,OAAG,MAAMP,WAAW,CAAC,CAAC;EAAC;EAAAF,cAAA,GAAAS,CAAA;EACvC,IAAI,CAACE,UAAU,CAACC,OAAO,EAAE;IAAA;IAAAZ,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAS,CAAA;IACvB,OAAOE,UAAU,CAACG,QAAQ;EAC5B,CAAC;EAAA;EAAA;IAAAd,cAAA,GAAAa,CAAA;EAAA;EAED,MAAME,OAAO;EAAA;EAAA,CAAAf,cAAA,GAAAS,CAAA,OAAGE,UAAU,CAACI,OAAO,CAAC;EAAC;EAAAf,cAAA,GAAAS,CAAA;EAEpC,IAAI;IACF;IACA;IACA,MAAMO,WAAyB;IAAA;IAAA,CAAAhB,cAAA,GAAAS,CAAA,OAAG,EAAE;IACpC,MAAMQ,YAAY;IAAA;IAAA,CAAAjB,cAAA,GAAAS,CAAA,OAAG,IAAI;IAAC;IAAAT,cAAA,GAAAS,CAAA;IAE1B,OAAON,qBAAqB,CAAC;MAC3Be,OAAO,EAAEF,WAAW;MACpBC,YAAY,EAAEA;IAChB,CAAC,EAAE,qCAAqC,CAAC;EAE3C,CAAC,CAAC,OAAOE,KAAK,EAAE;IAAA;IAAAnB,cAAA,GAAAS,CAAA;IACdW,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IAAC;IAAAnB,cAAA,GAAAS,CAAA;IACrD,OAAOL,mBAAmB,CACxB,8BAA8B,EAC9BC,YAAY,CAACgB,cAAc,EAC3Bf,UAAU,CAACgB,qBACb,CAAC;EACH;AACF,CAAC,CAAC", "ignoreList": []}