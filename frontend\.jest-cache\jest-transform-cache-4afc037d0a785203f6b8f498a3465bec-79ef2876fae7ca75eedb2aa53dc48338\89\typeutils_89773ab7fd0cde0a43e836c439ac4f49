6b5952670666be14ccfaad74305b231e
/* istanbul ignore next */
function cov_1o0gle82dd() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\type-utils.ts";
  var hash = "ff1c9306b8fe233452b492d79c15b42794353627";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\type-utils.ts",
    statementMap: {
      "0": {
        start: {
          line: 27,
          column: 26
        },
        end: {
          line: 38,
          column: 2
        }
      },
      "1": {
        start: {
          line: 40,
          column: 28
        },
        end: {
          line: 49,
          column: 2
        }
      },
      "2": {
        start: {
          line: 51,
          column: 29
        },
        end: {
          line: 62,
          column: 2
        }
      },
      "3": {
        start: {
          line: 66,
          column: 2
        },
        end: {
          line: 71,
          column: 3
        }
      },
      "4": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 67,
          column: 26
        }
      },
      "5": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 68,
          column: 23
        }
      },
      "6": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 70,
          column: 17
        }
      },
      "7": {
        start: {
          line: 75,
          column: 2
        },
        end: {
          line: 80,
          column: 3
        }
      },
      "8": {
        start: {
          line: 76,
          column: 4
        },
        end: {
          line: 76,
          column: 28
        }
      },
      "9": {
        start: {
          line: 77,
          column: 4
        },
        end: {
          line: 77,
          column: 25
        }
      },
      "10": {
        start: {
          line: 79,
          column: 4
        },
        end: {
          line: 79,
          column: 17
        }
      },
      "11": {
        start: {
          line: 84,
          column: 2
        },
        end: {
          line: 89,
          column: 3
        }
      },
      "12": {
        start: {
          line: 85,
          column: 4
        },
        end: {
          line: 85,
          column: 29
        }
      },
      "13": {
        start: {
          line: 86,
          column: 4
        },
        end: {
          line: 86,
          column: 26
        }
      },
      "14": {
        start: {
          line: 88,
          column: 4
        },
        end: {
          line: 88,
          column: 17
        }
      },
      "15": {
        start: {
          line: 94,
          column: 2
        },
        end: {
          line: 94,
          column: 49
        }
      },
      "16": {
        start: {
          line: 98,
          column: 2
        },
        end: {
          line: 98,
          column: 51
        }
      },
      "17": {
        start: {
          line: 102,
          column: 2
        },
        end: {
          line: 102,
          column: 52
        }
      },
      "18": {
        start: {
          line: 107,
          column: 2
        },
        end: {
          line: 107,
          column: 37
        }
      },
      "19": {
        start: {
          line: 111,
          column: 2
        },
        end: {
          line: 111,
          column: 39
        }
      },
      "20": {
        start: {
          line: 115,
          column: 2
        },
        end: {
          line: 115,
          column: 46
        }
      },
      "21": {
        start: {
          line: 123,
          column: 2
        },
        end: {
          line: 125,
          column: 3
        }
      },
      "22": {
        start: {
          line: 124,
          column: 4
        },
        end: {
          line: 124,
          column: 17
        }
      },
      "23": {
        start: {
          line: 127,
          column: 2
        },
        end: {
          line: 129,
          column: 3
        }
      },
      "24": {
        start: {
          line: 128,
          column: 4
        },
        end: {
          line: 128,
          column: 35
        }
      },
      "25": {
        start: {
          line: 131,
          column: 2
        },
        end: {
          line: 131,
          column: 14
        }
      },
      "26": {
        start: {
          line: 135,
          column: 2
        },
        end: {
          line: 135,
          column: 77
        }
      },
      "27": {
        start: {
          line: 140,
          column: 2
        },
        end: {
          line: 142,
          column: 3
        }
      },
      "28": {
        start: {
          line: 141,
          column: 4
        },
        end: {
          line: 141,
          column: 82
        }
      },
      "29": {
        start: {
          line: 143,
          column: 2
        },
        end: {
          line: 143,
          column: 13
        }
      },
      "30": {
        start: {
          line: 147,
          column: 2
        },
        end: {
          line: 149,
          column: 3
        }
      },
      "31": {
        start: {
          line: 148,
          column: 4
        },
        end: {
          line: 148,
          column: 84
        }
      },
      "32": {
        start: {
          line: 150,
          column: 2
        },
        end: {
          line: 150,
          column: 13
        }
      },
      "33": {
        start: {
          line: 154,
          column: 2
        },
        end: {
          line: 156,
          column: 3
        }
      },
      "34": {
        start: {
          line: 155,
          column: 4
        },
        end: {
          line: 155,
          column: 91
        }
      },
      "35": {
        start: {
          line: 157,
          column: 2
        },
        end: {
          line: 157,
          column: 13
        }
      },
      "36": {
        start: {
          line: 161,
          column: 2
        },
        end: {
          line: 163,
          column: 3
        }
      },
      "37": {
        start: {
          line: 162,
          column: 4
        },
        end: {
          line: 162,
          column: 89
        }
      },
      "38": {
        start: {
          line: 164,
          column: 2
        },
        end: {
          line: 164,
          column: 13
        }
      },
      "39": {
        start: {
          line: 172,
          column: 2
        },
        end: {
          line: 172,
          column: 20
        }
      },
      "40": {
        start: {
          line: 180,
          column: 2
        },
        end: {
          line: 194,
          column: 3
        }
      },
      "41": {
        start: {
          line: 181,
          column: 17
        },
        end: {
          line: 181,
          column: 32
        }
      },
      "42": {
        start: {
          line: 182,
          column: 18
        },
        end: {
          line: 182,
          column: 21
        }
      },
      "43": {
        start: {
          line: 184,
          column: 4
        },
        end: {
          line: 189,
          column: 5
        }
      },
      "44": {
        start: {
          line: 185,
          column: 6
        },
        end: {
          line: 187,
          column: 7
        }
      },
      "45": {
        start: {
          line: 186,
          column: 8
        },
        end: {
          line: 186,
          column: 28
        }
      },
      "46": {
        start: {
          line: 188,
          column: 6
        },
        end: {
          line: 188,
          column: 29
        }
      },
      "47": {
        start: {
          line: 191,
          column: 4
        },
        end: {
          line: 191,
          column: 35
        }
      },
      "48": {
        start: {
          line: 193,
          column: 4
        },
        end: {
          line: 193,
          column: 24
        }
      },
      "49": {
        start: {
          line: 202,
          column: 2
        },
        end: {
          line: 202,
          column: 20
        }
      },
      "50": {
        start: {
          line: 209,
          column: 2
        },
        end: {
          line: 209,
          column: 61
        }
      },
      "51": {
        start: {
          line: 216,
          column: 2
        },
        end: {
          line: 216,
          column: 61
        }
      },
      "52": {
        start: {
          line: 223,
          column: 2
        },
        end: {
          line: 223,
          column: 62
        }
      },
      "53": {
        start: {
          line: 228,
          column: 2
        },
        end: {
          line: 228,
          column: 101
        }
      },
      "54": {
        start: {
          line: 232,
          column: 2
        },
        end: {
          line: 232,
          column: 99
        }
      },
      "55": {
        start: {
          line: 236,
          column: 2
        },
        end: {
          line: 236,
          column: 82
        }
      },
      "56": {
        start: {
          line: 241,
          column: 2
        },
        end: {
          line: 241,
          column: 58
        }
      },
      "57": {
        start: {
          line: 245,
          column: 2
        },
        end: {
          line: 245,
          column: 46
        }
      },
      "58": {
        start: {
          line: 245,
          column: 33
        },
        end: {
          line: 245,
          column: 46
        }
      },
      "59": {
        start: {
          line: 246,
          column: 15
        },
        end: {
          line: 246,
          column: 30
        }
      },
      "60": {
        start: {
          line: 247,
          column: 2
        },
        end: {
          line: 247,
          column: 27
        }
      },
      "61": {
        start: {
          line: 252,
          column: 2
        },
        end: {
          line: 252,
          column: 46
        }
      },
      "62": {
        start: {
          line: 252,
          column: 33
        },
        end: {
          line: 252,
          column: 46
        }
      },
      "63": {
        start: {
          line: 253,
          column: 21
        },
        end: {
          line: 253,
          column: 49
        }
      },
      "64": {
        start: {
          line: 254,
          column: 2
        },
        end: {
          line: 254,
          column: 32
        }
      },
      "65": {
        start: {
          line: 259,
          column: 2
        },
        end: {
          line: 259,
          column: 46
        }
      },
      "66": {
        start: {
          line: 259,
          column: 33
        },
        end: {
          line: 259,
          column: 46
        }
      },
      "67": {
        start: {
          line: 260,
          column: 20
        },
        end: {
          line: 260,
          column: 96
        }
      },
      "68": {
        start: {
          line: 261,
          column: 2
        },
        end: {
          line: 261,
          column: 31
        }
      },
      "69": {
        start: {
          line: 266,
          column: 2
        },
        end: {
          line: 266,
          column: 50
        }
      },
      "70": {
        start: {
          line: 270,
          column: 2
        },
        end: {
          line: 270,
          column: 79
        }
      },
      "71": {
        start: {
          line: 270,
          column: 53
        },
        end: {
          line: 270,
          column: 77
        }
      },
      "72": {
        start: {
          line: 274,
          column: 2
        },
        end: {
          line: 274,
          column: 79
        }
      },
      "73": {
        start: {
          line: 274,
          column: 53
        },
        end: {
          line: 274,
          column: 77
        }
      },
      "74": {
        start: {
          line: 279,
          column: 2
        },
        end: {
          line: 283,
          column: 4
        }
      },
      "75": {
        start: {
          line: 290,
          column: 2
        },
        end: {
          line: 290,
          column: 40
        }
      },
      "76": {
        start: {
          line: 290,
          column: 27
        },
        end: {
          line: 290,
          column: 40
        }
      },
      "77": {
        start: {
          line: 291,
          column: 2
        },
        end: {
          line: 291,
          column: 39
        }
      },
      "78": {
        start: {
          line: 291,
          column: 27
        },
        end: {
          line: 291,
          column: 37
        }
      },
      "79": {
        start: {
          line: 296,
          column: 2
        },
        end: {
          line: 296,
          column: 32
        }
      },
      "80": {
        start: {
          line: 300,
          column: 2
        },
        end: {
          line: 300,
          column: 77
        }
      },
      "81": {
        start: {
          line: 304,
          column: 2
        },
        end: {
          line: 304,
          column: 89
        }
      },
      "82": {
        start: {
          line: 309,
          column: 2
        },
        end: {
          line: 309,
          column: 48
        }
      },
      "83": {
        start: {
          line: 317,
          column: 2
        },
        end: {
          line: 325,
          column: 3
        }
      },
      "84": {
        start: {
          line: 318,
          column: 19
        },
        end: {
          line: 318,
          column: 35
        }
      },
      "85": {
        start: {
          line: 319,
          column: 4
        },
        end: {
          line: 321,
          column: 5
        }
      },
      "86": {
        start: {
          line: 320,
          column: 6
        },
        end: {
          line: 320,
          column: 18
        }
      },
      "87": {
        start: {
          line: 322,
          column: 4
        },
        end: {
          line: 322,
          column: 18
        }
      },
      "88": {
        start: {
          line: 324,
          column: 4
        },
        end: {
          line: 324,
          column: 16
        }
      }
    },
    fnMap: {
      "0": {
        name: "validateUser",
        decl: {
          start: {
            line: 65,
            column: 16
          },
          end: {
            line: 65,
            column: 28
          }
        },
        loc: {
          start: {
            line: 65,
            column: 56
          },
          end: {
            line: 72,
            column: 1
          }
        },
        line: 65
      },
      "1": {
        name: "validateClient",
        decl: {
          start: {
            line: 74,
            column: 16
          },
          end: {
            line: 74,
            column: 30
          }
        },
        loc: {
          start: {
            line: 74,
            column: 60
          },
          end: {
            line: 81,
            column: 1
          }
        },
        line: 74
      },
      "2": {
        name: "validateRenewal",
        decl: {
          start: {
            line: 83,
            column: 16
          },
          end: {
            line: 83,
            column: 31
          }
        },
        loc: {
          start: {
            line: 83,
            column: 62
          },
          end: {
            line: 90,
            column: 1
          }
        },
        line: 83
      },
      "3": {
        name: "isUserArray",
        decl: {
          start: {
            line: 93,
            column: 16
          },
          end: {
            line: 93,
            column: 27
          }
        },
        loc: {
          start: {
            line: 93,
            column: 57
          },
          end: {
            line: 95,
            column: 1
          }
        },
        line: 93
      },
      "4": {
        name: "isClientArray",
        decl: {
          start: {
            line: 97,
            column: 16
          },
          end: {
            line: 97,
            column: 29
          }
        },
        loc: {
          start: {
            line: 97,
            column: 61
          },
          end: {
            line: 99,
            column: 1
          }
        },
        line: 97
      },
      "5": {
        name: "isRenewalArray",
        decl: {
          start: {
            line: 101,
            column: 16
          },
          end: {
            line: 101,
            column: 30
          }
        },
        loc: {
          start: {
            line: 101,
            column: 63
          },
          end: {
            line: 103,
            column: 1
          }
        },
        line: 101
      },
      "6": {
        name: "isNullableUser",
        decl: {
          start: {
            line: 106,
            column: 16
          },
          end: {
            line: 106,
            column: 30
          }
        },
        loc: {
          start: {
            line: 106,
            column: 65
          },
          end: {
            line: 108,
            column: 1
          }
        },
        line: 106
      },
      "7": {
        name: "isNullableClient",
        decl: {
          start: {
            line: 110,
            column: 16
          },
          end: {
            line: 110,
            column: 32
          }
        },
        loc: {
          start: {
            line: 110,
            column: 69
          },
          end: {
            line: 112,
            column: 1
          }
        },
        line: 110
      },
      "8": {
        name: "isNullableTenantContext",
        decl: {
          start: {
            line: 114,
            column: 16
          },
          end: {
            line: 114,
            column: 39
          }
        },
        loc: {
          start: {
            line: 114,
            column: 83
          },
          end: {
            line: 116,
            column: 1
          }
        },
        line: 114
      },
      "9": {
        name: "isSuccessResponse",
        decl: {
          start: {
            line: 119,
            column: 16
          },
          end: {
            line: 119,
            column: 33
          }
        },
        loc: {
          start: {
            line: 122,
            column: 54
          },
          end: {
            line: 132,
            column: 1
          }
        },
        line: 122
      },
      "10": {
        name: "isErrorResponse",
        decl: {
          start: {
            line: 134,
            column: 16
          },
          end: {
            line: 134,
            column: 31
          }
        },
        loc: {
          start: {
            line: 134,
            column: 102
          },
          end: {
            line: 136,
            column: 1
          }
        },
        line: 134
      },
      "11": {
        name: "assertUser",
        decl: {
          start: {
            line: 139,
            column: 16
          },
          end: {
            line: 139,
            column: 26
          }
        },
        loc: {
          start: {
            line: 139,
            column: 68
          },
          end: {
            line: 144,
            column: 1
          }
        },
        line: 139
      },
      "12": {
        name: "assertClient",
        decl: {
          start: {
            line: 146,
            column: 16
          },
          end: {
            line: 146,
            column: 28
          }
        },
        loc: {
          start: {
            line: 146,
            column: 72
          },
          end: {
            line: 151,
            column: 1
          }
        },
        line: 146
      },
      "13": {
        name: "assertTenantContext",
        decl: {
          start: {
            line: 153,
            column: 16
          },
          end: {
            line: 153,
            column: 35
          }
        },
        loc: {
          start: {
            line: 153,
            column: 86
          },
          end: {
            line: 158,
            column: 1
          }
        },
        line: 153
      },
      "14": {
        name: "assertAuthSession",
        decl: {
          start: {
            line: 160,
            column: 16
          },
          end: {
            line: 160,
            column: 33
          }
        },
        loc: {
          start: {
            line: 160,
            column: 82
          },
          end: {
            line: 165,
            column: 1
          }
        },
        line: 160
      },
      "15": {
        name: "safeGetProperty",
        decl: {
          start: {
            line: 168,
            column: 16
          },
          end: {
            line: 168,
            column: 31
          }
        },
        loc: {
          start: {
            line: 171,
            column: 20
          },
          end: {
            line: 173,
            column: 1
          }
        },
        line: 171
      },
      "16": {
        name: "safeGetNestedProperty",
        decl: {
          start: {
            line: 175,
            column: 16
          },
          end: {
            line: 175,
            column: 37
          }
        },
        loc: {
          start: {
            line: 179,
            column: 17
          },
          end: {
            line: 195,
            column: 1
          }
        },
        line: 179
      },
      "17": {
        name: "hasProperty",
        decl: {
          start: {
            line: 198,
            column: 16
          },
          end: {
            line: 198,
            column: 27
          }
        },
        loc: {
          start: {
            line: 201,
            column: 33
          },
          end: {
            line: 203,
            column: 1
          }
        },
        line: 201
      },
      "18": {
        name: "hasStringProperty",
        decl: {
          start: {
            line: 205,
            column: 16
          },
          end: {
            line: 205,
            column: 33
          }
        },
        loc: {
          start: {
            line: 208,
            column: 32
          },
          end: {
            line: 210,
            column: 1
          }
        },
        line: 208
      },
      "19": {
        name: "hasNumberProperty",
        decl: {
          start: {
            line: 212,
            column: 16
          },
          end: {
            line: 212,
            column: 33
          }
        },
        loc: {
          start: {
            line: 215,
            column: 32
          },
          end: {
            line: 217,
            column: 1
          }
        },
        line: 215
      },
      "20": {
        name: "hasBooleanProperty",
        decl: {
          start: {
            line: 219,
            column: 16
          },
          end: {
            line: 219,
            column: 34
          }
        },
        loc: {
          start: {
            line: 222,
            column: 33
          },
          end: {
            line: 224,
            column: 1
          }
        },
        line: 222
      },
      "21": {
        name: "isValidStatus",
        decl: {
          start: {
            line: 227,
            column: 16
          },
          end: {
            line: 227,
            column: 29
          }
        },
        loc: {
          start: {
            line: 227,
            column: 104
          },
          end: {
            line: 229,
            column: 1
          }
        },
        line: 227
      },
      "22": {
        name: "isValidRenewalStatus",
        decl: {
          start: {
            line: 231,
            column: 16
          },
          end: {
            line: 231,
            column: 36
          }
        },
        loc: {
          start: {
            line: 231,
            column: 109
          },
          end: {
            line: 233,
            column: 1
          }
        },
        line: 231
      },
      "23": {
        name: "isValidTheme",
        decl: {
          start: {
            line: 235,
            column: 16
          },
          end: {
            line: 235,
            column: 28
          }
        },
        loc: {
          start: {
            line: 235,
            column: 83
          },
          end: {
            line: 237,
            column: 1
          }
        },
        line: 235
      },
      "24": {
        name: "isValidDate",
        decl: {
          start: {
            line: 240,
            column: 16
          },
          end: {
            line: 240,
            column: 27
          }
        },
        loc: {
          start: {
            line: 240,
            column: 59
          },
          end: {
            line: 242,
            column: 1
          }
        },
        line: 240
      },
      "25": {
        name: "isValidDateString",
        decl: {
          start: {
            line: 244,
            column: 16
          },
          end: {
            line: 244,
            column: 33
          }
        },
        loc: {
          start: {
            line: 244,
            column: 67
          },
          end: {
            line: 248,
            column: 1
          }
        },
        line: 244
      },
      "26": {
        name: "isValidEmail",
        decl: {
          start: {
            line: 251,
            column: 16
          },
          end: {
            line: 251,
            column: 28
          }
        },
        loc: {
          start: {
            line: 251,
            column: 62
          },
          end: {
            line: 255,
            column: 1
          }
        },
        line: 251
      },
      "27": {
        name: "isValidUUID",
        decl: {
          start: {
            line: 258,
            column: 16
          },
          end: {
            line: 258,
            column: 27
          }
        },
        loc: {
          start: {
            line: 258,
            column: 61
          },
          end: {
            line: 262,
            column: 1
          }
        },
        line: 258
      },
      "28": {
        name: "isNonEmptyArray",
        decl: {
          start: {
            line: 265,
            column: 16
          },
          end: {
            line: 265,
            column: 31
          }
        },
        loc: {
          start: {
            line: 265,
            column: 73
          },
          end: {
            line: 267,
            column: 1
          }
        },
        line: 265
      },
      "29": {
        name: "isStringArray",
        decl: {
          start: {
            line: 269,
            column: 16
          },
          end: {
            line: 269,
            column: 29
          }
        },
        loc: {
          start: {
            line: 269,
            column: 65
          },
          end: {
            line: 271,
            column: 1
          }
        },
        line: 269
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 270,
            column: 45
          },
          end: {
            line: 270,
            column: 46
          }
        },
        loc: {
          start: {
            line: 270,
            column: 53
          },
          end: {
            line: 270,
            column: 77
          }
        },
        line: 270
      },
      "31": {
        name: "isNumberArray",
        decl: {
          start: {
            line: 273,
            column: 16
          },
          end: {
            line: 273,
            column: 29
          }
        },
        loc: {
          start: {
            line: 273,
            column: 65
          },
          end: {
            line: 275,
            column: 1
          }
        },
        line: 273
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 274,
            column: 45
          },
          end: {
            line: 274,
            column: 46
          }
        },
        loc: {
          start: {
            line: 274,
            column: 53
          },
          end: {
            line: 274,
            column: 77
          }
        },
        line: 274
      },
      "33": {
        name: "isPlainObject",
        decl: {
          start: {
            line: 278,
            column: 16
          },
          end: {
            line: 278,
            column: 29
          }
        },
        loc: {
          start: {
            line: 278,
            column: 80
          },
          end: {
            line: 284,
            column: 1
          }
        },
        line: 278
      },
      "34": {
        name: "hasRequiredKeys",
        decl: {
          start: {
            line: 286,
            column: 16
          },
          end: {
            line: 286,
            column: 31
          }
        },
        loc: {
          start: {
            line: 289,
            column: 12
          },
          end: {
            line: 292,
            column: 1
          }
        },
        line: 289
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 291,
            column: 20
          },
          end: {
            line: 291,
            column: 21
          }
        },
        loc: {
          start: {
            line: 291,
            column: 27
          },
          end: {
            line: 291,
            column: 37
          }
        },
        line: 291
      },
      "36": {
        name: "isError",
        decl: {
          start: {
            line: 295,
            column: 16
          },
          end: {
            line: 295,
            column: 23
          }
        },
        loc: {
          start: {
            line: 295,
            column: 56
          },
          end: {
            line: 297,
            column: 1
          }
        },
        line: 295
      },
      "37": {
        name: "isErrorWithCode",
        decl: {
          start: {
            line: 299,
            column: 16
          },
          end: {
            line: 299,
            column: 31
          }
        },
        loc: {
          start: {
            line: 299,
            column: 83
          },
          end: {
            line: 301,
            column: 1
          }
        },
        line: 299
      },
      "38": {
        name: "isErrorWithStatus",
        decl: {
          start: {
            line: 303,
            column: 16
          },
          end: {
            line: 303,
            column: 33
          }
        },
        loc: {
          start: {
            line: 303,
            column: 91
          },
          end: {
            line: 305,
            column: 1
          }
        },
        line: 303
      },
      "39": {
        name: "assertNever",
        decl: {
          start: {
            line: 308,
            column: 16
          },
          end: {
            line: 308,
            column: 27
          }
        },
        loc: {
          start: {
            line: 308,
            column: 49
          },
          end: {
            line: 310,
            column: 1
          }
        },
        line: 308
      },
      "40": {
        name: "safeJsonParse",
        decl: {
          start: {
            line: 313,
            column: 16
          },
          end: {
            line: 313,
            column: 29
          }
        },
        loc: {
          start: {
            line: 316,
            column: 12
          },
          end: {
            line: 326,
            column: 1
          }
        },
        line: 316
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 94,
            column: 9
          },
          end: {
            line: 94,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 94,
            column: 9
          },
          end: {
            line: 94,
            column: 27
          }
        }, {
          start: {
            line: 94,
            column: 31
          },
          end: {
            line: 94,
            column: 48
          }
        }],
        line: 94
      },
      "1": {
        loc: {
          start: {
            line: 98,
            column: 9
          },
          end: {
            line: 98,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 98,
            column: 9
          },
          end: {
            line: 98,
            column: 27
          }
        }, {
          start: {
            line: 98,
            column: 31
          },
          end: {
            line: 98,
            column: 50
          }
        }],
        line: 98
      },
      "2": {
        loc: {
          start: {
            line: 102,
            column: 9
          },
          end: {
            line: 102,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 102,
            column: 9
          },
          end: {
            line: 102,
            column: 27
          }
        }, {
          start: {
            line: 102,
            column: 31
          },
          end: {
            line: 102,
            column: 51
          }
        }],
        line: 102
      },
      "3": {
        loc: {
          start: {
            line: 107,
            column: 9
          },
          end: {
            line: 107,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 107,
            column: 9
          },
          end: {
            line: 107,
            column: 21
          }
        }, {
          start: {
            line: 107,
            column: 25
          },
          end: {
            line: 107,
            column: 36
          }
        }],
        line: 107
      },
      "4": {
        loc: {
          start: {
            line: 111,
            column: 9
          },
          end: {
            line: 111,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 111,
            column: 9
          },
          end: {
            line: 111,
            column: 21
          }
        }, {
          start: {
            line: 111,
            column: 25
          },
          end: {
            line: 111,
            column: 38
          }
        }],
        line: 111
      },
      "5": {
        loc: {
          start: {
            line: 115,
            column: 9
          },
          end: {
            line: 115,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 115,
            column: 9
          },
          end: {
            line: 115,
            column: 21
          }
        }, {
          start: {
            line: 115,
            column: 25
          },
          end: {
            line: 115,
            column: 45
          }
        }],
        line: 115
      },
      "6": {
        loc: {
          start: {
            line: 123,
            column: 2
          },
          end: {
            line: 125,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 123,
            column: 2
          },
          end: {
            line: 125,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 123
      },
      "7": {
        loc: {
          start: {
            line: 123,
            column: 6
          },
          end: {
            line: 123,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 123,
            column: 6
          },
          end: {
            line: 123,
            column: 25
          }
        }, {
          start: {
            line: 123,
            column: 29
          },
          end: {
            line: 123,
            column: 41
          }
        }, {
          start: {
            line: 123,
            column: 45
          },
          end: {
            line: 123,
            column: 54
          }
        }],
        line: 123
      },
      "8": {
        loc: {
          start: {
            line: 127,
            column: 2
          },
          end: {
            line: 129,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 127,
            column: 2
          },
          end: {
            line: 129,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 127
      },
      "9": {
        loc: {
          start: {
            line: 135,
            column: 9
          },
          end: {
            line: 135,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 135,
            column: 9
          },
          end: {
            line: 135,
            column: 27
          }
        }, {
          start: {
            line: 135,
            column: 31
          },
          end: {
            line: 135,
            column: 43
          }
        }, {
          start: {
            line: 135,
            column: 47
          },
          end: {
            line: 135,
            column: 76
          }
        }],
        line: 135
      },
      "10": {
        loc: {
          start: {
            line: 139,
            column: 41
          },
          end: {
            line: 139,
            column: 60
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 139,
            column: 51
          },
          end: {
            line: 139,
            column: 60
          }
        }],
        line: 139
      },
      "11": {
        loc: {
          start: {
            line: 140,
            column: 2
          },
          end: {
            line: 142,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 140,
            column: 2
          },
          end: {
            line: 142,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 140
      },
      "12": {
        loc: {
          start: {
            line: 146,
            column: 43
          },
          end: {
            line: 146,
            column: 62
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 146,
            column: 53
          },
          end: {
            line: 146,
            column: 62
          }
        }],
        line: 146
      },
      "13": {
        loc: {
          start: {
            line: 147,
            column: 2
          },
          end: {
            line: 149,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 147,
            column: 2
          },
          end: {
            line: 149,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 147
      },
      "14": {
        loc: {
          start: {
            line: 153,
            column: 50
          },
          end: {
            line: 153,
            column: 69
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 153,
            column: 60
          },
          end: {
            line: 153,
            column: 69
          }
        }],
        line: 153
      },
      "15": {
        loc: {
          start: {
            line: 154,
            column: 2
          },
          end: {
            line: 156,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 154,
            column: 2
          },
          end: {
            line: 156,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 154
      },
      "16": {
        loc: {
          start: {
            line: 160,
            column: 48
          },
          end: {
            line: 160,
            column: 67
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 160,
            column: 58
          },
          end: {
            line: 160,
            column: 67
          }
        }],
        line: 160
      },
      "17": {
        loc: {
          start: {
            line: 161,
            column: 2
          },
          end: {
            line: 163,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 161,
            column: 2
          },
          end: {
            line: 163,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 161
      },
      "18": {
        loc: {
          start: {
            line: 185,
            column: 6
          },
          end: {
            line: 187,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 185,
            column: 6
          },
          end: {
            line: 187,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 185
      },
      "19": {
        loc: {
          start: {
            line: 185,
            column: 10
          },
          end: {
            line: 185,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 185,
            column: 10
          },
          end: {
            line: 185,
            column: 25
          }
        }, {
          start: {
            line: 185,
            column: 29
          },
          end: {
            line: 185,
            column: 56
          }
        }],
        line: 185
      },
      "20": {
        loc: {
          start: {
            line: 191,
            column: 11
          },
          end: {
            line: 191,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 191,
            column: 11
          },
          end: {
            line: 191,
            column: 18
          }
        }, {
          start: {
            line: 191,
            column: 22
          },
          end: {
            line: 191,
            column: 34
          }
        }],
        line: 191
      },
      "21": {
        loc: {
          start: {
            line: 209,
            column: 9
          },
          end: {
            line: 209,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 209,
            column: 9
          },
          end: {
            line: 209,
            column: 19
          }
        }, {
          start: {
            line: 209,
            column: 23
          },
          end: {
            line: 209,
            column: 60
          }
        }],
        line: 209
      },
      "22": {
        loc: {
          start: {
            line: 216,
            column: 9
          },
          end: {
            line: 216,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 216,
            column: 9
          },
          end: {
            line: 216,
            column: 19
          }
        }, {
          start: {
            line: 216,
            column: 23
          },
          end: {
            line: 216,
            column: 60
          }
        }],
        line: 216
      },
      "23": {
        loc: {
          start: {
            line: 223,
            column: 9
          },
          end: {
            line: 223,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 223,
            column: 9
          },
          end: {
            line: 223,
            column: 19
          }
        }, {
          start: {
            line: 223,
            column: 23
          },
          end: {
            line: 223,
            column: 61
          }
        }],
        line: 223
      },
      "24": {
        loc: {
          start: {
            line: 228,
            column: 9
          },
          end: {
            line: 228,
            column: 100
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 228,
            column: 9
          },
          end: {
            line: 228,
            column: 34
          }
        }, {
          start: {
            line: 228,
            column: 38
          },
          end: {
            line: 228,
            column: 100
          }
        }],
        line: 228
      },
      "25": {
        loc: {
          start: {
            line: 232,
            column: 9
          },
          end: {
            line: 232,
            column: 98
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 232,
            column: 9
          },
          end: {
            line: 232,
            column: 34
          }
        }, {
          start: {
            line: 232,
            column: 38
          },
          end: {
            line: 232,
            column: 98
          }
        }],
        line: 232
      },
      "26": {
        loc: {
          start: {
            line: 236,
            column: 9
          },
          end: {
            line: 236,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 236,
            column: 9
          },
          end: {
            line: 236,
            column: 34
          }
        }, {
          start: {
            line: 236,
            column: 38
          },
          end: {
            line: 236,
            column: 81
          }
        }],
        line: 236
      },
      "27": {
        loc: {
          start: {
            line: 241,
            column: 9
          },
          end: {
            line: 241,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 241,
            column: 9
          },
          end: {
            line: 241,
            column: 30
          }
        }, {
          start: {
            line: 241,
            column: 34
          },
          end: {
            line: 241,
            column: 57
          }
        }],
        line: 241
      },
      "28": {
        loc: {
          start: {
            line: 245,
            column: 2
          },
          end: {
            line: 245,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 245,
            column: 2
          },
          end: {
            line: 245,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 245
      },
      "29": {
        loc: {
          start: {
            line: 252,
            column: 2
          },
          end: {
            line: 252,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 252,
            column: 2
          },
          end: {
            line: 252,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 252
      },
      "30": {
        loc: {
          start: {
            line: 259,
            column: 2
          },
          end: {
            line: 259,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 259,
            column: 2
          },
          end: {
            line: 259,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 259
      },
      "31": {
        loc: {
          start: {
            line: 266,
            column: 9
          },
          end: {
            line: 266,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 266,
            column: 9
          },
          end: {
            line: 266,
            column: 29
          }
        }, {
          start: {
            line: 266,
            column: 33
          },
          end: {
            line: 266,
            column: 49
          }
        }],
        line: 266
      },
      "32": {
        loc: {
          start: {
            line: 270,
            column: 9
          },
          end: {
            line: 270,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 270,
            column: 9
          },
          end: {
            line: 270,
            column: 29
          }
        }, {
          start: {
            line: 270,
            column: 33
          },
          end: {
            line: 270,
            column: 78
          }
        }],
        line: 270
      },
      "33": {
        loc: {
          start: {
            line: 274,
            column: 9
          },
          end: {
            line: 274,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 274,
            column: 9
          },
          end: {
            line: 274,
            column: 29
          }
        }, {
          start: {
            line: 274,
            column: 33
          },
          end: {
            line: 274,
            column: 78
          }
        }],
        line: 274
      },
      "34": {
        loc: {
          start: {
            line: 280,
            column: 4
          },
          end: {
            line: 282,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 280,
            column: 4
          },
          end: {
            line: 280,
            column: 18
          }
        }, {
          start: {
            line: 281,
            column: 4
          },
          end: {
            line: 281,
            column: 29
          }
        }, {
          start: {
            line: 282,
            column: 4
          },
          end: {
            line: 282,
            column: 63
          }
        }],
        line: 280
      },
      "35": {
        loc: {
          start: {
            line: 290,
            column: 2
          },
          end: {
            line: 290,
            column: 40
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 290,
            column: 2
          },
          end: {
            line: 290,
            column: 40
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 290
      },
      "36": {
        loc: {
          start: {
            line: 300,
            column: 9
          },
          end: {
            line: 300,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 300,
            column: 9
          },
          end: {
            line: 300,
            column: 23
          }
        }, {
          start: {
            line: 300,
            column: 27
          },
          end: {
            line: 300,
            column: 42
          }
        }, {
          start: {
            line: 300,
            column: 46
          },
          end: {
            line: 300,
            column: 76
          }
        }],
        line: 300
      },
      "37": {
        loc: {
          start: {
            line: 304,
            column: 9
          },
          end: {
            line: 304,
            column: 88
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 304,
            column: 9
          },
          end: {
            line: 304,
            column: 23
          }
        }, {
          start: {
            line: 304,
            column: 27
          },
          end: {
            line: 304,
            column: 48
          }
        }, {
          start: {
            line: 304,
            column: 52
          },
          end: {
            line: 304,
            column: 88
          }
        }],
        line: 304
      },
      "38": {
        loc: {
          start: {
            line: 319,
            column: 4
          },
          end: {
            line: 321,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 319,
            column: 4
          },
          end: {
            line: 321,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 319
      },
      "39": {
        loc: {
          start: {
            line: 319,
            column: 8
          },
          end: {
            line: 319,
            column: 39
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 319,
            column: 8
          },
          end: {
            line: 319,
            column: 17
          }
        }, {
          start: {
            line: 319,
            column: 21
          },
          end: {
            line: 319,
            column: 39
          }
        }],
        line: 319
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0, 0],
      "8": [0, 0],
      "9": [0, 0, 0],
      "10": [0],
      "11": [0, 0],
      "12": [0],
      "13": [0, 0],
      "14": [0],
      "15": [0, 0],
      "16": [0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0, 0],
      "35": [0, 0],
      "36": [0, 0, 0],
      "37": [0, 0, 0],
      "38": [0, 0],
      "39": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ff1c9306b8fe233452b492d79c15b42794353627"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1o0gle82dd = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1o0gle82dd();
/**
 * Type Utilities and Advanced Type Guards
 * 
 * This module provides advanced type checking utilities, runtime type validation,
 * and helper functions for working with TypeScript types safely.
 */

import { z } from 'zod';
import { isUser, isClient, isRenewal, isTenantContext, isApiResponse, isAuthSession } from './types';

// Runtime type validation schemas
export const userSchema =
/* istanbul ignore next */
(cov_1o0gle82dd().s[0]++, z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  name: z.string().optional(),
  given_name: z.string().optional(),
  family_name: z.string().optional(),
  roles: z.array(z.string()),
  status: z.enum(['active', 'inactive', 'suspended', 'deleted']),
  created_at: z.date(),
  updated_at: z.date().optional(),
  last_login: z.date().optional()
}));
export const clientSchema =
/* istanbul ignore next */
(cov_1o0gle82dd().s[1]++, z.object({
  id: z.string().uuid(),
  name: z.string().min(1),
  domain: z.string().min(1),
  domains: z.array(z.string()).optional(),
  status: z.enum(['active', 'inactive', 'suspended']),
  settings: z.record(z.any()),
  created_at: z.date(),
  updated_at: z.date().optional()
}));
export const renewalSchema =
/* istanbul ignore next */
(cov_1o0gle82dd().s[2]++, z.object({
  id: z.string().uuid(),
  name: z.string().min(1),
  vendor: z.string().min(1),
  vendor_id: z.string().optional(),
  status: z.enum(['active', 'inactive', 'pending', 'expired']),
  due_date: z.date().optional(),
  annual_cost: z.number().min(0).optional(),
  description: z.string().optional(),
  created_at: z.date(),
  updated_at: z.date().optional()
}));

// Advanced type guards with detailed validation
export function validateUser(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[0]++;
  cov_1o0gle82dd().s[3]++;
  try {
    /* istanbul ignore next */
    cov_1o0gle82dd().s[4]++;
    userSchema.parse(obj);
    /* istanbul ignore next */
    cov_1o0gle82dd().s[5]++;
    return isUser(obj);
  } catch {
    /* istanbul ignore next */
    cov_1o0gle82dd().s[6]++;
    return false;
  }
}
export function validateClient(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[1]++;
  cov_1o0gle82dd().s[7]++;
  try {
    /* istanbul ignore next */
    cov_1o0gle82dd().s[8]++;
    clientSchema.parse(obj);
    /* istanbul ignore next */
    cov_1o0gle82dd().s[9]++;
    return isClient(obj);
  } catch {
    /* istanbul ignore next */
    cov_1o0gle82dd().s[10]++;
    return false;
  }
}
export function validateRenewal(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[2]++;
  cov_1o0gle82dd().s[11]++;
  try {
    /* istanbul ignore next */
    cov_1o0gle82dd().s[12]++;
    renewalSchema.parse(obj);
    /* istanbul ignore next */
    cov_1o0gle82dd().s[13]++;
    return isRenewal(obj);
  } catch {
    /* istanbul ignore next */
    cov_1o0gle82dd().s[14]++;
    return false;
  }
}

// Array type guards
export function isUserArray(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[3]++;
  cov_1o0gle82dd().s[15]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[0][0]++, Array.isArray(obj)) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[0][1]++, obj.every(isUser));
}
export function isClientArray(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[4]++;
  cov_1o0gle82dd().s[16]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[1][0]++, Array.isArray(obj)) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[1][1]++, obj.every(isClient));
}
export function isRenewalArray(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[5]++;
  cov_1o0gle82dd().s[17]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[2][0]++, Array.isArray(obj)) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[2][1]++, obj.every(isRenewal));
}

// Nullable type guards
export function isNullableUser(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[6]++;
  cov_1o0gle82dd().s[18]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[3][0]++, obj === null) ||
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[3][1]++, isUser(obj));
}
export function isNullableClient(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[7]++;
  cov_1o0gle82dd().s[19]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[4][0]++, obj === null) ||
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[4][1]++, isClient(obj));
}
export function isNullableTenantContext(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[8]++;
  cov_1o0gle82dd().s[20]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[5][0]++, obj === null) ||
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[5][1]++, isTenantContext(obj));
}

// API response type guards
export function isSuccessResponse(obj, dataValidator) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[9]++;
  cov_1o0gle82dd().s[21]++;
  if (
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[7][0]++, !isApiResponse(obj)) ||
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[7][1]++, !obj.success) ||
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[7][2]++, !obj.data)) {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[6][0]++;
    cov_1o0gle82dd().s[22]++;
    return false;
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[6][1]++;
  }
  cov_1o0gle82dd().s[23]++;
  if (dataValidator) {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[8][0]++;
    cov_1o0gle82dd().s[24]++;
    return dataValidator(obj.data);
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[8][1]++;
  }
  cov_1o0gle82dd().s[25]++;
  return true;
}
export function isErrorResponse(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[10]++;
  cov_1o0gle82dd().s[26]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[9][0]++, isApiResponse(obj)) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[9][1]++, !obj.success) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[9][2]++, typeof obj.error === 'string');
}

// Type assertion helpers with runtime validation
export function assertUser(obj, context =
/* istanbul ignore next */
(cov_1o0gle82dd().b[10][0]++, 'Unknown')) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[11]++;
  cov_1o0gle82dd().s[27]++;
  if (!isUser(obj)) {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[11][0]++;
    cov_1o0gle82dd().s[28]++;
    throw new TypeError(`Expected User object in ${context}, got: ${typeof obj}`);
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[11][1]++;
  }
  cov_1o0gle82dd().s[29]++;
  return obj;
}
export function assertClient(obj, context =
/* istanbul ignore next */
(cov_1o0gle82dd().b[12][0]++, 'Unknown')) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[12]++;
  cov_1o0gle82dd().s[30]++;
  if (!isClient(obj)) {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[13][0]++;
    cov_1o0gle82dd().s[31]++;
    throw new TypeError(`Expected Client object in ${context}, got: ${typeof obj}`);
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[13][1]++;
  }
  cov_1o0gle82dd().s[32]++;
  return obj;
}
export function assertTenantContext(obj, context =
/* istanbul ignore next */
(cov_1o0gle82dd().b[14][0]++, 'Unknown')) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[13]++;
  cov_1o0gle82dd().s[33]++;
  if (!isTenantContext(obj)) {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[15][0]++;
    cov_1o0gle82dd().s[34]++;
    throw new TypeError(`Expected TenantContext object in ${context}, got: ${typeof obj}`);
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[15][1]++;
  }
  cov_1o0gle82dd().s[35]++;
  return obj;
}
export function assertAuthSession(obj, context =
/* istanbul ignore next */
(cov_1o0gle82dd().b[16][0]++, 'Unknown')) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[14]++;
  cov_1o0gle82dd().s[36]++;
  if (!isAuthSession(obj)) {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[17][0]++;
    cov_1o0gle82dd().s[37]++;
    throw new TypeError(`Expected AuthSession object in ${context}, got: ${typeof obj}`);
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[17][1]++;
  }
  cov_1o0gle82dd().s[38]++;
  return obj;
}

// Safe property access helpers
export function safeGetProperty(obj, key) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[15]++;
  cov_1o0gle82dd().s[39]++;
  return obj?.[key];
}
export function safeGetNestedProperty(obj, path, defaultValue) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[16]++;
  cov_1o0gle82dd().s[40]++;
  try {
    const keys =
    /* istanbul ignore next */
    (cov_1o0gle82dd().s[41]++, path.split('.'));
    let current =
    /* istanbul ignore next */
    (cov_1o0gle82dd().s[42]++, obj);
    /* istanbul ignore next */
    cov_1o0gle82dd().s[43]++;
    for (const key of keys) {
      /* istanbul ignore next */
      cov_1o0gle82dd().s[44]++;
      if (
      /* istanbul ignore next */
      (cov_1o0gle82dd().b[19][0]++, current == null) ||
      /* istanbul ignore next */
      (cov_1o0gle82dd().b[19][1]++, typeof current !== 'object')) {
        /* istanbul ignore next */
        cov_1o0gle82dd().b[18][0]++;
        cov_1o0gle82dd().s[45]++;
        return defaultValue;
      } else
      /* istanbul ignore next */
      {
        cov_1o0gle82dd().b[18][1]++;
      }
      cov_1o0gle82dd().s[46]++;
      current = current[key];
    }
    /* istanbul ignore next */
    cov_1o0gle82dd().s[47]++;
    return /* istanbul ignore next */(cov_1o0gle82dd().b[20][0]++, current) ??
    /* istanbul ignore next */
    (cov_1o0gle82dd().b[20][1]++, defaultValue);
  } catch {
    /* istanbul ignore next */
    cov_1o0gle82dd().s[48]++;
    return defaultValue;
  }
}

// Type narrowing helpers
export function hasProperty(obj, key) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[17]++;
  cov_1o0gle82dd().s[49]++;
  return key in obj;
}
export function hasStringProperty(obj, key) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[18]++;
  cov_1o0gle82dd().s[50]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[21][0]++, key in obj) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[21][1]++, typeof obj[key] === 'string');
}
export function hasNumberProperty(obj, key) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[19]++;
  cov_1o0gle82dd().s[51]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[22][0]++, key in obj) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[22][1]++, typeof obj[key] === 'number');
}
export function hasBooleanProperty(obj, key) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[20]++;
  cov_1o0gle82dd().s[52]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[23][0]++, key in obj) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[23][1]++, typeof obj[key] === 'boolean');
}

// Enum validation helpers
export function isValidStatus(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[21]++;
  cov_1o0gle82dd().s[53]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[24][0]++, typeof value === 'string') &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[24][1]++, ['active', 'inactive', 'suspended', 'deleted'].includes(value));
}
export function isValidRenewalStatus(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[22]++;
  cov_1o0gle82dd().s[54]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[25][0]++, typeof value === 'string') &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[25][1]++, ['active', 'inactive', 'pending', 'expired'].includes(value));
}
export function isValidTheme(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[23]++;
  cov_1o0gle82dd().s[55]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[26][0]++, typeof value === 'string') &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[26][1]++, ['light', 'dark', 'system'].includes(value));
}

// Date validation helpers
export function isValidDate(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[24]++;
  cov_1o0gle82dd().s[56]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[27][0]++, value instanceof Date) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[27][1]++, !isNaN(value.getTime()));
}
export function isValidDateString(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[25]++;
  cov_1o0gle82dd().s[57]++;
  if (typeof value !== 'string') {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[28][0]++;
    cov_1o0gle82dd().s[58]++;
    return false;
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[28][1]++;
  }
  const date =
  /* istanbul ignore next */
  (cov_1o0gle82dd().s[59]++, new Date(value));
  /* istanbul ignore next */
  cov_1o0gle82dd().s[60]++;
  return isValidDate(date);
}

// Email validation helper
export function isValidEmail(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[26]++;
  cov_1o0gle82dd().s[61]++;
  if (typeof value !== 'string') {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[29][0]++;
    cov_1o0gle82dd().s[62]++;
    return false;
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[29][1]++;
  }
  const emailRegex =
  /* istanbul ignore next */
  (cov_1o0gle82dd().s[63]++, /^[^\s@]+@[^\s@]+\.[^\s@]+$/);
  /* istanbul ignore next */
  cov_1o0gle82dd().s[64]++;
  return emailRegex.test(value);
}

// UUID validation helper
export function isValidUUID(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[27]++;
  cov_1o0gle82dd().s[65]++;
  if (typeof value !== 'string') {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[30][0]++;
    cov_1o0gle82dd().s[66]++;
    return false;
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[30][1]++;
  }
  const uuidRegex =
  /* istanbul ignore next */
  (cov_1o0gle82dd().s[67]++, /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
  /* istanbul ignore next */
  cov_1o0gle82dd().s[68]++;
  return uuidRegex.test(value);
}

// Array validation helpers
export function isNonEmptyArray(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[28]++;
  cov_1o0gle82dd().s[69]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[31][0]++, Array.isArray(value)) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[31][1]++, value.length > 0);
}
export function isStringArray(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[29]++;
  cov_1o0gle82dd().s[70]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[32][0]++, Array.isArray(value)) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[32][1]++, value.every(item => {
    /* istanbul ignore next */
    cov_1o0gle82dd().f[30]++;
    cov_1o0gle82dd().s[71]++;
    return typeof item === 'string';
  }));
}
export function isNumberArray(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[31]++;
  cov_1o0gle82dd().s[72]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[33][0]++, Array.isArray(value)) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[33][1]++, value.every(item => {
    /* istanbul ignore next */
    cov_1o0gle82dd().f[32]++;
    cov_1o0gle82dd().s[73]++;
    return typeof item === 'number';
  }));
}

// Object validation helpers
export function isPlainObject(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[33]++;
  cov_1o0gle82dd().s[74]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[34][0]++, value !== null) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[34][1]++, typeof value === 'object') &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[34][2]++, Object.prototype.toString.call(value) === '[object Object]');
}
export function hasRequiredKeys(obj, keys) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[34]++;
  cov_1o0gle82dd().s[75]++;
  if (!isPlainObject(obj)) {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[35][0]++;
    cov_1o0gle82dd().s[76]++;
    return false;
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[35][1]++;
  }
  cov_1o0gle82dd().s[77]++;
  return keys.every(key => {
    /* istanbul ignore next */
    cov_1o0gle82dd().f[35]++;
    cov_1o0gle82dd().s[78]++;
    return key in obj;
  });
}

// Error handling type guards
export function isError(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[36]++;
  cov_1o0gle82dd().s[79]++;
  return value instanceof Error;
}
export function isErrorWithCode(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[37]++;
  cov_1o0gle82dd().s[80]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[36][0]++, isError(value)) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[36][1]++, 'code' in value) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[36][2]++, typeof value.code === 'string');
}
export function isErrorWithStatus(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[38]++;
  cov_1o0gle82dd().s[81]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[37][0]++, isError(value)) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[37][1]++, 'statusCode' in value) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[37][2]++, typeof value.statusCode === 'number');
}

// Utility type for exhaustive checking
export function assertNever(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[39]++;
  cov_1o0gle82dd().s[82]++;
  throw new Error(`Unexpected value: ${value}`);
}

// Type-safe JSON parsing
export function safeJsonParse(json, validator) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[40]++;
  cov_1o0gle82dd().s[83]++;
  try {
    const parsed =
    /* istanbul ignore next */
    (cov_1o0gle82dd().s[84]++, JSON.parse(json));
    /* istanbul ignore next */
    cov_1o0gle82dd().s[85]++;
    if (
    /* istanbul ignore next */
    (cov_1o0gle82dd().b[39][0]++, validator) &&
    /* istanbul ignore next */
    (cov_1o0gle82dd().b[39][1]++, !validator(parsed))) {
      /* istanbul ignore next */
      cov_1o0gle82dd().b[38][0]++;
      cov_1o0gle82dd().s[86]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_1o0gle82dd().b[38][1]++;
    }
    cov_1o0gle82dd().s[87]++;
    return parsed;
  } catch {
    /* istanbul ignore next */
    cov_1o0gle82dd().s[88]++;
    return null;
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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