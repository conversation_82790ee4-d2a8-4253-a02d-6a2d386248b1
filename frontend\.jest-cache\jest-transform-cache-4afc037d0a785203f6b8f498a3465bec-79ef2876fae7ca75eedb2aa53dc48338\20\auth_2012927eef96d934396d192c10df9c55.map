{"version": 3, "names": ["cov_1vq3bzo45z", "actualCoverage", "fetchAuthSession", "signOut", "amplifySignOut", "configureAmplify", "publicConfig", "validate<PERSON><PERSON><PERSON><PERSON><PERSON>", "s", "getLoginUrl", "f", "cognitoDomain", "userPoolClientId", "aws", "redirectSignIn", "auth", "encodeURIComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "idToken", "document", "cookie", "console", "log", "isAuthenticated", "session", "tokens", "error", "global", "localStorage", "removeItem", "verifyAuth", "request", "auth<PERSON><PERSON><PERSON>", "headers", "get", "split", "find", "c", "trim", "startsWith", "b", "payload", "userId", "sub", "email", "name", "given_name", "roles"], "sources": ["auth.ts"], "sourcesContent": ["import { fetchAuthSession, signOut as amplifySignOut } from 'aws-amplify/auth'\r\nimport { configureAmplify } from './amplify-config'\r\nimport { publicConfig } from './config'\r\nimport { validateAuthCookie } from './jwt-validator'\r\n\r\n// Get login URL using configuration\r\nexport const getLoginUrl = () => {\r\n  const { cognitoDomain, userPoolClientId } = publicConfig.aws;\r\n  const { redirectSignIn } = publicConfig.auth;\r\n\r\n  return `https://${cognitoDomain}/login?client_id=${userPoolClientId}&response_type=code&scope=aws.cognito.signin.user.admin+email+openid+profile&redirect_uri=${encodeURIComponent(redirectSignIn)}`;\r\n}\r\n\r\n// Set auth cookie\r\nexport const setAuthCookie = (idToken: string) => {\r\n  document.cookie = `idToken=${idToken}; path=/; max-age=${60*60*24*7}; SameSite=Lax`;\r\n  console.log('Auth cookie set successfully');\r\n}\r\n\r\n// Check if user is authenticated\r\nexport const isAuthenticated = async () => {\r\n  try {\r\n    // Ensure Amplify is configured\r\n    configureAmplify();\r\n    \r\n    const session = await fetchAuthSession();\r\n    return !!session?.tokens?.idToken;\r\n  } catch (error) {\r\n    console.error('Auth check error:', error);\r\n    return false;\r\n  }\r\n}\r\n\r\n// Sign out\r\nexport const signOut = async () => {\r\n  try {\r\n    // Ensure Amplify is configured\r\n    configureAmplify();\r\n    \r\n    // Sign out from Amplify\r\n    await amplifySignOut({ global: true });\r\n    \r\n    // Clear local storage and cookies\r\n    localStorage.removeItem('isAuthenticated');\r\n    document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';\r\n    \r\n    return true;\r\n  } catch (error) {\r\n    console.error('Error signing out:', error);\r\n    \r\n    // Clear local storage and cookies anyway\r\n    localStorage.removeItem('isAuthenticated');\r\n    document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';\r\n    \r\n    return true;\r\n  }\r\n}\r\n\r\n// Note: JWT parsing and validation is now handled by the secure jwt-validator module\r\n\r\n// Server-side function to verify authentication with secure JWT validation\r\nexport const verifyAuth = async (request: Request) => {\r\n  try {\r\n    const authCookie = request.headers.get('Cookie')?.split(';')\r\n      .find(c => c.trim().startsWith('idToken='))\r\n      ?.split('=')[1];\r\n\r\n    if (!authCookie) {\r\n      return false;\r\n    }\r\n\r\n    // Use secure JWT validation with signature verification\r\n    const payload = await validateAuthCookie(authCookie);\r\n    if (!payload) {\r\n      return false;\r\n    }\r\n\r\n    return {\r\n      isAuthenticated: true,\r\n      userId: payload.sub,\r\n      email: payload.email,\r\n      name: payload.name || payload.given_name,\r\n      roles: payload['cognito:groups'] || []\r\n    };\r\n  } catch (error) {\r\n    console.error('Auth verification error:', error);\r\n    return false;\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ,SAASE,gBAAgB,EAAEC,OAAO,IAAIC,cAAc,QAAQ,kBAAkB;AAC9E,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,kBAAkB,QAAQ,iBAAiB;;AAEpD;AAAA;AAAAP,cAAA,GAAAQ,CAAA;AACA,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAA;EAAAT,cAAA,GAAAU,CAAA;EAC/B,MAAM;IAAEC,aAAa;IAAEC;EAAiB,CAAC;EAAA;EAAA,CAAAZ,cAAA,GAAAQ,CAAA,OAAGF,YAAY,CAACO,GAAG;EAC5D,MAAM;IAAEC;EAAe,CAAC;EAAA;EAAA,CAAAd,cAAA,GAAAQ,CAAA,OAAGF,YAAY,CAACS,IAAI;EAAC;EAAAf,cAAA,GAAAQ,CAAA;EAE7C,OAAO,WAAWG,aAAa,oBAAoBC,gBAAgB,6FAA6FI,kBAAkB,CAACF,cAAc,CAAC,EAAE;AACtM,CAAC;;AAED;AAAA;AAAAd,cAAA,GAAAQ,CAAA;AACA,OAAO,MAAMS,aAAa,GAAIC,OAAe,IAAK;EAAA;EAAAlB,cAAA,GAAAU,CAAA;EAAAV,cAAA,GAAAQ,CAAA;EAChDW,QAAQ,CAACC,MAAM,GAAG,WAAWF,OAAO,qBAAqB,EAAE,GAAC,EAAE,GAAC,EAAE,GAAC,CAAC,gBAAgB;EAAC;EAAAlB,cAAA,GAAAQ,CAAA;EACpFa,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;AAC7C,CAAC;;AAED;AAAA;AAAAtB,cAAA,GAAAQ,CAAA;AACA,OAAO,MAAMe,eAAe,GAAG,MAAAA,CAAA,KAAY;EAAA;EAAAvB,cAAA,GAAAU,CAAA;EAAAV,cAAA,GAAAQ,CAAA;EACzC,IAAI;IAAA;IAAAR,cAAA,GAAAQ,CAAA;IACF;IACAH,gBAAgB,CAAC,CAAC;IAElB,MAAMmB,OAAO;IAAA;IAAA,CAAAxB,cAAA,GAAAQ,CAAA,QAAG,MAAMN,gBAAgB,CAAC,CAAC;IAAC;IAAAF,cAAA,GAAAQ,CAAA;IACzC,OAAO,CAAC,CAACgB,OAAO,EAAEC,MAAM,EAAEP,OAAO;EACnC,CAAC,CAAC,OAAOQ,KAAK,EAAE;IAAA;IAAA1B,cAAA,GAAAQ,CAAA;IACda,OAAO,CAACK,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;IAAC;IAAA1B,cAAA,GAAAQ,CAAA;IAC1C,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AAAA;AAAAR,cAAA,GAAAQ,CAAA;AACA,OAAO,MAAML,OAAO,GAAG,MAAAA,CAAA,KAAY;EAAA;EAAAH,cAAA,GAAAU,CAAA;EAAAV,cAAA,GAAAQ,CAAA;EACjC,IAAI;IAAA;IAAAR,cAAA,GAAAQ,CAAA;IACF;IACAH,gBAAgB,CAAC,CAAC;;IAElB;IAAA;IAAAL,cAAA,GAAAQ,CAAA;IACA,MAAMJ,cAAc,CAAC;MAAEuB,MAAM,EAAE;IAAK,CAAC,CAAC;;IAEtC;IAAA;IAAA3B,cAAA,GAAAQ,CAAA;IACAoB,YAAY,CAACC,UAAU,CAAC,iBAAiB,CAAC;IAAC;IAAA7B,cAAA,GAAAQ,CAAA;IAC3CW,QAAQ,CAACC,MAAM,GAAG,2CAA2C;IAAC;IAAApB,cAAA,GAAAQ,CAAA;IAE9D,OAAO,IAAI;EACb,CAAC,CAAC,OAAOkB,KAAK,EAAE;IAAA;IAAA1B,cAAA,GAAAQ,CAAA;IACda,OAAO,CAACK,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;;IAE1C;IAAA;IAAA1B,cAAA,GAAAQ,CAAA;IACAoB,YAAY,CAACC,UAAU,CAAC,iBAAiB,CAAC;IAAC;IAAA7B,cAAA,GAAAQ,CAAA;IAC3CW,QAAQ,CAACC,MAAM,GAAG,2CAA2C;IAAC;IAAApB,cAAA,GAAAQ,CAAA;IAE9D,OAAO,IAAI;EACb;AACF,CAAC;;AAED;;AAEA;AAAA;AAAAR,cAAA,GAAAQ,CAAA;AACA,OAAO,MAAMsB,UAAU,GAAG,MAAOC,OAAgB,IAAK;EAAA;EAAA/B,cAAA,GAAAU,CAAA;EAAAV,cAAA,GAAAQ,CAAA;EACpD,IAAI;IACF,MAAMwB,UAAU;IAAA;IAAA,CAAAhC,cAAA,GAAAQ,CAAA,QAAGuB,OAAO,CAACE,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,EAAEC,KAAK,CAAC,GAAG,CAAC,CACzDC,IAAI,CAACC,CAAC,IAAI;MAAA;MAAArC,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAQ,CAAA;MAAA,OAAA6B,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,UAAU,CAAC,UAAU,CAAC;IAAD,CAAC,CAAC,EACzCJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC;IAAAnC,cAAA,GAAAQ,CAAA;IAElB,IAAI,CAACwB,UAAU,EAAE;MAAA;MAAAhC,cAAA,GAAAwC,CAAA;MAAAxC,cAAA,GAAAQ,CAAA;MACf,OAAO,KAAK;IACd,CAAC;IAAA;IAAA;MAAAR,cAAA,GAAAwC,CAAA;IAAA;;IAED;IACA,MAAMC,OAAO;IAAA;IAAA,CAAAzC,cAAA,GAAAQ,CAAA,QAAG,MAAMD,kBAAkB,CAACyB,UAAU,CAAC;IAAC;IAAAhC,cAAA,GAAAQ,CAAA;IACrD,IAAI,CAACiC,OAAO,EAAE;MAAA;MAAAzC,cAAA,GAAAwC,CAAA;MAAAxC,cAAA,GAAAQ,CAAA;MACZ,OAAO,KAAK;IACd,CAAC;IAAA;IAAA;MAAAR,cAAA,GAAAwC,CAAA;IAAA;IAAAxC,cAAA,GAAAQ,CAAA;IAED,OAAO;MACLe,eAAe,EAAE,IAAI;MACrBmB,MAAM,EAAED,OAAO,CAACE,GAAG;MACnBC,KAAK,EAAEH,OAAO,CAACG,KAAK;MACpBC,IAAI;MAAE;MAAA,CAAA7C,cAAA,GAAAwC,CAAA,UAAAC,OAAO,CAACI,IAAI;MAAA;MAAA,CAAA7C,cAAA,GAAAwC,CAAA,UAAIC,OAAO,CAACK,UAAU;MACxCC,KAAK;MAAE;MAAA,CAAA/C,cAAA,GAAAwC,CAAA,UAAAC,OAAO,CAAC,gBAAgB,CAAC;MAAA;MAAA,CAAAzC,cAAA,GAAAwC,CAAA,UAAI,EAAE;IACxC,CAAC;EACH,CAAC,CAAC,OAAOd,KAAK,EAAE;IAAA;IAAA1B,cAAA,GAAAQ,CAAA;IACda,OAAO,CAACK,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAAC;IAAA1B,cAAA,GAAAQ,CAAA;IACjD,OAAO,KAAK;EACd;AACF,CAAC", "ignoreList": []}