2b7c672b63c71bf282134393c2957ba5
/* istanbul ignore next */
function cov_44vatrfhc() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\config.ts";
  var hash = "1f8392462d5b192fa3d4cda736596d304883c24c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\config.ts",
    statementMap: {
      "0": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 32,
          column: 2
        }
      },
      "1": {
        start: {
          line: 36,
          column: 2
        },
        end: {
          line: 41,
          column: 3
        }
      },
      "2": {
        start: {
          line: 37,
          column: 4
        },
        end: {
          line: 37,
          column: 40
        }
      },
      "3": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 65
        }
      },
      "4": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 40,
          column: 84
        }
      },
      "5": {
        start: {
          line: 45,
          column: 12
        },
        end: {
          line: 45,
          column: 25
        }
      },
      "6": {
        start: {
          line: 48,
          column: 28
        },
        end: {
          line: 64,
          column: 10
        }
      },
      "7": {
        start: {
          line: 67,
          column: 28
        },
        end: {
          line: 76,
          column: 10
        }
      },
      "8": {
        start: {
          line: 83,
          column: 29
        },
        end: {
          line: 83,
          column: 52
        }
      },
      "9": {
        start: {
          line: 83,
          column: 35
        },
        end: {
          line: 83,
          column: 52
        }
      },
      "10": {
        start: {
          line: 84,
          column: 28
        },
        end: {
          line: 84,
          column: 50
        }
      },
      "11": {
        start: {
          line: 84,
          column: 34
        },
        end: {
          line: 84,
          column: 50
        }
      },
      "12": {
        start: {
          line: 85,
          column: 33
        },
        end: {
          line: 90,
          column: 1
        }
      },
      "13": {
        start: {
          line: 86,
          column: 2
        },
        end: {
          line: 88,
          column: 3
        }
      },
      "14": {
        start: {
          line: 87,
          column: 4
        },
        end: {
          line: 87,
          column: 82
        }
      },
      "15": {
        start: {
          line: 89,
          column: 2
        },
        end: {
          line: 89,
          column: 31
        }
      },
      "16": {
        start: {
          line: 93,
          column: 30
        },
        end: {
          line: 108,
          column: 1
        }
      },
      "17": {
        start: {
          line: 94,
          column: 29
        },
        end: {
          line: 99,
          column: 3
        }
      },
      "18": {
        start: {
          line: 101,
          column: 18
        },
        end: {
          line: 101,
          column: 69
        }
      },
      "19": {
        start: {
          line: 101,
          column: 51
        },
        end: {
          line: 101,
          column: 68
        }
      },
      "20": {
        start: {
          line: 103,
          column: 2
        },
        end: {
          line: 105,
          column: 3
        }
      },
      "21": {
        start: {
          line: 104,
          column: 4
        },
        end: {
          line: 104,
          column: 85
        }
      },
      "22": {
        start: {
          line: 107,
          column: 2
        },
        end: {
          line: 107,
          column: 14
        }
      }
    },
    fnMap: {
      "0": {
        name: "validateEnv",
        decl: {
          start: {
            line: 35,
            column: 9
          },
          end: {
            line: 35,
            column: 20
          }
        },
        loc: {
          start: {
            line: 35,
            column: 23
          },
          end: {
            line: 42,
            column: 1
          }
        },
        line: 35
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 83,
            column: 29
          },
          end: {
            line: 83,
            column: 30
          }
        },
        loc: {
          start: {
            line: 83,
            column: 35
          },
          end: {
            line: 83,
            column: 52
          }
        },
        line: 83
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 84,
            column: 28
          },
          end: {
            line: 84,
            column: 29
          }
        },
        loc: {
          start: {
            line: 84,
            column: 34
          },
          end: {
            line: 84,
            column: 50
          }
        },
        line: 84
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 85,
            column: 33
          },
          end: {
            line: 85,
            column: 34
          }
        },
        loc: {
          start: {
            line: 85,
            column: 39
          },
          end: {
            line: 90,
            column: 1
          }
        },
        line: 85
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 93,
            column: 30
          },
          end: {
            line: 93,
            column: 31
          }
        },
        loc: {
          start: {
            line: 93,
            column: 36
          },
          end: {
            line: 108,
            column: 1
          }
        },
        line: 93
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 101,
            column: 44
          },
          end: {
            line: 101,
            column: 45
          }
        },
        loc: {
          start: {
            line: 101,
            column: 51
          },
          end: {
            line: 101,
            column: 68
          }
        },
        line: 101
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 86,
            column: 2
          },
          end: {
            line: 88,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 86,
            column: 2
          },
          end: {
            line: 88,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 86
      },
      "1": {
        loc: {
          start: {
            line: 103,
            column: 2
          },
          end: {
            line: 105,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 103,
            column: 2
          },
          end: {
            line: 105,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 103
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1f8392462d5b192fa3d4cda736596d304883c24c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_44vatrfhc = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_44vatrfhc();
/**
 * Centralized Configuration Management
 * 
 * This module provides a secure, centralized way to manage all application configuration.
 * It validates environment variables and provides type-safe access to configuration values.
 */

import { z } from 'zod';

// Environment validation schema
const envSchema =
/* istanbul ignore next */
(cov_44vatrfhc().s[0]++, z.object({
  // AWS Configuration
  NEXT_PUBLIC_AWS_REGION: z.string().min(1, 'AWS region is required'),
  NEXT_PUBLIC_AWS_USER_POOLS_ID: z.string().min(1, 'User pool ID is required'),
  NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID: z.string().min(1, 'User pool client ID is required'),
  NEXT_PUBLIC_AWS_COGNITO_DOMAIN: z.string().min(1, 'Cognito domain is required'),
  // Redirect URLs
  NEXT_PUBLIC_REDIRECT_SIGN_IN: z.string().url('Invalid sign-in redirect URL'),
  NEXT_PUBLIC_REDIRECT_SIGN_OUT: z.string().url('Invalid sign-out redirect URL'),
  // Database Configuration (server-side only)
  DB_USER: z.string().optional(),
  DB_PASSWORD: z.string().optional(),
  DB_HOST: z.string().optional(),
  DB_NAME: z.string().optional(),
  DATABASE_URL: z.string().optional(),
  DATABASE_SSL: z.string().optional(),
  // Environment
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development')
}));

// Validate environment variables
function validateEnv() {
  /* istanbul ignore next */
  cov_44vatrfhc().f[0]++;
  cov_44vatrfhc().s[1]++;
  try {
    /* istanbul ignore next */
    cov_44vatrfhc().s[2]++;
    return envSchema.parse(process.env);
  } catch (error) {
    /* istanbul ignore next */
    cov_44vatrfhc().s[3]++;
    console.error('❌ Invalid environment configuration:', error);
    /* istanbul ignore next */
    cov_44vatrfhc().s[4]++;
    throw new Error('Environment validation failed. Please check your .env files.');
  }
}

// Get validated environment variables
const env =
/* istanbul ignore next */
(cov_44vatrfhc().s[5]++, validateEnv());

// Public configuration (safe to expose to client)
export const publicConfig =
/* istanbul ignore next */
(cov_44vatrfhc().s[6]++, {
  aws: {
    region: env.NEXT_PUBLIC_AWS_REGION,
    userPoolId: env.NEXT_PUBLIC_AWS_USER_POOLS_ID,
    userPoolClientId: env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID,
    cognitoDomain: env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN
  },
  auth: {
    redirectSignIn: env.NEXT_PUBLIC_REDIRECT_SIGN_IN,
    redirectSignOut: env.NEXT_PUBLIC_REDIRECT_SIGN_OUT
  },
  app: {
    environment: env.NODE_ENV,
    isDevelopment: env.NODE_ENV === 'development',
    isProduction: env.NODE_ENV === 'production'
  }
});

// Server-side configuration (never expose to client)
export const serverConfig =
/* istanbul ignore next */
(cov_44vatrfhc().s[7]++, {
  database: {
    user: env.DB_USER,
    password: env.DB_PASSWORD,
    host: env.DB_HOST,
    name: env.DB_NAME,
    url: env.DATABASE_URL,
    ssl: env.DATABASE_SSL === 'true'
  }
});

// Type exports for better TypeScript support
/* istanbul ignore next */
// Utility functions
cov_44vatrfhc().s[8]++;
export const getAuthConfig = () => {
  /* istanbul ignore next */
  cov_44vatrfhc().f[1]++;
  cov_44vatrfhc().s[9]++;
  return publicConfig.auth;
};
/* istanbul ignore next */
cov_44vatrfhc().s[10]++;
export const getAwsConfig = () => {
  /* istanbul ignore next */
  cov_44vatrfhc().f[2]++;
  cov_44vatrfhc().s[11]++;
  return publicConfig.aws;
};
/* istanbul ignore next */
cov_44vatrfhc().s[12]++;
export const getDatabaseConfig = () => {
  /* istanbul ignore next */
  cov_44vatrfhc().f[3]++;
  cov_44vatrfhc().s[13]++;
  if (typeof window !== 'undefined') {
    /* istanbul ignore next */
    cov_44vatrfhc().b[0][0]++;
    cov_44vatrfhc().s[14]++;
    throw new Error('Database configuration is not available on the client side');
  } else
  /* istanbul ignore next */
  {
    cov_44vatrfhc().b[0][1]++;
  }
  cov_44vatrfhc().s[15]++;
  return serverConfig.database;
};

// Configuration validation for runtime checks
/* istanbul ignore next */
cov_44vatrfhc().s[16]++;
export const validateConfig = () => {
  /* istanbul ignore next */
  cov_44vatrfhc().f[4]++;
  const requiredPublicVars =
  /* istanbul ignore next */
  (cov_44vatrfhc().s[17]++, ['NEXT_PUBLIC_AWS_REGION', 'NEXT_PUBLIC_AWS_USER_POOLS_ID', 'NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID', 'NEXT_PUBLIC_AWS_COGNITO_DOMAIN']);
  const missing =
  /* istanbul ignore next */
  (cov_44vatrfhc().s[18]++, requiredPublicVars.filter(key => {
    /* istanbul ignore next */
    cov_44vatrfhc().f[5]++;
    cov_44vatrfhc().s[19]++;
    return !process.env[key];
  }));
  /* istanbul ignore next */
  cov_44vatrfhc().s[20]++;
  if (missing.length > 0) {
    /* istanbul ignore next */
    cov_44vatrfhc().b[1][0]++;
    cov_44vatrfhc().s[21]++;
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  } else
  /* istanbul ignore next */
  {
    cov_44vatrfhc().b[1][1]++;
  }
  cov_44vatrfhc().s[22]++;
  return true;
};

// Export environment for backward compatibility (to be removed)
export { env };
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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