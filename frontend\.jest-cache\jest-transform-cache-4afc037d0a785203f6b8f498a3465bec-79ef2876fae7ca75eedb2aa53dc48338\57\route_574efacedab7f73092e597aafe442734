eb31e43867771153b32c591bd1ffb387
/* istanbul ignore next */
function cov_gmoz0mnhh() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\clients\\[id]\\route.ts";
  var hash = "d2794923177ec9baaff5c0b0e7505ac82c9fdd34";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\clients\\[id]\\route.ts",
    statementMap: {
      "0": {
        start: {
          line: 7,
          column: 27
        },
        end: {
          line: 15,
          column: 2
        }
      },
      "1": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 22,
          column: 39
        }
      },
      "2": {
        start: {
          line: 23,
          column: 2
        },
        end: {
          line: 25,
          column: 3
        }
      },
      "3": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 73
        }
      },
      "4": {
        start: {
          line: 27,
          column: 19
        },
        end: {
          line: 27,
          column: 28
        }
      },
      "5": {
        start: {
          line: 28,
          column: 2
        },
        end: {
          line: 30,
          column: 3
        }
      },
      "6": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 29,
          column: 82
        }
      },
      "7": {
        start: {
          line: 32,
          column: 2
        },
        end: {
          line: 60,
          column: 3
        }
      },
      "8": {
        start: {
          line: 33,
          column: 17
        },
        end: {
          line: 33,
          column: 37
        }
      },
      "9": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 43,
          column: 5
        }
      },
      "10": {
        start: {
          line: 37,
          column: 6
        },
        end: {
          line: 37,
          column: 37
        }
      },
      "11": {
        start: {
          line: 39,
          column: 6
        },
        end: {
          line: 42,
          column: 26
        }
      },
      "12": {
        start: {
          line: 45,
          column: 26
        },
        end: {
          line: 50,
          column: 6
        }
      },
      "13": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 54,
          column: 5
        }
      },
      "14": {
        start: {
          line: 53,
          column: 6
        },
        end: {
          line: 53,
          column: 79
        }
      },
      "15": {
        start: {
          line: 56,
          column: 4
        },
        end: {
          line: 56,
          column: 44
        }
      },
      "16": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 58,
          column: 51
        }
      },
      "17": {
        start: {
          line: 59,
          column: 4
        },
        end: {
          line: 59,
          column: 84
        }
      }
    },
    fnMap: {
      "0": {
        name: "PATCH",
        decl: {
          start: {
            line: 17,
            column: 22
          },
          end: {
            line: 17,
            column: 27
          }
        },
        loc: {
          start: {
            line: 20,
            column: 2
          },
          end: {
            line: 61,
            column: 1
          }
        },
        line: 20
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 23,
            column: 2
          },
          end: {
            line: 25,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 2
          },
          end: {
            line: 25,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "1": {
        loc: {
          start: {
            line: 23,
            column: 6
          },
          end: {
            line: 23,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 23,
            column: 6
          },
          end: {
            line: 23,
            column: 14
          }
        }, {
          start: {
            line: 23,
            column: 18
          },
          end: {
            line: 23,
            column: 50
          }
        }],
        line: 23
      },
      "2": {
        loc: {
          start: {
            line: 28,
            column: 2
          },
          end: {
            line: 30,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 2
          },
          end: {
            line: 30,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "3": {
        loc: {
          start: {
            line: 41,
            column: 17
          },
          end: {
            line: 41,
            column: 76
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 41,
            column: 42
          },
          end: {
            line: 41,
            column: 55
          }
        }, {
          start: {
            line: 41,
            column: 58
          },
          end: {
            line: 41,
            column: 76
          }
        }],
        line: 41
      },
      "4": {
        loc: {
          start: {
            line: 52,
            column: 4
          },
          end: {
            line: 54,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 52,
            column: 4
          },
          end: {
            line: 54,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 52
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d2794923177ec9baaff5c0b0e7505ac82c9fdd34"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_gmoz0mnhh = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_gmoz0mnhh();
import { NextResponse } from 'next/server';
import { updateClient } from '../../../../lib/clients';
import { verifySession } from '../../../../lib/dal';
import { z } from 'zod';

// Define schema for client updates
const updateClientSchema =
/* istanbul ignore next */
(cov_gmoz0mnhh().s[0]++, z.object({
  name: z.string().min(2).max(255).optional(),
  domain: z.string().min(3).max(255).regex(/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/, 'Invalid domain format').optional(),
  status: z.enum(['active', 'inactive', 'pending']).optional(),
  settings: z.record(z.unknown()).optional()
}));
export async function PATCH(request, {
  params
}) {
  /* istanbul ignore next */
  cov_gmoz0mnhh().f[0]++;
  // Verify authentication and authorization
  const session =
  /* istanbul ignore next */
  (cov_gmoz0mnhh().s[1]++, await verifySession());
  /* istanbul ignore next */
  cov_gmoz0mnhh().s[2]++;
  if (
  /* istanbul ignore next */
  (cov_gmoz0mnhh().b[1][0]++, !session) ||
  /* istanbul ignore next */
  (cov_gmoz0mnhh().b[1][1]++, !session.roles.includes('admin'))) {
    /* istanbul ignore next */
    cov_gmoz0mnhh().b[0][0]++;
    cov_gmoz0mnhh().s[3]++;
    return NextResponse.json({
      error: 'Unauthorized'
    }, {
      status: 401
    });
  } else
  /* istanbul ignore next */
  {
    cov_gmoz0mnhh().b[0][1]++;
  }
  const clientId =
  /* istanbul ignore next */
  (cov_gmoz0mnhh().s[4]++, params.id);
  /* istanbul ignore next */
  cov_gmoz0mnhh().s[5]++;
  if (!clientId) {
    /* istanbul ignore next */
    cov_gmoz0mnhh().b[2][0]++;
    cov_gmoz0mnhh().s[6]++;
    return NextResponse.json({
      error: 'Client ID is required'
    }, {
      status: 400
    });
  } else
  /* istanbul ignore next */
  {
    cov_gmoz0mnhh().b[2][1]++;
  }
  cov_gmoz0mnhh().s[7]++;
  try {
    const body =
    /* istanbul ignore next */
    (cov_gmoz0mnhh().s[8]++, await request.json());

    // Validate input
    /* istanbul ignore next */
    cov_gmoz0mnhh().s[9]++;
    try {
      /* istanbul ignore next */
      cov_gmoz0mnhh().s[10]++;
      updateClientSchema.parse(body);
    } catch (error) {
      /* istanbul ignore next */
      cov_gmoz0mnhh().s[11]++;
      return NextResponse.json({
        error: 'Invalid client data',
        details: error instanceof Error ?
        /* istanbul ignore next */
        (cov_gmoz0mnhh().b[3][0]++, error.message) :
        /* istanbul ignore next */
        (cov_gmoz0mnhh().b[3][1]++, 'Validation error')
      }, {
        status: 400
      });
    }
    const updatedClient =
    /* istanbul ignore next */
    (cov_gmoz0mnhh().s[12]++, await updateClient(clientId, {
      name: body.name,
      domain: body.domain,
      status: body.status,
      settings: body.settings
    }));
    /* istanbul ignore next */
    cov_gmoz0mnhh().s[13]++;
    if (!updatedClient) {
      /* istanbul ignore next */
      cov_gmoz0mnhh().b[4][0]++;
      cov_gmoz0mnhh().s[14]++;
      return NextResponse.json({
        error: 'Client not found'
      }, {
        status: 404
      });
    } else
    /* istanbul ignore next */
    {
      cov_gmoz0mnhh().b[4][1]++;
    }
    cov_gmoz0mnhh().s[15]++;
    return NextResponse.json(updatedClient);
  } catch (error) {
    /* istanbul ignore next */
    cov_gmoz0mnhh().s[16]++;
    console.error('Error updating client:', error);
    /* istanbul ignore next */
    cov_gmoz0mnhh().s[17]++;
    return NextResponse.json({
      error: 'Failed to update client'
    }, {
      status: 500
    });
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfZ21vejBtbmhoIiwiYWN0dWFsQ292ZXJhZ2UiLCJOZXh0UmVzcG9uc2UiLCJ1cGRhdGVDbGllbnQiLCJ2ZXJpZnlTZXNzaW9uIiwieiIsInVwZGF0ZUNsaWVudFNjaGVtYSIsInMiLCJvYmplY3QiLCJuYW1lIiwic3RyaW5nIiwibWluIiwibWF4Iiwib3B0aW9uYWwiLCJkb21haW4iLCJyZWdleCIsInN0YXR1cyIsImVudW0iLCJzZXR0aW5ncyIsInJlY29yZCIsInVua25vd24iLCJQQVRDSCIsInJlcXVlc3QiLCJwYXJhbXMiLCJmIiwic2Vzc2lvbiIsImIiLCJyb2xlcyIsImluY2x1ZGVzIiwianNvbiIsImVycm9yIiwiY2xpZW50SWQiLCJpZCIsImJvZHkiLCJwYXJzZSIsImRldGFpbHMiLCJFcnJvciIsIm1lc3NhZ2UiLCJ1cGRhdGVkQ2xpZW50IiwiY29uc29sZSJdLCJzb3VyY2VzIjpbInJvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5pbXBvcnQgeyB1cGRhdGVDbGllbnQgfSBmcm9tICcuLi8uLi8uLi8uLi9saWIvY2xpZW50cyc7XG5pbXBvcnQgeyB2ZXJpZnlTZXNzaW9uIH0gZnJvbSAnLi4vLi4vLi4vLi4vbGliL2RhbCc7XG5pbXBvcnQgeyB6IH0gZnJvbSAnem9kJztcblxuLy8gRGVmaW5lIHNjaGVtYSBmb3IgY2xpZW50IHVwZGF0ZXNcbmNvbnN0IHVwZGF0ZUNsaWVudFNjaGVtYSA9IHoub2JqZWN0KHtcbiAgbmFtZTogei5zdHJpbmcoKS5taW4oMikubWF4KDI1NSkub3B0aW9uYWwoKSxcbiAgZG9tYWluOiB6LnN0cmluZygpLm1pbigzKS5tYXgoMjU1KS5yZWdleChcbiAgICAvXlthLXpBLVowLTldW2EtekEtWjAtOS1dezAsNjF9W2EtekEtWjAtOV0oPzpcXC5bYS16QS1aXXsyLH0pKyQvLFxuICAgICdJbnZhbGlkIGRvbWFpbiBmb3JtYXQnXG4gICkub3B0aW9uYWwoKSxcbiAgc3RhdHVzOiB6LmVudW0oWydhY3RpdmUnLCAnaW5hY3RpdmUnLCAncGVuZGluZyddKS5vcHRpb25hbCgpLFxuICBzZXR0aW5nczogei5yZWNvcmQoei51bmtub3duKCkpLm9wdGlvbmFsKClcbn0pO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUEFUQ0goXG4gIHJlcXVlc3Q6IE5leHRSZXF1ZXN0LFxuICB7IHBhcmFtcyB9OiB7IHBhcmFtczogeyBpZDogc3RyaW5nIH0gfVxuKSB7XG4gIC8vIFZlcmlmeSBhdXRoZW50aWNhdGlvbiBhbmQgYXV0aG9yaXphdGlvblxuICBjb25zdCBzZXNzaW9uID0gYXdhaXQgdmVyaWZ5U2Vzc2lvbigpO1xuICBpZiAoIXNlc3Npb24gfHwgIXNlc3Npb24ucm9sZXMuaW5jbHVkZXMoJ2FkbWluJykpIHtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ1VuYXV0aG9yaXplZCcgfSwgeyBzdGF0dXM6IDQwMSB9KTtcbiAgfVxuICBcbiAgY29uc3QgY2xpZW50SWQgPSBwYXJhbXMuaWQ7XG4gIGlmICghY2xpZW50SWQpIHtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ0NsaWVudCBJRCBpcyByZXF1aXJlZCcgfSwgeyBzdGF0dXM6IDQwMCB9KTtcbiAgfVxuICBcbiAgdHJ5IHtcbiAgICBjb25zdCBib2R5ID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XG4gICAgXG4gICAgLy8gVmFsaWRhdGUgaW5wdXRcbiAgICB0cnkge1xuICAgICAgdXBkYXRlQ2xpZW50U2NoZW1hLnBhcnNlKGJvZHkpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBcbiAgICAgICAgZXJyb3I6ICdJbnZhbGlkIGNsaWVudCBkYXRhJyxcbiAgICAgICAgZGV0YWlsczogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVmFsaWRhdGlvbiBlcnJvcidcbiAgICAgIH0sIHsgc3RhdHVzOiA0MDAgfSk7XG4gICAgfVxuICAgIFxuICAgIGNvbnN0IHVwZGF0ZWRDbGllbnQgPSBhd2FpdCB1cGRhdGVDbGllbnQoY2xpZW50SWQsIHtcbiAgICAgIG5hbWU6IGJvZHkubmFtZSxcbiAgICAgIGRvbWFpbjogYm9keS5kb21haW4sXG4gICAgICBzdGF0dXM6IGJvZHkuc3RhdHVzLFxuICAgICAgc2V0dGluZ3M6IGJvZHkuc2V0dGluZ3NcbiAgICB9KTtcbiAgICBcbiAgICBpZiAoIXVwZGF0ZWRDbGllbnQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnQ2xpZW50IG5vdCBmb3VuZCcgfSwgeyBzdGF0dXM6IDQwNCB9KTtcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHVwZGF0ZWRDbGllbnQpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIGNsaWVudDonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdGYWlsZWQgdG8gdXBkYXRlIGNsaWVudCcgfSwgeyBzdGF0dXM6IDUwMCB9KTtcbiAgfVxufSJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFlWTtJQUFBQSxhQUFBLFlBQUFBLENBQUE7TUFBQSxPQUFBQyxjQUFBO0lBQUE7RUFBQTtFQUFBLE9BQUFBLGNBQUE7QUFBQTtBQUFBRCxhQUFBO0FBZlosU0FBc0JFLFlBQVksUUFBUSxhQUFhO0FBQ3ZELFNBQVNDLFlBQVksUUFBUSx5QkFBeUI7QUFDdEQsU0FBU0MsYUFBYSxRQUFRLHFCQUFxQjtBQUNuRCxTQUFTQyxDQUFDLFFBQVEsS0FBSzs7QUFFdkI7QUFDQSxNQUFNQyxrQkFBa0I7QUFBQTtBQUFBLENBQUFOLGFBQUEsR0FBQU8sQ0FBQSxPQUFHRixDQUFDLENBQUNHLE1BQU0sQ0FBQztFQUNsQ0MsSUFBSSxFQUFFSixDQUFDLENBQUNLLE1BQU0sQ0FBQyxDQUFDLENBQUNDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQ0MsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFDQyxRQUFRLENBQUMsQ0FBQztFQUMzQ0MsTUFBTSxFQUFFVCxDQUFDLENBQUNLLE1BQU0sQ0FBQyxDQUFDLENBQUNDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQ0MsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFDRyxLQUFLLENBQ3RDLCtEQUErRCxFQUMvRCx1QkFDRixDQUFDLENBQUNGLFFBQVEsQ0FBQyxDQUFDO0VBQ1pHLE1BQU0sRUFBRVgsQ0FBQyxDQUFDWSxJQUFJLENBQUMsQ0FBQyxRQUFRLEVBQUUsVUFBVSxFQUFFLFNBQVMsQ0FBQyxDQUFDLENBQUNKLFFBQVEsQ0FBQyxDQUFDO0VBQzVESyxRQUFRLEVBQUViLENBQUMsQ0FBQ2MsTUFBTSxDQUFDZCxDQUFDLENBQUNlLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQ1AsUUFBUSxDQUFDO0FBQzNDLENBQUMsQ0FBQztBQUVGLE9BQU8sZUFBZVEsS0FBS0EsQ0FDekJDLE9BQW9CLEVBQ3BCO0VBQUVDO0FBQW1DLENBQUMsRUFDdEM7RUFBQTtFQUFBdkIsYUFBQSxHQUFBd0IsQ0FBQTtFQUNBO0VBQ0EsTUFBTUMsT0FBTztFQUFBO0VBQUEsQ0FBQXpCLGFBQUEsR0FBQU8sQ0FBQSxPQUFHLE1BQU1ILGFBQWEsQ0FBQyxDQUFDO0VBQUM7RUFBQUosYUFBQSxHQUFBTyxDQUFBO0VBQ3RDO0VBQUk7RUFBQSxDQUFBUCxhQUFBLEdBQUEwQixDQUFBLFdBQUNELE9BQU87RUFBQTtFQUFBLENBQUF6QixhQUFBLEdBQUEwQixDQUFBLFVBQUksQ0FBQ0QsT0FBTyxDQUFDRSxLQUFLLENBQUNDLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRTtJQUFBO0lBQUE1QixhQUFBLEdBQUEwQixDQUFBO0lBQUExQixhQUFBLEdBQUFPLENBQUE7SUFDaEQsT0FBT0wsWUFBWSxDQUFDMkIsSUFBSSxDQUFDO01BQUVDLEtBQUssRUFBRTtJQUFlLENBQUMsRUFBRTtNQUFFZCxNQUFNLEVBQUU7SUFBSSxDQUFDLENBQUM7RUFDdEUsQ0FBQztFQUFBO0VBQUE7SUFBQWhCLGFBQUEsR0FBQTBCLENBQUE7RUFBQTtFQUVELE1BQU1LLFFBQVE7RUFBQTtFQUFBLENBQUEvQixhQUFBLEdBQUFPLENBQUEsT0FBR2dCLE1BQU0sQ0FBQ1MsRUFBRTtFQUFDO0VBQUFoQyxhQUFBLEdBQUFPLENBQUE7RUFDM0IsSUFBSSxDQUFDd0IsUUFBUSxFQUFFO0lBQUE7SUFBQS9CLGFBQUEsR0FBQTBCLENBQUE7SUFBQTFCLGFBQUEsR0FBQU8sQ0FBQTtJQUNiLE9BQU9MLFlBQVksQ0FBQzJCLElBQUksQ0FBQztNQUFFQyxLQUFLLEVBQUU7SUFBd0IsQ0FBQyxFQUFFO01BQUVkLE1BQU0sRUFBRTtJQUFJLENBQUMsQ0FBQztFQUMvRSxDQUFDO0VBQUE7RUFBQTtJQUFBaEIsYUFBQSxHQUFBMEIsQ0FBQTtFQUFBO0VBQUExQixhQUFBLEdBQUFPLENBQUE7RUFFRCxJQUFJO0lBQ0YsTUFBTTBCLElBQUk7SUFBQTtJQUFBLENBQUFqQyxhQUFBLEdBQUFPLENBQUEsT0FBRyxNQUFNZSxPQUFPLENBQUNPLElBQUksQ0FBQyxDQUFDOztJQUVqQztJQUFBO0lBQUE3QixhQUFBLEdBQUFPLENBQUE7SUFDQSxJQUFJO01BQUE7TUFBQVAsYUFBQSxHQUFBTyxDQUFBO01BQ0ZELGtCQUFrQixDQUFDNEIsS0FBSyxDQUFDRCxJQUFJLENBQUM7SUFDaEMsQ0FBQyxDQUFDLE9BQU9ILEtBQUssRUFBRTtNQUFBO01BQUE5QixhQUFBLEdBQUFPLENBQUE7TUFDZCxPQUFPTCxZQUFZLENBQUMyQixJQUFJLENBQUM7UUFDdkJDLEtBQUssRUFBRSxxQkFBcUI7UUFDNUJLLE9BQU8sRUFBRUwsS0FBSyxZQUFZTSxLQUFLO1FBQUE7UUFBQSxDQUFBcEMsYUFBQSxHQUFBMEIsQ0FBQSxVQUFHSSxLQUFLLENBQUNPLE9BQU87UUFBQTtRQUFBLENBQUFyQyxhQUFBLEdBQUEwQixDQUFBLFVBQUcsa0JBQWtCO01BQ3RFLENBQUMsRUFBRTtRQUFFVixNQUFNLEVBQUU7TUFBSSxDQUFDLENBQUM7SUFDckI7SUFFQSxNQUFNc0IsYUFBYTtJQUFBO0lBQUEsQ0FBQXRDLGFBQUEsR0FBQU8sQ0FBQSxRQUFHLE1BQU1KLFlBQVksQ0FBQzRCLFFBQVEsRUFBRTtNQUNqRHRCLElBQUksRUFBRXdCLElBQUksQ0FBQ3hCLElBQUk7TUFDZkssTUFBTSxFQUFFbUIsSUFBSSxDQUFDbkIsTUFBTTtNQUNuQkUsTUFBTSxFQUFFaUIsSUFBSSxDQUFDakIsTUFBTTtNQUNuQkUsUUFBUSxFQUFFZSxJQUFJLENBQUNmO0lBQ2pCLENBQUMsQ0FBQztJQUFDO0lBQUFsQixhQUFBLEdBQUFPLENBQUE7SUFFSCxJQUFJLENBQUMrQixhQUFhLEVBQUU7TUFBQTtNQUFBdEMsYUFBQSxHQUFBMEIsQ0FBQTtNQUFBMUIsYUFBQSxHQUFBTyxDQUFBO01BQ2xCLE9BQU9MLFlBQVksQ0FBQzJCLElBQUksQ0FBQztRQUFFQyxLQUFLLEVBQUU7TUFBbUIsQ0FBQyxFQUFFO1FBQUVkLE1BQU0sRUFBRTtNQUFJLENBQUMsQ0FBQztJQUMxRSxDQUFDO0lBQUE7SUFBQTtNQUFBaEIsYUFBQSxHQUFBMEIsQ0FBQTtJQUFBO0lBQUExQixhQUFBLEdBQUFPLENBQUE7SUFFRCxPQUFPTCxZQUFZLENBQUMyQixJQUFJLENBQUNTLGFBQWEsQ0FBQztFQUN6QyxDQUFDLENBQUMsT0FBT1IsS0FBSyxFQUFFO0lBQUE7SUFBQTlCLGFBQUEsR0FBQU8sQ0FBQTtJQUNkZ0MsT0FBTyxDQUFDVCxLQUFLLENBQUMsd0JBQXdCLEVBQUVBLEtBQUssQ0FBQztJQUFDO0lBQUE5QixhQUFBLEdBQUFPLENBQUE7SUFDL0MsT0FBT0wsWUFBWSxDQUFDMkIsSUFBSSxDQUFDO01BQUVDLEtBQUssRUFBRTtJQUEwQixDQUFDLEVBQUU7TUFBRWQsTUFBTSxFQUFFO0lBQUksQ0FBQyxDQUFDO0VBQ2pGO0FBQ0YiLCJpZ25vcmVMaXN0IjpbXX0=