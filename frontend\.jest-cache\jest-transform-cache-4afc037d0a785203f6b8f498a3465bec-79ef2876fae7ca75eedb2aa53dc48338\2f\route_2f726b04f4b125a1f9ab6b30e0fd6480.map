{"version": 3, "names": ["cov_gmoz0mnhh", "actualCoverage", "NextResponse", "updateClient", "verifySession", "z", "updateClientSchema", "s", "object", "name", "string", "min", "max", "optional", "domain", "regex", "status", "enum", "settings", "record", "unknown", "PATCH", "request", "params", "f", "session", "b", "roles", "includes", "json", "error", "clientId", "id", "body", "parse", "details", "Error", "message", "updatedClient", "console"], "sources": ["route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { updateClient } from '../../../../lib/clients';\nimport { verifySession } from '../../../../lib/dal';\nimport { z } from 'zod';\n\n// Define schema for client updates\nconst updateClientSchema = z.object({\n  name: z.string().min(2).max(255).optional(),\n  domain: z.string().min(3).max(255).regex(\n    /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\\.[a-zA-Z]{2,})+$/,\n    'Invalid domain format'\n  ).optional(),\n  status: z.enum(['active', 'inactive', 'pending']).optional(),\n  settings: z.record(z.unknown()).optional()\n});\n\nexport async function PATCH(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  // Verify authentication and authorization\n  const session = await verifySession();\n  if (!session || !session.roles.includes('admin')) {\n    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n  }\n  \n  const clientId = params.id;\n  if (!clientId) {\n    return NextResponse.json({ error: 'Client ID is required' }, { status: 400 });\n  }\n  \n  try {\n    const body = await request.json();\n    \n    // Validate input\n    try {\n      updateClientSchema.parse(body);\n    } catch (error) {\n      return NextResponse.json({ \n        error: 'Invalid client data',\n        details: error instanceof Error ? error.message : 'Validation error'\n      }, { status: 400 });\n    }\n    \n    const updatedClient = await updateClient(clientId, {\n      name: body.name,\n      domain: body.domain,\n      status: body.status,\n      settings: body.settings\n    });\n    \n    if (!updatedClient) {\n      return NextResponse.json({ error: 'Client not found' }, { status: 404 });\n    }\n    \n    return NextResponse.json(updatedClient);\n  } catch (error) {\n    console.error('Error updating client:', error);\n    return NextResponse.json({ error: 'Failed to update client' }, { status: 500 });\n  }\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAfZ,SAAsBE,YAAY,QAAQ,aAAa;AACvD,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,CAAC,QAAQ,KAAK;;AAEvB;AACA,MAAMC,kBAAkB;AAAA;AAAA,CAAAN,aAAA,GAAAO,CAAA,OAAGF,CAAC,CAACG,MAAM,CAAC;EAClCC,IAAI,EAAEJ,CAAC,CAACK,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,GAAG,CAAC,CAACC,QAAQ,CAAC,CAAC;EAC3CC,MAAM,EAAET,CAAC,CAACK,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,GAAG,CAAC,CAACG,KAAK,CACtC,+DAA+D,EAC/D,uBACF,CAAC,CAACF,QAAQ,CAAC,CAAC;EACZG,MAAM,EAAEX,CAAC,CAACY,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC;EAC5DK,QAAQ,EAAEb,CAAC,CAACc,MAAM,CAACd,CAAC,CAACe,OAAO,CAAC,CAAC,CAAC,CAACP,QAAQ,CAAC;AAC3C,CAAC,CAAC;AAEF,OAAO,eAAeQ,KAAKA,CACzBC,OAAoB,EACpB;EAAEC;AAAmC,CAAC,EACtC;EAAA;EAAAvB,aAAA,GAAAwB,CAAA;EACA;EACA,MAAMC,OAAO;EAAA;EAAA,CAAAzB,aAAA,GAAAO,CAAA,OAAG,MAAMH,aAAa,CAAC,CAAC;EAAC;EAAAJ,aAAA,GAAAO,CAAA;EACtC;EAAI;EAAA,CAAAP,aAAA,GAAA0B,CAAA,WAACD,OAAO;EAAA;EAAA,CAAAzB,aAAA,GAAA0B,CAAA,UAAI,CAACD,OAAO,CAACE,KAAK,CAACC,QAAQ,CAAC,OAAO,CAAC,GAAE;IAAA;IAAA5B,aAAA,GAAA0B,CAAA;IAAA1B,aAAA,GAAAO,CAAA;IAChD,OAAOL,YAAY,CAAC2B,IAAI,CAAC;MAAEC,KAAK,EAAE;IAAe,CAAC,EAAE;MAAEd,MAAM,EAAE;IAAI,CAAC,CAAC;EACtE,CAAC;EAAA;EAAA;IAAAhB,aAAA,GAAA0B,CAAA;EAAA;EAED,MAAMK,QAAQ;EAAA;EAAA,CAAA/B,aAAA,GAAAO,CAAA,OAAGgB,MAAM,CAACS,EAAE;EAAC;EAAAhC,aAAA,GAAAO,CAAA;EAC3B,IAAI,CAACwB,QAAQ,EAAE;IAAA;IAAA/B,aAAA,GAAA0B,CAAA;IAAA1B,aAAA,GAAAO,CAAA;IACb,OAAOL,YAAY,CAAC2B,IAAI,CAAC;MAAEC,KAAK,EAAE;IAAwB,CAAC,EAAE;MAAEd,MAAM,EAAE;IAAI,CAAC,CAAC;EAC/E,CAAC;EAAA;EAAA;IAAAhB,aAAA,GAAA0B,CAAA;EAAA;EAAA1B,aAAA,GAAAO,CAAA;EAED,IAAI;IACF,MAAM0B,IAAI;IAAA;IAAA,CAAAjC,aAAA,GAAAO,CAAA,OAAG,MAAMe,OAAO,CAACO,IAAI,CAAC,CAAC;;IAEjC;IAAA;IAAA7B,aAAA,GAAAO,CAAA;IACA,IAAI;MAAA;MAAAP,aAAA,GAAAO,CAAA;MACFD,kBAAkB,CAAC4B,KAAK,CAACD,IAAI,CAAC;IAChC,CAAC,CAAC,OAAOH,KAAK,EAAE;MAAA;MAAA9B,aAAA,GAAAO,CAAA;MACd,OAAOL,YAAY,CAAC2B,IAAI,CAAC;QACvBC,KAAK,EAAE,qBAAqB;QAC5BK,OAAO,EAAEL,KAAK,YAAYM,KAAK;QAAA;QAAA,CAAApC,aAAA,GAAA0B,CAAA,UAAGI,KAAK,CAACO,OAAO;QAAA;QAAA,CAAArC,aAAA,GAAA0B,CAAA,UAAG,kBAAkB;MACtE,CAAC,EAAE;QAAEV,MAAM,EAAE;MAAI,CAAC,CAAC;IACrB;IAEA,MAAMsB,aAAa;IAAA;IAAA,CAAAtC,aAAA,GAAAO,CAAA,QAAG,MAAMJ,YAAY,CAAC4B,QAAQ,EAAE;MACjDtB,IAAI,EAAEwB,IAAI,CAACxB,IAAI;MACfK,MAAM,EAAEmB,IAAI,CAACnB,MAAM;MACnBE,MAAM,EAAEiB,IAAI,CAACjB,MAAM;MACnBE,QAAQ,EAAEe,IAAI,CAACf;IACjB,CAAC,CAAC;IAAC;IAAAlB,aAAA,GAAAO,CAAA;IAEH,IAAI,CAAC+B,aAAa,EAAE;MAAA;MAAAtC,aAAA,GAAA0B,CAAA;MAAA1B,aAAA,GAAAO,CAAA;MAClB,OAAOL,YAAY,CAAC2B,IAAI,CAAC;QAAEC,KAAK,EAAE;MAAmB,CAAC,EAAE;QAAEd,MAAM,EAAE;MAAI,CAAC,CAAC;IAC1E,CAAC;IAAA;IAAA;MAAAhB,aAAA,GAAA0B,CAAA;IAAA;IAAA1B,aAAA,GAAAO,CAAA;IAED,OAAOL,YAAY,CAAC2B,IAAI,CAACS,aAAa,CAAC;EACzC,CAAC,CAAC,OAAOR,KAAK,EAAE;IAAA;IAAA9B,aAAA,GAAAO,CAAA;IACdgC,OAAO,CAACT,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAAC;IAAA9B,aAAA,GAAAO,CAAA;IAC/C,OAAOL,YAAY,CAAC2B,IAAI,CAAC;MAAEC,KAAK,EAAE;IAA0B,CAAC,EAAE;MAAEd,MAAM,EAAE;IAAI,CAAC,CAAC;EACjF;AACF", "ignoreList": []}