0a5f3900358cb05fe16aa8a816ab2d28
'use client';

/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\login\\page.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_ap6xb2nes() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\login\\page.tsx";
  var hash = "35885658dc4d209327eb2a30e89684d01f025d0f";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\login\\page.tsx",
    statementMap: {
      "0": {
        start: {
          line: 9,
          column: 17
        },
        end: {
          line: 9,
          column: 28
        }
      },
      "1": {
        start: {
          line: 11,
          column: 2
        },
        end: {
          line: 46,
          column: 14
        }
      },
      "2": {
        start: {
          line: 13,
          column: 6
        },
        end: {
          line: 42,
          column: 7
        }
      },
      "3": {
        start: {
          line: 15,
          column: 26
        },
        end: {
          line: 15,
          column: 62
        }
      },
      "4": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 37,
          column: 9
        }
      },
      "5": {
        start: {
          line: 19,
          column: 10
        },
        end: {
          line: 19,
          column: 68
        }
      },
      "6": {
        start: {
          line: 20,
          column: 10
        },
        end: {
          line: 20,
          column: 35
        }
      },
      "7": {
        start: {
          line: 23,
          column: 10
        },
        end: {
          line: 32,
          column: 11
        }
      },
      "8": {
        start: {
          line: 24,
          column: 28
        },
        end: {
          line: 24,
          column: 52
        }
      },
      "9": {
        start: {
          line: 25,
          column: 12
        },
        end: {
          line: 29,
          column: 13
        }
      },
      "10": {
        start: {
          line: 26,
          column: 14
        },
        end: {
          line: 26,
          column: 76
        }
      },
      "11": {
        start: {
          line: 27,
          column: 14
        },
        end: {
          line: 27,
          column: 39
        }
      },
      "12": {
        start: {
          line: 28,
          column: 14
        },
        end: {
          line: 28,
          column: 20
        }
      },
      "13": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 31,
          column: 51
        }
      },
      "14": {
        start: {
          line: 35,
          column: 10
        },
        end: {
          line: 35,
          column: 68
        }
      },
      "15": {
        start: {
          line: 36,
          column: 10
        },
        end: {
          line: 36,
          column: 46
        }
      },
      "16": {
        start: {
          line: 39,
          column: 8
        },
        end: {
          line: 39,
          column: 52
        }
      },
      "17": {
        start: {
          line: 41,
          column: 8
        },
        end: {
          line: 41,
          column: 44
        }
      },
      "18": {
        start: {
          line: 45,
          column: 4
        },
        end: {
          line: 45,
          column: 26
        }
      },
      "19": {
        start: {
          line: 49,
          column: 2
        },
        end: {
          line: 53,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "LoginPage",
        decl: {
          start: {
            line: 8,
            column: 24
          },
          end: {
            line: 8,
            column: 33
          }
        },
        loc: {
          start: {
            line: 8,
            column: 36
          },
          end: {
            line: 54,
            column: 1
          }
        },
        line: 8
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 11,
            column: 12
          },
          end: {
            line: 11,
            column: 13
          }
        },
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 46,
            column: 3
          }
        },
        line: 11
      },
      "2": {
        name: "checkAuthAndRedirect",
        decl: {
          start: {
            line: 12,
            column: 19
          },
          end: {
            line: 12,
            column: 39
          }
        },
        loc: {
          start: {
            line: 12,
            column: 42
          },
          end: {
            line: 43,
            column: 5
          }
        },
        line: 12
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 17,
            column: 8
          },
          end: {
            line: 37,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 17,
            column: 8
          },
          end: {
            line: 37,
            column: 9
          }
        }, {
          start: {
            line: 21,
            column: 15
          },
          end: {
            line: 37,
            column: 9
          }
        }],
        line: 17
      },
      "1": {
        loc: {
          start: {
            line: 25,
            column: 12
          },
          end: {
            line: 29,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 25,
            column: 12
          },
          end: {
            line: 29,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 25
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "35885658dc4d209327eb2a30e89684d01f025d0f"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_ap6xb2nes = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_ap6xb2nes();
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getLoginUrl } from '@/lib/auth';
import { fetchAuthSession } from 'aws-amplify/auth';
export default function LoginPage() {
  /* istanbul ignore next */
  cov_ap6xb2nes().f[0]++;
  const router =
  /* istanbul ignore next */
  (cov_ap6xb2nes().s[0]++, useRouter());
  /* istanbul ignore next */
  cov_ap6xb2nes().s[1]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_ap6xb2nes().f[1]++;
    async function checkAuthAndRedirect() {
      /* istanbul ignore next */
      cov_ap6xb2nes().f[2]++;
      cov_ap6xb2nes().s[2]++;
      try {
        // Check if we have an idToken cookie
        const hasCookie =
        /* istanbul ignore next */
        (cov_ap6xb2nes().s[3]++, document.cookie.includes('idToken='));
        /* istanbul ignore next */
        cov_ap6xb2nes().s[4]++;
        if (hasCookie) {
          /* istanbul ignore next */
          cov_ap6xb2nes().b[0][0]++;
          cov_ap6xb2nes().s[5]++;
          // If we have a cookie, redirect to dashboard
          console.log('Auth cookie found, redirecting to dashboard');
          /* istanbul ignore next */
          cov_ap6xb2nes().s[6]++;
          router.push('/dashboard');
        } else {
          /* istanbul ignore next */
          cov_ap6xb2nes().b[0][1]++;
          cov_ap6xb2nes().s[7]++;
          // Try to get the session from Amplify
          try {
            const session =
            /* istanbul ignore next */
            (cov_ap6xb2nes().s[8]++, await fetchAuthSession());
            /* istanbul ignore next */
            cov_ap6xb2nes().s[9]++;
            if (session?.tokens?.idToken) {
              /* istanbul ignore next */
              cov_ap6xb2nes().b[1][0]++;
              cov_ap6xb2nes().s[10]++;
              console.log('Amplify session found, redirecting to dashboard');
              /* istanbul ignore next */
              cov_ap6xb2nes().s[11]++;
              router.push('/dashboard');
              /* istanbul ignore next */
              cov_ap6xb2nes().s[12]++;
              return;
            } else
            /* istanbul ignore next */
            {
              cov_ap6xb2nes().b[1][1]++;
            }
          } catch (error) {
            /* istanbul ignore next */
            cov_ap6xb2nes().s[13]++;
            console.log('No Amplify session found');
          }

          // No cookie or Amplify session, redirect to Cognito login
          /* istanbul ignore next */
          cov_ap6xb2nes().s[14]++;
          console.log('No auth found, redirecting to Cognito login');
          /* istanbul ignore next */
          cov_ap6xb2nes().s[15]++;
          window.location.href = getLoginUrl();
        }
      } catch (error) {
        /* istanbul ignore next */
        cov_ap6xb2nes().s[16]++;
        console.error('Error in login page:', error);
        // On error, redirect to Cognito login
        /* istanbul ignore next */
        cov_ap6xb2nes().s[17]++;
        window.location.href = getLoginUrl();
      }
    }
    /* istanbul ignore next */
    cov_ap6xb2nes().s[18]++;
    checkAuthAndRedirect();
  }, [router]);

  // Show loading spinner while redirecting
  /* istanbul ignore next */
  cov_ap6xb2nes().s[19]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex items-center justify-center min-h-screen",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 50,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 51,
      columnNumber: 7
    }
  }));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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