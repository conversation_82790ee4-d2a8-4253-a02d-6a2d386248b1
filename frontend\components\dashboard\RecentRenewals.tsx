/**
 * Recent Renewals Component
 * 
 * Displays a list of recently added renewals with proper loading and empty states.
 * Focused responsibility: Rendering recent renewals list only.
 */

'use client'

import { Renewal, BaseComponentProps } from '@/lib/types'

interface RecentRenewalsProps extends BaseComponentProps {
  renewals: Renewal[]
  isLoading?: boolean
  onRenewalClick?: (renewal: Renewal) => void
  maxItems?: number
}

interface RenewalItemProps {
  renewal: Renewal
  onClick?: (renewal: Renewal) => void
}

function RenewalItem({ renewal, onClick }: RenewalItemProps) {
  const handleClick = () => {
    onClick?.(renewal)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      handleClick()
    }
  }

  return (
    <div 
      className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={`View details for ${renewal.name}`}
    >
      <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
        <span className="text-blue-600 text-sm">📄</span>
      </div>
      <div className="flex-1">
        <p className="font-medium text-sm">{renewal.name}</p>
        <p className="text-xs text-secondary">
          Added on {renewal.created_at ? new Date(renewal.created_at).toLocaleDateString() : 'Unknown'}
        </p>
      </div>
      <div className="text-right">
        <p className="text-xs text-secondary">{renewal.vendor}</p>
        <p className="text-xs text-blue-600">View Details</p>
      </div>
    </div>
  )
}

function LoadingSkeleton() {
  return (
    <div className="space-y-3">
      {[...Array(5)].map((_, index) => (
        <div key={index} className="flex items-center space-x-3 p-3 border rounded-lg animate-pulse">
          <div className="w-8 h-8 bg-gray-200 rounded"></div>
          <div className="flex-1">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
          <div className="text-right">
            <div className="h-3 bg-gray-200 rounded w-16 mb-1"></div>
            <div className="h-3 bg-gray-200 rounded w-20"></div>
          </div>
        </div>
      ))}
    </div>
  )
}

function EmptyState() {
  return (
    <div className="text-center py-8">
      <div className="text-4xl mb-4">📄</div>
      <h3 className="text-lg font-medium mb-2">No recent renewals</h3>
      <p className="text-secondary">
        Recent renewals will appear here once you start adding them.
      </p>
    </div>
  )
}

export default function RecentRenewals({
  renewals,
  isLoading = false,
  onRenewalClick,
  maxItems = 5,
  className = '',
  'data-testid': testId
}: RecentRenewalsProps) {
  const displayRenewals = renewals.slice(0, maxItems)

  return (
    <div 
      className={`card ${className}`}
      data-testid={testId}
    >
      <div className="card-header">
        <h2 className="text-lg font-semibold">Recently Added Renewals</h2>
        {renewals.length > maxItems && (
          <span className="text-sm text-secondary">
            Showing {maxItems} of {renewals.length}
          </span>
        )}
      </div>
      <div className="card-content">
        {isLoading ? (
          <LoadingSkeleton />
        ) : displayRenewals.length > 0 ? (
          <div className="space-y-3">
            {displayRenewals.map((renewal) => (
              <RenewalItem
                key={renewal.id}
                renewal={renewal}
                onClick={onRenewalClick}
              />
            ))}
          </div>
        ) : (
          <EmptyState />
        )}
      </div>
    </div>
  )
}
