8ad1575ac354a2098b81987f84e7d143
/* istanbul ignore next */
function cov_2c8mebnvkg() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\amplify-config.ts";
  var hash = "399d252e0eb9de4ddbba3eea73668974dd29a146";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\amplify-config.ts",
    statementMap: {
      "0": {
        start: {
          line: 7,
          column: 2
        },
        end: {
          line: 10,
          column: 3
        }
      },
      "1": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 8,
          column: 59
        }
      },
      "2": {
        start: {
          line: 9,
          column: 4
        },
        end: {
          line: 9,
          column: 11
        }
      },
      "3": {
        start: {
          line: 13,
          column: 24
        },
        end: {
          line: 13,
          column: 36
        }
      },
      "4": {
        start: {
          line: 14,
          column: 17
        },
        end: {
          line: 21,
          column: 3
        }
      },
      "5": {
        start: {
          line: 23,
          column: 2
        },
        end: {
          line: 30,
          column: 5
        }
      },
      "6": {
        start: {
          line: 32,
          column: 2
        },
        end: {
          line: 56,
          column: 3
        }
      },
      "7": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 51,
          column: 7
        }
      },
      "8": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 52,
          column: 92
        }
      },
      "9": {
        start: {
          line: 54,
          column: 4
        },
        end: {
          line: 54,
          column: 55
        }
      },
      "10": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 55,
          column: 16
        }
      },
      "11": {
        start: {
          line: 62,
          column: 2
        },
        end: {
          line: 64,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "configureAmplify",
        decl: {
          start: {
            line: 5,
            column: 16
          },
          end: {
            line: 5,
            column: 32
          }
        },
        loc: {
          start: {
            line: 5,
            column: 35
          },
          end: {
            line: 57,
            column: 1
          }
        },
        line: 5
      },
      "1": {
        name: "getAmplifySSR",
        decl: {
          start: {
            line: 60,
            column: 16
          },
          end: {
            line: 60,
            column: 29
          }
        },
        loc: {
          start: {
            line: 60,
            column: 49
          },
          end: {
            line: 65,
            column: 1
          }
        },
        line: 60
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 7,
            column: 2
          },
          end: {
            line: 10,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 7,
            column: 2
          },
          end: {
            line: 10,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 7
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {
      "0": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "399d252e0eb9de4ddbba3eea73668974dd29a146"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2c8mebnvkg = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2c8mebnvkg();
import { Amplify } from 'aws-amplify';
import { clientConfig } from './client-config';

// Amplify configuration using centralized configuration
export function configureAmplify() {
  /* istanbul ignore next */
  cov_2c8mebnvkg().f[0]++;
  cov_2c8mebnvkg().s[0]++;
  // Only configure once
  if (Amplify.getConfig().Auth) {
    /* istanbul ignore next */
    cov_2c8mebnvkg().b[0][0]++;
    cov_2c8mebnvkg().s[1]++;
    console.log('Amplify already configured, skipping...');
    /* istanbul ignore next */
    cov_2c8mebnvkg().s[2]++;
    return;
  } else
  /* istanbul ignore next */
  {
    cov_2c8mebnvkg().b[0][1]++;
  }

  // Get configuration from centralized config
  const {
    aws,
    auth
  } =
  /* istanbul ignore next */
  (cov_2c8mebnvkg().s[3]++, clientConfig);
  const config =
  /* istanbul ignore next */
  (cov_2c8mebnvkg().s[4]++, {
    region: aws.region,
    userPoolId: aws.userPoolId,
    userPoolClientId: aws.userPoolClientId,
    cognitoDomain: aws.cognitoDomain,
    redirectSignIn: auth.redirectSignIn,
    redirectSignOut: auth.redirectSignOut
  });
  /* istanbul ignore next */
  cov_2c8mebnvkg().s[5]++;
  console.log('Configuring Amplify with:', {
    region: config.region,
    userPoolId: config.userPoolId,
    userPoolClientId: config.userPoolClientId,
    cognitoDomain: config.cognitoDomain,
    redirectSignIn: config.redirectSignIn,
    redirectSignOut: config.redirectSignOut
  });
  /* istanbul ignore next */
  cov_2c8mebnvkg().s[6]++;
  try {
    /* istanbul ignore next */
    cov_2c8mebnvkg().s[7]++;
    Amplify.configure({
      Auth: {
        Cognito: {
          userPoolId: config.userPoolId,
          userPoolClientId: config.userPoolClientId,
          loginWith: {
            oauth: {
              domain: config.cognitoDomain,
              scopes: ["email", "profile", "openid", "aws.cognito.signin.user.admin"],
              redirectSignIn: [config.redirectSignIn],
              redirectSignOut: [config.redirectSignOut],
              responseType: "code"
            }
          }
        }
      }
    }, {
      ssr: true
    });
    /* istanbul ignore next */
    cov_2c8mebnvkg().s[8]++;
    console.log('Amplify configured successfully with OAuth domain:', config.cognitoDomain);
  } catch (error) {
    /* istanbul ignore next */
    cov_2c8mebnvkg().s[9]++;
    console.error('Error configuring Amplify:', error);
    /* istanbul ignore next */
    cov_2c8mebnvkg().s[10]++;
    throw error;
  }
}

// Export configured instance for server-side operations
export function getAmplifySSR(_request) {
  /* istanbul ignore next */
  cov_2c8mebnvkg().f[1]++;
  cov_2c8mebnvkg().s[11]++;
  // For Amplify v6, we need to create a new instance with SSR context
  return {
    Auth: Amplify.getConfig().Auth
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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