{"version": 3, "names": ["cov_28yqnqlnl", "actualCoverage", "clientConfig", "s", "aws", "region", "process", "env", "NEXT_PUBLIC_AWS_REGION", "userPoolId", "NEXT_PUBLIC_AWS_USER_POOLS_ID", "userPoolClientId", "NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID", "cognitoDomain", "NEXT_PUBLIC_AWS_COGNITO_DOMAIN", "auth", "redirectSignIn", "NEXT_PUBLIC_REDIRECT_SIGN_IN", "redirectSignOut", "NEXT_PUBLIC_REDIRECT_SIGN_OUT", "app", "environment", "b", "NODE_ENV", "isDevelopment", "isProduction", "validateClientConfig", "f", "requiredVars", "missing", "filter", "key", "length", "console", "error", "Error", "join", "getAwsConfig", "getAuthConfig", "getAppConfig"], "sources": ["client-config.ts"], "sourcesContent": ["/**\n * Client-side configuration\n * This file provides environment variables that are safe to expose to the client\n */\n\n// These values are injected at build time by Next.js\nexport const clientConfig = {\n  aws: {\n    region: process.env.NEXT_PUBLIC_AWS_REGION!,\n    userPoolId: process.env.NEXT_PUBLIC_AWS_USER_POOLS_ID!,\n    userPoolClientId: process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID!,\n    cognitoDomain: process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN!,\n  },\n  auth: {\n    redirectSignIn: process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN!,\n    redirectSignOut: process.env.NEXT_PUBLIC_REDIRECT_SIGN_OUT!,\n  },\n  app: {\n    environment: process.env.NODE_ENV || 'development',\n    isDevelopment: process.env.NODE_ENV === 'development',\n    isProduction: process.env.NODE_ENV === 'production',\n  },\n} as const;\n\n// Validation function for client config\nexport const validateClientConfig = () => {\n  const requiredVars = [\n    'NEXT_PUBLIC_AWS_REGION',\n    'NEXT_PUBLIC_AWS_USER_POOLS_ID', \n    'NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID',\n    'NEXT_PUBLIC_AWS_COGNITO_DOMAIN',\n    'NEXT_PUBLIC_REDIRECT_SIGN_IN',\n    'NEXT_PUBLIC_REDIRECT_SIGN_OUT',\n  ];\n\n  const missing = requiredVars.filter(key => !process.env[key]);\n  \n  if (missing.length > 0) {\n    console.error('Missing required environment variables:', missing);\n    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);\n  }\n\n  return true;\n};\n\n// Export individual getters for convenience\nexport const getAwsConfig = () => clientConfig.aws;\nexport const getAuthConfig = () => clientConfig.auth;\nexport const getAppConfig = () => clientConfig.app;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAfZ;AACA;AACA;AACA;;AAEA;AACA,OAAO,MAAME,YAAY;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAG;EAC1BC,GAAG,EAAE;IACHC,MAAM,EAAEC,OAAO,CAACC,GAAG,CAACC,sBAAuB;IAC3CC,UAAU,EAAEH,OAAO,CAACC,GAAG,CAACG,6BAA8B;IACtDC,gBAAgB,EAAEL,OAAO,CAACC,GAAG,CAACK,wCAAyC;IACvEC,aAAa,EAAEP,OAAO,CAACC,GAAG,CAACO;EAC7B,CAAC;EACDC,IAAI,EAAE;IACJC,cAAc,EAAEV,OAAO,CAACC,GAAG,CAACU,4BAA6B;IACzDC,eAAe,EAAEZ,OAAO,CAACC,GAAG,CAACY;EAC/B,CAAC;EACDC,GAAG,EAAE;IACHC,WAAW;IAAE;IAAA,CAAArB,aAAA,GAAAsB,CAAA,UAAAhB,OAAO,CAACC,GAAG,CAACgB,QAAQ;IAAA;IAAA,CAAAvB,aAAA,GAAAsB,CAAA,UAAI,aAAa;IAClDE,aAAa,EAAElB,OAAO,CAACC,GAAG,CAACgB,QAAQ,KAAK,aAAa;IACrDE,YAAY,EAAEnB,OAAO,CAACC,GAAG,CAACgB,QAAQ,KAAK;EACzC;AACF,CAAC,CAAS;;AAEV;AAAA;AAAAvB,aAAA,GAAAG,CAAA;AACA,OAAO,MAAMuB,oBAAoB,GAAGA,CAAA,KAAM;EAAA;EAAA1B,aAAA,GAAA2B,CAAA;EACxC,MAAMC,YAAY;EAAA;EAAA,CAAA5B,aAAA,GAAAG,CAAA,OAAG,CACnB,wBAAwB,EACxB,+BAA+B,EAC/B,0CAA0C,EAC1C,gCAAgC,EAChC,8BAA8B,EAC9B,+BAA+B,CAChC;EAED,MAAM0B,OAAO;EAAA;EAAA,CAAA7B,aAAA,GAAAG,CAAA,OAAGyB,YAAY,CAACE,MAAM,CAACC,GAAG,IAAI;IAAA;IAAA/B,aAAA,GAAA2B,CAAA;IAAA3B,aAAA,GAAAG,CAAA;IAAA,QAACG,OAAO,CAACC,GAAG,CAACwB,GAAG,CAAC;EAAD,CAAC,CAAC;EAAC;EAAA/B,aAAA,GAAAG,CAAA;EAE9D,IAAI0B,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;IAAA;IAAAhC,aAAA,GAAAsB,CAAA;IAAAtB,aAAA,GAAAG,CAAA;IACtB8B,OAAO,CAACC,KAAK,CAAC,yCAAyC,EAAEL,OAAO,CAAC;IAAC;IAAA7B,aAAA,GAAAG,CAAA;IAClE,MAAM,IAAIgC,KAAK,CAAC,2CAA2CN,OAAO,CAACO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EAClF,CAAC;EAAA;EAAA;IAAApC,aAAA,GAAAsB,CAAA;EAAA;EAAAtB,aAAA,GAAAG,CAAA;EAED,OAAO,IAAI;AACb,CAAC;;AAED;AAAA;AAAAH,aAAA,GAAAG,CAAA;AACA,OAAO,MAAMkC,YAAY,GAAGA,CAAA,KAAM;EAAA;EAAArC,aAAA,GAAA2B,CAAA;EAAA3B,aAAA,GAAAG,CAAA;EAAA,OAAAD,YAAY,CAACE,GAAG;AAAD,CAAC;AAAC;AAAAJ,aAAA,GAAAG,CAAA;AACnD,OAAO,MAAMmC,aAAa,GAAGA,CAAA,KAAM;EAAA;EAAAtC,aAAA,GAAA2B,CAAA;EAAA3B,aAAA,GAAAG,CAAA;EAAA,OAAAD,YAAY,CAACa,IAAI;AAAD,CAAC;AAAC;AAAAf,aAAA,GAAAG,CAAA;AACrD,OAAO,MAAMoC,YAAY,GAAGA,CAAA,KAAM;EAAA;EAAAvC,aAAA,GAAA2B,CAAA;EAAA3B,aAAA,GAAAG,CAAA;EAAA,OAAAD,YAAY,CAACkB,GAAG;AAAD,CAAC", "ignoreList": []}