{"version": 3, "names": ["cov_jg567abuj", "actualCoverage", "verifySession", "createUnauthorizedResponse", "createForbiddenResponse", "requireAuth", "f", "s", "session", "b", "success", "response", "error", "console", "requireRole", "requiredRoles", "authResult", "roles", "Array", "isArray", "userRoles", "hasRequiredRole", "some", "role", "includes", "join", "requireAdmin", "hasAnyRole", "hasAllRoles", "every", "getUserId", "userId", "getUserEmail", "email", "isAuthenticated", "isAuth", "<PERSON><PERSON><PERSON>", "handler", "args", "with<PERSON><PERSON>", "with<PERSON><PERSON><PERSON>", "rateLimitMap", "Map", "checkRateLimit", "identifier", "maxRequests", "windowMs", "now", "Date", "windowStart", "key", "value", "entries", "resetTime", "delete", "current", "get", "set", "count", "allowed", "remaining", "getRateLimitIdentifier", "request", "forwarded", "headers", "realIp", "ip", "split"], "sources": ["auth-middleware.ts"], "sourcesContent": ["/**\n * Authentication Middleware\n * \n * This module provides reusable authentication middleware for API routes.\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { verifySession } from './dal';\nimport { createUnauthorizedResponse, createForbiddenResponse, ApiResponse } from './api-response';\n\n// Session interface\nexport interface AuthSession {\n  isAuth: boolean;\n  userId: string;\n  email: string;\n  roles: string[];\n}\n\n// Authentication result\nexport interface AuthResult {\n  success: boolean;\n  session?: AuthSession;\n  response?: NextResponse<ApiResponse>;\n}\n\n/**\n * Verify authentication for API routes\n */\nexport async function requireAuth(): Promise<AuthResult> {\n  try {\n    const session = await verifySession();\n    \n    if (!session) {\n      return {\n        success: false,\n        response: createUnauthorizedResponse('Authentication required')\n      };\n    }\n\n    return {\n      success: true,\n      session\n    };\n  } catch (error) {\n    console.error('Authentication verification failed:', error);\n    return {\n      success: false,\n      response: createUnauthorizedResponse('Authentication verification failed')\n    };\n  }\n}\n\n/**\n * Verify authentication and check for specific roles\n */\nexport async function requireRole(requiredRoles: string | string[]): Promise<AuthResult> {\n  const authResult = await requireAuth();\n  \n  if (!authResult.success) {\n    return authResult;\n  }\n\n  const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];\n  const userRoles = authResult.session!.roles;\n  \n  const hasRequiredRole = roles.some(role => userRoles.includes(role));\n  \n  if (!hasRequiredRole) {\n    return {\n      success: false,\n      response: createForbiddenResponse(`Required role(s): ${roles.join(', ')}`)\n    };\n  }\n\n  return authResult;\n}\n\n/**\n * Verify authentication for admin operations\n */\nexport async function requireAdmin(): Promise<AuthResult> {\n  return requireRole(['admin', 'super_admin']);\n}\n\n/**\n * Check if user has any of the specified roles\n */\nexport function hasAnyRole(userRoles: string[], requiredRoles: string[]): boolean {\n  return requiredRoles.some(role => userRoles.includes(role));\n}\n\n/**\n * Check if user has all of the specified roles\n */\nexport function hasAllRoles(userRoles: string[], requiredRoles: string[]): boolean {\n  return requiredRoles.every(role => userRoles.includes(role));\n}\n\n/**\n * Extract user ID from session\n */\nexport function getUserId(session: AuthSession): string {\n  return session.userId;\n}\n\n/**\n * Extract user email from session\n */\nexport function getUserEmail(session: AuthSession): string {\n  return session.email;\n}\n\n/**\n * Check if user is authenticated\n */\nexport function isAuthenticated(session: AuthSession | null): session is AuthSession {\n  return session !== null && session.isAuth;\n}\n\n/**\n * Higher-order function to wrap API handlers with authentication\n */\nexport function withAuth<T extends any[], R>(\n  handler: (session: AuthSession, ...args: T) => Promise<R>\n) {\n  return async (...args: T): Promise<R | NextResponse<ApiResponse>> => {\n    const authResult = await requireAuth();\n\n    if (!authResult.success) {\n      return authResult.response!;\n    }\n\n    return handler(authResult.session!, ...args);\n  };\n}\n\n/**\n * Higher-order function to wrap API handlers with role-based authentication\n */\nexport function withRole<T extends any[], R>(\n  requiredRoles: string | string[],\n  handler: (session: AuthSession, ...args: T) => Promise<R>\n) {\n  return async (...args: T): Promise<R | NextResponse<ApiResponse>> => {\n    const authResult = await requireRole(requiredRoles);\n\n    if (!authResult.success) {\n      return authResult.response!;\n    }\n\n    return handler(authResult.session!, ...args);\n  };\n}\n\n/**\n * Higher-order function to wrap API handlers with admin authentication\n */\nexport function withAdmin<T extends any[], R>(\n  handler: (session: AuthSession, ...args: T) => Promise<R>\n) {\n  return withRole(['admin', 'super_admin'], handler);\n}\n\n/**\n * Rate limiting helper (basic implementation)\n */\nconst rateLimitMap = new Map<string, { count: number; resetTime: number }>();\n\nexport function checkRateLimit(\n  identifier: string,\n  maxRequests: number = 100,\n  windowMs: number = 60000\n): { allowed: boolean; remaining: number } {\n  const now = Date.now();\n  const windowStart = now - windowMs;\n  \n  // Clean up old entries\n  for (const [key, value] of rateLimitMap.entries()) {\n    if (value.resetTime < windowStart) {\n      rateLimitMap.delete(key);\n    }\n  }\n  \n  const current = rateLimitMap.get(identifier);\n  \n  if (!current || current.resetTime < windowStart) {\n    // First request in window or window expired\n    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });\n    return { allowed: true, remaining: maxRequests - 1 };\n  }\n  \n  if (current.count >= maxRequests) {\n    return { allowed: false, remaining: 0 };\n  }\n  \n  current.count++;\n  return { allowed: true, remaining: maxRequests - current.count };\n}\n\n/**\n * Get rate limit identifier from request\n */\nexport function getRateLimitIdentifier(request: NextRequest, session?: AuthSession): string {\n  // Use user ID if authenticated, otherwise use IP\n  if (session) {\n    return `user:${session.userId}`;\n  }\n  \n  // Get IP from various headers\n  const forwarded = request.headers.get('x-forwarded-for');\n  const realIp = request.headers.get('x-real-ip');\n  const ip = forwarded?.split(',')[0] || realIp || 'unknown';\n  \n  return `ip:${ip}`;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAfZ;AACA;AACA;AACA;AACA;;AAGA,SAASE,aAAa,QAAQ,OAAO;AACrC,SAASC,0BAA0B,EAAEC,uBAAuB,QAAqB,gBAAgB;;AAEjG;;AAQA;;AAOA;AACA;AACA;AACA,OAAO,eAAeC,WAAWA,CAAA,EAAwB;EAAA;EAAAL,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAO,CAAA;EACvD,IAAI;IACF,MAAMC,OAAO;IAAA;IAAA,CAAAR,aAAA,GAAAO,CAAA,OAAG,MAAML,aAAa,CAAC,CAAC;IAAC;IAAAF,aAAA,GAAAO,CAAA;IAEtC,IAAI,CAACC,OAAO,EAAE;MAAA;MAAAR,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAO,CAAA;MACZ,OAAO;QACLG,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAER,0BAA0B,CAAC,yBAAyB;MAChE,CAAC;IACH,CAAC;IAAA;IAAA;MAAAH,aAAA,GAAAS,CAAA;IAAA;IAAAT,aAAA,GAAAO,CAAA;IAED,OAAO;MACLG,OAAO,EAAE,IAAI;MACbF;IACF,CAAC;EACH,CAAC,CAAC,OAAOI,KAAK,EAAE;IAAA;IAAAZ,aAAA,GAAAO,CAAA;IACdM,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAAC;IAAAZ,aAAA,GAAAO,CAAA;IAC5D,OAAO;MACLG,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAER,0BAA0B,CAAC,oCAAoC;IAC3E,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA,OAAO,eAAeW,WAAWA,CAACC,aAAgC,EAAuB;EAAA;EAAAf,aAAA,GAAAM,CAAA;EACvF,MAAMU,UAAU;EAAA;EAAA,CAAAhB,aAAA,GAAAO,CAAA,OAAG,MAAMF,WAAW,CAAC,CAAC;EAAC;EAAAL,aAAA,GAAAO,CAAA;EAEvC,IAAI,CAACS,UAAU,CAACN,OAAO,EAAE;IAAA;IAAAV,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAO,CAAA;IACvB,OAAOS,UAAU;EACnB,CAAC;EAAA;EAAA;IAAAhB,aAAA,GAAAS,CAAA;EAAA;EAED,MAAMQ,KAAK;EAAA;EAAA,CAAAjB,aAAA,GAAAO,CAAA,QAAGW,KAAK,CAACC,OAAO,CAACJ,aAAa,CAAC;EAAA;EAAA,CAAAf,aAAA,GAAAS,CAAA,UAAGM,aAAa;EAAA;EAAA,CAAAf,aAAA,GAAAS,CAAA,UAAG,CAACM,aAAa,CAAC;EAC5E,MAAMK,SAAS;EAAA;EAAA,CAAApB,aAAA,GAAAO,CAAA,QAAGS,UAAU,CAACR,OAAO,CAAES,KAAK;EAE3C,MAAMI,eAAe;EAAA;EAAA,CAAArB,aAAA,GAAAO,CAAA,QAAGU,KAAK,CAACK,IAAI,CAACC,IAAI,IAAI;IAAA;IAAAvB,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAO,CAAA;IAAA,OAAAa,SAAS,CAACI,QAAQ,CAACD,IAAI,CAAC;EAAD,CAAC,CAAC;EAAC;EAAAvB,aAAA,GAAAO,CAAA;EAErE,IAAI,CAACc,eAAe,EAAE;IAAA;IAAArB,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAO,CAAA;IACpB,OAAO;MACLG,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAEP,uBAAuB,CAAC,qBAAqBa,KAAK,CAACQ,IAAI,CAAC,IAAI,CAAC,EAAE;IAC3E,CAAC;EACH,CAAC;EAAA;EAAA;IAAAzB,aAAA,GAAAS,CAAA;EAAA;EAAAT,aAAA,GAAAO,CAAA;EAED,OAAOS,UAAU;AACnB;;AAEA;AACA;AACA;AACA,OAAO,eAAeU,YAAYA,CAAA,EAAwB;EAAA;EAAA1B,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAO,CAAA;EACxD,OAAOO,WAAW,CAAC,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;AAC9C;;AAEA;AACA;AACA;AACA,OAAO,SAASa,UAAUA,CAACP,SAAmB,EAAEL,aAAuB,EAAW;EAAA;EAAAf,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAO,CAAA;EAChF,OAAOQ,aAAa,CAACO,IAAI,CAACC,IAAI,IAAI;IAAA;IAAAvB,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAO,CAAA;IAAA,OAAAa,SAAS,CAACI,QAAQ,CAACD,IAAI,CAAC;EAAD,CAAC,CAAC;AAC7D;;AAEA;AACA;AACA;AACA,OAAO,SAASK,WAAWA,CAACR,SAAmB,EAAEL,aAAuB,EAAW;EAAA;EAAAf,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAO,CAAA;EACjF,OAAOQ,aAAa,CAACc,KAAK,CAACN,IAAI,IAAI;IAAA;IAAAvB,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAO,CAAA;IAAA,OAAAa,SAAS,CAACI,QAAQ,CAACD,IAAI,CAAC;EAAD,CAAC,CAAC;AAC9D;;AAEA;AACA;AACA;AACA,OAAO,SAASO,SAASA,CAACtB,OAAoB,EAAU;EAAA;EAAAR,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAO,CAAA;EACtD,OAAOC,OAAO,CAACuB,MAAM;AACvB;;AAEA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACxB,OAAoB,EAAU;EAAA;EAAAR,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAO,CAAA;EACzD,OAAOC,OAAO,CAACyB,KAAK;AACtB;;AAEA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAAC1B,OAA2B,EAA0B;EAAA;EAAAR,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAO,CAAA;EACnF,OAAO,2BAAAP,aAAA,GAAAS,CAAA,UAAAD,OAAO,KAAK,IAAI;EAAA;EAAA,CAAAR,aAAA,GAAAS,CAAA,UAAID,OAAO,CAAC2B,MAAM;AAC3C;;AAEA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CACtBC,OAAyD,EACzD;EAAA;EAAArC,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAO,CAAA;EACA,OAAO,OAAO,GAAG+B,IAAO,KAA6C;IAAA;IAAAtC,aAAA,GAAAM,CAAA;IACnE,MAAMU,UAAU;IAAA;IAAA,CAAAhB,aAAA,GAAAO,CAAA,QAAG,MAAMF,WAAW,CAAC,CAAC;IAAC;IAAAL,aAAA,GAAAO,CAAA;IAEvC,IAAI,CAACS,UAAU,CAACN,OAAO,EAAE;MAAA;MAAAV,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAO,CAAA;MACvB,OAAOS,UAAU,CAACL,QAAQ;IAC5B,CAAC;IAAA;IAAA;MAAAX,aAAA,GAAAS,CAAA;IAAA;IAAAT,aAAA,GAAAO,CAAA;IAED,OAAO8B,OAAO,CAACrB,UAAU,CAACR,OAAO,EAAG,GAAG8B,IAAI,CAAC;EAC9C,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CACtBxB,aAAgC,EAChCsB,OAAyD,EACzD;EAAA;EAAArC,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAO,CAAA;EACA,OAAO,OAAO,GAAG+B,IAAO,KAA6C;IAAA;IAAAtC,aAAA,GAAAM,CAAA;IACnE,MAAMU,UAAU;IAAA;IAAA,CAAAhB,aAAA,GAAAO,CAAA,QAAG,MAAMO,WAAW,CAACC,aAAa,CAAC;IAAC;IAAAf,aAAA,GAAAO,CAAA;IAEpD,IAAI,CAACS,UAAU,CAACN,OAAO,EAAE;MAAA;MAAAV,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAO,CAAA;MACvB,OAAOS,UAAU,CAACL,QAAQ;IAC5B,CAAC;IAAA;IAAA;MAAAX,aAAA,GAAAS,CAAA;IAAA;IAAAT,aAAA,GAAAO,CAAA;IAED,OAAO8B,OAAO,CAACrB,UAAU,CAACR,OAAO,EAAG,GAAG8B,IAAI,CAAC;EAC9C,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASE,SAASA,CACvBH,OAAyD,EACzD;EAAA;EAAArC,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAO,CAAA;EACA,OAAOgC,QAAQ,CAAC,CAAC,OAAO,EAAE,aAAa,CAAC,EAAEF,OAAO,CAAC;AACpD;;AAEA;AACA;AACA;AACA,MAAMI,YAAY;AAAA;AAAA,CAAAzC,aAAA,GAAAO,CAAA,QAAG,IAAImC,GAAG,CAA+C,CAAC;AAE5E,OAAO,SAASC,cAAcA,CAC5BC,UAAkB,EAClBC,WAAmB;AAAA;AAAA,CAAA7C,aAAA,GAAAS,CAAA,UAAG,GAAG,GACzBqC,QAAgB;AAAA;AAAA,CAAA9C,aAAA,GAAAS,CAAA,UAAG,KAAK,GACiB;EAAA;EAAAT,aAAA,GAAAM,CAAA;EACzC,MAAMyC,GAAG;EAAA;EAAA,CAAA/C,aAAA,GAAAO,CAAA,QAAGyC,IAAI,CAACD,GAAG,CAAC,CAAC;EACtB,MAAME,WAAW;EAAA;EAAA,CAAAjD,aAAA,GAAAO,CAAA,QAAGwC,GAAG,GAAGD,QAAQ;;EAElC;EAAA;EAAA9C,aAAA,GAAAO,CAAA;EACA,KAAK,MAAM,CAAC2C,GAAG,EAAEC,KAAK,CAAC,IAAIV,YAAY,CAACW,OAAO,CAAC,CAAC,EAAE;IAAA;IAAApD,aAAA,GAAAO,CAAA;IACjD,IAAI4C,KAAK,CAACE,SAAS,GAAGJ,WAAW,EAAE;MAAA;MAAAjD,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAO,CAAA;MACjCkC,YAAY,CAACa,MAAM,CAACJ,GAAG,CAAC;IAC1B,CAAC;IAAA;IAAA;MAAAlD,aAAA,GAAAS,CAAA;IAAA;EACH;EAEA,MAAM8C,OAAO;EAAA;EAAA,CAAAvD,aAAA,GAAAO,CAAA,QAAGkC,YAAY,CAACe,GAAG,CAACZ,UAAU,CAAC;EAAC;EAAA5C,aAAA,GAAAO,CAAA;EAE7C;EAAI;EAAA,CAAAP,aAAA,GAAAS,CAAA,YAAC8C,OAAO;EAAA;EAAA,CAAAvD,aAAA,GAAAS,CAAA,WAAI8C,OAAO,CAACF,SAAS,GAAGJ,WAAW,GAAE;IAAA;IAAAjD,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAO,CAAA;IAC/C;IACAkC,YAAY,CAACgB,GAAG,CAACb,UAAU,EAAE;MAAEc,KAAK,EAAE,CAAC;MAAEL,SAAS,EAAEN,GAAG,GAAGD;IAAS,CAAC,CAAC;IAAC;IAAA9C,aAAA,GAAAO,CAAA;IACtE,OAAO;MAAEoD,OAAO,EAAE,IAAI;MAAEC,SAAS,EAAEf,WAAW,GAAG;IAAE,CAAC;EACtD,CAAC;EAAA;EAAA;IAAA7C,aAAA,GAAAS,CAAA;EAAA;EAAAT,aAAA,GAAAO,CAAA;EAED,IAAIgD,OAAO,CAACG,KAAK,IAAIb,WAAW,EAAE;IAAA;IAAA7C,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAO,CAAA;IAChC,OAAO;MAAEoD,OAAO,EAAE,KAAK;MAAEC,SAAS,EAAE;IAAE,CAAC;EACzC,CAAC;EAAA;EAAA;IAAA5D,aAAA,GAAAS,CAAA;EAAA;EAAAT,aAAA,GAAAO,CAAA;EAEDgD,OAAO,CAACG,KAAK,EAAE;EAAC;EAAA1D,aAAA,GAAAO,CAAA;EAChB,OAAO;IAAEoD,OAAO,EAAE,IAAI;IAAEC,SAAS,EAAEf,WAAW,GAAGU,OAAO,CAACG;EAAM,CAAC;AAClE;;AAEA;AACA;AACA;AACA,OAAO,SAASG,sBAAsBA,CAACC,OAAoB,EAAEtD,OAAqB,EAAU;EAAA;EAAAR,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAO,CAAA;EAC1F;EACA,IAAIC,OAAO,EAAE;IAAA;IAAAR,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAO,CAAA;IACX,OAAO,QAAQC,OAAO,CAACuB,MAAM,EAAE;EACjC,CAAC;EAAA;EAAA;IAAA/B,aAAA,GAAAS,CAAA;EAAA;;EAED;EACA,MAAMsD,SAAS;EAAA;EAAA,CAAA/D,aAAA,GAAAO,CAAA,QAAGuD,OAAO,CAACE,OAAO,CAACR,GAAG,CAAC,iBAAiB,CAAC;EACxD,MAAMS,MAAM;EAAA;EAAA,CAAAjE,aAAA,GAAAO,CAAA,QAAGuD,OAAO,CAACE,OAAO,CAACR,GAAG,CAAC,WAAW,CAAC;EAC/C,MAAMU,EAAE;EAAA;EAAA,CAAAlE,aAAA,GAAAO,CAAA;EAAG;EAAA,CAAAP,aAAA,GAAAS,CAAA,WAAAsD,SAAS,EAAEI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAAA;EAAA,CAAAnE,aAAA,GAAAS,CAAA,WAAIwD,MAAM;EAAA;EAAA,CAAAjE,aAAA,GAAAS,CAAA,WAAI,SAAS;EAAC;EAAAT,aAAA,GAAAO,CAAA;EAE3D,OAAO,MAAM2D,EAAE,EAAE;AACnB", "ignoreList": []}