import { NextResponse } from 'next/server';
import { verifySession } from '@/lib/dal';
import { getDbPool } from '@/lib/db-config';
import { getClientByEmailDomain } from '@/lib/clients';

export async function GET() {
  // Verify authentication
  const session = await verifySession();
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Get tenant context using the new secure method
    const clientResult = await getClientByEmailDomain(session.email);

    if (!clientResult.success) {
      const statusCode = clientResult.errorCode === 'NOT_FOUND' ? 404 : 500;
      return NextResponse.json(
        { error: clientResult.error },
        { status: statusCode }
      );
    }

    const tenant = clientResult.client!;
    const pool = await getDbPool();

    // Query tenant schema for actual renewal data
    try {
      const renewalsQuery = `
        SELECT
          "RenewalID" as id,
          "RenewalName" as name,
          "VendorName" as vendor,
          "Status" as status,
          "DueDate" as due_date,
          "CreatedOn" as created_at
        FROM "${tenant.tenantSchema}"."Renewals"
        WHERE "Active" = true
        ORDER BY "CreatedOn" DESC
        LIMIT 10
      `;

      const result = await pool.query(renewalsQuery);

      // Format the results
      const renewals = result.rows.map(row => ({
        id: row.id.toString(),
        name: row.name,
        vendor: row.vendor,
        status: row.status,
        dueDate: row.due_date ? new Date(row.due_date).toLocaleDateString() : 'Not set',
        addedDate: row.created_at ? new Date(row.created_at).toLocaleDateString() : 'Unknown'
      }));

      return NextResponse.json(renewals);

    } catch (schemaError) {
      console.log(`Tenant schema ${tenant.tenantSchema} not ready yet, using mock data`);

      // Mock data fallback
      const renewals = [
        { id: '1', name: 'Unlimited Renewal', vendor: 'Vox Audio', status: 'Not available', dueDate: 'Run Now 2025', addedDate: '2025-01-01' },
        { id: '2', name: 'Core Pro', vendor: 'Vox Audio', status: 'Not available', dueDate: 'Run Now 2025', addedDate: '2025-01-02' },
        { id: '3', name: 'Test Software', vendor: 'Vox Tools', status: 'Not available', dueDate: 'Run Now 2025', addedDate: '2025-01-03' },
        { id: '4', name: 'Mobile app', vendor: 'TopCar', status: 'Not available', dueDate: 'Run Now 2025', addedDate: '2025-01-04' },
        { id: '5', name: 'ASAUDB', vendor: 'Cubix', status: 'Not available', dueDate: 'Run Now 2025', addedDate: '2025-01-05' }
      ];

      return NextResponse.json(renewals);
    }

  } catch (error) {
    console.error('Error fetching dashboard renewals:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
