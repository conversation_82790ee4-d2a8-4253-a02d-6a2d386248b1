import { NextRequest, NextResponse } from 'next/server';
import { verifySession } from '@/lib/dal';
import { getDbPool } from '@/lib/db-config';

export async function GET(request: NextRequest) {
  // Verify authentication
  const session = await verifySession();
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const pool = await getDbPool();

    // Get client ID from user's email domain
    const userEmail = session.email || session.userId; // Fallback to userId if email not available
    let clientId = null;

    if (userEmail && userEmail.includes('@')) {
      const domain = userEmail.split('@')[1];
      const clientQuery = `
        SELECT client_id
        FROM tenant_management.clients
        WHERE domain = $1 AND status = 'active'
        LIMIT 1
      `;
      const clientResult = await pool.query(clientQuery, [domain]);
      if (clientResult.rows.length > 0) {
        clientId = clientResult.rows[0].client_id;
      }
    }

    // If no client found, return placeholder data
    if (!clientId) {
      const renewals = [
        {
          id: '1',
          name: 'Unlimited Renewal',
          vendor: 'Vox Audio',
          status: 'Not available',
          dueDate: 'Run Now 2025'
        },
        {
          id: '2',
          name: 'Core Pro',
          vendor: 'Vox Audio',
          status: 'Not available',
          dueDate: 'Run Now 2025'
        },
        {
          id: '3',
          name: 'Test Software',
          vendor: 'Vox Tools',
          status: 'Not available',
          dueDate: 'Run Now 2025'
        },
        {
          id: '4',
          name: 'Mobile app',
          vendor: 'TopCar',
          status: 'Not available',
          dueDate: 'Run Now 2025'
        },
        {
          id: '5',
          name: 'ASAUDB',
          vendor: 'Cubix',
          status: 'Not available',
          dueDate: 'Run Now 2025'
        }
      ];
      return NextResponse.json(renewals);
    }

    // Get recent renewals from database
    const renewalsQuery = `
      SELECT
        renewal_id as id,
        name,
        vendor,
        status,
        renewal_date,
        cost,
        currency,
        created_at
      FROM tenant_management.renewals
      WHERE client_id = $1 AND status = 'active'
      ORDER BY created_at DESC
      LIMIT 5
    `;

    const result = await pool.query(renewalsQuery, [clientId]);

    const renewals = result.rows.map(row => ({
      id: row.id,
      name: row.name,
      vendor: row.vendor,
      status: row.status,
      dueDate: new Date(row.renewal_date).toLocaleDateString(),
      cost: row.cost ? `${row.currency || 'USD'} ${parseFloat(row.cost).toLocaleString()}` : 'N/A',
      addedDate: new Date(row.created_at).toLocaleDateString()
    }));

    return NextResponse.json(renewals);
  } catch (error) {
    console.error('Error fetching recent renewals:', error);
    // Return fallback data on error
    const renewals = [
      {
        id: '1',
        name: 'Unlimited Renewal',
        vendor: 'Vox Audio',
        status: 'Not available',
        dueDate: 'Run Now 2025'
      },
      {
        id: '2',
        name: 'Core Pro',
        vendor: 'Vox Audio',
        status: 'Not available',
        dueDate: 'Run Now 2025'
      },
      {
        id: '3',
        name: 'Test Software',
        vendor: 'Vox Tools',
        status: 'Not available',
        dueDate: 'Run Now 2025'
      },
      {
        id: '4',
        name: 'Mobile app',
        vendor: 'TopCar',
        status: 'Not available',
        dueDate: 'Run Now 2025'
      },
      {
        id: '5',
        name: 'ASAUDB',
        vendor: 'Cubix',
        status: 'Not available',
        dueDate: 'Run Now 2025'
      }
    ];
    return NextResponse.json(renewals);
  }
}
