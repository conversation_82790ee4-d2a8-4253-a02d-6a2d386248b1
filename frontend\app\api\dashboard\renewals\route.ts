import { getClientByEmailDomain } from '@/lib/clients';
import { executeQuery, schemaExists } from '@/lib/database';
import { requireAuth } from '@/lib/auth-middleware';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api-response';

// Renewal interface
interface Renewal {
  id: string;
  name: string;
  vendor: string;
  status: string;
  dueDate: string;
  addedDate: string;
}

export const GET = withErrorHandling(async () => {
  // Verify authentication
  const authResult = await requireAuth();
  if (!authResult.success) {
    return authResult.response!;
  }

  const session = authResult.session!;

  // Get tenant context using the new secure method
  const clientResult = await getClientByEmailDomain(session.email);

  if (!clientResult.success) {
    const statusCode = clientResult.errorCode === 'NOT_FOUND' ? HttpStatus.NOT_FOUND : HttpStatus.INTERNAL_SERVER_ERROR;
    const apiErrorCode = clientResult.errorCode === 'NOT_FOUND' ? ApiErrorCode.NOT_FOUND : ApiErrorCode.DATABASE_ERROR;

    return createErrorResponse(
      clientResult.error || 'Failed to fetch tenant information',
      apiErrorCode,
      statusCode
    );
  }

  const tenant = clientResult.client!;

  // Check if tenant schema exists
  const schemaReady = await schemaExists(tenant.tenantSchema);

  let renewals: Renewal[] = [];

  if (schemaReady) {
    // Query tenant schema for actual renewal data using correct column names
    const renewalsQuery = `
      SELECT
        "RenewalID" as id,
        "RenewalName" as name,
        "VendorName" as vendor,
        "Status" as status,
        "RenewalDate" as due_date,
        "CreatedOn" as created_at
      FROM "${tenant.tenantSchema}"."Renewals"
      WHERE "Active" = true
      ORDER BY "CreatedOn" DESC
      LIMIT 10
    `;

    const result = await executeQuery(renewalsQuery, [], { schema: tenant.tenantSchema });

    if (result.success && result.data) {
      // Format the results
      renewals = result.data.map((row: any) => ({
        id: row.id.toString(),
        name: row.name,
        vendor: row.vendor,
        status: row.status,
        dueDate: row.due_date ? new Date(row.due_date).toLocaleDateString() : 'Not set',
        addedDate: row.created_at ? new Date(row.created_at).toLocaleDateString() : 'Unknown'
      }));
    }
  } else {
    console.log(`Tenant schema ${tenant.tenantSchema} not ready yet, using mock data`);
  }

  // Use mock data if no real data available
  if (renewals.length === 0) {
    renewals = [
      { id: '1', name: 'Unlimited Renewal', vendor: 'Vox Audio', status: 'Not available', dueDate: 'Run Now 2025', addedDate: '2025-01-01' },
      { id: '2', name: 'Core Pro', vendor: 'Vox Audio', status: 'Not available', dueDate: 'Run Now 2025', addedDate: '2025-01-02' },
      { id: '3', name: 'Test Software', vendor: 'Vox Tools', status: 'Not available', dueDate: 'Run Now 2025', addedDate: '2025-01-03' },
      { id: '4', name: 'Mobile app', vendor: 'TopCar', status: 'Not available', dueDate: 'Run Now 2025', addedDate: '2025-01-04' },
      { id: '5', name: 'ASAUDB', vendor: 'Cubix', status: 'Not available', dueDate: 'Run Now 2025', addedDate: '2025-01-05' }
    ];
  }

  return createSuccessResponse(renewals, 'Renewals retrieved successfully');
});
