/**
 * Dashboard Header Component
 * 
 * Contains the dashboard title, subtitle, search functionality, and action buttons.
 * Focused responsibility: Header section with search and actions.
 */

'use client'

import { useState } from 'react'
import { BaseComponentProps } from '@/lib/types'

interface DashboardHeaderProps extends BaseComponentProps {
  clientName?: string
  onSearch?: (query: string) => void
  onAddRenewal?: () => void
  searchPlaceholder?: string
}

export default function DashboardHeader({
  clientName = 'Unknown Client',
  onSearch,
  onAddRenewal,
  searchPlaceholder = 'Search renewals...',
  className = '',
  'data-testid': testId
}: DashboardHeaderProps) {
  const [searchQuery, setSearchQuery] = useState('')

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setSearchQuery(query)
    onSearch?.(query)
  }

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch?.(searchQuery)
  }

  const handleAddRenewal = () => {
    onAddRenewal?.()
  }

  return (
    <div 
      className={`dashboard-header ${className}`}
      data-testid={testId}
    >
      <div className="dashboard-title-section">
        <h1 className="dashboard-title">
          Dashboard - {clientName}
        </h1>
        <p className="dashboard-subtitle">
          Manage your subscriptions, maintenance, support and warranties
        </p>
      </div>
      
      <div className="dashboard-actions">
        <form onSubmit={handleSearchSubmit} className="search-container">
          <input
            type="text"
            placeholder={searchPlaceholder}
            className="search-input"
            value={searchQuery}
            onChange={handleSearchChange}
            aria-label="Search renewals"
          />
          <button 
            type="submit"
            className="search-icon"
            aria-label="Submit search"
          >
            🔍
          </button>
        </form>
        
        <button 
          className="btn btn-primary"
          onClick={handleAddRenewal}
          aria-label="Add new renewal"
        >
          + Add Renewal
        </button>
      </div>
    </div>
  )
}
