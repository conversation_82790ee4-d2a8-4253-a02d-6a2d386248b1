{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "jwtVerify", "jest", "fn", "createRemoteJWKSet", "_globals", "require", "_jwtValidator", "_testUtils", "describe", "beforeEach", "clearAllMocks", "mockJWKS", "mockJwtVerify", "jose", "mockReturnValue", "it", "mockPayload", "sub", "email", "aud", "iss", "exp", "Math", "floor", "Date", "now", "mockResolvedValue", "payload", "<PERSON><PERSON><PERSON><PERSON>", "alg", "result", "validateJwtToken", "expect", "toEqual", "toHaveBeenCalledWith", "objectContaining", "issuer", "audience", "mockRejectedValue", "Error", "rejects", "toThrow", "mockRequest", "mockResponse", "headers", "cookies", "status", "mockReturnThis", "json", "authorization", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "token", "startsWith", "substring", "toBe", "idToken", "cookieToken", "toBeUndefined", "mockUser", "testUtils", "generateTestData", "user", "session", "isAuthenticated", "expiresAt", "toBeInstanceOf", "expiredSession", "isExpired", "authError", "code", "message", "refreshError", "validTokenFormat", "toMatch", "not", "id", "roles", "permissions", "hasPermission", "permission", "includes", "tenantId", "requestedTenantId", "hasAccess"], "sources": ["auth.test.ts"], "sourcesContent": ["/**\n * Authentication Library Tests\n * \n * Tests for the authentication utilities and JWT validation\n */\n\nimport { jest } from '@jest/globals'\nimport { validateJwtToken } from '@/lib/jwt-validator'\nimport { testUtils } from '../utils/test-utils'\n\n// Mock jose library\njest.mock('jose', () => ({\n  jwtVerify: jest.fn(),\n  createRemoteJWKSet: jest.fn(),\n}))\n\ndescribe('Authentication Library', () => {\n  beforeEach(() => {\n    jest.clearAllMocks()\n  })\n\n  describe('JWT Validation', () => {\n    const mockJWKS = jest.fn()\n    const mockJwtVerify = jest.fn()\n\n    beforeEach(() => {\n      const jose = require('jose')\n      jose.createRemoteJWKSet.mockReturnValue(mockJWKS)\n      jose.jwtVerify = mockJwtVerify\n    })\n\n    it('should validate a valid JWT token', async () => {\n      const mockPayload = {\n        sub: 'test-user-id',\n        email: '<EMAIL>',\n        aud: 'test-client-id',\n        iss: 'https://cognito-idp.ca-central-1.amazonaws.com/ca-central-1_test',\n        exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now\n      }\n\n      mockJwtVerify.mockResolvedValue({\n        payload: mockPayload,\n        protectedHeader: { alg: 'RS256' },\n      })\n\n      const result = await validateJwtToken(\n        'valid-jwt-token',\n        'ca-central-1_test',\n        'test-client-id'\n      )\n\n      expect(result).toEqual(mockPayload)\n      expect(mockJwtVerify).toHaveBeenCalledWith(\n        'valid-jwt-token',\n        mockJWKS,\n        expect.objectContaining({\n          issuer: 'https://cognito-idp.ca-central-1.amazonaws.com/ca-central-1_test',\n          audience: 'test-client-id',\n        })\n      )\n    })\n\n    it('should reject an expired JWT token', async () => {\n      mockJwtVerify.mockRejectedValue(new Error('JWT expired'))\n\n      await expect(\n        validateJwtToken(\n          'expired-jwt-token',\n          'ca-central-1_test',\n          'test-client-id'\n        )\n      ).rejects.toThrow('JWT expired')\n    })\n\n    it('should reject a JWT token with invalid audience', async () => {\n      mockJwtVerify.mockRejectedValue(new Error('Invalid audience'))\n\n      await expect(\n        validateJwtToken(\n          'invalid-audience-token',\n          'ca-central-1_test',\n          'wrong-client-id'\n        )\n      ).rejects.toThrow('Invalid audience')\n    })\n\n    it('should reject a JWT token with invalid issuer', async () => {\n      mockJwtVerify.mockRejectedValue(new Error('Invalid issuer'))\n\n      await expect(\n        validateJwtToken(\n          'invalid-issuer-token',\n          'wrong-pool-id',\n          'test-client-id'\n        )\n      ).rejects.toThrow('Invalid issuer')\n    })\n\n    it('should handle malformed JWT tokens', async () => {\n      mockJwtVerify.mockRejectedValue(new Error('Malformed token'))\n\n      await expect(\n        validateJwtToken(\n          'malformed.token',\n          'ca-central-1_test',\n          'test-client-id'\n        )\n      ).rejects.toThrow('Malformed token')\n    })\n\n    it('should handle network errors when fetching JWKS', async () => {\n      mockJwtVerify.mockRejectedValue(new Error('Network error'))\n\n      await expect(\n        validateJwtToken(\n          'valid-token',\n          'ca-central-1_test',\n          'test-client-id'\n        )\n      ).rejects.toThrow('Network error')\n    })\n  })\n\n  describe('Authentication Middleware', () => {\n    let mockRequest: any\n    let mockResponse: any\n\n    beforeEach(() => {\n      mockRequest = {\n        headers: {},\n        cookies: {},\n      }\n      mockResponse = {\n        status: jest.fn().mockReturnThis(),\n        json: jest.fn().mockReturnThis(),\n      }\n    })\n\n    it('should extract token from Authorization header', () => {\n      mockRequest.headers.authorization = 'Bearer valid-token'\n      \n      // Test token extraction logic\n      const authHeader = mockRequest.headers.authorization\n      const token = authHeader?.startsWith('Bearer ') \n        ? authHeader.substring(7) \n        : null\n\n      expect(token).toBe('valid-token')\n    })\n\n    it('should extract token from cookies', () => {\n      mockRequest.cookies.idToken = 'cookie-token'\n      \n      const token = mockRequest.cookies.idToken\n      expect(token).toBe('cookie-token')\n    })\n\n    it('should handle missing authentication', () => {\n      // No token in headers or cookies\n      const authHeader = mockRequest.headers.authorization\n      const cookieToken = mockRequest.cookies.idToken\n      \n      expect(authHeader).toBeUndefined()\n      expect(cookieToken).toBeUndefined()\n    })\n  })\n\n  describe('Session Management', () => {\n    it('should create a valid session object', () => {\n      const mockUser = testUtils.generateTestData.user()\n      const session = {\n        user: mockUser,\n        isAuthenticated: true,\n        expiresAt: new Date(Date.now() + 3600000), // 1 hour from now\n      }\n\n      expect(session.user).toEqual(mockUser)\n      expect(session.isAuthenticated).toBe(true)\n      expect(session.expiresAt).toBeInstanceOf(Date)\n    })\n\n    it('should handle session expiration', () => {\n      const expiredSession = {\n        user: null,\n        isAuthenticated: false,\n        expiresAt: new Date(Date.now() - 3600000), // 1 hour ago\n      }\n\n      const isExpired = expiredSession.expiresAt < new Date()\n      expect(isExpired).toBe(true)\n      expect(expiredSession.isAuthenticated).toBe(false)\n    })\n  })\n\n  describe('Error Handling', () => {\n    it('should handle authentication errors gracefully', () => {\n      const authError = {\n        code: 'UNAUTHORIZED',\n        message: 'Invalid credentials',\n        status: 401,\n      }\n\n      expect(authError.code).toBe('UNAUTHORIZED')\n      expect(authError.status).toBe(401)\n    })\n\n    it('should handle token refresh errors', () => {\n      const refreshError = {\n        code: 'TOKEN_REFRESH_FAILED',\n        message: 'Unable to refresh token',\n        status: 401,\n      }\n\n      expect(refreshError.code).toBe('TOKEN_REFRESH_FAILED')\n      expect(refreshError.status).toBe(401)\n    })\n  })\n\n  describe('Security Validations', () => {\n    it('should validate token format', () => {\n      const validTokenFormat = /^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+$/\n      \n      expect('valid.jwt.token').toMatch(validTokenFormat)\n      expect('invalid-token').not.toMatch(validTokenFormat)\n      expect('').not.toMatch(validTokenFormat)\n    })\n\n    it('should validate user permissions', () => {\n      const user = {\n        id: 'test-user',\n        roles: ['user', 'admin'],\n        permissions: ['read', 'write', 'delete'],\n      }\n\n      const hasPermission = (permission: string) => \n        user.permissions.includes(permission)\n\n      expect(hasPermission('read')).toBe(true)\n      expect(hasPermission('write')).toBe(true)\n      expect(hasPermission('admin')).toBe(false)\n    })\n\n    it('should validate tenant access', () => {\n      const user = {\n        id: 'test-user',\n        tenantId: 'tenant-123',\n      }\n\n      const requestedTenantId = 'tenant-123'\n      const hasAccess = user.tenantId === requestedTenantId\n\n      expect(hasAccess).toBe(true)\n    })\n  })\n})\n"], "mappings": ";;AAUA;AACAA,WAAA,GAAKC,IAAI,CAAC,MAAM,EAAE,OAAO;EACvBC,SAAS,EAAEC,aAAI,CAACC,EAAE,CAAC,CAAC;EACpBC,kBAAkB,EAAEF,aAAI,CAACC,EAAE,CAAC;AAC9B,CAAC,CAAC,CAAC;AARH,IAAAE,QAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AAA+C,SAAAP,YAAA;EAAA;IAAAG;EAAA,IAAAI,OAAA;EAAAP,WAAA,GAAAA,CAAA,KAAAG,IAAA;EAAA,OAAAA,IAAA;AAAA;AAR/C;AACA;AACA;AACA;AACA;AAYAO,QAAQ,CAAC,wBAAwB,EAAE,MAAM;EACvCC,UAAU,CAAC,MAAM;IACfR,aAAI,CAACS,aAAa,CAAC,CAAC;EACtB,CAAC,CAAC;EAEFF,QAAQ,CAAC,gBAAgB,EAAE,MAAM;IAC/B,MAAMG,QAAQ,GAAGV,aAAI,CAACC,EAAE,CAAC,CAAC;IAC1B,MAAMU,aAAa,GAAGX,aAAI,CAACC,EAAE,CAAC,CAAC;IAE/BO,UAAU,CAAC,MAAM;MACf,MAAMI,IAAI,GAAGR,OAAO,CAAC,MAAM,CAAC;MAC5BQ,IAAI,CAACV,kBAAkB,CAACW,eAAe,CAACH,QAAQ,CAAC;MACjDE,IAAI,CAACb,SAAS,GAAGY,aAAa;IAChC,CAAC,CAAC;IAEFG,EAAE,CAAC,mCAAmC,EAAE,YAAY;MAClD,MAAMC,WAAW,GAAG;QAClBC,GAAG,EAAE,cAAc;QACnBC,KAAK,EAAE,kBAAkB;QACzBC,GAAG,EAAE,gBAAgB;QACrBC,GAAG,EAAE,kEAAkE;QACvEC,GAAG,EAAEC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAE;MAC7C,CAAC;MAEDb,aAAa,CAACc,iBAAiB,CAAC;QAC9BC,OAAO,EAAEX,WAAW;QACpBY,eAAe,EAAE;UAAEC,GAAG,EAAE;QAAQ;MAClC,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAG,MAAM,IAAAC,8BAAgB,EACnC,iBAAiB,EACjB,mBAAmB,EACnB,gBACF,CAAC;MAEDC,MAAM,CAACF,MAAM,CAAC,CAACG,OAAO,CAACjB,WAAW,CAAC;MACnCgB,MAAM,CAACpB,aAAa,CAAC,CAACsB,oBAAoB,CACxC,iBAAiB,EACjBvB,QAAQ,EACRqB,MAAM,CAACG,gBAAgB,CAAC;QACtBC,MAAM,EAAE,kEAAkE;QAC1EC,QAAQ,EAAE;MACZ,CAAC,CACH,CAAC;IACH,CAAC,CAAC;IAEFtB,EAAE,CAAC,oCAAoC,EAAE,YAAY;MACnDH,aAAa,CAAC0B,iBAAiB,CAAC,IAAIC,KAAK,CAAC,aAAa,CAAC,CAAC;MAEzD,MAAMP,MAAM,CACV,IAAAD,8BAAgB,EACd,mBAAmB,EACnB,mBAAmB,EACnB,gBACF,CACF,CAAC,CAACS,OAAO,CAACC,OAAO,CAAC,aAAa,CAAC;IAClC,CAAC,CAAC;IAEF1B,EAAE,CAAC,iDAAiD,EAAE,YAAY;MAChEH,aAAa,CAAC0B,iBAAiB,CAAC,IAAIC,KAAK,CAAC,kBAAkB,CAAC,CAAC;MAE9D,MAAMP,MAAM,CACV,IAAAD,8BAAgB,EACd,wBAAwB,EACxB,mBAAmB,EACnB,iBACF,CACF,CAAC,CAACS,OAAO,CAACC,OAAO,CAAC,kBAAkB,CAAC;IACvC,CAAC,CAAC;IAEF1B,EAAE,CAAC,+CAA+C,EAAE,YAAY;MAC9DH,aAAa,CAAC0B,iBAAiB,CAAC,IAAIC,KAAK,CAAC,gBAAgB,CAAC,CAAC;MAE5D,MAAMP,MAAM,CACV,IAAAD,8BAAgB,EACd,sBAAsB,EACtB,eAAe,EACf,gBACF,CACF,CAAC,CAACS,OAAO,CAACC,OAAO,CAAC,gBAAgB,CAAC;IACrC,CAAC,CAAC;IAEF1B,EAAE,CAAC,oCAAoC,EAAE,YAAY;MACnDH,aAAa,CAAC0B,iBAAiB,CAAC,IAAIC,KAAK,CAAC,iBAAiB,CAAC,CAAC;MAE7D,MAAMP,MAAM,CACV,IAAAD,8BAAgB,EACd,iBAAiB,EACjB,mBAAmB,EACnB,gBACF,CACF,CAAC,CAACS,OAAO,CAACC,OAAO,CAAC,iBAAiB,CAAC;IACtC,CAAC,CAAC;IAEF1B,EAAE,CAAC,iDAAiD,EAAE,YAAY;MAChEH,aAAa,CAAC0B,iBAAiB,CAAC,IAAIC,KAAK,CAAC,eAAe,CAAC,CAAC;MAE3D,MAAMP,MAAM,CACV,IAAAD,8BAAgB,EACd,aAAa,EACb,mBAAmB,EACnB,gBACF,CACF,CAAC,CAACS,OAAO,CAACC,OAAO,CAAC,eAAe,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjC,QAAQ,CAAC,2BAA2B,EAAE,MAAM;IAC1C,IAAIkC,WAAgB;IACpB,IAAIC,YAAiB;IAErBlC,UAAU,CAAC,MAAM;MACfiC,WAAW,GAAG;QACZE,OAAO,EAAE,CAAC,CAAC;QACXC,OAAO,EAAE,CAAC;MACZ,CAAC;MACDF,YAAY,GAAG;QACbG,MAAM,EAAE7C,aAAI,CAACC,EAAE,CAAC,CAAC,CAAC6C,cAAc,CAAC,CAAC;QAClCC,IAAI,EAAE/C,aAAI,CAACC,EAAE,CAAC,CAAC,CAAC6C,cAAc,CAAC;MACjC,CAAC;IACH,CAAC,CAAC;IAEFhC,EAAE,CAAC,gDAAgD,EAAE,MAAM;MACzD2B,WAAW,CAACE,OAAO,CAACK,aAAa,GAAG,oBAAoB;;MAExD;MACA,MAAMC,UAAU,GAAGR,WAAW,CAACE,OAAO,CAACK,aAAa;MACpD,MAAME,KAAK,GAAGD,UAAU,EAAEE,UAAU,CAAC,SAAS,CAAC,GAC3CF,UAAU,CAACG,SAAS,CAAC,CAAC,CAAC,GACvB,IAAI;MAERrB,MAAM,CAACmB,KAAK,CAAC,CAACG,IAAI,CAAC,aAAa,CAAC;IACnC,CAAC,CAAC;IAEFvC,EAAE,CAAC,mCAAmC,EAAE,MAAM;MAC5C2B,WAAW,CAACG,OAAO,CAACU,OAAO,GAAG,cAAc;MAE5C,MAAMJ,KAAK,GAAGT,WAAW,CAACG,OAAO,CAACU,OAAO;MACzCvB,MAAM,CAACmB,KAAK,CAAC,CAACG,IAAI,CAAC,cAAc,CAAC;IACpC,CAAC,CAAC;IAEFvC,EAAE,CAAC,sCAAsC,EAAE,MAAM;MAC/C;MACA,MAAMmC,UAAU,GAAGR,WAAW,CAACE,OAAO,CAACK,aAAa;MACpD,MAAMO,WAAW,GAAGd,WAAW,CAACG,OAAO,CAACU,OAAO;MAE/CvB,MAAM,CAACkB,UAAU,CAAC,CAACO,aAAa,CAAC,CAAC;MAClCzB,MAAM,CAACwB,WAAW,CAAC,CAACC,aAAa,CAAC,CAAC;IACrC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjD,QAAQ,CAAC,oBAAoB,EAAE,MAAM;IACnCO,EAAE,CAAC,sCAAsC,EAAE,MAAM;MAC/C,MAAM2C,QAAQ,GAAGC,oBAAS,CAACC,gBAAgB,CAACC,IAAI,CAAC,CAAC;MAClD,MAAMC,OAAO,GAAG;QACdD,IAAI,EAAEH,QAAQ;QACdK,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,IAAIxC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAE;MAC7C,CAAC;MAEDO,MAAM,CAAC8B,OAAO,CAACD,IAAI,CAAC,CAAC5B,OAAO,CAACyB,QAAQ,CAAC;MACtC1B,MAAM,CAAC8B,OAAO,CAACC,eAAe,CAAC,CAACT,IAAI,CAAC,IAAI,CAAC;MAC1CtB,MAAM,CAAC8B,OAAO,CAACE,SAAS,CAAC,CAACC,cAAc,CAACzC,IAAI,CAAC;IAChD,CAAC,CAAC;IAEFT,EAAE,CAAC,kCAAkC,EAAE,MAAM;MAC3C,MAAMmD,cAAc,GAAG;QACrBL,IAAI,EAAE,IAAI;QACVE,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,IAAIxC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAE;MAC7C,CAAC;MAED,MAAM0C,SAAS,GAAGD,cAAc,CAACF,SAAS,GAAG,IAAIxC,IAAI,CAAC,CAAC;MACvDQ,MAAM,CAACmC,SAAS,CAAC,CAACb,IAAI,CAAC,IAAI,CAAC;MAC5BtB,MAAM,CAACkC,cAAc,CAACH,eAAe,CAAC,CAACT,IAAI,CAAC,KAAK,CAAC;IACpD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF9C,QAAQ,CAAC,gBAAgB,EAAE,MAAM;IAC/BO,EAAE,CAAC,gDAAgD,EAAE,MAAM;MACzD,MAAMqD,SAAS,GAAG;QAChBC,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE,qBAAqB;QAC9BxB,MAAM,EAAE;MACV,CAAC;MAEDd,MAAM,CAACoC,SAAS,CAACC,IAAI,CAAC,CAACf,IAAI,CAAC,cAAc,CAAC;MAC3CtB,MAAM,CAACoC,SAAS,CAACtB,MAAM,CAAC,CAACQ,IAAI,CAAC,GAAG,CAAC;IACpC,CAAC,CAAC;IAEFvC,EAAE,CAAC,oCAAoC,EAAE,MAAM;MAC7C,MAAMwD,YAAY,GAAG;QACnBF,IAAI,EAAE,sBAAsB;QAC5BC,OAAO,EAAE,yBAAyB;QAClCxB,MAAM,EAAE;MACV,CAAC;MAEDd,MAAM,CAACuC,YAAY,CAACF,IAAI,CAAC,CAACf,IAAI,CAAC,sBAAsB,CAAC;MACtDtB,MAAM,CAACuC,YAAY,CAACzB,MAAM,CAAC,CAACQ,IAAI,CAAC,GAAG,CAAC;IACvC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF9C,QAAQ,CAAC,sBAAsB,EAAE,MAAM;IACrCO,EAAE,CAAC,8BAA8B,EAAE,MAAM;MACvC,MAAMyD,gBAAgB,GAAG,kDAAkD;MAE3ExC,MAAM,CAAC,iBAAiB,CAAC,CAACyC,OAAO,CAACD,gBAAgB,CAAC;MACnDxC,MAAM,CAAC,eAAe,CAAC,CAAC0C,GAAG,CAACD,OAAO,CAACD,gBAAgB,CAAC;MACrDxC,MAAM,CAAC,EAAE,CAAC,CAAC0C,GAAG,CAACD,OAAO,CAACD,gBAAgB,CAAC;IAC1C,CAAC,CAAC;IAEFzD,EAAE,CAAC,kCAAkC,EAAE,MAAM;MAC3C,MAAM8C,IAAI,GAAG;QACXc,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;QACxBC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ;MACzC,CAAC;MAED,MAAMC,aAAa,GAAIC,UAAkB,IACvClB,IAAI,CAACgB,WAAW,CAACG,QAAQ,CAACD,UAAU,CAAC;MAEvC/C,MAAM,CAAC8C,aAAa,CAAC,MAAM,CAAC,CAAC,CAACxB,IAAI,CAAC,IAAI,CAAC;MACxCtB,MAAM,CAAC8C,aAAa,CAAC,OAAO,CAAC,CAAC,CAACxB,IAAI,CAAC,IAAI,CAAC;MACzCtB,MAAM,CAAC8C,aAAa,CAAC,OAAO,CAAC,CAAC,CAACxB,IAAI,CAAC,KAAK,CAAC;IAC5C,CAAC,CAAC;IAEFvC,EAAE,CAAC,+BAA+B,EAAE,MAAM;MACxC,MAAM8C,IAAI,GAAG;QACXc,EAAE,EAAE,WAAW;QACfM,QAAQ,EAAE;MACZ,CAAC;MAED,MAAMC,iBAAiB,GAAG,YAAY;MACtC,MAAMC,SAAS,GAAGtB,IAAI,CAACoB,QAAQ,KAAKC,iBAAiB;MAErDlD,MAAM,CAACmD,SAAS,CAAC,CAAC7B,IAAI,CAAC,IAAI,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}