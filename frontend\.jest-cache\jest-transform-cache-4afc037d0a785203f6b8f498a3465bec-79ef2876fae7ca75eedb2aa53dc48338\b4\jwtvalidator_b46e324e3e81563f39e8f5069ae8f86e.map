{"version": 3, "names": ["_jose", "require", "_config", "cov_2k8i4ud2ua", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "_coverageSchema", "coverage", "actualCoverage", "jwksCache", "getJwksUrl", "region", "userPoolId", "publicConfig", "aws", "getJwks", "jwksUrl", "createRemoteJWKSet", "URL", "validateJwtToken", "token", "jwks", "userPoolClientId", "<PERSON><PERSON><PERSON><PERSON>", "payload", "jwtVerify", "issuer", "audience", "cognitoPayload", "token_use", "console", "warn", "email", "email_verified", "error", "validate<PERSON><PERSON><PERSON><PERSON><PERSON>", "cookieValue", "trim", "includes", "split", "length", "parseJwtUnsafe", "app", "isProduction", "Error", "parts", "JSON", "parse", "<PERSON><PERSON><PERSON>", "from", "toString", "currentTime", "Math", "floor", "Date", "now", "exp", "isTokenExpired", "getTokenExpirationTime", "isTokenExpiringWithin", "minutes", "expirationTime", "warningTime"], "sources": ["jwt-validator.ts"], "sourcesContent": ["/**\n * Secure JWT Token Validation\n * \n * This module provides secure JWT token validation with proper signature verification\n * using AWS Cognito's JWKS (JSON Web Key Set).\n */\n\nimport { jwtVerify, createRemoteJWKSet, JWTPayload } from 'jose';\nimport { publicConfig } from './config';\n\n// Cognito JWT payload interface\nexport interface CognitoJwtPayload extends JWTPayload {\n  sub: string;\n  email: string;\n  email_verified?: boolean;\n  name?: string;\n  given_name?: string;\n  family_name?: string;\n  'cognito:groups'?: string[];\n  'cognito:username'?: string;\n  token_use: 'id' | 'access';\n  auth_time: number;\n  iat: number;\n  exp: number;\n  aud: string;\n  iss: string;\n}\n\n// Cache for JWKS\nlet jwksCache: ReturnType<typeof createRemoteJWKSet> | null = null;\n\n/**\n * Get JWKS URL for the Cognito User Pool\n */\nfunction getJwksUrl(): string {\n  const { region, userPoolId } = publicConfig.aws;\n  return `https://cognito-idp.${region}.amazonaws.com/${userPoolId}/.well-known/jwks.json`;\n}\n\n/**\n * Get or create JWKS instance\n */\nfunction getJwks() {\n  if (!jwksCache) {\n    const jwksUrl = getJwksUrl();\n    jwksCache = createRemoteJWKSet(new URL(jwksUrl));\n  }\n  return jwksCache;\n}\n\n/**\n * Validate JWT token with proper signature verification\n */\nexport async function validateJwtToken(token: string): Promise<CognitoJwtPayload | null> {\n  try {\n    const jwks = getJwks();\n    const { region, userPoolId, userPoolClientId } = publicConfig.aws;\n    \n    // Expected issuer\n    const expectedIssuer = `https://cognito-idp.${region}.amazonaws.com/${userPoolId}`;\n    \n    // Verify the JWT\n    const { payload } = await jwtVerify(token, jwks, {\n      issuer: expectedIssuer,\n      audience: userPoolClientId,\n    });\n\n    // Additional validation\n    const cognitoPayload = payload as CognitoJwtPayload;\n    \n    // Validate token type (should be 'id' for ID tokens)\n    if (cognitoPayload.token_use !== 'id') {\n      console.warn('Invalid token type:', cognitoPayload.token_use);\n      return null;\n    }\n\n    // Validate email is present and verified (if available)\n    if (!cognitoPayload.email) {\n      console.warn('Token missing email claim');\n      return null;\n    }\n\n    // Check if email is verified (optional but recommended)\n    if (cognitoPayload.email_verified === false) {\n      console.warn('Email not verified for user:', cognitoPayload.email);\n      // You might want to reject unverified emails in production\n    }\n\n    return cognitoPayload;\n  } catch (error) {\n    console.error('JWT validation failed:', error);\n    return null;\n  }\n}\n\n/**\n * Extract and validate JWT from cookie string\n */\nexport async function validateAuthCookie(cookieValue: string): Promise<CognitoJwtPayload | null> {\n  if (!cookieValue) {\n    return null;\n  }\n\n  // Remove any potential cookie prefix/suffix\n  const token = cookieValue.trim();\n  \n  // Basic JWT format check\n  if (!token.includes('.') || token.split('.').length !== 3) {\n    console.warn('Invalid JWT format in cookie');\n    return null;\n  }\n\n  return validateJwtToken(token);\n}\n\n/**\n * Fallback JWT parsing (INSECURE - only for development/debugging)\n * This should NOT be used in production\n */\nexport function parseJwtUnsafe(token: string): CognitoJwtPayload | null {\n  if (publicConfig.app.isProduction) {\n    throw new Error('Unsafe JWT parsing is not allowed in production');\n  }\n\n  try {\n    const parts = token.split('.');\n    if (parts.length !== 3) {\n      return null;\n    }\n\n    const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());\n    \n    // Check expiration\n    const currentTime = Math.floor(Date.now() / 1000);\n    if (payload.exp && payload.exp < currentTime) {\n      console.warn('Token has expired');\n      return null;\n    }\n\n    return payload as CognitoJwtPayload;\n  } catch (error) {\n    console.error('Failed to parse JWT:', error);\n    return null;\n  }\n}\n\n/**\n * Validate token expiration\n */\nexport function isTokenExpired(payload: CognitoJwtPayload): boolean {\n  const currentTime = Math.floor(Date.now() / 1000);\n  return payload.exp < currentTime;\n}\n\n/**\n * Get token expiration time in milliseconds\n */\nexport function getTokenExpirationTime(payload: CognitoJwtPayload): number {\n  return payload.exp * 1000;\n}\n\n/**\n * Check if token expires within the specified minutes\n */\nexport function isTokenExpiringWithin(payload: CognitoJwtPayload, minutes: number): boolean {\n  const expirationTime = getTokenExpirationTime(payload);\n  const warningTime = Date.now() + (minutes * 60 * 1000);\n  return expirationTime <= warningTime;\n}\n"], "mappings": ";;;;;;;;;;;;AAOA;AAAA;AAAAA,KAAA,GAAAC,OAAA;AACA;AAAA;AAAAC,OAAA,GAAAD,OAAA;AAAwC;AAAA,SAAAE,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,eAAA;IAAArB,IAAA;EAAA;EAAA,IAAAsB,QAAA,GAAArB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAmB,QAAA,CAAAvB,IAAA,KAAAuB,QAAA,CAAAvB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAsB,QAAA,CAAAvB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAmB,cAAA,GAAAD,QAAA,CAAAvB,IAAA;EAAA;IAO5B;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAyB,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAzB,cAAA;AAfZ;AACA;AACA;AACA;AACA;AACA;AAKA;;AAkBA;AACA,IAAI0B,SAAuD;AAAA;AAAA,CAAA1B,cAAA,GAAAoB,CAAA,OAAG,IAAI;;AAElE;AACA;AACA;AACA,SAASO,UAAUA,CAAA,EAAW;EAAA;EAAA3B,cAAA,GAAAqB,CAAA;EAC5B,MAAM;IAAEO,MAAM;IAAEC;EAAW,CAAC;EAAA;EAAA,CAAA7B,cAAA,GAAAoB,CAAA;EAAGU;EAAAA;EAAAA;EAAAA,YAAY,CAACC,GAAG;EAAC;EAAA/B,cAAA,GAAAoB,CAAA;EAChD,OAAO,uBAAuBQ,MAAM,kBAAkBC,UAAU,wBAAwB;AAC1F;;AAEA;AACA;AACA;AACA,SAASG,OAAOA,CAAA,EAAG;EAAA;EAAAhC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACjB,IAAI,CAACM,SAAS,EAAE;IAAA;IAAA1B,cAAA,GAAAsB,CAAA;IACd,MAAMW,OAAO;IAAA;IAAA,CAAAjC,cAAA,GAAAoB,CAAA,OAAGO,UAAU,CAAC,CAAC;IAAC;IAAA3B,cAAA,GAAAoB,CAAA;IAC7BM,SAAS;IAAG;IAAA;IAAAQ;IAAAA;IAAAA;IAAAA,kBAAkB,EAAC,IAAIC,GAAG,CAACF,OAAO,CAAC,CAAC;EAClD,CAAC;EAAA;EAAA;IAAAjC,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EACD,OAAOM,SAAS;AAClB;;AAEA;AACA;AACA;AACO,eAAeU,gBAAgBA,CAACC,KAAa,EAAqC;EAAA;EAAArC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACvF,IAAI;IACF,MAAMkB,IAAI;IAAA;IAAA,CAAAtC,cAAA,GAAAoB,CAAA,OAAGY,OAAO,CAAC,CAAC;IACtB,MAAM;MAAEJ,MAAM;MAAEC,UAAU;MAAEU;IAAiB,CAAC;IAAA;IAAA,CAAAvC,cAAA,GAAAoB,CAAA;IAAGU;IAAAA;IAAAA;IAAAA,YAAY,CAACC,GAAG;;IAEjE;IACA,MAAMS,cAAc;IAAA;IAAA,CAAAxC,cAAA,GAAAoB,CAAA,QAAG,uBAAuBQ,MAAM,kBAAkBC,UAAU,EAAE;;IAElF;IACA,MAAM;MAAEY;IAAQ,CAAC;IAAA;IAAA,CAAAzC,cAAA,GAAAoB,CAAA,QAAG;IAAM;IAAA;IAAAsB;IAAAA;IAAAA;IAAAA,SAAS,EAACL,KAAK,EAAEC,IAAI,EAAE;MAC/CK,MAAM,EAAEH,cAAc;MACtBI,QAAQ,EAAEL;IACZ,CAAC,CAAC;;IAEF;IACA,MAAMM,cAAc;IAAA;IAAA,CAAA7C,cAAA,GAAAoB,CAAA,QAAGqB,OAAO,CAAqB;;IAEnD;IAAA;IAAAzC,cAAA,GAAAoB,CAAA;IACA,IAAIyB,cAAc,CAACC,SAAS,KAAK,IAAI,EAAE;MAAA;MAAA9C,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACrC2B,OAAO,CAACC,IAAI,CAAC,qBAAqB,EAAEH,cAAc,CAACC,SAAS,CAAC;MAAC;MAAA9C,cAAA,GAAAoB,CAAA;MAC9D,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA,IAAI,CAACyB,cAAc,CAACI,KAAK,EAAE;MAAA;MAAAjD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACzB2B,OAAO,CAACC,IAAI,CAAC,2BAA2B,CAAC;MAAC;MAAAhD,cAAA,GAAAoB,CAAA;MAC1C,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA,IAAIyB,cAAc,CAACK,cAAc,KAAK,KAAK,EAAE;MAAA;MAAAlD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC3C2B,OAAO,CAACC,IAAI,CAAC,8BAA8B,EAAEH,cAAc,CAACI,KAAK,CAAC;MAClE;IACF,CAAC;IAAA;IAAA;MAAAjD,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAOyB,cAAc;EACvB,CAAC,CAAC,OAAOM,KAAK,EAAE;IAAA;IAAAnD,cAAA,GAAAoB,CAAA;IACd2B,OAAO,CAACI,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAAC;IAAAnD,cAAA,GAAAoB,CAAA;IAC/C,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACO,eAAegC,kBAAkBA,CAACC,WAAmB,EAAqC;EAAA;EAAArD,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EAC/F,IAAI,CAACiC,WAAW,EAAE;IAAA;IAAArD,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAChB,OAAO,IAAI;EACb,CAAC;EAAA;EAAA;IAAApB,cAAA,GAAAsB,CAAA;EAAA;;EAED;EACA,MAAMe,KAAK;EAAA;EAAA,CAAArC,cAAA,GAAAoB,CAAA,QAAGiC,WAAW,CAACC,IAAI,CAAC,CAAC;;EAEhC;EAAA;EAAAtD,cAAA,GAAAoB,CAAA;EACA;EAAI;EAAA,CAAApB,cAAA,GAAAsB,CAAA,WAACe,KAAK,CAACkB,QAAQ,CAAC,GAAG,CAAC;EAAA;EAAA,CAAAvD,cAAA,GAAAsB,CAAA,UAAIe,KAAK,CAACmB,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,KAAK,CAAC,GAAE;IAAA;IAAAzD,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACzD2B,OAAO,CAACC,IAAI,CAAC,8BAA8B,CAAC;IAAC;IAAAhD,cAAA,GAAAoB,CAAA;IAC7C,OAAO,IAAI;EACb,CAAC;EAAA;EAAA;IAAApB,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EAED,OAAOgB,gBAAgB,CAACC,KAAK,CAAC;AAChC;;AAEA;AACA;AACA;AACA;AACO,SAASqB,cAAcA,CAACrB,KAAa,EAA4B;EAAA;EAAArC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACtE;EAAIU;EAAAA;EAAAA;EAAAA,YAAY,CAAC6B,GAAG,CAACC,YAAY,EAAE;IAAA;IAAA5D,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACjC,MAAM,IAAIyC,KAAK,CAAC,iDAAiD,CAAC;EACpE,CAAC;EAAA;EAAA;IAAA7D,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EAED,IAAI;IACF,MAAM0C,KAAK;IAAA;IAAA,CAAA9D,cAAA,GAAAoB,CAAA,QAAGiB,KAAK,CAACmB,KAAK,CAAC,GAAG,CAAC;IAAC;IAAAxD,cAAA,GAAAoB,CAAA;IAC/B,IAAI0C,KAAK,CAACL,MAAM,KAAK,CAAC,EAAE;MAAA;MAAAzD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACtB,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAED,MAAMmB,OAAO;IAAA;IAAA,CAAAzC,cAAA,GAAAoB,CAAA,QAAG2C,IAAI,CAACC,KAAK,CAACC,MAAM,CAACC,IAAI,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAC;;IAEtE;IACA,MAAMC,WAAW;IAAA;IAAA,CAAApE,cAAA,GAAAoB,CAAA,QAAGiD,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;IAAC;IAAAxE,cAAA,GAAAoB,CAAA;IAClD;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAmB,OAAO,CAACgC,GAAG;IAAA;IAAA,CAAAzE,cAAA,GAAAsB,CAAA,WAAImB,OAAO,CAACgC,GAAG,GAAGL,WAAW,GAAE;MAAA;MAAApE,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC5C2B,OAAO,CAACC,IAAI,CAAC,mBAAmB,CAAC;MAAC;MAAAhD,cAAA,GAAAoB,CAAA;MAClC,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAOqB,OAAO;EAChB,CAAC,CAAC,OAAOU,KAAK,EAAE;IAAA;IAAAnD,cAAA,GAAAoB,CAAA;IACd2B,OAAO,CAACI,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAAC;IAAAnD,cAAA,GAAAoB,CAAA;IAC7C,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACO,SAASsD,cAAcA,CAACjC,OAA0B,EAAW;EAAA;EAAAzC,cAAA,GAAAqB,CAAA;EAClE,MAAM+C,WAAW;EAAA;EAAA,CAAApE,cAAA,GAAAoB,CAAA,QAAGiD,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;EAAC;EAAAxE,cAAA,GAAAoB,CAAA;EAClD,OAAOqB,OAAO,CAACgC,GAAG,GAAGL,WAAW;AAClC;;AAEA;AACA;AACA;AACO,SAASO,sBAAsBA,CAAClC,OAA0B,EAAU;EAAA;EAAAzC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACzE,OAAOqB,OAAO,CAACgC,GAAG,GAAG,IAAI;AAC3B;;AAEA;AACA;AACA;AACO,SAASG,qBAAqBA,CAACnC,OAA0B,EAAEoC,OAAe,EAAW;EAAA;EAAA7E,cAAA,GAAAqB,CAAA;EAC1F,MAAMyD,cAAc;EAAA;EAAA,CAAA9E,cAAA,GAAAoB,CAAA,QAAGuD,sBAAsB,CAAClC,OAAO,CAAC;EACtD,MAAMsC,WAAW;EAAA;EAAA,CAAA/E,cAAA,GAAAoB,CAAA,QAAGmD,IAAI,CAACC,GAAG,CAAC,CAAC,GAAIK,OAAO,GAAG,EAAE,GAAG,IAAK;EAAC;EAAA7E,cAAA,GAAAoB,CAAA;EACvD,OAAO0D,cAAc,IAAIC,WAAW;AACtC", "ignoreList": []}