/**
 * Performance Tests
 * 
 * Tests for performance optimizations including caching,
 * memoization, lazy loading, and render performance
 */

import { renderHook, act } from '@testing-library/react'
import { jest } from '@jest/globals'
import { 
  useDebounce, 
  useThrottle, 
  usePerformanceMonitor,
  useVirtualScrolling,
  performanceUtils 
} from '@/lib/performance'
import { AdvancedCache } from '@/lib/cache'

describe('Performance Optimizations', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  describe('Debouncing', () => {
    it('should debounce rapid value changes', () => {
      const { result, rerender } = renderHook(
        ({ value, delay }) => useDebounce(value, delay),
        { initialProps: { value: 'initial', delay: 300 } }
      )

      expect(result.current).toBe('initial')

      // Rapid changes
      rerender({ value: 'change1', delay: 300 })
      rerender({ value: 'change2', delay: 300 })
      rerender({ value: 'final', delay: 300 })

      // Should still be initial value
      expect(result.current).toBe('initial')

      // Fast forward time
      act(() => {
        jest.advanceTimersByTime(300)
      })

      // Should now be the final value
      expect(result.current).toBe('final')
    })

    it('should handle different delay values', () => {
      const { result, rerender } = renderHook(
        ({ value, delay }) => useDebounce(value, delay),
        { initialProps: { value: 'test', delay: 100 } }
      )

      rerender({ value: 'changed', delay: 100 })

      act(() => {
        jest.advanceTimersByTime(50)
      })
      expect(result.current).toBe('test')

      act(() => {
        jest.advanceTimersByTime(50)
      })
      expect(result.current).toBe('changed')
    })

    it('should cleanup timers on unmount', () => {
      const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout')
      
      const { unmount } = renderHook(() => useDebounce('test', 300))
      
      unmount()
      
      expect(clearTimeoutSpy).toHaveBeenCalled()
    })
  })

  describe('Throttling', () => {
    it('should throttle function calls', () => {
      const mockFn = jest.fn()
      
      const { result } = renderHook(() => useThrottle(mockFn, 100))
      
      const throttledFn = result.current

      // Rapid calls
      throttledFn('call1')
      throttledFn('call2')
      throttledFn('call3')

      // Should only call once immediately
      expect(mockFn).toHaveBeenCalledTimes(1)
      expect(mockFn).toHaveBeenCalledWith('call1')

      // Advance time
      act(() => {
        jest.advanceTimersByTime(100)
      })

      // Call again
      throttledFn('call4')

      expect(mockFn).toHaveBeenCalledTimes(2)
      expect(mockFn).toHaveBeenLastCalledWith('call4')
    })

    it('should maintain function context', () => {
      const mockFn = jest.fn(function(this: any, value: string) {
        return this.prefix + value
      })

      const context = { prefix: 'test-' }
      
      const { result } = renderHook(() => useThrottle(mockFn.bind(context), 100))
      
      result.current('value')
      
      expect(mockFn).toHaveBeenCalledWith('value')
    })
  })

  describe('Performance Monitoring', () => {
    it('should track render count', () => {
      const { result, rerender } = renderHook(() => 
        usePerformanceMonitor('TestComponent')
      )

      expect(result.current.renderCount).toBe(1)

      rerender()
      expect(result.current.renderCount).toBe(2)

      rerender()
      expect(result.current.renderCount).toBe(3)
    })

    it('should warn about slow renders in development', () => {
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'development'
      
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
      
      // Mock slow performance
      const mockPerformance = {
        now: jest.fn()
          .mockReturnValueOnce(0)    // Start time
          .mockReturnValueOnce(20),  // End time (20ms - slow)
      }
      
      Object.defineProperty(global, 'performance', {
        value: mockPerformance,
        writable: true,
      })

      renderHook(() => usePerformanceMonitor('SlowComponent'))

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Slow render detected')
      )

      consoleSpy.mockRestore()
      process.env.NODE_ENV = originalEnv
    })
  })

  describe('Virtual Scrolling', () => {
    const mockItems = Array.from({ length: 1000 }, (_, i) => `Item ${i}`)

    it('should calculate visible items correctly', () => {
      const { result } = renderHook(() =>
        useVirtualScrolling(mockItems, 50, 300)
      )

      expect(result.current.startIndex).toBe(0)
      expect(result.current.endIndex).toBeLessThan(mockItems.length)
      expect(result.current.items.length).toBeLessThan(mockItems.length)
      expect(result.current.totalHeight).toBe(50000) // 1000 * 50
    })

    it('should update visible items on scroll', () => {
      const { result } = renderHook(() =>
        useVirtualScrolling(mockItems, 50, 300)
      )

      const initialStartIndex = result.current.startIndex

      // Simulate scroll
      act(() => {
        result.current.handleScroll({
          currentTarget: { scrollTop: 500 }
        } as any)
      })

      expect(result.current.startIndex).toBeGreaterThan(initialStartIndex)
      expect(result.current.offsetY).toBeGreaterThan(0)
    })

    it('should handle edge cases', () => {
      const { result } = renderHook(() =>
        useVirtualScrolling([], 50, 300)
      )

      expect(result.current.items).toEqual([])
      expect(result.current.totalHeight).toBe(0)
    })
  })

  describe('Performance Utils', () => {
    beforeEach(() => {
      // Mock performance API
      Object.defineProperty(global, 'performance', {
        value: {
          mark: jest.fn(),
          measure: jest.fn(),
          getEntriesByName: jest.fn(() => [{ duration: 50 }]),
          clearMarks: jest.fn(),
          clearMeasures: jest.fn(),
        },
        writable: true,
      })
    })

    it('should mark performance points', () => {
      performanceUtils.mark('test-operation')

      expect(performance.mark).toHaveBeenCalledWith('test-operation-start')
    })

    it('should measure performance duration', () => {
      performanceUtils.measure('test-operation')

      expect(performance.mark).toHaveBeenCalledWith('test-operation-end')
      expect(performance.measure).toHaveBeenCalledWith(
        'test-operation',
        'test-operation-start',
        'test-operation-end'
      )
    })

    it('should log slow operations', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation()
      
      // Mock slow operation (>100ms)
      performance.getEntriesByName = jest.fn(() => [{ duration: 150 }])

      performanceUtils.measure('slow-operation')

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Performance: slow-operation took 150.00ms')
      )

      consoleSpy.mockRestore()
    })

    it('should clear performance marks', () => {
      performanceUtils.clear('test-operation')

      expect(performance.clearMarks).toHaveBeenCalledWith('test-operation-start')
      expect(performance.clearMarks).toHaveBeenCalledWith('test-operation-end')
      expect(performance.clearMeasures).toHaveBeenCalledWith('test-operation')
    })

    it('should handle missing performance API gracefully', () => {
      const originalPerformance = global.performance
      delete (global as any).performance

      expect(() => {
        performanceUtils.mark('test')
        performanceUtils.measure('test')
        performanceUtils.clear('test')
      }).not.toThrow()

      global.performance = originalPerformance
    })
  })

  describe('Cache Performance', () => {
    let cache: AdvancedCache<string>

    beforeEach(() => {
      cache = new AdvancedCache({
        maxSize: 100,
        defaultTTL: 1000,
        enableMetrics: true,
      })
    })

    afterEach(() => {
      cache.destroy()
    })

    it('should perform well with many operations', () => {
      const startTime = performance.now()

      // Perform many cache operations
      for (let i = 0; i < 1000; i++) {
        cache.set(`key${i}`, `value${i}`)
      }

      for (let i = 0; i < 1000; i++) {
        cache.get(`key${i}`)
      }

      const endTime = performance.now()
      const duration = endTime - startTime

      // Should complete in reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(100) // 100ms
    })

    it('should maintain good hit rates', () => {
      // Set up cache with data
      for (let i = 0; i < 50; i++) {
        cache.set(`key${i}`, `value${i}`)
      }

      // Access cached items
      for (let i = 0; i < 50; i++) {
        cache.get(`key${i}`)
      }

      // Access some non-existent items
      for (let i = 50; i < 60; i++) {
        cache.get(`key${i}`)
      }

      const metrics = cache.getMetrics()
      expect(metrics.hitRate).toBeGreaterThan(0.8) // 80% hit rate
    })

    it('should handle memory efficiently', () => {
      const initialSize = cache.size()

      // Fill cache beyond capacity
      for (let i = 0; i < 200; i++) {
        cache.set(`key${i}`, `value${i}`)
      }

      // Should not exceed max size
      expect(cache.size()).toBeLessThanOrEqual(100)
      expect(cache.getMetrics().evictions).toBeGreaterThan(0)
    })
  })

  describe('Render Performance', () => {
    it('should minimize re-renders with memoization', () => {
      let renderCount = 0
      
      const TestComponent = React.memo(() => {
        renderCount++
        return <div>Test</div>
      })

      const { rerender } = render(<TestComponent />)
      expect(renderCount).toBe(1)

      // Re-render with same props
      rerender(<TestComponent />)
      expect(renderCount).toBe(1) // Should not re-render

      // Re-render with different props
      rerender(<TestComponent key="different" />)
      expect(renderCount).toBe(2) // Should re-render
    })

    it('should handle large lists efficiently', () => {
      const largeList = Array.from({ length: 10000 }, (_, i) => i)
      
      const startTime = performance.now()
      
      const { result } = renderHook(() =>
        useVirtualScrolling(largeList, 30, 600)
      )

      const endTime = performance.now()
      
      // Should calculate visible items quickly
      expect(endTime - startTime).toBeLessThan(10) // 10ms
      expect(result.current.items.length).toBeLessThan(100) // Only render visible items
    })
  })

  describe('Memory Management', () => {
    it('should cleanup resources on unmount', () => {
      const cleanupSpy = jest.fn()
      
      const TestHook = () => {
        React.useEffect(() => {
          return cleanupSpy
        }, [])
        
        return useDebounce('test', 300)
      }

      const { unmount } = renderHook(TestHook)
      
      unmount()
      
      expect(cleanupSpy).toHaveBeenCalled()
    })

    it('should prevent memory leaks in event handlers', () => {
      const mockFn = jest.fn()
      const { result, unmount } = renderHook(() => useThrottle(mockFn, 100))

      // Use the throttled function
      result.current('test')

      // Should not throw on unmount
      expect(() => unmount()).not.toThrow()
    })
  })
})
