38ab1a566832ecfb5c12cefb178d5a58
/* istanbul ignore next */
function cov_44vatrfhc() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\config.ts";
  var hash = "7771125f7f7c86ce4b41eb6c5b4b1d49019f7fbe";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\config.ts",
    statementMap: {
      "0": {
        start: {
          line: 11,
          column: 24
        },
        end: {
          line: 21,
          column: 2
        }
      },
      "1": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 35,
          column: 2
        }
      },
      "2": {
        start: {
          line: 39,
          column: 2
        },
        end: {
          line: 70,
          column: 3
        }
      },
      "3": {
        start: {
          line: 40,
          column: 21
        },
        end: {
          line: 40,
          column: 50
        }
      },
      "4": {
        start: {
          line: 42,
          column: 4
        },
        end: {
          line: 57,
          column: 5
        }
      },
      "5": {
        start: {
          line: 44,
          column: 6
        },
        end: {
          line: 44,
          column: 48
        }
      },
      "6": {
        start: {
          line: 47,
          column: 24
        },
        end: {
          line: 54,
          column: 7
        }
      },
      "7": {
        start: {
          line: 56,
          column: 6
        },
        end: {
          line: 56,
          column: 46
        }
      },
      "8": {
        start: {
          line: 59,
          column: 4
        },
        end: {
          line: 59,
          column: 65
        }
      },
      "9": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 67,
          column: 5
        }
      },
      "10": {
        start: {
          line: 63,
          column: 6
        },
        end: {
          line: 63,
          column: 42
        }
      },
      "11": {
        start: {
          line: 64,
          column: 6
        },
        end: {
          line: 66,
          column: 9
        }
      },
      "12": {
        start: {
          line: 65,
          column: 8
        },
        end: {
          line: 65,
          column: 71
        }
      },
      "13": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 69,
          column: 84
        }
      },
      "14": {
        start: {
          line: 74,
          column: 57
        },
        end: {
          line: 74,
          column: 61
        }
      },
      "15": {
        start: {
          line: 75,
          column: 57
        },
        end: {
          line: 75,
          column: 61
        }
      },
      "16": {
        start: {
          line: 78,
          column: 19
        },
        end: {
          line: 78,
          column: 48
        }
      },
      "17": {
        start: {
          line: 80,
          column: 2
        },
        end: {
          line: 90,
          column: 3
        }
      },
      "18": {
        start: {
          line: 81,
          column: 4
        },
        end: {
          line: 83,
          column: 5
        }
      },
      "19": {
        start: {
          line: 82,
          column: 6
        },
        end: {
          line: 82,
          column: 68
        }
      },
      "20": {
        start: {
          line: 84,
          column: 4
        },
        end: {
          line: 84,
          column: 22
        }
      },
      "21": {
        start: {
          line: 86,
          column: 4
        },
        end: {
          line: 88,
          column: 5
        }
      },
      "22": {
        start: {
          line: 87,
          column: 6
        },
        end: {
          line: 87,
          column: 68
        }
      },
      "23": {
        start: {
          line: 89,
          column: 4
        },
        end: {
          line: 89,
          column: 22
        }
      },
      "24": {
        start: {
          line: 94,
          column: 24
        },
        end: {
          line: 113,
          column: 1
        }
      },
      "25": {
        start: {
          line: 96,
          column: 2
        },
        end: {
          line: 112,
          column: 4
        }
      },
      "26": {
        start: {
          line: 116,
          column: 28
        },
        end: {
          line: 156,
          column: 10
        }
      },
      "27": {
        start: {
          line: 118,
          column: 21
        },
        end: {
          line: 118,
          column: 50
        }
      },
      "28": {
        start: {
          line: 119,
          column: 4
        },
        end: {
          line: 129,
          column: 5
        }
      },
      "29": {
        start: {
          line: 120,
          column: 18
        },
        end: {
          line: 120,
          column: 26
        }
      },
      "30": {
        start: {
          line: 121,
          column: 6
        },
        end: {
          line: 126,
          column: 8
        }
      },
      "31": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 35
        }
      },
      "32": {
        start: {
          line: 132,
          column: 21
        },
        end: {
          line: 132,
          column: 50
        }
      },
      "33": {
        start: {
          line: 133,
          column: 4
        },
        end: {
          line: 141,
          column: 5
        }
      },
      "34": {
        start: {
          line: 134,
          column: 18
        },
        end: {
          line: 134,
          column: 26
        }
      },
      "35": {
        start: {
          line: 135,
          column: 6
        },
        end: {
          line: 138,
          column: 8
        }
      },
      "36": {
        start: {
          line: 140,
          column: 6
        },
        end: {
          line: 140,
          column: 36
        }
      },
      "37": {
        start: {
          line: 144,
          column: 21
        },
        end: {
          line: 144,
          column: 50
        }
      },
      "38": {
        start: {
          line: 145,
          column: 4
        },
        end: {
          line: 154,
          column: 5
        }
      },
      "39": {
        start: {
          line: 146,
          column: 18
        },
        end: {
          line: 146,
          column: 26
        }
      },
      "40": {
        start: {
          line: 147,
          column: 6
        },
        end: {
          line: 151,
          column: 8
        }
      },
      "41": {
        start: {
          line: 153,
          column: 6
        },
        end: {
          line: 153,
          column: 35
        }
      },
      "42": {
        start: {
          line: 159,
          column: 28
        },
        end: {
          line: 171,
          column: 10
        }
      },
      "43": {
        start: {
          line: 161,
          column: 16
        },
        end: {
          line: 161,
          column: 24
        }
      },
      "44": {
        start: {
          line: 162,
          column: 4
        },
        end: {
          line: 169,
          column: 6
        }
      },
      "45": {
        start: {
          line: 178,
          column: 29
        },
        end: {
          line: 178,
          column: 52
        }
      },
      "46": {
        start: {
          line: 178,
          column: 35
        },
        end: {
          line: 178,
          column: 52
        }
      },
      "47": {
        start: {
          line: 179,
          column: 28
        },
        end: {
          line: 179,
          column: 50
        }
      },
      "48": {
        start: {
          line: 179,
          column: 34
        },
        end: {
          line: 179,
          column: 50
        }
      },
      "49": {
        start: {
          line: 180,
          column: 33
        },
        end: {
          line: 185,
          column: 1
        }
      },
      "50": {
        start: {
          line: 181,
          column: 2
        },
        end: {
          line: 183,
          column: 3
        }
      },
      "51": {
        start: {
          line: 182,
          column: 4
        },
        end: {
          line: 182,
          column: 82
        }
      },
      "52": {
        start: {
          line: 184,
          column: 2
        },
        end: {
          line: 184,
          column: 31
        }
      },
      "53": {
        start: {
          line: 188,
          column: 30
        },
        end: {
          line: 203,
          column: 1
        }
      },
      "54": {
        start: {
          line: 189,
          column: 29
        },
        end: {
          line: 194,
          column: 3
        }
      },
      "55": {
        start: {
          line: 196,
          column: 18
        },
        end: {
          line: 196,
          column: 69
        }
      },
      "56": {
        start: {
          line: 196,
          column: 51
        },
        end: {
          line: 196,
          column: 68
        }
      },
      "57": {
        start: {
          line: 198,
          column: 2
        },
        end: {
          line: 200,
          column: 3
        }
      },
      "58": {
        start: {
          line: 199,
          column: 4
        },
        end: {
          line: 199,
          column: 85
        }
      },
      "59": {
        start: {
          line: 202,
          column: 2
        },
        end: {
          line: 202,
          column: 14
        }
      },
      "60": {
        start: {
          line: 206,
          column: 19
        },
        end: {
          line: 206,
          column: 25
        }
      }
    },
    fnMap: {
      "0": {
        name: "validateEnv",
        decl: {
          start: {
            line: 38,
            column: 9
          },
          end: {
            line: 38,
            column: 20
          }
        },
        loc: {
          start: {
            line: 38,
            column: 23
          },
          end: {
            line: 71,
            column: 1
          }
        },
        line: 38
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 64,
            column: 27
          },
          end: {
            line: 64,
            column: 28
          }
        },
        loc: {
          start: {
            line: 64,
            column: 38
          },
          end: {
            line: 66,
            column: 7
          }
        },
        line: 64
      },
      "2": {
        name: "getEnv",
        decl: {
          start: {
            line: 77,
            column: 9
          },
          end: {
            line: 77,
            column: 15
          }
        },
        loc: {
          start: {
            line: 77,
            column: 18
          },
          end: {
            line: 91,
            column: 1
          }
        },
        line: 77
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 94,
            column: 24
          },
          end: {
            line: 94,
            column: 25
          }
        },
        loc: {
          start: {
            line: 94,
            column: 30
          },
          end: {
            line: 113,
            column: 1
          }
        },
        line: 94
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 117,
            column: 2
          },
          end: {
            line: 117,
            column: 3
          }
        },
        loc: {
          start: {
            line: 117,
            column: 12
          },
          end: {
            line: 130,
            column: 3
          }
        },
        line: 117
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 131,
            column: 2
          },
          end: {
            line: 131,
            column: 3
          }
        },
        loc: {
          start: {
            line: 131,
            column: 13
          },
          end: {
            line: 142,
            column: 3
          }
        },
        line: 131
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 143,
            column: 2
          },
          end: {
            line: 143,
            column: 3
          }
        },
        loc: {
          start: {
            line: 143,
            column: 12
          },
          end: {
            line: 155,
            column: 3
          }
        },
        line: 143
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 160,
            column: 2
          },
          end: {
            line: 160,
            column: 3
          }
        },
        loc: {
          start: {
            line: 160,
            column: 17
          },
          end: {
            line: 170,
            column: 3
          }
        },
        line: 160
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 178,
            column: 29
          },
          end: {
            line: 178,
            column: 30
          }
        },
        loc: {
          start: {
            line: 178,
            column: 35
          },
          end: {
            line: 178,
            column: 52
          }
        },
        line: 178
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 179,
            column: 28
          },
          end: {
            line: 179,
            column: 29
          }
        },
        loc: {
          start: {
            line: 179,
            column: 34
          },
          end: {
            line: 179,
            column: 50
          }
        },
        line: 179
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 180,
            column: 33
          },
          end: {
            line: 180,
            column: 34
          }
        },
        loc: {
          start: {
            line: 180,
            column: 39
          },
          end: {
            line: 185,
            column: 1
          }
        },
        line: 180
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 188,
            column: 30
          },
          end: {
            line: 188,
            column: 31
          }
        },
        loc: {
          start: {
            line: 188,
            column: 36
          },
          end: {
            line: 203,
            column: 1
          }
        },
        line: 188
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 196,
            column: 44
          },
          end: {
            line: 196,
            column: 45
          }
        },
        loc: {
          start: {
            line: 196,
            column: 51
          },
          end: {
            line: 196,
            column: 68
          }
        },
        line: 196
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 42,
            column: 4
          },
          end: {
            line: 57,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 4
          },
          end: {
            line: 57,
            column: 5
          }
        }, {
          start: {
            line: 45,
            column: 11
          },
          end: {
            line: 57,
            column: 5
          }
        }],
        line: 42
      },
      "1": {
        loc: {
          start: {
            line: 62,
            column: 4
          },
          end: {
            line: 67,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 62,
            column: 4
          },
          end: {
            line: 67,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 62
      },
      "2": {
        loc: {
          start: {
            line: 80,
            column: 2
          },
          end: {
            line: 90,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 80,
            column: 2
          },
          end: {
            line: 90,
            column: 3
          }
        }, {
          start: {
            line: 85,
            column: 9
          },
          end: {
            line: 90,
            column: 3
          }
        }],
        line: 80
      },
      "3": {
        loc: {
          start: {
            line: 81,
            column: 4
          },
          end: {
            line: 83,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 81,
            column: 4
          },
          end: {
            line: 83,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 81
      },
      "4": {
        loc: {
          start: {
            line: 86,
            column: 4
          },
          end: {
            line: 88,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 86,
            column: 4
          },
          end: {
            line: 88,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 86
      },
      "5": {
        loc: {
          start: {
            line: 98,
            column: 14
          },
          end: {
            line: 98,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 98,
            column: 14
          },
          end: {
            line: 98,
            column: 48
          }
        }, {
          start: {
            line: 98,
            column: 52
          },
          end: {
            line: 98,
            column: 54
          }
        }],
        line: 98
      },
      "6": {
        loc: {
          start: {
            line: 99,
            column: 18
          },
          end: {
            line: 99,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 99,
            column: 18
          },
          end: {
            line: 99,
            column: 59
          }
        }, {
          start: {
            line: 99,
            column: 63
          },
          end: {
            line: 99,
            column: 65
          }
        }],
        line: 99
      },
      "7": {
        loc: {
          start: {
            line: 100,
            column: 24
          },
          end: {
            line: 100,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 100,
            column: 24
          },
          end: {
            line: 100,
            column: 76
          }
        }, {
          start: {
            line: 100,
            column: 80
          },
          end: {
            line: 100,
            column: 82
          }
        }],
        line: 100
      },
      "8": {
        loc: {
          start: {
            line: 101,
            column: 21
          },
          end: {
            line: 101,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 101,
            column: 21
          },
          end: {
            line: 101,
            column: 63
          }
        }, {
          start: {
            line: 101,
            column: 67
          },
          end: {
            line: 101,
            column: 69
          }
        }],
        line: 101
      },
      "9": {
        loc: {
          start: {
            line: 104,
            column: 22
          },
          end: {
            line: 104,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 104,
            column: 22
          },
          end: {
            line: 104,
            column: 62
          }
        }, {
          start: {
            line: 104,
            column: 66
          },
          end: {
            line: 104,
            column: 68
          }
        }],
        line: 104
      },
      "10": {
        loc: {
          start: {
            line: 105,
            column: 23
          },
          end: {
            line: 105,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 105,
            column: 23
          },
          end: {
            line: 105,
            column: 64
          }
        }, {
          start: {
            line: 105,
            column: 68
          },
          end: {
            line: 105,
            column: 70
          }
        }],
        line: 105
      },
      "11": {
        loc: {
          start: {
            line: 108,
            column: 19
          },
          end: {
            line: 108,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 108,
            column: 19
          },
          end: {
            line: 108,
            column: 39
          }
        }, {
          start: {
            line: 108,
            column: 43
          },
          end: {
            line: 108,
            column: 56
          }
        }],
        line: 108
      },
      "12": {
        loc: {
          start: {
            line: 119,
            column: 4
          },
          end: {
            line: 129,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 119,
            column: 4
          },
          end: {
            line: 129,
            column: 5
          }
        }, {
          start: {
            line: 127,
            column: 11
          },
          end: {
            line: 129,
            column: 5
          }
        }],
        line: 119
      },
      "13": {
        loc: {
          start: {
            line: 133,
            column: 4
          },
          end: {
            line: 141,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 133,
            column: 4
          },
          end: {
            line: 141,
            column: 5
          }
        }, {
          start: {
            line: 139,
            column: 11
          },
          end: {
            line: 141,
            column: 5
          }
        }],
        line: 133
      },
      "14": {
        loc: {
          start: {
            line: 145,
            column: 4
          },
          end: {
            line: 154,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 145,
            column: 4
          },
          end: {
            line: 154,
            column: 5
          }
        }, {
          start: {
            line: 152,
            column: 11
          },
          end: {
            line: 154,
            column: 5
          }
        }],
        line: 145
      },
      "15": {
        loc: {
          start: {
            line: 181,
            column: 2
          },
          end: {
            line: 183,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 181,
            column: 2
          },
          end: {
            line: 183,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 181
      },
      "16": {
        loc: {
          start: {
            line: 198,
            column: 2
          },
          end: {
            line: 200,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 198,
            column: 2
          },
          end: {
            line: 200,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 198
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "7771125f7f7c86ce4b41eb6c5b4b1d49019f7fbe"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_44vatrfhc = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_44vatrfhc();
/**
 * Centralized Configuration Management
 * 
 * This module provides a secure, centralized way to manage all application configuration.
 * It validates environment variables and provides type-safe access to configuration values.
 */

import { z } from 'zod';

// Client-side environment validation schema (only NEXT_PUBLIC_ variables)
const clientEnvSchema =
/* istanbul ignore next */
(cov_44vatrfhc().s[0]++, z.object({
  // AWS Configuration
  NEXT_PUBLIC_AWS_REGION: z.string().min(1, 'AWS region is required'),
  NEXT_PUBLIC_AWS_USER_POOLS_ID: z.string().min(1, 'User pool ID is required'),
  NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID: z.string().min(1, 'User pool client ID is required'),
  NEXT_PUBLIC_AWS_COGNITO_DOMAIN: z.string().min(1, 'Cognito domain is required'),
  // Redirect URLs
  NEXT_PUBLIC_REDIRECT_SIGN_IN: z.string().url('Invalid sign-in redirect URL'),
  NEXT_PUBLIC_REDIRECT_SIGN_OUT: z.string().url('Invalid sign-out redirect URL')
}));

// Server-side environment validation schema (includes all variables)
const serverEnvSchema =
/* istanbul ignore next */
(cov_44vatrfhc().s[1]++, clientEnvSchema.extend({
  // Database Configuration (server-side only)
  DB_USER: z.string().optional(),
  DB_PASSWORD: z.string().optional(),
  DB_HOST: z.string().optional(),
  DB_NAME: z.string().optional(),
  DATABASE_URL: z.string().optional(),
  DATABASE_SSL: z.string().optional(),
  // Environment
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development')
}));

// Validate environment variables based on context
function validateEnv() {
  /* istanbul ignore next */
  cov_44vatrfhc().f[0]++;
  cov_44vatrfhc().s[2]++;
  try {
    const isServer =
    /* istanbul ignore next */
    (cov_44vatrfhc().s[3]++, typeof window === 'undefined');
    /* istanbul ignore next */
    cov_44vatrfhc().s[4]++;
    if (isServer) {
      /* istanbul ignore next */
      cov_44vatrfhc().b[0][0]++;
      cov_44vatrfhc().s[5]++;
      // Server-side: validate all environment variables
      return serverEnvSchema.parse(process.env);
    } else {
      /* istanbul ignore next */
      cov_44vatrfhc().b[0][1]++;
      // Client-side: only validate NEXT_PUBLIC_ variables that should be available
      const clientEnv =
      /* istanbul ignore next */
      (cov_44vatrfhc().s[6]++, {
        NEXT_PUBLIC_AWS_REGION: process.env.NEXT_PUBLIC_AWS_REGION,
        NEXT_PUBLIC_AWS_USER_POOLS_ID: process.env.NEXT_PUBLIC_AWS_USER_POOLS_ID,
        NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID: process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID,
        NEXT_PUBLIC_AWS_COGNITO_DOMAIN: process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN,
        NEXT_PUBLIC_REDIRECT_SIGN_IN: process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN,
        NEXT_PUBLIC_REDIRECT_SIGN_OUT: process.env.NEXT_PUBLIC_REDIRECT_SIGN_OUT
      });
      /* istanbul ignore next */
      cov_44vatrfhc().s[7]++;
      return clientEnvSchema.parse(clientEnv);
    }
  } catch (error) {
    /* istanbul ignore next */
    cov_44vatrfhc().s[8]++;
    console.error('❌ Invalid environment configuration:', error);

    // If it's a Zod error, show detailed validation issues
    /* istanbul ignore next */
    cov_44vatrfhc().s[9]++;
    if (error instanceof z.ZodError) {
      /* istanbul ignore next */
      cov_44vatrfhc().b[1][0]++;
      cov_44vatrfhc().s[10]++;
      console.error('Validation errors:');
      /* istanbul ignore next */
      cov_44vatrfhc().s[11]++;
      error.issues.forEach(issue => {
        /* istanbul ignore next */
        cov_44vatrfhc().f[1]++;
        cov_44vatrfhc().s[12]++;
        console.error(`  - ${issue.path.join('.')}: ${issue.message}`);
      });
    } else
    /* istanbul ignore next */
    {
      cov_44vatrfhc().b[1][1]++;
    }
    cov_44vatrfhc().s[13]++;
    throw new Error('Environment validation failed. Please check your .env files.');
  }
}

// Separate caches for client and server
let _serverEnv =
/* istanbul ignore next */
(cov_44vatrfhc().s[14]++, null);
let _clientEnv =
/* istanbul ignore next */
(cov_44vatrfhc().s[15]++, null);
function getEnv() {
  /* istanbul ignore next */
  cov_44vatrfhc().f[2]++;
  const isServer =
  /* istanbul ignore next */
  (cov_44vatrfhc().s[16]++, typeof window === 'undefined');
  /* istanbul ignore next */
  cov_44vatrfhc().s[17]++;
  if (isServer) {
    /* istanbul ignore next */
    cov_44vatrfhc().b[2][0]++;
    cov_44vatrfhc().s[18]++;
    if (_serverEnv === null) {
      /* istanbul ignore next */
      cov_44vatrfhc().b[3][0]++;
      cov_44vatrfhc().s[19]++;
      _serverEnv = validateEnv();
    } else
    /* istanbul ignore next */
    {
      cov_44vatrfhc().b[3][1]++;
    }
    cov_44vatrfhc().s[20]++;
    return _serverEnv;
  } else {
    /* istanbul ignore next */
    cov_44vatrfhc().b[2][1]++;
    cov_44vatrfhc().s[21]++;
    if (_clientEnv === null) {
      /* istanbul ignore next */
      cov_44vatrfhc().b[4][0]++;
      cov_44vatrfhc().s[22]++;
      _clientEnv = validateEnv();
    } else
    /* istanbul ignore next */
    {
      cov_44vatrfhc().b[4][1]++;
    }
    cov_44vatrfhc().s[23]++;
    return _clientEnv;
  }
}

// Client-safe configuration that doesn't require validation
/* istanbul ignore next */
cov_44vatrfhc().s[24]++;
const getClientConfig = () => {
  /* istanbul ignore next */
  cov_44vatrfhc().f[3]++;
  cov_44vatrfhc().s[25]++;
  // Direct access to process.env for client-side NEXT_PUBLIC_ variables
  return {
    aws: {
      region:
      /* istanbul ignore next */
      (cov_44vatrfhc().b[5][0]++, process.env.NEXT_PUBLIC_AWS_REGION) ||
      /* istanbul ignore next */
      (cov_44vatrfhc().b[5][1]++, ''),
      userPoolId:
      /* istanbul ignore next */
      (cov_44vatrfhc().b[6][0]++, process.env.NEXT_PUBLIC_AWS_USER_POOLS_ID) ||
      /* istanbul ignore next */
      (cov_44vatrfhc().b[6][1]++, ''),
      userPoolClientId:
      /* istanbul ignore next */
      (cov_44vatrfhc().b[7][0]++, process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID) ||
      /* istanbul ignore next */
      (cov_44vatrfhc().b[7][1]++, ''),
      cognitoDomain:
      /* istanbul ignore next */
      (cov_44vatrfhc().b[8][0]++, process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN) ||
      /* istanbul ignore next */
      (cov_44vatrfhc().b[8][1]++, '')
    },
    auth: {
      redirectSignIn:
      /* istanbul ignore next */
      (cov_44vatrfhc().b[9][0]++, process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN) ||
      /* istanbul ignore next */
      (cov_44vatrfhc().b[9][1]++, ''),
      redirectSignOut:
      /* istanbul ignore next */
      (cov_44vatrfhc().b[10][0]++, process.env.NEXT_PUBLIC_REDIRECT_SIGN_OUT) ||
      /* istanbul ignore next */
      (cov_44vatrfhc().b[10][1]++, '')
    },
    app: {
      environment:
      /* istanbul ignore next */
      (cov_44vatrfhc().b[11][0]++, process.env.NODE_ENV) ||
      /* istanbul ignore next */
      (cov_44vatrfhc().b[11][1]++, 'development'),
      isDevelopment: process.env.NODE_ENV === 'development',
      isProduction: process.env.NODE_ENV === 'production'
    }
  };
};

// Public configuration (safe to expose to client) - using getters for lazy evaluation
export const publicConfig =
/* istanbul ignore next */
(cov_44vatrfhc().s[26]++, {
  get aws() {
    /* istanbul ignore next */
    cov_44vatrfhc().f[4]++;
    const isServer =
    /* istanbul ignore next */
    (cov_44vatrfhc().s[27]++, typeof window === 'undefined');
    /* istanbul ignore next */
    cov_44vatrfhc().s[28]++;
    if (isServer) {
      /* istanbul ignore next */
      cov_44vatrfhc().b[12][0]++;
      const env =
      /* istanbul ignore next */
      (cov_44vatrfhc().s[29]++, getEnv());
      /* istanbul ignore next */
      cov_44vatrfhc().s[30]++;
      return {
        region: env.NEXT_PUBLIC_AWS_REGION,
        userPoolId: env.NEXT_PUBLIC_AWS_USER_POOLS_ID,
        userPoolClientId: env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID,
        cognitoDomain: env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN
      };
    } else {
      /* istanbul ignore next */
      cov_44vatrfhc().b[12][1]++;
      cov_44vatrfhc().s[31]++;
      return getClientConfig().aws;
    }
  },
  get auth() {
    /* istanbul ignore next */
    cov_44vatrfhc().f[5]++;
    const isServer =
    /* istanbul ignore next */
    (cov_44vatrfhc().s[32]++, typeof window === 'undefined');
    /* istanbul ignore next */
    cov_44vatrfhc().s[33]++;
    if (isServer) {
      /* istanbul ignore next */
      cov_44vatrfhc().b[13][0]++;
      const env =
      /* istanbul ignore next */
      (cov_44vatrfhc().s[34]++, getEnv());
      /* istanbul ignore next */
      cov_44vatrfhc().s[35]++;
      return {
        redirectSignIn: env.NEXT_PUBLIC_REDIRECT_SIGN_IN,
        redirectSignOut: env.NEXT_PUBLIC_REDIRECT_SIGN_OUT
      };
    } else {
      /* istanbul ignore next */
      cov_44vatrfhc().b[13][1]++;
      cov_44vatrfhc().s[36]++;
      return getClientConfig().auth;
    }
  },
  get app() {
    /* istanbul ignore next */
    cov_44vatrfhc().f[6]++;
    const isServer =
    /* istanbul ignore next */
    (cov_44vatrfhc().s[37]++, typeof window === 'undefined');
    /* istanbul ignore next */
    cov_44vatrfhc().s[38]++;
    if (isServer) {
      /* istanbul ignore next */
      cov_44vatrfhc().b[14][0]++;
      const env =
      /* istanbul ignore next */
      (cov_44vatrfhc().s[39]++, getEnv());
      /* istanbul ignore next */
      cov_44vatrfhc().s[40]++;
      return {
        environment: env.NODE_ENV,
        isDevelopment: env.NODE_ENV === 'development',
        isProduction: env.NODE_ENV === 'production'
      };
    } else {
      /* istanbul ignore next */
      cov_44vatrfhc().b[14][1]++;
      cov_44vatrfhc().s[41]++;
      return getClientConfig().app;
    }
  }
});

// Server-side configuration (never expose to client) - using getters for lazy evaluation
export const serverConfig =
/* istanbul ignore next */
(cov_44vatrfhc().s[42]++, {
  get database() {
    /* istanbul ignore next */
    cov_44vatrfhc().f[7]++;
    const env =
    /* istanbul ignore next */
    (cov_44vatrfhc().s[43]++, getEnv());
    /* istanbul ignore next */
    cov_44vatrfhc().s[44]++;
    return {
      user: env.DB_USER,
      password: env.DB_PASSWORD,
      host: env.DB_HOST,
      name: env.DB_NAME,
      url: env.DATABASE_URL,
      ssl: env.DATABASE_SSL === 'true'
    };
  }
});

// Type exports for better TypeScript support
/* istanbul ignore next */
// Utility functions
cov_44vatrfhc().s[45]++;
export const getAuthConfig = () => {
  /* istanbul ignore next */
  cov_44vatrfhc().f[8]++;
  cov_44vatrfhc().s[46]++;
  return publicConfig.auth;
};
/* istanbul ignore next */
cov_44vatrfhc().s[47]++;
export const getAwsConfig = () => {
  /* istanbul ignore next */
  cov_44vatrfhc().f[9]++;
  cov_44vatrfhc().s[48]++;
  return publicConfig.aws;
};
/* istanbul ignore next */
cov_44vatrfhc().s[49]++;
export const getDatabaseConfig = () => {
  /* istanbul ignore next */
  cov_44vatrfhc().f[10]++;
  cov_44vatrfhc().s[50]++;
  if (typeof window !== 'undefined') {
    /* istanbul ignore next */
    cov_44vatrfhc().b[15][0]++;
    cov_44vatrfhc().s[51]++;
    throw new Error('Database configuration is not available on the client side');
  } else
  /* istanbul ignore next */
  {
    cov_44vatrfhc().b[15][1]++;
  }
  cov_44vatrfhc().s[52]++;
  return serverConfig.database;
};

// Configuration validation for runtime checks
/* istanbul ignore next */
cov_44vatrfhc().s[53]++;
export const validateConfig = () => {
  /* istanbul ignore next */
  cov_44vatrfhc().f[11]++;
  const requiredPublicVars =
  /* istanbul ignore next */
  (cov_44vatrfhc().s[54]++, ['NEXT_PUBLIC_AWS_REGION', 'NEXT_PUBLIC_AWS_USER_POOLS_ID', 'NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID', 'NEXT_PUBLIC_AWS_COGNITO_DOMAIN']);
  const missing =
  /* istanbul ignore next */
  (cov_44vatrfhc().s[55]++, requiredPublicVars.filter(key => {
    /* istanbul ignore next */
    cov_44vatrfhc().f[12]++;
    cov_44vatrfhc().s[56]++;
    return !process.env[key];
  }));
  /* istanbul ignore next */
  cov_44vatrfhc().s[57]++;
  if (missing.length > 0) {
    /* istanbul ignore next */
    cov_44vatrfhc().b[16][0]++;
    cov_44vatrfhc().s[58]++;
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  } else
  /* istanbul ignore next */
  {
    cov_44vatrfhc().b[16][1]++;
  }
  cov_44vatrfhc().s[59]++;
  return true;
};

// Export environment for backward compatibility (to be removed)
export const env =
/* istanbul ignore next */
(cov_44vatrfhc().s[60]++, getEnv);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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