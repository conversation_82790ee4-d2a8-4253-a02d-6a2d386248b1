{"version": 3, "names": ["cov_1z5bmcajii", "actualCoverage", "NextResponse", "createClient", "verifySession", "z", "createClientSchema", "s", "object", "name", "string", "min", "max", "domain", "regex", "status", "enum", "optional", "settings", "record", "unknown", "updateClientSchema", "POST", "request", "f", "session", "b", "roles", "includes", "json", "error", "body", "parse", "details", "Error", "message", "client", "console"], "sources": ["route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { createClient, updateClient } from '../../../lib/clients';\nimport { verifySession } from '../../../lib/dal';\nimport { z } from 'zod';\n\n// Define schema for client creation\nconst createClientSchema = z.object({\n  name: z.string().min(2).max(255),\n  domain: z.string().min(3).max(255).regex(\n    /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\\.[a-zA-Z]{2,})+$/,\n    'Invalid domain format'\n  ),\n  status: z.enum(['active', 'inactive', 'pending']).optional(),\n  settings: z.record(z.unknown()).optional()\n});\n\n// Define schema for client updates\nconst updateClientSchema = z.object({\n  name: z.string().min(2).max(255).optional(),\n  domain: z.string().min(3).max(255).regex(\n    /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\\.[a-zA-Z]{2,})+$/,\n    'Invalid domain format'\n  ).optional(),\n  status: z.enum(['active', 'inactive', 'pending']).optional(),\n  settings: z.record(z.unknown()).optional()\n});\n\nexport async function POST(request: NextRequest) {\n  // Verify authentication and authorization\n  const session = await verifySession();\n  if (!session || !session.roles.includes('admin')) {\n    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n  }\n  \n  try {\n    const body = await request.json();\n    \n    // Validate input\n    try {\n      createClientSchema.parse(body);\n    } catch (error) {\n      return NextResponse.json({ \n        error: 'Invalid client data',\n        details: error instanceof Error ? error.message : 'Validation error'\n      }, { status: 400 });\n    }\n    \n    const client = await createClient({\n      name: body.name,\n      domain: body.domain,\n      status: body.status,\n      settings: body.settings\n    });\n    \n    return NextResponse.json(client, { status: 201 });\n  } catch (error) {\n    console.error('Error creating client:', error);\n    return NextResponse.json({ error: 'Failed to create client' }, { status: 500 });\n  }\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ,SAAsBE,YAAY,QAAQ,aAAa;AACvD,SAASC,YAAY,QAAsB,sBAAsB;AACjE,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,CAAC,QAAQ,KAAK;;AAEvB;AACA,MAAMC,kBAAkB;AAAA;AAAA,CAAAN,cAAA,GAAAO,CAAA,OAAGF,CAAC,CAACG,MAAM,CAAC;EAClCC,IAAI,EAAEJ,CAAC,CAACK,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,GAAG,CAAC;EAChCC,MAAM,EAAER,CAAC,CAACK,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,GAAG,CAAC,CAACE,KAAK,CACtC,+DAA+D,EAC/D,uBACF,CAAC;EACDC,MAAM,EAAEV,CAAC,CAACW,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;EAC5DC,QAAQ,EAAEb,CAAC,CAACc,MAAM,CAACd,CAAC,CAACe,OAAO,CAAC,CAAC,CAAC,CAACH,QAAQ,CAAC;AAC3C,CAAC,CAAC;;AAEF;AACA,MAAMI,kBAAkB;AAAA;AAAA,CAAArB,cAAA,GAAAO,CAAA,OAAGF,CAAC,CAACG,MAAM,CAAC;EAClCC,IAAI,EAAEJ,CAAC,CAACK,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,GAAG,CAAC,CAACK,QAAQ,CAAC,CAAC;EAC3CJ,MAAM,EAAER,CAAC,CAACK,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,GAAG,CAAC,CAACE,KAAK,CACtC,+DAA+D,EAC/D,uBACF,CAAC,CAACG,QAAQ,CAAC,CAAC;EACZF,MAAM,EAAEV,CAAC,CAACW,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;EAC5DC,QAAQ,EAAEb,CAAC,CAACc,MAAM,CAACd,CAAC,CAACe,OAAO,CAAC,CAAC,CAAC,CAACH,QAAQ,CAAC;AAC3C,CAAC,CAAC;AAEF,OAAO,eAAeK,IAAIA,CAACC,OAAoB,EAAE;EAAA;EAAAvB,cAAA,GAAAwB,CAAA;EAC/C;EACA,MAAMC,OAAO;EAAA;EAAA,CAAAzB,cAAA,GAAAO,CAAA,OAAG,MAAMH,aAAa,CAAC,CAAC;EAAC;EAAAJ,cAAA,GAAAO,CAAA;EACtC;EAAI;EAAA,CAAAP,cAAA,GAAA0B,CAAA,WAACD,OAAO;EAAA;EAAA,CAAAzB,cAAA,GAAA0B,CAAA,UAAI,CAACD,OAAO,CAACE,KAAK,CAACC,QAAQ,CAAC,OAAO,CAAC,GAAE;IAAA;IAAA5B,cAAA,GAAA0B,CAAA;IAAA1B,cAAA,GAAAO,CAAA;IAChD,OAAOL,YAAY,CAAC2B,IAAI,CAAC;MAAEC,KAAK,EAAE;IAAe,CAAC,EAAE;MAAEf,MAAM,EAAE;IAAI,CAAC,CAAC;EACtE,CAAC;EAAA;EAAA;IAAAf,cAAA,GAAA0B,CAAA;EAAA;EAAA1B,cAAA,GAAAO,CAAA;EAED,IAAI;IACF,MAAMwB,IAAI;IAAA;IAAA,CAAA/B,cAAA,GAAAO,CAAA,OAAG,MAAMgB,OAAO,CAACM,IAAI,CAAC,CAAC;;IAEjC;IAAA;IAAA7B,cAAA,GAAAO,CAAA;IACA,IAAI;MAAA;MAAAP,cAAA,GAAAO,CAAA;MACFD,kBAAkB,CAAC0B,KAAK,CAACD,IAAI,CAAC;IAChC,CAAC,CAAC,OAAOD,KAAK,EAAE;MAAA;MAAA9B,cAAA,GAAAO,CAAA;MACd,OAAOL,YAAY,CAAC2B,IAAI,CAAC;QACvBC,KAAK,EAAE,qBAAqB;QAC5BG,OAAO,EAAEH,KAAK,YAAYI,KAAK;QAAA;QAAA,CAAAlC,cAAA,GAAA0B,CAAA,UAAGI,KAAK,CAACK,OAAO;QAAA;QAAA,CAAAnC,cAAA,GAAA0B,CAAA,UAAG,kBAAkB;MACtE,CAAC,EAAE;QAAEX,MAAM,EAAE;MAAI,CAAC,CAAC;IACrB;IAEA,MAAMqB,MAAM;IAAA;IAAA,CAAApC,cAAA,GAAAO,CAAA,QAAG,MAAMJ,YAAY,CAAC;MAChCM,IAAI,EAAEsB,IAAI,CAACtB,IAAI;MACfI,MAAM,EAAEkB,IAAI,CAAClB,MAAM;MACnBE,MAAM,EAAEgB,IAAI,CAAChB,MAAM;MACnBG,QAAQ,EAAEa,IAAI,CAACb;IACjB,CAAC,CAAC;IAAC;IAAAlB,cAAA,GAAAO,CAAA;IAEH,OAAOL,YAAY,CAAC2B,IAAI,CAACO,MAAM,EAAE;MAAErB,MAAM,EAAE;IAAI,CAAC,CAAC;EACnD,CAAC,CAAC,OAAOe,KAAK,EAAE;IAAA;IAAA9B,cAAA,GAAAO,CAAA;IACd8B,OAAO,CAACP,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAAC;IAAA9B,cAAA,GAAAO,CAAA;IAC/C,OAAOL,YAAY,CAAC2B,IAAI,CAAC;MAAEC,KAAK,EAAE;IAA0B,CAAC,EAAE;MAAEf,MAAM,EAAE;IAAI,CAAC,CAAC;EACjF;AACF", "ignoreList": []}