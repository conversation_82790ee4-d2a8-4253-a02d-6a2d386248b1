4d51d2f3cc766e3986887d10570988fe
/* istanbul ignore next */
import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
const _excluded = ["total_count"];
function cov_1win19t05x() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\users\\route.ts";
  var hash = "310c054491770ab0dccd1671eb38b79747362a4d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\users\\route.ts",
    statementMap: {
      "0": {
        start: {
          line: 47,
          column: 19
        },
        end: {
          line: 130,
          column: 2
        }
      },
      "1": {
        start: {
          line: 49,
          column: 21
        },
        end: {
          line: 49,
          column: 40
        }
      },
      "2": {
        start: {
          line: 50,
          column: 2
        },
        end: {
          line: 57,
          column: 3
        }
      },
      "3": {
        start: {
          line: 51,
          column: 24
        },
        end: {
          line: 51,
          column: 75
        }
      },
      "4": {
        start: {
          line: 52,
          column: 22
        },
        end: {
          line: 52,
          column: 61
        }
      },
      "5": {
        start: {
          line: 54,
          column: 4
        },
        end: {
          line: 56,
          column: 5
        }
      },
      "6": {
        start: {
          line: 55,
          column: 6
        },
        end: {
          line: 55,
          column: 39
        }
      },
      "7": {
        start: {
          line: 60,
          column: 21
        },
        end: {
          line: 60,
          column: 65
        }
      },
      "8": {
        start: {
          line: 61,
          column: 2
        },
        end: {
          line: 63,
          column: 3
        }
      },
      "9": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 62,
          column: 32
        }
      },
      "10": {
        start: {
          line: 65,
          column: 18
        },
        end: {
          line: 65,
          column: 37
        }
      },
      "11": {
        start: {
          line: 68,
          column: 27
        },
        end: {
          line: 68,
          column: 47
        }
      },
      "12": {
        start: {
          line: 69,
          column: 26
        },
        end: {
          line: 69,
          column: 77
        }
      },
      "13": {
        start: {
          line: 71,
          column: 2
        },
        end: {
          line: 73,
          column: 3
        }
      },
      "14": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 72,
          column: 36
        }
      },
      "15": {
        start: {
          line: 75,
          column: 63
        },
        end: {
          line: 75,
          column: 83
        }
      },
      "16": {
        start: {
          line: 78,
          column: 17
        },
        end: {
          line: 78,
          column: 35
        }
      },
      "17": {
        start: {
          line: 81,
          column: 28
        },
        end: {
          line: 81,
          column: 73
        }
      },
      "18": {
        start: {
          line: 82,
          column: 20
        },
        end: {
          line: 82,
          column: 84
        }
      },
      "19": {
        start: {
          line: 84,
          column: 16
        },
        end: {
          line: 99,
          column: 3
        }
      },
      "20": {
        start: {
          line: 101,
          column: 17
        },
        end: {
          line: 105,
          column: 3
        }
      },
      "21": {
        start: {
          line: 107,
          column: 2
        },
        end: {
          line: 113,
          column: 3
        }
      },
      "22": {
        start: {
          line: 108,
          column: 4
        },
        end: {
          line: 112,
          column: 6
        }
      },
      "23": {
        start: {
          line: 115,
          column: 16
        },
        end: {
          line: 115,
          column: 33
        }
      },
      "24": {
        start: {
          line: 116,
          column: 21
        },
        end: {
          line: 116,
          column: 85
        }
      },
      "25": {
        start: {
          line: 117,
          column: 21
        },
        end: {
          line: 117,
          column: 50
        }
      },
      "26": {
        start: {
          line: 119,
          column: 2
        },
        end: {
          line: 129,
          column: 37
        }
      },
      "27": {
        start: {
          line: 120,
          column: 51
        },
        end: {
          line: 120,
          column: 55
        }
      },
      "28": {
        start: {
          line: 133,
          column: 20
        },
        end: {
          line: 219,
          column: 2
        }
      },
      "29": {
        start: {
          line: 135,
          column: 21
        },
        end: {
          line: 135,
          column: 65
        }
      },
      "30": {
        start: {
          line: 136,
          column: 2
        },
        end: {
          line: 138,
          column: 3
        }
      },
      "31": {
        start: {
          line: 137,
          column: 4
        },
        end: {
          line: 137,
          column: 32
        }
      },
      "32": {
        start: {
          line: 140,
          column: 18
        },
        end: {
          line: 140,
          column: 37
        }
      },
      "33": {
        start: {
          line: 143,
          column: 22
        },
        end: {
          line: 143,
          column: 62
        }
      },
      "34": {
        start: {
          line: 144,
          column: 20
        },
        end: {
          line: 144,
          column: 58
        }
      },
      "35": {
        start: {
          line: 146,
          column: 2
        },
        end: {
          line: 148,
          column: 3
        }
      },
      "36": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 147,
          column: 37
        }
      },
      "37": {
        start: {
          line: 151,
          column: 25
        },
        end: {
          line: 151,
          column: 77
        }
      },
      "38": {
        start: {
          line: 153,
          column: 2
        },
        end: {
          line: 155,
          column: 3
        }
      },
      "39": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 154,
          column: 35
        }
      },
      "40": {
        start: {
          line: 157,
          column: 35
        },
        end: {
          line: 157,
          column: 54
        }
      },
      "41": {
        start: {
          line: 160,
          column: 28
        },
        end: {
          line: 160,
          column: 67
        }
      },
      "42": {
        start: {
          line: 161,
          column: 23
        },
        end: {
          line: 161,
          column: 84
        }
      },
      "43": {
        start: {
          line: 163,
          column: 2
        },
        end: {
          line: 169,
          column: 3
        }
      },
      "44": {
        start: {
          line: 164,
          column: 4
        },
        end: {
          line: 168,
          column: 6
        }
      },
      "45": {
        start: {
          line: 172,
          column: 22
        },
        end: {
          line: 190,
          column: 3
        }
      },
      "46": {
        start: {
          line: 192,
          column: 23
        },
        end: {
          line: 201,
          column: 3
        }
      },
      "47": {
        start: {
          line: 203,
          column: 2
        },
        end: {
          line: 209,
          column: 3
        }
      },
      "48": {
        start: {
          line: 204,
          column: 4
        },
        end: {
          line: 208,
          column: 6
        }
      },
      "49": {
        start: {
          line: 212,
          column: 2
        },
        end: {
          line: 212,
          column: 69
        }
      },
      "50": {
        start: {
          line: 214,
          column: 2
        },
        end: {
          line: 218,
          column: 4
        }
      },
      "51": {
        start: {
          line: 223,
          column: 2
        },
        end: {
          line: 231,
          column: 5
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 47,
            column: 37
          },
          end: {
            line: 47,
            column: 38
          }
        },
        loc: {
          start: {
            line: 47,
            column: 69
          },
          end: {
            line: 130,
            column: 1
          }
        },
        line: 47
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 120,
            column: 21
          },
          end: {
            line: 120,
            column: 22
          }
        },
        loc: {
          start: {
            line: 120,
            column: 51
          },
          end: {
            line: 120,
            column: 55
          }
        },
        line: 120
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 133,
            column: 38
          },
          end: {
            line: 133,
            column: 39
          }
        },
        loc: {
          start: {
            line: 133,
            column: 70
          },
          end: {
            line: 219,
            column: 1
          }
        },
        line: 133
      },
      "3": {
        name: "OPTIONS",
        decl: {
          start: {
            line: 222,
            column: 22
          },
          end: {
            line: 222,
            column: 29
          }
        },
        loc: {
          start: {
            line: 222,
            column: 32
          },
          end: {
            line: 232,
            column: 1
          }
        },
        line: 222
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 50,
            column: 2
          },
          end: {
            line: 57,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 2
          },
          end: {
            line: 57,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "1": {
        loc: {
          start: {
            line: 54,
            column: 4
          },
          end: {
            line: 56,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 54,
            column: 4
          },
          end: {
            line: 56,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 54
      },
      "2": {
        loc: {
          start: {
            line: 61,
            column: 2
          },
          end: {
            line: 63,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 61,
            column: 2
          },
          end: {
            line: 63,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 61
      },
      "3": {
        loc: {
          start: {
            line: 71,
            column: 2
          },
          end: {
            line: 73,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 71,
            column: 2
          },
          end: {
            line: 73,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 71
      },
      "4": {
        loc: {
          start: {
            line: 82,
            column: 20
          },
          end: {
            line: 82,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 82,
            column: 63
          },
          end: {
            line: 82,
            column: 69
          }
        }, {
          start: {
            line: 82,
            column: 72
          },
          end: {
            line: 82,
            column: 84
          }
        }],
        line: 82
      },
      "5": {
        loc: {
          start: {
            line: 82,
            column: 47
          },
          end: {
            line: 82,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 82,
            column: 47
          },
          end: {
            line: 82,
            column: 53
          }
        }, {
          start: {
            line: 82,
            column: 57
          },
          end: {
            line: 82,
            column: 59
          }
        }],
        line: 82
      },
      "6": {
        loc: {
          start: {
            line: 107,
            column: 2
          },
          end: {
            line: 113,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 107,
            column: 2
          },
          end: {
            line: 113,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 107
      },
      "7": {
        loc: {
          start: {
            line: 115,
            column: 16
          },
          end: {
            line: 115,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 115,
            column: 16
          },
          end: {
            line: 115,
            column: 27
          }
        }, {
          start: {
            line: 115,
            column: 31
          },
          end: {
            line: 115,
            column: 33
          }
        }],
        line: 115
      },
      "8": {
        loc: {
          start: {
            line: 116,
            column: 21
          },
          end: {
            line: 116,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 116,
            column: 40
          },
          end: {
            line: 116,
            column: 81
          }
        }, {
          start: {
            line: 116,
            column: 84
          },
          end: {
            line: 116,
            column: 85
          }
        }],
        line: 116
      },
      "9": {
        loc: {
          start: {
            line: 136,
            column: 2
          },
          end: {
            line: 138,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 136,
            column: 2
          },
          end: {
            line: 138,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 136
      },
      "10": {
        loc: {
          start: {
            line: 146,
            column: 2
          },
          end: {
            line: 148,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 146,
            column: 2
          },
          end: {
            line: 148,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 146
      },
      "11": {
        loc: {
          start: {
            line: 153,
            column: 2
          },
          end: {
            line: 155,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 153,
            column: 2
          },
          end: {
            line: 155,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 153
      },
      "12": {
        loc: {
          start: {
            line: 163,
            column: 2
          },
          end: {
            line: 169,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 163,
            column: 2
          },
          end: {
            line: 169,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 163
      },
      "13": {
        loc: {
          start: {
            line: 163,
            column: 6
          },
          end: {
            line: 163,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 163,
            column: 6
          },
          end: {
            line: 163,
            column: 26
          }
        }, {
          start: {
            line: 163,
            column: 30
          },
          end: {
            line: 163,
            column: 47
          }
        }],
        line: 163
      },
      "14": {
        loc: {
          start: {
            line: 196,
            column: 6
          },
          end: {
            line: 196,
            column: 27
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 196,
            column: 6
          },
          end: {
            line: 196,
            column: 19
          }
        }, {
          start: {
            line: 196,
            column: 23
          },
          end: {
            line: 196,
            column: 27
          }
        }],
        line: 196
      },
      "15": {
        loc: {
          start: {
            line: 197,
            column: 6
          },
          end: {
            line: 197,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 197,
            column: 6
          },
          end: {
            line: 197,
            column: 25
          }
        }, {
          start: {
            line: 197,
            column: 29
          },
          end: {
            line: 197,
            column: 33
          }
        }],
        line: 197
      },
      "16": {
        loc: {
          start: {
            line: 198,
            column: 6
          },
          end: {
            line: 198,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 198,
            column: 6
          },
          end: {
            line: 198,
            column: 26
          }
        }, {
          start: {
            line: 198,
            column: 30
          },
          end: {
            line: 198,
            column: 34
          }
        }],
        line: 198
      },
      "17": {
        loc: {
          start: {
            line: 203,
            column: 2
          },
          end: {
            line: 209,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 203,
            column: 2
          },
          end: {
            line: 209,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 203
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "310c054491770ab0dccd1671eb38b79747362a4d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1win19t05x = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1win19t05x();
/**
 * Users API Route
 * 
 * This route demonstrates best practices for API implementation including:
 * - Secure authentication and authorization
 * - Input validation with Zod schemas
 * - Standardized error handling
 * - Proper database operations
 * - Rate limiting
 * - Security headers
 */

import { requireAuth, requireRole, checkRateLimit, getRateLimitIdentifier } from '@/lib/auth-middleware';
import { createSuccessResponse, createErrorResponse, createRateLimitResponse, ApiErrorCode, HttpStatus, withErrorHandling } from '@/lib/api-response';
import { validateRequestBody, validateQueryParams, userCreateSchema, paginationSchema } from '@/lib/validation';
import { executeQuery, executeQuerySingle } from '@/lib/database';

// User interface

// GET /api/users - List users with pagination
export const GET =
/* istanbul ignore next */
(cov_1win19t05x().s[0]++, withErrorHandling(async request => {
  /* istanbul ignore next */
  cov_1win19t05x().f[0]++;
  // Rate limiting
  const authResult =
  /* istanbul ignore next */
  (cov_1win19t05x().s[1]++, await requireAuth());
  /* istanbul ignore next */
  cov_1win19t05x().s[2]++;
  if (authResult.success) {
    /* istanbul ignore next */
    cov_1win19t05x().b[0][0]++;
    const rateLimitId =
    /* istanbul ignore next */
    (cov_1win19t05x().s[3]++, getRateLimitIdentifier(request, authResult.session));
    const rateLimit =
    /* istanbul ignore next */
    (cov_1win19t05x().s[4]++, checkRateLimit(rateLimitId, 100, 60000)); // 100 requests per minute
    /* istanbul ignore next */
    cov_1win19t05x().s[5]++;
    if (!rateLimit.allowed) {
      /* istanbul ignore next */
      cov_1win19t05x().b[1][0]++;
      cov_1win19t05x().s[6]++;
      return createRateLimitResponse();
    } else
    /* istanbul ignore next */
    {
      cov_1win19t05x().b[1][1]++;
    }
  } else
  /* istanbul ignore next */
  {
    cov_1win19t05x().b[0][1]++;
  }

  // Verify authentication and admin role
  const roleResult =
  /* istanbul ignore next */
  (cov_1win19t05x().s[7]++, await requireRole(['admin', 'user_manager']));
  /* istanbul ignore next */
  cov_1win19t05x().s[8]++;
  if (!roleResult.success) {
    /* istanbul ignore next */
    cov_1win19t05x().b[2][0]++;
    cov_1win19t05x().s[9]++;
    return roleResult.response;
  } else
  /* istanbul ignore next */
  {
    cov_1win19t05x().b[2][1]++;
  }
  const session =
  /* istanbul ignore next */
  (cov_1win19t05x().s[10]++, roleResult.session);

  // Validate query parameters
  const {
    searchParams
  } =
  /* istanbul ignore next */
  (cov_1win19t05x().s[11]++, new URL(request.url));
  const queryValidation =
  /* istanbul ignore next */
  (cov_1win19t05x().s[12]++, validateQueryParams(searchParams, paginationSchema));
  /* istanbul ignore next */
  cov_1win19t05x().s[13]++;
  if (!queryValidation.success) {
    /* istanbul ignore next */
    cov_1win19t05x().b[3][0]++;
    cov_1win19t05x().s[14]++;
    return queryValidation.response;
  } else
  /* istanbul ignore next */
  {
    cov_1win19t05x().b[3][1]++;
  }
  const {
    page,
    limit,
    sortBy,
    sortOrder
  } =
  /* istanbul ignore next */
  (cov_1win19t05x().s[15]++, queryValidation.data);

  // Calculate offset for pagination
  const offset =
  /* istanbul ignore next */
  (cov_1win19t05x().s[16]++, (page - 1) * limit);

  // Build query with proper sorting
  const allowedSortFields =
  /* istanbul ignore next */
  (cov_1win19t05x().s[17]++, ['email', 'name', 'created_at', 'last_login']);
  const sortField =
  /* istanbul ignore next */
  (cov_1win19t05x().s[18]++, allowedSortFields.includes(
  /* istanbul ignore next */
  (cov_1win19t05x().b[5][0]++, sortBy) ||
  /* istanbul ignore next */
  (cov_1win19t05x().b[5][1]++, '')) ?
  /* istanbul ignore next */
  (cov_1win19t05x().b[4][0]++, sortBy) :
  /* istanbul ignore next */
  (cov_1win19t05x().b[4][1]++, 'created_at'));
  const query =
  /* istanbul ignore next */
  (cov_1win19t05x().s[19]++, `
    SELECT 
      id,
      email,
      name,
      given_name,
      family_name,
      roles,
      created_at,
      updated_at,
      last_login,
      COUNT(*) OVER() as total_count
    FROM users
    ORDER BY ${sortField} ${sortOrder.toUpperCase()}
    LIMIT $1 OFFSET $2
  `);
  const result =
  /* istanbul ignore next */
  (cov_1win19t05x().s[20]++, await executeQuery(query, [limit, offset], {
    timeout: 10000
  }));
  /* istanbul ignore next */
  cov_1win19t05x().s[21]++;
  if (!result.success) {
    /* istanbul ignore next */
    cov_1win19t05x().b[6][0]++;
    cov_1win19t05x().s[22]++;
    return createErrorResponse('Failed to fetch users', ApiErrorCode.DATABASE_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
  } else
  /* istanbul ignore next */
  {
    cov_1win19t05x().b[6][1]++;
  }
  const users =
  /* istanbul ignore next */
  (cov_1win19t05x().s[23]++,
  /* istanbul ignore next */
  (cov_1win19t05x().b[7][0]++, result.data) ||
  /* istanbul ignore next */
  (cov_1win19t05x().b[7][1]++, []));
  const totalCount =
  /* istanbul ignore next */
  (cov_1win19t05x().s[24]++, users.length > 0 ?
  /* istanbul ignore next */
  (cov_1win19t05x().b[8][0]++, parseInt(users[0].total_count.toString())) :
  /* istanbul ignore next */
  (cov_1win19t05x().b[8][1]++, 0));
  const totalPages =
  /* istanbul ignore next */
  (cov_1win19t05x().s[25]++, Math.ceil(totalCount / limit));
  /* istanbul ignore next */
  cov_1win19t05x().s[26]++;
  return createSuccessResponse({
    users: users.map(
    /* istanbul ignore next */
    _ref => {
      /* istanbul ignore next */
      let {
          total_count
        } = _ref,
        user = _objectWithoutProperties(_ref, _excluded);
      /* istanbul ignore next */
      cov_1win19t05x().f[1]++;
      cov_1win19t05x().s[27]++;
      return user;
    }),
    pagination: {
      page,
      limit,
      totalCount,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  }, 'Users retrieved successfully');
}));

// POST /api/users - Create a new user
export const POST =
/* istanbul ignore next */
(cov_1win19t05x().s[28]++, withErrorHandling(async request => {
  /* istanbul ignore next */
  cov_1win19t05x().f[2]++;
  // Verify authentication and admin role
  const roleResult =
  /* istanbul ignore next */
  (cov_1win19t05x().s[29]++, await requireRole(['admin', 'user_manager']));
  /* istanbul ignore next */
  cov_1win19t05x().s[30]++;
  if (!roleResult.success) {
    /* istanbul ignore next */
    cov_1win19t05x().b[9][0]++;
    cov_1win19t05x().s[31]++;
    return roleResult.response;
  } else
  /* istanbul ignore next */
  {
    cov_1win19t05x().b[9][1]++;
  }
  const session =
  /* istanbul ignore next */
  (cov_1win19t05x().s[32]++, roleResult.session);

  // Rate limiting for user creation (more restrictive)
  const rateLimitId =
  /* istanbul ignore next */
  (cov_1win19t05x().s[33]++, getRateLimitIdentifier(request, session));
  const rateLimit =
  /* istanbul ignore next */
  (cov_1win19t05x().s[34]++, checkRateLimit(rateLimitId, 10, 60000)); // 10 user creations per minute
  /* istanbul ignore next */
  cov_1win19t05x().s[35]++;
  if (!rateLimit.allowed) {
    /* istanbul ignore next */
    cov_1win19t05x().b[10][0]++;
    cov_1win19t05x().s[36]++;
    return createRateLimitResponse();
  } else
  /* istanbul ignore next */
  {
    cov_1win19t05x().b[10][1]++;
  }

  // Validate request body
  const bodyValidation =
  /* istanbul ignore next */
  (cov_1win19t05x().s[37]++, await validateRequestBody(request, userCreateSchema));
  /* istanbul ignore next */
  cov_1win19t05x().s[38]++;
  if (!bodyValidation.success) {
    /* istanbul ignore next */
    cov_1win19t05x().b[11][0]++;
    cov_1win19t05x().s[39]++;
    return bodyValidation.response;
  } else
  /* istanbul ignore next */
  {
    cov_1win19t05x().b[11][1]++;
  }
  const userData =
  /* istanbul ignore next */
  (cov_1win19t05x().s[40]++, bodyValidation.data);

  // Check if user already exists
  const existingUserQuery =
  /* istanbul ignore next */
  (cov_1win19t05x().s[41]++, 'SELECT id FROM users WHERE email = $1');
  const existingUser =
  /* istanbul ignore next */
  (cov_1win19t05x().s[42]++, await executeQuerySingle(existingUserQuery, [userData.email]));
  /* istanbul ignore next */
  cov_1win19t05x().s[43]++;
  if (
  /* istanbul ignore next */
  (cov_1win19t05x().b[13][0]++, existingUser.success) &&
  /* istanbul ignore next */
  (cov_1win19t05x().b[13][1]++, existingUser.data)) {
    /* istanbul ignore next */
    cov_1win19t05x().b[12][0]++;
    cov_1win19t05x().s[44]++;
    return createErrorResponse('User with this email already exists', ApiErrorCode.VALIDATION_ERROR, HttpStatus.CONFLICT);
  } else
  /* istanbul ignore next */
  {
    cov_1win19t05x().b[12][1]++;
  }

  // Create new user
  const insertQuery =
  /* istanbul ignore next */
  (cov_1win19t05x().s[45]++, `
    INSERT INTO users (
      email, 
      name, 
      given_name, 
      family_name, 
      roles,
      created_at
    ) 
    VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
    RETURNING 
      id,
      email,
      name,
      given_name,
      family_name,
      roles,
      created_at
  `);
  const insertResult =
  /* istanbul ignore next */
  (cov_1win19t05x().s[46]++, await executeQuerySingle(insertQuery, [userData.email,
  /* istanbul ignore next */
  (cov_1win19t05x().b[14][0]++, userData.name) ||
  /* istanbul ignore next */
  (cov_1win19t05x().b[14][1]++, null),
  /* istanbul ignore next */
  (cov_1win19t05x().b[15][0]++, userData.given_name) ||
  /* istanbul ignore next */
  (cov_1win19t05x().b[15][1]++, null),
  /* istanbul ignore next */
  (cov_1win19t05x().b[16][0]++, userData.family_name) ||
  /* istanbul ignore next */
  (cov_1win19t05x().b[16][1]++, null), JSON.stringify(userData.roles)]));
  /* istanbul ignore next */
  cov_1win19t05x().s[47]++;
  if (!insertResult.success) {
    /* istanbul ignore next */
    cov_1win19t05x().b[17][0]++;
    cov_1win19t05x().s[48]++;
    return createErrorResponse('Failed to create user', ApiErrorCode.DATABASE_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
  } else
  /* istanbul ignore next */
  {
    cov_1win19t05x().b[17][1]++;
  }

  // Log user creation for audit
  cov_1win19t05x().s[49]++;
  console.log(`User created: ${userData.email} by ${session.email}`);
  /* istanbul ignore next */
  cov_1win19t05x().s[50]++;
  return createSuccessResponse(insertResult.data, 'User created successfully', HttpStatus.CREATED);
}));

// OPTIONS /api/users - CORS preflight
export async function OPTIONS() {
  /* istanbul ignore next */
  cov_1win19t05x().f[3]++;
  cov_1win19t05x().s[51]++;
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400'
    }
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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