{"version": 3, "names": ["cov_w3tbhl4ew", "actualCoverage", "getClientByEmailDomain", "execute<PERSON>uery", "schemaExists", "requireAuth", "createSuccessResponse", "createErrorResponse", "ApiErrorCode", "HttpStatus", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GET", "s", "f", "authResult", "success", "b", "response", "session", "clientResult", "email", "statusCode", "errorCode", "NOT_FOUND", "INTERNAL_SERVER_ERROR", "apiErrorCode", "DATABASE_ERROR", "error", "tenant", "client", "schemaReady", "tenantSchema", "stats", "totalRenewals", "renewalsDue", "vendors", "annualSpend", "queries", "query", "key", "result", "schema", "data", "length", "value", "parseInt", "total_renewals", "renewals_due", "amount", "parseFloat", "annual_spend", "toLocaleString", "console", "log"], "sources": ["route.ts"], "sourcesContent": ["import { getClientByEmailDomain } from '@/lib/clients';\nimport { executeQuery, schemaExists } from '@/lib/database';\nimport { requireAuth } from '@/lib/auth-middleware';\nimport {\n  createSuccessResponse,\n  createErrorResponse,\n  ApiErrorCode,\n  HttpStatus,\n  withErrorHandling\n} from '@/lib/api-response';\n\n// Dashboard stats interface\ninterface DashboardStats {\n  totalRenewals: number;\n  renewalsDue: number;\n  vendors: number;\n  annualSpend: string;\n}\n\nexport const GET = withErrorHandling(async () => {\n  // Verify authentication\n  const authResult = await requireAuth();\n  if (!authResult.success) {\n    return authResult.response!;\n  }\n\n  const session = authResult.session!;\n\n  // Get tenant context using the new secure method\n  const clientResult = await getClientByEmailDomain(session.email);\n\n  if (!clientResult.success) {\n    const statusCode = clientResult.errorCode === 'NOT_FOUND' ? HttpStatus.NOT_FOUND : HttpStatus.INTERNAL_SERVER_ERROR;\n    const apiErrorCode = clientResult.errorCode === 'NOT_FOUND' ? ApiErrorCode.NOT_FOUND : ApiErrorCode.DATABASE_ERROR;\n\n    return createErrorResponse(\n      clientResult.error || 'Failed to fetch tenant information',\n      apiErrorCode,\n      statusCode\n    );\n  }\n\n  const tenant = clientResult.client!;\n\n  // Check if tenant schema exists\n  const schemaReady = await schemaExists(tenant.tenantSchema);\n\n  let stats: DashboardStats = {\n    totalRenewals: 0,\n    renewalsDue: 0,\n    vendors: 0,\n    annualSpend: '$0'\n  };\n\n  if (schemaReady) {\n    // Query tenant schema for actual data\n    const queries = [\n      {\n        query: `SELECT COUNT(*) as total_renewals FROM \"${tenant.tenantSchema}\".\"Renewals\" WHERE \"Active\" = true`,\n        key: 'totalRenewals'\n      },\n      {\n        query: `SELECT COUNT(*) as renewals_due FROM \"${tenant.tenantSchema}\".\"Renewals\" WHERE \"Active\" = true AND \"DueDate\" <= CURRENT_DATE + INTERVAL '30 days'`,\n        key: 'renewalsDue'\n      },\n      {\n        query: `SELECT COUNT(DISTINCT \"VendorID\") as vendors FROM \"${tenant.tenantSchema}\".\"Renewals\" WHERE \"Active\" = true`,\n        key: 'vendors'\n      },\n      {\n        query: `SELECT COALESCE(SUM(\"AnnualCost\"), 0) as annual_spend FROM \"${tenant.tenantSchema}\".\"Renewals\" WHERE \"Active\" = true`,\n        key: 'annualSpend'\n      }\n    ];\n\n    // Execute queries with proper error handling\n    for (const { query, key } of queries) {\n      const result = await executeQuery(query, [], { schema: tenant.tenantSchema });\n\n      if (result.success && result.data && result.data.length > 0) {\n        const value = result.data[0];\n        switch (key) {\n          case 'totalRenewals':\n            stats.totalRenewals = parseInt(value.total_renewals || '0');\n            break;\n          case 'renewalsDue':\n            stats.renewalsDue = parseInt(value.renewals_due || '0');\n            break;\n          case 'vendors':\n            stats.vendors = parseInt(value.vendors || '0');\n            break;\n          case 'annualSpend':\n            const amount = parseFloat(value.annual_spend || '0');\n            stats.annualSpend = `$${amount.toLocaleString()}`;\n            break;\n        }\n      }\n    }\n  } else {\n    console.log(`Tenant schema ${tenant.tenantSchema} not ready yet, using mock data`);\n    // Return mock data as fallback\n    stats = {\n      totalRenewals: 14,\n      renewalsDue: 3,\n      vendors: 9,\n      annualSpend: '$817,340'\n    };\n  }\n\n  return createSuccessResponse(stats, 'Dashboard statistics retrieved successfully');\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAfZ,SAASE,sBAAsB,QAAQ,eAAe;AACtD,SAASC,YAAY,EAAEC,YAAY,QAAQ,gBAAgB;AAC3D,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SACEC,qBAAqB,EACrBC,mBAAmB,EACnBC,YAAY,EACZC,UAAU,EACVC,iBAAiB,QACZ,oBAAoB;;AAE3B;;AAQA,OAAO,MAAMC,GAAG;AAAA;AAAA,CAAAX,aAAA,GAAAY,CAAA,OAAGF,iBAAiB,CAAC,YAAY;EAAA;EAAAV,aAAA,GAAAa,CAAA;EAC/C;EACA,MAAMC,UAAU;EAAA;EAAA,CAAAd,aAAA,GAAAY,CAAA,OAAG,MAAMP,WAAW,CAAC,CAAC;EAAC;EAAAL,aAAA,GAAAY,CAAA;EACvC,IAAI,CAACE,UAAU,CAACC,OAAO,EAAE;IAAA;IAAAf,aAAA,GAAAgB,CAAA;IAAAhB,aAAA,GAAAY,CAAA;IACvB,OAAOE,UAAU,CAACG,QAAQ;EAC5B,CAAC;EAAA;EAAA;IAAAjB,aAAA,GAAAgB,CAAA;EAAA;EAED,MAAME,OAAO;EAAA;EAAA,CAAAlB,aAAA,GAAAY,CAAA,OAAGE,UAAU,CAACI,OAAO,CAAC;;EAEnC;EACA,MAAMC,YAAY;EAAA;EAAA,CAAAnB,aAAA,GAAAY,CAAA,OAAG,MAAMV,sBAAsB,CAACgB,OAAO,CAACE,KAAK,CAAC;EAAC;EAAApB,aAAA,GAAAY,CAAA;EAEjE,IAAI,CAACO,YAAY,CAACJ,OAAO,EAAE;IAAA;IAAAf,aAAA,GAAAgB,CAAA;IACzB,MAAMK,UAAU;IAAA;IAAA,CAAArB,aAAA,GAAAY,CAAA,OAAGO,YAAY,CAACG,SAAS,KAAK,WAAW;IAAA;IAAA,CAAAtB,aAAA,GAAAgB,CAAA,UAAGP,UAAU,CAACc,SAAS;IAAA;IAAA,CAAAvB,aAAA,GAAAgB,CAAA,UAAGP,UAAU,CAACe,qBAAqB;IACnH,MAAMC,YAAY;IAAA;IAAA,CAAAzB,aAAA,GAAAY,CAAA,OAAGO,YAAY,CAACG,SAAS,KAAK,WAAW;IAAA;IAAA,CAAAtB,aAAA,GAAAgB,CAAA,UAAGR,YAAY,CAACe,SAAS;IAAA;IAAA,CAAAvB,aAAA,GAAAgB,CAAA,UAAGR,YAAY,CAACkB,cAAc;IAAC;IAAA1B,aAAA,GAAAY,CAAA;IAEnH,OAAOL,mBAAmB;IACxB;IAAA,CAAAP,aAAA,GAAAgB,CAAA,UAAAG,YAAY,CAACQ,KAAK;IAAA;IAAA,CAAA3B,aAAA,GAAAgB,CAAA,UAAI,oCAAoC,GAC1DS,YAAY,EACZJ,UACF,CAAC;EACH,CAAC;EAAA;EAAA;IAAArB,aAAA,GAAAgB,CAAA;EAAA;EAED,MAAMY,MAAM;EAAA;EAAA,CAAA5B,aAAA,GAAAY,CAAA,QAAGO,YAAY,CAACU,MAAM,CAAC;;EAEnC;EACA,MAAMC,WAAW;EAAA;EAAA,CAAA9B,aAAA,GAAAY,CAAA,QAAG,MAAMR,YAAY,CAACwB,MAAM,CAACG,YAAY,CAAC;EAE3D,IAAIC,KAAqB;EAAA;EAAA,CAAAhC,aAAA,GAAAY,CAAA,QAAG;IAC1BqB,aAAa,EAAE,CAAC;IAChBC,WAAW,EAAE,CAAC;IACdC,OAAO,EAAE,CAAC;IACVC,WAAW,EAAE;EACf,CAAC;EAAC;EAAApC,aAAA,GAAAY,CAAA;EAEF,IAAIkB,WAAW,EAAE;IAAA;IAAA9B,aAAA,GAAAgB,CAAA;IACf;IACA,MAAMqB,OAAO;IAAA;IAAA,CAAArC,aAAA,GAAAY,CAAA,QAAG,CACd;MACE0B,KAAK,EAAE,2CAA2CV,MAAM,CAACG,YAAY,oCAAoC;MACzGQ,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,yCAAyCV,MAAM,CAACG,YAAY,uFAAuF;MAC1JQ,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,sDAAsDV,MAAM,CAACG,YAAY,oCAAoC;MACpHQ,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,+DAA+DV,MAAM,CAACG,YAAY,oCAAoC;MAC7HQ,GAAG,EAAE;IACP,CAAC,CACF;;IAED;IAAA;IAAAvC,aAAA,GAAAY,CAAA;IACA,KAAK,MAAM;MAAE0B,KAAK;MAAEC;IAAI,CAAC,IAAIF,OAAO,EAAE;MACpC,MAAMG,MAAM;MAAA;MAAA,CAAAxC,aAAA,GAAAY,CAAA,QAAG,MAAMT,YAAY,CAACmC,KAAK,EAAE,EAAE,EAAE;QAAEG,MAAM,EAAEb,MAAM,CAACG;MAAa,CAAC,CAAC;MAAC;MAAA/B,aAAA,GAAAY,CAAA;MAE9E;MAAI;MAAA,CAAAZ,aAAA,GAAAgB,CAAA,UAAAwB,MAAM,CAACzB,OAAO;MAAA;MAAA,CAAAf,aAAA,GAAAgB,CAAA,UAAIwB,MAAM,CAACE,IAAI;MAAA;MAAA,CAAA1C,aAAA,GAAAgB,CAAA,UAAIwB,MAAM,CAACE,IAAI,CAACC,MAAM,GAAG,CAAC,GAAE;QAAA;QAAA3C,aAAA,GAAAgB,CAAA;QAC3D,MAAM4B,KAAK;QAAA;QAAA,CAAA5C,aAAA,GAAAY,CAAA,QAAG4B,MAAM,CAACE,IAAI,CAAC,CAAC,CAAC;QAAC;QAAA1C,aAAA,GAAAY,CAAA;QAC7B,QAAQ2B,GAAG;UACT,KAAK,eAAe;YAAA;YAAAvC,aAAA,GAAAgB,CAAA;YAAAhB,aAAA,GAAAY,CAAA;YAClBoB,KAAK,CAACC,aAAa,GAAGY,QAAQ;YAAC;YAAA,CAAA7C,aAAA,GAAAgB,CAAA,UAAA4B,KAAK,CAACE,cAAc;YAAA;YAAA,CAAA9C,aAAA,GAAAgB,CAAA,UAAI,GAAG,EAAC;YAAC;YAAAhB,aAAA,GAAAY,CAAA;YAC5D;UACF,KAAK,aAAa;YAAA;YAAAZ,aAAA,GAAAgB,CAAA;YAAAhB,aAAA,GAAAY,CAAA;YAChBoB,KAAK,CAACE,WAAW,GAAGW,QAAQ;YAAC;YAAA,CAAA7C,aAAA,GAAAgB,CAAA,WAAA4B,KAAK,CAACG,YAAY;YAAA;YAAA,CAAA/C,aAAA,GAAAgB,CAAA,WAAI,GAAG,EAAC;YAAC;YAAAhB,aAAA,GAAAY,CAAA;YACxD;UACF,KAAK,SAAS;YAAA;YAAAZ,aAAA,GAAAgB,CAAA;YAAAhB,aAAA,GAAAY,CAAA;YACZoB,KAAK,CAACG,OAAO,GAAGU,QAAQ;YAAC;YAAA,CAAA7C,aAAA,GAAAgB,CAAA,WAAA4B,KAAK,CAACT,OAAO;YAAA;YAAA,CAAAnC,aAAA,GAAAgB,CAAA,WAAI,GAAG,EAAC;YAAC;YAAAhB,aAAA,GAAAY,CAAA;YAC/C;UACF,KAAK,aAAa;YAAA;YAAAZ,aAAA,GAAAgB,CAAA;YAChB,MAAMgC,MAAM;YAAA;YAAA,CAAAhD,aAAA,GAAAY,CAAA,QAAGqC,UAAU;YAAC;YAAA,CAAAjD,aAAA,GAAAgB,CAAA,WAAA4B,KAAK,CAACM,YAAY;YAAA;YAAA,CAAAlD,aAAA,GAAAgB,CAAA,WAAI,GAAG,EAAC;YAAC;YAAAhB,aAAA,GAAAY,CAAA;YACrDoB,KAAK,CAACI,WAAW,GAAG,IAAIY,MAAM,CAACG,cAAc,CAAC,CAAC,EAAE;YAAC;YAAAnD,aAAA,GAAAY,CAAA;YAClD;QACJ;MACF,CAAC;MAAA;MAAA;QAAAZ,aAAA,GAAAgB,CAAA;MAAA;IACH;EACF,CAAC,MAAM;IAAA;IAAAhB,aAAA,GAAAgB,CAAA;IAAAhB,aAAA,GAAAY,CAAA;IACLwC,OAAO,CAACC,GAAG,CAAC,iBAAiBzB,MAAM,CAACG,YAAY,iCAAiC,CAAC;IAClF;IAAA;IAAA/B,aAAA,GAAAY,CAAA;IACAoB,KAAK,GAAG;MACNC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,CAAC;MACVC,WAAW,EAAE;IACf,CAAC;EACH;EAAC;EAAApC,aAAA,GAAAY,CAAA;EAED,OAAON,qBAAqB,CAAC0B,KAAK,EAAE,6CAA6C,CAAC;AACpF,CAAC,CAAC", "ignoreList": []}