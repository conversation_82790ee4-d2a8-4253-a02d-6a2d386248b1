{"version": 3, "names": ["cov_1oh1qd3dcz", "actualCoverage", "getClientByEmailDomain", "requireAuth", "createSuccessResponse", "createErrorResponse", "ApiErrorCode", "HttpStatus", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GET", "s", "f", "authResult", "success", "b", "response", "session", "email", "INVALID_INPUT", "BAD_REQUEST", "result", "statusCode", "errorCode", "NOT_FOUND", "INTERNAL_SERVER_ERROR", "apiErrorCode", "DATABASE_ERROR", "error", "client"], "sources": ["route.ts"], "sourcesContent": ["import { getClientByEmailDomain } from '@/lib/clients';\nimport { requireAuth } from '@/lib/auth-middleware';\nimport {\n  createSuccessResponse,\n  createErrorResponse,\n  ApiErrorCode,\n  HttpStatus,\n  withErrorHandling\n} from '@/lib/api-response';\n\nexport const GET = withErrorHandling(async () => {\n  // Verify authentication and get session\n  const authResult = await requireAuth();\n  if (!authResult.success) {\n    return authResult.response!;\n  }\n\n  const session = authResult.session!;\n\n  // Get email from session instead of URL parameters for security\n  const email = session.email;\n  if (!email) {\n    return createErrorResponse(\n      'No email found in session',\n      ApiErrorCode.INVALID_INPUT,\n      HttpStatus.BAD_REQUEST\n    );\n  }\n\n  const result = await getClientByEmailDomain(email);\n\n  if (!result.success) {\n    const statusCode = result.errorCode === 'NOT_FOUND' ? HttpStatus.NOT_FOUND : HttpStatus.INTERNAL_SERVER_ERROR;\n    const apiErrorCode = result.errorCode === 'NOT_FOUND' ? ApiErrorCode.NOT_FOUND : ApiErrorCode.DATABASE_ERROR;\n\n    return createErrorResponse(\n      result.error || 'Failed to fetch client',\n      apiErrorCode,\n      statusCode\n    );\n  }\n\n  // Return tenant context\n  return createSuccessResponse({\n    client: result.client\n  }, 'Client information retrieved successfully');\n});\n\n\n\n\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ,SAASE,sBAAsB,QAAQ,eAAe;AACtD,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SACEC,qBAAqB,EACrBC,mBAAmB,EACnBC,YAAY,EACZC,UAAU,EACVC,iBAAiB,QACZ,oBAAoB;AAE3B,OAAO,MAAMC,GAAG;AAAA;AAAA,CAAAT,cAAA,GAAAU,CAAA,OAAGF,iBAAiB,CAAC,YAAY;EAAA;EAAAR,cAAA,GAAAW,CAAA;EAC/C;EACA,MAAMC,UAAU;EAAA;EAAA,CAAAZ,cAAA,GAAAU,CAAA,OAAG,MAAMP,WAAW,CAAC,CAAC;EAAC;EAAAH,cAAA,GAAAU,CAAA;EACvC,IAAI,CAACE,UAAU,CAACC,OAAO,EAAE;IAAA;IAAAb,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAU,CAAA;IACvB,OAAOE,UAAU,CAACG,QAAQ;EAC5B,CAAC;EAAA;EAAA;IAAAf,cAAA,GAAAc,CAAA;EAAA;EAED,MAAME,OAAO;EAAA;EAAA,CAAAhB,cAAA,GAAAU,CAAA,OAAGE,UAAU,CAACI,OAAO,CAAC;;EAEnC;EACA,MAAMC,KAAK;EAAA;EAAA,CAAAjB,cAAA,GAAAU,CAAA,OAAGM,OAAO,CAACC,KAAK;EAAC;EAAAjB,cAAA,GAAAU,CAAA;EAC5B,IAAI,CAACO,KAAK,EAAE;IAAA;IAAAjB,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAU,CAAA;IACV,OAAOL,mBAAmB,CACxB,2BAA2B,EAC3BC,YAAY,CAACY,aAAa,EAC1BX,UAAU,CAACY,WACb,CAAC;EACH,CAAC;EAAA;EAAA;IAAAnB,cAAA,GAAAc,CAAA;EAAA;EAED,MAAMM,MAAM;EAAA;EAAA,CAAApB,cAAA,GAAAU,CAAA,OAAG,MAAMR,sBAAsB,CAACe,KAAK,CAAC;EAAC;EAAAjB,cAAA,GAAAU,CAAA;EAEnD,IAAI,CAACU,MAAM,CAACP,OAAO,EAAE;IAAA;IAAAb,cAAA,GAAAc,CAAA;IACnB,MAAMO,UAAU;IAAA;IAAA,CAAArB,cAAA,GAAAU,CAAA,QAAGU,MAAM,CAACE,SAAS,KAAK,WAAW;IAAA;IAAA,CAAAtB,cAAA,GAAAc,CAAA,UAAGP,UAAU,CAACgB,SAAS;IAAA;IAAA,CAAAvB,cAAA,GAAAc,CAAA,UAAGP,UAAU,CAACiB,qBAAqB;IAC7G,MAAMC,YAAY;IAAA;IAAA,CAAAzB,cAAA,GAAAU,CAAA,QAAGU,MAAM,CAACE,SAAS,KAAK,WAAW;IAAA;IAAA,CAAAtB,cAAA,GAAAc,CAAA,UAAGR,YAAY,CAACiB,SAAS;IAAA;IAAA,CAAAvB,cAAA,GAAAc,CAAA,UAAGR,YAAY,CAACoB,cAAc;IAAC;IAAA1B,cAAA,GAAAU,CAAA;IAE7G,OAAOL,mBAAmB;IACxB;IAAA,CAAAL,cAAA,GAAAc,CAAA,UAAAM,MAAM,CAACO,KAAK;IAAA;IAAA,CAAA3B,cAAA,GAAAc,CAAA,UAAI,wBAAwB,GACxCW,YAAY,EACZJ,UACF,CAAC;EACH,CAAC;EAAA;EAAA;IAAArB,cAAA,GAAAc,CAAA;EAAA;;EAED;EAAAd,cAAA,GAAAU,CAAA;EACA,OAAON,qBAAqB,CAAC;IAC3BwB,MAAM,EAAER,MAAM,CAACQ;EACjB,CAAC,EAAE,2CAA2C,CAAC;AACjD,CAAC,CAAC", "ignoreList": []}