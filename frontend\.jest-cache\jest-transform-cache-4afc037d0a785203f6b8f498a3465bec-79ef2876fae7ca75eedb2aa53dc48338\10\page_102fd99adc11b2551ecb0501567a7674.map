{"version": 3, "names": ["_jsxFileName", "React", "__jsx", "createElement", "cov_ap6xb2nes", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "_coverageSchema", "coverage", "actualCoverage", "useEffect", "useRouter", "getLoginUrl", "fetchAuthSession", "LoginPage", "router", "checkAuthAndRedirect", "<PERSON><PERSON><PERSON><PERSON>", "document", "cookie", "includes", "console", "log", "push", "session", "tokens", "idToken", "error", "window", "location", "href", "className", "__self", "__source", "fileName", "lineNumber", "columnNumber"], "sources": ["page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useEffect, useState } from 'react'\r\nimport { useRouter } from 'next/navigation'\r\nimport { getLoginUrl } from '@/lib/auth'\r\nimport { fetchAuthSession } from 'aws-amplify/auth'\r\n\r\nexport default function LoginPage() {\r\n  const router = useRouter()\r\n\r\n  useEffect(() => {\r\n    async function checkAuthAndRedirect() {\r\n      try {\r\n        // Check if we have an idToken cookie\r\n        const hasCookie = document.cookie.includes('idToken=')\r\n\r\n        if (hasCookie) {\r\n          // If we have a cookie, redirect to dashboard\r\n          console.log('Auth cookie found, redirecting to dashboard')\r\n          router.push('/dashboard')\r\n        } else {\r\n          // Try to get the session from Amplify\r\n          try {\r\n            const session = await fetchAuthSession()\r\n            if (session?.tokens?.idToken) {\r\n              console.log('Amplify session found, redirecting to dashboard')\r\n              router.push('/dashboard')\r\n              return\r\n            }\r\n          } catch (error) {\r\n            console.log('No Amplify session found')\r\n          }\r\n\r\n          // No cookie or Amplify session, redirect to Cognito login\r\n          console.log('No auth found, redirecting to Cognito login')\r\n          window.location.href = getLoginUrl()\r\n        }\r\n      } catch (error) {\r\n        console.error('Error in login page:', error)\r\n        // On error, redirect to Cognito login\r\n        window.location.href = getLoginUrl()\r\n      }\r\n    }\r\n\r\n    checkAuthAndRedirect()\r\n  }, [router])\r\n\r\n  // Show loading spinner while redirecting\r\n  return (\r\n    <div className=\"flex items-center justify-center min-h-screen\">\r\n      <div className=\"w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin\"></div>\r\n    </div>\r\n  )\r\n}\r\n\r\n\r\n\r\n"], "mappings": "AAAA,YAAY;;AAAA;AAAA,IAAAA,YAAA;AAAA,OAAAC,KAAA;AAAA,IAAAC,KAAA,GAAAD,KAAA,CAAAE,aAAA;AAAA,SAAAC,cAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;IAAA;IAAAC,eAAA;IAAArB,IAAA;EAAA;EAAA,IAAAsB,QAAA,GAAArB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAmB,QAAA,CAAAvB,IAAA,KAAAuB,QAAA,CAAAvB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAsB,QAAA,CAAAvB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAmB,cAAA,GAAAD,QAAA,CAAAvB,IAAA;EAAA;IAeA;IAAAD,aAAA,YAAAA,CAAA;MAAA,OAAAyB,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAzB,aAAA;AAbZ,SAAS0B,SAAS,QAAkB,OAAO;AAC3C,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,WAAW,QAAQ,YAAY;AACxC,SAASC,gBAAgB,QAAQ,kBAAkB;AAEnD,eAAe,SAASC,SAASA,CAAA,EAAG;EAAA;EAAA9B,aAAA,GAAAqB,CAAA;EAClC,MAAMU,MAAM;EAAA;EAAA,CAAA/B,aAAA,GAAAoB,CAAA,OAAGO,SAAS,CAAC,CAAC;EAAA;EAAA3B,aAAA,GAAAoB,CAAA;EAE1BM,SAAS,CAAC,MAAM;IAAA;IAAA1B,aAAA,GAAAqB,CAAA;IACd,eAAeW,oBAAoBA,CAAA,EAAG;MAAA;MAAAhC,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MACpC,IAAI;QACF;QACA,MAAMa,SAAS;QAAA;QAAA,CAAAjC,aAAA,GAAAoB,CAAA,OAAGc,QAAQ,CAACC,MAAM,CAACC,QAAQ,CAAC,UAAU,CAAC;QAAA;QAAApC,aAAA,GAAAoB,CAAA;QAEtD,IAAIa,SAAS,EAAE;UAAA;UAAAjC,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UACb;UACAiB,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;UAAA;UAAAtC,aAAA,GAAAoB,CAAA;UAC1DW,MAAM,CAACQ,IAAI,CAAC,YAAY,CAAC;QAC3B,CAAC,MAAM;UAAA;UAAAvC,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UACL;UACA,IAAI;YACF,MAAMoB,OAAO;YAAA;YAAA,CAAAxC,aAAA,GAAAoB,CAAA,OAAG,MAAMS,gBAAgB,CAAC,CAAC;YAAA;YAAA7B,aAAA,GAAAoB,CAAA;YACxC,IAAIoB,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAE;cAAA;cAAA1C,aAAA,GAAAsB,CAAA;cAAAtB,aAAA,GAAAoB,CAAA;cAC5BiB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;cAAA;cAAAtC,aAAA,GAAAoB,CAAA;cAC9DW,MAAM,CAACQ,IAAI,CAAC,YAAY,CAAC;cAAA;cAAAvC,aAAA,GAAAoB,CAAA;cACzB;YACF,CAAC;YAAA;YAAA;cAAApB,aAAA,GAAAsB,CAAA;YAAA;UACH,CAAC,CAAC,OAAOqB,KAAK,EAAE;YAAA;YAAA3C,aAAA,GAAAoB,CAAA;YACdiB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;UACzC;;UAEA;UAAA;UAAAtC,aAAA,GAAAoB,CAAA;UACAiB,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;UAAA;UAAAtC,aAAA,GAAAoB,CAAA;UAC1DwB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGlB,WAAW,CAAC,CAAC;QACtC;MACF,CAAC,CAAC,OAAOe,KAAK,EAAE;QAAA;QAAA3C,aAAA,GAAAoB,CAAA;QACdiB,OAAO,CAACM,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C;QAAA;QAAA3C,aAAA,GAAAoB,CAAA;QACAwB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGlB,WAAW,CAAC,CAAC;MACtC;IACF;IAAC;IAAA5B,aAAA,GAAAoB,CAAA;IAEDY,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAACD,MAAM,CAAC,CAAC;;EAEZ;EAAA;EAAA/B,aAAA,GAAAoB,CAAA;EACA,OACE,0BAAAtB,KAAA;EAAA;EAAA;EAAA;EAAA;IAAKiD,SAAS,EAAC,+CAA+C;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAtD,YAAA;MAAAuD,UAAA;MAAAC,YAAA;IAAA;EAAA;EAC5D;EAAAtD,KAAA;EAAA;EAAA;EAAA;EAAA;IAAKiD,SAAS,EAAC,6EAA6E;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAtD,YAAA;MAAAuD,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAM,CAC/F,CAAC;AAEV", "ignoreList": []}