e8dfd01a73194c25927d583f78937d49
/* istanbul ignore next */
"use strict";
/**
 * Dashboard Data Hook
 * 
 * Custom hook for managing dashboard data fetching, caching, and state management.
 * Focused responsibility: Data layer for dashboard components.
 */

'use client';

/* istanbul ignore next */
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useDashboardData = useDashboardData;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var
/* istanbul ignore next */
_react = require("react");
var
/* istanbul ignore next */
_AppContext = require("@/contexts/AppContext");
var
/* istanbul ignore next */
_typeUtils = require("@/lib/type-utils");
var
/* istanbul ignore next */
_cache = require("@/lib/cache");
var
/* istanbul ignore next */
_performance = require("@/lib/performance");
/* istanbul ignore next */
function cov_120psm3w3w() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\hooks\\useDashboardData.ts";
  var hash = "28d28ed66791866b1bea51849975f5ccf854c11c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\hooks\\useDashboardData.ts",
    statementMap: {
      "0": {
        start: {
          line: 38,
          column: 37
        },
        end: {
          line: 43,
          column: 1
        }
      },
      "1": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 37
        }
      },
      "2": {
        start: {
          line: 48,
          column: 18
        },
        end: {
          line: 48,
          column: 31
        }
      },
      "3": {
        start: {
          line: 49,
          column: 19
        },
        end: {
          line: 52,
          column: 10
        }
      },
      "4": {
        start: {
          line: 56,
          column: 38
        },
        end: {
          line: 56,
          column: 45
        }
      },
      "5": {
        start: {
          line: 58,
          column: 21
        },
        end: {
          line: 58,
          column: 42
        }
      },
      "6": {
        start: {
          line: 59,
          column: 20
        },
        end: {
          line: 59,
          column: 65
        }
      },
      "7": {
        start: {
          line: 59,
          column: 37
        },
        end: {
          line: 59,
          column: 55
        }
      },
      "8": {
        start: {
          line: 61,
          column: 2
        },
        end: {
          line: 74,
          column: 3
        }
      },
      "9": {
        start: {
          line: 62,
          column: 21
        },
        end: {
          line: 67,
          column: 6
        }
      },
      "10": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 69,
          column: 27
        }
      },
      "11": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 70,
          column: 19
        }
      },
      "12": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 72,
          column: 27
        }
      },
      "13": {
        start: {
          line: 73,
          column: 4
        },
        end: {
          line: 73,
          column: 15
        }
      },
      "14": {
        start: {
          line: 78,
          column: 65
        },
        end: {
          line: 78,
          column: 76
        }
      },
      "15": {
        start: {
          line: 80,
          column: 26
        },
        end: {
          line: 84,
          column: 4
        }
      },
      "16": {
        start: {
          line: 86,
          column: 36
        },
        end: {
          line: 86,
          column: 51
        }
      },
      "17": {
        start: {
          line: 87,
          column: 28
        },
        end: {
          line: 87,
          column: 57
        }
      },
      "18": {
        start: {
          line: 90,
          column: 23
        },
        end: {
          line: 90,
          column: 35
        }
      },
      "19": {
        start: {
          line: 91,
          column: 29
        },
        end: {
          line: 91,
          column: 65
        }
      },
      "20": {
        start: {
          line: 94,
          column: 26
        },
        end: {
          line: 94,
          column: 50
        }
      },
      "21": {
        start: {
          line: 97,
          column: 20
        },
        end: {
          line: 103,
          column: 33
        }
      },
      "22": {
        start: {
          line: 98,
          column: 4
        },
        end: {
          line: 98,
          column: 37
        }
      },
      "23": {
        start: {
          line: 98,
          column: 26
        },
        end: {
          line: 98,
          column: 37
        }
      },
      "24": {
        start: {
          line: 99,
          column: 4
        },
        end: {
          line: 102,
          column: 5
        }
      },
      "25": {
        start: {
          line: 106,
          column: 21
        },
        end: {
          line: 138,
          column: 14
        }
      },
      "26": {
        start: {
          line: 107,
          column: 4
        },
        end: {
          line: 107,
          column: 56
        }
      },
      "27": {
        start: {
          line: 107,
          column: 17
        },
        end: {
          line: 107,
          column: 56
        }
      },
      "28": {
        start: {
          line: 109,
          column: 4
        },
        end: {
          line: 109,
          column: 39
        }
      },
      "29": {
        start: {
          line: 111,
          column: 21
        },
        end: {
          line: 111,
          column: 77
        }
      },
      "30": {
        start: {
          line: 112,
          column: 19
        },
        end: {
          line: 112,
          column: 41
        }
      },
      "31": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 116,
          column: 5
        }
      },
      "32": {
        start: {
          line: 114,
          column: 6
        },
        end: {
          line: 114,
          column: 44
        }
      },
      "33": {
        start: {
          line: 115,
          column: 6
        },
        end: {
          line: 115,
          column: 19
        }
      },
      "34": {
        start: {
          line: 118,
          column: 21
        },
        end: {
          line: 118,
          column: 76
        }
      },
      "35": {
        start: {
          line: 120,
          column: 4
        },
        end: {
          line: 122,
          column: 5
        }
      },
      "36": {
        start: {
          line: 121,
          column: 6
        },
        end: {
          line: 121,
          column: 89
        }
      },
      "37": {
        start: {
          line: 124,
          column: 19
        },
        end: {
          line: 124,
          column: 40
        }
      },
      "38": {
        start: {
          line: 126,
          column: 4
        },
        end: {
          line: 137,
          column: 5
        }
      },
      "39": {
        start: {
          line: 127,
          column: 6
        },
        end: {
          line: 127,
          column: 89
        }
      },
      "40": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 44
        }
      },
      "41": {
        start: {
          line: 129,
          column: 6
        },
        end: {
          line: 129,
          column: 24
        }
      },
      "42": {
        start: {
          line: 130,
          column: 11
        },
        end: {
          line: 137,
          column: 5
        }
      },
      "43": {
        start: {
          line: 131,
          column: 6
        },
        end: {
          line: 131,
          column: 35
        }
      },
      "44": {
        start: {
          line: 134,
          column: 6
        },
        end: {
          line: 134,
          column: 84
        }
      },
      "45": {
        start: {
          line: 135,
          column: 6
        },
        end: {
          line: 135,
          column: 44
        }
      },
      "46": {
        start: {
          line: 136,
          column: 6
        },
        end: {
          line: 136,
          column: 19
        }
      },
      "47": {
        start: {
          line: 141,
          column: 24
        },
        end: {
          line: 173,
          column: 14
        }
      },
      "48": {
        start: {
          line: 142,
          column: 4
        },
        end: {
          line: 142,
          column: 56
        }
      },
      "49": {
        start: {
          line: 142,
          column: 17
        },
        end: {
          line: 142,
          column: 56
        }
      },
      "50": {
        start: {
          line: 144,
          column: 4
        },
        end: {
          line: 144,
          column: 42
        }
      },
      "51": {
        start: {
          line: 146,
          column: 21
        },
        end: {
          line: 146,
          column: 80
        }
      },
      "52": {
        start: {
          line: 147,
          column: 19
        },
        end: {
          line: 147,
          column: 41
        }
      },
      "53": {
        start: {
          line: 148,
          column: 4
        },
        end: {
          line: 151,
          column: 5
        }
      },
      "54": {
        start: {
          line: 149,
          column: 6
        },
        end: {
          line: 149,
          column: 47
        }
      },
      "55": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 150,
          column: 19
        }
      },
      "56": {
        start: {
          line: 153,
          column: 21
        },
        end: {
          line: 153,
          column: 79
        }
      },
      "57": {
        start: {
          line: 155,
          column: 4
        },
        end: {
          line: 157,
          column: 5
        }
      },
      "58": {
        start: {
          line: 156,
          column: 6
        },
        end: {
          line: 156,
          column: 92
        }
      },
      "59": {
        start: {
          line: 159,
          column: 19
        },
        end: {
          line: 159,
          column: 40
        }
      },
      "60": {
        start: {
          line: 161,
          column: 4
        },
        end: {
          line: 172,
          column: 5
        }
      },
      "61": {
        start: {
          line: 162,
          column: 6
        },
        end: {
          line: 162,
          column: 92
        }
      },
      "62": {
        start: {
          line: 163,
          column: 6
        },
        end: {
          line: 163,
          column: 47
        }
      },
      "63": {
        start: {
          line: 164,
          column: 6
        },
        end: {
          line: 164,
          column: 24
        }
      },
      "64": {
        start: {
          line: 165,
          column: 11
        },
        end: {
          line: 172,
          column: 5
        }
      },
      "65": {
        start: {
          line: 166,
          column: 6
        },
        end: {
          line: 166,
          column: 35
        }
      },
      "66": {
        start: {
          line: 169,
          column: 6
        },
        end: {
          line: 169,
          column: 87
        }
      },
      "67": {
        start: {
          line: 170,
          column: 6
        },
        end: {
          line: 170,
          column: 47
        }
      },
      "68": {
        start: {
          line: 171,
          column: 6
        },
        end: {
          line: 171,
          column: 19
        }
      },
      "69": {
        start: {
          line: 176,
          column: 29
        },
        end: {
          line: 225,
          column: 41
        }
      },
      "70": {
        start: {
          line: 177,
          column: 4
        },
        end: {
          line: 177,
          column: 48
        }
      },
      "71": {
        start: {
          line: 177,
          column: 42
        },
        end: {
          line: 177,
          column: 48
        }
      },
      "72": {
        start: {
          line: 180,
          column: 4
        },
        end: {
          line: 182,
          column: 5
        }
      },
      "73": {
        start: {
          line: 181,
          column: 6
        },
        end: {
          line: 181,
          column: 40
        }
      },
      "74": {
        start: {
          line: 184,
          column: 4
        },
        end: {
          line: 184,
          column: 54
        }
      },
      "75": {
        start: {
          line: 185,
          column: 19
        },
        end: {
          line: 185,
          column: 52
        }
      },
      "76": {
        start: {
          line: 187,
          column: 4
        },
        end: {
          line: 187,
          column: 22
        }
      },
      "77": {
        start: {
          line: 188,
          column: 4
        },
        end: {
          line: 188,
          column: 18
        }
      },
      "78": {
        start: {
          line: 190,
          column: 4
        },
        end: {
          line: 224,
          column: 5
        }
      },
      "79": {
        start: {
          line: 191,
          column: 40
        },
        end: {
          line: 194,
          column: 8
        }
      },
      "80": {
        start: {
          line: 196,
          column: 6
        },
        end: {
          line: 196,
          column: 39
        }
      },
      "81": {
        start: {
          line: 196,
          column: 33
        },
        end: {
          line: 196,
          column: 39
        }
      },
      "82": {
        start: {
          line: 198,
          column: 37
        },
        end: {
          line: 202,
          column: 7
        }
      },
      "83": {
        start: {
          line: 204,
          column: 6
        },
        end: {
          line: 204,
          column: 22
        }
      },
      "84": {
        start: {
          line: 207,
          column: 6
        },
        end: {
          line: 209,
          column: 7
        }
      },
      "85": {
        start: {
          line: 208,
          column: 8
        },
        end: {
          line: 208,
          column: 65
        }
      },
      "86": {
        start: {
          line: 210,
          column: 6
        },
        end: {
          line: 212,
          column: 7
        }
      },
      "87": {
        start: {
          line: 211,
          column: 8
        },
        end: {
          line: 211,
          column: 71
        }
      },
      "88": {
        start: {
          line: 215,
          column: 6
        },
        end: {
          line: 215,
          column: 39
        }
      },
      "89": {
        start: {
          line: 215,
          column: 33
        },
        end: {
          line: 215,
          column: 39
        }
      },
      "90": {
        start: {
          line: 217,
          column: 27
        },
        end: {
          line: 217,
          column: 96
        }
      },
      "91": {
        start: {
          line: 218,
          column: 6
        },
        end: {
          line: 218,
          column: 28
        }
      },
      "92": {
        start: {
          line: 219,
          column: 6
        },
        end: {
          line: 219,
          column: 55
        }
      },
      "93": {
        start: {
          line: 221,
          column: 6
        },
        end: {
          line: 223,
          column: 7
        }
      },
      "94": {
        start: {
          line: 222,
          column: 8
        },
        end: {
          line: 222,
          column: 27
        }
      },
      "95": {
        start: {
          line: 228,
          column: 23
        },
        end: {
          line: 239,
          column: 26
        }
      },
      "96": {
        start: {
          line: 229,
          column: 4
        },
        end: {
          line: 229,
          column: 23
        }
      },
      "97": {
        start: {
          line: 229,
          column: 17
        },
        end: {
          line: 229,
          column: 23
        }
      },
      "98": {
        start: {
          line: 231,
          column: 4
        },
        end: {
          line: 238,
          column: 5
        }
      },
      "99": {
        start: {
          line: 232,
          column: 20
        },
        end: {
          line: 232,
          column: 38
        }
      },
      "100": {
        start: {
          line: 233,
          column: 6
        },
        end: {
          line: 235,
          column: 7
        }
      },
      "101": {
        start: {
          line: 234,
          column: 8
        },
        end: {
          line: 234,
          column: 45
        }
      },
      "102": {
        start: {
          line: 234,
          column: 25
        },
        end: {
          line: 234,
          column: 43
        }
      },
      "103": {
        start: {
          line: 237,
          column: 6
        },
        end: {
          line: 237,
          column: 52
        }
      },
      "104": {
        start: {
          line: 241,
          column: 26
        },
        end: {
          line: 256,
          column: 29
        }
      },
      "105": {
        start: {
          line: 242,
          column: 4
        },
        end: {
          line: 242,
          column: 23
        }
      },
      "106": {
        start: {
          line: 242,
          column: 17
        },
        end: {
          line: 242,
          column: 23
        }
      },
      "107": {
        start: {
          line: 244,
          column: 4
        },
        end: {
          line: 255,
          column: 5
        }
      },
      "108": {
        start: {
          line: 245,
          column: 23
        },
        end: {
          line: 245,
          column: 44
        }
      },
      "109": {
        start: {
          line: 246,
          column: 6
        },
        end: {
          line: 252,
          column: 7
        }
      },
      "110": {
        start: {
          line: 247,
          column: 8
        },
        end: {
          line: 251,
          column: 11
        }
      },
      "111": {
        start: {
          line: 247,
          column: 25
        },
        end: {
          line: 251,
          column: 9
        }
      },
      "112": {
        start: {
          line: 254,
          column: 6
        },
        end: {
          line: 254,
          column: 55
        }
      },
      "113": {
        start: {
          line: 259,
          column: 2
        },
        end: {
          line: 265,
          column: 62
        }
      },
      "114": {
        start: {
          line: 260,
          column: 4
        },
        end: {
          line: 264,
          column: 5
        }
      },
      "115": {
        start: {
          line: 261,
          column: 6
        },
        end: {
          line: 261,
          column: 26
        }
      },
      "116": {
        start: {
          line: 262,
          column: 11
        },
        end: {
          line: 264,
          column: 5
        }
      },
      "117": {
        start: {
          line: 263,
          column: 6
        },
        end: {
          line: 263,
          column: 27
        }
      },
      "118": {
        start: {
          line: 268,
          column: 2
        },
        end: {
          line: 275,
          column: 8
        }
      },
      "119": {
        start: {
          line: 269,
          column: 4
        },
        end: {
          line: 274,
          column: 5
        }
      },
      "120": {
        start: {
          line: 270,
          column: 6
        },
        end: {
          line: 270,
          column: 34
        }
      },
      "121": {
        start: {
          line: 271,
          column: 6
        },
        end: {
          line: 273,
          column: 7
        }
      },
      "122": {
        start: {
          line: 272,
          column: 8
        },
        end: {
          line: 272,
          column: 42
        }
      },
      "123": {
        start: {
          line: 277,
          column: 2
        },
        end: {
          line: 284,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "fetchWithTimeout",
        decl: {
          start: {
            line: 55,
            column: 15
          },
          end: {
            line: 55,
            column: 31
          }
        },
        loc: {
          start: {
            line: 55,
            column: 92
          },
          end: {
            line: 75,
            column: 1
          }
        },
        line: 55
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 59,
            column: 31
          },
          end: {
            line: 59,
            column: 32
          }
        },
        loc: {
          start: {
            line: 59,
            column: 37
          },
          end: {
            line: 59,
            column: 55
          }
        },
        line: 59
      },
      "2": {
        name: "useDashboardData",
        decl: {
          start: {
            line: 77,
            column: 16
          },
          end: {
            line: 77,
            column: 32
          }
        },
        loc: {
          start: {
            line: 77,
            column: 59
          },
          end: {
            line: 285,
            column: 1
          }
        },
        line: 77
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 97,
            column: 28
          },
          end: {
            line: 97,
            column: 29
          }
        },
        loc: {
          start: {
            line: 97,
            column: 34
          },
          end: {
            line: 103,
            column: 3
          }
        },
        line: 97
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 106,
            column: 33
          },
          end: {
            line: 106,
            column: 34
          }
        },
        loc: {
          start: {
            line: 106,
            column: 96
          },
          end: {
            line: 138,
            column: 3
          }
        },
        line: 106
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 141,
            column: 36
          },
          end: {
            line: 141,
            column: 37
          }
        },
        loc: {
          start: {
            line: 141,
            column: 94
          },
          end: {
            line: 173,
            column: 3
          }
        },
        line: 141
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 176,
            column: 41
          },
          end: {
            line: 176,
            column: 42
          }
        },
        loc: {
          start: {
            line: 176,
            column: 68
          },
          end: {
            line: 225,
            column: 3
          }
        },
        line: 176
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 228,
            column: 35
          },
          end: {
            line: 228,
            column: 36
          }
        },
        loc: {
          start: {
            line: 228,
            column: 62
          },
          end: {
            line: 239,
            column: 3
          }
        },
        line: 228
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 234,
            column: 16
          },
          end: {
            line: 234,
            column: 17
          }
        },
        loc: {
          start: {
            line: 234,
            column: 25
          },
          end: {
            line: 234,
            column: 43
          }
        },
        line: 234
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 241,
            column: 38
          },
          end: {
            line: 241,
            column: 39
          }
        },
        loc: {
          start: {
            line: 241,
            column: 65
          },
          end: {
            line: 256,
            column: 3
          }
        },
        line: 241
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 247,
            column: 16
          },
          end: {
            line: 247,
            column: 17
          }
        },
        loc: {
          start: {
            line: 247,
            column: 25
          },
          end: {
            line: 251,
            column: 9
          }
        },
        line: 247
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 259,
            column: 12
          },
          end: {
            line: 259,
            column: 13
          }
        },
        loc: {
          start: {
            line: 259,
            column: 18
          },
          end: {
            line: 265,
            column: 3
          }
        },
        line: 259
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 268,
            column: 12
          },
          end: {
            line: 268,
            column: 13
          }
        },
        loc: {
          start: {
            line: 268,
            column: 18
          },
          end: {
            line: 275,
            column: 3
          }
        },
        line: 268
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 269,
            column: 11
          },
          end: {
            line: 269,
            column: 12
          }
        },
        loc: {
          start: {
            line: 269,
            column: 17
          },
          end: {
            line: 274,
            column: 5
          }
        },
        line: 269
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 55,
            column: 45
          },
          end: {
            line: 55,
            column: 71
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 55,
            column: 69
          },
          end: {
            line: 55,
            column: 71
          }
        }],
        line: 55
      },
      "1": {
        loc: {
          start: {
            line: 56,
            column: 18
          },
          end: {
            line: 56,
            column: 33
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 56,
            column: 28
          },
          end: {
            line: 56,
            column: 33
          }
        }],
        line: 56
      },
      "2": {
        loc: {
          start: {
            line: 63,
            column: 14
          },
          end: {
            line: 63,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 63,
            column: 14
          },
          end: {
            line: 63,
            column: 20
          }
        }, {
          start: {
            line: 63,
            column: 24
          },
          end: {
            line: 63,
            column: 41
          }
        }],
        line: 63
      },
      "3": {
        loc: {
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 98,
            column: 37
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 98,
            column: 37
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 98
      },
      "4": {
        loc: {
          start: {
            line: 106,
            column: 40
          },
          end: {
            line: 106,
            column: 66
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 106,
            column: 64
          },
          end: {
            line: 106,
            column: 66
          }
        }],
        line: 106
      },
      "5": {
        loc: {
          start: {
            line: 107,
            column: 4
          },
          end: {
            line: 107,
            column: 56
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 107,
            column: 4
          },
          end: {
            line: 107,
            column: 56
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 107
      },
      "6": {
        loc: {
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 116,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 116,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 113
      },
      "7": {
        loc: {
          start: {
            line: 120,
            column: 4
          },
          end: {
            line: 122,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 120,
            column: 4
          },
          end: {
            line: 122,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 120
      },
      "8": {
        loc: {
          start: {
            line: 126,
            column: 4
          },
          end: {
            line: 137,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 126,
            column: 4
          },
          end: {
            line: 137,
            column: 5
          }
        }, {
          start: {
            line: 130,
            column: 11
          },
          end: {
            line: 137,
            column: 5
          }
        }],
        line: 126
      },
      "9": {
        loc: {
          start: {
            line: 130,
            column: 11
          },
          end: {
            line: 137,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 130,
            column: 11
          },
          end: {
            line: 137,
            column: 5
          }
        }, {
          start: {
            line: 132,
            column: 11
          },
          end: {
            line: 137,
            column: 5
          }
        }],
        line: 130
      },
      "10": {
        loc: {
          start: {
            line: 141,
            column: 43
          },
          end: {
            line: 141,
            column: 69
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 141,
            column: 67
          },
          end: {
            line: 141,
            column: 69
          }
        }],
        line: 141
      },
      "11": {
        loc: {
          start: {
            line: 142,
            column: 4
          },
          end: {
            line: 142,
            column: 56
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 142,
            column: 4
          },
          end: {
            line: 142,
            column: 56
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 142
      },
      "12": {
        loc: {
          start: {
            line: 148,
            column: 4
          },
          end: {
            line: 151,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 148,
            column: 4
          },
          end: {
            line: 151,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 148
      },
      "13": {
        loc: {
          start: {
            line: 155,
            column: 4
          },
          end: {
            line: 157,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 155,
            column: 4
          },
          end: {
            line: 157,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 155
      },
      "14": {
        loc: {
          start: {
            line: 161,
            column: 4
          },
          end: {
            line: 172,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 161,
            column: 4
          },
          end: {
            line: 172,
            column: 5
          }
        }, {
          start: {
            line: 165,
            column: 11
          },
          end: {
            line: 172,
            column: 5
          }
        }],
        line: 161
      },
      "15": {
        loc: {
          start: {
            line: 165,
            column: 11
          },
          end: {
            line: 172,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 165,
            column: 11
          },
          end: {
            line: 172,
            column: 5
          }
        }, {
          start: {
            line: 167,
            column: 11
          },
          end: {
            line: 172,
            column: 5
          }
        }],
        line: 165
      },
      "16": {
        loc: {
          start: {
            line: 177,
            column: 4
          },
          end: {
            line: 177,
            column: 48
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 177,
            column: 4
          },
          end: {
            line: 177,
            column: 48
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 177
      },
      "17": {
        loc: {
          start: {
            line: 177,
            column: 8
          },
          end: {
            line: 177,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 177,
            column: 8
          },
          end: {
            line: 177,
            column: 15
          }
        }, {
          start: {
            line: 177,
            column: 19
          },
          end: {
            line: 177,
            column: 40
          }
        }],
        line: 177
      },
      "18": {
        loc: {
          start: {
            line: 180,
            column: 4
          },
          end: {
            line: 182,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 180,
            column: 4
          },
          end: {
            line: 182,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 180
      },
      "19": {
        loc: {
          start: {
            line: 196,
            column: 6
          },
          end: {
            line: 196,
            column: 39
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 196,
            column: 6
          },
          end: {
            line: 196,
            column: 39
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 196
      },
      "20": {
        loc: {
          start: {
            line: 199,
            column: 15
          },
          end: {
            line: 199,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 199,
            column: 50
          },
          end: {
            line: 199,
            column: 65
          }
        }, {
          start: {
            line: 199,
            column: 68
          },
          end: {
            line: 199,
            column: 80
          }
        }],
        line: 199
      },
      "21": {
        loc: {
          start: {
            line: 200,
            column: 24
          },
          end: {
            line: 200,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 200,
            column: 62
          },
          end: {
            line: 200,
            column: 80
          }
        }, {
          start: {
            line: 200,
            column: 83
          },
          end: {
            line: 200,
            column: 98
          }
        }],
        line: 200
      },
      "22": {
        loc: {
          start: {
            line: 201,
            column: 26
          },
          end: {
            line: 201,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 201,
            column: 64
          },
          end: {
            line: 201,
            column: 82
          }
        }, {
          start: {
            line: 201,
            column: 85
          },
          end: {
            line: 201,
            column: 100
          }
        }],
        line: 201
      },
      "23": {
        loc: {
          start: {
            line: 207,
            column: 6
          },
          end: {
            line: 209,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 207,
            column: 6
          },
          end: {
            line: 209,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 207
      },
      "24": {
        loc: {
          start: {
            line: 210,
            column: 6
          },
          end: {
            line: 212,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 210,
            column: 6
          },
          end: {
            line: 212,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 210
      },
      "25": {
        loc: {
          start: {
            line: 215,
            column: 6
          },
          end: {
            line: 215,
            column: 39
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 215,
            column: 6
          },
          end: {
            line: 215,
            column: 39
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 215
      },
      "26": {
        loc: {
          start: {
            line: 217,
            column: 27
          },
          end: {
            line: 217,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 217,
            column: 50
          },
          end: {
            line: 217,
            column: 61
          }
        }, {
          start: {
            line: 217,
            column: 64
          },
          end: {
            line: 217,
            column: 96
          }
        }],
        line: 217
      },
      "27": {
        loc: {
          start: {
            line: 221,
            column: 6
          },
          end: {
            line: 223,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 221,
            column: 6
          },
          end: {
            line: 223,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 221
      },
      "28": {
        loc: {
          start: {
            line: 229,
            column: 4
          },
          end: {
            line: 229,
            column: 23
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 229,
            column: 4
          },
          end: {
            line: 229,
            column: 23
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 229
      },
      "29": {
        loc: {
          start: {
            line: 233,
            column: 6
          },
          end: {
            line: 235,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 233,
            column: 6
          },
          end: {
            line: 235,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 233
      },
      "30": {
        loc: {
          start: {
            line: 242,
            column: 4
          },
          end: {
            line: 242,
            column: 23
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 242,
            column: 4
          },
          end: {
            line: 242,
            column: 23
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 242
      },
      "31": {
        loc: {
          start: {
            line: 246,
            column: 6
          },
          end: {
            line: 252,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 246,
            column: 6
          },
          end: {
            line: 252,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 246
      },
      "32": {
        loc: {
          start: {
            line: 260,
            column: 4
          },
          end: {
            line: 264,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 260,
            column: 4
          },
          end: {
            line: 264,
            column: 5
          }
        }, {
          start: {
            line: 262,
            column: 11
          },
          end: {
            line: 264,
            column: 5
          }
        }],
        line: 260
      },
      "33": {
        loc: {
          start: {
            line: 260,
            column: 8
          },
          end: {
            line: 260,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 260,
            column: 8
          },
          end: {
            line: 260,
            column: 14
          }
        }, {
          start: {
            line: 260,
            column: 18
          },
          end: {
            line: 260,
            column: 32
          }
        }, {
          start: {
            line: 260,
            column: 36
          },
          end: {
            line: 260,
            column: 48
          }
        }],
        line: 260
      },
      "34": {
        loc: {
          start: {
            line: 262,
            column: 11
          },
          end: {
            line: 264,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 262,
            column: 11
          },
          end: {
            line: 264,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 262
      },
      "35": {
        loc: {
          start: {
            line: 271,
            column: 6
          },
          end: {
            line: 273,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 271,
            column: 6
          },
          end: {
            line: 273,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 271
      },
      "36": {
        loc: {
          start: {
            line: 279,
            column: 15
          },
          end: {
            line: 279,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 279,
            column: 15
          },
          end: {
            line: 279,
            column: 24
          }
        }, {
          start: {
            line: 279,
            column: 28
          },
          end: {
            line: 279,
            column: 41
          }
        }],
        line: 279
      },
      "37": {
        loc: {
          start: {
            line: 280,
            column: 11
          },
          end: {
            line: 280,
            column: 31
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 280,
            column: 11
          },
          end: {
            line: 280,
            column: 16
          }
        }, {
          start: {
            line: 280,
            column: 20
          },
          end: {
            line: 280,
            column: 31
          }
        }],
        line: 280
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "28d28ed66791866b1bea51849975f5ccf854c11c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_120psm3w3w = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_120psm3w3w();
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
// Default data
const defaultStats =
/* istanbul ignore next */
(cov_120psm3w3w().s[0]++, {
  totalRenewals: 0,
  renewalsDue: 0,
  vendors: 0,
  annualSpend: '$0'
});
const defaultRenewals =
/* istanbul ignore next */
(cov_120psm3w3w().s[1]++, []);

// Cache configuration - now using advanced cache
const CACHE_TTL =
/* istanbul ignore next */
(cov_120psm3w3w().s[2]++, 5 * 60 * 1000); // 5 minutes
const CACHE_TAGS =
/* istanbul ignore next */
(cov_120psm3w3w().s[3]++, {
  STATS: 'dashboard-stats',
  RENEWALS: 'dashboard-renewals'
});

// Fetch with timeout and error handling
async function fetchWithTimeout(url, options =
/* istanbul ignore next */
(cov_120psm3w3w().b[0][0]++, {})) {
  /* istanbul ignore next */
  cov_120psm3w3w().f[0]++;
  const {
    signal,
    timeout =
    /* istanbul ignore next */
    (cov_120psm3w3w().b[1][0]++, 10000)
  } =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[4]++, options);
  const controller =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[5]++, new AbortController());
  const timeoutId =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[6]++, setTimeout(() => {
    /* istanbul ignore next */
    cov_120psm3w3w().f[1]++;
    cov_120psm3w3w().s[7]++;
    return controller.abort();
  }, timeout));
  /* istanbul ignore next */
  cov_120psm3w3w().s[8]++;
  try {
    const response =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[9]++, await fetch(url, {
      signal:
      /* istanbul ignore next */
      (cov_120psm3w3w().b[2][0]++, signal) ||
      /* istanbul ignore next */
      (cov_120psm3w3w().b[2][1]++, controller.signal),
      headers: {
        'Content-Type': 'application/json'
      }
    }));
    /* istanbul ignore next */
    cov_120psm3w3w().s[10]++;
    clearTimeout(timeoutId);
    /* istanbul ignore next */
    cov_120psm3w3w().s[11]++;
    return response;
  } catch (error) {
    /* istanbul ignore next */
    cov_120psm3w3w().s[12]++;
    clearTimeout(timeoutId);
    /* istanbul ignore next */
    cov_120psm3w3w().s[13]++;
    throw error;
  }
}
function useDashboardData() {
  /* istanbul ignore next */
  cov_120psm3w3w().f[2]++;
  const {
    tenant,
    loading: tenantLoading,
    error: tenantError
  } =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[14]++,
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _AppContext.
  /* istanbul ignore next */
  useTenant)());
  const [data, setData] =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[15]++,
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _react.
  /* istanbul ignore next */
  useState)({
    stats: defaultStats,
    recentRenewals: defaultRenewals,
    upcomingRenewals: defaultRenewals
  }));
  const [isLoading, setIsLoading] =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[16]++,
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _react.
  /* istanbul ignore next */
  useState)(false));
  const [error, setError] =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[17]++,
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _react.
  /* istanbul ignore next */
  useState)(null));

  // Use ref to track if component is mounted
  const isMountedRef =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[18]++,
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _react.
  /* istanbul ignore next */
  useRef)(true));
  const abortControllerRef =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[19]++,
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _react.
  /* istanbul ignore next */
  useRef)(null));

  // Debounce tenant changes to prevent excessive API calls
  const debouncedTenant =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[20]++,
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _performance.
  /* istanbul ignore next */
  useDebounce)(tenant, 300));

  // Memoize cache keys to prevent recreation
  const cacheKeys =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[21]++,
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _react.
  /* istanbul ignore next */
  useMemo)(() => {
    /* istanbul ignore next */
    cov_120psm3w3w().f[3]++;
    cov_120psm3w3w().s[22]++;
    if (!debouncedTenant) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[3][0]++;
      cov_120psm3w3w().s[23]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[3][1]++;
    }
    cov_120psm3w3w().s[24]++;
    return {
      stats:
      /* istanbul ignore next */
      _cache.
      /* istanbul ignore next */
      cacheUtils.createKey('dashboard-stats', debouncedTenant.tenantId),
      renewals:
      /* istanbul ignore next */
      _cache.
      /* istanbul ignore next */
      cacheUtils.createKey('dashboard-renewals', debouncedTenant.tenantId)
    };
  }, [debouncedTenant?.tenantId]));

  // Fetch dashboard stats with advanced caching
  const fetchStats =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[25]++,
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _react.
  /* istanbul ignore next */
  useCallback)(async (options =
  /* istanbul ignore next */
  (cov_120psm3w3w().b[4][0]++, {})) => {
    /* istanbul ignore next */
    cov_120psm3w3w().f[4]++;
    cov_120psm3w3w().s[26]++;
    if (!tenant) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[5][0]++;
      cov_120psm3w3w().s[27]++;
      throw new Error('Tenant not available');
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[5][1]++;
    }
    cov_120psm3w3w().s[28]++;
    /* istanbul ignore next */
    _performance.
    /* istanbul ignore next */
    performanceUtils.mark('fetchStats');
    const cacheKey =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[29]++,
    /* istanbul ignore next */
    _cache.
    /* istanbul ignore next */
    cacheUtils.createKey('dashboard-stats', tenant.tenantId));
    const cached =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[30]++,
    /* istanbul ignore next */
    _cache.
    /* istanbul ignore next */
    apiCache.get(cacheKey));
    /* istanbul ignore next */
    cov_120psm3w3w().s[31]++;
    if (cached) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[6][0]++;
      cov_120psm3w3w().s[32]++;
      /* istanbul ignore next */
      _performance.
      /* istanbul ignore next */
      performanceUtils.measure('fetchStats');
      /* istanbul ignore next */
      cov_120psm3w3w().s[33]++;
      return cached;
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[6][1]++;
    }
    const response =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[34]++, await fetchWithTimeout('/api/dashboard/stats', options));
    /* istanbul ignore next */
    cov_120psm3w3w().s[35]++;
    if (!response.ok) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[7][0]++;
      cov_120psm3w3w().s[36]++;
      throw new Error(`Failed to fetch stats: ${response.status} ${response.statusText}`);
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[7][1]++;
    }
    const result =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[37]++, await response.json());
    /* istanbul ignore next */
    cov_120psm3w3w().s[38]++;
    if (
    /* istanbul ignore next */
    (0,
    /* istanbul ignore next */
    _typeUtils.
    /* istanbul ignore next */
    isSuccessResponse)(result)) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[8][0]++;
      cov_120psm3w3w().s[39]++;
      /* istanbul ignore next */
      _cache.
      /* istanbul ignore next */
      apiCache.set(cacheKey, result.data, CACHE_TTL, [CACHE_TAGS.STATS, tenant.tenantId]);
      /* istanbul ignore next */
      cov_120psm3w3w().s[40]++;
      /* istanbul ignore next */
      _performance.
      /* istanbul ignore next */
      performanceUtils.measure('fetchStats');
      /* istanbul ignore next */
      cov_120psm3w3w().s[41]++;
      return result.data;
    } else {
      /* istanbul ignore next */
      cov_120psm3w3w().b[8][1]++;
      cov_120psm3w3w().s[42]++;
      if (
      /* istanbul ignore next */
      (0,
      /* istanbul ignore next */
      _typeUtils.
      /* istanbul ignore next */
      isErrorResponse)(result)) {
        /* istanbul ignore next */
        cov_120psm3w3w().b[9][0]++;
        cov_120psm3w3w().s[43]++;
        throw new Error(result.error);
      } else {
        /* istanbul ignore next */
        cov_120psm3w3w().b[9][1]++;
        cov_120psm3w3w().s[44]++;
        // Fallback for legacy API responses
        /* istanbul ignore next */
        _cache.
        /* istanbul ignore next */
        apiCache.set(cacheKey, result, CACHE_TTL, [CACHE_TAGS.STATS, tenant.tenantId]);
        /* istanbul ignore next */
        cov_120psm3w3w().s[45]++;
        /* istanbul ignore next */
        _performance.
        /* istanbul ignore next */
        performanceUtils.measure('fetchStats');
        /* istanbul ignore next */
        cov_120psm3w3w().s[46]++;
        return result;
      }
    }
  }, [tenant]));

  // Fetch recent renewals with advanced caching
  const fetchRenewals =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[47]++,
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _react.
  /* istanbul ignore next */
  useCallback)(async (options =
  /* istanbul ignore next */
  (cov_120psm3w3w().b[10][0]++, {})) => {
    /* istanbul ignore next */
    cov_120psm3w3w().f[5]++;
    cov_120psm3w3w().s[48]++;
    if (!tenant) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[11][0]++;
      cov_120psm3w3w().s[49]++;
      throw new Error('Tenant not available');
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[11][1]++;
    }
    cov_120psm3w3w().s[50]++;
    /* istanbul ignore next */
    _performance.
    /* istanbul ignore next */
    performanceUtils.mark('fetchRenewals');
    const cacheKey =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[51]++,
    /* istanbul ignore next */
    _cache.
    /* istanbul ignore next */
    cacheUtils.createKey('dashboard-renewals', tenant.tenantId));
    const cached =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[52]++,
    /* istanbul ignore next */
    _cache.
    /* istanbul ignore next */
    apiCache.get(cacheKey));
    /* istanbul ignore next */
    cov_120psm3w3w().s[53]++;
    if (cached) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[12][0]++;
      cov_120psm3w3w().s[54]++;
      /* istanbul ignore next */
      _performance.
      /* istanbul ignore next */
      performanceUtils.measure('fetchRenewals');
      /* istanbul ignore next */
      cov_120psm3w3w().s[55]++;
      return cached;
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[12][1]++;
    }
    const response =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[56]++, await fetchWithTimeout('/api/dashboard/renewals', options));
    /* istanbul ignore next */
    cov_120psm3w3w().s[57]++;
    if (!response.ok) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[13][0]++;
      cov_120psm3w3w().s[58]++;
      throw new Error(`Failed to fetch renewals: ${response.status} ${response.statusText}`);
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[13][1]++;
    }
    const result =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[59]++, await response.json());
    /* istanbul ignore next */
    cov_120psm3w3w().s[60]++;
    if (
    /* istanbul ignore next */
    (0,
    /* istanbul ignore next */
    _typeUtils.
    /* istanbul ignore next */
    isSuccessResponse)(result)) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[14][0]++;
      cov_120psm3w3w().s[61]++;
      /* istanbul ignore next */
      _cache.
      /* istanbul ignore next */
      apiCache.set(cacheKey, result.data, CACHE_TTL, [CACHE_TAGS.RENEWALS, tenant.tenantId]);
      /* istanbul ignore next */
      cov_120psm3w3w().s[62]++;
      /* istanbul ignore next */
      _performance.
      /* istanbul ignore next */
      performanceUtils.measure('fetchRenewals');
      /* istanbul ignore next */
      cov_120psm3w3w().s[63]++;
      return result.data;
    } else {
      /* istanbul ignore next */
      cov_120psm3w3w().b[14][1]++;
      cov_120psm3w3w().s[64]++;
      if (
      /* istanbul ignore next */
      (0,
      /* istanbul ignore next */
      _typeUtils.
      /* istanbul ignore next */
      isErrorResponse)(result)) {
        /* istanbul ignore next */
        cov_120psm3w3w().b[15][0]++;
        cov_120psm3w3w().s[65]++;
        throw new Error(result.error);
      } else {
        /* istanbul ignore next */
        cov_120psm3w3w().b[15][1]++;
        cov_120psm3w3w().s[66]++;
        // Fallback for legacy API responses
        /* istanbul ignore next */
        _cache.
        /* istanbul ignore next */
        apiCache.set(cacheKey, result, CACHE_TTL, [CACHE_TAGS.RENEWALS, tenant.tenantId]);
        /* istanbul ignore next */
        cov_120psm3w3w().s[67]++;
        /* istanbul ignore next */
        _performance.
        /* istanbul ignore next */
        performanceUtils.measure('fetchRenewals');
        /* istanbul ignore next */
        cov_120psm3w3w().s[68]++;
        return result;
      }
    }
  }, [tenant]));

  // Fetch all dashboard data
  const fetchDashboardData =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[69]++,
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _react.
  /* istanbul ignore next */
  useCallback)(async () => {
    /* istanbul ignore next */
    cov_120psm3w3w().f[6]++;
    cov_120psm3w3w().s[70]++;
    if (
    /* istanbul ignore next */
    (cov_120psm3w3w().b[17][0]++, !tenant) ||
    /* istanbul ignore next */
    (cov_120psm3w3w().b[17][1]++, !isMountedRef.current)) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[16][0]++;
      cov_120psm3w3w().s[71]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[16][1]++;
    }

    // Cancel any existing request
    cov_120psm3w3w().s[72]++;
    if (abortControllerRef.current) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[18][0]++;
      cov_120psm3w3w().s[73]++;
      abortControllerRef.current.abort();
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[18][1]++;
    }
    cov_120psm3w3w().s[74]++;
    abortControllerRef.current = new AbortController();
    const signal =
    /* istanbul ignore next */
    (cov_120psm3w3w().s[75]++, abortControllerRef.current.signal);
    /* istanbul ignore next */
    cov_120psm3w3w().s[76]++;
    setIsLoading(true);
    /* istanbul ignore next */
    cov_120psm3w3w().s[77]++;
    setError(null);
    /* istanbul ignore next */
    cov_120psm3w3w().s[78]++;
    try {
      const [statsData, renewalsData] =
      /* istanbul ignore next */
      (cov_120psm3w3w().s[79]++, await Promise.allSettled([fetchStats({
        signal
      }), fetchRenewals({
        signal
      })]));
      /* istanbul ignore next */
      cov_120psm3w3w().s[80]++;
      if (!isMountedRef.current) {
        /* istanbul ignore next */
        cov_120psm3w3w().b[19][0]++;
        cov_120psm3w3w().s[81]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_120psm3w3w().b[19][1]++;
      }
      const newData =
      /* istanbul ignore next */
      (cov_120psm3w3w().s[82]++, {
        stats: statsData.status === 'fulfilled' ?
        /* istanbul ignore next */
        (cov_120psm3w3w().b[20][0]++, statsData.value) :
        /* istanbul ignore next */
        (cov_120psm3w3w().b[20][1]++, defaultStats),
        recentRenewals: renewalsData.status === 'fulfilled' ?
        /* istanbul ignore next */
        (cov_120psm3w3w().b[21][0]++, renewalsData.value) :
        /* istanbul ignore next */
        (cov_120psm3w3w().b[21][1]++, defaultRenewals),
        upcomingRenewals: renewalsData.status === 'fulfilled' ?
        /* istanbul ignore next */
        (cov_120psm3w3w().b[22][0]++, renewalsData.value) :
        /* istanbul ignore next */
        (cov_120psm3w3w().b[22][1]++, defaultRenewals)
      });
      /* istanbul ignore next */
      cov_120psm3w3w().s[83]++;
      setData(newData);

      // Log any errors but don't fail the entire operation
      /* istanbul ignore next */
      cov_120psm3w3w().s[84]++;
      if (statsData.status === 'rejected') {
        /* istanbul ignore next */
        cov_120psm3w3w().b[23][0]++;
        cov_120psm3w3w().s[85]++;
        console.error('Failed to fetch stats:', statsData.reason);
      } else
      /* istanbul ignore next */
      {
        cov_120psm3w3w().b[23][1]++;
      }
      cov_120psm3w3w().s[86]++;
      if (renewalsData.status === 'rejected') {
        /* istanbul ignore next */
        cov_120psm3w3w().b[24][0]++;
        cov_120psm3w3w().s[87]++;
        console.error('Failed to fetch renewals:', renewalsData.reason);
      } else
      /* istanbul ignore next */
      {
        cov_120psm3w3w().b[24][1]++;
      }
    } catch (err) {
      /* istanbul ignore next */
      cov_120psm3w3w().s[88]++;
      if (!isMountedRef.current) {
        /* istanbul ignore next */
        cov_120psm3w3w().b[25][0]++;
        cov_120psm3w3w().s[89]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_120psm3w3w().b[25][1]++;
      }
      const errorMessage =
      /* istanbul ignore next */
      (cov_120psm3w3w().s[90]++, err instanceof Error ?
      /* istanbul ignore next */
      (cov_120psm3w3w().b[26][0]++, err.message) :
      /* istanbul ignore next */
      (cov_120psm3w3w().b[26][1]++, 'Failed to fetch dashboard data'));
      /* istanbul ignore next */
      cov_120psm3w3w().s[91]++;
      setError(errorMessage);
      /* istanbul ignore next */
      cov_120psm3w3w().s[92]++;
      console.error('Dashboard data fetch error:', err);
    } finally {
      /* istanbul ignore next */
      cov_120psm3w3w().s[93]++;
      if (isMountedRef.current) {
        /* istanbul ignore next */
        cov_120psm3w3w().b[27][0]++;
        cov_120psm3w3w().s[94]++;
        setIsLoading(false);
      } else
      /* istanbul ignore next */
      {
        cov_120psm3w3w().b[27][1]++;
      }
    }
  }, [tenant, fetchStats, fetchRenewals]));

  // Individual refetch functions
  const refetchStats =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[95]++,
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _react.
  /* istanbul ignore next */
  useCallback)(async () => {
    /* istanbul ignore next */
    cov_120psm3w3w().f[7]++;
    cov_120psm3w3w().s[96]++;
    if (!tenant) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[28][0]++;
      cov_120psm3w3w().s[97]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[28][1]++;
    }
    cov_120psm3w3w().s[98]++;
    try {
      const stats =
      /* istanbul ignore next */
      (cov_120psm3w3w().s[99]++, await fetchStats());
      /* istanbul ignore next */
      cov_120psm3w3w().s[100]++;
      if (isMountedRef.current) {
        /* istanbul ignore next */
        cov_120psm3w3w().b[29][0]++;
        cov_120psm3w3w().s[101]++;
        setData(prev => {
          /* istanbul ignore next */
          cov_120psm3w3w().f[8]++;
          cov_120psm3w3w().s[102]++;
          return /* istanbul ignore next */_objectSpread(_objectSpread({}, prev), {}, {
            stats
          });
        });
      } else
      /* istanbul ignore next */
      {
        cov_120psm3w3w().b[29][1]++;
      }
    } catch (err) {
      /* istanbul ignore next */
      cov_120psm3w3w().s[103]++;
      console.error('Failed to refetch stats:', err);
    }
  }, [tenant, fetchStats]));
  const refetchRenewals =
  /* istanbul ignore next */
  (cov_120psm3w3w().s[104]++,
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _react.
  /* istanbul ignore next */
  useCallback)(async () => {
    /* istanbul ignore next */
    cov_120psm3w3w().f[9]++;
    cov_120psm3w3w().s[105]++;
    if (!tenant) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[30][0]++;
      cov_120psm3w3w().s[106]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_120psm3w3w().b[30][1]++;
    }
    cov_120psm3w3w().s[107]++;
    try {
      const renewals =
      /* istanbul ignore next */
      (cov_120psm3w3w().s[108]++, await fetchRenewals());
      /* istanbul ignore next */
      cov_120psm3w3w().s[109]++;
      if (isMountedRef.current) {
        /* istanbul ignore next */
        cov_120psm3w3w().b[31][0]++;
        cov_120psm3w3w().s[110]++;
        setData(prev => {
          /* istanbul ignore next */
          cov_120psm3w3w().f[10]++;
          cov_120psm3w3w().s[111]++;
          return /* istanbul ignore next */_objectSpread(_objectSpread({}, prev), {}, {
            recentRenewals: renewals,
            upcomingRenewals: renewals
          });
        });
      } else
      /* istanbul ignore next */
      {
        cov_120psm3w3w().b[31][1]++;
      }
    } catch (err) {
      /* istanbul ignore next */
      cov_120psm3w3w().s[112]++;
      console.error('Failed to refetch renewals:', err);
    }
  }, [tenant, fetchRenewals]));

  // Main effect to fetch data when tenant changes
  /* istanbul ignore next */
  cov_120psm3w3w().s[113]++;
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _react.
  /* istanbul ignore next */
  useEffect)(() => {
    /* istanbul ignore next */
    cov_120psm3w3w().f[11]++;
    cov_120psm3w3w().s[114]++;
    if (
    /* istanbul ignore next */
    (cov_120psm3w3w().b[33][0]++, tenant) &&
    /* istanbul ignore next */
    (cov_120psm3w3w().b[33][1]++, !tenantLoading) &&
    /* istanbul ignore next */
    (cov_120psm3w3w().b[33][2]++, !tenantError)) {
      /* istanbul ignore next */
      cov_120psm3w3w().b[32][0]++;
      cov_120psm3w3w().s[115]++;
      fetchDashboardData();
    } else {
      /* istanbul ignore next */
      cov_120psm3w3w().b[32][1]++;
      cov_120psm3w3w().s[116]++;
      if (tenantError) {
        /* istanbul ignore next */
        cov_120psm3w3w().b[34][0]++;
        cov_120psm3w3w().s[117]++;
        setError(tenantError);
      } else
      /* istanbul ignore next */
      {
        cov_120psm3w3w().b[34][1]++;
      }
    }
  }, [tenant, tenantLoading, tenantError, fetchDashboardData]);

  // Cleanup effect
  /* istanbul ignore next */
  cov_120psm3w3w().s[118]++;
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _react.
  /* istanbul ignore next */
  useEffect)(() => {
    /* istanbul ignore next */
    cov_120psm3w3w().f[12]++;
    cov_120psm3w3w().s[119]++;
    return () => {
      /* istanbul ignore next */
      cov_120psm3w3w().f[13]++;
      cov_120psm3w3w().s[120]++;
      isMountedRef.current = false;
      /* istanbul ignore next */
      cov_120psm3w3w().s[121]++;
      if (abortControllerRef.current) {
        /* istanbul ignore next */
        cov_120psm3w3w().b[35][0]++;
        cov_120psm3w3w().s[122]++;
        abortControllerRef.current.abort();
      } else
      /* istanbul ignore next */
      {
        cov_120psm3w3w().b[35][1]++;
      }
    };
  }, []);
  /* istanbul ignore next */
  cov_120psm3w3w().s[123]++;
  return {
    data,
    isLoading:
    /* istanbul ignore next */
    (cov_120psm3w3w().b[36][0]++, isLoading) ||
    /* istanbul ignore next */
    (cov_120psm3w3w().b[36][1]++, tenantLoading),
    error:
    /* istanbul ignore next */
    (cov_120psm3w3w().b[37][0]++, error) ||
    /* istanbul ignore next */
    (cov_120psm3w3w().b[37][1]++, tenantError),
    refetch: fetchDashboardData,
    refetchStats,
    refetchRenewals
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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