253225b4d24f0a460c180b50c85565f1
/* istanbul ignore next */
function cov_1t9xjs7xl8() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\dashboard\\scan-results\\route.ts";
  var hash = "84f27884dca77a65a9f8c55588dc5cd49f3cc6d2";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\dashboard\\scan-results\\route.ts",
    statementMap: {
      "0": {
        start: {
          line: 20,
          column: 19
        },
        end: {
          line: 48,
          column: 2
        }
      },
      "1": {
        start: {
          line: 22,
          column: 21
        },
        end: {
          line: 22,
          column: 40
        }
      },
      "2": {
        start: {
          line: 23,
          column: 2
        },
        end: {
          line: 25,
          column: 3
        }
      },
      "3": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 32
        }
      },
      "4": {
        start: {
          line: 27,
          column: 18
        },
        end: {
          line: 27,
          column: 37
        }
      },
      "5": {
        start: {
          line: 29,
          column: 2
        },
        end: {
          line: 47,
          column: 3
        }
      },
      "6": {
        start: {
          line: 32,
          column: 38
        },
        end: {
          line: 32,
          column: 40
        }
      },
      "7": {
        start: {
          line: 33,
          column: 25
        },
        end: {
          line: 33,
          column: 29
        }
      },
      "8": {
        start: {
          line: 35,
          column: 4
        },
        end: {
          line: 38,
          column: 46
        }
      },
      "9": {
        start: {
          line: 41,
          column: 4
        },
        end: {
          line: 41,
          column: 57
        }
      },
      "10": {
        start: {
          line: 42,
          column: 4
        },
        end: {
          line: 46,
          column: 6
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 20,
            column: 37
          },
          end: {
            line: 20,
            column: 38
          }
        },
        loc: {
          start: {
            line: 20,
            column: 49
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 20
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 23,
            column: 2
          },
          end: {
            line: 25,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 2
          },
          end: {
            line: 25,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "84f27884dca77a65a9f8c55588dc5cd49f3cc6d2"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1t9xjs7xl8 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1t9xjs7xl8();
import { requireAuth } from '@/lib/auth-middleware';
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus, withErrorHandling } from '@/lib/api-response';

// Scan result interface

export const GET =
/* istanbul ignore next */
(cov_1t9xjs7xl8().s[0]++, withErrorHandling(async () => {
  /* istanbul ignore next */
  cov_1t9xjs7xl8().f[0]++;
  // Verify authentication and get session
  const authResult =
  /* istanbul ignore next */
  (cov_1t9xjs7xl8().s[1]++, await requireAuth());
  /* istanbul ignore next */
  cov_1t9xjs7xl8().s[2]++;
  if (!authResult.success) {
    /* istanbul ignore next */
    cov_1t9xjs7xl8().b[0][0]++;
    cov_1t9xjs7xl8().s[3]++;
    return authResult.response;
  } else
  /* istanbul ignore next */
  {
    cov_1t9xjs7xl8().b[0][1]++;
  }
  const session =
  /* istanbul ignore next */
  (cov_1t9xjs7xl8().s[4]++, authResult.session);
  /* istanbul ignore next */
  cov_1t9xjs7xl8().s[5]++;
  try {
    // TODO: Replace with actual database query
    // For now, return empty results as placeholder
    const scanResults =
    /* istanbul ignore next */
    (cov_1t9xjs7xl8().s[6]++, []);
    const lastScanDate =
    /* istanbul ignore next */
    (cov_1t9xjs7xl8().s[7]++, null);
    /* istanbul ignore next */
    cov_1t9xjs7xl8().s[8]++;
    return createSuccessResponse({
      results: scanResults,
      lastScanDate: lastScanDate
    }, 'Scan results retrieved successfully');
  } catch (error) {
    /* istanbul ignore next */
    cov_1t9xjs7xl8().s[9]++;
    console.error('Error fetching scan results:', error);
    /* istanbul ignore next */
    cov_1t9xjs7xl8().s[10]++;
    return createErrorResponse('Failed to fetch scan results', ApiErrorCode.DATABASE_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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