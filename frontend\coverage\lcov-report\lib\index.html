
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for lib</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> lib</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">12.13% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>109/898</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">6.84% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>36/526</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">14.15% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>30/212</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">12.18% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>106/870</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="actions.ts"><a href="actions.ts.html">actions.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="24" class="abs low">0/24</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="13" class="abs low">0/13</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="24" class="abs low">0/24</td>
	</tr>

<tr>
	<td class="file low" data-value="amplify-config.ts"><a href="amplify-config.ts.html">amplify-config.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	</tr>

<tr>
	<td class="file low" data-value="api-response.ts"><a href="api-response.ts.html">api-response.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="38" class="abs low">0/38</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="25" class="abs low">0/25</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="13" class="abs low">0/13</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="37" class="abs low">0/37</td>
	</tr>

<tr>
	<td class="file low" data-value="auth-middleware.ts"><a href="auth-middleware.ts.html">auth-middleware.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="56" class="abs low">0/56</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="29" class="abs low">0/29</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="18" class="abs low">0/18</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="53" class="abs low">0/53</td>
	</tr>

<tr>
	<td class="file low" data-value="auth.ts"><a href="auth.ts.html">auth.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="37" class="abs low">0/37</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="37" class="abs low">0/37</td>
	</tr>

<tr>
	<td class="file low" data-value="aws-config.ts"><a href="aws-config.ts.html">aws-config.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="11" class="abs low">0/11</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="11" class="abs low">0/11</td>
	</tr>

<tr>
	<td class="file high" data-value="cache.ts"><a href="cache.ts.html">cache.ts</a></td>
	<td data-value="82.57" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 82%"></div><div class="cover-empty" style="width: 18%"></div></div>
	</td>
	<td data-value="82.57" class="pct high">82.57%</td>
	<td data-value="132" class="abs high">109/132</td>
	<td data-value="70.58" class="pct high">70.58%</td>
	<td data-value="51" class="abs high">36/51</td>
	<td data-value="81.08" class="pct high">81.08%</td>
	<td data-value="37" class="abs high">30/37</td>
	<td data-value="83.46" class="pct high">83.46%</td>
	<td data-value="127" class="abs high">106/127</td>
	</tr>

<tr>
	<td class="file low" data-value="clients.ts"><a href="clients.ts.html">clients.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="72" class="abs low">0/72</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="54" class="abs low">0/54</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="5" class="abs low">0/5</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="72" class="abs low">0/72</td>
	</tr>

<tr>
	<td class="file low" data-value="config.ts"><a href="config.ts.html">config.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="23" class="abs low">0/23</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="20" class="abs low">0/20</td>
	</tr>

<tr>
	<td class="file low" data-value="dal.ts"><a href="dal.ts.html">dal.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="39" class="abs low">0/39</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="20" class="abs low">0/20</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="37" class="abs low">0/37</td>
	</tr>

<tr>
	<td class="file low" data-value="database.ts"><a href="database.ts.html">database.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="89" class="abs low">0/89</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="57" class="abs low">0/57</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="88" class="abs low">0/88</td>
	</tr>

<tr>
	<td class="file low" data-value="db-config.ts"><a href="db-config.ts.html">db-config.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="30" class="abs low">0/30</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="19" class="abs low">0/19</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="5" class="abs low">0/5</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="29" class="abs low">0/29</td>
	</tr>

<tr>
	<td class="file low" data-value="direct-auth-config.ts"><a href="direct-auth-config.ts.html">direct-auth-config.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	</tr>

<tr>
	<td class="file low" data-value="jwt-validator.ts"><a href="jwt-validator.ts.html">jwt-validator.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="51" class="abs low">0/51</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="22" class="abs low">0/22</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="51" class="abs low">0/51</td>
	</tr>

<tr>
	<td class="file low" data-value="performance.ts"><a href="performance.ts.html">performance.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="118" class="abs low">0/118</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="55" class="abs low">0/55</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="36" class="abs low">0/36</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="114" class="abs low">0/114</td>
	</tr>

<tr>
	<td class="file low" data-value="runtime-env.ts"><a href="runtime-env.ts.html">runtime-env.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	</tr>

<tr>
	<td class="file low" data-value="type-utils.ts"><a href="type-utils.ts.html">type-utils.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="89" class="abs low">0/89</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="81" class="abs low">0/81</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="41" class="abs low">0/41</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="82" class="abs low">0/82</td>
	</tr>

<tr>
	<td class="file low" data-value="types.ts"><a href="types.ts.html">types.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="49" class="abs low">0/49</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	</tr>

<tr>
	<td class="file low" data-value="validation.ts"><a href="validation.ts.html">validation.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="58" class="abs low">0/58</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="23" class="abs low">0/23</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="5" class="abs low">0/5</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="58" class="abs low">0/58</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-19T01:37:05.440Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    