7211f4e4c07b2e65e22678f81f64712a
"use strict";

var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\__tests__\\components\\modals\\AddRenewalModal.test.tsx";
// Mock the step components
_getJestObj().mock('@/components/modals/steps/RenewalDetailsStep', () => {
  return function MockRenewalDetailsStep({
    data,
    onChange,
    onNext,
    onCancel
  }) {
    return __jsx("div", {
      "data-testid": "renewal-details-step",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 14,
        columnNumber: 7
      }
    }, __jsx("input", {
      "data-testid": "product-name",
      value: data.productName,
      onChange: e => onChange(_objectSpread(_objectSpread({}, data), {}, {
        productName: e.target.value
      })),
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 15,
        columnNumber: 9
      }
    }), __jsx("button", {
      "data-testid": "next-button",
      onClick: onNext,
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 20,
        columnNumber: 9
      }
    }, "Next"), __jsx("button", {
      "data-testid": "cancel-button",
      onClick: onCancel,
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 23,
        columnNumber: 9
      }
    }, "Cancel"));
  };
});
_getJestObj().mock('@/components/modals/steps/SetupAlertsStep', () => {
  return function MockSetupAlertsStep({
    data,
    onChange,
    onBack,
    onSubmit,
    isSubmitting
  }) {
    return __jsx("div", {
      "data-testid": "setup-alerts-step",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 34,
        columnNumber: 7
      }
    }, __jsx("input", {
      "data-testid": "days-before",
      type: "number",
      value: data[0]?.daysBeforeRenewal || 30,
      onChange: e => {
        const newData = [...data];
        newData[0] = _objectSpread(_objectSpread({}, newData[0]), {}, {
          daysBeforeRenewal: parseInt(e.target.value) || 30
        });
        onChange(newData);
      },
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 35,
        columnNumber: 9
      }
    }), __jsx("button", {
      "data-testid": "back-button",
      onClick: onBack,
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 45,
        columnNumber: 9
      }
    }, "Back"), __jsx("button", {
      "data-testid": "submit-button",
      onClick: onSubmit,
      disabled: isSubmitting,
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 48,
        columnNumber: 9
      }
    }, isSubmitting ? 'Saving...' : 'Save & Finish'));
  };
});
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _react = _interopRequireDefault(require("react"));
var _react2 = require("@testing-library/react");
var _userEvent = _interopRequireDefault(require("@testing-library/user-event"));
var _AddRenewalModal = _interopRequireDefault(require("@/components/modals/AddRenewalModal"));
/**
 * AddRenewalModal Component Tests
 */
var __jsx = _react.default.createElement;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
describe('AddRenewalModal', () => {
  const mockOnClose = jest.fn();
  const mockOnSubmit = jest.fn();
  const defaultProps = {
    isOpen: true,
    onClose: mockOnClose,
    onSubmit: mockOnSubmit
  };
  beforeEach(() => {
    jest.clearAllMocks();
  });
  it('renders the modal when open', () => {
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 71,
        columnNumber: 12
      }
    })));
    expect(_react2.screen.getByText('Add New Renewal')).toBeInTheDocument();
    expect(_react2.screen.getByText('Enter the details of the renewal you want to track.')).toBeInTheDocument();
  });
  it('does not render when closed', () => {
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      isOpen: false,
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 78,
        columnNumber: 12
      }
    })));
    expect(_react2.screen.queryByText('Add New Renewal')).not.toBeInTheDocument();
  });
  it('shows step 1 initially', () => {
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 84,
        columnNumber: 12
      }
    })));
    expect(_react2.screen.getByTestId('renewal-details-step')).toBeInTheDocument();
    expect(_react2.screen.queryByTestId('setup-alerts-step')).not.toBeInTheDocument();
  });
  it('navigates to step 2 when next is clicked', async () => {
    const user = _userEvent.default.setup();
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 92,
        columnNumber: 12
      }
    })));
    const nextButton = _react2.screen.getByTestId('next-button');
    await user.click(nextButton);
    expect(_react2.screen.getByTestId('setup-alerts-step')).toBeInTheDocument();
    expect(_react2.screen.queryByTestId('renewal-details-step')).not.toBeInTheDocument();
    expect(_react2.screen.getByText('Edit Renewal')).toBeInTheDocument();
  });
  it('navigates back to step 1 from step 2', async () => {
    const user = _userEvent.default.setup();
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 104,
        columnNumber: 12
      }
    })));

    // Go to step 2
    await user.click(_react2.screen.getByTestId('next-button'));

    // Go back to step 1
    await user.click(_react2.screen.getByTestId('back-button'));
    expect(_react2.screen.getByTestId('renewal-details-step')).toBeInTheDocument();
    expect(_react2.screen.queryByTestId('setup-alerts-step')).not.toBeInTheDocument();
    expect(_react2.screen.getByText('Add New Renewal')).toBeInTheDocument();
  });
  it('calls onClose when cancel is clicked', async () => {
    const user = _userEvent.default.setup();
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 119,
        columnNumber: 12
      }
    })));
    await user.click(_react2.screen.getByTestId('cancel-button'));
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });
  it('calls onClose when close button is clicked', async () => {
    const user = _userEvent.default.setup();
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 128,
        columnNumber: 12
      }
    })));
    const closeButton = _react2.screen.getByLabelText('Close modal');
    await user.click(closeButton);
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });
  it('calls onSubmit with correct data when form is submitted', async () => {
    const user = _userEvent.default.setup();
    mockOnSubmit.mockResolvedValue(undefined);
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 140,
        columnNumber: 12
      }
    })));

    // Fill in some data in step 1
    const productNameInput = _react2.screen.getByTestId('product-name');
    await user.type(productNameInput, 'Test Product');

    // Go to step 2
    await user.click(_react2.screen.getByTestId('next-button'));

    // Modify alert data
    const daysBeforeInput = _react2.screen.getByTestId('days-before');
    _react2.fireEvent.change(daysBeforeInput, {
      target: {
        value: '60'
      }
    });

    // Submit
    await user.click(_react2.screen.getByTestId('submit-button'));
    await (0, _react2.waitFor)(() => {
      expect(mockOnSubmit).toHaveBeenCalledTimes(1);
    });
    const [renewalData, alertsData] = mockOnSubmit.mock.calls[0];
    expect(renewalData.productName).toBe('Test Product');
    expect(alertsData[0].daysBeforeRenewal).toBe(60);
  });
  it('shows loading state during submission', async () => {
    const user = _userEvent.default.setup();
    mockOnSubmit.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)));
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 169,
        columnNumber: 12
      }
    })));

    // Go to step 2
    await user.click(_react2.screen.getByTestId('next-button'));

    // Submit
    await user.click(_react2.screen.getByTestId('submit-button'));
    expect(_react2.screen.getByText('Saving...')).toBeInTheDocument();
    expect(_react2.screen.getByTestId('submit-button')).toBeDisabled();
  });
  it('resets form data after successful submission', async () => {
    const user = _userEvent.default.setup();
    mockOnSubmit.mockResolvedValue(undefined);
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 185,
        columnNumber: 12
      }
    })));

    // Fill in data
    const productNameInput = _react2.screen.getByTestId('product-name');
    await user.type(productNameInput, 'Test Product');

    // Go to step 2 and submit
    await user.click(_react2.screen.getByTestId('next-button'));
    await user.click(_react2.screen.getByTestId('submit-button'));
    await (0, _react2.waitFor)(() => {
      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });
  });
  it('handles submission errors gracefully', async () => {
    const user = _userEvent.default.setup();
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    mockOnSubmit.mockRejectedValue(new Error('Submission failed'));
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 205,
        columnNumber: 12
      }
    })));

    // Go to step 2 and submit
    await user.click(_react2.screen.getByTestId('next-button'));
    await user.click(_react2.screen.getByTestId('submit-button'));
    await (0, _react2.waitFor)(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error submitting renewal:', expect.any(Error));
    });

    // Modal should still be open
    expect(_react2.screen.getByTestId('setup-alerts-step')).toBeInTheDocument();
    consoleErrorSpy.mockRestore();
  });
  it('shows correct step indicators', async () => {
    const user = _userEvent.default.setup();
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 223,
        columnNumber: 12
      }
    })));

    // Step 1 should be active
    const step1 = _react2.screen.getByText('1');
    const step2 = _react2.screen.getByText('2');
    expect(step1.closest('.step')).toHaveClass('active');
    expect(step2.closest('.step')).not.toHaveClass('active');

    // Go to step 2
    await user.click(_react2.screen.getByTestId('next-button'));
    expect(step1.closest('.step')).toHaveClass('completed');
    expect(step2.closest('.step')).toHaveClass('active');
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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