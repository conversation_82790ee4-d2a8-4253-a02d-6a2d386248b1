ba7340c74b4904972415cd0be20cd017
/* istanbul ignore next */
function cov_28yqnqlnl() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\client-config.ts";
  var hash = "99318f2694e4f6a8f5f1e034441decab4bcfa3c9";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\client-config.ts",
    statementMap: {
      "0": {
        start: {
          line: 7,
          column: 28
        },
        end: {
          line: 23,
          column: 10
        }
      },
      "1": {
        start: {
          line: 26,
          column: 36
        },
        end: {
          line: 44,
          column: 1
        }
      },
      "2": {
        start: {
          line: 27,
          column: 23
        },
        end: {
          line: 34,
          column: 3
        }
      },
      "3": {
        start: {
          line: 36,
          column: 18
        },
        end: {
          line: 36,
          column: 63
        }
      },
      "4": {
        start: {
          line: 36,
          column: 45
        },
        end: {
          line: 36,
          column: 62
        }
      },
      "5": {
        start: {
          line: 38,
          column: 2
        },
        end: {
          line: 41,
          column: 3
        }
      },
      "6": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 70
        }
      },
      "7": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "8": {
        start: {
          line: 43,
          column: 2
        },
        end: {
          line: 43,
          column: 14
        }
      },
      "9": {
        start: {
          line: 47,
          column: 28
        },
        end: {
          line: 47,
          column: 50
        }
      },
      "10": {
        start: {
          line: 47,
          column: 34
        },
        end: {
          line: 47,
          column: 50
        }
      },
      "11": {
        start: {
          line: 48,
          column: 29
        },
        end: {
          line: 48,
          column: 52
        }
      },
      "12": {
        start: {
          line: 48,
          column: 35
        },
        end: {
          line: 48,
          column: 52
        }
      },
      "13": {
        start: {
          line: 49,
          column: 28
        },
        end: {
          line: 49,
          column: 50
        }
      },
      "14": {
        start: {
          line: 49,
          column: 34
        },
        end: {
          line: 49,
          column: 50
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 26,
            column: 36
          },
          end: {
            line: 26,
            column: 37
          }
        },
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 44,
            column: 1
          }
        },
        line: 26
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 36,
            column: 38
          },
          end: {
            line: 36,
            column: 39
          }
        },
        loc: {
          start: {
            line: 36,
            column: 45
          },
          end: {
            line: 36,
            column: 62
          }
        },
        line: 36
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 47,
            column: 28
          },
          end: {
            line: 47,
            column: 29
          }
        },
        loc: {
          start: {
            line: 47,
            column: 34
          },
          end: {
            line: 47,
            column: 50
          }
        },
        line: 47
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 48,
            column: 29
          },
          end: {
            line: 48,
            column: 30
          }
        },
        loc: {
          start: {
            line: 48,
            column: 35
          },
          end: {
            line: 48,
            column: 52
          }
        },
        line: 48
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 49,
            column: 28
          },
          end: {
            line: 49,
            column: 29
          }
        },
        loc: {
          start: {
            line: 49,
            column: 34
          },
          end: {
            line: 49,
            column: 50
          }
        },
        line: 49
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 19,
            column: 17
          },
          end: {
            line: 19,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 17
          },
          end: {
            line: 19,
            column: 37
          }
        }, {
          start: {
            line: 19,
            column: 41
          },
          end: {
            line: 19,
            column: 54
          }
        }],
        line: 19
      },
      "1": {
        loc: {
          start: {
            line: 38,
            column: 2
          },
          end: {
            line: 41,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 2
          },
          end: {
            line: 41,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "99318f2694e4f6a8f5f1e034441decab4bcfa3c9"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_28yqnqlnl = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_28yqnqlnl();
/**
 * Client-side configuration
 * This file provides environment variables that are safe to expose to the client
 */

// These values are injected at build time by Next.js
export const clientConfig =
/* istanbul ignore next */
(cov_28yqnqlnl().s[0]++, {
  aws: {
    region: process.env.NEXT_PUBLIC_AWS_REGION,
    userPoolId: process.env.NEXT_PUBLIC_AWS_USER_POOLS_ID,
    userPoolClientId: process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID,
    cognitoDomain: process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN
  },
  auth: {
    redirectSignIn: process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN,
    redirectSignOut: process.env.NEXT_PUBLIC_REDIRECT_SIGN_OUT
  },
  app: {
    environment:
    /* istanbul ignore next */
    (cov_28yqnqlnl().b[0][0]++, process.env.NODE_ENV) ||
    /* istanbul ignore next */
    (cov_28yqnqlnl().b[0][1]++, 'development'),
    isDevelopment: process.env.NODE_ENV === 'development',
    isProduction: process.env.NODE_ENV === 'production'
  }
});

// Validation function for client config
/* istanbul ignore next */
cov_28yqnqlnl().s[1]++;
export const validateClientConfig = () => {
  /* istanbul ignore next */
  cov_28yqnqlnl().f[0]++;
  const requiredVars =
  /* istanbul ignore next */
  (cov_28yqnqlnl().s[2]++, ['NEXT_PUBLIC_AWS_REGION', 'NEXT_PUBLIC_AWS_USER_POOLS_ID', 'NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID', 'NEXT_PUBLIC_AWS_COGNITO_DOMAIN', 'NEXT_PUBLIC_REDIRECT_SIGN_IN', 'NEXT_PUBLIC_REDIRECT_SIGN_OUT']);
  const missing =
  /* istanbul ignore next */
  (cov_28yqnqlnl().s[3]++, requiredVars.filter(key => {
    /* istanbul ignore next */
    cov_28yqnqlnl().f[1]++;
    cov_28yqnqlnl().s[4]++;
    return !process.env[key];
  }));
  /* istanbul ignore next */
  cov_28yqnqlnl().s[5]++;
  if (missing.length > 0) {
    /* istanbul ignore next */
    cov_28yqnqlnl().b[1][0]++;
    cov_28yqnqlnl().s[6]++;
    console.error('Missing required environment variables:', missing);
    /* istanbul ignore next */
    cov_28yqnqlnl().s[7]++;
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  } else
  /* istanbul ignore next */
  {
    cov_28yqnqlnl().b[1][1]++;
  }
  cov_28yqnqlnl().s[8]++;
  return true;
};

// Export individual getters for convenience
/* istanbul ignore next */
cov_28yqnqlnl().s[9]++;
export const getAwsConfig = () => {
  /* istanbul ignore next */
  cov_28yqnqlnl().f[2]++;
  cov_28yqnqlnl().s[10]++;
  return clientConfig.aws;
};
/* istanbul ignore next */
cov_28yqnqlnl().s[11]++;
export const getAuthConfig = () => {
  /* istanbul ignore next */
  cov_28yqnqlnl().f[3]++;
  cov_28yqnqlnl().s[12]++;
  return clientConfig.auth;
};
/* istanbul ignore next */
cov_28yqnqlnl().s[13]++;
export const getAppConfig = () => {
  /* istanbul ignore next */
  cov_28yqnqlnl().f[4]++;
  cov_28yqnqlnl().s[14]++;
  return clientConfig.app;
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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