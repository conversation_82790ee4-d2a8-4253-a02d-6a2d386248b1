3aecd43bd1c6313fd8ce423b7f21ce07
/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\layout\\Sidebar.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_2lfieank0g() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\layout\\Sidebar.tsx";
  var hash = "a5883d49600f226bd450199263ea6b9e7a55e239";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\layout\\Sidebar.tsx",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 19
        },
        end: {
          line: 6,
          column: 32
        }
      },
      "1": {
        start: {
          line: 7,
          column: 28
        },
        end: {
          line: 7,
          column: 37
        }
      },
      "2": {
        start: {
          line: 10,
          column: 17
        },
        end: {
          line: 14,
          column: 3
        }
      },
      "3": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 47
        }
      },
      "4": {
        start: {
          line: 11,
          column: 35
        },
        end: {
          line: 11,
          column: 47
        }
      },
      "5": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 72
        }
      },
      "6": {
        start: {
          line: 17,
          column: 22
        },
        end: {
          line: 21,
          column: 44
        }
      },
      "7": {
        start: {
          line: 24,
          column: 23
        },
        end: {
          line: 26,
          column: 49
        }
      },
      "8": {
        start: {
          line: 28,
          column: 2
        },
        end: {
          line: 153,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "Sidebar",
        decl: {
          start: {
            line: 5,
            column: 24
          },
          end: {
            line: 5,
            column: 31
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 154,
            column: 1
          }
        },
        line: 5
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 10,
            column: 17
          },
          end: {
            line: 10,
            column: 18
          }
        },
        loc: {
          start: {
            line: 10,
            column: 34
          },
          end: {
            line: 14,
            column: 3
          }
        },
        line: 10
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 11,
            column: 4
          },
          end: {
            line: 11,
            column: 47
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 11,
            column: 4
          },
          end: {
            line: 11,
            column: 47
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 11
      },
      "1": {
        loc: {
          start: {
            line: 11,
            column: 8
          },
          end: {
            line: 11,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 8
          },
          end: {
            line: 11,
            column: 12
          }
        }, {
          start: {
            line: 11,
            column: 16
          },
          end: {
            line: 11,
            column: 33
          }
        }],
        line: 11
      },
      "2": {
        loc: {
          start: {
            line: 13,
            column: 11
          },
          end: {
            line: 13,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 12
          },
          end: {
            line: 13,
            column: 29
          }
        }, {
          start: {
            line: 13,
            column: 33
          },
          end: {
            line: 13,
            column: 48
          }
        }, {
          start: {
            line: 13,
            column: 53
          },
          end: {
            line: 13,
            column: 71
          }
        }],
        line: 13
      },
      "3": {
        loc: {
          start: {
            line: 17,
            column: 22
          },
          end: {
            line: 21,
            column: 44
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 6
          },
          end: {
            line: 18,
            column: 46
          }
        }, {
          start: {
            line: 19,
            column: 6
          },
          end: {
            line: 21,
            column: 44
          }
        }],
        line: 17
      },
      "4": {
        loc: {
          start: {
            line: 17,
            column: 22
          },
          end: {
            line: 17,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 22
          },
          end: {
            line: 17,
            column: 38
          }
        }, {
          start: {
            line: 17,
            column: 42
          },
          end: {
            line: 17,
            column: 59
          }
        }],
        line: 17
      },
      "5": {
        loc: {
          start: {
            line: 19,
            column: 6
          },
          end: {
            line: 21,
            column: 44
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 20,
            column: 17
          }
        }, {
          start: {
            line: 21,
            column: 8
          },
          end: {
            line: 21,
            column: 44
          }
        }],
        line: 19
      },
      "6": {
        loc: {
          start: {
            line: 21,
            column: 8
          },
          end: {
            line: 21,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 8
          },
          end: {
            line: 21,
            column: 34
          }
        }, {
          start: {
            line: 21,
            column: 38
          },
          end: {
            line: 21,
            column: 44
          }
        }],
        line: 21
      },
      "7": {
        loc: {
          start: {
            line: 24,
            column: 23
          },
          end: {
            line: 26,
            column: 49
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 25,
            column: 6
          },
          end: {
            line: 25,
            column: 16
          }
        }, {
          start: {
            line: 26,
            column: 6
          },
          end: {
            line: 26,
            column: 49
          }
        }],
        line: 24
      },
      "8": {
        loc: {
          start: {
            line: 24,
            column: 23
          },
          end: {
            line: 24,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 23
          },
          end: {
            line: 24,
            column: 34
          }
        }, {
          start: {
            line: 24,
            column: 38
          },
          end: {
            line: 24,
            column: 57
          }
        }],
        line: 24
      },
      "9": {
        loc: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 38
          }
        }, {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 44
          }
        }],
        line: 26
      },
      "10": {
        loc: {
          start: {
            line: 38,
            column: 59
          },
          end: {
            line: 38,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 38,
            column: 87
          },
          end: {
            line: 38,
            column: 95
          }
        }, {
          start: {
            line: 38,
            column: 98
          },
          end: {
            line: 38,
            column: 100
          }
        }],
        line: 38
      },
      "11": {
        loc: {
          start: {
            line: 48,
            column: 58
          },
          end: {
            line: 48,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 48,
            column: 85
          },
          end: {
            line: 48,
            column: 93
          }
        }, {
          start: {
            line: 48,
            column: 96
          },
          end: {
            line: 48,
            column: 98
          }
        }],
        line: 48
      },
      "12": {
        loc: {
          start: {
            line: 55,
            column: 54
          },
          end: {
            line: 55,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 55,
            column: 77
          },
          end: {
            line: 55,
            column: 85
          }
        }, {
          start: {
            line: 55,
            column: 88
          },
          end: {
            line: 55,
            column: 90
          }
        }],
        line: 55
      },
      "13": {
        loc: {
          start: {
            line: 62,
            column: 61
          },
          end: {
            line: 62,
            column: 104
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 62,
            column: 91
          },
          end: {
            line: 62,
            column: 99
          }
        }, {
          start: {
            line: 62,
            column: 102
          },
          end: {
            line: 62,
            column: 104
          }
        }],
        line: 62
      },
      "14": {
        loc: {
          start: {
            line: 70,
            column: 60
          },
          end: {
            line: 70,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 70,
            column: 89
          },
          end: {
            line: 70,
            column: 97
          }
        }, {
          start: {
            line: 70,
            column: 100
          },
          end: {
            line: 70,
            column: 102
          }
        }],
        line: 70
      },
      "15": {
        loc: {
          start: {
            line: 78,
            column: 57
          },
          end: {
            line: 78,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 78,
            column: 83
          },
          end: {
            line: 78,
            column: 91
          }
        }, {
          start: {
            line: 78,
            column: 94
          },
          end: {
            line: 78,
            column: 96
          }
        }],
        line: 78
      },
      "16": {
        loc: {
          start: {
            line: 85,
            column: 57
          },
          end: {
            line: 85,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 85,
            column: 83
          },
          end: {
            line: 85,
            column: 91
          }
        }, {
          start: {
            line: 85,
            column: 94
          },
          end: {
            line: 85,
            column: 96
          }
        }],
        line: 85
      },
      "17": {
        loc: {
          start: {
            line: 92,
            column: 65
          },
          end: {
            line: 92,
            column: 112
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 92,
            column: 99
          },
          end: {
            line: 92,
            column: 107
          }
        }, {
          start: {
            line: 92,
            column: 110
          },
          end: {
            line: 92,
            column: 112
          }
        }],
        line: 92
      },
      "18": {
        loc: {
          start: {
            line: 99,
            column: 57
          },
          end: {
            line: 99,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 99,
            column: 83
          },
          end: {
            line: 99,
            column: 91
          }
        }, {
          start: {
            line: 99,
            column: 94
          },
          end: {
            line: 99,
            column: 96
          }
        }],
        line: 99
      },
      "19": {
        loc: {
          start: {
            line: 106,
            column: 58
          },
          end: {
            line: 106,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 106,
            column: 85
          },
          end: {
            line: 106,
            column: 93
          }
        }, {
          start: {
            line: 106,
            column: 96
          },
          end: {
            line: 106,
            column: 98
          }
        }],
        line: 106
      },
      "20": {
        loc: {
          start: {
            line: 114,
            column: 58
          },
          end: {
            line: 114,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 114,
            column: 85
          },
          end: {
            line: 114,
            column: 93
          }
        }, {
          start: {
            line: 114,
            column: 96
          },
          end: {
            line: 114,
            column: 98
          }
        }],
        line: 114
      },
      "21": {
        loc: {
          start: {
            line: 121,
            column: 59
          },
          end: {
            line: 121,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 121,
            column: 87
          },
          end: {
            line: 121,
            column: 95
          }
        }, {
          start: {
            line: 121,
            column: 98
          },
          end: {
            line: 121,
            column: 100
          }
        }],
        line: 121
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a5883d49600f226bd450199263ea6b9e7a55e239"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2lfieank0g = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2lfieank0g();
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '../../contexts/AppContext';
export default function Sidebar() {
  /* istanbul ignore next */
  cov_2lfieank0g().f[0]++;
  const pathname =
  /* istanbul ignore next */
  (cov_2lfieank0g().s[0]++, usePathname());
  const {
    user,
    signOut
  } =
  /* istanbul ignore next */
  (cov_2lfieank0g().s[1]++, useAuth());

  // Check if a string looks like a UUID or is empty/invalid
  /* istanbul ignore next */
  cov_2lfieank0g().s[2]++;
  const isUUID = str => {
    /* istanbul ignore next */
    cov_2lfieank0g().f[1]++;
    cov_2lfieank0g().s[3]++;
    if (
    /* istanbul ignore next */
    (cov_2lfieank0g().b[1][0]++, !str) ||
    /* istanbul ignore next */
    (cov_2lfieank0g().b[1][1]++, str.trim() === '')) {
      /* istanbul ignore next */
      cov_2lfieank0g().b[0][0]++;
      cov_2lfieank0g().s[4]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_2lfieank0g().b[0][1]++;
    }
    // Check for UUID pattern or if it doesn't contain @ (not an email)
    cov_2lfieank0g().s[5]++;
    return /* istanbul ignore next */(cov_2lfieank0g().b[2][0]++, str.includes('-')) &&
    /* istanbul ignore next */
    (cov_2lfieank0g().b[2][1]++, str.length > 30) ||
    /* istanbul ignore next */
    (cov_2lfieank0g().b[2][2]++, !str.includes('@'));
  };

  // Format display name - safely access user properties
  const displayName =
  /* istanbul ignore next */
  (cov_2lfieank0g().s[6]++,
  /* istanbul ignore next */
  (cov_2lfieank0g().b[4][0]++, user?.given_name) &&
  /* istanbul ignore next */
  (cov_2lfieank0g().b[4][1]++, user?.family_name) ?
  /* istanbul ignore next */
  (cov_2lfieank0g().b[3][0]++, `${user.given_name} ${user.family_name}`) :
  /* istanbul ignore next */
  (cov_2lfieank0g().b[3][1]++, user?.name ?
  /* istanbul ignore next */
  (cov_2lfieank0g().b[5][0]++, user.name) :
  /* istanbul ignore next */
  (cov_2lfieank0g().b[5][1]++,
  /* istanbul ignore next */
  (cov_2lfieank0g().b[6][0]++, user?.email?.split('@')[0]) ||
  /* istanbul ignore next */
  (cov_2lfieank0g().b[6][1]++, 'User'))));

  // Format email display - only show email if it's a valid email
  const displayEmail =
  /* istanbul ignore next */
  (cov_2lfieank0g().s[7]++,
  /* istanbul ignore next */
  (cov_2lfieank0g().b[8][0]++, user?.email) &&
  /* istanbul ignore next */
  (cov_2lfieank0g().b[8][1]++, !isUUID(user.email)) ?
  /* istanbul ignore next */
  (cov_2lfieank0g().b[7][0]++, user.email) :
  /* istanbul ignore next */
  (cov_2lfieank0g().b[7][1]++, `ID: ${
  /* istanbul ignore next */
  (cov_2lfieank0g().b[9][0]++, user?.id?.substring(0, 8)) ||
  /* istanbul ignore next */
  (cov_2lfieank0g().b[9][1]++, '')}...`));
  /* istanbul ignore next */
  cov_2lfieank0g().s[8]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "aside",
  /* istanbul ignore next */
  {
    className: "sidebar",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 29,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "sidebar-logo",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 31,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h1",
  /* istanbul ignore next */
  {
    className: "text-2xl font-bold",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 32,
      columnNumber: 9
    }
  }, "R2T2"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-xs text-secondary",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 33,
      columnNumber: 9
    }
  }, "RENEWALS TRACKER")),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "nav",
  /* istanbul ignore next */
  {
    className: "sidebar-nav",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 37,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/dashboard",
    className: `sidebar-link ${pathname === '/dashboard' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[10][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[10][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 38,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 39,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M4 5a1 1 0 011-1h4a1 1 0 011 1v5a1 1 0 01-1 1H5a1 1 0 01-1-1V5z",
    fill: "currentColor",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 40,
      columnNumber: 13
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M14 5a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V5z",
    fill: "currentColor",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 41,
      columnNumber: 13
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M4 14a1 1 0 011-1h4a1 1 0 011 1v5a1 1 0 01-1 1H5a1 1 0 01-1-1v-5z",
    fill: "currentColor",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 42,
      columnNumber: 13
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M14 12a1 1 0 011-1h4a1 1 0 011 1v7a1 1 0 01-1 1h-4a1 1 0 01-1-1v-7z",
    fill: "currentColor",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 43,
      columnNumber: 13
    }
  })), "Dashboard"),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/renewals",
    className: `sidebar-link ${pathname === '/renewals' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[11][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[11][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 48,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 49,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 50,
      columnNumber: 13
    }
  })), "Renewals Inventory"),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/scan",
    className: `sidebar-link ${pathname === '/scan' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[12][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[12][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 55,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 56,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 57,
      columnNumber: 13
    }
  })), "Scan Network"),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/departments",
    className: `sidebar-link ${pathname === '/departments' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[13][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[13][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 62,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 63,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 64,
      columnNumber: 13
    }
  })), "Departments",
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    className: "badge badge-primary ml-auto",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 67,
      columnNumber: 11
    }
  }, "BETA")),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/financials",
    className: `sidebar-link ${pathname === '/financials' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[14][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[14][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 70,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 71,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 72,
      columnNumber: 13
    }
  })), "Financials",
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    className: "badge badge-primary ml-auto",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 75,
      columnNumber: 11
    }
  }, "BETA")),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/reports",
    className: `sidebar-link ${pathname === '/reports' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[15][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[15][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 78,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 79,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 80,
      columnNumber: 13
    }
  })), "Reports"),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/vendors",
    className: `sidebar-link ${pathname === '/vendors' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[16][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[16][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 85,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 86,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 87,
      columnNumber: 13
    }
  })), "Vendors"),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/user-management",
    className: `sidebar-link ${pathname === '/user-management' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[17][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[17][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 92,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 93,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 94,
      columnNumber: 13
    }
  })), "User Management"),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/license",
    className: `sidebar-link ${pathname === '/license' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[18][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[18][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 99,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 100,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 101,
      columnNumber: 13
    }
  })), "License"),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/settings",
    className: `sidebar-link ${pathname === '/settings' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[19][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[19][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 106,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 107,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 108,
      columnNumber: 13
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M15 12a3 3 0 11-6 0 3 3 0 016 0z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 109,
      columnNumber: 13
    }
  })), "Settings"),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/timeline",
    className: `sidebar-link ${pathname === '/timeline' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[20][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[20][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 114,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 115,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 116,
      columnNumber: 13
    }
  })), "Event Timeline"),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/documents",
    className: `sidebar-link ${pathname === '/documents' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[21][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[21][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 121,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 122,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 123,
      columnNumber: 13
    }
  })), "Documents")),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "sidebar-user",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 130,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "user-avatar",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 131,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    width: "16",
    height: "16",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 132,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",
    fill: "currentColor",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 133,
      columnNumber: 13
    }
  }))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex-1",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 136,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "user-email",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 137,
      columnNumber: 11
    }
  }, displayEmail),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "user-role",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 138,
      columnNumber: 11
    }
  }, displayName)),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  {
    onClick: signOut,
    className: "sidebar-signout",
    title: "Sign out",
    /* istanbul ignore next */
    "aria-label": "Sign out",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 140,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    width: "18",
    height: "18",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 146,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 147,
      columnNumber: 13
    }
  })))));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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