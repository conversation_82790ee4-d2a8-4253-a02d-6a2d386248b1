/**
 * Admin API endpoint to test vendor matching algorithm
 */

import { NextRequest } from 'next/server';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  ApiErrorCode, 
  HttpStatus,
  withErrorHandling 
} from '@/lib/api-response';
import { VendorMatcher } from '@/lib/sync/vendor-matcher';

export const GET = withErrorHandling(async (request: NextRequest) => {
  try {
    console.log('Testing vendor matching algorithm...');
    
    // Test the matching algorithm
    const matchResult = await VendorMatcher.testMatching();
    
    console.log('✅ Vendor matching test completed!');
    console.log('Match result:', JSON.stringify(matchResult, null, 2));
    
    return createSuccessResponse(
      matchResult,
      'Vendor matching algorithm tested successfully'
    );
    
  } catch (error) {
    console.error('❌ Error testing vendor matching:', error);
    return createErrorResponse(
      `Failed to test vendor matching: ${error}`,
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});
