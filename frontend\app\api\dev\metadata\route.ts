/**
 * Development-only Metadata API Endpoint
 * 
 * This endpoint bypasses authentication for development testing
 * WARNING: This should NEVER be deployed to production
 */

import { NextRequest } from 'next/server';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  ApiErrorCode, 
  HttpStatus,
  withErrorHandling 
} from '@/lib/api-response';
import { executeQuery } from '@/lib/database';
import { publicConfig } from '@/lib/config';

// Only allow in development
if (publicConfig.app.isProduction) {
  throw new Error('Development endpoints are not allowed in production');
}

// Interfaces for metadata types
export interface PurchaseType {
  PurchaseTypeID: number;
  PurchaseTypeName: string;
  Active: boolean;
  DisplayOrder: number;
}

export interface RenewalType {
  RenewalTypeID: number;
  RenewalTypeName: string;
  Active: boolean;
  DisplayOrder: number;
}

export interface Currency {
  CurrencyID: string;
  CurrencyName: string;
  CurrencySymbol: string;
  Active: boolean;
  DisplayOrder: number;
}

export interface MetadataResponse {
  purchaseTypes: PurchaseType[];
  renewalTypes: RenewalType[];
  currencies: Currency[];
}

// GET /api/dev/metadata - Get all metadata options (development only)
export const GET = withErrorHandling(async (request: NextRequest) => {
  console.log('⚠️  WARNING: Using development-only metadata endpoint');

  try {
    // Execute all metadata queries in parallel for better performance
    const [purchaseTypesResult, renewalTypesResult, currenciesResult] = await Promise.all([
      // Purchase Types query
      executeQuery<PurchaseType>(
        `SELECT 
          PurchaseTypeID,
          PurchaseTypeName,
          Active,
          DisplayOrder
        FROM metadata.PurchaseTypes 
        WHERE Active = true 
        ORDER BY DisplayOrder ASC, PurchaseTypeName ASC`,
        []
      ),
      
      // Renewal Types query
      executeQuery<RenewalType>(
        `SELECT 
          RenewalTypeID,
          RenewalTypeName,
          Active,
          DisplayOrder
        FROM metadata.RenewalTypes 
        WHERE Active = true 
        ORDER BY DisplayOrder ASC, RenewalTypeName ASC`,
        []
      ),
      
      // Currencies query
      executeQuery<Currency>(
        `SELECT 
          CurrencyID,
          CurrencyName,
          CurrencySymbol,
          Active,
          DisplayOrder
        FROM metadata.Currencies 
        WHERE Active = true 
        ORDER BY DisplayOrder ASC, CurrencyName ASC`,
        []
      )
    ]);

    // Check if all queries were successful
    if (!purchaseTypesResult.success) {
      console.error('Failed to fetch purchase types:', purchaseTypesResult.error);
      return createErrorResponse(
        'Failed to fetch purchase types',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    if (!renewalTypesResult.success) {
      console.error('Failed to fetch renewal types:', renewalTypesResult.error);
      return createErrorResponse(
        'Failed to fetch renewal types',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    if (!currenciesResult.success) {
      console.error('Failed to fetch currencies:', currenciesResult.error);
      return createErrorResponse(
        'Failed to fetch currencies',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    // Prepare response data
    const responseData: MetadataResponse = {
      purchaseTypes: purchaseTypesResult.data || [],
      renewalTypes: renewalTypesResult.data || [],
      currencies: currenciesResult.data || []
    };

    // Log successful fetch for monitoring
    console.log('Development metadata fetched successfully:', {
      purchaseTypes: responseData.purchaseTypes.length,
      renewalTypes: responseData.renewalTypes.length,
      currencies: responseData.currencies.length
    });

    return createSuccessResponse(
      responseData,
      'Metadata options retrieved successfully (development mode)'
    );

  } catch (error) {
    console.error('Error fetching metadata:', error);
    return createErrorResponse(
      'Failed to fetch metadata options',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

// Export types for use in other modules
export type { PurchaseType, RenewalType, Currency, MetadataResponse };
