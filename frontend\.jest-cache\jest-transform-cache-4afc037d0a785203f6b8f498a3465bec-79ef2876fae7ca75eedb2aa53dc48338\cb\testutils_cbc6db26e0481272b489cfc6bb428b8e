64580aeb5c90bea5dfe0c77fb0a17165
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
var _exportNames = {
  renderWithProviders: true,
  render: true,
  testUtils: true
};
exports.testUtils = exports.renderWithProviders = exports.render = exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _react = _interopRequireDefault(require("react"));
var _react2 = require("@testing-library/react");
Object.keys(_react2).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _react2[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _react2[key];
    }
  });
});
var _globals = require("@jest/globals");
const _excluded = ["withProviders"];
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\__tests__\\utils\\test-utils.tsx";
/**
 * Test Utilities
 * 
 * Comprehensive testing utilities for React components and hooks
 */
var __jsx = _react.default.createElement;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
// Mock providers for testing
const MockAuthProvider = ({
  children
}) => {
  const mockAuthContext = {
    user: global.testUtils.mockUser,
    isAuthenticated: true,
    isLoading: false,
    error: null,
    signIn: _globals.jest.fn(),
    signOut: _globals.jest.fn(),
    signUp: _globals.jest.fn()
  };
  return __jsx("div", {
    "data-testid": "mock-auth-provider",
    __self: void 0,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 24,
      columnNumber: 5
    }
  }, children);
};
const MockTenantProvider = ({
  children
}) => {
  const mockTenantContext = {
    tenant: global.testUtils.mockTenant,
    loading: false,
    error: null,
    refetch: _globals.jest.fn()
  };
  return __jsx("div", {
    "data-testid": "mock-tenant-provider",
    __self: void 0,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 39,
      columnNumber: 5
    }
  }, children);
};
const MockAppProvider = ({
  children
}) => {
  return __jsx(MockAuthProvider, {
    __self: void 0,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 47,
      columnNumber: 5
    }
  }, __jsx(MockTenantProvider, {
    __self: void 0,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 48,
      columnNumber: 7
    }
  }, children));
};

// Custom render function with providers

const renderWithProviders = (ui, options = {}) => {
  const {
      withProviders = true
    } = options,
    renderOptions = (0, _objectWithoutProperties2.default)(options, _excluded);
  const Wrapper = ({
    children
  }) => {
    if (withProviders) {
      return __jsx(MockAppProvider, {
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 69,
          columnNumber: 14
        }
      }, children);
    }
    return __jsx(_react.default.Fragment, null, children);
  };
  return (0, _react2.render)(ui, _objectSpread({
    wrapper: Wrapper
  }, renderOptions));
};

// Re-export everything from testing library
exports.render = exports.renderWithProviders = renderWithProviders;
// Custom matchers and utilities
const testUtils = exports.testUtils = {
  // Mock API responses
  mockApiSuccess: data => ({
    ok: true,
    status: 200,
    json: _globals.jest.fn().mockResolvedValue({
      success: true,
      data
    })
  }),
  mockApiError: (error, status = 400) => ({
    ok: false,
    status,
    json: _globals.jest.fn().mockResolvedValue({
      success: false,
      error
    })
  }),
  // Mock fetch responses
  mockFetch: response => {
    global.fetch = _globals.jest.fn().mockResolvedValue(response);
  },
  // Mock localStorage
  mockLocalStorage: () => {
    const store = {};
    return {
      getItem: _globals.jest.fn(key => store[key] || null),
      setItem: _globals.jest.fn((key, value) => {
        store[key] = value;
      }),
      removeItem: _globals.jest.fn(key => {
        delete store[key];
      }),
      clear: _globals.jest.fn(() => {
        Object.keys(store).forEach(key => delete store[key]);
      })
    };
  },
  // Mock console methods
  mockConsole: () => {
    const originalConsole = _objectSpread({}, console);
    return {
      log: _globals.jest.fn(),
      error: _globals.jest.fn(),
      warn: _globals.jest.fn(),
      info: _globals.jest.fn(),
      restore: () => {
        Object.assign(console, originalConsole);
      }
    };
  },
  // Wait for async operations
  waitFor: async (callback, timeout = 1000) => {
    const startTime = Date.now();
    while (Date.now() - startTime < timeout) {
      try {
        await callback();
        return;
      } catch (error) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }
    throw new Error(`Timeout after ${timeout}ms`);
  },
  // Create mock component
  createMockComponent: (name, props) => {
    const MockComponent = componentProps => __jsx("div", (0, _extends2.default)({
      "data-testid": `mock-${name.toLowerCase()}`
    }, props, componentProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 149,
        columnNumber: 7
      }
    }), name, " Mock");
    MockComponent.displayName = `Mock${name}`;
    return MockComponent;
  },
  // Mock hook return values
  mockHook: (hookName, returnValue) => {
    const mockModule = _globals.jest.fn().mockReturnValue(returnValue);
    _globals.jest.doMock(hookName, () => mockModule);
    return mockModule;
  },
  // Generate test data
  generateTestData: {
    user: (overrides = {}) => _objectSpread({
      id: 'test-user-id',
      email: '<EMAIL>',
      name: 'Test User',
      tenantId: 'test-tenant-id'
    }, overrides),
    tenant: (overrides = {}) => _objectSpread({
      tenantId: 'test-tenant-id',
      clientId: 'test-client-id',
      clientName: 'Test Client',
      domain: 'test.example.com'
    }, overrides),
    renewal: (overrides = {}) => _objectSpread({
      id: 'test-renewal-id',
      name: 'Test Software',
      vendor: 'Test Vendor',
      renewalDate: new Date('2025-03-01'),
      cost: 1000,
      status: 'active'
    }, overrides),
    dashboardStats: (overrides = {}) => _objectSpread({
      totalRenewals: 25,
      renewalsDue: 5,
      vendors: 12,
      annualSpend: '$125,000'
    }, overrides)
  },
  // Mock timers
  mockTimers: () => {
    _globals.jest.useFakeTimers();
    return {
      advanceTimersByTime: _globals.jest.advanceTimersByTime,
      runAllTimers: _globals.jest.runAllTimers,
      runOnlyPendingTimers: _globals.jest.runOnlyPendingTimers,
      restore: () => _globals.jest.useRealTimers()
    };
  },
  // Mock dates
  mockDate: date => {
    const mockDate = new Date(date);
    const originalDate = Date;
    global.Date = _globals.jest.fn(() => mockDate);
    global.Date.now = _globals.jest.fn(() => mockDate.getTime());
    return {
      restore: () => {
        global.Date = originalDate;
      }
    };
  },
  // Mock window methods
  mockWindow: (overrides = {}) => {
    const originalWindow = _objectSpread({}, window);
    Object.assign(window, overrides);
    return {
      restore: () => {
        Object.assign(window, originalWindow);
      }
    };
  },
  // Mock environment variables
  mockEnv: env => {
    const originalEnv = _objectSpread({}, process.env);
    Object.assign(process.env, env);
    return {
      restore: () => {
        process.env = originalEnv;
      }
    };
  }
};

// Export default render function
var _default = exports.default = renderWithProviders;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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