import { NextResponse } from 'next/server';
import { getClientByEmailDomain } from '@/lib/clients';
import { verifySession } from '@/lib/dal';

export async function GET() {
  try {
    // Verify authentication and get session
    const session = await verifySession();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get email from session instead of URL parameters for security
    const email = session.email;
    if (!email) {
      return NextResponse.json(
        { error: 'No email found in session' },
        { status: 400 }
      );
    }

    const result = await getClientByEmailDomain(email);

    if (!result.success) {
      const statusCode = result.errorCode === 'NOT_FOUND' ? 404 : 500;
      return NextResponse.json(
        {
          error: result.error,
          errorCode: result.errorCode
        },
        { status: statusCode }
      );
    }

    // Return tenant context
    return NextResponse.json({
      success: true,
      client: result.client
    });
  } catch (error) {
    console.error('Error in client domain API:', error);
    return NextResponse.json(
      {
        error: 'An error occurred, contact support',
        errorCode: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}





