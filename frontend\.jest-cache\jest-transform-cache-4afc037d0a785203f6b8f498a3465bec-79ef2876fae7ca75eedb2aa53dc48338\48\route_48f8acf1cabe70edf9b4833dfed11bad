c6b2f1389404d46fd2b356dc54bbfd22
/* istanbul ignore next */
function cov_20xbgfgwtk() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\tenants\\domain\\route.ts";
  var hash = "d7a267b3f30c8bcc341ca7bc14b549795ba00e23";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\tenants\\domain\\route.ts",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 22
        },
        end: {
          line: 8,
          column: 2
        }
      },
      "1": {
        start: {
          line: 12,
          column: 19
        },
        end: {
          line: 12,
          column: 37
        }
      },
      "2": {
        start: {
          line: 13,
          column: 2
        },
        end: {
          line: 13,
          column: 51
        }
      },
      "3": {
        start: {
          line: 14,
          column: 2
        },
        end: {
          line: 14,
          column: 54
        }
      },
      "4": {
        start: {
          line: 16,
          column: 23
        },
        end: {
          line: 16,
          column: 51
        }
      },
      "5": {
        start: {
          line: 17,
          column: 17
        },
        end: {
          line: 17,
          column: 43
        }
      },
      "6": {
        start: {
          line: 20,
          column: 2
        },
        end: {
          line: 27,
          column: 3
        }
      },
      "7": {
        start: {
          line: 21,
          column: 4
        },
        end: {
          line: 21,
          column: 36
        }
      },
      "8": {
        start: {
          line: 23,
          column: 4
        },
        end: {
          line: 26,
          column: 24
        }
      },
      "9": {
        start: {
          line: 29,
          column: 2
        },
        end: {
          line: 31,
          column: 3
        }
      },
      "10": {
        start: {
          line: 30,
          column: 4
        },
        end: {
          line: 30,
          column: 89
        }
      },
      "11": {
        start: {
          line: 33,
          column: 2
        },
        end: {
          line: 51,
          column: 3
        }
      },
      "12": {
        start: {
          line: 34,
          column: 19
        },
        end: {
          line: 34,
          column: 50
        }
      },
      "13": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 38,
          column: 5
        }
      },
      "14": {
        start: {
          line: 37,
          column: 6
        },
        end: {
          line: 37,
          column: 95
        }
      },
      "15": {
        start: {
          line: 41,
          column: 4
        },
        end: {
          line: 47,
          column: 7
        }
      },
      "16": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 49,
          column: 59
        }
      },
      "17": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 50,
          column: 82
        }
      }
    },
    fnMap: {
      "0": {
        name: "GET",
        decl: {
          start: {
            line: 10,
            column: 22
          },
          end: {
            line: 10,
            column: 25
          }
        },
        loc: {
          start: {
            line: 10,
            column: 48
          },
          end: {
            line: 52,
            column: 1
          }
        },
        line: 10
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 25,
            column: 15
          },
          end: {
            line: 25,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 25,
            column: 40
          },
          end: {
            line: 25,
            column: 53
          }
        }, {
          start: {
            line: 25,
            column: 56
          },
          end: {
            line: 25,
            column: 74
          }
        }],
        line: 25
      },
      "1": {
        loc: {
          start: {
            line: 29,
            column: 2
          },
          end: {
            line: 31,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 2
          },
          end: {
            line: 31,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "2": {
        loc: {
          start: {
            line: 36,
            column: 4
          },
          end: {
            line: 38,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 36,
            column: 4
          },
          end: {
            line: 38,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 36
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d7a267b3f30c8bcc341ca7bc14b549795ba00e23"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_20xbgfgwtk = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_20xbgfgwtk();
import { NextResponse } from 'next/server';
import { getTenantByDomain } from '../../../../lib/clients';
import { z } from 'zod'; // Add zod for input validation

// Define schema for request validation
const requestSchema =
/* istanbul ignore next */
(cov_20xbgfgwtk().s[0]++, z.object({
  domain: z.string().min(3).max(255).regex(/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/)
}));
export async function GET(request) {
  /* istanbul ignore next */
  cov_20xbgfgwtk().f[0]++;
  // Rate limiting headers
  const response =
  /* istanbul ignore next */
  (cov_20xbgfgwtk().s[1]++, new NextResponse());
  /* istanbul ignore next */
  cov_20xbgfgwtk().s[2]++;
  response.headers.set('X-RateLimit-Limit', '100');
  /* istanbul ignore next */
  cov_20xbgfgwtk().s[3]++;
  response.headers.set('X-RateLimit-Remaining', '99'); // This would be dynamic in a real implementation

  const searchParams =
  /* istanbul ignore next */
  (cov_20xbgfgwtk().s[4]++, request.nextUrl.searchParams);
  const domain =
  /* istanbul ignore next */
  (cov_20xbgfgwtk().s[5]++, searchParams.get('domain'));

  // Validate input
  /* istanbul ignore next */
  cov_20xbgfgwtk().s[6]++;
  try {
    /* istanbul ignore next */
    cov_20xbgfgwtk().s[7]++;
    requestSchema.parse({
      domain
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_20xbgfgwtk().s[8]++;
    return NextResponse.json({
      error: 'Invalid domain format',
      details: error instanceof Error ?
      /* istanbul ignore next */
      (cov_20xbgfgwtk().b[0][0]++, error.message) :
      /* istanbul ignore next */
      (cov_20xbgfgwtk().b[0][1]++, 'Validation error')
    }, {
      status: 400
    });
  }
  /* istanbul ignore next */
  cov_20xbgfgwtk().s[9]++;
  if (!domain) {
    /* istanbul ignore next */
    cov_20xbgfgwtk().b[1][0]++;
    cov_20xbgfgwtk().s[10]++;
    return NextResponse.json({
      error: 'Domain parameter is required'
    }, {
      status: 400
    });
  } else
  /* istanbul ignore next */
  {
    cov_20xbgfgwtk().b[1][1]++;
  }
  cov_20xbgfgwtk().s[11]++;
  try {
    const tenant =
    /* istanbul ignore next */
    (cov_20xbgfgwtk().s[12]++, await getTenantByDomain(domain));
    /* istanbul ignore next */
    cov_20xbgfgwtk().s[13]++;
    if (!tenant) {
      /* istanbul ignore next */
      cov_20xbgfgwtk().b[2][0]++;
      cov_20xbgfgwtk().s[14]++;
      return NextResponse.json({
        error: 'Tenant not found for this domain'
      }, {
        status: 404
      });
    } else
    /* istanbul ignore next */
    {
      cov_20xbgfgwtk().b[2][1]++;
    }

    // Don't expose internal IDs or sensitive data
    cov_20xbgfgwtk().s[15]++;
    return NextResponse.json({
      id: tenant.tenant_id,
      name: tenant.tenant_name,
      subdomain: tenant.subdomain,
      status: tenant.status
      // Don't return schema_name as it's internal implementation detail
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_20xbgfgwtk().s[16]++;
    console.error('Error in tenant domain lookup:', error);
    /* istanbul ignore next */
    cov_20xbgfgwtk().s[17]++;
    return NextResponse.json({
      error: 'Internal server error'
    }, {
      status: 500
    });
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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