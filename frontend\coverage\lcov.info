TN:
SF:app\layout.tsx
FN:28,RootLayout
FNF:1
FNH:0
FNDA:0,RootLayout
DA:9,0
DA:12,0
DA:18,0
DA:33,0
LF:4
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:app\page.tsx
FN:3,Home
FNF:1
FNH:0
FNDA:0,Home
DA:5,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:app\api\clients\route.ts
FN:28,POST
FNF:1
FNH:0
FNDA:0,POST
DA:7,0
DA:18,0
DA:30,0
DA:31,0
DA:32,0
DA:35,0
DA:36,0
DA:39,0
DA:40,0
DA:42,0
DA:48,0
DA:55,0
DA:57,0
DA:58,0
LF:14
LH:0
BRDA:31,0,0,0
BRDA:31,0,1,0
BRDA:31,1,0,0
BRDA:31,1,1,0
BRDA:44,2,0,0
BRDA:44,2,1,0
BRF:6
BRH:0
end_of_record
TN:
SF:app\api\clients\[id]\route.ts
FN:17,PATCH
FNF:1
FNH:0
FNDA:0,PATCH
DA:7,0
DA:22,0
DA:23,0
DA:24,0
DA:27,0
DA:28,0
DA:29,0
DA:32,0
DA:33,0
DA:36,0
DA:37,0
DA:39,0
DA:45,0
DA:52,0
DA:53,0
DA:56,0
DA:58,0
DA:59,0
LF:18
LH:0
BRDA:23,0,0,0
BRDA:23,0,1,0
BRDA:23,1,0,0
BRDA:23,1,1,0
BRDA:28,2,0,0
BRDA:28,2,1,0
BRDA:41,3,0,0
BRDA:41,3,1,0
BRDA:52,4,0,0
BRDA:52,4,1,0
BRF:10
BRH:0
end_of_record
TN:
SF:app\api\clients\domain\route.ts
FN:11,(anonymous_0)
FNF:1
FNH:0
FNDA:0,(anonymous_0)
DA:11,0
DA:13,0
DA:14,0
DA:15,0
DA:18,0
DA:21,0
DA:22,0
DA:23,0
DA:30,0
DA:32,0
DA:33,0
DA:34,0
DA:36,0
DA:44,0
LF:14
LH:0
BRDA:14,0,0,0
BRDA:14,0,1,0
BRDA:22,1,0,0
BRDA:22,1,1,0
BRDA:32,2,0,0
BRDA:32,2,1,0
BRDA:33,3,0,0
BRDA:33,3,1,0
BRDA:34,4,0,0
BRDA:34,4,1,0
BRDA:37,5,0,0
BRDA:37,5,1,0
BRF:12
BRH:0
end_of_record
TN:
SF:app\api\dashboard\renewals\route.ts
FN:22,(anonymous_0)
FN:72,(anonymous_1)
FNF:2
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
DA:22,0
DA:24,0
DA:25,0
DA:26,0
DA:29,0
DA:32,0
DA:34,0
DA:35,0
DA:36,0
DA:38,0
DA:45,0
DA:48,0
DA:50,0
DA:52,0
DA:54,0
DA:68,0
DA:70,0
DA:72,0
DA:82,0
DA:86,0
DA:87,0
DA:96,0
LF:22
LH:0
BRDA:25,0,0,0
BRDA:25,0,1,0
BRDA:34,1,0,0
BRDA:34,1,1,0
BRDA:35,2,0,0
BRDA:35,2,1,0
BRDA:36,3,0,0
BRDA:36,3,1,0
BRDA:39,4,0,0
BRDA:39,4,1,0
BRDA:52,5,0,0
BRDA:52,5,1,0
BRDA:70,6,0,0
BRDA:70,6,1,0
BRDA:70,7,0,0
BRDA:70,7,1,0
BRDA:77,8,0,0
BRDA:77,8,1,0
BRDA:78,9,0,0
BRDA:78,9,1,0
BRDA:86,10,0,0
BRDA:86,10,1,0
BRF:22
BRH:0
end_of_record
TN:
SF:app\api\dashboard\stats\route.ts
FN:20,(anonymous_0)
FNF:1
FNH:0
FNDA:0,(anonymous_0)
DA:20,0
DA:22,0
DA:23,0
DA:24,0
DA:27,0
DA:30,0
DA:32,0
DA:33,0
DA:34,0
DA:36,0
DA:43,0
DA:46,0
DA:48,0
DA:55,0
DA:57,0
DA:77,0
DA:78,0
DA:80,0
DA:81,0
DA:82,0
DA:84,0
DA:85,0
DA:87,0
DA:88,0
DA:90,0
DA:91,0
DA:93,0
DA:94,0
DA:95,0
DA:100,0
DA:102,0
DA:110,0
LF:32
LH:0
BRDA:23,0,0,0
BRDA:23,0,1,0
BRDA:32,1,0,0
BRDA:32,1,1,0
BRDA:33,2,0,0
BRDA:33,2,1,0
BRDA:34,3,0,0
BRDA:34,3,1,0
BRDA:37,4,0,0
BRDA:37,4,1,0
BRDA:55,5,0,0
BRDA:55,5,1,0
BRDA:80,6,0,0
BRDA:80,6,1,0
BRDA:80,7,0,0
BRDA:80,7,1,0
BRDA:80,7,2,0
BRDA:82,8,0,0
BRDA:82,8,1,0
BRDA:82,8,2,0
BRDA:82,8,3,0
BRDA:84,9,0,0
BRDA:84,9,1,0
BRDA:87,10,0,0
BRDA:87,10,1,0
BRDA:90,11,0,0
BRDA:90,11,1,0
BRDA:93,12,0,0
BRDA:93,12,1,0
BRF:29
BRH:0
end_of_record
TN:
SF:app\api\health\database\route.ts
FN:18,(anonymous_0)
FNF:1
FNH:0
FNDA:0,(anonymous_0)
DA:18,0
DA:20,0
DA:21,0
DA:22,0
DA:26,0
DA:28,0
DA:29,0
DA:36,0
LF:8
LH:0
BRDA:21,0,0,0
BRDA:21,0,1,0
BRDA:28,1,0,0
BRDA:28,1,1,0
BRDA:43,2,0,0
BRDA:43,2,1,0
BRF:6
BRH:0
end_of_record
TN:
SF:app\api\tenants\domain\route.ts
FN:10,GET
FNF:1
FNH:0
FNDA:0,GET
DA:6,0
DA:12,0
DA:13,0
DA:14,0
DA:16,0
DA:17,0
DA:20,0
DA:21,0
DA:23,0
DA:29,0
DA:30,0
DA:33,0
DA:34,0
DA:36,0
DA:37,0
DA:41,0
DA:49,0
DA:50,0
LF:18
LH:0
BRDA:25,0,0,0
BRDA:25,0,1,0
BRDA:29,1,0,0
BRDA:29,1,1,0
BRDA:36,2,0,0
BRDA:36,2,1,0
BRF:6
BRH:0
end_of_record
TN:
SF:app\api\users\route.ts
FN:47,(anonymous_0)
FN:120,(anonymous_1)
FN:133,(anonymous_2)
FN:222,OPTIONS
FNF:4
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,OPTIONS
DA:47,0
DA:49,0
DA:50,0
DA:51,0
DA:52,0
DA:54,0
DA:55,0
DA:60,0
DA:61,0
DA:62,0
DA:65,0
DA:68,0
DA:69,0
DA:71,0
DA:72,0
DA:75,0
DA:78,0
DA:81,0
DA:82,0
DA:84,0
DA:101,0
DA:107,0
DA:108,0
DA:115,0
DA:116,0
DA:117,0
DA:119,0
DA:120,0
DA:133,0
DA:135,0
DA:136,0
DA:137,0
DA:140,0
DA:143,0
DA:144,0
DA:146,0
DA:147,0
DA:151,0
DA:153,0
DA:154,0
DA:157,0
DA:160,0
DA:161,0
DA:163,0
DA:164,0
DA:172,0
DA:192,0
DA:203,0
DA:204,0
DA:212,0
DA:214,0
DA:223,0
LF:52
LH:0
BRDA:50,0,0,0
BRDA:50,0,1,0
BRDA:54,1,0,0
BRDA:54,1,1,0
BRDA:61,2,0,0
BRDA:61,2,1,0
BRDA:71,3,0,0
BRDA:71,3,1,0
BRDA:82,4,0,0
BRDA:82,4,1,0
BRDA:82,5,0,0
BRDA:82,5,1,0
BRDA:107,6,0,0
BRDA:107,6,1,0
BRDA:115,7,0,0
BRDA:115,7,1,0
BRDA:116,8,0,0
BRDA:116,8,1,0
BRDA:136,9,0,0
BRDA:136,9,1,0
BRDA:146,10,0,0
BRDA:146,10,1,0
BRDA:153,11,0,0
BRDA:153,11,1,0
BRDA:163,12,0,0
BRDA:163,12,1,0
BRDA:163,13,0,0
BRDA:163,13,1,0
BRDA:196,14,0,0
BRDA:196,14,1,0
BRDA:197,15,0,0
BRDA:197,15,1,0
BRDA:198,16,0,0
BRDA:198,16,1,0
BRDA:203,17,0,0
BRDA:203,17,1,0
BRF:36
BRH:0
end_of_record
TN:
SF:app\api\users\[id]\route.ts
FN:45,(anonymous_0)
FN:121,(anonymous_1)
FN:249,(anonymous_2)
FNF:3
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
DA:45,0
DA:50,0
DA:51,0
DA:52,0
DA:55,0
DA:58,0
DA:59,0
DA:60,0
DA:63,0
DA:67,0
DA:68,0
DA:70,0
DA:71,0
DA:79,0
DA:95,0
DA:97,0
DA:98,0
DA:105,0
DA:106,0
DA:110,0
DA:111,0
DA:113,0
DA:114,0
DA:117,0
DA:121,0
DA:126,0
DA:127,0
DA:128,0
DA:131,0
DA:134,0
DA:135,0
DA:136,0
DA:139,0
DA:142,0
DA:143,0
DA:144,0
DA:147,0
DA:150,0
DA:151,0
DA:156,0
DA:157,0
DA:164,0
DA:165,0
DA:169,0
DA:170,0
DA:171,0
DA:173,0
DA:174,0
DA:175,0
DA:178,0
DA:179,0
DA:180,0
DA:183,0
DA:184,0
DA:185,0
DA:188,0
DA:189,0
DA:190,0
DA:193,0
DA:194,0
DA:202,0
DA:203,0
DA:206,0
DA:208,0
DA:224,0
DA:226,0
DA:227,0
DA:234,0
DA:235,0
DA:243,0
DA:245,0
DA:249,0
DA:254,0
DA:255,0
DA:256,0
DA:259,0
DA:262,0
DA:263,0
DA:264,0
DA:267,0
DA:270,0
DA:271,0
DA:279,0
DA:289,0
DA:294,0
DA:295,0
DA:302,0
DA:303,0
DA:307,0
DA:309,0
LF:90
LH:0
BRDA:51,0,0,0
BRDA:51,0,1,0
BRDA:59,1,0,0
BRDA:59,1,1,0
BRDA:67,2,0,0
BRDA:67,2,1,0
BRDA:70,3,0,0
BRDA:70,3,1,0
BRDA:70,4,0,0
BRDA:70,4,1,0
BRDA:97,5,0,0
BRDA:97,5,1,0
BRDA:105,6,0,0
BRDA:105,6,1,0
BRDA:111,7,0,0
BRDA:111,7,1,0
BRDA:111,8,0,0
BRDA:111,8,1,0
BRDA:127,9,0,0
BRDA:127,9,1,0
BRDA:135,10,0,0
BRDA:135,10,1,0
BRDA:143,11,0,0
BRDA:143,11,1,0
BRDA:156,12,0,0
BRDA:156,12,1,0
BRDA:164,13,0,0
BRDA:164,13,1,0
BRDA:173,14,0,0
BRDA:173,14,1,0
BRDA:178,15,0,0
BRDA:178,15,1,0
BRDA:183,16,0,0
BRDA:183,16,1,0
BRDA:188,17,0,0
BRDA:188,17,1,0
BRDA:193,18,0,0
BRDA:193,18,1,0
BRDA:226,19,0,0
BRDA:226,19,1,0
BRDA:234,20,0,0
BRDA:234,20,1,0
BRDA:255,21,0,0
BRDA:255,21,1,0
BRDA:263,22,0,0
BRDA:263,22,1,0
BRDA:270,23,0,0
BRDA:270,23,1,0
BRDA:294,24,0,0
BRDA:294,24,1,0
BRDA:302,25,0,0
BRDA:302,25,1,0
BRF:52
BRH:0
end_of_record
TN:
SF:app\callback\page.tsx
FN:9,CallbackPage
FN:15,(anonymous_1)
FN:18,handleCallback
FN:85,(anonymous_3)
FN:94,(anonymous_4)
FNF:5
FNH:0
FNDA:0,CallbackPage
FNDA:0,(anonymous_1)
FNDA:0,handleCallback
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
DA:10,0
DA:11,0
DA:12,0
DA:13,0
DA:15,0
DA:16,0
DA:19,0
DA:20,0
DA:23,0
DA:26,0
DA:27,0
DA:28,0
DA:31,0
DA:33,0
DA:35,0
DA:36,0
DA:37,0
DA:38,0
DA:39,0
DA:40,0
DA:41,0
DA:45,0
DA:46,0
DA:49,0
DA:50,0
DA:52,0
DA:53,0
DA:55,0
DA:58,0
DA:61,0
DA:63,0
DA:66,0
DA:68,0
DA:69,0
DA:71,0
DA:72,0
DA:73,0
DA:74,0
DA:76,0
DA:77,0
DA:79,0
DA:82,0
DA:83,0
DA:84,0
DA:85,0
DA:86,0
DA:92,0
DA:94,0
DA:95,0
DA:99,0
LF:50
LH:0
BRDA:27,0,0,0
BRDA:27,0,1,0
BRDA:36,1,0,0
BRDA:36,1,1,0
BRDA:50,2,0,0
BRDA:50,2,1,0
BRDA:52,3,0,0
BRDA:52,3,1,0
BRDA:68,4,0,0
BRDA:68,4,1,0
BRDA:68,5,0,0
BRDA:68,5,1,0
BRDA:72,6,0,0
BRDA:72,6,1,0
BRDA:83,7,0,0
BRDA:83,7,1,0
BRDA:101,8,0,0
BRDA:101,8,1,0
BRF:18
BRH:0
end_of_record
TN:
SF:app\dashboard\layout.tsx
FN:8,DashboardLayout
FN:17,(anonymous_1)
FNF:2
FNH:0
FNDA:0,DashboardLayout
FNDA:0,(anonymous_1)
DA:13,0
DA:14,0
DA:15,0
DA:17,0
DA:18,0
DA:26,0
DA:27,0
DA:29,0
DA:30,0
DA:32,0
DA:36,0
DA:37,0
DA:38,0
DA:39,0
DA:40,0
DA:41,0
DA:44,0
DA:45,0
DA:46,0
DA:52,0
DA:53,0
DA:62,0
DA:63,0
DA:71,0
LF:24
LH:0
BRDA:29,0,0,0
BRDA:29,0,1,0
BRDA:36,1,0,0
BRDA:36,1,1,0
BRDA:36,2,0,0
BRDA:36,2,1,0
BRDA:36,2,2,0
BRDA:40,3,0,0
BRDA:40,3,1,0
BRDA:40,4,0,0
BRDA:40,4,1,0
BRDA:44,5,0,0
BRDA:44,5,1,0
BRDA:52,6,0,0
BRDA:52,6,1,0
BRDA:62,7,0,0
BRDA:62,7,1,0
BRF:17
BRH:0
end_of_record
TN:
SF:app\dashboard\page.tsx
FN:37,DashboardPage
FN:50,(anonymous_1)
FN:56,(anonymous_2)
FN:61,(anonymous_3)
FN:66,(anonymous_4)
FN:71,(anonymous_5)
FN:77,(anonymous_6)
FN:125,(anonymous_7)
FN:136,(anonymous_8)
FNF:9
FNH:0
FNDA:0,DashboardPage
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
FNDA:0,(anonymous_6)
FNDA:0,(anonymous_7)
FNDA:0,(anonymous_8)
DA:38,0
DA:39,0
DA:42,0
DA:44,0
DA:47,0
DA:50,0
DA:51,0
DA:53,0
DA:56,0
DA:58,0
DA:61,0
DA:63,0
DA:66,0
DA:68,0
DA:71,0
DA:73,0
DA:77,0
DA:105,0
DA:106,0
DA:116,0
DA:117,0
DA:125,0
DA:134,0
DA:137,0
LF:24
LH:0
BRDA:105,0,0,0
BRDA:105,0,1,0
BRDA:105,1,0,0
BRDA:105,1,1,0
BRDA:116,2,0,0
BRDA:116,2,1,0
BRDA:116,3,0,0
BRDA:116,3,1,0
BRDA:122,4,0,0
BRDA:122,4,1,0
BRF:10
BRH:0
end_of_record
TN:
SF:app\login\page.tsx
FN:8,LoginPage
FN:11,(anonymous_1)
FN:12,checkAuthAndRedirect
FNF:3
FNH:0
FNDA:0,LoginPage
FNDA:0,(anonymous_1)
FNDA:0,checkAuthAndRedirect
DA:9,0
DA:11,0
DA:13,0
DA:15,0
DA:17,0
DA:19,0
DA:20,0
DA:23,0
DA:24,0
DA:25,0
DA:26,0
DA:27,0
DA:28,0
DA:31,0
DA:35,0
DA:36,0
DA:39,0
DA:41,0
DA:45,0
DA:49,0
LF:20
LH:0
BRDA:17,0,0,0
BRDA:17,0,1,0
BRDA:25,1,0,0
BRDA:25,1,1,0
BRF:4
BRH:0
end_of_record
TN:
SF:app\signout\page.tsx
FN:7,SignOutPage
FN:10,(anonymous_1)
FN:11,handleSignOut
FNF:3
FNH:0
FNDA:0,SignOutPage
FNDA:0,(anonymous_1)
FNDA:0,handleSignOut
DA:8,0
DA:10,0
DA:12,0
DA:13,0
DA:14,0
DA:16,0
DA:18,0
DA:22,0
DA:25,0
LF:9
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:components\EnvDebugger.tsx
FN:5,EnvDebugger
FN:16,(anonymous_1)
FN:19,(anonymous_2)
FNF:3
FNH:0
FNDA:0,EnvDebugger
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
DA:6,0
DA:9,0
DA:16,0
DA:18,0
DA:19,0
DA:20,0
DA:21,0
DA:24,0
DA:27,0
LF:9
LH:0
BRDA:20,0,0,0
BRDA:20,0,1,0
BRF:2
BRH:0
end_of_record
TN:
SF:components\providers.tsx
FN:6,Providers
FNF:1
FNH:0
FNDA:0,Providers
DA:7,0
LF:1
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:components\common\ErrorBoundary.tsx
FN:32,DefaultErrorFallback
FN:51,(anonymous_1)
FN:75,(anonymous_2)
FN:80,(anonymous_3)
FN:85,(anonymous_4)
FN:108,(anonymous_5)
FN:115,(anonymous_6)
FN:123,(anonymous_7)
FN:129,(anonymous_8)
FN:133,(anonymous_9)
FN:161,useErrorHandler
FN:164,(anonymous_11)
FN:168,(anonymous_12)
FN:181,withErrorBoundary
FN:185,(anonymous_14)
FNF:15
FNH:0
FNDA:0,DefaultErrorFallback
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
FNDA:0,(anonymous_6)
FNDA:0,(anonymous_7)
FNDA:0,(anonymous_8)
FNDA:0,(anonymous_9)
FNDA:0,useErrorHandler
FNDA:0,(anonymous_11)
FNDA:0,(anonymous_12)
FNDA:0,withErrorBoundary
FNDA:0,(anonymous_14)
DA:33,0
DA:51,0
DA:73,0
DA:76,0
DA:77,0
DA:82,0
DA:87,0
DA:88,0
DA:92,0
DA:95,0
DA:98,0
DA:100,0
DA:109,0
DA:110,0
DA:113,0
DA:114,0
DA:115,0
DA:117,0
DA:118,0
DA:124,0
DA:125,0
DA:129,0
DA:130,0
DA:134,0
DA:135,0
DA:137,0
DA:139,0
DA:140,0
DA:147,0
DA:156,0
DA:162,0
DA:164,0
DA:165,0
DA:168,0
DA:169,0
DA:173,0
DA:174,0
DA:177,0
DA:185,0
DA:186,0
DA:191,0
DA:193,0
LF:42
LH:0
BRDA:32,0,0,0
BRDA:39,1,0,0
BRDA:39,1,1,0
BRDA:57,2,0,0
BRDA:57,2,1,0
BRDA:57,2,2,0
BRDA:87,3,0,0
BRDA:87,3,1,0
BRDA:98,4,0,0
BRDA:98,4,1,0
BRDA:113,5,0,0
BRDA:113,5,1,0
BRDA:113,6,0,0
BRDA:113,6,1,0
BRDA:113,6,2,0
BRDA:114,7,0,0
BRDA:114,7,1,0
BRDA:117,8,0,0
BRDA:117,8,1,0
BRDA:124,9,0,0
BRDA:124,9,1,0
BRDA:135,10,0,0
BRDA:137,11,0,0
BRDA:137,11,1,0
BRDA:139,12,0,0
BRDA:139,12,1,0
BRDA:173,13,0,0
BRDA:173,13,1,0
BRDA:191,14,0,0
BRDA:191,14,1,0
BRF:30
BRH:0
end_of_record
TN:
SF:components\common\LazyLoad.tsx
FN:42,LazyComponent
FN:57,(anonymous_1)
FN:73,LazyImage
FN:90,(anonymous_3)
FN:94,(anonymous_4)
FN:100,(anonymous_5)
FN:134,VirtualList
FN:155,(anonymous_7)
FN:168,(anonymous_8)
FN:183,createLazyRoute
FN:189,LazyRoute
FN:199,ProgressiveEnhancement
FN:212,(anonymous_12)
FN:214,(anonymous_13)
FN:218,(anonymous_14)
FN:232,LazySection
FN:252,(anonymous_16)
FN:274,LazyTabs
FN:291,(anonymous_18)
FN:292,(anonymous_19)
FN:298,(anonymous_20)
FN:301,(anonymous_21)
FN:314,(anonymous_22)
FNF:23
FNH:0
FNDA:0,LazyComponent
FNDA:0,(anonymous_1)
FNDA:0,LazyImage
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
FNDA:0,VirtualList
FNDA:0,(anonymous_7)
FNDA:0,(anonymous_8)
FNDA:0,createLazyRoute
FNDA:0,LazyRoute
FNDA:0,ProgressiveEnhancement
FNDA:0,(anonymous_12)
FNDA:0,(anonymous_13)
FNDA:0,(anonymous_14)
FNDA:0,LazySection
FNDA:0,(anonymous_16)
FNDA:0,LazyTabs
FNDA:0,(anonymous_18)
FNDA:0,(anonymous_19)
FNDA:0,(anonymous_20)
FNDA:0,(anonymous_21)
FNDA:0,(anonymous_22)
DA:42,0
DA:50,0
DA:55,0
DA:57,0
DA:58,0
DA:59,0
DA:63,0
DA:65,0
DA:73,0
DA:81,0
DA:86,0
DA:87,0
DA:88,0
DA:90,0
DA:91,0
DA:92,0
DA:94,0
DA:95,0
DA:96,0
DA:97,0
DA:100,0
DA:101,0
DA:102,0
DA:105,0
DA:109,0
DA:142,0
DA:143,0
DA:145,0
DA:146,0
DA:151,0
DA:152,0
DA:153,0
DA:155,0
DA:156,0
DA:159,0
DA:169,0
DA:187,0
DA:189,0
DA:190,0
DA:199,0
DA:210,0
DA:212,0
DA:213,0
DA:214,0
DA:215,0
DA:218,0
DA:219,0
DA:220,0
DA:224,0
DA:225,0
DA:228,0
DA:232,0
DA:245,0
DA:250,0
DA:252,0
DA:253,0
DA:254,0
DA:258,0
DA:274,0
DA:289,0
DA:291,0
DA:292,0
DA:295,0
DA:299,0
DA:301,0
DA:315,0
LF:66
LH:0
BRDA:44,0,0,0
BRDA:45,1,0,0
BRDA:46,2,0,0
BRDA:47,3,0,0
BRDA:48,4,0,0
BRDA:58,5,0,0
BRDA:58,5,1,0
BRDA:58,6,0,0
BRDA:58,6,1,0
BRDA:63,7,0,0
BRDA:63,7,1,0
BRDA:67,8,0,0
BRDA:67,8,1,0
BRDA:76,9,0,0
BRDA:77,10,0,0
BRDA:91,11,0,0
BRDA:91,11,1,0
BRDA:91,12,0,0
BRDA:91,12,1,0
BRDA:91,12,2,0
BRDA:115,13,0,0
BRDA:115,13,1,0
BRDA:119,14,0,0
BRDA:119,14,1,0
BRDA:119,14,2,0
BRDA:124,15,0,0
BRDA:124,15,1,0
BRDA:139,16,0,0
BRDA:140,17,0,0
BRDA:191,18,0,0
BRDA:191,18,1,0
BRDA:202,19,0,0
BRDA:203,20,0,0
BRDA:210,21,0,0
BRDA:210,21,1,0
BRDA:213,22,0,0
BRDA:213,22,1,0
BRDA:213,23,0,0
BRDA:213,23,1,0
BRDA:219,24,0,0
BRDA:219,24,1,0
BRDA:224,25,0,0
BRDA:224,25,1,0
BRDA:225,26,0,0
BRDA:225,26,1,0
BRDA:235,27,0,0
BRDA:236,28,0,0
BRDA:237,29,0,0
BRDA:253,30,0,0
BRDA:253,30,1,0
BRDA:260,31,0,0
BRDA:260,31,1,0
BRDA:263,32,0,0
BRDA:263,32,1,0
BRDA:278,33,0,0
BRDA:303,34,0,0
BRDA:303,34,1,0
BRDA:317,35,0,0
BRDA:317,35,1,0
BRDA:319,36,0,0
BRDA:319,36,1,0
BRF:61
BRH:0
end_of_record
TN:
SF:components\common\LoadingStates.tsx
FN:37,LoadingSpinner
FN:92,LoadingSkeleton
FN:116,(anonymous_2)
FN:133,LoadingPage
FN:162,LoadingCard
FN:186,LoadingButton
FN:221,LoadingOverlay
FN:250,LoadingList
FN:263,(anonymous_8)
FN:282,LoadingTable
FN:301,(anonymous_10)
FN:310,(anonymous_11)
FN:312,(anonymous_12)
FNF:13
FNH:0
FNDA:0,LoadingSpinner
FNDA:0,LoadingSkeleton
FNDA:0,(anonymous_2)
FNDA:0,LoadingPage
FNDA:0,LoadingCard
FNDA:0,LoadingButton
FNDA:0,LoadingOverlay
FNDA:0,LoadingList
FNDA:0,(anonymous_8)
FNDA:0,LoadingTable
FNDA:0,(anonymous_10)
FNDA:0,(anonymous_11)
FNDA:0,(anonymous_12)
DA:44,0
DA:51,0
DA:57,0
DA:100,0
DA:102,0
DA:103,0
DA:114,0
DA:117,0
DA:140,0
DA:168,0
DA:201,0
DA:232,0
DA:234,0
DA:261,0
DA:264,0
DA:295,0
DA:302,0
DA:311,0
DA:313,0
LF:19
LH:0
BRDA:38,0,0,0
BRDA:39,1,0,0
BRDA:41,2,0,0
BRDA:62,3,0,0
BRDA:62,3,1,0
BRDA:84,4,0,0
BRDA:84,4,1,0
BRDA:93,5,0,0
BRDA:94,6,0,0
BRDA:95,7,0,0
BRDA:96,8,0,0
BRDA:97,9,0,0
BRDA:100,10,0,0
BRDA:100,10,1,0
BRDA:102,11,0,0
BRDA:102,11,1,0
BRDA:122,12,0,0
BRDA:122,12,1,0
BRDA:134,13,0,0
BRDA:136,14,0,0
BRDA:137,15,0,0
BRDA:150,16,0,0
BRDA:150,16,1,0
BRDA:164,17,0,0
BRDA:165,18,0,0
BRDA:173,19,0,0
BRDA:173,19,1,0
BRDA:188,20,0,0
BRDA:189,21,0,0
BRDA:190,22,0,0
BRDA:203,23,0,0
BRDA:203,23,1,0
BRDA:204,24,0,0
BRDA:204,24,1,0
BRDA:205,25,0,0
BRDA:205,25,1,0
BRDA:208,26,0,0
BRDA:208,26,1,0
BRDA:222,27,0,0
BRDA:223,28,0,0
BRDA:224,29,0,0
BRDA:232,30,0,0
BRDA:232,30,1,0
BRDA:251,31,0,0
BRDA:252,32,0,0
BRDA:253,33,0,0
BRDA:265,34,0,0
BRDA:265,34,1,0
BRDA:283,35,0,0
BRDA:284,36,0,0
BRDA:285,37,0,0
BRDA:286,38,0,0
BRDA:298,39,0,0
BRDA:298,39,1,0
BRDA:315,40,0,0
BRDA:315,40,1,0
BRF:56
BRH:0
end_of_record
TN:
SF:components\common\PerformanceMonitor.tsx
FN:35,PerformanceMonitor
FN:54,(anonymous_1)
FN:61,(anonymous_2)
FN:93,(anonymous_3)
FN:105,(anonymous_4)
FN:113,(anonymous_5)
FN:127,(anonymous_6)
FN:139,(anonymous_7)
FN:146,(anonymous_8)
FN:205,(anonymous_9)
FNF:10
FNH:0
FNDA:0,PerformanceMonitor
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
FNDA:0,(anonymous_6)
FNDA:0,(anonymous_7)
FNDA:0,(anonymous_8)
FNDA:0,(anonymous_9)
DA:35,0
DA:40,0
DA:51,0
DA:52,0
DA:54,0
DA:55,0
DA:57,0
DA:58,0
DA:59,0
DA:61,0
DA:62,0
DA:63,0
DA:65,0
DA:66,0
DA:70,0
DA:71,0
DA:72,0
DA:79,0
DA:87,0
DA:91,0
DA:93,0
DA:96,0
DA:98,0
DA:105,0
DA:106,0
DA:107,0
DA:108,0
DA:109,0
DA:110,0
DA:113,0
DA:114,0
DA:115,0
DA:116,0
DA:119,0
DA:127,0
DA:139,0
DA:146,0
DA:206,0
LF:38
LH:0
BRDA:36,0,0,0
BRDA:37,1,0,0
BRDA:38,2,0,0
BRDA:55,3,0,0
BRDA:55,3,1,0
BRDA:70,4,0,0
BRDA:70,4,1,0
BRDA:96,5,0,0
BRDA:96,5,1,0
BRDA:106,6,0,0
BRDA:106,6,1,0
BRDA:114,7,0,0
BRDA:114,7,1,0
BRDA:115,8,0,0
BRDA:115,8,1,0
BRDA:122,9,0,0
BRDA:122,9,1,0
BRDA:125,10,0,0
BRDA:125,10,1,0
BRDA:179,11,0,0
BRDA:179,11,1,0
BRDA:229,12,0,0
BRDA:229,12,1,0
BRDA:232,13,0,0
BRDA:232,13,1,0
BRDA:232,13,2,0
BRDA:235,14,0,0
BRDA:235,14,1,0
BRF:28
BRH:0
end_of_record
TN:
SF:components\dashboard\DashboardHeader.tsx
FN:20,DashboardHeader
FN:30,(anonymous_1)
FN:36,(anonymous_2)
FN:41,(anonymous_3)
FNF:4
FNH:0
FNDA:0,DashboardHeader
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
DA:28,0
DA:30,0
DA:31,0
DA:32,0
DA:33,0
DA:36,0
DA:37,0
DA:38,0
DA:41,0
DA:42,0
DA:45,0
LF:11
LH:0
BRDA:21,0,0,0
BRDA:24,1,0,0
BRDA:25,2,0,0
BRF:3
BRH:0
end_of_record
TN:
SF:components\dashboard\DashboardStats.tsx
FN:27,StatCard
FN:49,DashboardStats
FN:59,(anonymous_2)
FN:93,(anonymous_3)
FNF:4
FNH:0
FNDA:0,StatCard
FNDA:0,DashboardStats
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
DA:27,0
DA:28,0
DA:29,0
DA:40,0
DA:49,0
DA:56,0
DA:59,0
DA:86,0
DA:94,0
LF:9
LH:0
BRDA:28,0,0,0
BRDA:28,0,1,0
BRDA:51,1,0,0
BRDA:52,2,0,0
BRF:4
BRH:0
end_of_record
TN:
SF:components\dashboard\RecentRenewals.tsx
FN:24,RenewalItem
FN:25,(anonymous_1)
FN:29,(anonymous_2)
FN:62,LoadingSkeleton
FN:65,(anonymous_4)
FN:82,EmptyState
FN:94,RecentRenewals
FN:122,(anonymous_7)
FNF:8
FNH:0
FNDA:0,RenewalItem
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,LoadingSkeleton
FNDA:0,(anonymous_4)
FNDA:0,EmptyState
FNDA:0,RecentRenewals
FNDA:0,(anonymous_7)
DA:25,0
DA:26,0
DA:29,0
DA:30,0
DA:31,0
DA:32,0
DA:36,0
DA:63,0
DA:66,0
DA:83,0
DA:102,0
DA:104,0
DA:123,0
LF:13
LH:0
BRDA:30,0,0,0
BRDA:30,0,1,0
BRDA:30,1,0,0
BRDA:30,1,1,0
BRDA:51,2,0,0
BRDA:51,2,1,0
BRDA:96,3,0,0
BRDA:98,4,0,0
BRDA:99,5,0,0
BRDA:111,6,0,0
BRDA:111,6,1,0
BRDA:118,7,0,0
BRDA:118,7,1,0
BRDA:120,8,0,0
BRDA:120,8,1,0
BRF:15
BRH:0
end_of_record
TN:
SF:components\dashboard\ScanResults.tsx
FN:34,ScanResultItem
FN:35,(anonymous_1)
FN:39,(anonymous_2)
FN:54,(anonymous_3)
FN:101,LoadingSkeleton
FN:104,(anonymous_5)
FN:124,EmptyState
FN:141,ScanHeader
FN:163,ScanResults
FN:185,(anonymous_9)
FNF:10
FNH:0
FNDA:0,ScanResultItem
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,LoadingSkeleton
FNDA:0,(anonymous_5)
FNDA:0,EmptyState
FNDA:0,ScanHeader
FNDA:0,ScanResults
FNDA:0,(anonymous_9)
DA:35,0
DA:36,0
DA:39,0
DA:40,0
DA:42,0
DA:44,0
DA:46,0
DA:48,0
DA:50,0
DA:54,0
DA:55,0
DA:57,0
DA:59,0
DA:61,0
DA:63,0
DA:65,0
DA:69,0
DA:102,0
DA:105,0
DA:125,0
DA:142,0
DA:172,0
DA:186,0
LF:23
LH:0
BRDA:40,0,0,0
BRDA:40,0,1,0
BRDA:40,0,2,0
BRDA:40,0,3,0
BRDA:40,0,4,0
BRDA:55,1,0,0
BRDA:55,1,1,0
BRDA:55,1,2,0
BRDA:55,1,3,0
BRDA:55,1,4,0
BRDA:84,2,0,0
BRDA:84,2,1,0
BRDA:146,3,0,0
BRDA:146,3,1,0
BRDA:165,4,0,0
BRDA:169,5,0,0
BRDA:181,6,0,0
BRDA:181,6,1,0
BRDA:183,7,0,0
BRDA:183,7,1,0
BRF:20
BRH:0
end_of_record
TN:
SF:components\dashboard\UpcomingRenewals.tsx
FN:25,UpcomingRenewalItem
FN:26,(anonymous_1)
FN:30,(anonymous_2)
FN:37,(anonymous_3)
FN:80,LoadingSkeleton
FN:83,(anonymous_5)
FN:103,EmptyState
FN:116,getDaysUntilDue
FN:124,getUpcomingRenewals
FN:126,(anonymous_9)
FN:131,(anonymous_10)
FN:138,UpcomingRenewals
FN:167,(anonymous_12)
FNF:13
FNH:0
FNDA:0,UpcomingRenewalItem
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,LoadingSkeleton
FNDA:0,(anonymous_5)
FNDA:0,EmptyState
FNDA:0,getDaysUntilDue
FNDA:0,getUpcomingRenewals
FNDA:0,(anonymous_9)
FNDA:0,(anonymous_10)
FNDA:0,UpcomingRenewals
FNDA:0,(anonymous_12)
DA:26,0
DA:27,0
DA:30,0
DA:31,0
DA:32,0
DA:33,0
DA:34,0
DA:37,0
DA:38,0
DA:39,0
DA:40,0
DA:41,0
DA:44,0
DA:81,0
DA:84,0
DA:104,0
DA:117,0
DA:118,0
DA:119,0
DA:120,0
DA:125,0
DA:126,0
DA:127,0
DA:128,0
DA:129,0
DA:130,0
DA:133,0
DA:134,0
DA:146,0
DA:148,0
DA:168,0
LF:31
LH:0
BRDA:31,0,0,0
BRDA:31,0,1,0
BRDA:32,1,0,0
BRDA:32,1,1,0
BRDA:33,2,0,0
BRDA:33,2,1,0
BRDA:38,3,0,0
BRDA:38,3,1,0
BRDA:39,4,0,0
BRDA:39,4,1,0
BRDA:40,5,0,0
BRDA:40,5,1,0
BRDA:59,6,0,0
BRDA:59,6,1,0
BRDA:70,7,0,0
BRDA:70,7,1,0
BRDA:127,8,0,0
BRDA:127,8,1,0
BRDA:130,9,0,0
BRDA:130,9,1,0
BRDA:133,10,0,0
BRDA:133,10,1,0
BRDA:133,11,0,0
BRDA:133,11,1,0
BRDA:140,12,0,0
BRDA:142,13,0,0
BRDA:143,14,0,0
BRDA:156,15,0,0
BRDA:156,15,1,0
BRDA:158,16,0,0
BRDA:158,16,1,0
BRDA:163,17,0,0
BRDA:163,17,1,0
BRDA:165,18,0,0
BRDA:165,18,1,0
BRDA:172,19,0,0
BRDA:172,19,1,0
BRF:37
BRH:0
end_of_record
TN:
SF:components\layout\MainLayout.tsx
FN:9,MainLayout
FN:17,(anonymous_1)
FNF:2
FNH:0
FNDA:0,MainLayout
FNDA:0,(anonymous_1)
DA:14,0
DA:15,0
DA:17,0
DA:18,0
DA:19,0
DA:24,0
DA:25,0
DA:29,0
DA:30,0
DA:33,0
LF:10
LH:0
BRDA:18,0,0,0
BRDA:18,0,1,0
BRDA:18,1,0,0
BRDA:18,1,1,0
BRDA:24,2,0,0
BRDA:24,2,1,0
BRDA:29,3,0,0
BRDA:29,3,1,0
BRF:8
BRH:0
end_of_record
TN:
SF:components\layout\Sidebar.tsx
FN:5,Sidebar
FN:10,(anonymous_1)
FNF:2
FNH:0
FNDA:0,Sidebar
FNDA:0,(anonymous_1)
DA:6,0
DA:7,0
DA:10,0
DA:11,0
DA:13,0
DA:17,0
DA:24,0
DA:28,0
LF:8
LH:0
BRDA:11,0,0,0
BRDA:11,0,1,0
BRDA:11,1,0,0
BRDA:11,1,1,0
BRDA:13,2,0,0
BRDA:13,2,1,0
BRDA:13,2,2,0
BRDA:17,3,0,0
BRDA:17,3,1,0
BRDA:17,4,0,0
BRDA:17,4,1,0
BRDA:19,5,0,0
BRDA:19,5,1,0
BRDA:21,6,0,0
BRDA:21,6,1,0
BRDA:24,7,0,0
BRDA:24,7,1,0
BRDA:24,8,0,0
BRDA:24,8,1,0
BRDA:26,9,0,0
BRDA:26,9,1,0
BRDA:38,10,0,0
BRDA:38,10,1,0
BRDA:48,11,0,0
BRDA:48,11,1,0
BRDA:55,12,0,0
BRDA:55,12,1,0
BRDA:62,13,0,0
BRDA:62,13,1,0
BRDA:70,14,0,0
BRDA:70,14,1,0
BRDA:78,15,0,0
BRDA:78,15,1,0
BRDA:85,16,0,0
BRDA:85,16,1,0
BRDA:92,17,0,0
BRDA:92,17,1,0
BRDA:99,18,0,0
BRDA:99,18,1,0
BRDA:106,19,0,0
BRDA:106,19,1,0
BRDA:114,20,0,0
BRDA:114,20,1,0
BRDA:121,21,0,0
BRDA:121,21,1,0
BRF:45
BRH:0
end_of_record
TN:
SF:contexts\AppContext.tsx
FN:68,AppProvider
FN:80,(anonymous_1)
FN:109,(anonymous_2)
FN:121,(anonymous_3)
FN:146,(anonymous_4)
FN:149,(anonymous_5)
FN:161,(anonymous_6)
FN:163,(anonymous_7)
FN:188,(anonymous_8)
FN:216,(anonymous_9)
FN:222,(anonymous_10)
FN:251,(anonymous_11)
FN:256,(anonymous_12)
FN:262,(anonymous_13)
FN:275,(anonymous_14)
FN:280,(anonymous_15)
FN:320,(anonymous_16)
FN:329,(anonymous_17)
FN:334,(anonymous_18)
FN:339,(anonymous_19)
FNF:20
FNH:0
FNDA:0,AppProvider
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
FNDA:0,(anonymous_6)
FNDA:0,(anonymous_7)
FNDA:0,(anonymous_8)
FNDA:0,(anonymous_9)
FNDA:0,(anonymous_10)
FNDA:0,(anonymous_11)
FNDA:0,(anonymous_12)
FNDA:0,(anonymous_13)
FNDA:0,(anonymous_14)
FNDA:0,(anonymous_15)
FNDA:0,(anonymous_16)
FNDA:0,(anonymous_17)
FNDA:0,(anonymous_18)
FNDA:0,(anonymous_19)
DA:62,0
DA:70,0
DA:71,0
DA:72,0
DA:75,0
DA:76,0
DA:77,0
DA:80,0
DA:81,0
DA:82,0
DA:83,0
DA:85,0
DA:103,0
DA:104,0
DA:109,0
DA:110,0
DA:111,0
DA:112,0
DA:114,0
DA:115,0
DA:117,0
DA:119,0
DA:120,0
DA:121,0
DA:122,0
DA:124,0
DA:126,0
DA:129,0
DA:130,0
DA:131,0
DA:133,0
DA:136,0
DA:137,0
DA:138,0
DA:139,0
DA:141,0
DA:146,0
DA:147,0
DA:149,0
DA:150,0
DA:152,0
DA:153,0
DA:156,0
DA:157,0
DA:159,0
DA:160,0
DA:161,0
DA:163,0
DA:167,0
DA:168,0
DA:170,0
DA:177,0
DA:178,0
DA:180,0
DA:181,0
DA:183,0
DA:184,0
DA:185,0
DA:188,0
DA:189,0
DA:193,0
DA:194,0
DA:195,0
DA:196,0
DA:197,0
DA:201,0
DA:202,0
DA:203,0
DA:204,0
DA:205,0
DA:208,0
DA:209,0
DA:214,0
DA:216,0
DA:217,0
DA:222,0
DA:223,0
DA:224,0
DA:227,0
DA:230,0
DA:233,0
DA:234,0
DA:235,0
DA:237,0
DA:239,0
DA:242,0
DA:243,0
DA:244,0
DA:246,0
DA:251,0
DA:252,0
DA:256,0
DA:257,0
DA:259,0
DA:261,0
DA:262,0
DA:265,0
DA:267,0
DA:269,0
DA:270,0
DA:275,0
DA:276,0
DA:280,0
DA:281,0
DA:283,0
DA:284,0
DA:287,0
DA:288,0
DA:291,0
DA:292,0
DA:295,0
DA:298,0
DA:312,0
DA:320,0
DA:321,0
DA:322,0
DA:323,0
DA:325,0
DA:329,0
DA:330,0
DA:331,0
DA:334,0
DA:335,0
DA:336,0
DA:339,0
DA:340,0
DA:341,0
LF:127
LH:0
BRDA:87,0,0,0
BRDA:87,0,1,0
BRDA:91,1,0,0
BRDA:91,1,1,0
BRDA:109,2,0,0
BRDA:117,3,0,0
BRDA:117,3,1,0
BRDA:119,4,0,0
BRDA:119,4,1,0
BRDA:119,5,0,0
BRDA:119,5,1,0
BRDA:126,6,0,0
BRDA:126,6,1,0
BRDA:129,7,0,0
BRDA:129,7,1,0
BRDA:129,8,0,0
BRDA:129,8,1,0
BRDA:136,9,0,0
BRDA:136,9,1,0
BRDA:150,10,0,0
BRDA:150,10,1,0
BRDA:159,11,0,0
BRDA:159,11,1,0
BRDA:177,12,0,0
BRDA:177,12,1,0
BRDA:183,13,0,0
BRDA:183,13,1,0
BRDA:183,14,0,0
BRDA:183,14,1,0
BRDA:194,15,0,0
BRDA:194,15,1,0
BRDA:202,16,0,0
BRDA:202,16,1,0
BRDA:208,17,0,0
BRDA:208,17,1,0
BRDA:257,18,0,0
BRDA:257,18,1,0
BRDA:262,19,0,0
BRDA:262,19,1,0
BRDA:276,20,0,0
BRDA:276,20,1,0
BRDA:281,21,0,0
BRDA:281,21,1,0
BRDA:283,22,0,0
BRDA:283,22,1,0
BRDA:283,23,0,0
BRDA:283,23,1,0
BRDA:287,24,0,0
BRDA:287,24,1,0
BRDA:291,25,0,0
BRDA:291,25,1,0
BRDA:322,26,0,0
BRDA:322,26,1,0
BRF:53
BRH:0
end_of_record
TN:
SF:contexts\AuthContext.tsx
FN:23,(anonymous_0)
FN:24,(anonymous_1)
FN:27,AuthProvider
FN:33,(anonymous_3)
FN:52,(anonymous_4)
FN:55,(anonymous_5)
FN:68,(anonymous_6)
FN:71,(anonymous_7)
FN:137,(anonymous_8)
FN:146,(anonymous_9)
FN:153,(anonymous_10)
FN:158,(anonymous_11)
FN:204,(anonymous_12)
FNF:13
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
FNDA:0,AuthProvider
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
FNDA:0,(anonymous_6)
FNDA:0,(anonymous_7)
FNDA:0,(anonymous_8)
FNDA:0,(anonymous_9)
FNDA:0,(anonymous_10)
FNDA:0,(anonymous_11)
FNDA:0,(anonymous_12)
DA:19,0
DA:23,0
DA:24,0
DA:28,0
DA:29,0
DA:30,0
DA:33,0
DA:34,0
DA:35,0
DA:36,0
DA:38,0
DA:47,0
DA:48,0
DA:52,0
DA:53,0
DA:55,0
DA:56,0
DA:58,0
DA:59,0
DA:62,0
DA:63,0
DA:65,0
DA:66,0
DA:68,0
DA:71,0
DA:75,0
DA:76,0
DA:78,0
DA:86,0
DA:87,0
DA:89,0
DA:90,0
DA:91,0
DA:93,0
DA:94,0
DA:95,0
DA:96,0
DA:97,0
DA:98,0
DA:99,0
DA:100,0
DA:103,0
DA:104,0
DA:105,0
DA:106,0
DA:110,0
DA:111,0
DA:112,0
DA:113,0
DA:117,0
DA:118,0
DA:119,0
DA:120,0
DA:123,0
DA:124,0
DA:125,0
DA:134,0
DA:137,0
DA:138,0
DA:139,0
DA:140,0
DA:144,0
DA:146,0
DA:147,0
DA:148,0
DA:153,0
DA:154,0
DA:158,0
DA:159,0
DA:160,0
DA:163,0
DA:166,0
DA:169,0
DA:170,0
DA:172,0
DA:174,0
DA:177,0
DA:178,0
DA:181,0
DA:182,0
DA:184,0
DA:188,0
DA:204,0
LF:83
LH:0
BRDA:40,0,0,0
BRDA:40,0,1,0
BRDA:44,1,0,0
BRDA:44,1,1,0
BRDA:56,2,0,0
BRDA:56,2,1,0
BRDA:65,3,0,0
BRDA:65,3,1,0
BRDA:86,4,0,0
BRDA:86,4,1,0
BRDA:93,5,0,0
BRDA:93,5,1,0
BRDA:93,6,0,0
BRDA:93,6,1,0
BRDA:97,7,0,0
BRDA:97,7,1,0
BRDA:104,8,0,0
BRDA:104,8,1,0
BRDA:111,9,0,0
BRDA:111,9,1,0
BRDA:118,10,0,0
BRDA:118,10,1,0
BRDA:123,11,0,0
BRDA:123,11,1,0
BRDA:138,12,0,0
BRDA:138,12,1,0
BRDA:154,13,0,0
BRDA:154,13,1,0
BRF:28
BRH:0
end_of_record
TN:
SF:contexts\ClientContext.tsx
FN:18,(anonymous_0)
FN:19,(anonymous_1)
FN:20,(anonymous_2)
FN:21,(anonymous_3)
FN:24,ClientProvider
FN:30,(anonymous_5)
FN:56,(anonymous_6)
FN:82,(anonymous_7)
FN:112,(anonymous_8)
FN:154,(anonymous_9)
FNF:10
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,ClientProvider
FNDA:0,(anonymous_5)
FNDA:0,(anonymous_6)
FNDA:0,(anonymous_7)
FNDA:0,(anonymous_8)
FNDA:0,(anonymous_9)
DA:15,0
DA:18,0
DA:19,0
DA:20,0
DA:21,0
DA:25,0
DA:26,0
DA:27,0
DA:30,0
DA:31,0
DA:33,0
DA:34,0
DA:35,0
DA:37,0
DA:38,0
DA:39,0
DA:41,0
DA:44,0
DA:45,0
DA:46,0
DA:48,0
DA:49,0
DA:51,0
DA:56,0
DA:57,0
DA:59,0
DA:60,0
DA:61,0
DA:63,0
DA:64,0
DA:65,0
DA:67,0
DA:70,0
DA:71,0
DA:72,0
DA:74,0
DA:75,0
DA:77,0
DA:82,0
DA:83,0
DA:85,0
DA:86,0
DA:87,0
DA:95,0
DA:96,0
DA:97,0
DA:100,0
DA:101,0
DA:102,0
DA:104,0
DA:105,0
DA:107,0
DA:112,0
DA:113,0
DA:115,0
DA:116,0
DA:117,0
DA:119,0
DA:120,0
DA:121,0
DA:123,0
DA:126,0
DA:127,0
DA:128,0
DA:130,0
DA:131,0
DA:133,0
DA:137,0
DA:154,0
LF:69
LH:0
BRDA:31,0,0,0
BRDA:31,0,1,0
BRDA:37,1,0,0
BRDA:37,1,1,0
BRDA:38,2,0,0
BRDA:38,2,1,0
BRDA:57,3,0,0
BRDA:57,3,1,0
BRDA:63,4,0,0
BRDA:63,4,1,0
BRDA:64,5,0,0
BRDA:64,5,1,0
BRDA:83,6,0,0
BRDA:83,6,1,0
BRDA:95,7,0,0
BRDA:95,7,1,0
BRDA:113,8,0,0
BRDA:113,8,1,0
BRDA:119,9,0,0
BRDA:119,9,1,0
BRDA:120,10,0,0
BRDA:120,10,1,0
BRF:22
BRH:0
end_of_record
TN:
SF:contexts\TenantContext.tsx
FN:19,TenantProvider
FN:24,(anonymous_1)
FN:36,(anonymous_2)
FN:60,(anonymous_3)
FN:64,(anonymous_4)
FN:66,(anonymous_5)
FN:70,(anonymous_6)
FN:87,useTenant
FN:96,useTenantSchema
FN:102,useTenantFeatures
FN:105,(anonymous_10)
FN:109,(anonymous_11)
FNF:12
FNH:0
FNDA:0,TenantProvider
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
FNDA:0,(anonymous_6)
FNDA:0,useTenant
FNDA:0,useTenantSchema
FNDA:0,useTenantFeatures
FNDA:0,(anonymous_10)
FNDA:0,(anonymous_11)
DA:13,0
DA:20,0
DA:21,0
DA:22,0
DA:24,0
DA:25,0
DA:26,0
DA:27,0
DA:29,0
DA:30,0
DA:32,0
DA:34,0
DA:35,0
DA:36,0
DA:37,0
DA:39,0
DA:41,0
DA:44,0
DA:45,0
DA:46,0
DA:48,0
DA:51,0
DA:52,0
DA:53,0
DA:54,0
DA:56,0
DA:60,0
DA:61,0
DA:64,0
DA:66,0
DA:67,0
DA:70,0
DA:73,0
DA:80,0
DA:88,0
DA:89,0
DA:90,0
DA:92,0
DA:97,0
DA:98,0
DA:103,0
DA:105,0
DA:106,0
DA:109,0
DA:110,0
DA:113,0
LF:46
LH:0
BRDA:24,0,0,0
BRDA:32,1,0,0
BRDA:32,1,1,0
BRDA:34,2,0,0
BRDA:34,2,1,0
BRDA:34,3,0,0
BRDA:34,3,1,0
BRDA:41,4,0,0
BRDA:41,4,1,0
BRDA:44,5,0,0
BRDA:44,5,1,0
BRDA:44,6,0,0
BRDA:44,6,1,0
BRDA:51,7,0,0
BRDA:51,7,1,0
BRDA:89,8,0,0
BRDA:89,8,1,0
BRDA:98,9,0,0
BRDA:98,9,1,0
BRDA:116,10,0,0
BRDA:116,10,1,0
BRF:21
BRH:0
end_of_record
TN:
SF:contexts\UserContext.tsx
FN:43,(anonymous_0)
FN:44,(anonymous_1)
FN:45,(anonymous_2)
FN:46,(anonymous_3)
FN:49,UserProvider
FN:56,(anonymous_5)
FN:78,(anonymous_6)
FN:81,(anonymous_7)
FN:89,(anonymous_8)
FN:97,(anonymous_9)
FN:113,(anonymous_10)
FN:131,(anonymous_11)
FN:142,(anonymous_12)
FN:150,(anonymous_13)
FN:175,(anonymous_14)
FN:184,(anonymous_15)
FN:196,(anonymous_16)
FN:208,(anonymous_17)
FN:229,(anonymous_18)
FNF:19
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,UserProvider
FNDA:0,(anonymous_5)
FNDA:0,(anonymous_6)
FNDA:0,(anonymous_7)
FNDA:0,(anonymous_8)
FNDA:0,(anonymous_9)
FNDA:0,(anonymous_10)
FNDA:0,(anonymous_11)
FNDA:0,(anonymous_12)
FNDA:0,(anonymous_13)
FNDA:0,(anonymous_14)
FNDA:0,(anonymous_15)
FNDA:0,(anonymous_16)
FNDA:0,(anonymous_17)
FNDA:0,(anonymous_18)
DA:40,0
DA:43,0
DA:44,0
DA:45,0
DA:46,0
DA:50,0
DA:51,0
DA:52,0
DA:53,0
DA:56,0
DA:57,0
DA:59,0
DA:73,0
DA:78,0
DA:79,0
DA:81,0
DA:82,0
DA:83,0
DA:86,0
DA:87,0
DA:88,0
DA:89,0
DA:90,0
DA:94,0
DA:95,0
DA:96,0
DA:97,0
DA:100,0
DA:103,0
DA:105,0
DA:109,0
DA:113,0
DA:114,0
DA:116,0
DA:117,0
DA:118,0
DA:126,0
DA:127,0
DA:130,0
DA:131,0
DA:132,0
DA:134,0
DA:135,0
DA:137,0
DA:142,0
DA:143,0
DA:145,0
DA:146,0
DA:149,0
DA:150,0
DA:153,0
DA:156,0
DA:164,0
DA:165,0
DA:168,0
DA:170,0
DA:172,0
DA:173,0
DA:174,0
DA:175,0
DA:177,0
DA:179,0
DA:184,0
DA:185,0
DA:187,0
DA:188,0
DA:191,0
DA:192,0
DA:196,0
DA:197,0
DA:200,0
DA:201,0
DA:204,0
DA:208,0
DA:209,0
DA:212,0
DA:229,0
LF:77
LH:0
BRDA:57,0,0,0
BRDA:57,0,1,0
BRDA:57,1,0,0
BRDA:57,1,1,0
BRDA:61,2,0,0
BRDA:61,2,1,0
BRDA:62,3,0,0
BRDA:62,3,1,0
BRDA:79,4,0,0
BRDA:79,4,1,0
BRDA:87,5,0,0
BRDA:87,5,1,0
BRDA:89,6,0,0
BRDA:89,6,1,0
BRDA:95,7,0,0
BRDA:95,7,1,0
BRDA:97,8,0,0
BRDA:97,8,1,0
BRDA:114,9,0,0
BRDA:114,9,1,0
BRDA:126,10,0,0
BRDA:126,10,1,0
BRDA:131,11,0,0
BRDA:131,11,1,0
BRDA:143,12,0,0
BRDA:143,12,1,0
BRDA:150,13,0,0
BRDA:150,13,1,0
BRDA:164,14,0,0
BRDA:164,14,1,0
BRDA:173,15,0,0
BRDA:173,15,1,0
BRDA:175,16,0,0
BRDA:175,16,1,0
BRDA:185,17,0,0
BRDA:185,17,1,0
BRDA:187,18,0,0
BRDA:187,18,1,0
BRDA:187,19,0,0
BRDA:187,19,1,0
BRDA:191,20,0,0
BRDA:191,20,1,0
BRDA:200,21,0,0
BRDA:200,21,1,0
BRDA:200,22,0,0
BRDA:200,22,1,0
BRDA:209,23,0,0
BRDA:209,23,1,0
BRF:48
BRH:0
end_of_record
TN:
SF:hooks\useDashboardData.ts
FN:55,fetchWithTimeout
FN:59,(anonymous_1)
FN:77,useDashboardData
FN:97,(anonymous_3)
FN:106,(anonymous_4)
FN:141,(anonymous_5)
FN:176,(anonymous_6)
FN:228,(anonymous_7)
FN:234,(anonymous_8)
FN:241,(anonymous_9)
FN:247,(anonymous_10)
FN:259,(anonymous_11)
FN:268,(anonymous_12)
FN:269,(anonymous_13)
FNF:14
FNH:0
FNDA:0,fetchWithTimeout
FNDA:0,(anonymous_1)
FNDA:0,useDashboardData
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
FNDA:0,(anonymous_6)
FNDA:0,(anonymous_7)
FNDA:0,(anonymous_8)
FNDA:0,(anonymous_9)
FNDA:0,(anonymous_10)
FNDA:0,(anonymous_11)
FNDA:0,(anonymous_12)
FNDA:0,(anonymous_13)
DA:38,0
DA:45,0
DA:48,0
DA:49,0
DA:56,0
DA:58,0
DA:59,0
DA:61,0
DA:62,0
DA:69,0
DA:70,0
DA:72,0
DA:73,0
DA:78,0
DA:80,0
DA:86,0
DA:87,0
DA:90,0
DA:91,0
DA:94,0
DA:97,0
DA:98,0
DA:99,0
DA:106,0
DA:107,0
DA:109,0
DA:111,0
DA:112,0
DA:113,0
DA:114,0
DA:115,0
DA:118,0
DA:120,0
DA:121,0
DA:124,0
DA:126,0
DA:127,0
DA:128,0
DA:129,0
DA:130,0
DA:131,0
DA:134,0
DA:135,0
DA:136,0
DA:141,0
DA:142,0
DA:144,0
DA:146,0
DA:147,0
DA:148,0
DA:149,0
DA:150,0
DA:153,0
DA:155,0
DA:156,0
DA:159,0
DA:161,0
DA:162,0
DA:163,0
DA:164,0
DA:165,0
DA:166,0
DA:169,0
DA:170,0
DA:171,0
DA:176,0
DA:177,0
DA:180,0
DA:181,0
DA:184,0
DA:185,0
DA:187,0
DA:188,0
DA:190,0
DA:191,0
DA:196,0
DA:198,0
DA:204,0
DA:207,0
DA:208,0
DA:210,0
DA:211,0
DA:215,0
DA:217,0
DA:218,0
DA:219,0
DA:221,0
DA:222,0
DA:228,0
DA:229,0
DA:231,0
DA:232,0
DA:233,0
DA:234,0
DA:237,0
DA:241,0
DA:242,0
DA:244,0
DA:245,0
DA:246,0
DA:247,0
DA:254,0
DA:259,0
DA:260,0
DA:261,0
DA:262,0
DA:263,0
DA:268,0
DA:269,0
DA:270,0
DA:271,0
DA:272,0
DA:277,0
LF:113
LH:0
BRDA:55,0,0,0
BRDA:56,1,0,0
BRDA:63,2,0,0
BRDA:63,2,1,0
BRDA:98,3,0,0
BRDA:98,3,1,0
BRDA:106,4,0,0
BRDA:107,5,0,0
BRDA:107,5,1,0
BRDA:113,6,0,0
BRDA:113,6,1,0
BRDA:120,7,0,0
BRDA:120,7,1,0
BRDA:126,8,0,0
BRDA:126,8,1,0
BRDA:130,9,0,0
BRDA:130,9,1,0
BRDA:141,10,0,0
BRDA:142,11,0,0
BRDA:142,11,1,0
BRDA:148,12,0,0
BRDA:148,12,1,0
BRDA:155,13,0,0
BRDA:155,13,1,0
BRDA:161,14,0,0
BRDA:161,14,1,0
BRDA:165,15,0,0
BRDA:165,15,1,0
BRDA:177,16,0,0
BRDA:177,16,1,0
BRDA:177,17,0,0
BRDA:177,17,1,0
BRDA:180,18,0,0
BRDA:180,18,1,0
BRDA:196,19,0,0
BRDA:196,19,1,0
BRDA:199,20,0,0
BRDA:199,20,1,0
BRDA:200,21,0,0
BRDA:200,21,1,0
BRDA:201,22,0,0
BRDA:201,22,1,0
BRDA:207,23,0,0
BRDA:207,23,1,0
BRDA:210,24,0,0
BRDA:210,24,1,0
BRDA:215,25,0,0
BRDA:215,25,1,0
BRDA:217,26,0,0
BRDA:217,26,1,0
BRDA:221,27,0,0
BRDA:221,27,1,0
BRDA:229,28,0,0
BRDA:229,28,1,0
BRDA:233,29,0,0
BRDA:233,29,1,0
BRDA:242,30,0,0
BRDA:242,30,1,0
BRDA:246,31,0,0
BRDA:246,31,1,0
BRDA:260,32,0,0
BRDA:260,32,1,0
BRDA:260,33,0,0
BRDA:260,33,1,0
BRDA:260,33,2,0
BRDA:262,34,0,0
BRDA:262,34,1,0
BRDA:271,35,0,0
BRDA:271,35,1,0
BRDA:279,36,0,0
BRDA:279,36,1,0
BRDA:280,37,0,0
BRDA:280,37,1,0
BRF:73
BRH:0
end_of_record
TN:
SF:lib\actions.ts
FN:9,(anonymous_0)
FN:28,generateCSRFToken
FN:44,updateUserProfile
FNF:3
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,generateCSRFToken
FNDA:0,updateUserProfile
DA:9,0
DA:10,0
DA:11,0
DA:12,0
DA:14,0
DA:16,0
DA:21,0
DA:24,0
DA:29,0
DA:32,0
DA:40,0
DA:46,0
DA:49,0
DA:50,0
DA:51,0
DA:55,0
DA:56,0
DA:60,0
DA:61,0
DA:62,0
DA:66,0
DA:68,0
DA:70,0
DA:71,0
LF:24
LH:0
BRDA:14,0,0,0
BRDA:14,0,1,0
BRDA:14,1,0,0
BRDA:14,1,1,0
BRDA:14,1,2,0
BRDA:50,2,0,0
BRDA:50,2,1,0
BRDA:55,3,0,0
BRDA:55,3,1,0
BRDA:61,4,0,0
BRDA:61,4,1,0
BRDA:61,5,0,0
BRDA:61,5,1,0
BRF:13
BRH:0
end_of_record
TN:
SF:lib\amplify-config.ts
FN:5,configureAmplify
FN:60,getAmplifySSR
FNF:2
FNH:0
FNDA:0,configureAmplify
FNDA:0,getAmplifySSR
DA:7,0
DA:8,0
DA:9,0
DA:13,0
DA:14,0
DA:23,0
DA:32,0
DA:33,0
DA:52,0
DA:54,0
DA:55,0
DA:62,0
LF:12
LH:0
BRDA:7,0,0,0
BRDA:7,0,1,0
BRF:2
BRH:0
end_of_record
TN:
SF:lib\api-response.ts
FN:51,createSuccessResponse
FN:69,createErrorResponse
FN:93,handleValidationError
FN:94,(anonymous_3)
FN:107,handleDatabaseError
FN:126,createUnauthorizedResponse
FN:137,createForbiddenResponse
FN:148,createNotFoundResponse
FN:159,createRateLimitResponse
FN:177,handleApiError
FN:206,addSecurityHeaders
FN:218,withErrorHandling
FN:221,(anonymous_12)
FNF:13
FNH:0
FNDA:0,createSuccessResponse
FNDA:0,createErrorResponse
FNDA:0,handleValidationError
FNDA:0,(anonymous_3)
FNDA:0,handleDatabaseError
FNDA:0,createUnauthorizedResponse
FNDA:0,createForbiddenResponse
FNDA:0,createNotFoundResponse
FNDA:0,createRateLimitResponse
FNDA:0,handleApiError
FNDA:0,addSecurityHeaders
FNDA:0,withErrorHandling
FNDA:0,(anonymous_12)
DA:56,0
DA:63,0
DA:75,0
DA:83,0
DA:84,0
DA:87,0
DA:94,0
DA:96,0
DA:108,0
DA:111,0
DA:115,0
DA:127,0
DA:138,0
DA:149,0
DA:160,0
DA:167,0
DA:168,0
DA:169,0
DA:171,0
DA:178,0
DA:181,0
DA:182,0
DA:186,0
DA:187,0
DA:191,0
DA:195,0
DA:207,0
DA:208,0
DA:209,0
DA:210,0
DA:212,0
DA:221,0
DA:222,0
DA:223,0
DA:224,0
DA:226,0
DA:227,0
LF:37
LH:0
BRDA:54,0,0,0
BRDA:83,1,0,0
BRDA:83,1,1,0
BRDA:83,2,0,0
BRDA:83,2,1,0
BRDA:111,3,0,0
BRDA:111,3,1,0
BRDA:119,4,0,0
BRDA:119,4,1,0
BRDA:126,5,0,0
BRDA:137,6,0,0
BRDA:148,7,0,0
BRDA:181,8,0,0
BRDA:181,8,1,0
BRDA:186,9,0,0
BRDA:186,9,1,0
BRDA:186,10,0,0
BRDA:186,10,1,0
BRDA:186,10,2,0
BRDA:191,11,0,0
BRDA:191,11,1,0
BRDA:192,12,0,0
BRDA:192,12,1,0
BRDA:199,13,0,0
BRDA:199,13,1,0
BRF:25
BRH:0
end_of_record
TN:
SF:lib\auth-middleware.ts
FN:29,requireAuth
FN:56,requireRole
FN:66,(anonymous_2)
FN:81,requireAdmin
FN:88,hasAnyRole
FN:89,(anonymous_5)
FN:95,hasAllRoles
FN:96,(anonymous_7)
FN:102,getUserId
FN:109,getUserEmail
FN:116,isAuthenticated
FN:123,withAuth
FN:126,(anonymous_12)
FN:140,withRole
FN:144,(anonymous_14)
FN:158,withAdmin
FN:169,checkRateLimit
FN:203,getRateLimitIdentifier
FNF:18
FNH:0
FNDA:0,requireAuth
FNDA:0,requireRole
FNDA:0,(anonymous_2)
FNDA:0,requireAdmin
FNDA:0,hasAnyRole
FNDA:0,(anonymous_5)
FNDA:0,hasAllRoles
FNDA:0,(anonymous_7)
FNDA:0,getUserId
FNDA:0,getUserEmail
FNDA:0,isAuthenticated
FNDA:0,withAuth
FNDA:0,(anonymous_12)
FNDA:0,withRole
FNDA:0,(anonymous_14)
FNDA:0,withAdmin
FNDA:0,checkRateLimit
FNDA:0,getRateLimitIdentifier
DA:30,0
DA:31,0
DA:33,0
DA:34,0
DA:40,0
DA:45,0
DA:46,0
DA:57,0
DA:59,0
DA:60,0
DA:63,0
DA:64,0
DA:66,0
DA:68,0
DA:69,0
DA:75,0
DA:82,0
DA:89,0
DA:96,0
DA:103,0
DA:110,0
DA:117,0
DA:126,0
DA:127,0
DA:129,0
DA:130,0
DA:133,0
DA:144,0
DA:145,0
DA:147,0
DA:148,0
DA:151,0
DA:161,0
DA:167,0
DA:174,0
DA:175,0
DA:178,0
DA:179,0
DA:180,0
DA:184,0
DA:186,0
DA:188,0
DA:189,0
DA:192,0
DA:193,0
DA:196,0
DA:197,0
DA:205,0
DA:206,0
DA:210,0
DA:211,0
DA:212,0
DA:214,0
LF:53
LH:0
BRDA:33,0,0,0
BRDA:33,0,1,0
BRDA:59,1,0,0
BRDA:59,1,1,0
BRDA:63,2,0,0
BRDA:63,2,1,0
BRDA:68,3,0,0
BRDA:68,3,1,0
BRDA:117,4,0,0
BRDA:117,4,1,0
BRDA:129,5,0,0
BRDA:129,5,1,0
BRDA:147,6,0,0
BRDA:147,6,1,0
BRDA:171,7,0,0
BRDA:172,8,0,0
BRDA:179,9,0,0
BRDA:179,9,1,0
BRDA:186,10,0,0
BRDA:186,10,1,0
BRDA:186,11,0,0
BRDA:186,11,1,0
BRDA:192,12,0,0
BRDA:192,12,1,0
BRDA:205,13,0,0
BRDA:205,13,1,0
BRDA:212,14,0,0
BRDA:212,14,1,0
BRDA:212,14,2,0
BRF:29
BRH:0
end_of_record
TN:
SF:lib\auth.ts
FN:7,(anonymous_0)
FN:15,(anonymous_1)
FN:21,(anonymous_2)
FN:35,(anonymous_3)
FN:62,(anonymous_4)
FN:65,(anonymous_5)
FNF:6
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
DA:7,0
DA:8,0
DA:9,0
DA:11,0
DA:15,0
DA:16,0
DA:17,0
DA:21,0
DA:22,0
DA:24,0
DA:26,0
DA:27,0
DA:29,0
DA:30,0
DA:35,0
DA:36,0
DA:38,0
DA:41,0
DA:44,0
DA:45,0
DA:47,0
DA:49,0
DA:52,0
DA:53,0
DA:55,0
DA:62,0
DA:63,0
DA:64,0
DA:65,0
DA:68,0
DA:69,0
DA:73,0
DA:74,0
DA:75,0
DA:78,0
DA:86,0
DA:87,0
LF:37
LH:0
BRDA:68,0,0,0
BRDA:68,0,1,0
BRDA:74,1,0,0
BRDA:74,1,1,0
BRDA:82,2,0,0
BRDA:82,2,1,0
BRDA:83,3,0,0
BRDA:83,3,1,0
BRF:8
BRH:0
end_of_record
TN:
SF:lib\aws-config.ts
FN:5,getCredentials
FN:33,getParameter
FNF:2
FNH:0
FNDA:0,getCredentials
FNDA:0,getParameter
DA:6,0
DA:7,0
DA:14,0
DA:16,0
DA:22,0
DA:24,0
DA:34,0
DA:36,0
DA:45,0
DA:50,0
DA:51,0
LF:11
LH:0
BRDA:6,0,0,0
BRDA:6,0,1,0
BRDA:37,1,0,0
BRDA:37,1,1,0
BRDA:39,2,0,0
BRDA:39,2,1,0
BRDA:40,3,0,0
BRDA:40,3,1,0
BRF:8
BRH:0
end_of_record
TN:
SF:lib\cache.ts
FN:51,(anonymous_0)
FN:69,(anonymous_1)
FN:93,(anonymous_2)
FN:113,(anonymous_3)
FN:126,(anonymous_4)
FN:131,(anonymous_5)
FN:134,(anonymous_6)
FN:143,(anonymous_7)
FN:149,(anonymous_8)
FN:159,(anonymous_9)
FN:164,(anonymous_10)
FN:169,(anonymous_11)
FN:174,(anonymous_12)
FN:201,(anonymous_13)
FN:216,(anonymous_14)
FN:231,(anonymous_15)
FN:241,(anonymous_16)
FN:256,(anonymous_17)
FN:273,(anonymous_18)
FN:284,(anonymous_19)
FN:285,(anonymous_20)
FN:291,(anonymous_21)
FN:301,(anonymous_22)
FN:305,(anonymous_23)
FN:335,(anonymous_24)
FN:340,(anonymous_25)
FN:342,(anonymous_26)
FN:354,(anonymous_27)
FN:356,(anonymous_28)
FN:368,(anonymous_29)
FN:375,(anonymous_30)
FN:383,useCache
FN:387,(anonymous_32)
FN:388,(anonymous_33)
FN:390,(anonymous_34)
FN:391,(anonymous_35)
FN:392,(anonymous_36)
FNF:37
FNH:30
FNDA:37,(anonymous_0)
FNDA:36,(anonymous_1)
FNDA:143,(anonymous_2)
FNDA:2,(anonymous_3)
FNDA:3,(anonymous_4)
FNDA:3,(anonymous_5)
FNDA:9,(anonymous_6)
FNDA:36,(anonymous_7)
FNDA:7,(anonymous_8)
FNDA:1,(anonymous_9)
FNDA:7,(anonymous_10)
FNDA:19,(anonymous_11)
FNDA:101,(anonymous_12)
FNDA:99,(anonymous_13)
FNDA:1,(anonymous_14)
FNDA:0,(anonymous_15)
FNDA:1,(anonymous_16)
FNDA:179,(anonymous_17)
FNDA:36,(anonymous_18)
FNDA:37,(anonymous_19)
FNDA:6,(anonymous_20)
FNDA:6,(anonymous_21)
FNDA:2,(anonymous_22)
FNDA:32,(anonymous_23)
FNDA:1,(anonymous_24)
FNDA:3,(anonymous_25)
FNDA:8,(anonymous_26)
FNDA:2,(anonymous_27)
FNDA:3,(anonymous_28)
FNDA:1,(anonymous_29)
FNDA:1,(anonymous_30)
FNDA:0,useCache
FNDA:0,(anonymous_32)
FNDA:0,(anonymous_33)
FNDA:0,(anonymous_34)
FNDA:0,(anonymous_35)
FNDA:0,(anonymous_36)
DA:39,37
DA:41,37
DA:55,37
DA:62,37
DA:65,37
DA:70,36
DA:72,36
DA:73,18
DA:74,18
DA:78,18
DA:79,0
DA:80,0
DA:81,0
DA:85,18
DA:86,18
DA:88,18
DA:89,18
DA:95,143
DA:96,101
DA:99,143
DA:108,143
DA:109,143
DA:114,2
DA:115,2
DA:117,1
DA:118,0
DA:119,0
DA:122,1
DA:127,3
DA:132,3
DA:133,3
DA:134,9
DA:135,5
DA:136,5
DA:139,3
DA:144,36
DA:145,36
DA:150,7
DA:151,7
DA:160,1
DA:165,7
DA:170,19
DA:175,101
DA:177,101
DA:179,101
DA:181,99
DA:182,99
DA:184,1
DA:185,1
DA:187,0
DA:188,0
DA:190,1
DA:191,1
DA:194,101
DA:195,58
DA:196,58
DA:202,99
DA:203,99
DA:205,99
DA:206,2971
DA:207,57
DA:208,57
DA:212,99
DA:217,1
DA:218,1
DA:220,1
DA:221,3
DA:222,3
DA:223,3
DA:227,1
DA:232,0
DA:233,0
DA:234,0
DA:237,0
DA:242,1
DA:243,1
DA:245,1
DA:246,3
DA:247,0
DA:248,0
DA:252,1
DA:257,179
DA:259,179
DA:261,18
DA:262,18
DA:264,18
DA:265,18
DA:268,143
DA:274,36
DA:285,37
DA:286,6
DA:292,6
DA:293,6
DA:295,6
DA:296,4
DA:297,2
DA:301,6
DA:306,32
DA:307,32
DA:309,32
DA:314,1
DA:320,1
DA:326,1
DA:333,1
DA:336,1
DA:341,3
DA:342,3
DA:343,8
DA:344,0
DA:346,8
DA:349,1
DA:355,2
DA:356,2
DA:357,3
DA:358,0
DA:360,3
DA:363,1
DA:368,1
DA:376,1
DA:377,1
DA:378,1
DA:386,0
DA:387,0
DA:389,0
DA:390,0
DA:391,0
DA:392,0
LF:127
LH:106
BRDA:52,0,0,0
BRDA:53,1,0,0
BRDA:72,2,0,18
BRDA:72,2,1,18
BRDA:78,3,0,0
BRDA:78,3,1,18
BRDA:93,4,0,137
BRDA:95,5,0,101
BRDA:95,5,1,42
BRDA:102,6,0,143
BRDA:102,6,1,137
BRDA:115,7,0,1
BRDA:115,7,1,1
BRDA:117,8,0,0
BRDA:117,8,1,1
BRDA:134,9,0,5
BRDA:134,9,1,1
BRDA:154,10,0,2
BRDA:154,10,1,5
BRDA:175,11,0,0
BRDA:175,11,1,101
BRDA:179,12,0,99
BRDA:179,12,1,1
BRDA:179,12,2,0
BRDA:179,12,3,1
BRDA:194,13,0,58
BRDA:194,13,1,43
BRDA:206,14,0,57
BRDA:206,14,1,2914
BRDA:221,15,0,3
BRDA:221,15,1,0
BRDA:233,16,0,0
BRDA:233,16,1,0
BRDA:246,17,0,0
BRDA:246,17,1,3
BRDA:257,18,0,0
BRDA:257,18,1,179
BRDA:259,19,0,18
BRDA:259,19,1,18
BRDA:259,19,2,143
BRDA:296,20,0,2
BRDA:296,20,1,2
BRDA:306,21,0,32
BRDA:306,21,1,0
BRDA:343,22,0,0
BRDA:343,22,1,8
BRDA:357,23,0,0
BRDA:357,23,1,3
BRDA:357,24,0,3
BRDA:357,24,1,3
BRDA:384,25,0,0
BRF:51
BRH:36
end_of_record
TN:
SF:lib\clients.ts
FN:13,getTenantByDomain
FN:27,getClientByDomain
FN:76,getClientByEmailDomain
FN:174,createClient
FN:216,updateClient
FNF:5
FNH:0
FNDA:0,getTenantByDomain
FNDA:0,getClientByDomain
FNDA:0,getClientByEmailDomain
FNDA:0,createClient
FNDA:0,updateClient
DA:14,0
DA:22,0
DA:23,0
DA:28,0
DA:29,0
DA:32,0
DA:46,0
DA:48,0
DA:49,0
DA:54,0
DA:57,0
DA:58,0
DA:62,0
DA:63,0
DA:64,0
DA:65,0
DA:67,0
DA:68,0
DA:72,0
DA:77,0
DA:78,0
DA:85,0
DA:86,0
DA:87,0
DA:88,0
DA:95,0
DA:109,0
DA:111,0
DA:112,0
DA:119,0
DA:120,0
DA:127,0
DA:128,0
DA:129,0
DA:137,0
DA:140,0
DA:141,0
DA:143,0
DA:155,0
DA:161,0
DA:165,0
DA:180,0
DA:198,0
DA:205,0
DA:207,0
DA:208,0
DA:209,0
DA:212,0
DA:226,0
DA:227,0
DA:228,0
DA:230,0
DA:231,0
DA:232,0
DA:235,0
DA:236,0
DA:237,0
DA:240,0
DA:241,0
DA:242,0
DA:245,0
DA:246,0
DA:247,0
DA:250,0
DA:251,0
DA:255,0
DA:257,0
DA:271,0
DA:273,0
DA:274,0
DA:275,0
DA:278,0
LF:72
LH:0
BRDA:23,0,0,0
BRDA:23,0,1,0
BRDA:28,1,0,0
BRDA:28,1,1,0
BRDA:48,2,0,0
BRDA:48,2,1,0
BRDA:57,3,0,0
BRDA:57,3,1,0
BRDA:63,4,0,0
BRDA:63,4,1,0
BRDA:63,5,0,0
BRDA:63,5,1,0
BRDA:77,6,0,0
BRDA:77,6,1,0
BRDA:77,7,0,0
BRDA:77,7,1,0
BRDA:87,8,0,0
BRDA:87,8,1,0
BRDA:111,9,0,0
BRDA:111,9,1,0
BRDA:119,10,0,0
BRDA:119,10,1,0
BRDA:119,11,0,0
BRDA:119,11,1,0
BRDA:127,12,0,0
BRDA:127,12,1,0
BRDA:140,13,0,0
BRDA:140,13,1,0
BRDA:146,14,0,0
BRDA:146,14,1,0
BRDA:148,15,0,0
BRDA:148,15,1,0
BRDA:152,16,0,0
BRDA:152,16,1,0
BRDA:163,17,0,0
BRDA:163,17,1,0
BRDA:201,18,0,0
BRDA:201,18,1,0
BRDA:202,19,0,0
BRDA:202,19,1,0
BRDA:207,20,0,0
BRDA:207,20,1,0
BRDA:230,21,0,0
BRDA:230,21,1,0
BRDA:235,22,0,0
BRDA:235,22,1,0
BRDA:240,23,0,0
BRDA:240,23,1,0
BRDA:245,24,0,0
BRDA:245,24,1,0
BRDA:250,25,0,0
BRDA:250,25,1,0
BRDA:273,26,0,0
BRDA:273,26,1,0
BRF:54
BRH:0
end_of_record
TN:
SF:lib\config.ts
FN:35,validateEnv
FN:83,(anonymous_1)
FN:84,(anonymous_2)
FN:85,(anonymous_3)
FN:93,(anonymous_4)
FN:101,(anonymous_5)
FNF:6
FNH:0
FNDA:0,validateEnv
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
DA:11,0
DA:36,0
DA:37,0
DA:39,0
DA:40,0
DA:45,0
DA:48,0
DA:67,0
DA:83,0
DA:84,0
DA:85,0
DA:86,0
DA:87,0
DA:89,0
DA:93,0
DA:94,0
DA:101,0
DA:103,0
DA:104,0
DA:107,0
LF:20
LH:0
BRDA:86,0,0,0
BRDA:86,0,1,0
BRDA:103,1,0,0
BRDA:103,1,1,0
BRF:4
BRH:0
end_of_record
TN:
SF:lib\dal.ts
FN:7,(anonymous_0)
FN:75,(anonymous_1)
FN:85,(anonymous_2)
FNF:3
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
DA:7,0
DA:13,0
DA:14,0
DA:15,0
DA:18,0
DA:24,0
DA:27,0
DA:28,0
DA:29,0
DA:30,0
DA:31,0
DA:32,0
DA:37,0
DA:38,0
DA:40,0
DA:41,0
DA:42,0
DA:43,0
DA:44,0
DA:49,0
DA:50,0
DA:51,0
DA:55,0
DA:56,0
DA:57,0
DA:58,0
DA:61,0
DA:63,0
DA:70,0
DA:71,0
DA:75,0
DA:76,0
DA:77,0
DA:81,0
DA:85,0
DA:86,0
DA:87,0
LF:37
LH:0
BRDA:29,0,0,0
BRDA:29,0,1,0
BRDA:37,1,0,0
BRDA:37,1,1,0
BRDA:41,2,0,0
BRDA:41,2,1,0
BRDA:41,3,0,0
BRDA:41,3,1,0
BRDA:49,4,0,0
BRDA:49,4,1,0
BRDA:56,5,0,0
BRDA:56,5,1,0
BRDA:67,6,0,0
BRDA:67,6,1,0
BRDA:77,7,0,0
BRDA:77,7,1,0
BRDA:86,8,0,0
BRDA:86,8,1,0
BRDA:86,9,0,0
BRDA:86,9,1,0
BRF:20
BRH:0
end_of_record
TN:
SF:lib\database.ts
FN:48,executeQuery
FN:113,executeQuerySingle
FN:134,executeTransaction
FN:163,(anonymous_3)
FN:198,schemaExists
FN:210,tableExists
FN:222,getConnectionHealth
FN:251,closeDatabase
FN:264,getDatabaseErrorMessage
FN:298,buildWhereClause
FN:310,(anonymous_10)
FN:330,escapeIdentifier
FNF:12
FNH:0
FNDA:0,executeQuery
FNDA:0,executeQuerySingle
FNDA:0,executeTransaction
FNDA:0,(anonymous_3)
FNDA:0,schemaExists
FNDA:0,tableExists
FNDA:0,getConnectionHealth
FNDA:0,closeDatabase
FNDA:0,getDatabaseErrorMessage
FNDA:0,buildWhereClause
FNDA:0,(anonymous_10)
FNDA:0,escapeIdentifier
DA:43,0
DA:53,0
DA:55,0
DA:56,0
DA:57,0
DA:60,0
DA:61,0
DA:65,0
DA:66,0
DA:69,0
DA:70,0
DA:71,0
DA:74,0
DA:75,0
DA:82,0
DA:89,0
DA:90,0
DA:98,0
DA:104,0
DA:105,0
DA:118,0
DA:120,0
DA:121,0
DA:124,0
DA:138,0
DA:140,0
DA:141,0
DA:142,0
DA:145,0
DA:146,0
DA:149,0
DA:151,0
DA:153,0
DA:154,0
DA:155,0
DA:158,0
DA:160,0
DA:163,0
DA:167,0
DA:168,0
DA:169,0
DA:171,0
DA:175,0
DA:176,0
DA:183,0
DA:189,0
DA:190,0
DA:199,0
DA:204,0
DA:211,0
DA:216,0
DA:228,0
DA:229,0
DA:231,0
DA:238,0
DA:239,0
DA:252,0
DA:253,0
DA:254,0
DA:255,0
DA:257,0
DA:265,0
DA:267,0
DA:269,0
DA:271,0
DA:273,0
DA:275,0
DA:277,0
DA:279,0
DA:281,0
DA:283,0
DA:285,0
DA:287,0
DA:289,0
DA:291,0
DA:302,0
DA:303,0
DA:304,0
DA:306,0
DA:307,0
DA:308,0
DA:310,0
DA:311,0
DA:312,0
DA:314,0
DA:315,0
DA:320,0
DA:331,0
LF:88
LH:0
BRDA:50,0,0,0
BRDA:51,1,0,0
BRDA:60,2,0,0
BRDA:60,2,1,0
BRDA:65,3,0,0
BRDA:65,3,1,0
BRDA:74,4,0,0
BRDA:74,4,1,0
BRDA:85,5,0,0
BRDA:85,5,1,0
BRDA:101,6,0,0
BRDA:101,6,1,0
BRDA:104,7,0,0
BRDA:104,7,1,0
BRDA:115,8,0,0
BRDA:116,9,0,0
BRDA:120,10,0,0
BRDA:120,10,1,0
BRDA:126,11,0,0
BRDA:126,11,1,0
BRDA:136,12,0,0
BRDA:145,13,0,0
BRDA:145,13,1,0
BRDA:153,14,0,0
BRDA:167,15,0,0
BRDA:167,15,1,0
BRDA:186,16,0,0
BRDA:186,16,1,0
BRDA:189,17,0,0
BRDA:189,17,1,0
BRDA:204,18,0,0
BRDA:204,18,1,0
BRDA:210,19,0,0
BRDA:216,20,0,0
BRDA:216,20,1,0
BRDA:265,21,0,0
BRDA:265,21,1,0
BRDA:265,21,2,0
BRDA:265,21,3,0
BRDA:265,21,4,0
BRDA:265,21,5,0
BRDA:265,21,6,0
BRDA:265,21,7,0
BRDA:265,21,8,0
BRDA:265,21,9,0
BRDA:265,21,10,0
BRDA:265,21,11,0
BRDA:265,21,12,0
BRDA:300,22,0,0
BRDA:307,23,0,0
BRDA:307,23,1,0
BRDA:307,24,0,0
BRDA:307,24,1,0
BRDA:308,25,0,0
BRDA:308,25,1,0
BRDA:321,26,0,0
BRDA:321,26,1,0
BRF:57
BRH:0
end_of_record
TN:
SF:lib\db-config.ts
FN:9,(anonymous_0)
FN:37,getDbPool
FN:55,(anonymous_2)
FN:59,(anonymous_3)
FN:63,(anonymous_4)
FNF:5
FNH:0
FNDA:0,(anonymous_0)
FNDA:0,getDbPool
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
DA:6,0
DA:9,0
DA:10,0
DA:34,0
DA:38,0
DA:40,0
DA:43,0
DA:44,0
DA:51,0
DA:52,0
DA:55,0
DA:56,0
DA:59,0
DA:60,0
DA:63,0
DA:64,0
DA:67,0
DA:71,0
DA:72,0
DA:73,0
DA:74,0
DA:77,0
DA:85,0
DA:87,0
DA:88,0
DA:92,0
DA:101,0
DA:103,0
DA:104,0
LF:29
LH:0
BRDA:13,0,0,0
BRDA:13,0,1,0
BRDA:16,1,0,0
BRDA:16,1,1,0
BRDA:38,2,0,0
BRDA:38,2,1,0
BRDA:43,3,0,0
BRDA:43,3,1,0
BRDA:46,4,0,0
BRDA:46,4,1,0
BRDA:72,5,0,0
BRDA:72,5,1,0
BRDA:72,5,2,0
BRDA:73,6,0,0
BRDA:73,6,1,0
BRDA:74,7,0,0
BRDA:74,7,1,0
BRDA:87,8,0,0
BRDA:87,8,1,0
BRF:19
BRH:0
end_of_record
TN:
SF:lib\direct-auth-config.ts
FN:3,configureAmplifyDirectly
FNF:1
FNH:0
FNDA:0,configureAmplifyDirectly
DA:5,0
DA:8,0
DA:16,0
DA:17,0
DA:36,0
DA:38,0
LF:6
LH:0
BRDA:5,0,0,0
BRDA:5,0,1,0
BRF:2
BRH:0
end_of_record
TN:
SF:lib\jwt-validator.ts
FN:35,getJwksUrl
FN:43,getJwks
FN:54,validateJwtToken
FN:99,validateAuthCookie
FN:120,parseJwtUnsafe
FN:150,isTokenExpired
FN:158,getTokenExpirationTime
FN:165,isTokenExpiringWithin
FNF:8
FNH:0
FNDA:0,getJwksUrl
FNDA:0,getJwks
FNDA:0,validateJwtToken
FNDA:0,validateAuthCookie
FNDA:0,parseJwtUnsafe
FNDA:0,isTokenExpired
FNDA:0,getTokenExpirationTime
FNDA:0,isTokenExpiringWithin
DA:30,0
DA:36,0
DA:37,0
DA:44,0
DA:45,0
DA:46,0
DA:48,0
DA:55,0
DA:56,0
DA:57,0
DA:60,0
DA:63,0
DA:69,0
DA:72,0
DA:73,0
DA:74,0
DA:78,0
DA:79,0
DA:80,0
DA:84,0
DA:85,0
DA:89,0
DA:91,0
DA:92,0
DA:100,0
DA:101,0
DA:105,0
DA:108,0
DA:109,0
DA:110,0
DA:113,0
DA:121,0
DA:122,0
DA:125,0
DA:126,0
DA:127,0
DA:128,0
DA:131,0
DA:134,0
DA:135,0
DA:136,0
DA:137,0
DA:140,0
DA:142,0
DA:143,0
DA:151,0
DA:152,0
DA:159,0
DA:166,0
DA:167,0
DA:168,0
LF:51
LH:0
BRDA:44,0,0,0
BRDA:44,0,1,0
BRDA:72,1,0,0
BRDA:72,1,1,0
BRDA:78,2,0,0
BRDA:78,2,1,0
BRDA:84,3,0,0
BRDA:84,3,1,0
BRDA:100,4,0,0
BRDA:100,4,1,0
BRDA:108,5,0,0
BRDA:108,5,1,0
BRDA:108,6,0,0
BRDA:108,6,1,0
BRDA:121,7,0,0
BRDA:121,7,1,0
BRDA:127,8,0,0
BRDA:127,8,1,0
BRDA:135,9,0,0
BRDA:135,9,1,0
BRDA:135,10,0,0
BRDA:135,10,1,0
BRF:22
BRH:0
end_of_record
TN:
SF:lib\performance.ts
FN:11,useDebounce
FN:14,(anonymous_1)
FN:15,(anonymous_2)
FN:19,(anonymous_3)
FN:28,useThrottle
FN:35,(anonymous_5)
FN:46,useOptimizedCallback
FN:54,useDeepMemo
FN:65,areEqual
FN:78,usePerformanceMonitor
FN:82,(anonymous_10)
FN:105,useIntersectionObserver
FN:111,(anonymous_12)
FN:116,(anonymous_13)
FN:127,(anonymous_14)
FN:136,useVirtualScrolling
FN:143,(anonymous_16)
FN:159,(anonymous_17)
FN:170,useBatchedState
FN:177,(anonymous_19)
FN:184,(anonymous_20)
FN:185,(anonymous_21)
FN:196,(anonymous_22)
FN:197,(anonymous_23)
FN:208,useMemoryMonitor
FN:209,(anonymous_25)
FN:211,(anonymous_26)
FN:222,(anonymous_27)
FN:228,createOptimizedEventHandler
FN:233,(anonymous_29)
FN:246,useRenderOptimization
FN:250,(anonymous_31)
FN:256,(anonymous_32)
FN:278,(anonymous_33)
FN:285,(anonymous_34)
FN:301,(anonymous_35)
FNF:36
FNH:0
FNDA:0,useDebounce
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,useThrottle
FNDA:0,(anonymous_5)
FNDA:0,useOptimizedCallback
FNDA:0,useDeepMemo
FNDA:0,areEqual
FNDA:0,usePerformanceMonitor
FNDA:0,(anonymous_10)
FNDA:0,useIntersectionObserver
FNDA:0,(anonymous_12)
FNDA:0,(anonymous_13)
FNDA:0,(anonymous_14)
FNDA:0,useVirtualScrolling
FNDA:0,(anonymous_16)
FNDA:0,(anonymous_17)
FNDA:0,useBatchedState
FNDA:0,(anonymous_19)
FNDA:0,(anonymous_20)
FNDA:0,(anonymous_21)
FNDA:0,(anonymous_22)
FNDA:0,(anonymous_23)
FNDA:0,useMemoryMonitor
FNDA:0,(anonymous_25)
FNDA:0,(anonymous_26)
FNDA:0,(anonymous_27)
FNDA:0,createOptimizedEventHandler
FNDA:0,(anonymous_29)
FNDA:0,useRenderOptimization
FNDA:0,(anonymous_31)
FNDA:0,(anonymous_32)
FNDA:0,(anonymous_33)
FNDA:0,(anonymous_34)
FNDA:0,(anonymous_35)
DA:12,0
DA:14,0
DA:15,0
DA:16,0
DA:19,0
DA:20,0
DA:24,0
DA:32,0
DA:34,0
DA:36,0
DA:37,0
DA:38,0
DA:50,0
DA:55,0
DA:57,0
DA:58,0
DA:61,0
DA:66,0
DA:68,0
DA:69,0
DA:70,0
DA:74,0
DA:79,0
DA:80,0
DA:82,0
DA:83,0
DA:85,0
DA:86,0
DA:87,0
DA:90,0
DA:91,0
DA:93,0
DA:94,0
DA:97,0
DA:101,0
DA:108,0
DA:109,0
DA:111,0
DA:112,0
DA:113,0
DA:115,0
DA:117,0
DA:125,0
DA:127,0
DA:128,0
DA:132,0
DA:141,0
DA:143,0
DA:144,0
DA:145,0
DA:150,0
DA:159,0
DA:160,0
DA:163,0
DA:173,0
DA:174,0
DA:175,0
DA:177,0
DA:178,0
DA:180,0
DA:181,0
DA:184,0
DA:185,0
DA:186,0
DA:187,0
DA:188,0
DA:190,0
DA:191,0
DA:196,0
DA:197,0
DA:198,0
DA:199,0
DA:204,0
DA:209,0
DA:210,0
DA:211,0
DA:212,0
DA:213,0
DA:214,0
DA:221,0
DA:222,0
DA:232,0
DA:234,0
DA:236,0
DA:238,0
DA:247,0
DA:248,0
DA:250,0
DA:251,0
DA:253,0
DA:254,0
DA:255,0
DA:256,0
DA:259,0
DA:260,0
DA:261,0
DA:262,0
DA:266,0
DA:270,0
DA:276,0
DA:279,0
DA:280,0
DA:286,0
DA:287,0
DA:288,0
DA:290,0
DA:291,0
DA:292,0
DA:293,0
DA:294,0
DA:302,0
DA:303,0
DA:304,0
DA:305,0
LF:114
LH:0
BRDA:36,0,0,0
BRDA:36,0,1,0
BRDA:57,1,0,0
BRDA:57,1,1,0
BRDA:57,2,0,0
BRDA:57,2,1,0
BRDA:66,3,0,0
BRDA:66,3,1,0
BRDA:69,4,0,0
BRDA:69,4,1,0
BRDA:85,5,0,0
BRDA:85,5,1,0
BRDA:86,6,0,0
BRDA:86,6,1,0
BRDA:93,7,0,0
BRDA:93,7,1,0
BRDA:106,8,0,0
BRDA:113,9,0,0
BRDA:113,9,1,0
BRDA:180,10,0,0
BRDA:180,10,1,0
BRDA:198,11,0,0
BRDA:198,11,1,0
BRDA:210,12,0,0
BRDA:210,12,1,0
BRDA:210,13,0,0
BRDA:210,13,1,0
BRDA:213,14,0,0
BRDA:213,14,1,0
BRDA:230,15,0,0
BRDA:234,16,0,0
BRDA:234,16,1,0
BRDA:253,17,0,0
BRDA:253,17,1,0
BRDA:254,18,0,0
BRDA:254,18,1,0
BRDA:259,19,0,0
BRDA:259,19,1,0
BRDA:261,20,0,0
BRDA:261,20,1,0
BRDA:279,21,0,0
BRDA:279,21,1,0
BRDA:279,22,0,0
BRDA:279,22,1,0
BRDA:286,23,0,0
BRDA:286,23,1,0
BRDA:286,24,0,0
BRDA:286,24,1,0
BRDA:286,24,2,0
BRDA:291,25,0,0
BRDA:291,25,1,0
BRDA:293,26,0,0
BRDA:293,26,1,0
BRDA:302,27,0,0
BRDA:302,27,1,0
BRF:55
BRH:0
end_of_record
TN:
SF:lib\runtime-env.ts
FN:14,getRuntimeEnv
FNF:1
FNH:0
FNDA:0,getRuntimeEnv
DA:16,0
DA:18,0
DA:22,0
DA:31,0
LF:4
LH:0
BRDA:16,0,0,0
BRDA:16,0,1,0
BRDA:16,1,0,0
BRDA:16,1,1,0
BRF:4
BRH:0
end_of_record
TN:
SF:lib\type-utils.ts
FN:65,validateUser
FN:74,validateClient
FN:83,validateRenewal
FN:93,isUserArray
FN:97,isClientArray
FN:101,isRenewalArray
FN:106,isNullableUser
FN:110,isNullableClient
FN:114,isNullableTenantContext
FN:119,isSuccessResponse
FN:134,isErrorResponse
FN:139,assertUser
FN:146,assertClient
FN:153,assertTenantContext
FN:160,assertAuthSession
FN:168,safeGetProperty
FN:175,safeGetNestedProperty
FN:198,hasProperty
FN:205,hasStringProperty
FN:212,hasNumberProperty
FN:219,hasBooleanProperty
FN:227,isValidStatus
FN:231,isValidRenewalStatus
FN:235,isValidTheme
FN:240,isValidDate
FN:244,isValidDateString
FN:251,isValidEmail
FN:258,isValidUUID
FN:265,isNonEmptyArray
FN:269,isStringArray
FN:270,(anonymous_30)
FN:273,isNumberArray
FN:274,(anonymous_32)
FN:278,isPlainObject
FN:286,hasRequiredKeys
FN:291,(anonymous_35)
FN:295,isError
FN:299,isErrorWithCode
FN:303,isErrorWithStatus
FN:308,assertNever
FN:313,safeJsonParse
FNF:41
FNH:0
FNDA:0,validateUser
FNDA:0,validateClient
FNDA:0,validateRenewal
FNDA:0,isUserArray
FNDA:0,isClientArray
FNDA:0,isRenewalArray
FNDA:0,isNullableUser
FNDA:0,isNullableClient
FNDA:0,isNullableTenantContext
FNDA:0,isSuccessResponse
FNDA:0,isErrorResponse
FNDA:0,assertUser
FNDA:0,assertClient
FNDA:0,assertTenantContext
FNDA:0,assertAuthSession
FNDA:0,safeGetProperty
FNDA:0,safeGetNestedProperty
FNDA:0,hasProperty
FNDA:0,hasStringProperty
FNDA:0,hasNumberProperty
FNDA:0,hasBooleanProperty
FNDA:0,isValidStatus
FNDA:0,isValidRenewalStatus
FNDA:0,isValidTheme
FNDA:0,isValidDate
FNDA:0,isValidDateString
FNDA:0,isValidEmail
FNDA:0,isValidUUID
FNDA:0,isNonEmptyArray
FNDA:0,isStringArray
FNDA:0,(anonymous_30)
FNDA:0,isNumberArray
FNDA:0,(anonymous_32)
FNDA:0,isPlainObject
FNDA:0,hasRequiredKeys
FNDA:0,(anonymous_35)
FNDA:0,isError
FNDA:0,isErrorWithCode
FNDA:0,isErrorWithStatus
FNDA:0,assertNever
FNDA:0,safeJsonParse
DA:27,0
DA:40,0
DA:51,0
DA:66,0
DA:67,0
DA:68,0
DA:70,0
DA:75,0
DA:76,0
DA:77,0
DA:79,0
DA:84,0
DA:85,0
DA:86,0
DA:88,0
DA:94,0
DA:98,0
DA:102,0
DA:107,0
DA:111,0
DA:115,0
DA:123,0
DA:124,0
DA:127,0
DA:128,0
DA:131,0
DA:135,0
DA:140,0
DA:141,0
DA:143,0
DA:147,0
DA:148,0
DA:150,0
DA:154,0
DA:155,0
DA:157,0
DA:161,0
DA:162,0
DA:164,0
DA:172,0
DA:180,0
DA:181,0
DA:182,0
DA:184,0
DA:185,0
DA:186,0
DA:188,0
DA:191,0
DA:193,0
DA:202,0
DA:209,0
DA:216,0
DA:223,0
DA:228,0
DA:232,0
DA:236,0
DA:241,0
DA:245,0
DA:246,0
DA:247,0
DA:252,0
DA:253,0
DA:254,0
DA:259,0
DA:260,0
DA:261,0
DA:266,0
DA:270,0
DA:274,0
DA:279,0
DA:290,0
DA:291,0
DA:296,0
DA:300,0
DA:304,0
DA:309,0
DA:317,0
DA:318,0
DA:319,0
DA:320,0
DA:322,0
DA:324,0
LF:82
LH:0
BRDA:94,0,0,0
BRDA:94,0,1,0
BRDA:98,1,0,0
BRDA:98,1,1,0
BRDA:102,2,0,0
BRDA:102,2,1,0
BRDA:107,3,0,0
BRDA:107,3,1,0
BRDA:111,4,0,0
BRDA:111,4,1,0
BRDA:115,5,0,0
BRDA:115,5,1,0
BRDA:123,6,0,0
BRDA:123,6,1,0
BRDA:123,7,0,0
BRDA:123,7,1,0
BRDA:123,7,2,0
BRDA:127,8,0,0
BRDA:127,8,1,0
BRDA:135,9,0,0
BRDA:135,9,1,0
BRDA:135,9,2,0
BRDA:139,10,0,0
BRDA:140,11,0,0
BRDA:140,11,1,0
BRDA:146,12,0,0
BRDA:147,13,0,0
BRDA:147,13,1,0
BRDA:153,14,0,0
BRDA:154,15,0,0
BRDA:154,15,1,0
BRDA:160,16,0,0
BRDA:161,17,0,0
BRDA:161,17,1,0
BRDA:185,18,0,0
BRDA:185,18,1,0
BRDA:185,19,0,0
BRDA:185,19,1,0
BRDA:191,20,0,0
BRDA:191,20,1,0
BRDA:209,21,0,0
BRDA:209,21,1,0
BRDA:216,22,0,0
BRDA:216,22,1,0
BRDA:223,23,0,0
BRDA:223,23,1,0
BRDA:228,24,0,0
BRDA:228,24,1,0
BRDA:232,25,0,0
BRDA:232,25,1,0
BRDA:236,26,0,0
BRDA:236,26,1,0
BRDA:241,27,0,0
BRDA:241,27,1,0
BRDA:245,28,0,0
BRDA:245,28,1,0
BRDA:252,29,0,0
BRDA:252,29,1,0
BRDA:259,30,0,0
BRDA:259,30,1,0
BRDA:266,31,0,0
BRDA:266,31,1,0
BRDA:270,32,0,0
BRDA:270,32,1,0
BRDA:274,33,0,0
BRDA:274,33,1,0
BRDA:280,34,0,0
BRDA:280,34,1,0
BRDA:280,34,2,0
BRDA:290,35,0,0
BRDA:290,35,1,0
BRDA:300,36,0,0
BRDA:300,36,1,0
BRDA:300,36,2,0
BRDA:304,37,0,0
BRDA:304,37,1,0
BRDA:304,37,2,0
BRDA:319,38,0,0
BRDA:319,38,1,0
BRDA:319,39,0,0
BRDA:319,39,1,0
BRF:81
BRH:0
end_of_record
TN:
SF:lib\types.ts
FN:213,isUser
FN:224,isClient
FN:235,isTenantContext
FN:248,isRenewal
FN:259,isApiResponse
FN:268,isPaginatedResponse
FN:279,isAuthSession
FN:290,isCognitoJwtPayload
FNF:8
FNH:0
FNDA:0,isUser
FNDA:0,isClient
FNDA:0,isTenantContext
FNDA:0,isRenewal
FNDA:0,isApiResponse
FNDA:0,isPaginatedResponse
FNDA:0,isAuthSession
FNDA:0,isCognitoJwtPayload
DA:214,0
DA:225,0
DA:236,0
DA:249,0
DA:260,0
DA:269,0
DA:280,0
DA:291,0
LF:8
LH:0
BRDA:215,0,0,0
BRDA:215,0,1,0
BRDA:215,0,2,0
BRDA:215,0,3,0
BRDA:215,0,4,0
BRDA:215,0,5,0
BRDA:226,1,0,0
BRDA:226,1,1,0
BRDA:226,1,2,0
BRDA:226,1,3,0
BRDA:226,1,4,0
BRDA:226,1,5,0
BRDA:237,2,0,0
BRDA:237,2,1,0
BRDA:237,2,2,0
BRDA:237,2,3,0
BRDA:237,2,4,0
BRDA:237,2,5,0
BRDA:237,2,6,0
BRDA:237,2,7,0
BRDA:250,3,0,0
BRDA:250,3,1,0
BRDA:250,3,2,0
BRDA:250,3,3,0
BRDA:250,3,4,0
BRDA:250,3,5,0
BRDA:261,4,0,0
BRDA:261,4,1,0
BRDA:261,4,2,0
BRDA:261,4,3,0
BRDA:270,5,0,0
BRDA:270,5,1,0
BRDA:270,5,2,0
BRDA:270,5,3,0
BRDA:270,5,4,0
BRDA:270,5,5,0
BRDA:281,6,0,0
BRDA:281,6,1,0
BRDA:281,6,2,0
BRDA:281,6,3,0
BRDA:281,6,4,0
BRDA:281,6,5,0
BRDA:292,7,0,0
BRDA:292,7,1,0
BRDA:292,7,2,0
BRDA:292,7,3,0
BRDA:292,7,4,0
BRDA:292,7,5,0
BRDA:292,7,6,0
BRF:49
BRH:0
end_of_record
TN:
SF:lib\validation.ts
FN:112,validateRequestBody
FN:128,validateQueryParams
FN:160,validatePathParams
FN:182,sanitizeString
FN:186,sanitizeObject
FNF:5
FNH:0
FNDA:0,validateRequestBody
FNDA:0,validateQueryParams
FNDA:0,validatePathParams
FNDA:0,sanitizeString
FNDA:0,sanitizeObject
DA:13,0
DA:14,0
DA:15,0
DA:16,0
DA:19,0
DA:27,0
DA:34,0
DA:42,0
DA:50,0
DA:58,0
DA:69,0
DA:78,0
DA:88,0
DA:94,0
DA:103,0
DA:107,0
DA:116,0
DA:117,0
DA:118,0
DA:119,0
DA:121,0
DA:122,0
DA:124,0
DA:132,0
DA:134,0
DA:136,0
DA:138,0
DA:139,0
DA:140,0
DA:141,0
DA:142,0
DA:144,0
DA:146,0
DA:150,0
DA:151,0
DA:153,0
DA:154,0
DA:156,0
DA:164,0
DA:166,0
DA:167,0
DA:168,0
DA:171,0
DA:172,0
DA:174,0
DA:175,0
DA:177,0
DA:183,0
DA:187,0
DA:189,0
DA:190,0
DA:191,0
DA:192,0
DA:193,0
DA:195,0
DA:199,0
DA:203,0
DA:209,0
LF:58
LH:0
BRDA:121,0,0,0
BRDA:121,0,1,0
BRDA:138,1,0,0
BRDA:138,1,1,0
BRDA:140,2,0,0
BRDA:140,2,1,0
BRDA:142,3,0,0
BRDA:142,3,1,0
BRDA:142,4,0,0
BRDA:142,4,1,0
BRDA:153,5,0,0
BRDA:153,5,1,0
BRDA:168,6,0,0
BRDA:168,6,1,0
BRDA:174,7,0,0
BRDA:174,7,1,0
BRDA:190,8,0,0
BRDA:190,8,1,0
BRDA:192,9,0,0
BRDA:192,9,1,0
BRDA:192,10,0,0
BRDA:192,10,1,0
BRDA:192,10,2,0
BRF:23
BRH:0
end_of_record
