import { Amplify } from 'aws-amplify'
import { clientConfig } from './client-config'

// Amplify configuration using centralized configuration
export function configureAmplify() {
  // Only configure once
  if (Amplify.getConfig().Auth) {
    console.log('Amplify already configured, skipping...');
    return;
  }

  // Get configuration from centralized config
  const { aws, auth } = clientConfig;
  const config = {
    region: aws.region,
    userPoolId: aws.userPoolId,
    userPoolClientId: aws.userPoolClientId,
    cognitoDomain: aws.cognitoDomain,
    redirectSignIn: auth.redirectSignIn,
    redirectSignOut: auth.redirectSignOut
  };

  console.log('Configuring Amplify with:', {
    region: config.region,
    userPoolId: config.userPoolId,
    userPoolClientId: config.userPoolClientId,
    cognitoDomain: config.cognitoDomain,
    redirectSignIn: config.redirectSignIn,
    redirectSignOut: config.redirectSignOut
  });

  try {
    Amplify.configure({
      Auth: {
        Cognito: {
          userPoolId: config.userPoolId,
          userPoolClientId: config.userPoolClientId,
          loginWith: {
            oauth: {
              domain: config.cognitoDomain,
              scopes: ["email", "profile", "openid", "aws.cognito.signin.user.admin"],
              redirectSignIn: [config.redirectSignIn],
              redirectSignOut: [config.redirectSignOut],
              responseType: "code"
            }
          }
        }
      }
    }, {
      ssr: true
    });
    console.log('Amplify configured successfully with OAuth domain:', config.cognitoDomain);
  } catch (error) {
    console.error('Error configuring Amplify:', error);
    throw error;
  }
}

// Export configured instance for server-side operations
export function getAmplifySSR(_request: Request) {
  // For Amplify v6, we need to create a new instance with SSR context
  return {
    Auth: Amplify.getConfig().Auth
  }
}















