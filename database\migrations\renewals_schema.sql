-- Create renewals table for tracking software subscriptions and renewals
CREATE TABLE IF NOT EXISTS tenant_management.renewals (
    renewal_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID NOT NULL REFERENCES tenant_management.clients(client_id) ON DELETE CASCADE,
    name VA<PERSON>HAR(255) NOT NULL,
    vendor VARCHAR(255) NOT NULL,
    description TEXT,
    renewal_date DATE NOT NULL,
    cost DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    category VARCHAR(100),
    license_count INTEGER,
    auto_renewal BOOLEAN DEFAULT false,
    notification_days INTEGER DEFAULT 30,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_renewals_client_id ON tenant_management.renewals(client_id);
CREATE INDEX IF NOT EXISTS idx_renewals_renewal_date ON tenant_management.renewals(renewal_date);
CREATE INDEX IF NOT EXISTS idx_renewals_status ON tenant_management.renewals(status);

-- Create vendors table for tracking software vendors
CREATE TABLE IF NOT EXISTS tenant_management.vendors (
    vendor_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE,
    website VARCHAR(500),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on vendor name
CREATE INDEX IF NOT EXISTS idx_vendors_name ON tenant_management.vendors(name);

-- Add trigger to automatically update the updated_at timestamp for renewals
CREATE TRIGGER update_renewals_timestamp
BEFORE UPDATE ON tenant_management.renewals
FOR EACH ROW
EXECUTE FUNCTION tenant_management.update_timestamp();

-- Add trigger to automatically update the updated_at timestamp for vendors
CREATE TRIGGER update_vendors_timestamp
BEFORE UPDATE ON tenant_management.vendors
FOR EACH ROW
EXECUTE FUNCTION tenant_management.update_timestamp();

-- Sample vendor data
INSERT INTO tenant_management.vendors (name, website, contact_email)
VALUES 
    ('Vox Audio', 'https://voxaudio.com', '<EMAIL>'),
    ('Vox Tools', 'https://voxtools.com', '<EMAIL>'),
    ('TopCar', 'https://topcar.com', '<EMAIL>'),
    ('Cubix', 'https://cubix.com', '<EMAIL>'),
    ('Microsoft', 'https://microsoft.com', '<EMAIL>'),
    ('Adobe', 'https://adobe.com', '<EMAIL>'),
    ('Salesforce', 'https://salesforce.com', '<EMAIL>'),
    ('Slack', 'https://slack.com', '<EMAIL>'),
    ('Zoom', 'https://zoom.us', '<EMAIL>')
ON CONFLICT (name) DO NOTHING;

-- Sample renewal data (will be inserted for existing clients)
-- This assumes we have at least one client in the system
DO $$
DECLARE
    sample_client_id UUID;
BEGIN
    -- Get the first active client
    SELECT client_id INTO sample_client_id 
    FROM tenant_management.clients 
    WHERE status = 'active' 
    LIMIT 1;
    
    -- Only insert sample data if we have a client
    IF sample_client_id IS NOT NULL THEN
        INSERT INTO tenant_management.renewals (
            client_id, name, vendor, description, renewal_date, cost, currency, status, category, license_count, auto_renewal
        )
        VALUES 
            (sample_client_id, 'Unlimited Renewal', 'Vox Audio', 'Audio processing software license', '2025-12-31', 2400.00, 'USD', 'active', 'Software', 10, true),
            (sample_client_id, 'Core Pro', 'Vox Audio', 'Professional audio editing suite', '2025-06-30', 1800.00, 'USD', 'active', 'Software', 5, true),
            (sample_client_id, 'Test Software', 'Vox Tools', 'Testing and QA automation tools', '2025-09-15', 3600.00, 'USD', 'active', 'Development', 20, false),
            (sample_client_id, 'Mobile app', 'TopCar', 'Mobile application development platform', '2025-03-20', 1200.00, 'USD', 'active', 'Development', 3, true),
            (sample_client_id, 'ASAUDB', 'Cubix', 'Database management system', '2025-11-10', 4800.00, 'USD', 'active', 'Database', 1, true),
            (sample_client_id, 'Office 365', 'Microsoft', 'Productivity suite', '2025-01-15', 15000.00, 'USD', 'active', 'Productivity', 100, true),
            (sample_client_id, 'Creative Cloud', 'Adobe', 'Design and creative software suite', '2025-08-30', 7200.00, 'USD', 'active', 'Design', 25, true),
            (sample_client_id, 'Salesforce CRM', 'Salesforce', 'Customer relationship management', '2025-05-12', 12000.00, 'USD', 'active', 'CRM', 50, true),
            (sample_client_id, 'Slack Business+', 'Slack', 'Team communication platform', '2025-07-08', 3000.00, 'USD', 'active', 'Communication', 75, true),
            (sample_client_id, 'Zoom Pro', 'Zoom', 'Video conferencing solution', '2025-04-25', 1800.00, 'USD', 'active', 'Communication', 30, true),
            (sample_client_id, 'GitHub Enterprise', 'GitHub', 'Code repository and collaboration', '2025-10-18', 2400.00, 'USD', 'active', 'Development', 15, true),
            (sample_client_id, 'Jira Software', 'Atlassian', 'Project management and issue tracking', '2025-02-28', 1500.00, 'USD', 'active', 'Project Management', 40, true),
            (sample_client_id, 'Figma Professional', 'Figma', 'Design and prototyping tool', '2025-06-15', 1440.00, 'USD', 'active', 'Design', 12, true),
            (sample_client_id, 'AWS Enterprise Support', 'Amazon Web Services', 'Cloud infrastructure support', '2025-12-01', 24000.00, 'USD', 'active', 'Infrastructure', 1, true)
        ON CONFLICT DO NOTHING;
    END IF;
END $$;
