a8dd429e59666bd8e91da3551e5cb14f
/* istanbul ignore next */
function cov_1j8gdkw6xy() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\dal.ts";
  var hash = "1cbfd8028efb91c6e73d1bf1f05b6c58bc2d3626";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\dal.ts",
    statementMap: {
      "0": {
        start: {
          line: 7,
          column: 29
        },
        end: {
          line: 73,
          column: 2
        }
      },
      "1": {
        start: {
          line: 13,
          column: 2
        },
        end: {
          line: 72,
          column: 3
        }
      },
      "2": {
        start: {
          line: 14,
          column: 24
        },
        end: {
          line: 14,
          column: 39
        }
      },
      "3": {
        start: {
          line: 15,
          column: 33
        },
        end: {
          line: 15,
          column: 49
        }
      },
      "4": {
        start: {
          line: 18,
          column: 33
        },
        end: {
          line: 22,
          column: 5
        }
      },
      "5": {
        start: {
          line: 24,
          column: 35
        },
        end: {
          line: 24,
          column: 39
        }
      },
      "6": {
        start: {
          line: 27,
          column: 4
        },
        end: {
          line: 34,
          column: 5
        }
      },
      "7": {
        start: {
          line: 28,
          column: 21
        },
        end: {
          line: 28,
          column: 48
        }
      },
      "8": {
        start: {
          line: 29,
          column: 6
        },
        end: {
          line: 33,
          column: 7
        }
      },
      "9": {
        start: {
          line: 30,
          column: 8
        },
        end: {
          line: 30,
          column: 33
        }
      },
      "10": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 65
        }
      },
      "11": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 14
        }
      },
      "12": {
        start: {
          line: 37,
          column: 4
        },
        end: {
          line: 47,
          column: 5
        }
      },
      "13": {
        start: {
          line: 38,
          column: 25
        },
        end: {
          line: 38,
          column: 45
        }
      },
      "14": {
        start: {
          line: 40,
          column: 6
        },
        end: {
          line: 46,
          column: 7
        }
      },
      "15": {
        start: {
          line: 41,
          column: 8
        },
        end: {
          line: 45,
          column: 9
        }
      },
      "16": {
        start: {
          line: 42,
          column: 10
        },
        end: {
          line: 42,
          column: 35
        }
      },
      "17": {
        start: {
          line: 43,
          column: 10
        },
        end: {
          line: 43,
          column: 73
        }
      },
      "18": {
        start: {
          line: 44,
          column: 10
        },
        end: {
          line: 44,
          column: 16
        }
      },
      "19": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 52,
          column: 5
        }
      },
      "20": {
        start: {
          line: 50,
          column: 6
        },
        end: {
          line: 50,
          column: 62
        }
      },
      "21": {
        start: {
          line: 51,
          column: 6
        },
        end: {
          line: 51,
          column: 18
        }
      },
      "22": {
        start: {
          line: 55,
          column: 20
        },
        end: {
          line: 55,
          column: 55
        }
      },
      "23": {
        start: {
          line: 56,
          column: 4
        },
        end: {
          line: 59,
          column: 5
        }
      },
      "24": {
        start: {
          line: 57,
          column: 6
        },
        end: {
          line: 57,
          column: 43
        }
      },
      "25": {
        start: {
          line: 58,
          column: 6
        },
        end: {
          line: 58,
          column: 18
        }
      },
      "26": {
        start: {
          line: 61,
          column: 4
        },
        end: {
          line: 61,
          column: 74
        }
      },
      "27": {
        start: {
          line: 63,
          column: 4
        },
        end: {
          line: 68,
          column: 5
        }
      },
      "28": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 70,
          column: 52
        }
      },
      "29": {
        start: {
          line: 71,
          column: 4
        },
        end: {
          line: 71,
          column: 15
        }
      },
      "30": {
        start: {
          line: 75,
          column: 23
        },
        end: {
          line: 82,
          column: 2
        }
      },
      "31": {
        start: {
          line: 76,
          column: 18
        },
        end: {
          line: 76,
          column: 39
        }
      },
      "32": {
        start: {
          line: 77,
          column: 2
        },
        end: {
          line: 77,
          column: 27
        }
      },
      "33": {
        start: {
          line: 77,
          column: 16
        },
        end: {
          line: 77,
          column: 27
        }
      },
      "34": {
        start: {
          line: 81,
          column: 2
        },
        end: {
          line: 81,
          column: 31
        }
      },
      "35": {
        start: {
          line: 85,
          column: 23
        },
        end: {
          line: 88,
          column: 1
        }
      },
      "36": {
        start: {
          line: 86,
          column: 2
        },
        end: {
          line: 86,
          column: 46
        }
      },
      "37": {
        start: {
          line: 86,
          column: 34
        },
        end: {
          line: 86,
          column: 46
        }
      },
      "38": {
        start: {
          line: 87,
          column: 2
        },
        end: {
          line: 87,
          column: 45
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 7,
            column: 35
          },
          end: {
            line: 7,
            column: 36
          }
        },
        loc: {
          start: {
            line: 12,
            column: 13
          },
          end: {
            line: 73,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 75,
            column: 29
          },
          end: {
            line: 75,
            column: 30
          }
        },
        loc: {
          start: {
            line: 75,
            column: 41
          },
          end: {
            line: 82,
            column: 1
          }
        },
        line: 75
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 85,
            column: 23
          },
          end: {
            line: 85,
            column: 24
          }
        },
        loc: {
          start: {
            line: 85,
            column: 63
          },
          end: {
            line: 88,
            column: 1
          }
        },
        line: 85
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 29,
            column: 6
          },
          end: {
            line: 33,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 6
          },
          end: {
            line: 33,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "1": {
        loc: {
          start: {
            line: 37,
            column: 4
          },
          end: {
            line: 47,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 4
          },
          end: {
            line: 47,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "2": {
        loc: {
          start: {
            line: 41,
            column: 8
          },
          end: {
            line: 45,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 8
          },
          end: {
            line: 45,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "3": {
        loc: {
          start: {
            line: 41,
            column: 12
          },
          end: {
            line: 41,
            column: 101
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 41,
            column: 12
          },
          end: {
            line: 41,
            column: 66
          }
        }, {
          start: {
            line: 41,
            column: 70
          },
          end: {
            line: 41,
            column: 101
          }
        }],
        line: 41
      },
      "4": {
        loc: {
          start: {
            line: 49,
            column: 4
          },
          end: {
            line: 52,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 49,
            column: 4
          },
          end: {
            line: 52,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 49
      },
      "5": {
        loc: {
          start: {
            line: 56,
            column: 4
          },
          end: {
            line: 59,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 56,
            column: 4
          },
          end: {
            line: 59,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 56
      },
      "6": {
        loc: {
          start: {
            line: 67,
            column: 13
          },
          end: {
            line: 67,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 67,
            column: 13
          },
          end: {
            line: 67,
            column: 38
          }
        }, {
          start: {
            line: 67,
            column: 42
          },
          end: {
            line: 67,
            column: 44
          }
        }],
        line: 67
      },
      "7": {
        loc: {
          start: {
            line: 77,
            column: 2
          },
          end: {
            line: 77,
            column: 27
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 2
          },
          end: {
            line: 77,
            column: 27
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 77
      },
      "8": {
        loc: {
          start: {
            line: 86,
            column: 2
          },
          end: {
            line: 86,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 86,
            column: 2
          },
          end: {
            line: 86,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 86
      },
      "9": {
        loc: {
          start: {
            line: 86,
            column: 6
          },
          end: {
            line: 86,
            column: 32
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 86,
            column: 6
          },
          end: {
            line: 86,
            column: 14
          }
        }, {
          start: {
            line: 86,
            column: 18
          },
          end: {
            line: 86,
            column: 32
          }
        }],
        line: 86
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1cbfd8028efb91c6e73d1bf1f05b6c58bc2d3626"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1j8gdkw6xy = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1j8gdkw6xy();
import 'server-only';
import { cache } from 'react';
import { cookies } from 'next/headers';
import { validateAuthCookie } from './jwt-validator';
import { publicConfig } from './config';
export const verifySession =
/* istanbul ignore next */
(cov_1j8gdkw6xy().s[0]++, cache(async () => {
  /* istanbul ignore next */
  cov_1j8gdkw6xy().f[0]++;
  cov_1j8gdkw6xy().s[1]++;
  try {
    const cookieStore =
    /* istanbul ignore next */
    (cov_1j8gdkw6xy().s[2]++, await cookies());
    const {
      userPoolClientId
    } =
    /* istanbul ignore next */
    (cov_1j8gdkw6xy().s[3]++, publicConfig.aws);

    // Try to get authentication tokens from cookies
    const possibleTokenCookies =
    /* istanbul ignore next */
    (cov_1j8gdkw6xy().s[4]++, ['idToken', `CognitoIdentityServiceProvider.${userPoolClientId}.LastAuthUser`, 'amplify-signin-with-hostedUI']);
    let authToken =
    /* istanbul ignore next */
    (cov_1j8gdkw6xy().s[5]++, null);

    // Check for various Amplify cookie patterns
    /* istanbul ignore next */
    cov_1j8gdkw6xy().s[6]++;
    for (const cookieName of possibleTokenCookies) {
      const cookie =
      /* istanbul ignore next */
      (cov_1j8gdkw6xy().s[7]++, cookieStore.get(cookieName));
      /* istanbul ignore next */
      cov_1j8gdkw6xy().s[8]++;
      if (cookie?.value) {
        /* istanbul ignore next */
        cov_1j8gdkw6xy().b[0][0]++;
        cov_1j8gdkw6xy().s[9]++;
        authToken = cookie.value;
        /* istanbul ignore next */
        cov_1j8gdkw6xy().s[10]++;
        console.log(`Found auth token in cookie: ${cookieName}`);
        /* istanbul ignore next */
        cov_1j8gdkw6xy().s[11]++;
        break;
      } else
      /* istanbul ignore next */
      {
        cov_1j8gdkw6xy().b[0][1]++;
      }
    }

    // Also check for Cognito-specific cookies
    /* istanbul ignore next */
    cov_1j8gdkw6xy().s[12]++;
    if (!authToken) {
      /* istanbul ignore next */
      cov_1j8gdkw6xy().b[1][0]++;
      const allCookies =
      /* istanbul ignore next */
      (cov_1j8gdkw6xy().s[13]++, cookieStore.getAll());
      /* istanbul ignore next */
      cov_1j8gdkw6xy().s[14]++;
      for (const cookie of allCookies) {
        /* istanbul ignore next */
        cov_1j8gdkw6xy().s[15]++;
        if (
        /* istanbul ignore next */
        (cov_1j8gdkw6xy().b[3][0]++, cookie.name.includes('CognitoIdentityServiceProvider')) &&
        /* istanbul ignore next */
        (cov_1j8gdkw6xy().b[3][1]++, cookie.name.includes('idToken'))) {
          /* istanbul ignore next */
          cov_1j8gdkw6xy().b[2][0]++;
          cov_1j8gdkw6xy().s[16]++;
          authToken = cookie.value;
          /* istanbul ignore next */
          cov_1j8gdkw6xy().s[17]++;
          console.log(`Found Cognito idToken in cookie: ${cookie.name}`);
          /* istanbul ignore next */
          cov_1j8gdkw6xy().s[18]++;
          break;
        } else
        /* istanbul ignore next */
        {
          cov_1j8gdkw6xy().b[2][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_1j8gdkw6xy().b[1][1]++;
    }
    cov_1j8gdkw6xy().s[19]++;
    if (!authToken) {
      /* istanbul ignore next */
      cov_1j8gdkw6xy().b[4][0]++;
      cov_1j8gdkw6xy().s[20]++;
      console.log('No authentication token found in cookies');
      /* istanbul ignore next */
      cov_1j8gdkw6xy().s[21]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_1j8gdkw6xy().b[4][1]++;
    }

    // Use secure JWT validation
    const payload =
    /* istanbul ignore next */
    (cov_1j8gdkw6xy().s[22]++, await validateAuthCookie(authToken));
    /* istanbul ignore next */
    cov_1j8gdkw6xy().s[23]++;
    if (!payload) {
      /* istanbul ignore next */
      cov_1j8gdkw6xy().b[5][0]++;
      cov_1j8gdkw6xy().s[24]++;
      console.log('JWT validation failed');
      /* istanbul ignore next */
      cov_1j8gdkw6xy().s[25]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_1j8gdkw6xy().b[5][1]++;
    }
    cov_1j8gdkw6xy().s[26]++;
    console.log('Successfully verified session for user:', payload.email);
    /* istanbul ignore next */
    cov_1j8gdkw6xy().s[27]++;
    return {
      isAuth: true,
      userId: payload.sub,
      email: payload.email,
      roles:
      /* istanbul ignore next */
      (cov_1j8gdkw6xy().b[6][0]++, payload['cognito:groups']) ||
      /* istanbul ignore next */
      (cov_1j8gdkw6xy().b[6][1]++, [])
    };
  } catch (error) {
    /* istanbul ignore next */
    cov_1j8gdkw6xy().s[28]++;
    console.error('Auth verification error:', error);
    /* istanbul ignore next */
    cov_1j8gdkw6xy().s[29]++;
    return null;
  }
}));
export const getUser =
/* istanbul ignore next */
(cov_1j8gdkw6xy().s[30]++, cache(async () => {
  /* istanbul ignore next */
  cov_1j8gdkw6xy().f[1]++;
  const session =
  /* istanbul ignore next */
  (cov_1j8gdkw6xy().s[31]++, await verifySession());
  /* istanbul ignore next */
  cov_1j8gdkw6xy().s[32]++;
  if (!session) {
    /* istanbul ignore next */
    cov_1j8gdkw6xy().b[7][0]++;
    cov_1j8gdkw6xy().s[33]++;
    return null;
  } else
  /* istanbul ignore next */
  {
    cov_1j8gdkw6xy().b[7][1]++;
  }

  // Get user data from session
  // Add your user fetching logic here
  cov_1j8gdkw6xy().s[34]++;
  return {
    id: session.userId
  };
}));

// Role-based authorization helper
/* istanbul ignore next */
cov_1j8gdkw6xy().s[35]++;
export const hasRole = (session, requiredRole) => {
  /* istanbul ignore next */
  cov_1j8gdkw6xy().f[2]++;
  cov_1j8gdkw6xy().s[36]++;
  if (
  /* istanbul ignore next */
  (cov_1j8gdkw6xy().b[9][0]++, !session) ||
  /* istanbul ignore next */
  (cov_1j8gdkw6xy().b[9][1]++, !session.roles)) {
    /* istanbul ignore next */
    cov_1j8gdkw6xy().b[8][0]++;
    cov_1j8gdkw6xy().s[37]++;
    return false;
  } else
  /* istanbul ignore next */
  {
    cov_1j8gdkw6xy().b[8][1]++;
  }
  cov_1j8gdkw6xy().s[38]++;
  return session.roles.includes(requiredRole);
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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