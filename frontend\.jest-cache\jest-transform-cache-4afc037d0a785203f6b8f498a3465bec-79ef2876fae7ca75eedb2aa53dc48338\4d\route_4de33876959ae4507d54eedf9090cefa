11ab283de3bd38da69b6949d8a758e0c
/* istanbul ignore next */
function cov_2itgipuc8j() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\dashboard\\run-scan\\route.ts";
  var hash = "8943a468967276095a143e516cd9dd62ed10524f";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\dashboard\\run-scan\\route.ts",
    statementMap: {
      "0": {
        start: {
          line: 10,
          column: 20
        },
        end: {
          line: 38,
          column: 2
        }
      },
      "1": {
        start: {
          line: 12,
          column: 21
        },
        end: {
          line: 12,
          column: 40
        }
      },
      "2": {
        start: {
          line: 13,
          column: 2
        },
        end: {
          line: 15,
          column: 3
        }
      },
      "3": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 32
        }
      },
      "4": {
        start: {
          line: 17,
          column: 18
        },
        end: {
          line: 17,
          column: 37
        }
      },
      "5": {
        start: {
          line: 19,
          column: 2
        },
        end: {
          line: 37,
          column: 3
        }
      },
      "6": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 22,
          column: 60
        }
      },
      "7": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 28,
          column: 38
        }
      },
      "8": {
        start: {
          line: 31,
          column: 4
        },
        end: {
          line: 31,
          column: 48
        }
      },
      "9": {
        start: {
          line: 32,
          column: 4
        },
        end: {
          line: 36,
          column: 6
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 10,
            column: 38
          },
          end: {
            line: 10,
            column: 39
          }
        },
        loc: {
          start: {
            line: 10,
            column: 50
          },
          end: {
            line: 38,
            column: 1
          }
        },
        line: 10
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 13,
            column: 2
          },
          end: {
            line: 15,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 13,
            column: 2
          },
          end: {
            line: 15,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 13
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "8943a468967276095a143e516cd9dd62ed10524f"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2itgipuc8j = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2itgipuc8j();
import { requireAuth } from '@/lib/auth-middleware';
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus, withErrorHandling } from '@/lib/api-response';
export const POST =
/* istanbul ignore next */
(cov_2itgipuc8j().s[0]++, withErrorHandling(async () => {
  /* istanbul ignore next */
  cov_2itgipuc8j().f[0]++;
  // Verify authentication and get session
  const authResult =
  /* istanbul ignore next */
  (cov_2itgipuc8j().s[1]++, await requireAuth());
  /* istanbul ignore next */
  cov_2itgipuc8j().s[2]++;
  if (!authResult.success) {
    /* istanbul ignore next */
    cov_2itgipuc8j().b[0][0]++;
    cov_2itgipuc8j().s[3]++;
    return authResult.response;
  } else
  /* istanbul ignore next */
  {
    cov_2itgipuc8j().b[0][1]++;
  }
  const session =
  /* istanbul ignore next */
  (cov_2itgipuc8j().s[4]++, authResult.session);
  /* istanbul ignore next */
  cov_2itgipuc8j().s[5]++;
  try {
    /* istanbul ignore next */
    cov_2itgipuc8j().s[6]++;
    // TODO: Implement actual scan functionality
    // For now, just return success
    console.log(`Scan initiated by user: ${session.email}`);
    /* istanbul ignore next */
    cov_2itgipuc8j().s[7]++;
    return createSuccessResponse({
      scanId: `scan_${Date.now()}`,
      status: 'initiated',
      message: 'Scan started successfully'
    }, 'Scan initiated successfully');
  } catch (error) {
    /* istanbul ignore next */
    cov_2itgipuc8j().s[8]++;
    console.error('Error running scan:', error);
    /* istanbul ignore next */
    cov_2itgipuc8j().s[9]++;
    return createErrorResponse('Failed to run scan', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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