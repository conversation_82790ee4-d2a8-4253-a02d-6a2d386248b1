{"version": 3, "names": ["cov_23mv97lfr6", "actualCoverage", "execute<PERSON>uery", "executeQuerySingle", "schemaExists", "getTenantByDomain", "domain", "f", "query", "s", "result", "schema", "success", "b", "data", "getClientByDomain", "Error", "console", "error", "errorCode", "client", "settings", "JSON", "parse", "parseError", "warn", "getClientByEmailDomain", "email", "split", "length", "clientData", "tenantSchema", "tenant_id", "client_id", "schemaReady", "tenantContext", "clientId", "toString", "clientName", "client_name", "tenantId", "domains", "Array", "isArray", "isActive", "is_active", "createdAt", "Date", "created_at", "updatedAt", "updated_at", "message", "String", "createClient", "values", "name", "status", "stringify", "updateClient", "updates", "updateFields", "paramIndex", "undefined", "push", "join"], "sources": ["clients.ts"], "sourcesContent": ["import { executeQuery, executeQ<PERSON>y<PERSON><PERSON><PERSON>, schemaExists } from './database';\nimport { TenantContext } from './types';\n\n// Client lookup result interface\nexport interface ClientLookupResult {\n  success: boolean;\n  client?: TenantContext;\n  error?: string;\n  errorCode?: 'NOT_FOUND' | 'MULTIPLE_FOUND' | 'DATABASE_ERROR' | 'INVALID_INPUT';\n}\n\n// Get tenant by email domain\nexport async function getTenantByDomain(domain: string) {\n  const query = `\n    SELECT t.tenant_id, t.tenant_name, t.schema_name, t.subdomain, t.status, t.created_at\n    FROM tenant_management.tenants t\n    JOIN tenant_management.domains d ON t.tenant_id = d.tenant_id\n    WHERE d.domain_name = $1 AND t.status = 'active'\n    LIMIT 1\n  `;\n\n  const result = await executeQuerySingle(query, [domain], { schema: 'tenant_management' });\n  return result.success ? result.data : null;\n}\n\n// Get client by domain (replacing DynamoDB with PostgreSQL)\nexport async function getClientByDomain(domain: string) {\n  if (!domain) {\n    throw new Error('Domain parameter is required');\n  }\n\n  const query = `\n    SELECT\n      client_id as id,\n      name,\n      domain,\n      status,\n      settings,\n      created_at,\n      updated_at\n    FROM tenant_management.clients\n    WHERE domain = $1 AND status = 'active'\n    LIMIT 1\n  `;\n\n  const result = await executeQuerySingle(query, [domain], { schema: 'tenant_management' });\n\n  if (!result.success) {\n    console.error('Error fetching client by domain:', {\n      domain,\n      error: result.error,\n      errorCode: result.errorCode\n    });\n    throw new Error('Failed to retrieve client information');\n  }\n\n  if (!result.data) {\n    return null;\n  }\n\n  // Parse JSON settings if needed\n  const client = result.data;\n  if (client.settings && typeof client.settings === 'string') {\n    try {\n      client.settings = JSON.parse(client.settings);\n    } catch (parseError) {\n      console.warn('Failed to parse client settings JSON:', parseError);\n      client.settings = {};\n    }\n  }\n\n  return client;\n}\n\n// Get client by email domain with enhanced error handling\nexport async function getClientByEmailDomain(email: string): Promise<ClientLookupResult> {\n  if (!email || typeof email !== 'string') {\n    return {\n      success: false,\n      error: 'Invalid email provided',\n      errorCode: 'INVALID_INPUT'\n    };\n  }\n\n  try {\n    const domain = email.split('@')[1];\n    if (!domain) {\n      return {\n        success: false,\n        error: 'Invalid email format',\n        errorCode: 'INVALID_INPUT'\n      };\n    }\n\n    const query = `\n      SELECT\n        \"ClientID\" as client_id,\n        \"ClientName\" as client_name,\n        \"ClientDomain\" as domains,\n        \"TenantID\" as tenant_id,\n        \"Active\" as is_active,\n        \"IndustryID\" as industry_id,\n        \"CreatedOn\" as created_at,\n        \"ChangedOn\" as updated_at\n      FROM metadata.\"Clients\"\n      WHERE $1 = ANY(\"ClientDomain\") AND \"Active\" = true\n    `;\n\n    const result = await executeQuery(query, [domain], { schema: 'metadata' });\n\n    if (!result.success) {\n      return {\n        success: false,\n        error: 'An error occurred, contact support',\n        errorCode: 'DATABASE_ERROR'\n      };\n    }\n\n    if (!result.data || result.data.length === 0) {\n      return {\n        success: false,\n        error: 'A client was not found, contact support',\n        errorCode: 'NOT_FOUND'\n      };\n    }\n\n    if (result.data.length > 1) {\n      console.error('Multiple clients found for domain:', domain, 'Count:', result.data.length);\n      return {\n        success: false,\n        error: 'An error occurred, contact support',\n        errorCode: 'MULTIPLE_FOUND'\n      };\n    }\n\n    // Single client found - create tenant context\n    const clientData = result.data[0];\n\n    // Check if tenant schema exists\n    const tenantSchema = clientData.tenant_id || `tenant_${clientData.client_id}`;\n    const schemaReady = await schemaExists(tenantSchema);\n\n    const tenantContext: TenantContext = {\n      clientId: clientData.client_id.toString(),\n      clientName: clientData.client_name,\n      tenantId: clientData.tenant_id || `tenant_${clientData.client_id}`,\n      tenantSchema: tenantSchema,\n      domains: Array.isArray(clientData.domains) ? clientData.domains : [domain],\n      isActive: clientData.is_active,\n      settings: { schemaReady }, // Include schema readiness status\n      createdAt: new Date(clientData.created_at),\n      updatedAt: clientData.updated_at ? new Date(clientData.updated_at) : null\n    };\n\n    return {\n      success: true,\n      client: tenantContext\n    };\n\n  } catch (error) {\n    console.error('Database error fetching client by email domain:', {\n      email,\n      error: error instanceof Error ? error.message : String(error)\n    });\n    return {\n      success: false,\n      error: 'An error occurred, contact support',\n      errorCode: 'DATABASE_ERROR'\n    };\n  }\n}\n\n// Create a new client\nexport async function createClient(clientData: {\n  name: string;\n  domain: string;\n  status?: string;\n  settings?: Record<string, any>;\n}) {\n  const query = `\n    INSERT INTO tenant_management.clients (\n      name,\n      domain,\n      status,\n      settings\n    )\n    VALUES ($1, $2, $3, $4)\n    RETURNING\n      client_id as id,\n      name,\n      domain,\n      status,\n      settings,\n      created_at,\n      updated_at\n  `;\n\n  const values = [\n    clientData.name,\n    clientData.domain,\n    clientData.status || 'active',\n    clientData.settings ? JSON.stringify(clientData.settings) : '{}'\n  ];\n\n  const result = await executeQuerySingle(query, values, { schema: 'tenant_management' });\n\n  if (!result.success) {\n    console.error('Error creating client:', result.error);\n    throw new Error('Failed to create client');\n  }\n\n  return result.data;\n}\n\n// Update an existing client\nexport async function updateClient(\n  clientId: string,\n  updates: Partial<{\n    name: string;\n    domain: string;\n    status: string;\n    settings: Record<string, any>;\n  }>\n) {\n  // Build dynamic update query\n  const updateFields: string[] = [];\n  const values: any[] = [];\n  let paramIndex = 1;\n\n  if (updates.name !== undefined) {\n    updateFields.push(`name = $${paramIndex++}`);\n    values.push(updates.name);\n  }\n\n  if (updates.domain !== undefined) {\n    updateFields.push(`domain = $${paramIndex++}`);\n    values.push(updates.domain);\n  }\n\n  if (updates.status !== undefined) {\n    updateFields.push(`status = $${paramIndex++}`);\n    values.push(updates.status);\n  }\n\n  if (updates.settings !== undefined) {\n    updateFields.push(`settings = $${paramIndex++}`);\n    values.push(JSON.stringify(updates.settings));\n  }\n\n  if (updateFields.length === 0) {\n    return null; // Nothing to update\n  }\n\n  // Add client_id to values array\n  values.push(clientId);\n\n  const query = `\n    UPDATE tenant_management.clients\n    SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP\n    WHERE client_id = $${paramIndex}\n    RETURNING\n      client_id as id,\n      name,\n      domain,\n      status,\n      settings,\n      created_at,\n      updated_at\n  `;\n\n  const result = await executeQuerySingle(query, values, { schema: 'tenant_management' });\n\n  if (!result.success) {\n    console.error('Error updating client:', result.error);\n    throw new Error('Failed to update client');\n  }\n\n  return result.data;\n}\n\n\n\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ,SAASE,YAAY,EAAEC,kBAAkB,EAAEC,YAAY,QAAQ,YAAY;;AAG3E;;AAQA;AACA,OAAO,eAAeC,iBAAiBA,CAACC,MAAc,EAAE;EAAA;EAAAN,cAAA,GAAAO,CAAA;EACtD,MAAMC,KAAK;EAAA;EAAA,CAAAR,cAAA,GAAAS,CAAA,OAAG;AAChB;AACA;AACA;AACA;AACA;AACA,GAAG;EAED,MAAMC,MAAM;EAAA;EAAA,CAAAV,cAAA,GAAAS,CAAA,OAAG,MAAMN,kBAAkB,CAACK,KAAK,EAAE,CAACF,MAAM,CAAC,EAAE;IAAEK,MAAM,EAAE;EAAoB,CAAC,CAAC;EAAC;EAAAX,cAAA,GAAAS,CAAA;EAC1F,OAAOC,MAAM,CAACE,OAAO;EAAA;EAAA,CAAAZ,cAAA,GAAAa,CAAA,UAAGH,MAAM,CAACI,IAAI;EAAA;EAAA,CAAAd,cAAA,GAAAa,CAAA,UAAG,IAAI;AAC5C;;AAEA;AACA,OAAO,eAAeE,iBAAiBA,CAACT,MAAc,EAAE;EAAA;EAAAN,cAAA,GAAAO,CAAA;EAAAP,cAAA,GAAAS,CAAA;EACtD,IAAI,CAACH,MAAM,EAAE;IAAA;IAAAN,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAS,CAAA;IACX,MAAM,IAAIO,KAAK,CAAC,8BAA8B,CAAC;EACjD,CAAC;EAAA;EAAA;IAAAhB,cAAA,GAAAa,CAAA;EAAA;EAED,MAAML,KAAK;EAAA;EAAA,CAAAR,cAAA,GAAAS,CAAA,OAAG;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;EAED,MAAMC,MAAM;EAAA;EAAA,CAAAV,cAAA,GAAAS,CAAA,OAAG,MAAMN,kBAAkB,CAACK,KAAK,EAAE,CAACF,MAAM,CAAC,EAAE;IAAEK,MAAM,EAAE;EAAoB,CAAC,CAAC;EAAC;EAAAX,cAAA,GAAAS,CAAA;EAE1F,IAAI,CAACC,MAAM,CAACE,OAAO,EAAE;IAAA;IAAAZ,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAS,CAAA;IACnBQ,OAAO,CAACC,KAAK,CAAC,kCAAkC,EAAE;MAChDZ,MAAM;MACNY,KAAK,EAAER,MAAM,CAACQ,KAAK;MACnBC,SAAS,EAAET,MAAM,CAACS;IACpB,CAAC,CAAC;IAAC;IAAAnB,cAAA,GAAAS,CAAA;IACH,MAAM,IAAIO,KAAK,CAAC,uCAAuC,CAAC;EAC1D,CAAC;EAAA;EAAA;IAAAhB,cAAA,GAAAa,CAAA;EAAA;EAAAb,cAAA,GAAAS,CAAA;EAED,IAAI,CAACC,MAAM,CAACI,IAAI,EAAE;IAAA;IAAAd,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAS,CAAA;IAChB,OAAO,IAAI;EACb,CAAC;EAAA;EAAA;IAAAT,cAAA,GAAAa,CAAA;EAAA;;EAED;EACA,MAAMO,MAAM;EAAA;EAAA,CAAApB,cAAA,GAAAS,CAAA,QAAGC,MAAM,CAACI,IAAI;EAAC;EAAAd,cAAA,GAAAS,CAAA;EAC3B;EAAI;EAAA,CAAAT,cAAA,GAAAa,CAAA,UAAAO,MAAM,CAACC,QAAQ;EAAA;EAAA,CAAArB,cAAA,GAAAa,CAAA,UAAI,OAAOO,MAAM,CAACC,QAAQ,KAAK,QAAQ,GAAE;IAAA;IAAArB,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAS,CAAA;IAC1D,IAAI;MAAA;MAAAT,cAAA,GAAAS,CAAA;MACFW,MAAM,CAACC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,MAAM,CAACC,QAAQ,CAAC;IAC/C,CAAC,CAAC,OAAOG,UAAU,EAAE;MAAA;MAAAxB,cAAA,GAAAS,CAAA;MACnBQ,OAAO,CAACQ,IAAI,CAAC,uCAAuC,EAAED,UAAU,CAAC;MAAC;MAAAxB,cAAA,GAAAS,CAAA;MAClEW,MAAM,CAACC,QAAQ,GAAG,CAAC,CAAC;IACtB;EACF,CAAC;EAAA;EAAA;IAAArB,cAAA,GAAAa,CAAA;EAAA;EAAAb,cAAA,GAAAS,CAAA;EAED,OAAOW,MAAM;AACf;;AAEA;AACA,OAAO,eAAeM,sBAAsBA,CAACC,KAAa,EAA+B;EAAA;EAAA3B,cAAA,GAAAO,CAAA;EAAAP,cAAA,GAAAS,CAAA;EACvF;EAAI;EAAA,CAAAT,cAAA,GAAAa,CAAA,WAACc,KAAK;EAAA;EAAA,CAAA3B,cAAA,GAAAa,CAAA,UAAI,OAAOc,KAAK,KAAK,QAAQ,GAAE;IAAA;IAAA3B,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAS,CAAA;IACvC,OAAO;MACLG,OAAO,EAAE,KAAK;MACdM,KAAK,EAAE,wBAAwB;MAC/BC,SAAS,EAAE;IACb,CAAC;EACH,CAAC;EAAA;EAAA;IAAAnB,cAAA,GAAAa,CAAA;EAAA;EAAAb,cAAA,GAAAS,CAAA;EAED,IAAI;IACF,MAAMH,MAAM;IAAA;IAAA,CAAAN,cAAA,GAAAS,CAAA,QAAGkB,KAAK,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC;IAAA5B,cAAA,GAAAS,CAAA;IACnC,IAAI,CAACH,MAAM,EAAE;MAAA;MAAAN,cAAA,GAAAa,CAAA;MAAAb,cAAA,GAAAS,CAAA;MACX,OAAO;QACLG,OAAO,EAAE,KAAK;QACdM,KAAK,EAAE,sBAAsB;QAC7BC,SAAS,EAAE;MACb,CAAC;IACH,CAAC;IAAA;IAAA;MAAAnB,cAAA,GAAAa,CAAA;IAAA;IAED,MAAML,KAAK;IAAA;IAAA,CAAAR,cAAA,GAAAS,CAAA,QAAG;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IAED,MAAMC,MAAM;IAAA;IAAA,CAAAV,cAAA,GAAAS,CAAA,QAAG,MAAMP,YAAY,CAACM,KAAK,EAAE,CAACF,MAAM,CAAC,EAAE;MAAEK,MAAM,EAAE;IAAW,CAAC,CAAC;IAAC;IAAAX,cAAA,GAAAS,CAAA;IAE3E,IAAI,CAACC,MAAM,CAACE,OAAO,EAAE;MAAA;MAAAZ,cAAA,GAAAa,CAAA;MAAAb,cAAA,GAAAS,CAAA;MACnB,OAAO;QACLG,OAAO,EAAE,KAAK;QACdM,KAAK,EAAE,oCAAoC;QAC3CC,SAAS,EAAE;MACb,CAAC;IACH,CAAC;IAAA;IAAA;MAAAnB,cAAA,GAAAa,CAAA;IAAA;IAAAb,cAAA,GAAAS,CAAA;IAED;IAAI;IAAA,CAAAT,cAAA,GAAAa,CAAA,YAACH,MAAM,CAACI,IAAI;IAAA;IAAA,CAAAd,cAAA,GAAAa,CAAA,WAAIH,MAAM,CAACI,IAAI,CAACe,MAAM,KAAK,CAAC,GAAE;MAAA;MAAA7B,cAAA,GAAAa,CAAA;MAAAb,cAAA,GAAAS,CAAA;MAC5C,OAAO;QACLG,OAAO,EAAE,KAAK;QACdM,KAAK,EAAE,yCAAyC;QAChDC,SAAS,EAAE;MACb,CAAC;IACH,CAAC;IAAA;IAAA;MAAAnB,cAAA,GAAAa,CAAA;IAAA;IAAAb,cAAA,GAAAS,CAAA;IAED,IAAIC,MAAM,CAACI,IAAI,CAACe,MAAM,GAAG,CAAC,EAAE;MAAA;MAAA7B,cAAA,GAAAa,CAAA;MAAAb,cAAA,GAAAS,CAAA;MAC1BQ,OAAO,CAACC,KAAK,CAAC,oCAAoC,EAAEZ,MAAM,EAAE,QAAQ,EAAEI,MAAM,CAACI,IAAI,CAACe,MAAM,CAAC;MAAC;MAAA7B,cAAA,GAAAS,CAAA;MAC1F,OAAO;QACLG,OAAO,EAAE,KAAK;QACdM,KAAK,EAAE,oCAAoC;QAC3CC,SAAS,EAAE;MACb,CAAC;IACH,CAAC;IAAA;IAAA;MAAAnB,cAAA,GAAAa,CAAA;IAAA;;IAED;IACA,MAAMiB,UAAU;IAAA;IAAA,CAAA9B,cAAA,GAAAS,CAAA,QAAGC,MAAM,CAACI,IAAI,CAAC,CAAC,CAAC;;IAEjC;IACA,MAAMiB,YAAY;IAAA;IAAA,CAAA/B,cAAA,GAAAS,CAAA;IAAG;IAAA,CAAAT,cAAA,GAAAa,CAAA,WAAAiB,UAAU,CAACE,SAAS;IAAA;IAAA,CAAAhC,cAAA,GAAAa,CAAA,WAAI,UAAUiB,UAAU,CAACG,SAAS,EAAE;IAC7E,MAAMC,WAAW;IAAA;IAAA,CAAAlC,cAAA,GAAAS,CAAA,QAAG,MAAML,YAAY,CAAC2B,YAAY,CAAC;IAEpD,MAAMI,aAA4B;IAAA;IAAA,CAAAnC,cAAA,GAAAS,CAAA,QAAG;MACnC2B,QAAQ,EAAEN,UAAU,CAACG,SAAS,CAACI,QAAQ,CAAC,CAAC;MACzCC,UAAU,EAAER,UAAU,CAACS,WAAW;MAClCC,QAAQ;MAAE;MAAA,CAAAxC,cAAA,GAAAa,CAAA,WAAAiB,UAAU,CAACE,SAAS;MAAA;MAAA,CAAAhC,cAAA,GAAAa,CAAA,WAAI,UAAUiB,UAAU,CAACG,SAAS,EAAE;MAClEF,YAAY,EAAEA,YAAY;MAC1BU,OAAO,EAAEC,KAAK,CAACC,OAAO,CAACb,UAAU,CAACW,OAAO,CAAC;MAAA;MAAA,CAAAzC,cAAA,GAAAa,CAAA,WAAGiB,UAAU,CAACW,OAAO;MAAA;MAAA,CAAAzC,cAAA,GAAAa,CAAA,WAAG,CAACP,MAAM,CAAC;MAC1EsC,QAAQ,EAAEd,UAAU,CAACe,SAAS;MAC9BxB,QAAQ,EAAE;QAAEa;MAAY,CAAC;MAAE;MAC3BY,SAAS,EAAE,IAAIC,IAAI,CAACjB,UAAU,CAACkB,UAAU,CAAC;MAC1CC,SAAS,EAAEnB,UAAU,CAACoB,UAAU;MAAA;MAAA,CAAAlD,cAAA,GAAAa,CAAA,WAAG,IAAIkC,IAAI,CAACjB,UAAU,CAACoB,UAAU,CAAC;MAAA;MAAA,CAAAlD,cAAA,GAAAa,CAAA,WAAG,IAAI;IAC3E,CAAC;IAAC;IAAAb,cAAA,GAAAS,CAAA;IAEF,OAAO;MACLG,OAAO,EAAE,IAAI;MACbQ,MAAM,EAAEe;IACV,CAAC;EAEH,CAAC,CAAC,OAAOjB,KAAK,EAAE;IAAA;IAAAlB,cAAA,GAAAS,CAAA;IACdQ,OAAO,CAACC,KAAK,CAAC,iDAAiD,EAAE;MAC/DS,KAAK;MACLT,KAAK,EAAEA,KAAK,YAAYF,KAAK;MAAA;MAAA,CAAAhB,cAAA,GAAAa,CAAA,WAAGK,KAAK,CAACiC,OAAO;MAAA;MAAA,CAAAnD,cAAA,GAAAa,CAAA,WAAGuC,MAAM,CAAClC,KAAK,CAAC;IAC/D,CAAC,CAAC;IAAC;IAAAlB,cAAA,GAAAS,CAAA;IACH,OAAO;MACLG,OAAO,EAAE,KAAK;MACdM,KAAK,EAAE,oCAAoC;MAC3CC,SAAS,EAAE;IACb,CAAC;EACH;AACF;;AAEA;AACA,OAAO,eAAekC,YAAYA,CAACvB,UAKlC,EAAE;EAAA;EAAA9B,cAAA,GAAAO,CAAA;EACD,MAAMC,KAAK;EAAA;EAAA,CAAAR,cAAA,GAAAS,CAAA,QAAG;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;EAED,MAAM6C,MAAM;EAAA;EAAA,CAAAtD,cAAA,GAAAS,CAAA,QAAG,CACbqB,UAAU,CAACyB,IAAI,EACfzB,UAAU,CAACxB,MAAM;EACjB;EAAA,CAAAN,cAAA,GAAAa,CAAA,WAAAiB,UAAU,CAAC0B,MAAM;EAAA;EAAA,CAAAxD,cAAA,GAAAa,CAAA,WAAI,QAAQ,GAC7BiB,UAAU,CAACT,QAAQ;EAAA;EAAA,CAAArB,cAAA,GAAAa,CAAA,WAAGS,IAAI,CAACmC,SAAS,CAAC3B,UAAU,CAACT,QAAQ,CAAC;EAAA;EAAA,CAAArB,cAAA,GAAAa,CAAA,WAAG,IAAI,EACjE;EAED,MAAMH,MAAM;EAAA;EAAA,CAAAV,cAAA,GAAAS,CAAA,QAAG,MAAMN,kBAAkB,CAACK,KAAK,EAAE8C,MAAM,EAAE;IAAE3C,MAAM,EAAE;EAAoB,CAAC,CAAC;EAAC;EAAAX,cAAA,GAAAS,CAAA;EAExF,IAAI,CAACC,MAAM,CAACE,OAAO,EAAE;IAAA;IAAAZ,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAS,CAAA;IACnBQ,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAER,MAAM,CAACQ,KAAK,CAAC;IAAC;IAAAlB,cAAA,GAAAS,CAAA;IACtD,MAAM,IAAIO,KAAK,CAAC,yBAAyB,CAAC;EAC5C,CAAC;EAAA;EAAA;IAAAhB,cAAA,GAAAa,CAAA;EAAA;EAAAb,cAAA,GAAAS,CAAA;EAED,OAAOC,MAAM,CAACI,IAAI;AACpB;;AAEA;AACA,OAAO,eAAe4C,YAAYA,CAChCtB,QAAgB,EAChBuB,OAKE,EACF;EAAA;EAAA3D,cAAA,GAAAO,CAAA;EACA;EACA,MAAMqD,YAAsB;EAAA;EAAA,CAAA5D,cAAA,GAAAS,CAAA,QAAG,EAAE;EACjC,MAAM6C,MAAa;EAAA;EAAA,CAAAtD,cAAA,GAAAS,CAAA,QAAG,EAAE;EACxB,IAAIoD,UAAU;EAAA;EAAA,CAAA7D,cAAA,GAAAS,CAAA,QAAG,CAAC;EAAC;EAAAT,cAAA,GAAAS,CAAA;EAEnB,IAAIkD,OAAO,CAACJ,IAAI,KAAKO,SAAS,EAAE;IAAA;IAAA9D,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAS,CAAA;IAC9BmD,YAAY,CAACG,IAAI,CAAC,WAAWF,UAAU,EAAE,EAAE,CAAC;IAAC;IAAA7D,cAAA,GAAAS,CAAA;IAC7C6C,MAAM,CAACS,IAAI,CAACJ,OAAO,CAACJ,IAAI,CAAC;EAC3B,CAAC;EAAA;EAAA;IAAAvD,cAAA,GAAAa,CAAA;EAAA;EAAAb,cAAA,GAAAS,CAAA;EAED,IAAIkD,OAAO,CAACrD,MAAM,KAAKwD,SAAS,EAAE;IAAA;IAAA9D,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAS,CAAA;IAChCmD,YAAY,CAACG,IAAI,CAAC,aAAaF,UAAU,EAAE,EAAE,CAAC;IAAC;IAAA7D,cAAA,GAAAS,CAAA;IAC/C6C,MAAM,CAACS,IAAI,CAACJ,OAAO,CAACrD,MAAM,CAAC;EAC7B,CAAC;EAAA;EAAA;IAAAN,cAAA,GAAAa,CAAA;EAAA;EAAAb,cAAA,GAAAS,CAAA;EAED,IAAIkD,OAAO,CAACH,MAAM,KAAKM,SAAS,EAAE;IAAA;IAAA9D,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAS,CAAA;IAChCmD,YAAY,CAACG,IAAI,CAAC,aAAaF,UAAU,EAAE,EAAE,CAAC;IAAC;IAAA7D,cAAA,GAAAS,CAAA;IAC/C6C,MAAM,CAACS,IAAI,CAACJ,OAAO,CAACH,MAAM,CAAC;EAC7B,CAAC;EAAA;EAAA;IAAAxD,cAAA,GAAAa,CAAA;EAAA;EAAAb,cAAA,GAAAS,CAAA;EAED,IAAIkD,OAAO,CAACtC,QAAQ,KAAKyC,SAAS,EAAE;IAAA;IAAA9D,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAS,CAAA;IAClCmD,YAAY,CAACG,IAAI,CAAC,eAAeF,UAAU,EAAE,EAAE,CAAC;IAAC;IAAA7D,cAAA,GAAAS,CAAA;IACjD6C,MAAM,CAACS,IAAI,CAACzC,IAAI,CAACmC,SAAS,CAACE,OAAO,CAACtC,QAAQ,CAAC,CAAC;EAC/C,CAAC;EAAA;EAAA;IAAArB,cAAA,GAAAa,CAAA;EAAA;EAAAb,cAAA,GAAAS,CAAA;EAED,IAAImD,YAAY,CAAC/B,MAAM,KAAK,CAAC,EAAE;IAAA;IAAA7B,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAS,CAAA;IAC7B,OAAO,IAAI,CAAC,CAAC;EACf,CAAC;EAAA;EAAA;IAAAT,cAAA,GAAAa,CAAA;EAAA;;EAED;EAAAb,cAAA,GAAAS,CAAA;EACA6C,MAAM,CAACS,IAAI,CAAC3B,QAAQ,CAAC;EAErB,MAAM5B,KAAK;EAAA;EAAA,CAAAR,cAAA,GAAAS,CAAA,QAAG;AAChB;AACA,UAAUmD,YAAY,CAACI,IAAI,CAAC,IAAI,CAAC;AACjC,yBAAyBH,UAAU;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;EAED,MAAMnD,MAAM;EAAA;EAAA,CAAAV,cAAA,GAAAS,CAAA,QAAG,MAAMN,kBAAkB,CAACK,KAAK,EAAE8C,MAAM,EAAE;IAAE3C,MAAM,EAAE;EAAoB,CAAC,CAAC;EAAC;EAAAX,cAAA,GAAAS,CAAA;EAExF,IAAI,CAACC,MAAM,CAACE,OAAO,EAAE;IAAA;IAAAZ,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAS,CAAA;IACnBQ,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAER,MAAM,CAACQ,KAAK,CAAC;IAAC;IAAAlB,cAAA,GAAAS,CAAA;IACtD,MAAM,IAAIO,KAAK,CAAC,yBAAyB,CAAC;EAC5C,CAAC;EAAA;EAAA;IAAAhB,cAAA,GAAAa,CAAA;EAAA;EAAAb,cAAA,GAAAS,CAAA;EAED,OAAOC,MAAM,CAACI,IAAI;AACpB", "ignoreList": []}