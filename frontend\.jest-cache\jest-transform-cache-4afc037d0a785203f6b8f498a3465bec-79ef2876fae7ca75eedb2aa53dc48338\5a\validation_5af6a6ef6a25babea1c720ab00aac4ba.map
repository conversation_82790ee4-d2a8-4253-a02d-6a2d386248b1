{"version": 3, "names": ["cov_a3c688ugh", "actualCoverage", "z", "handleValidationError", "emailSchema", "s", "string", "email", "uuidSchema", "uuid", "positiveIntSchema", "number", "int", "positive", "nonEmptyStringSchema", "min", "paginationSchema", "object", "page", "default", "limit", "max", "sortBy", "optional", "sortOrder", "enum", "clientCreateSchema", "name", "domain", "status", "settings", "record", "any", "clientUpdateSchema", "userCreateSchema", "given_name", "family_name", "roles", "array", "userUpdateSchema", "userPreferencesSchema", "theme", "notifications", "boolean", "push", "sms", "displayDensity", "renewalCreateSchema", "vendor", "dueDate", "datetime", "annualCost", "description", "renewalUpdateSchema", "dashboardStatsQuerySchema", "startDate", "endDate", "includeInactive", "renewalsQuerySchema", "extend", "search", "dueBefore", "dueAfter", "idParamSchema", "id", "clientIdParamSchema", "clientId", "validateRequestBody", "request", "schema", "f", "body", "json", "validatedData", "parse", "success", "data", "error", "ZodError", "b", "response", "validateQueryParams", "searchParams", "params", "key", "value", "entries", "isNaN", "Number", "validatePathParams", "normalizedParams", "Object", "Array", "isArray", "sanitizeString", "input", "trim", "replace", "sanitizeObject", "obj", "sanitized", "rateLimitSchema", "maxRequests", "windowMs", "fileUploadSchema", "filename", "mimetype", "size"], "sources": ["validation.ts"], "sourcesContent": ["/**\n * Input Validation Schemas and Utilities\n * \n * This module provides comprehensive input validation using Zod schemas\n * for all API endpoints to ensure data integrity and security.\n */\n\nimport { z } from 'zod';\nimport { NextRequest } from 'next/server';\nimport { handleValidationError } from './api-response';\n\n// Common validation patterns\nconst emailSchema = z.string().email('Invalid email format');\nconst uuidSchema = z.string().uuid('Invalid UUID format');\nconst positiveIntSchema = z.number().int().positive('Must be a positive integer');\nconst nonEmptyStringSchema = z.string().min(1, 'Cannot be empty');\n\n// Pagination schema\nexport const paginationSchema = z.object({\n  page: z.number().int().min(1, 'Page must be at least 1').default(1),\n  limit: z.number().int().min(1, 'Limit must be at least 1').max(100, 'Limit cannot exceed 100').default(20),\n  sortBy: z.string().optional(),\n  sortOrder: z.enum(['asc', 'desc']).default('desc'),\n});\n\n// Client validation schemas\nexport const clientCreateSchema = z.object({\n  name: nonEmptyStringSchema.max(255, 'Name cannot exceed 255 characters'),\n  domain: z.string().min(1, 'Domain is required').max(255, 'Domain cannot exceed 255 characters'),\n  status: z.enum(['active', 'inactive', 'suspended']).default('active'),\n  settings: z.record(z.any()).optional(),\n});\n\nexport const clientUpdateSchema = z.object({\n  name: z.string().max(255, 'Name cannot exceed 255 characters').optional(),\n  domain: z.string().max(255, 'Domain cannot exceed 255 characters').optional(),\n  status: z.enum(['active', 'inactive', 'suspended']).optional(),\n  settings: z.record(z.any()).optional(),\n});\n\n// User validation schemas\nexport const userCreateSchema = z.object({\n  email: emailSchema,\n  name: z.string().max(255, 'Name cannot exceed 255 characters').optional(),\n  given_name: z.string().max(255, 'Given name cannot exceed 255 characters').optional(),\n  family_name: z.string().max(255, 'Family name cannot exceed 255 characters').optional(),\n  roles: z.array(z.string()).default([]),\n});\n\nexport const userUpdateSchema = z.object({\n  name: z.string().max(255, 'Name cannot exceed 255 characters').optional(),\n  given_name: z.string().max(255, 'Given name cannot exceed 255 characters').optional(),\n  family_name: z.string().max(255, 'Family name cannot exceed 255 characters').optional(),\n  roles: z.array(z.string()).optional(),\n});\n\n// User preferences validation\nexport const userPreferencesSchema = z.object({\n  theme: z.enum(['light', 'dark', 'system']).optional(),\n  notifications: z.object({\n    email: z.boolean().optional(),\n    push: z.boolean().optional(),\n    sms: z.boolean().optional(),\n  }).optional(),\n  displayDensity: z.enum(['comfortable', 'compact']).optional(),\n});\n\n// Renewal validation schemas\nexport const renewalCreateSchema = z.object({\n  name: nonEmptyStringSchema.max(255, 'Name cannot exceed 255 characters'),\n  vendor: nonEmptyStringSchema.max(255, 'Vendor cannot exceed 255 characters'),\n  status: z.enum(['active', 'inactive', 'pending', 'expired']).default('active'),\n  dueDate: z.string().datetime('Invalid date format').optional(),\n  annualCost: z.number().min(0, 'Annual cost cannot be negative').optional(),\n  description: z.string().max(1000, 'Description cannot exceed 1000 characters').optional(),\n});\n\nexport const renewalUpdateSchema = z.object({\n  name: z.string().max(255, 'Name cannot exceed 255 characters').optional(),\n  vendor: z.string().max(255, 'Vendor cannot exceed 255 characters').optional(),\n  status: z.enum(['active', 'inactive', 'pending', 'expired']).optional(),\n  dueDate: z.string().datetime('Invalid date format').optional(),\n  annualCost: z.number().min(0, 'Annual cost cannot be negative').optional(),\n  description: z.string().max(1000, 'Description cannot exceed 1000 characters').optional(),\n});\n\n// Query parameter validation\nexport const dashboardStatsQuerySchema = z.object({\n  startDate: z.string().datetime('Invalid start date format').optional(),\n  endDate: z.string().datetime('Invalid end date format').optional(),\n  includeInactive: z.boolean().default(false),\n});\n\nexport const renewalsQuerySchema = paginationSchema.extend({\n  status: z.enum(['active', 'inactive', 'pending', 'expired']).optional(),\n  vendor: z.string().optional(),\n  search: z.string().max(255, 'Search term cannot exceed 255 characters').optional(),\n  dueBefore: z.string().datetime('Invalid date format').optional(),\n  dueAfter: z.string().datetime('Invalid date format').optional(),\n});\n\n// ID parameter validation\nexport const idParamSchema = z.object({\n  id: uuidSchema,\n});\n\nexport const clientIdParamSchema = z.object({\n  clientId: z.string().min(1, 'Client ID is required'),\n});\n\n// Validation utility functions\nexport async function validateRequestBody<T>(\n  request: NextRequest,\n  schema: z.ZodSchema<T>\n): Promise<{ success: true; data: T } | { success: false; response: Response }> {\n  try {\n    const body = await request.json();\n    const validatedData = schema.parse(body);\n    return { success: true, data: validatedData };\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return { success: false, response: handleValidationError(error) };\n    }\n    throw error;\n  }\n}\n\nexport function validateQueryParams<T>(\n  searchParams: URLSearchParams,\n  schema: z.ZodSchema<T>\n): { success: true; data: T } | { success: false; response: Response } {\n  try {\n    // Convert URLSearchParams to object\n    const params: Record<string, any> = {};\n    \n    for (const [key, value] of searchParams.entries()) {\n      // Handle boolean conversion\n      if (value === 'true') {\n        params[key] = true;\n      } else if (value === 'false') {\n        params[key] = false;\n      } else if (!isNaN(Number(value)) && value !== '') {\n        // Handle number conversion\n        params[key] = Number(value);\n      } else {\n        params[key] = value;\n      }\n    }\n    \n    const validatedData = schema.parse(params);\n    return { success: true, data: validatedData };\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return { success: false, response: handleValidationError(error) };\n    }\n    throw error;\n  }\n}\n\nexport function validatePathParams<T>(\n  params: Record<string, string | string[]>,\n  schema: z.ZodSchema<T>\n): { success: true; data: T } | { success: false; response: Response } {\n  try {\n    // Convert array values to single strings (take first element)\n    const normalizedParams: Record<string, string> = {};\n    for (const [key, value] of Object.entries(params)) {\n      normalizedParams[key] = Array.isArray(value) ? value[0] : value;\n    }\n    \n    const validatedData = schema.parse(normalizedParams);\n    return { success: true, data: validatedData };\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return { success: false, response: handleValidationError(error) };\n    }\n    throw error;\n  }\n}\n\n// Sanitization utilities\nexport function sanitizeString(input: string): string {\n  return input.trim().replace(/[<>]/g, '');\n}\n\nexport function sanitizeObject(obj: Record<string, any>): Record<string, any> {\n  const sanitized: Record<string, any> = {};\n  \n  for (const [key, value] of Object.entries(obj)) {\n    if (typeof value === 'string') {\n      sanitized[key] = sanitizeString(value);\n    } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {\n      sanitized[key] = sanitizeObject(value);\n    } else {\n      sanitized[key] = value;\n    }\n  }\n  \n  return sanitized;\n}\n\n// Rate limiting validation\nexport const rateLimitSchema = z.object({\n  maxRequests: z.number().int().min(1).max(1000).default(100),\n  windowMs: z.number().int().min(1000).max(3600000).default(60000), // 1 second to 1 hour\n});\n\n// File upload validation\nexport const fileUploadSchema = z.object({\n  filename: z.string().min(1, 'Filename is required').max(255, 'Filename too long'),\n  mimetype: z.string().min(1, 'MIME type is required'),\n  size: z.number().int().min(1, 'File size must be positive').max(10485760, 'File size cannot exceed 10MB'), // 10MB limit\n});\n\n// Export types for TypeScript\nexport type PaginationParams = z.infer<typeof paginationSchema>;\nexport type ClientCreateData = z.infer<typeof clientCreateSchema>;\nexport type ClientUpdateData = z.infer<typeof clientUpdateSchema>;\nexport type UserCreateData = z.infer<typeof userCreateSchema>;\nexport type UserUpdateData = z.infer<typeof userUpdateSchema>;\nexport type UserPreferencesData = z.infer<typeof userPreferencesSchema>;\nexport type RenewalCreateData = z.infer<typeof renewalCreateSchema>;\nexport type RenewalUpdateData = z.infer<typeof renewalUpdateSchema>;\nexport type DashboardStatsQuery = z.infer<typeof dashboardStatsQuerySchema>;\nexport type RenewalsQuery = z.infer<typeof renewalsQuerySchema>;\nexport type IdParam = z.infer<typeof idParamSchema>;\nexport type ClientIdParam = z.infer<typeof clientIdParamSchema>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAfZ;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASE,CAAC,QAAQ,KAAK;AAEvB,SAASC,qBAAqB,QAAQ,gBAAgB;;AAEtD;AACA,MAAMC,WAAW;AAAA;AAAA,CAAAJ,aAAA,GAAAK,CAAA,OAAGH,CAAC,CAACI,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC,sBAAsB,CAAC;AAC5D,MAAMC,UAAU;AAAA;AAAA,CAAAR,aAAA,GAAAK,CAAA,OAAGH,CAAC,CAACI,MAAM,CAAC,CAAC,CAACG,IAAI,CAAC,qBAAqB,CAAC;AACzD,MAAMC,iBAAiB;AAAA;AAAA,CAAAV,aAAA,GAAAK,CAAA,OAAGH,CAAC,CAACS,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,4BAA4B,CAAC;AACjF,MAAMC,oBAAoB;AAAA;AAAA,CAAAd,aAAA,GAAAK,CAAA,OAAGH,CAAC,CAACI,MAAM,CAAC,CAAC,CAACS,GAAG,CAAC,CAAC,EAAE,iBAAiB,CAAC;;AAEjE;AACA,OAAO,MAAMC,gBAAgB;AAAA;AAAA,CAAAhB,aAAA,GAAAK,CAAA,OAAGH,CAAC,CAACe,MAAM,CAAC;EACvCC,IAAI,EAAEhB,CAAC,CAACS,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACG,GAAG,CAAC,CAAC,EAAE,yBAAyB,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC;EACnEC,KAAK,EAAElB,CAAC,CAACS,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACG,GAAG,CAAC,CAAC,EAAE,0BAA0B,CAAC,CAACM,GAAG,CAAC,GAAG,EAAE,yBAAyB,CAAC,CAACF,OAAO,CAAC,EAAE,CAAC;EAC1GG,MAAM,EAAEpB,CAAC,CAACI,MAAM,CAAC,CAAC,CAACiB,QAAQ,CAAC,CAAC;EAC7BC,SAAS,EAAEtB,CAAC,CAACuB,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAACN,OAAO,CAAC,MAAM;AACnD,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMO,kBAAkB;AAAA;AAAA,CAAA1B,aAAA,GAAAK,CAAA,OAAGH,CAAC,CAACe,MAAM,CAAC;EACzCU,IAAI,EAAEb,oBAAoB,CAACO,GAAG,CAAC,GAAG,EAAE,mCAAmC,CAAC;EACxEO,MAAM,EAAE1B,CAAC,CAACI,MAAM,CAAC,CAAC,CAACS,GAAG,CAAC,CAAC,EAAE,oBAAoB,CAAC,CAACM,GAAG,CAAC,GAAG,EAAE,qCAAqC,CAAC;EAC/FQ,MAAM,EAAE3B,CAAC,CAACuB,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAACN,OAAO,CAAC,QAAQ,CAAC;EACrEW,QAAQ,EAAE5B,CAAC,CAAC6B,MAAM,CAAC7B,CAAC,CAAC8B,GAAG,CAAC,CAAC,CAAC,CAACT,QAAQ,CAAC;AACvC,CAAC,CAAC;AAEF,OAAO,MAAMU,kBAAkB;AAAA;AAAA,CAAAjC,aAAA,GAAAK,CAAA,OAAGH,CAAC,CAACe,MAAM,CAAC;EACzCU,IAAI,EAAEzB,CAAC,CAACI,MAAM,CAAC,CAAC,CAACe,GAAG,CAAC,GAAG,EAAE,mCAAmC,CAAC,CAACE,QAAQ,CAAC,CAAC;EACzEK,MAAM,EAAE1B,CAAC,CAACI,MAAM,CAAC,CAAC,CAACe,GAAG,CAAC,GAAG,EAAE,qCAAqC,CAAC,CAACE,QAAQ,CAAC,CAAC;EAC7EM,MAAM,EAAE3B,CAAC,CAACuB,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC;EAC9DO,QAAQ,EAAE5B,CAAC,CAAC6B,MAAM,CAAC7B,CAAC,CAAC8B,GAAG,CAAC,CAAC,CAAC,CAACT,QAAQ,CAAC;AACvC,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMW,gBAAgB;AAAA;AAAA,CAAAlC,aAAA,GAAAK,CAAA,OAAGH,CAAC,CAACe,MAAM,CAAC;EACvCV,KAAK,EAAEH,WAAW;EAClBuB,IAAI,EAAEzB,CAAC,CAACI,MAAM,CAAC,CAAC,CAACe,GAAG,CAAC,GAAG,EAAE,mCAAmC,CAAC,CAACE,QAAQ,CAAC,CAAC;EACzEY,UAAU,EAAEjC,CAAC,CAACI,MAAM,CAAC,CAAC,CAACe,GAAG,CAAC,GAAG,EAAE,yCAAyC,CAAC,CAACE,QAAQ,CAAC,CAAC;EACrFa,WAAW,EAAElC,CAAC,CAACI,MAAM,CAAC,CAAC,CAACe,GAAG,CAAC,GAAG,EAAE,0CAA0C,CAAC,CAACE,QAAQ,CAAC,CAAC;EACvFc,KAAK,EAAEnC,CAAC,CAACoC,KAAK,CAACpC,CAAC,CAACI,MAAM,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,EAAE;AACvC,CAAC,CAAC;AAEF,OAAO,MAAMoB,gBAAgB;AAAA;AAAA,CAAAvC,aAAA,GAAAK,CAAA,OAAGH,CAAC,CAACe,MAAM,CAAC;EACvCU,IAAI,EAAEzB,CAAC,CAACI,MAAM,CAAC,CAAC,CAACe,GAAG,CAAC,GAAG,EAAE,mCAAmC,CAAC,CAACE,QAAQ,CAAC,CAAC;EACzEY,UAAU,EAAEjC,CAAC,CAACI,MAAM,CAAC,CAAC,CAACe,GAAG,CAAC,GAAG,EAAE,yCAAyC,CAAC,CAACE,QAAQ,CAAC,CAAC;EACrFa,WAAW,EAAElC,CAAC,CAACI,MAAM,CAAC,CAAC,CAACe,GAAG,CAAC,GAAG,EAAE,0CAA0C,CAAC,CAACE,QAAQ,CAAC,CAAC;EACvFc,KAAK,EAAEnC,CAAC,CAACoC,KAAK,CAACpC,CAAC,CAACI,MAAM,CAAC,CAAC,CAAC,CAACiB,QAAQ,CAAC;AACtC,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMiB,qBAAqB;AAAA;AAAA,CAAAxC,aAAA,GAAAK,CAAA,OAAGH,CAAC,CAACe,MAAM,CAAC;EAC5CwB,KAAK,EAAEvC,CAAC,CAACuB,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC;EACrDmB,aAAa,EAAExC,CAAC,CAACe,MAAM,CAAC;IACtBV,KAAK,EAAEL,CAAC,CAACyC,OAAO,CAAC,CAAC,CAACpB,QAAQ,CAAC,CAAC;IAC7BqB,IAAI,EAAE1C,CAAC,CAACyC,OAAO,CAAC,CAAC,CAACpB,QAAQ,CAAC,CAAC;IAC5BsB,GAAG,EAAE3C,CAAC,CAACyC,OAAO,CAAC,CAAC,CAACpB,QAAQ,CAAC;EAC5B,CAAC,CAAC,CAACA,QAAQ,CAAC,CAAC;EACbuB,cAAc,EAAE5C,CAAC,CAACuB,IAAI,CAAC,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,CAACF,QAAQ,CAAC;AAC9D,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMwB,mBAAmB;AAAA;AAAA,CAAA/C,aAAA,GAAAK,CAAA,QAAGH,CAAC,CAACe,MAAM,CAAC;EAC1CU,IAAI,EAAEb,oBAAoB,CAACO,GAAG,CAAC,GAAG,EAAE,mCAAmC,CAAC;EACxE2B,MAAM,EAAElC,oBAAoB,CAACO,GAAG,CAAC,GAAG,EAAE,qCAAqC,CAAC;EAC5EQ,MAAM,EAAE3B,CAAC,CAACuB,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAACN,OAAO,CAAC,QAAQ,CAAC;EAC9E8B,OAAO,EAAE/C,CAAC,CAACI,MAAM,CAAC,CAAC,CAAC4C,QAAQ,CAAC,qBAAqB,CAAC,CAAC3B,QAAQ,CAAC,CAAC;EAC9D4B,UAAU,EAAEjD,CAAC,CAACS,MAAM,CAAC,CAAC,CAACI,GAAG,CAAC,CAAC,EAAE,gCAAgC,CAAC,CAACQ,QAAQ,CAAC,CAAC;EAC1E6B,WAAW,EAAElD,CAAC,CAACI,MAAM,CAAC,CAAC,CAACe,GAAG,CAAC,IAAI,EAAE,2CAA2C,CAAC,CAACE,QAAQ,CAAC;AAC1F,CAAC,CAAC;AAEF,OAAO,MAAM8B,mBAAmB;AAAA;AAAA,CAAArD,aAAA,GAAAK,CAAA,QAAGH,CAAC,CAACe,MAAM,CAAC;EAC1CU,IAAI,EAAEzB,CAAC,CAACI,MAAM,CAAC,CAAC,CAACe,GAAG,CAAC,GAAG,EAAE,mCAAmC,CAAC,CAACE,QAAQ,CAAC,CAAC;EACzEyB,MAAM,EAAE9C,CAAC,CAACI,MAAM,CAAC,CAAC,CAACe,GAAG,CAAC,GAAG,EAAE,qCAAqC,CAAC,CAACE,QAAQ,CAAC,CAAC;EAC7EM,MAAM,EAAE3B,CAAC,CAACuB,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC;EACvE0B,OAAO,EAAE/C,CAAC,CAACI,MAAM,CAAC,CAAC,CAAC4C,QAAQ,CAAC,qBAAqB,CAAC,CAAC3B,QAAQ,CAAC,CAAC;EAC9D4B,UAAU,EAAEjD,CAAC,CAACS,MAAM,CAAC,CAAC,CAACI,GAAG,CAAC,CAAC,EAAE,gCAAgC,CAAC,CAACQ,QAAQ,CAAC,CAAC;EAC1E6B,WAAW,EAAElD,CAAC,CAACI,MAAM,CAAC,CAAC,CAACe,GAAG,CAAC,IAAI,EAAE,2CAA2C,CAAC,CAACE,QAAQ,CAAC;AAC1F,CAAC,CAAC;;AAEF;AACA,OAAO,MAAM+B,yBAAyB;AAAA;AAAA,CAAAtD,aAAA,GAAAK,CAAA,QAAGH,CAAC,CAACe,MAAM,CAAC;EAChDsC,SAAS,EAAErD,CAAC,CAACI,MAAM,CAAC,CAAC,CAAC4C,QAAQ,CAAC,2BAA2B,CAAC,CAAC3B,QAAQ,CAAC,CAAC;EACtEiC,OAAO,EAAEtD,CAAC,CAACI,MAAM,CAAC,CAAC,CAAC4C,QAAQ,CAAC,yBAAyB,CAAC,CAAC3B,QAAQ,CAAC,CAAC;EAClEkC,eAAe,EAAEvD,CAAC,CAACyC,OAAO,CAAC,CAAC,CAACxB,OAAO,CAAC,KAAK;AAC5C,CAAC,CAAC;AAEF,OAAO,MAAMuC,mBAAmB;AAAA;AAAA,CAAA1D,aAAA,GAAAK,CAAA,QAAGW,gBAAgB,CAAC2C,MAAM,CAAC;EACzD9B,MAAM,EAAE3B,CAAC,CAACuB,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC;EACvEyB,MAAM,EAAE9C,CAAC,CAACI,MAAM,CAAC,CAAC,CAACiB,QAAQ,CAAC,CAAC;EAC7BqC,MAAM,EAAE1D,CAAC,CAACI,MAAM,CAAC,CAAC,CAACe,GAAG,CAAC,GAAG,EAAE,0CAA0C,CAAC,CAACE,QAAQ,CAAC,CAAC;EAClFsC,SAAS,EAAE3D,CAAC,CAACI,MAAM,CAAC,CAAC,CAAC4C,QAAQ,CAAC,qBAAqB,CAAC,CAAC3B,QAAQ,CAAC,CAAC;EAChEuC,QAAQ,EAAE5D,CAAC,CAACI,MAAM,CAAC,CAAC,CAAC4C,QAAQ,CAAC,qBAAqB,CAAC,CAAC3B,QAAQ,CAAC;AAChE,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMwC,aAAa;AAAA;AAAA,CAAA/D,aAAA,GAAAK,CAAA,QAAGH,CAAC,CAACe,MAAM,CAAC;EACpC+C,EAAE,EAAExD;AACN,CAAC,CAAC;AAEF,OAAO,MAAMyD,mBAAmB;AAAA;AAAA,CAAAjE,aAAA,GAAAK,CAAA,QAAGH,CAAC,CAACe,MAAM,CAAC;EAC1CiD,QAAQ,EAAEhE,CAAC,CAACI,MAAM,CAAC,CAAC,CAACS,GAAG,CAAC,CAAC,EAAE,uBAAuB;AACrD,CAAC,CAAC;;AAEF;AACA,OAAO,eAAeoD,mBAAmBA,CACvCC,OAAoB,EACpBC,MAAsB,EACwD;EAAA;EAAArE,aAAA,GAAAsE,CAAA;EAAAtE,aAAA,GAAAK,CAAA;EAC9E,IAAI;IACF,MAAMkE,IAAI;IAAA;IAAA,CAAAvE,aAAA,GAAAK,CAAA,QAAG,MAAM+D,OAAO,CAACI,IAAI,CAAC,CAAC;IACjC,MAAMC,aAAa;IAAA;IAAA,CAAAzE,aAAA,GAAAK,CAAA,QAAGgE,MAAM,CAACK,KAAK,CAACH,IAAI,CAAC;IAAC;IAAAvE,aAAA,GAAAK,CAAA;IACzC,OAAO;MAAEsE,OAAO,EAAE,IAAI;MAAEC,IAAI,EAAEH;IAAc,CAAC;EAC/C,CAAC,CAAC,OAAOI,KAAK,EAAE;IAAA;IAAA7E,aAAA,GAAAK,CAAA;IACd,IAAIwE,KAAK,YAAY3E,CAAC,CAAC4E,QAAQ,EAAE;MAAA;MAAA9E,aAAA,GAAA+E,CAAA;MAAA/E,aAAA,GAAAK,CAAA;MAC/B,OAAO;QAAEsE,OAAO,EAAE,KAAK;QAAEK,QAAQ,EAAE7E,qBAAqB,CAAC0E,KAAK;MAAE,CAAC;IACnE,CAAC;IAAA;IAAA;MAAA7E,aAAA,GAAA+E,CAAA;IAAA;IAAA/E,aAAA,GAAAK,CAAA;IACD,MAAMwE,KAAK;EACb;AACF;AAEA,OAAO,SAASI,mBAAmBA,CACjCC,YAA6B,EAC7Bb,MAAsB,EAC+C;EAAA;EAAArE,aAAA,GAAAsE,CAAA;EAAAtE,aAAA,GAAAK,CAAA;EACrE,IAAI;IACF;IACA,MAAM8E,MAA2B;IAAA;IAAA,CAAAnF,aAAA,GAAAK,CAAA,QAAG,CAAC,CAAC;IAAC;IAAAL,aAAA,GAAAK,CAAA;IAEvC,KAAK,MAAM,CAAC+E,GAAG,EAAEC,KAAK,CAAC,IAAIH,YAAY,CAACI,OAAO,CAAC,CAAC,EAAE;MAAA;MAAAtF,aAAA,GAAAK,CAAA;MACjD;MACA,IAAIgF,KAAK,KAAK,MAAM,EAAE;QAAA;QAAArF,aAAA,GAAA+E,CAAA;QAAA/E,aAAA,GAAAK,CAAA;QACpB8E,MAAM,CAACC,GAAG,CAAC,GAAG,IAAI;MACpB,CAAC,MAAM;QAAA;QAAApF,aAAA,GAAA+E,CAAA;QAAA/E,aAAA,GAAAK,CAAA;QAAA,IAAIgF,KAAK,KAAK,OAAO,EAAE;UAAA;UAAArF,aAAA,GAAA+E,CAAA;UAAA/E,aAAA,GAAAK,CAAA;UAC5B8E,MAAM,CAACC,GAAG,CAAC,GAAG,KAAK;QACrB,CAAC,MAAM;UAAA;UAAApF,aAAA,GAAA+E,CAAA;UAAA/E,aAAA,GAAAK,CAAA;UAAA;UAAI;UAAA,CAAAL,aAAA,GAAA+E,CAAA,WAACQ,KAAK,CAACC,MAAM,CAACH,KAAK,CAAC,CAAC;UAAA;UAAA,CAAArF,aAAA,GAAA+E,CAAA,UAAIM,KAAK,KAAK,EAAE,GAAE;YAAA;YAAArF,aAAA,GAAA+E,CAAA;YAAA/E,aAAA,GAAAK,CAAA;YAChD;YACA8E,MAAM,CAACC,GAAG,CAAC,GAAGI,MAAM,CAACH,KAAK,CAAC;UAC7B,CAAC,MAAM;YAAA;YAAArF,aAAA,GAAA+E,CAAA;YAAA/E,aAAA,GAAAK,CAAA;YACL8E,MAAM,CAACC,GAAG,CAAC,GAAGC,KAAK;UACrB;QAAA;MAAA;IACF;IAEA,MAAMZ,aAAa;IAAA;IAAA,CAAAzE,aAAA,GAAAK,CAAA,QAAGgE,MAAM,CAACK,KAAK,CAACS,MAAM,CAAC;IAAC;IAAAnF,aAAA,GAAAK,CAAA;IAC3C,OAAO;MAAEsE,OAAO,EAAE,IAAI;MAAEC,IAAI,EAAEH;IAAc,CAAC;EAC/C,CAAC,CAAC,OAAOI,KAAK,EAAE;IAAA;IAAA7E,aAAA,GAAAK,CAAA;IACd,IAAIwE,KAAK,YAAY3E,CAAC,CAAC4E,QAAQ,EAAE;MAAA;MAAA9E,aAAA,GAAA+E,CAAA;MAAA/E,aAAA,GAAAK,CAAA;MAC/B,OAAO;QAAEsE,OAAO,EAAE,KAAK;QAAEK,QAAQ,EAAE7E,qBAAqB,CAAC0E,KAAK;MAAE,CAAC;IACnE,CAAC;IAAA;IAAA;MAAA7E,aAAA,GAAA+E,CAAA;IAAA;IAAA/E,aAAA,GAAAK,CAAA;IACD,MAAMwE,KAAK;EACb;AACF;AAEA,OAAO,SAASY,kBAAkBA,CAChCN,MAAyC,EACzCd,MAAsB,EAC+C;EAAA;EAAArE,aAAA,GAAAsE,CAAA;EAAAtE,aAAA,GAAAK,CAAA;EACrE,IAAI;IACF;IACA,MAAMqF,gBAAwC;IAAA;IAAA,CAAA1F,aAAA,GAAAK,CAAA,QAAG,CAAC,CAAC;IAAC;IAAAL,aAAA,GAAAK,CAAA;IACpD,KAAK,MAAM,CAAC+E,GAAG,EAAEC,KAAK,CAAC,IAAIM,MAAM,CAACL,OAAO,CAACH,MAAM,CAAC,EAAE;MAAA;MAAAnF,aAAA,GAAAK,CAAA;MACjDqF,gBAAgB,CAACN,GAAG,CAAC,GAAGQ,KAAK,CAACC,OAAO,CAACR,KAAK,CAAC;MAAA;MAAA,CAAArF,aAAA,GAAA+E,CAAA,UAAGM,KAAK,CAAC,CAAC,CAAC;MAAA;MAAA,CAAArF,aAAA,GAAA+E,CAAA,UAAGM,KAAK;IACjE;IAEA,MAAMZ,aAAa;IAAA;IAAA,CAAAzE,aAAA,GAAAK,CAAA,QAAGgE,MAAM,CAACK,KAAK,CAACgB,gBAAgB,CAAC;IAAC;IAAA1F,aAAA,GAAAK,CAAA;IACrD,OAAO;MAAEsE,OAAO,EAAE,IAAI;MAAEC,IAAI,EAAEH;IAAc,CAAC;EAC/C,CAAC,CAAC,OAAOI,KAAK,EAAE;IAAA;IAAA7E,aAAA,GAAAK,CAAA;IACd,IAAIwE,KAAK,YAAY3E,CAAC,CAAC4E,QAAQ,EAAE;MAAA;MAAA9E,aAAA,GAAA+E,CAAA;MAAA/E,aAAA,GAAAK,CAAA;MAC/B,OAAO;QAAEsE,OAAO,EAAE,KAAK;QAAEK,QAAQ,EAAE7E,qBAAqB,CAAC0E,KAAK;MAAE,CAAC;IACnE,CAAC;IAAA;IAAA;MAAA7E,aAAA,GAAA+E,CAAA;IAAA;IAAA/E,aAAA,GAAAK,CAAA;IACD,MAAMwE,KAAK;EACb;AACF;;AAEA;AACA,OAAO,SAASiB,cAAcA,CAACC,KAAa,EAAU;EAAA;EAAA/F,aAAA,GAAAsE,CAAA;EAAAtE,aAAA,GAAAK,CAAA;EACpD,OAAO0F,KAAK,CAACC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;AAC1C;AAEA,OAAO,SAASC,cAAcA,CAACC,GAAwB,EAAuB;EAAA;EAAAnG,aAAA,GAAAsE,CAAA;EAC5E,MAAM8B,SAA8B;EAAA;EAAA,CAAApG,aAAA,GAAAK,CAAA,QAAG,CAAC,CAAC;EAAC;EAAAL,aAAA,GAAAK,CAAA;EAE1C,KAAK,MAAM,CAAC+E,GAAG,EAAEC,KAAK,CAAC,IAAIM,MAAM,CAACL,OAAO,CAACa,GAAG,CAAC,EAAE;IAAA;IAAAnG,aAAA,GAAAK,CAAA;IAC9C,IAAI,OAAOgF,KAAK,KAAK,QAAQ,EAAE;MAAA;MAAArF,aAAA,GAAA+E,CAAA;MAAA/E,aAAA,GAAAK,CAAA;MAC7B+F,SAAS,CAAChB,GAAG,CAAC,GAAGU,cAAc,CAACT,KAAK,CAAC;IACxC,CAAC,MAAM;MAAA;MAAArF,aAAA,GAAA+E,CAAA;MAAA/E,aAAA,GAAAK,CAAA;MAAA;MAAI;MAAA,CAAAL,aAAA,GAAA+E,CAAA,kBAAOM,KAAK,KAAK,QAAQ;MAAA;MAAA,CAAArF,aAAA,GAAA+E,CAAA,WAAIM,KAAK,KAAK,IAAI;MAAA;MAAA,CAAArF,aAAA,GAAA+E,CAAA,WAAI,CAACa,KAAK,CAACC,OAAO,CAACR,KAAK,CAAC,GAAE;QAAA;QAAArF,aAAA,GAAA+E,CAAA;QAAA/E,aAAA,GAAAK,CAAA;QAC/E+F,SAAS,CAAChB,GAAG,CAAC,GAAGc,cAAc,CAACb,KAAK,CAAC;MACxC,CAAC,MAAM;QAAA;QAAArF,aAAA,GAAA+E,CAAA;QAAA/E,aAAA,GAAAK,CAAA;QACL+F,SAAS,CAAChB,GAAG,CAAC,GAAGC,KAAK;MACxB;IAAA;EACF;EAAC;EAAArF,aAAA,GAAAK,CAAA;EAED,OAAO+F,SAAS;AAClB;;AAEA;AACA,OAAO,MAAMC,eAAe;AAAA;AAAA,CAAArG,aAAA,GAAAK,CAAA,QAAGH,CAAC,CAACe,MAAM,CAAC;EACtCqF,WAAW,EAAEpG,CAAC,CAACS,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACG,GAAG,CAAC,CAAC,CAAC,CAACM,GAAG,CAAC,IAAI,CAAC,CAACF,OAAO,CAAC,GAAG,CAAC;EAC3DoF,QAAQ,EAAErG,CAAC,CAACS,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACG,GAAG,CAAC,IAAI,CAAC,CAACM,GAAG,CAAC,OAAO,CAAC,CAACF,OAAO,CAAC,KAAK,CAAC,CAAE;AACpE,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMqF,gBAAgB;AAAA;AAAA,CAAAxG,aAAA,GAAAK,CAAA,QAAGH,CAAC,CAACe,MAAM,CAAC;EACvCwF,QAAQ,EAAEvG,CAAC,CAACI,MAAM,CAAC,CAAC,CAACS,GAAG,CAAC,CAAC,EAAE,sBAAsB,CAAC,CAACM,GAAG,CAAC,GAAG,EAAE,mBAAmB,CAAC;EACjFqF,QAAQ,EAAExG,CAAC,CAACI,MAAM,CAAC,CAAC,CAACS,GAAG,CAAC,CAAC,EAAE,uBAAuB,CAAC;EACpD4F,IAAI,EAAEzG,CAAC,CAACS,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACG,GAAG,CAAC,CAAC,EAAE,4BAA4B,CAAC,CAACM,GAAG,CAAC,QAAQ,EAAE,8BAA8B,CAAC,CAAE;AAC7G,CAAC,CAAC;;AAEF", "ignoreList": []}