4d5eb2797f98ed29984cb6be1e628012
/* istanbul ignore next */
function cov_f7bzsmcq5() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\services\\renewalService.ts";
  var hash = "c514603888caf9f345ecb0d38a2ef372b6b18f40";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\services\\renewalService.ts",
    statementMap: {
      "0": {
        start: {
          line: 36,
          column: 2
        },
        end: {
          line: 79,
          column: 3
        }
      },
      "1": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 40,
          column: 61
        }
      },
      "2": {
        start: {
          line: 41,
          column: 4
        },
        end: {
          line: 41,
          column: 45
        }
      },
      "3": {
        start: {
          line: 42,
          column: 4
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "4": {
        start: {
          line: 45,
          column: 4
        },
        end: {
          line: 45,
          column: 59
        }
      },
      "5": {
        start: {
          line: 45,
          column: 33
        },
        end: {
          line: 45,
          column: 58
        }
      },
      "6": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 74,
          column: 5
        }
      },
      "7": {
        start: {
          line: 71,
          column: 45
        },
        end: {
          line: 71,
          column: 75
        }
      },
      "8": {
        start: {
          line: 77,
          column: 4
        },
        end: {
          line: 77,
          column: 49
        }
      },
      "9": {
        start: {
          line: 78,
          column: 4
        },
        end: {
          line: 78,
          column: 86
        }
      },
      "10": {
        start: {
          line: 95,
          column: 2
        },
        end: {
          line: 130,
          column: 3
        }
      },
      "11": {
        start: {
          line: 96,
          column: 4
        },
        end: {
          line: 96,
          column: 72
        }
      },
      "12": {
        start: {
          line: 97,
          column: 4
        },
        end: {
          line: 97,
          column: 44
        }
      },
      "13": {
        start: {
          line: 100,
          column: 4
        },
        end: {
          line: 100,
          column: 58
        }
      },
      "14": {
        start: {
          line: 100,
          column: 33
        },
        end: {
          line: 100,
          column: 57
        }
      },
      "15": {
        start: {
          line: 120,
          column: 4
        },
        end: {
          line: 125,
          column: 5
        }
      },
      "16": {
        start: {
          line: 128,
          column: 4
        },
        end: {
          line: 128,
          column: 51
        }
      },
      "17": {
        start: {
          line: 129,
          column: 4
        },
        end: {
          line: 129,
          column: 88
        }
      },
      "18": {
        start: {
          line: 144,
          column: 2
        },
        end: {
          line: 174,
          column: 3
        }
      },
      "19": {
        start: {
          line: 145,
          column: 4
        },
        end: {
          line: 145,
          column: 72
        }
      },
      "20": {
        start: {
          line: 148,
          column: 4
        },
        end: {
          line: 148,
          column: 58
        }
      },
      "21": {
        start: {
          line: 148,
          column: 33
        },
        end: {
          line: 148,
          column: 57
        }
      },
      "22": {
        start: {
          line: 166,
          column: 4
        },
        end: {
          line: 169,
          column: 5
        }
      },
      "23": {
        start: {
          line: 172,
          column: 4
        },
        end: {
          line: 172,
          column: 51
        }
      },
      "24": {
        start: {
          line: 173,
          column: 4
        },
        end: {
          line: 173,
          column: 88
        }
      },
      "25": {
        start: {
          line: 190,
          column: 2
        },
        end: {
          line: 224,
          column: 3
        }
      },
      "26": {
        start: {
          line: 191,
          column: 4
        },
        end: {
          line: 191,
          column: 77
        }
      },
      "27": {
        start: {
          line: 192,
          column: 4
        },
        end: {
          line: 192,
          column: 43
        }
      },
      "28": {
        start: {
          line: 195,
          column: 4
        },
        end: {
          line: 195,
          column: 58
        }
      },
      "29": {
        start: {
          line: 195,
          column: 33
        },
        end: {
          line: 195,
          column: 57
        }
      },
      "30": {
        start: {
          line: 215,
          column: 4
        },
        end: {
          line: 219,
          column: 5
        }
      },
      "31": {
        start: {
          line: 216,
          column: 45
        },
        end: {
          line: 216,
          column: 75
        }
      },
      "32": {
        start: {
          line: 222,
          column: 4
        },
        end: {
          line: 222,
          column: 48
        }
      },
      "33": {
        start: {
          line: 223,
          column: 4
        },
        end: {
          line: 223,
          column: 85
        }
      }
    },
    fnMap: {
      "0": {
        name: "saveRenewal",
        decl: {
          start: {
            line: 31,
            column: 22
          },
          end: {
            line: 31,
            column: 33
          }
        },
        loc: {
          start: {
            line: 35,
            column: 32
          },
          end: {
            line: 80,
            column: 1
          }
        },
        line: 35
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 45,
            column: 22
          },
          end: {
            line: 45,
            column: 23
          }
        },
        loc: {
          start: {
            line: 45,
            column: 33
          },
          end: {
            line: 45,
            column: 58
          }
        },
        line: 45
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 71,
            column: 31
          },
          end: {
            line: 71,
            column: 32
          }
        },
        loc: {
          start: {
            line: 71,
            column: 45
          },
          end: {
            line: 71,
            column: 75
          }
        },
        line: 71
      },
      "3": {
        name: "updateRenewal",
        decl: {
          start: {
            line: 90,
            column: 22
          },
          end: {
            line: 90,
            column: 35
          }
        },
        loc: {
          start: {
            line: 94,
            column: 32
          },
          end: {
            line: 131,
            column: 1
          }
        },
        line: 94
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 100,
            column: 22
          },
          end: {
            line: 100,
            column: 23
          }
        },
        loc: {
          start: {
            line: 100,
            column: 33
          },
          end: {
            line: 100,
            column: 57
          }
        },
        line: 100
      },
      "5": {
        name: "deleteRenewal",
        decl: {
          start: {
            line: 140,
            column: 22
          },
          end: {
            line: 140,
            column: 35
          }
        },
        loc: {
          start: {
            line: 143,
            column: 50
          },
          end: {
            line: 175,
            column: 1
          }
        },
        line: 143
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 148,
            column: 22
          },
          end: {
            line: 148,
            column: 23
          }
        },
        loc: {
          start: {
            line: 148,
            column: 33
          },
          end: {
            line: 148,
            column: 57
          }
        },
        line: 148
      },
      "7": {
        name: "saveAlerts",
        decl: {
          start: {
            line: 185,
            column: 22
          },
          end: {
            line: 185,
            column: 32
          }
        },
        loc: {
          start: {
            line: 189,
            column: 70
          },
          end: {
            line: 225,
            column: 1
          }
        },
        line: 189
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 195,
            column: 22
          },
          end: {
            line: 195,
            column: 23
          }
        },
        loc: {
          start: {
            line: 195,
            column: 33
          },
          end: {
            line: 195,
            column: 57
          }
        },
        line: 195
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 216,
            column: 31
          },
          end: {
            line: 216,
            column: 32
          }
        },
        loc: {
          start: {
            line: 216,
            column: 45
          },
          end: {
            line: 216,
            column: 75
          }
        },
        line: 216
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 78,
            column: 20
          },
          end: {
            line: 78,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 78,
            column: 45
          },
          end: {
            line: 78,
            column: 58
          }
        }, {
          start: {
            line: 78,
            column: 61
          },
          end: {
            line: 78,
            column: 85
          }
        }],
        line: 78
      },
      "1": {
        loc: {
          start: {
            line: 129,
            column: 20
          },
          end: {
            line: 129,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 129,
            column: 45
          },
          end: {
            line: 129,
            column: 58
          }
        }, {
          start: {
            line: 129,
            column: 61
          },
          end: {
            line: 129,
            column: 87
          }
        }],
        line: 129
      },
      "2": {
        loc: {
          start: {
            line: 173,
            column: 20
          },
          end: {
            line: 173,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 173,
            column: 45
          },
          end: {
            line: 173,
            column: 58
          }
        }, {
          start: {
            line: 173,
            column: 61
          },
          end: {
            line: 173,
            column: 87
          }
        }],
        line: 173
      },
      "3": {
        loc: {
          start: {
            line: 223,
            column: 20
          },
          end: {
            line: 223,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 223,
            column: 45
          },
          end: {
            line: 223,
            column: 58
          }
        }, {
          start: {
            line: 223,
            column: 61
          },
          end: {
            line: 223,
            column: 84
          }
        }],
        line: 223
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c514603888caf9f345ecb0d38a2ef372b6b18f40"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_f7bzsmcq5 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_f7bzsmcq5();
/**
 * Renewal Service
 * 
 * Handles API calls for renewal and alert management
 * Includes placeholder implementations for database operations
 */

/**
 * Save a new renewal and its associated alerts
 * 
 * @param tenantId - The tenant's client ID for schema routing
 * @param renewalData - The renewal information
 * @param alertsData - Array of alert configurations
 * @returns Promise with the save result
 */
export async function saveRenewal(tenantId, renewalData, alertsData) {
  /* istanbul ignore next */
  cov_f7bzsmcq5().f[0]++;
  cov_f7bzsmcq5().s[0]++;
  try {
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[1]++;
    // TODO: Replace with actual API call
    // This is a placeholder implementation

    console.log('Saving renewal to tenant schema:', tenantId);
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[2]++;
    console.log('Renewal data:', renewalData);
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[3]++;
    console.log('Alerts data:', alertsData);

    // Simulate API delay
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[4]++;
    await new Promise(resolve => {
      /* istanbul ignore next */
      cov_f7bzsmcq5().f[1]++;
      cov_f7bzsmcq5().s[5]++;
      return setTimeout(resolve, 1000);
    });

    // Placeholder for actual database operations:
    /*
    const response = await fetch('/api/renewals', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Tenant-ID': tenantId
      },
      body: JSON.stringify({
        renewal: renewalData,
        alerts: alertsData
      })
    })
    
    if (!response.ok) {
      throw new Error(`Failed to save renewal: ${response.statusText}`)
    }
    
    return await response.json()
    */

    // Mock successful response
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[6]++;
    return {
      renewalId: `renewal_${Date.now()}`,
      alertIds: alertsData.map((_, index) => {
        /* istanbul ignore next */
        cov_f7bzsmcq5().f[2]++;
        cov_f7bzsmcq5().s[7]++;
        return `alert_${Date.now()}_${index}`;
      }),
      success: true,
      message: 'Renewal and alerts saved successfully'
    };
  } catch (error) {
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[8]++;
    console.error('Error saving renewal:', error);
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[9]++;
    throw new Error(error instanceof Error ?
    /* istanbul ignore next */
    (cov_f7bzsmcq5().b[0][0]++, error.message) :
    /* istanbul ignore next */
    (cov_f7bzsmcq5().b[0][1]++, 'Failed to save renewal'));
  }
}

/**
 * Update an existing renewal
 * 
 * @param tenantId - The tenant's client ID
 * @param renewalId - The renewal ID to update
 * @param renewalData - Updated renewal data
 * @returns Promise with the update result
 */
export async function updateRenewal(tenantId, renewalId, renewalData) {
  /* istanbul ignore next */
  cov_f7bzsmcq5().f[3]++;
  cov_f7bzsmcq5().s[10]++;
  try {
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[11]++;
    console.log('Updating renewal:', renewalId, 'for tenant:', tenantId);
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[12]++;
    console.log('Update data:', renewalData);

    // Simulate API delay
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[13]++;
    await new Promise(resolve => {
      /* istanbul ignore next */
      cov_f7bzsmcq5().f[4]++;
      cov_f7bzsmcq5().s[14]++;
      return setTimeout(resolve, 800);
    });

    // TODO: Implement actual API call
    /*
    const response = await fetch(`/api/renewals/${renewalId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-Tenant-ID': tenantId
      },
      body: JSON.stringify(renewalData)
    })
    
    if (!response.ok) {
      throw new Error(`Failed to update renewal: ${response.statusText}`)
    }
    
    return await response.json()
    */
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[15]++;
    return {
      renewalId,
      alertIds: [],
      success: true,
      message: 'Renewal updated successfully'
    };
  } catch (error) {
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[16]++;
    console.error('Error updating renewal:', error);
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[17]++;
    throw new Error(error instanceof Error ?
    /* istanbul ignore next */
    (cov_f7bzsmcq5().b[1][0]++, error.message) :
    /* istanbul ignore next */
    (cov_f7bzsmcq5().b[1][1]++, 'Failed to update renewal'));
  }
}

/**
 * Delete a renewal and its associated alerts
 * 
 * @param tenantId - The tenant's client ID
 * @param renewalId - The renewal ID to delete
 * @returns Promise with the deletion result
 */
export async function deleteRenewal(tenantId, renewalId) {
  /* istanbul ignore next */
  cov_f7bzsmcq5().f[5]++;
  cov_f7bzsmcq5().s[18]++;
  try {
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[19]++;
    console.log('Deleting renewal:', renewalId, 'for tenant:', tenantId);

    // Simulate API delay
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[20]++;
    await new Promise(resolve => {
      /* istanbul ignore next */
      cov_f7bzsmcq5().f[6]++;
      cov_f7bzsmcq5().s[21]++;
      return setTimeout(resolve, 500);
    });

    // TODO: Implement actual API call
    /*
    const response = await fetch(`/api/renewals/${renewalId}`, {
      method: 'DELETE',
      headers: {
        'X-Tenant-ID': tenantId
      }
    })
    
    if (!response.ok) {
      throw new Error(`Failed to delete renewal: ${response.statusText}`)
    }
    
    return await response.json()
    */
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[22]++;
    return {
      success: true,
      message: 'Renewal deleted successfully'
    };
  } catch (error) {
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[23]++;
    console.error('Error deleting renewal:', error);
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[24]++;
    throw new Error(error instanceof Error ?
    /* istanbul ignore next */
    (cov_f7bzsmcq5().b[2][0]++, error.message) :
    /* istanbul ignore next */
    (cov_f7bzsmcq5().b[2][1]++, 'Failed to delete renewal'));
  }
}

/**
 * Save alerts for a renewal
 * 
 * @param tenantId - The tenant's client ID
 * @param renewalId - The renewal ID
 * @param alertsData - Array of alert configurations
 * @returns Promise with the save result
 */
export async function saveAlerts(tenantId, renewalId, alertsData) {
  /* istanbul ignore next */
  cov_f7bzsmcq5().f[7]++;
  cov_f7bzsmcq5().s[25]++;
  try {
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[26]++;
    console.log('Saving alerts for renewal:', renewalId, 'tenant:', tenantId);
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[27]++;
    console.log('Alerts data:', alertsData);

    // Simulate API delay
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[28]++;
    await new Promise(resolve => {
      /* istanbul ignore next */
      cov_f7bzsmcq5().f[8]++;
      cov_f7bzsmcq5().s[29]++;
      return setTimeout(resolve, 600);
    });

    // TODO: Implement actual API call
    /*
    const response = await fetch(`/api/renewals/${renewalId}/alerts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Tenant-ID': tenantId
      },
      body: JSON.stringify({ alerts: alertsData })
    })
    
    if (!response.ok) {
      throw new Error(`Failed to save alerts: ${response.statusText}`)
    }
    
    return await response.json()
    */
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[30]++;
    return {
      alertIds: alertsData.map((_, index) => {
        /* istanbul ignore next */
        cov_f7bzsmcq5().f[9]++;
        cov_f7bzsmcq5().s[31]++;
        return `alert_${Date.now()}_${index}`;
      }),
      success: true,
      message: 'Alerts saved successfully'
    };
  } catch (error) {
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[32]++;
    console.error('Error saving alerts:', error);
    /* istanbul ignore next */
    cov_f7bzsmcq5().s[33]++;
    throw new Error(error instanceof Error ?
    /* istanbul ignore next */
    (cov_f7bzsmcq5().b[3][0]++, error.message) :
    /* istanbul ignore next */
    (cov_f7bzsmcq5().b[3][1]++, 'Failed to save alerts'));
  }
}

/**
 * Database Schema Information
 * 
 * The following tables should exist in each tenant's schema:
 * 
 * Renewals Table:
 * - id (UUID, Primary Key)
 * - product_name (VARCHAR)
 * - version (VARCHAR)
 * - vendor (VARCHAR)
 * - type (VARCHAR)
 * - department (VARCHAR)
 * - purchase_type (VARCHAR)
 * - licensed_date (DATE)
 * - renewal_date (DATE)
 * - associated_emails (JSON/TEXT[])
 * - reseller (VARCHAR)
 * - currency (VARCHAR)
 * - cost (DECIMAL)
 * - cost_code (VARCHAR)
 * - license_count (INTEGER)
 * - description (TEXT)
 * - notes (TEXT)
 * - created_at (TIMESTAMP)
 * - updated_at (TIMESTAMP)
 * 
 * Alerts Table:
 * - id (UUID, Primary Key)
 * - renewal_id (UUID, Foreign Key to Renewals)
 * - days_before_renewal (INTEGER)
 * - email_recipients (JSON/TEXT[])
 * - custom_message (TEXT)
 * - enabled (BOOLEAN)
 * - created_at (TIMESTAMP)
 * - updated_at (TIMESTAMP)
 */
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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