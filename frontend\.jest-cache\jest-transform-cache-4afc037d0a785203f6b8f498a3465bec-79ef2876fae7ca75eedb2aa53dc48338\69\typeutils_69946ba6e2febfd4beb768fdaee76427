e2b7b252620c2f1dc51993c4b607f356
/* istanbul ignore next */
"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.assertAuthSession = assertAuthSession;
exports.assertClient = assertClient;
exports.assertNever = assertNever;
exports.assertTenantContext = assertTenantContext;
exports.assertUser = assertUser;
exports.clientSchema = void 0;
exports.hasBooleanProperty = hasBooleanProperty;
exports.hasNumberProperty = hasNumberProperty;
exports.hasProperty = hasProperty;
exports.hasRequiredKeys = hasRequiredKeys;
exports.hasStringProperty = hasStringProperty;
exports.isClientArray = isClientArray;
exports.isError = isError;
exports.isErrorResponse = isErrorResponse;
exports.isErrorWithCode = isErrorWithCode;
exports.isErrorWithStatus = isErrorWithStatus;
exports.isNonEmptyArray = isNonEmptyArray;
exports.isNullableClient = isNullableClient;
exports.isNullableTenantContext = isNullableTenantContext;
exports.isNullableUser = isNullableUser;
exports.isNumberArray = isNumberArray;
exports.isPlainObject = isPlainObject;
exports.isRenewalArray = isRenewalArray;
exports.isStringArray = isStringArray;
exports.isSuccessResponse = isSuccessResponse;
exports.isUserArray = isUserArray;
exports.isValidDate = isValidDate;
exports.isValidDateString = isValidDateString;
exports.isValidEmail = isValidEmail;
exports.isValidRenewalStatus = isValidRenewalStatus;
exports.isValidStatus = isValidStatus;
exports.isValidTheme = isValidTheme;
exports.isValidUUID = isValidUUID;
exports.renewalSchema = void 0;
exports.safeGetNestedProperty = safeGetNestedProperty;
exports.safeGetProperty = safeGetProperty;
exports.safeJsonParse = safeJsonParse;
exports.userSchema = void 0;
exports.validateClient = validateClient;
exports.validateRenewal = validateRenewal;
exports.validateUser = validateUser;
var
/* istanbul ignore next */
_zod = require("zod");
var
/* istanbul ignore next */
_types = require("./types");
/* istanbul ignore next */
function cov_1o0gle82dd() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\type-utils.ts";
  var hash = "ff1c9306b8fe233452b492d79c15b42794353627";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\type-utils.ts",
    statementMap: {
      "0": {
        start: {
          line: 27,
          column: 26
        },
        end: {
          line: 38,
          column: 2
        }
      },
      "1": {
        start: {
          line: 40,
          column: 28
        },
        end: {
          line: 49,
          column: 2
        }
      },
      "2": {
        start: {
          line: 51,
          column: 29
        },
        end: {
          line: 62,
          column: 2
        }
      },
      "3": {
        start: {
          line: 66,
          column: 2
        },
        end: {
          line: 71,
          column: 3
        }
      },
      "4": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 67,
          column: 26
        }
      },
      "5": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 68,
          column: 23
        }
      },
      "6": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 70,
          column: 17
        }
      },
      "7": {
        start: {
          line: 75,
          column: 2
        },
        end: {
          line: 80,
          column: 3
        }
      },
      "8": {
        start: {
          line: 76,
          column: 4
        },
        end: {
          line: 76,
          column: 28
        }
      },
      "9": {
        start: {
          line: 77,
          column: 4
        },
        end: {
          line: 77,
          column: 25
        }
      },
      "10": {
        start: {
          line: 79,
          column: 4
        },
        end: {
          line: 79,
          column: 17
        }
      },
      "11": {
        start: {
          line: 84,
          column: 2
        },
        end: {
          line: 89,
          column: 3
        }
      },
      "12": {
        start: {
          line: 85,
          column: 4
        },
        end: {
          line: 85,
          column: 29
        }
      },
      "13": {
        start: {
          line: 86,
          column: 4
        },
        end: {
          line: 86,
          column: 26
        }
      },
      "14": {
        start: {
          line: 88,
          column: 4
        },
        end: {
          line: 88,
          column: 17
        }
      },
      "15": {
        start: {
          line: 94,
          column: 2
        },
        end: {
          line: 94,
          column: 49
        }
      },
      "16": {
        start: {
          line: 98,
          column: 2
        },
        end: {
          line: 98,
          column: 51
        }
      },
      "17": {
        start: {
          line: 102,
          column: 2
        },
        end: {
          line: 102,
          column: 52
        }
      },
      "18": {
        start: {
          line: 107,
          column: 2
        },
        end: {
          line: 107,
          column: 37
        }
      },
      "19": {
        start: {
          line: 111,
          column: 2
        },
        end: {
          line: 111,
          column: 39
        }
      },
      "20": {
        start: {
          line: 115,
          column: 2
        },
        end: {
          line: 115,
          column: 46
        }
      },
      "21": {
        start: {
          line: 123,
          column: 2
        },
        end: {
          line: 125,
          column: 3
        }
      },
      "22": {
        start: {
          line: 124,
          column: 4
        },
        end: {
          line: 124,
          column: 17
        }
      },
      "23": {
        start: {
          line: 127,
          column: 2
        },
        end: {
          line: 129,
          column: 3
        }
      },
      "24": {
        start: {
          line: 128,
          column: 4
        },
        end: {
          line: 128,
          column: 35
        }
      },
      "25": {
        start: {
          line: 131,
          column: 2
        },
        end: {
          line: 131,
          column: 14
        }
      },
      "26": {
        start: {
          line: 135,
          column: 2
        },
        end: {
          line: 135,
          column: 77
        }
      },
      "27": {
        start: {
          line: 140,
          column: 2
        },
        end: {
          line: 142,
          column: 3
        }
      },
      "28": {
        start: {
          line: 141,
          column: 4
        },
        end: {
          line: 141,
          column: 82
        }
      },
      "29": {
        start: {
          line: 143,
          column: 2
        },
        end: {
          line: 143,
          column: 13
        }
      },
      "30": {
        start: {
          line: 147,
          column: 2
        },
        end: {
          line: 149,
          column: 3
        }
      },
      "31": {
        start: {
          line: 148,
          column: 4
        },
        end: {
          line: 148,
          column: 84
        }
      },
      "32": {
        start: {
          line: 150,
          column: 2
        },
        end: {
          line: 150,
          column: 13
        }
      },
      "33": {
        start: {
          line: 154,
          column: 2
        },
        end: {
          line: 156,
          column: 3
        }
      },
      "34": {
        start: {
          line: 155,
          column: 4
        },
        end: {
          line: 155,
          column: 91
        }
      },
      "35": {
        start: {
          line: 157,
          column: 2
        },
        end: {
          line: 157,
          column: 13
        }
      },
      "36": {
        start: {
          line: 161,
          column: 2
        },
        end: {
          line: 163,
          column: 3
        }
      },
      "37": {
        start: {
          line: 162,
          column: 4
        },
        end: {
          line: 162,
          column: 89
        }
      },
      "38": {
        start: {
          line: 164,
          column: 2
        },
        end: {
          line: 164,
          column: 13
        }
      },
      "39": {
        start: {
          line: 172,
          column: 2
        },
        end: {
          line: 172,
          column: 20
        }
      },
      "40": {
        start: {
          line: 180,
          column: 2
        },
        end: {
          line: 194,
          column: 3
        }
      },
      "41": {
        start: {
          line: 181,
          column: 17
        },
        end: {
          line: 181,
          column: 32
        }
      },
      "42": {
        start: {
          line: 182,
          column: 18
        },
        end: {
          line: 182,
          column: 21
        }
      },
      "43": {
        start: {
          line: 184,
          column: 4
        },
        end: {
          line: 189,
          column: 5
        }
      },
      "44": {
        start: {
          line: 185,
          column: 6
        },
        end: {
          line: 187,
          column: 7
        }
      },
      "45": {
        start: {
          line: 186,
          column: 8
        },
        end: {
          line: 186,
          column: 28
        }
      },
      "46": {
        start: {
          line: 188,
          column: 6
        },
        end: {
          line: 188,
          column: 29
        }
      },
      "47": {
        start: {
          line: 191,
          column: 4
        },
        end: {
          line: 191,
          column: 35
        }
      },
      "48": {
        start: {
          line: 193,
          column: 4
        },
        end: {
          line: 193,
          column: 24
        }
      },
      "49": {
        start: {
          line: 202,
          column: 2
        },
        end: {
          line: 202,
          column: 20
        }
      },
      "50": {
        start: {
          line: 209,
          column: 2
        },
        end: {
          line: 209,
          column: 61
        }
      },
      "51": {
        start: {
          line: 216,
          column: 2
        },
        end: {
          line: 216,
          column: 61
        }
      },
      "52": {
        start: {
          line: 223,
          column: 2
        },
        end: {
          line: 223,
          column: 62
        }
      },
      "53": {
        start: {
          line: 228,
          column: 2
        },
        end: {
          line: 228,
          column: 101
        }
      },
      "54": {
        start: {
          line: 232,
          column: 2
        },
        end: {
          line: 232,
          column: 99
        }
      },
      "55": {
        start: {
          line: 236,
          column: 2
        },
        end: {
          line: 236,
          column: 82
        }
      },
      "56": {
        start: {
          line: 241,
          column: 2
        },
        end: {
          line: 241,
          column: 58
        }
      },
      "57": {
        start: {
          line: 245,
          column: 2
        },
        end: {
          line: 245,
          column: 46
        }
      },
      "58": {
        start: {
          line: 245,
          column: 33
        },
        end: {
          line: 245,
          column: 46
        }
      },
      "59": {
        start: {
          line: 246,
          column: 15
        },
        end: {
          line: 246,
          column: 30
        }
      },
      "60": {
        start: {
          line: 247,
          column: 2
        },
        end: {
          line: 247,
          column: 27
        }
      },
      "61": {
        start: {
          line: 252,
          column: 2
        },
        end: {
          line: 252,
          column: 46
        }
      },
      "62": {
        start: {
          line: 252,
          column: 33
        },
        end: {
          line: 252,
          column: 46
        }
      },
      "63": {
        start: {
          line: 253,
          column: 21
        },
        end: {
          line: 253,
          column: 49
        }
      },
      "64": {
        start: {
          line: 254,
          column: 2
        },
        end: {
          line: 254,
          column: 32
        }
      },
      "65": {
        start: {
          line: 259,
          column: 2
        },
        end: {
          line: 259,
          column: 46
        }
      },
      "66": {
        start: {
          line: 259,
          column: 33
        },
        end: {
          line: 259,
          column: 46
        }
      },
      "67": {
        start: {
          line: 260,
          column: 20
        },
        end: {
          line: 260,
          column: 96
        }
      },
      "68": {
        start: {
          line: 261,
          column: 2
        },
        end: {
          line: 261,
          column: 31
        }
      },
      "69": {
        start: {
          line: 266,
          column: 2
        },
        end: {
          line: 266,
          column: 50
        }
      },
      "70": {
        start: {
          line: 270,
          column: 2
        },
        end: {
          line: 270,
          column: 79
        }
      },
      "71": {
        start: {
          line: 270,
          column: 53
        },
        end: {
          line: 270,
          column: 77
        }
      },
      "72": {
        start: {
          line: 274,
          column: 2
        },
        end: {
          line: 274,
          column: 79
        }
      },
      "73": {
        start: {
          line: 274,
          column: 53
        },
        end: {
          line: 274,
          column: 77
        }
      },
      "74": {
        start: {
          line: 279,
          column: 2
        },
        end: {
          line: 283,
          column: 4
        }
      },
      "75": {
        start: {
          line: 290,
          column: 2
        },
        end: {
          line: 290,
          column: 40
        }
      },
      "76": {
        start: {
          line: 290,
          column: 27
        },
        end: {
          line: 290,
          column: 40
        }
      },
      "77": {
        start: {
          line: 291,
          column: 2
        },
        end: {
          line: 291,
          column: 39
        }
      },
      "78": {
        start: {
          line: 291,
          column: 27
        },
        end: {
          line: 291,
          column: 37
        }
      },
      "79": {
        start: {
          line: 296,
          column: 2
        },
        end: {
          line: 296,
          column: 32
        }
      },
      "80": {
        start: {
          line: 300,
          column: 2
        },
        end: {
          line: 300,
          column: 77
        }
      },
      "81": {
        start: {
          line: 304,
          column: 2
        },
        end: {
          line: 304,
          column: 89
        }
      },
      "82": {
        start: {
          line: 309,
          column: 2
        },
        end: {
          line: 309,
          column: 48
        }
      },
      "83": {
        start: {
          line: 317,
          column: 2
        },
        end: {
          line: 325,
          column: 3
        }
      },
      "84": {
        start: {
          line: 318,
          column: 19
        },
        end: {
          line: 318,
          column: 35
        }
      },
      "85": {
        start: {
          line: 319,
          column: 4
        },
        end: {
          line: 321,
          column: 5
        }
      },
      "86": {
        start: {
          line: 320,
          column: 6
        },
        end: {
          line: 320,
          column: 18
        }
      },
      "87": {
        start: {
          line: 322,
          column: 4
        },
        end: {
          line: 322,
          column: 18
        }
      },
      "88": {
        start: {
          line: 324,
          column: 4
        },
        end: {
          line: 324,
          column: 16
        }
      }
    },
    fnMap: {
      "0": {
        name: "validateUser",
        decl: {
          start: {
            line: 65,
            column: 16
          },
          end: {
            line: 65,
            column: 28
          }
        },
        loc: {
          start: {
            line: 65,
            column: 56
          },
          end: {
            line: 72,
            column: 1
          }
        },
        line: 65
      },
      "1": {
        name: "validateClient",
        decl: {
          start: {
            line: 74,
            column: 16
          },
          end: {
            line: 74,
            column: 30
          }
        },
        loc: {
          start: {
            line: 74,
            column: 60
          },
          end: {
            line: 81,
            column: 1
          }
        },
        line: 74
      },
      "2": {
        name: "validateRenewal",
        decl: {
          start: {
            line: 83,
            column: 16
          },
          end: {
            line: 83,
            column: 31
          }
        },
        loc: {
          start: {
            line: 83,
            column: 62
          },
          end: {
            line: 90,
            column: 1
          }
        },
        line: 83
      },
      "3": {
        name: "isUserArray",
        decl: {
          start: {
            line: 93,
            column: 16
          },
          end: {
            line: 93,
            column: 27
          }
        },
        loc: {
          start: {
            line: 93,
            column: 57
          },
          end: {
            line: 95,
            column: 1
          }
        },
        line: 93
      },
      "4": {
        name: "isClientArray",
        decl: {
          start: {
            line: 97,
            column: 16
          },
          end: {
            line: 97,
            column: 29
          }
        },
        loc: {
          start: {
            line: 97,
            column: 61
          },
          end: {
            line: 99,
            column: 1
          }
        },
        line: 97
      },
      "5": {
        name: "isRenewalArray",
        decl: {
          start: {
            line: 101,
            column: 16
          },
          end: {
            line: 101,
            column: 30
          }
        },
        loc: {
          start: {
            line: 101,
            column: 63
          },
          end: {
            line: 103,
            column: 1
          }
        },
        line: 101
      },
      "6": {
        name: "isNullableUser",
        decl: {
          start: {
            line: 106,
            column: 16
          },
          end: {
            line: 106,
            column: 30
          }
        },
        loc: {
          start: {
            line: 106,
            column: 65
          },
          end: {
            line: 108,
            column: 1
          }
        },
        line: 106
      },
      "7": {
        name: "isNullableClient",
        decl: {
          start: {
            line: 110,
            column: 16
          },
          end: {
            line: 110,
            column: 32
          }
        },
        loc: {
          start: {
            line: 110,
            column: 69
          },
          end: {
            line: 112,
            column: 1
          }
        },
        line: 110
      },
      "8": {
        name: "isNullableTenantContext",
        decl: {
          start: {
            line: 114,
            column: 16
          },
          end: {
            line: 114,
            column: 39
          }
        },
        loc: {
          start: {
            line: 114,
            column: 83
          },
          end: {
            line: 116,
            column: 1
          }
        },
        line: 114
      },
      "9": {
        name: "isSuccessResponse",
        decl: {
          start: {
            line: 119,
            column: 16
          },
          end: {
            line: 119,
            column: 33
          }
        },
        loc: {
          start: {
            line: 122,
            column: 54
          },
          end: {
            line: 132,
            column: 1
          }
        },
        line: 122
      },
      "10": {
        name: "isErrorResponse",
        decl: {
          start: {
            line: 134,
            column: 16
          },
          end: {
            line: 134,
            column: 31
          }
        },
        loc: {
          start: {
            line: 134,
            column: 102
          },
          end: {
            line: 136,
            column: 1
          }
        },
        line: 134
      },
      "11": {
        name: "assertUser",
        decl: {
          start: {
            line: 139,
            column: 16
          },
          end: {
            line: 139,
            column: 26
          }
        },
        loc: {
          start: {
            line: 139,
            column: 68
          },
          end: {
            line: 144,
            column: 1
          }
        },
        line: 139
      },
      "12": {
        name: "assertClient",
        decl: {
          start: {
            line: 146,
            column: 16
          },
          end: {
            line: 146,
            column: 28
          }
        },
        loc: {
          start: {
            line: 146,
            column: 72
          },
          end: {
            line: 151,
            column: 1
          }
        },
        line: 146
      },
      "13": {
        name: "assertTenantContext",
        decl: {
          start: {
            line: 153,
            column: 16
          },
          end: {
            line: 153,
            column: 35
          }
        },
        loc: {
          start: {
            line: 153,
            column: 86
          },
          end: {
            line: 158,
            column: 1
          }
        },
        line: 153
      },
      "14": {
        name: "assertAuthSession",
        decl: {
          start: {
            line: 160,
            column: 16
          },
          end: {
            line: 160,
            column: 33
          }
        },
        loc: {
          start: {
            line: 160,
            column: 82
          },
          end: {
            line: 165,
            column: 1
          }
        },
        line: 160
      },
      "15": {
        name: "safeGetProperty",
        decl: {
          start: {
            line: 168,
            column: 16
          },
          end: {
            line: 168,
            column: 31
          }
        },
        loc: {
          start: {
            line: 171,
            column: 20
          },
          end: {
            line: 173,
            column: 1
          }
        },
        line: 171
      },
      "16": {
        name: "safeGetNestedProperty",
        decl: {
          start: {
            line: 175,
            column: 16
          },
          end: {
            line: 175,
            column: 37
          }
        },
        loc: {
          start: {
            line: 179,
            column: 17
          },
          end: {
            line: 195,
            column: 1
          }
        },
        line: 179
      },
      "17": {
        name: "hasProperty",
        decl: {
          start: {
            line: 198,
            column: 16
          },
          end: {
            line: 198,
            column: 27
          }
        },
        loc: {
          start: {
            line: 201,
            column: 33
          },
          end: {
            line: 203,
            column: 1
          }
        },
        line: 201
      },
      "18": {
        name: "hasStringProperty",
        decl: {
          start: {
            line: 205,
            column: 16
          },
          end: {
            line: 205,
            column: 33
          }
        },
        loc: {
          start: {
            line: 208,
            column: 32
          },
          end: {
            line: 210,
            column: 1
          }
        },
        line: 208
      },
      "19": {
        name: "hasNumberProperty",
        decl: {
          start: {
            line: 212,
            column: 16
          },
          end: {
            line: 212,
            column: 33
          }
        },
        loc: {
          start: {
            line: 215,
            column: 32
          },
          end: {
            line: 217,
            column: 1
          }
        },
        line: 215
      },
      "20": {
        name: "hasBooleanProperty",
        decl: {
          start: {
            line: 219,
            column: 16
          },
          end: {
            line: 219,
            column: 34
          }
        },
        loc: {
          start: {
            line: 222,
            column: 33
          },
          end: {
            line: 224,
            column: 1
          }
        },
        line: 222
      },
      "21": {
        name: "isValidStatus",
        decl: {
          start: {
            line: 227,
            column: 16
          },
          end: {
            line: 227,
            column: 29
          }
        },
        loc: {
          start: {
            line: 227,
            column: 104
          },
          end: {
            line: 229,
            column: 1
          }
        },
        line: 227
      },
      "22": {
        name: "isValidRenewalStatus",
        decl: {
          start: {
            line: 231,
            column: 16
          },
          end: {
            line: 231,
            column: 36
          }
        },
        loc: {
          start: {
            line: 231,
            column: 109
          },
          end: {
            line: 233,
            column: 1
          }
        },
        line: 231
      },
      "23": {
        name: "isValidTheme",
        decl: {
          start: {
            line: 235,
            column: 16
          },
          end: {
            line: 235,
            column: 28
          }
        },
        loc: {
          start: {
            line: 235,
            column: 83
          },
          end: {
            line: 237,
            column: 1
          }
        },
        line: 235
      },
      "24": {
        name: "isValidDate",
        decl: {
          start: {
            line: 240,
            column: 16
          },
          end: {
            line: 240,
            column: 27
          }
        },
        loc: {
          start: {
            line: 240,
            column: 59
          },
          end: {
            line: 242,
            column: 1
          }
        },
        line: 240
      },
      "25": {
        name: "isValidDateString",
        decl: {
          start: {
            line: 244,
            column: 16
          },
          end: {
            line: 244,
            column: 33
          }
        },
        loc: {
          start: {
            line: 244,
            column: 67
          },
          end: {
            line: 248,
            column: 1
          }
        },
        line: 244
      },
      "26": {
        name: "isValidEmail",
        decl: {
          start: {
            line: 251,
            column: 16
          },
          end: {
            line: 251,
            column: 28
          }
        },
        loc: {
          start: {
            line: 251,
            column: 62
          },
          end: {
            line: 255,
            column: 1
          }
        },
        line: 251
      },
      "27": {
        name: "isValidUUID",
        decl: {
          start: {
            line: 258,
            column: 16
          },
          end: {
            line: 258,
            column: 27
          }
        },
        loc: {
          start: {
            line: 258,
            column: 61
          },
          end: {
            line: 262,
            column: 1
          }
        },
        line: 258
      },
      "28": {
        name: "isNonEmptyArray",
        decl: {
          start: {
            line: 265,
            column: 16
          },
          end: {
            line: 265,
            column: 31
          }
        },
        loc: {
          start: {
            line: 265,
            column: 73
          },
          end: {
            line: 267,
            column: 1
          }
        },
        line: 265
      },
      "29": {
        name: "isStringArray",
        decl: {
          start: {
            line: 269,
            column: 16
          },
          end: {
            line: 269,
            column: 29
          }
        },
        loc: {
          start: {
            line: 269,
            column: 65
          },
          end: {
            line: 271,
            column: 1
          }
        },
        line: 269
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 270,
            column: 45
          },
          end: {
            line: 270,
            column: 46
          }
        },
        loc: {
          start: {
            line: 270,
            column: 53
          },
          end: {
            line: 270,
            column: 77
          }
        },
        line: 270
      },
      "31": {
        name: "isNumberArray",
        decl: {
          start: {
            line: 273,
            column: 16
          },
          end: {
            line: 273,
            column: 29
          }
        },
        loc: {
          start: {
            line: 273,
            column: 65
          },
          end: {
            line: 275,
            column: 1
          }
        },
        line: 273
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 274,
            column: 45
          },
          end: {
            line: 274,
            column: 46
          }
        },
        loc: {
          start: {
            line: 274,
            column: 53
          },
          end: {
            line: 274,
            column: 77
          }
        },
        line: 274
      },
      "33": {
        name: "isPlainObject",
        decl: {
          start: {
            line: 278,
            column: 16
          },
          end: {
            line: 278,
            column: 29
          }
        },
        loc: {
          start: {
            line: 278,
            column: 80
          },
          end: {
            line: 284,
            column: 1
          }
        },
        line: 278
      },
      "34": {
        name: "hasRequiredKeys",
        decl: {
          start: {
            line: 286,
            column: 16
          },
          end: {
            line: 286,
            column: 31
          }
        },
        loc: {
          start: {
            line: 289,
            column: 12
          },
          end: {
            line: 292,
            column: 1
          }
        },
        line: 289
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 291,
            column: 20
          },
          end: {
            line: 291,
            column: 21
          }
        },
        loc: {
          start: {
            line: 291,
            column: 27
          },
          end: {
            line: 291,
            column: 37
          }
        },
        line: 291
      },
      "36": {
        name: "isError",
        decl: {
          start: {
            line: 295,
            column: 16
          },
          end: {
            line: 295,
            column: 23
          }
        },
        loc: {
          start: {
            line: 295,
            column: 56
          },
          end: {
            line: 297,
            column: 1
          }
        },
        line: 295
      },
      "37": {
        name: "isErrorWithCode",
        decl: {
          start: {
            line: 299,
            column: 16
          },
          end: {
            line: 299,
            column: 31
          }
        },
        loc: {
          start: {
            line: 299,
            column: 83
          },
          end: {
            line: 301,
            column: 1
          }
        },
        line: 299
      },
      "38": {
        name: "isErrorWithStatus",
        decl: {
          start: {
            line: 303,
            column: 16
          },
          end: {
            line: 303,
            column: 33
          }
        },
        loc: {
          start: {
            line: 303,
            column: 91
          },
          end: {
            line: 305,
            column: 1
          }
        },
        line: 303
      },
      "39": {
        name: "assertNever",
        decl: {
          start: {
            line: 308,
            column: 16
          },
          end: {
            line: 308,
            column: 27
          }
        },
        loc: {
          start: {
            line: 308,
            column: 49
          },
          end: {
            line: 310,
            column: 1
          }
        },
        line: 308
      },
      "40": {
        name: "safeJsonParse",
        decl: {
          start: {
            line: 313,
            column: 16
          },
          end: {
            line: 313,
            column: 29
          }
        },
        loc: {
          start: {
            line: 316,
            column: 12
          },
          end: {
            line: 326,
            column: 1
          }
        },
        line: 316
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 94,
            column: 9
          },
          end: {
            line: 94,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 94,
            column: 9
          },
          end: {
            line: 94,
            column: 27
          }
        }, {
          start: {
            line: 94,
            column: 31
          },
          end: {
            line: 94,
            column: 48
          }
        }],
        line: 94
      },
      "1": {
        loc: {
          start: {
            line: 98,
            column: 9
          },
          end: {
            line: 98,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 98,
            column: 9
          },
          end: {
            line: 98,
            column: 27
          }
        }, {
          start: {
            line: 98,
            column: 31
          },
          end: {
            line: 98,
            column: 50
          }
        }],
        line: 98
      },
      "2": {
        loc: {
          start: {
            line: 102,
            column: 9
          },
          end: {
            line: 102,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 102,
            column: 9
          },
          end: {
            line: 102,
            column: 27
          }
        }, {
          start: {
            line: 102,
            column: 31
          },
          end: {
            line: 102,
            column: 51
          }
        }],
        line: 102
      },
      "3": {
        loc: {
          start: {
            line: 107,
            column: 9
          },
          end: {
            line: 107,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 107,
            column: 9
          },
          end: {
            line: 107,
            column: 21
          }
        }, {
          start: {
            line: 107,
            column: 25
          },
          end: {
            line: 107,
            column: 36
          }
        }],
        line: 107
      },
      "4": {
        loc: {
          start: {
            line: 111,
            column: 9
          },
          end: {
            line: 111,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 111,
            column: 9
          },
          end: {
            line: 111,
            column: 21
          }
        }, {
          start: {
            line: 111,
            column: 25
          },
          end: {
            line: 111,
            column: 38
          }
        }],
        line: 111
      },
      "5": {
        loc: {
          start: {
            line: 115,
            column: 9
          },
          end: {
            line: 115,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 115,
            column: 9
          },
          end: {
            line: 115,
            column: 21
          }
        }, {
          start: {
            line: 115,
            column: 25
          },
          end: {
            line: 115,
            column: 45
          }
        }],
        line: 115
      },
      "6": {
        loc: {
          start: {
            line: 123,
            column: 2
          },
          end: {
            line: 125,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 123,
            column: 2
          },
          end: {
            line: 125,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 123
      },
      "7": {
        loc: {
          start: {
            line: 123,
            column: 6
          },
          end: {
            line: 123,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 123,
            column: 6
          },
          end: {
            line: 123,
            column: 25
          }
        }, {
          start: {
            line: 123,
            column: 29
          },
          end: {
            line: 123,
            column: 41
          }
        }, {
          start: {
            line: 123,
            column: 45
          },
          end: {
            line: 123,
            column: 54
          }
        }],
        line: 123
      },
      "8": {
        loc: {
          start: {
            line: 127,
            column: 2
          },
          end: {
            line: 129,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 127,
            column: 2
          },
          end: {
            line: 129,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 127
      },
      "9": {
        loc: {
          start: {
            line: 135,
            column: 9
          },
          end: {
            line: 135,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 135,
            column: 9
          },
          end: {
            line: 135,
            column: 27
          }
        }, {
          start: {
            line: 135,
            column: 31
          },
          end: {
            line: 135,
            column: 43
          }
        }, {
          start: {
            line: 135,
            column: 47
          },
          end: {
            line: 135,
            column: 76
          }
        }],
        line: 135
      },
      "10": {
        loc: {
          start: {
            line: 139,
            column: 41
          },
          end: {
            line: 139,
            column: 60
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 139,
            column: 51
          },
          end: {
            line: 139,
            column: 60
          }
        }],
        line: 139
      },
      "11": {
        loc: {
          start: {
            line: 140,
            column: 2
          },
          end: {
            line: 142,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 140,
            column: 2
          },
          end: {
            line: 142,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 140
      },
      "12": {
        loc: {
          start: {
            line: 146,
            column: 43
          },
          end: {
            line: 146,
            column: 62
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 146,
            column: 53
          },
          end: {
            line: 146,
            column: 62
          }
        }],
        line: 146
      },
      "13": {
        loc: {
          start: {
            line: 147,
            column: 2
          },
          end: {
            line: 149,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 147,
            column: 2
          },
          end: {
            line: 149,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 147
      },
      "14": {
        loc: {
          start: {
            line: 153,
            column: 50
          },
          end: {
            line: 153,
            column: 69
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 153,
            column: 60
          },
          end: {
            line: 153,
            column: 69
          }
        }],
        line: 153
      },
      "15": {
        loc: {
          start: {
            line: 154,
            column: 2
          },
          end: {
            line: 156,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 154,
            column: 2
          },
          end: {
            line: 156,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 154
      },
      "16": {
        loc: {
          start: {
            line: 160,
            column: 48
          },
          end: {
            line: 160,
            column: 67
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 160,
            column: 58
          },
          end: {
            line: 160,
            column: 67
          }
        }],
        line: 160
      },
      "17": {
        loc: {
          start: {
            line: 161,
            column: 2
          },
          end: {
            line: 163,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 161,
            column: 2
          },
          end: {
            line: 163,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 161
      },
      "18": {
        loc: {
          start: {
            line: 185,
            column: 6
          },
          end: {
            line: 187,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 185,
            column: 6
          },
          end: {
            line: 187,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 185
      },
      "19": {
        loc: {
          start: {
            line: 185,
            column: 10
          },
          end: {
            line: 185,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 185,
            column: 10
          },
          end: {
            line: 185,
            column: 25
          }
        }, {
          start: {
            line: 185,
            column: 29
          },
          end: {
            line: 185,
            column: 56
          }
        }],
        line: 185
      },
      "20": {
        loc: {
          start: {
            line: 191,
            column: 11
          },
          end: {
            line: 191,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 191,
            column: 11
          },
          end: {
            line: 191,
            column: 18
          }
        }, {
          start: {
            line: 191,
            column: 22
          },
          end: {
            line: 191,
            column: 34
          }
        }],
        line: 191
      },
      "21": {
        loc: {
          start: {
            line: 209,
            column: 9
          },
          end: {
            line: 209,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 209,
            column: 9
          },
          end: {
            line: 209,
            column: 19
          }
        }, {
          start: {
            line: 209,
            column: 23
          },
          end: {
            line: 209,
            column: 60
          }
        }],
        line: 209
      },
      "22": {
        loc: {
          start: {
            line: 216,
            column: 9
          },
          end: {
            line: 216,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 216,
            column: 9
          },
          end: {
            line: 216,
            column: 19
          }
        }, {
          start: {
            line: 216,
            column: 23
          },
          end: {
            line: 216,
            column: 60
          }
        }],
        line: 216
      },
      "23": {
        loc: {
          start: {
            line: 223,
            column: 9
          },
          end: {
            line: 223,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 223,
            column: 9
          },
          end: {
            line: 223,
            column: 19
          }
        }, {
          start: {
            line: 223,
            column: 23
          },
          end: {
            line: 223,
            column: 61
          }
        }],
        line: 223
      },
      "24": {
        loc: {
          start: {
            line: 228,
            column: 9
          },
          end: {
            line: 228,
            column: 100
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 228,
            column: 9
          },
          end: {
            line: 228,
            column: 34
          }
        }, {
          start: {
            line: 228,
            column: 38
          },
          end: {
            line: 228,
            column: 100
          }
        }],
        line: 228
      },
      "25": {
        loc: {
          start: {
            line: 232,
            column: 9
          },
          end: {
            line: 232,
            column: 98
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 232,
            column: 9
          },
          end: {
            line: 232,
            column: 34
          }
        }, {
          start: {
            line: 232,
            column: 38
          },
          end: {
            line: 232,
            column: 98
          }
        }],
        line: 232
      },
      "26": {
        loc: {
          start: {
            line: 236,
            column: 9
          },
          end: {
            line: 236,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 236,
            column: 9
          },
          end: {
            line: 236,
            column: 34
          }
        }, {
          start: {
            line: 236,
            column: 38
          },
          end: {
            line: 236,
            column: 81
          }
        }],
        line: 236
      },
      "27": {
        loc: {
          start: {
            line: 241,
            column: 9
          },
          end: {
            line: 241,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 241,
            column: 9
          },
          end: {
            line: 241,
            column: 30
          }
        }, {
          start: {
            line: 241,
            column: 34
          },
          end: {
            line: 241,
            column: 57
          }
        }],
        line: 241
      },
      "28": {
        loc: {
          start: {
            line: 245,
            column: 2
          },
          end: {
            line: 245,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 245,
            column: 2
          },
          end: {
            line: 245,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 245
      },
      "29": {
        loc: {
          start: {
            line: 252,
            column: 2
          },
          end: {
            line: 252,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 252,
            column: 2
          },
          end: {
            line: 252,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 252
      },
      "30": {
        loc: {
          start: {
            line: 259,
            column: 2
          },
          end: {
            line: 259,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 259,
            column: 2
          },
          end: {
            line: 259,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 259
      },
      "31": {
        loc: {
          start: {
            line: 266,
            column: 9
          },
          end: {
            line: 266,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 266,
            column: 9
          },
          end: {
            line: 266,
            column: 29
          }
        }, {
          start: {
            line: 266,
            column: 33
          },
          end: {
            line: 266,
            column: 49
          }
        }],
        line: 266
      },
      "32": {
        loc: {
          start: {
            line: 270,
            column: 9
          },
          end: {
            line: 270,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 270,
            column: 9
          },
          end: {
            line: 270,
            column: 29
          }
        }, {
          start: {
            line: 270,
            column: 33
          },
          end: {
            line: 270,
            column: 78
          }
        }],
        line: 270
      },
      "33": {
        loc: {
          start: {
            line: 274,
            column: 9
          },
          end: {
            line: 274,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 274,
            column: 9
          },
          end: {
            line: 274,
            column: 29
          }
        }, {
          start: {
            line: 274,
            column: 33
          },
          end: {
            line: 274,
            column: 78
          }
        }],
        line: 274
      },
      "34": {
        loc: {
          start: {
            line: 280,
            column: 4
          },
          end: {
            line: 282,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 280,
            column: 4
          },
          end: {
            line: 280,
            column: 18
          }
        }, {
          start: {
            line: 281,
            column: 4
          },
          end: {
            line: 281,
            column: 29
          }
        }, {
          start: {
            line: 282,
            column: 4
          },
          end: {
            line: 282,
            column: 63
          }
        }],
        line: 280
      },
      "35": {
        loc: {
          start: {
            line: 290,
            column: 2
          },
          end: {
            line: 290,
            column: 40
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 290,
            column: 2
          },
          end: {
            line: 290,
            column: 40
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 290
      },
      "36": {
        loc: {
          start: {
            line: 300,
            column: 9
          },
          end: {
            line: 300,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 300,
            column: 9
          },
          end: {
            line: 300,
            column: 23
          }
        }, {
          start: {
            line: 300,
            column: 27
          },
          end: {
            line: 300,
            column: 42
          }
        }, {
          start: {
            line: 300,
            column: 46
          },
          end: {
            line: 300,
            column: 76
          }
        }],
        line: 300
      },
      "37": {
        loc: {
          start: {
            line: 304,
            column: 9
          },
          end: {
            line: 304,
            column: 88
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 304,
            column: 9
          },
          end: {
            line: 304,
            column: 23
          }
        }, {
          start: {
            line: 304,
            column: 27
          },
          end: {
            line: 304,
            column: 48
          }
        }, {
          start: {
            line: 304,
            column: 52
          },
          end: {
            line: 304,
            column: 88
          }
        }],
        line: 304
      },
      "38": {
        loc: {
          start: {
            line: 319,
            column: 4
          },
          end: {
            line: 321,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 319,
            column: 4
          },
          end: {
            line: 321,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 319
      },
      "39": {
        loc: {
          start: {
            line: 319,
            column: 8
          },
          end: {
            line: 319,
            column: 39
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 319,
            column: 8
          },
          end: {
            line: 319,
            column: 17
          }
        }, {
          start: {
            line: 319,
            column: 21
          },
          end: {
            line: 319,
            column: 39
          }
        }],
        line: 319
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0, 0],
      "8": [0, 0],
      "9": [0, 0, 0],
      "10": [0],
      "11": [0, 0],
      "12": [0],
      "13": [0, 0],
      "14": [0],
      "15": [0, 0],
      "16": [0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0, 0],
      "35": [0, 0],
      "36": [0, 0, 0],
      "37": [0, 0, 0],
      "38": [0, 0],
      "39": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ff1c9306b8fe233452b492d79c15b42794353627"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1o0gle82dd = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1o0gle82dd();
/**
 * Type Utilities and Advanced Type Guards
 * 
 * This module provides advanced type checking utilities, runtime type validation,
 * and helper functions for working with TypeScript types safely.
 */
// Runtime type validation schemas
const userSchema =
/* istanbul ignore next */
exports.userSchema = (cov_1o0gle82dd().s[0]++,
/* istanbul ignore next */
_zod.
/* istanbul ignore next */
z.object({
  id:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().uuid(),
  email:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().email(),
  name:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().optional(),
  given_name:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().optional(),
  family_name:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().optional(),
  roles:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.array(
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string()),
  status:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.enum(['active', 'inactive', 'suspended', 'deleted']),
  created_at:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.date(),
  updated_at:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.date().optional(),
  last_login:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.date().optional()
}));
const clientSchema =
/* istanbul ignore next */
exports.clientSchema = (cov_1o0gle82dd().s[1]++,
/* istanbul ignore next */
_zod.
/* istanbul ignore next */
z.object({
  id:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().uuid(),
  name:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().min(1),
  domain:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().min(1),
  domains:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.array(
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string()).optional(),
  status:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.enum(['active', 'inactive', 'suspended']),
  settings:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.record(
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.any()),
  created_at:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.date(),
  updated_at:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.date().optional()
}));
const renewalSchema =
/* istanbul ignore next */
exports.renewalSchema = (cov_1o0gle82dd().s[2]++,
/* istanbul ignore next */
_zod.
/* istanbul ignore next */
z.object({
  id:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().uuid(),
  name:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().min(1),
  vendor:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().min(1),
  vendor_id:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().optional(),
  status:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.enum(['active', 'inactive', 'pending', 'expired']),
  due_date:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.date().optional(),
  annual_cost:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.number().min(0).optional(),
  description:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().optional(),
  created_at:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.date(),
  updated_at:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.date().optional()
}));

// Advanced type guards with detailed validation
function validateUser(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[0]++;
  cov_1o0gle82dd().s[3]++;
  try {
    /* istanbul ignore next */
    cov_1o0gle82dd().s[4]++;
    userSchema.parse(obj);
    /* istanbul ignore next */
    cov_1o0gle82dd().s[5]++;
    return /* istanbul ignore next */(0,
    /* istanbul ignore next */
    _types.
    /* istanbul ignore next */
    isUser)(obj);
  } catch {
    /* istanbul ignore next */
    cov_1o0gle82dd().s[6]++;
    return false;
  }
}
function validateClient(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[1]++;
  cov_1o0gle82dd().s[7]++;
  try {
    /* istanbul ignore next */
    cov_1o0gle82dd().s[8]++;
    clientSchema.parse(obj);
    /* istanbul ignore next */
    cov_1o0gle82dd().s[9]++;
    return /* istanbul ignore next */(0,
    /* istanbul ignore next */
    _types.
    /* istanbul ignore next */
    isClient)(obj);
  } catch {
    /* istanbul ignore next */
    cov_1o0gle82dd().s[10]++;
    return false;
  }
}
function validateRenewal(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[2]++;
  cov_1o0gle82dd().s[11]++;
  try {
    /* istanbul ignore next */
    cov_1o0gle82dd().s[12]++;
    renewalSchema.parse(obj);
    /* istanbul ignore next */
    cov_1o0gle82dd().s[13]++;
    return /* istanbul ignore next */(0,
    /* istanbul ignore next */
    _types.
    /* istanbul ignore next */
    isRenewal)(obj);
  } catch {
    /* istanbul ignore next */
    cov_1o0gle82dd().s[14]++;
    return false;
  }
}

// Array type guards
function isUserArray(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[3]++;
  cov_1o0gle82dd().s[15]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[0][0]++, Array.isArray(obj)) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[0][1]++, obj.every(
  /* istanbul ignore next */
  _types.
  /* istanbul ignore next */
  isUser));
}
function isClientArray(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[4]++;
  cov_1o0gle82dd().s[16]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[1][0]++, Array.isArray(obj)) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[1][1]++, obj.every(
  /* istanbul ignore next */
  _types.
  /* istanbul ignore next */
  isClient));
}
function isRenewalArray(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[5]++;
  cov_1o0gle82dd().s[17]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[2][0]++, Array.isArray(obj)) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[2][1]++, obj.every(
  /* istanbul ignore next */
  _types.
  /* istanbul ignore next */
  isRenewal));
}

// Nullable type guards
function isNullableUser(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[6]++;
  cov_1o0gle82dd().s[18]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[3][0]++, obj === null) ||
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[3][1]++,
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _types.
  /* istanbul ignore next */
  isUser)(obj));
}
function isNullableClient(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[7]++;
  cov_1o0gle82dd().s[19]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[4][0]++, obj === null) ||
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[4][1]++,
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _types.
  /* istanbul ignore next */
  isClient)(obj));
}
function isNullableTenantContext(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[8]++;
  cov_1o0gle82dd().s[20]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[5][0]++, obj === null) ||
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[5][1]++,
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _types.
  /* istanbul ignore next */
  isTenantContext)(obj));
}

// API response type guards
function isSuccessResponse(obj, dataValidator) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[9]++;
  cov_1o0gle82dd().s[21]++;
  if (
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[7][0]++, !
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _types.
  /* istanbul ignore next */
  isApiResponse)(obj)) ||
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[7][1]++, !obj.success) ||
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[7][2]++, !obj.data)) {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[6][0]++;
    cov_1o0gle82dd().s[22]++;
    return false;
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[6][1]++;
  }
  cov_1o0gle82dd().s[23]++;
  if (dataValidator) {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[8][0]++;
    cov_1o0gle82dd().s[24]++;
    return dataValidator(obj.data);
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[8][1]++;
  }
  cov_1o0gle82dd().s[25]++;
  return true;
}
function isErrorResponse(obj) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[10]++;
  cov_1o0gle82dd().s[26]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[9][0]++,
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _types.
  /* istanbul ignore next */
  isApiResponse)(obj)) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[9][1]++, !obj.success) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[9][2]++, typeof obj.error === 'string');
}

// Type assertion helpers with runtime validation
function assertUser(obj, context =
/* istanbul ignore next */
(cov_1o0gle82dd().b[10][0]++, 'Unknown')) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[11]++;
  cov_1o0gle82dd().s[27]++;
  if (!
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _types.
  /* istanbul ignore next */
  isUser)(obj)) {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[11][0]++;
    cov_1o0gle82dd().s[28]++;
    throw new TypeError(`Expected User object in ${context}, got: ${typeof obj}`);
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[11][1]++;
  }
  cov_1o0gle82dd().s[29]++;
  return obj;
}
function assertClient(obj, context =
/* istanbul ignore next */
(cov_1o0gle82dd().b[12][0]++, 'Unknown')) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[12]++;
  cov_1o0gle82dd().s[30]++;
  if (!
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _types.
  /* istanbul ignore next */
  isClient)(obj)) {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[13][0]++;
    cov_1o0gle82dd().s[31]++;
    throw new TypeError(`Expected Client object in ${context}, got: ${typeof obj}`);
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[13][1]++;
  }
  cov_1o0gle82dd().s[32]++;
  return obj;
}
function assertTenantContext(obj, context =
/* istanbul ignore next */
(cov_1o0gle82dd().b[14][0]++, 'Unknown')) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[13]++;
  cov_1o0gle82dd().s[33]++;
  if (!
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _types.
  /* istanbul ignore next */
  isTenantContext)(obj)) {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[15][0]++;
    cov_1o0gle82dd().s[34]++;
    throw new TypeError(`Expected TenantContext object in ${context}, got: ${typeof obj}`);
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[15][1]++;
  }
  cov_1o0gle82dd().s[35]++;
  return obj;
}
function assertAuthSession(obj, context =
/* istanbul ignore next */
(cov_1o0gle82dd().b[16][0]++, 'Unknown')) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[14]++;
  cov_1o0gle82dd().s[36]++;
  if (!
  /* istanbul ignore next */
  (0,
  /* istanbul ignore next */
  _types.
  /* istanbul ignore next */
  isAuthSession)(obj)) {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[17][0]++;
    cov_1o0gle82dd().s[37]++;
    throw new TypeError(`Expected AuthSession object in ${context}, got: ${typeof obj}`);
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[17][1]++;
  }
  cov_1o0gle82dd().s[38]++;
  return obj;
}

// Safe property access helpers
function safeGetProperty(obj, key) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[15]++;
  cov_1o0gle82dd().s[39]++;
  return obj?.[key];
}
function safeGetNestedProperty(obj, path, defaultValue) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[16]++;
  cov_1o0gle82dd().s[40]++;
  try {
    const keys =
    /* istanbul ignore next */
    (cov_1o0gle82dd().s[41]++, path.split('.'));
    let current =
    /* istanbul ignore next */
    (cov_1o0gle82dd().s[42]++, obj);
    /* istanbul ignore next */
    cov_1o0gle82dd().s[43]++;
    for (const key of keys) {
      /* istanbul ignore next */
      cov_1o0gle82dd().s[44]++;
      if (
      /* istanbul ignore next */
      (cov_1o0gle82dd().b[19][0]++, current == null) ||
      /* istanbul ignore next */
      (cov_1o0gle82dd().b[19][1]++, typeof current !== 'object')) {
        /* istanbul ignore next */
        cov_1o0gle82dd().b[18][0]++;
        cov_1o0gle82dd().s[45]++;
        return defaultValue;
      } else
      /* istanbul ignore next */
      {
        cov_1o0gle82dd().b[18][1]++;
      }
      cov_1o0gle82dd().s[46]++;
      current = current[key];
    }
    /* istanbul ignore next */
    cov_1o0gle82dd().s[47]++;
    return /* istanbul ignore next */(cov_1o0gle82dd().b[20][0]++, current) ??
    /* istanbul ignore next */
    (cov_1o0gle82dd().b[20][1]++, defaultValue);
  } catch {
    /* istanbul ignore next */
    cov_1o0gle82dd().s[48]++;
    return defaultValue;
  }
}

// Type narrowing helpers
function hasProperty(obj, key) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[17]++;
  cov_1o0gle82dd().s[49]++;
  return key in obj;
}
function hasStringProperty(obj, key) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[18]++;
  cov_1o0gle82dd().s[50]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[21][0]++, key in obj) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[21][1]++, typeof obj[key] === 'string');
}
function hasNumberProperty(obj, key) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[19]++;
  cov_1o0gle82dd().s[51]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[22][0]++, key in obj) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[22][1]++, typeof obj[key] === 'number');
}
function hasBooleanProperty(obj, key) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[20]++;
  cov_1o0gle82dd().s[52]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[23][0]++, key in obj) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[23][1]++, typeof obj[key] === 'boolean');
}

// Enum validation helpers
function isValidStatus(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[21]++;
  cov_1o0gle82dd().s[53]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[24][0]++, typeof value === 'string') &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[24][1]++, ['active', 'inactive', 'suspended', 'deleted'].includes(value));
}
function isValidRenewalStatus(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[22]++;
  cov_1o0gle82dd().s[54]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[25][0]++, typeof value === 'string') &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[25][1]++, ['active', 'inactive', 'pending', 'expired'].includes(value));
}
function isValidTheme(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[23]++;
  cov_1o0gle82dd().s[55]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[26][0]++, typeof value === 'string') &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[26][1]++, ['light', 'dark', 'system'].includes(value));
}

// Date validation helpers
function isValidDate(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[24]++;
  cov_1o0gle82dd().s[56]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[27][0]++, value instanceof Date) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[27][1]++, !isNaN(value.getTime()));
}
function isValidDateString(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[25]++;
  cov_1o0gle82dd().s[57]++;
  if (typeof value !== 'string') {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[28][0]++;
    cov_1o0gle82dd().s[58]++;
    return false;
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[28][1]++;
  }
  const date =
  /* istanbul ignore next */
  (cov_1o0gle82dd().s[59]++, new Date(value));
  /* istanbul ignore next */
  cov_1o0gle82dd().s[60]++;
  return isValidDate(date);
}

// Email validation helper
function isValidEmail(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[26]++;
  cov_1o0gle82dd().s[61]++;
  if (typeof value !== 'string') {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[29][0]++;
    cov_1o0gle82dd().s[62]++;
    return false;
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[29][1]++;
  }
  const emailRegex =
  /* istanbul ignore next */
  (cov_1o0gle82dd().s[63]++, /^[^\s@]+@[^\s@]+\.[^\s@]+$/);
  /* istanbul ignore next */
  cov_1o0gle82dd().s[64]++;
  return emailRegex.test(value);
}

// UUID validation helper
function isValidUUID(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[27]++;
  cov_1o0gle82dd().s[65]++;
  if (typeof value !== 'string') {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[30][0]++;
    cov_1o0gle82dd().s[66]++;
    return false;
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[30][1]++;
  }
  const uuidRegex =
  /* istanbul ignore next */
  (cov_1o0gle82dd().s[67]++, /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
  /* istanbul ignore next */
  cov_1o0gle82dd().s[68]++;
  return uuidRegex.test(value);
}

// Array validation helpers
function isNonEmptyArray(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[28]++;
  cov_1o0gle82dd().s[69]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[31][0]++, Array.isArray(value)) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[31][1]++, value.length > 0);
}
function isStringArray(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[29]++;
  cov_1o0gle82dd().s[70]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[32][0]++, Array.isArray(value)) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[32][1]++, value.every(item => {
    /* istanbul ignore next */
    cov_1o0gle82dd().f[30]++;
    cov_1o0gle82dd().s[71]++;
    return typeof item === 'string';
  }));
}
function isNumberArray(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[31]++;
  cov_1o0gle82dd().s[72]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[33][0]++, Array.isArray(value)) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[33][1]++, value.every(item => {
    /* istanbul ignore next */
    cov_1o0gle82dd().f[32]++;
    cov_1o0gle82dd().s[73]++;
    return typeof item === 'number';
  }));
}

// Object validation helpers
function isPlainObject(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[33]++;
  cov_1o0gle82dd().s[74]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[34][0]++, value !== null) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[34][1]++, typeof value === 'object') &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[34][2]++, Object.prototype.toString.call(value) === '[object Object]');
}
function hasRequiredKeys(obj, keys) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[34]++;
  cov_1o0gle82dd().s[75]++;
  if (!isPlainObject(obj)) {
    /* istanbul ignore next */
    cov_1o0gle82dd().b[35][0]++;
    cov_1o0gle82dd().s[76]++;
    return false;
  } else
  /* istanbul ignore next */
  {
    cov_1o0gle82dd().b[35][1]++;
  }
  cov_1o0gle82dd().s[77]++;
  return keys.every(key => {
    /* istanbul ignore next */
    cov_1o0gle82dd().f[35]++;
    cov_1o0gle82dd().s[78]++;
    return key in obj;
  });
}

// Error handling type guards
function isError(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[36]++;
  cov_1o0gle82dd().s[79]++;
  return value instanceof Error;
}
function isErrorWithCode(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[37]++;
  cov_1o0gle82dd().s[80]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[36][0]++, isError(value)) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[36][1]++, 'code' in value) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[36][2]++, typeof value.code === 'string');
}
function isErrorWithStatus(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[38]++;
  cov_1o0gle82dd().s[81]++;
  return /* istanbul ignore next */(cov_1o0gle82dd().b[37][0]++, isError(value)) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[37][1]++, 'statusCode' in value) &&
  /* istanbul ignore next */
  (cov_1o0gle82dd().b[37][2]++, typeof value.statusCode === 'number');
}

// Utility type for exhaustive checking
function assertNever(value) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[39]++;
  cov_1o0gle82dd().s[82]++;
  throw new Error(`Unexpected value: ${value}`);
}

// Type-safe JSON parsing
function safeJsonParse(json, validator) {
  /* istanbul ignore next */
  cov_1o0gle82dd().f[40]++;
  cov_1o0gle82dd().s[83]++;
  try {
    const parsed =
    /* istanbul ignore next */
    (cov_1o0gle82dd().s[84]++, JSON.parse(json));
    /* istanbul ignore next */
    cov_1o0gle82dd().s[85]++;
    if (
    /* istanbul ignore next */
    (cov_1o0gle82dd().b[39][0]++, validator) &&
    /* istanbul ignore next */
    (cov_1o0gle82dd().b[39][1]++, !validator(parsed))) {
      /* istanbul ignore next */
      cov_1o0gle82dd().b[38][0]++;
      cov_1o0gle82dd().s[86]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_1o0gle82dd().b[38][1]++;
    }
    cov_1o0gle82dd().s[87]++;
    return parsed;
  } catch {
    /* istanbul ignore next */
    cov_1o0gle82dd().s[88]++;
    return null;
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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