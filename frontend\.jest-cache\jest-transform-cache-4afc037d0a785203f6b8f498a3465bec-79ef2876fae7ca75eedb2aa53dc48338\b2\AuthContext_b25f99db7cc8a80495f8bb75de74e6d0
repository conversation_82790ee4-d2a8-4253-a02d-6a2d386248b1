01648893233d69f9a9fcf03286ac8616
/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\AuthContext.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_bt3a0bpyd() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\AuthContext.tsx";
  var hash = "7f7be609c09d6883b69790a497290b64c56a1c48";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\AuthContext.tsx",
    statementMap: {
      "0": {
        start: {
          line: 19,
          column: 20
        },
        end: {
          line: 25,
          column: 2
        }
      },
      "1": {
        start: {
          line: 23,
          column: 23
        },
        end: {
          line: 23,
          column: 28
        }
      },
      "2": {
        start: {
          line: 24,
          column: 17
        },
        end: {
          line: 24,
          column: 22
        }
      },
      "3": {
        start: {
          line: 28,
          column: 42
        },
        end: {
          line: 28,
          column: 66
        }
      },
      "4": {
        start: {
          line: 29,
          column: 36
        },
        end: {
          line: 29,
          column: 59
        }
      },
      "5": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 53
        }
      },
      "6": {
        start: {
          line: 33,
          column: 25
        },
        end: {
          line: 50,
          column: 3
        }
      },
      "7": {
        start: {
          line: 34,
          column: 4
        },
        end: {
          line: 49,
          column: 5
        }
      },
      "8": {
        start: {
          line: 35,
          column: 26
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "9": {
        start: {
          line: 36,
          column: 25
        },
        end: {
          line: 36,
          column: 52
        }
      },
      "10": {
        start: {
          line: 38,
          column: 6
        },
        end: {
          line: 45,
          column: 8
        }
      },
      "11": {
        start: {
          line: 47,
          column: 6
        },
        end: {
          line: 47,
          column: 58
        }
      },
      "12": {
        start: {
          line: 48,
          column: 6
        },
        end: {
          line: 48,
          column: 18
        }
      },
      "13": {
        start: {
          line: 52,
          column: 2
        },
        end: {
          line: 150,
          column: 9
        }
      },
      "14": {
        start: {
          line: 53,
          column: 20
        },
        end: {
          line: 53,
          column: 24
        }
      },
      "15": {
        start: {
          line: 55,
          column: 22
        },
        end: {
          line: 132,
          column: 5
        }
      },
      "16": {
        start: {
          line: 56,
          column: 6
        },
        end: {
          line: 56,
          column: 29
        }
      },
      "17": {
        start: {
          line: 56,
          column: 22
        },
        end: {
          line: 56,
          column: 29
        }
      },
      "18": {
        start: {
          line: 58,
          column: 6
        },
        end: {
          line: 131,
          column: 7
        }
      },
      "19": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 59,
          column: 74
        }
      },
      "20": {
        start: {
          line: 62,
          column: 26
        },
        end: {
          line: 62,
          column: 69
        }
      },
      "21": {
        start: {
          line: 63,
          column: 29
        },
        end: {
          line: 63,
          column: 50
        }
      },
      "22": {
        start: {
          line: 65,
          column: 8
        },
        end: {
          line: 72,
          column: 9
        }
      },
      "23": {
        start: {
          line: 66,
          column: 10
        },
        end: {
          line: 66,
          column: 97
        }
      },
      "24": {
        start: {
          line: 68,
          column: 10
        },
        end: {
          line: 68,
          column: 66
        }
      },
      "25": {
        start: {
          line: 68,
          column: 39
        },
        end: {
          line: 68,
          column: 64
        }
      },
      "26": {
        start: {
          line: 71,
          column: 10
        },
        end: {
          line: 71,
          column: 66
        }
      },
      "27": {
        start: {
          line: 71,
          column: 39
        },
        end: {
          line: 71,
          column: 64
        }
      },
      "28": {
        start: {
          line: 75,
          column: 24
        },
        end: {
          line: 75,
          column: 71
        }
      },
      "29": {
        start: {
          line: 76,
          column: 30
        },
        end: {
          line: 76,
          column: 56
        }
      },
      "30": {
        start: {
          line: 78,
          column: 8
        },
        end: {
          line: 84,
          column: 11
        }
      },
      "31": {
        start: {
          line: 86,
          column: 8
        },
        end: {
          line: 115,
          column: 9
        }
      },
      "32": {
        start: {
          line: 87,
          column: 10
        },
        end: {
          line: 87,
          column: 89
        }
      },
      "33": {
        start: {
          line: 89,
          column: 10
        },
        end: {
          line: 108,
          column: 11
        }
      },
      "34": {
        start: {
          line: 90,
          column: 29
        },
        end: {
          line: 90,
          column: 51
        }
      },
      "35": {
        start: {
          line: 91,
          column: 12
        },
        end: {
          line: 91,
          column: 75
        }
      },
      "36": {
        start: {
          line: 93,
          column: 12
        },
        end: {
          line: 101,
          column: 13
        }
      },
      "37": {
        start: {
          line: 94,
          column: 14
        },
        end: {
          line: 94,
          column: 32
        }
      },
      "38": {
        start: {
          line: 95,
          column: 14
        },
        end: {
          line: 95,
          column: 33
        }
      },
      "39": {
        start: {
          line: 96,
          column: 14
        },
        end: {
          line: 96,
          column: 78
        }
      },
      "40": {
        start: {
          line: 97,
          column: 19
        },
        end: {
          line: 101,
          column: 13
        }
      },
      "41": {
        start: {
          line: 98,
          column: 14
        },
        end: {
          line: 98,
          column: 82
        }
      },
      "42": {
        start: {
          line: 99,
          column: 14
        },
        end: {
          line: 99,
          column: 28
        }
      },
      "43": {
        start: {
          line: 100,
          column: 14
        },
        end: {
          line: 100,
          column: 34
        }
      },
      "44": {
        start: {
          line: 103,
          column: 12
        },
        end: {
          line: 103,
          column: 82
        }
      },
      "45": {
        start: {
          line: 104,
          column: 12
        },
        end: {
          line: 107,
          column: 13
        }
      },
      "46": {
        start: {
          line: 105,
          column: 14
        },
        end: {
          line: 105,
          column: 28
        }
      },
      "47": {
        start: {
          line: 106,
          column: 14
        },
        end: {
          line: 106,
          column: 34
        }
      },
      "48": {
        start: {
          line: 110,
          column: 10
        },
        end: {
          line: 110,
          column: 65
        }
      },
      "49": {
        start: {
          line: 111,
          column: 10
        },
        end: {
          line: 114,
          column: 11
        }
      },
      "50": {
        start: {
          line: 112,
          column: 12
        },
        end: {
          line: 112,
          column: 26
        }
      },
      "51": {
        start: {
          line: 113,
          column: 12
        },
        end: {
          line: 113,
          column: 32
        }
      },
      "52": {
        start: {
          line: 117,
          column: 8
        },
        end: {
          line: 117,
          column: 67
        }
      },
      "53": {
        start: {
          line: 118,
          column: 8
        },
        end: {
          line: 121,
          column: 9
        }
      },
      "54": {
        start: {
          line: 119,
          column: 10
        },
        end: {
          line: 119,
          column: 24
        }
      },
      "55": {
        start: {
          line: 120,
          column: 10
        },
        end: {
          line: 120,
          column: 30
        }
      },
      "56": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 130,
          column: 9
        }
      },
      "57": {
        start: {
          line: 124,
          column: 10
        },
        end: {
          line: 124,
          column: 30
        }
      },
      "58": {
        start: {
          line: 125,
          column: 10
        },
        end: {
          line: 129,
          column: 13
        }
      },
      "59": {
        start: {
          line: 134,
          column: 4
        },
        end: {
          line: 134,
          column: 16
        }
      },
      "60": {
        start: {
          line: 137,
          column: 32
        },
        end: {
          line: 142,
          column: 5
        }
      },
      "61": {
        start: {
          line: 138,
          column: 6
        },
        end: {
          line: 141,
          column: 7
        }
      },
      "62": {
        start: {
          line: 139,
          column: 8
        },
        end: {
          line: 139,
          column: 79
        }
      },
      "63": {
        start: {
          line: 140,
          column: 8
        },
        end: {
          line: 140,
          column: 20
        }
      },
      "64": {
        start: {
          line: 144,
          column: 4
        },
        end: {
          line: 144,
          column: 60
        }
      },
      "65": {
        start: {
          line: 146,
          column: 4
        },
        end: {
          line: 149,
          column: 6
        }
      },
      "66": {
        start: {
          line: 147,
          column: 6
        },
        end: {
          line: 147,
          column: 24
        }
      },
      "67": {
        start: {
          line: 148,
          column: 6
        },
        end: {
          line: 148,
          column: 65
        }
      },
      "68": {
        start: {
          line: 153,
          column: 18
        },
        end: {
          line: 155,
          column: 3
        }
      },
      "69": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 154,
          column: 48
        }
      },
      "70": {
        start: {
          line: 158,
          column: 18
        },
        end: {
          line: 186,
          column: 3
        }
      },
      "71": {
        start: {
          line: 159,
          column: 4
        },
        end: {
          line: 185,
          column: 5
        }
      },
      "72": {
        start: {
          line: 160,
          column: 6
        },
        end: {
          line: 160,
          column: 45
        }
      },
      "73": {
        start: {
          line: 163,
          column: 6
        },
        end: {
          line: 163,
          column: 49
        }
      },
      "74": {
        start: {
          line: 166,
          column: 6
        },
        end: {
          line: 166,
          column: 68
        }
      },
      "75": {
        start: {
          line: 169,
          column: 6
        },
        end: {
          line: 169,
          column: 20
        }
      },
      "76": {
        start: {
          line: 170,
          column: 6
        },
        end: {
          line: 170,
          column: 26
        }
      },
      "77": {
        start: {
          line: 172,
          column: 6
        },
        end: {
          line: 172,
          column: 18
        }
      },
      "78": {
        start: {
          line: 174,
          column: 6
        },
        end: {
          line: 174,
          column: 49
        }
      },
      "79": {
        start: {
          line: 177,
          column: 6
        },
        end: {
          line: 177,
          column: 49
        }
      },
      "80": {
        start: {
          line: 178,
          column: 6
        },
        end: {
          line: 178,
          column: 68
        }
      },
      "81": {
        start: {
          line: 181,
          column: 6
        },
        end: {
          line: 181,
          column: 20
        }
      },
      "82": {
        start: {
          line: 182,
          column: 6
        },
        end: {
          line: 182,
          column: 26
        }
      },
      "83": {
        start: {
          line: 184,
          column: 6
        },
        end: {
          line: 184,
          column: 18
        }
      },
      "84": {
        start: {
          line: 188,
          column: 2
        },
        end: {
          line: 200,
          column: 4
        }
      },
      "85": {
        start: {
          line: 204,
          column: 23
        },
        end: {
          line: 204,
          column: 52
        }
      },
      "86": {
        start: {
          line: 204,
          column: 29
        },
        end: {
          line: 204,
          column: 52
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 23,
            column: 11
          },
          end: {
            line: 23,
            column: 12
          }
        },
        loc: {
          start: {
            line: 23,
            column: 23
          },
          end: {
            line: 23,
            column: 28
          }
        },
        line: 23
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 24,
            column: 11
          },
          end: {
            line: 24,
            column: 12
          }
        },
        loc: {
          start: {
            line: 24,
            column: 17
          },
          end: {
            line: 24,
            column: 22
          }
        },
        line: 24
      },
      "2": {
        name: "AuthProvider",
        decl: {
          start: {
            line: 27,
            column: 16
          },
          end: {
            line: 27,
            column: 28
          }
        },
        loc: {
          start: {
            line: 27,
            column: 68
          },
          end: {
            line: 201,
            column: 1
          }
        },
        line: 27
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 33,
            column: 25
          },
          end: {
            line: 33,
            column: 26
          }
        },
        loc: {
          start: {
            line: 33,
            column: 59
          },
          end: {
            line: 50,
            column: 3
          }
        },
        line: 33
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 52,
            column: 12
          },
          end: {
            line: 52,
            column: 13
          }
        },
        loc: {
          start: {
            line: 52,
            column: 18
          },
          end: {
            line: 150,
            column: 3
          }
        },
        line: 52
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 55,
            column: 22
          },
          end: {
            line: 55,
            column: 23
          }
        },
        loc: {
          start: {
            line: 55,
            column: 34
          },
          end: {
            line: 132,
            column: 5
          }
        },
        line: 55
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 68,
            column: 28
          },
          end: {
            line: 68,
            column: 29
          }
        },
        loc: {
          start: {
            line: 68,
            column: 39
          },
          end: {
            line: 68,
            column: 64
          }
        },
        line: 68
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 71,
            column: 28
          },
          end: {
            line: 71,
            column: 29
          }
        },
        loc: {
          start: {
            line: 71,
            column: 39
          },
          end: {
            line: 71,
            column: 64
          }
        },
        line: 71
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 137,
            column: 32
          },
          end: {
            line: 137,
            column: 33
          }
        },
        loc: {
          start: {
            line: 137,
            column: 53
          },
          end: {
            line: 142,
            column: 5
          }
        },
        line: 137
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 146,
            column: 11
          },
          end: {
            line: 146,
            column: 12
          }
        },
        loc: {
          start: {
            line: 146,
            column: 17
          },
          end: {
            line: 149,
            column: 5
          }
        },
        line: 146
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 153,
            column: 18
          },
          end: {
            line: 153,
            column: 19
          }
        },
        loc: {
          start: {
            line: 153,
            column: 36
          },
          end: {
            line: 155,
            column: 3
          }
        },
        line: 153
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 158,
            column: 18
          },
          end: {
            line: 158,
            column: 19
          }
        },
        loc: {
          start: {
            line: 158,
            column: 48
          },
          end: {
            line: 186,
            column: 3
          }
        },
        line: 158
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 204,
            column: 23
          },
          end: {
            line: 204,
            column: 24
          }
        },
        loc: {
          start: {
            line: 204,
            column: 29
          },
          end: {
            line: 204,
            column: 52
          }
        },
        line: 204
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 40,
            column: 15
          },
          end: {
            line: 40,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 15
          },
          end: {
            line: 40,
            column: 31
          }
        }, {
          start: {
            line: 40,
            column: 35
          },
          end: {
            line: 40,
            column: 37
          }
        }],
        line: 40
      },
      "1": {
        loc: {
          start: {
            line: 44,
            column: 15
          },
          end: {
            line: 44,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 44,
            column: 44
          },
          end: {
            line: 44,
            column: 82
          }
        }, {
          start: {
            line: 44,
            column: 85
          },
          end: {
            line: 44,
            column: 87
          }
        }],
        line: 44
      },
      "2": {
        loc: {
          start: {
            line: 56,
            column: 6
          },
          end: {
            line: 56,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 56,
            column: 6
          },
          end: {
            line: 56,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 56
      },
      "3": {
        loc: {
          start: {
            line: 65,
            column: 8
          },
          end: {
            line: 72,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 65,
            column: 8
          },
          end: {
            line: 72,
            column: 9
          }
        }, {
          start: {
            line: 69,
            column: 15
          },
          end: {
            line: 72,
            column: 9
          }
        }],
        line: 65
      },
      "4": {
        loc: {
          start: {
            line: 86,
            column: 8
          },
          end: {
            line: 115,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 86,
            column: 8
          },
          end: {
            line: 115,
            column: 9
          }
        }, {
          start: {
            line: 109,
            column: 15
          },
          end: {
            line: 115,
            column: 9
          }
        }],
        line: 86
      },
      "5": {
        loc: {
          start: {
            line: 93,
            column: 12
          },
          end: {
            line: 101,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 93,
            column: 12
          },
          end: {
            line: 101,
            column: 13
          }
        }, {
          start: {
            line: 97,
            column: 19
          },
          end: {
            line: 101,
            column: 13
          }
        }],
        line: 93
      },
      "6": {
        loc: {
          start: {
            line: 93,
            column: 16
          },
          end: {
            line: 93,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 93,
            column: 16
          },
          end: {
            line: 93,
            column: 25
          }
        }, {
          start: {
            line: 93,
            column: 29
          },
          end: {
            line: 93,
            column: 37
          }
        }],
        line: 93
      },
      "7": {
        loc: {
          start: {
            line: 97,
            column: 19
          },
          end: {
            line: 101,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 97,
            column: 19
          },
          end: {
            line: 101,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 97
      },
      "8": {
        loc: {
          start: {
            line: 104,
            column: 12
          },
          end: {
            line: 107,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 104,
            column: 12
          },
          end: {
            line: 107,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 104
      },
      "9": {
        loc: {
          start: {
            line: 111,
            column: 10
          },
          end: {
            line: 114,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 111,
            column: 10
          },
          end: {
            line: 114,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 111
      },
      "10": {
        loc: {
          start: {
            line: 118,
            column: 8
          },
          end: {
            line: 121,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 118,
            column: 8
          },
          end: {
            line: 121,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 118
      },
      "11": {
        loc: {
          start: {
            line: 123,
            column: 8
          },
          end: {
            line: 130,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 123,
            column: 8
          },
          end: {
            line: 130,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 123
      },
      "12": {
        loc: {
          start: {
            line: 138,
            column: 6
          },
          end: {
            line: 141,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 138,
            column: 6
          },
          end: {
            line: 141,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 138
      },
      "13": {
        loc: {
          start: {
            line: 154,
            column: 11
          },
          end: {
            line: 154,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 154,
            column: 11
          },
          end: {
            line: 154,
            column: 38
          }
        }, {
          start: {
            line: 154,
            column: 42
          },
          end: {
            line: 154,
            column: 47
          }
        }],
        line: 154
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "7f7be609c09d6883b69790a497290b64c56a1c48"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_bt3a0bpyd = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_bt3a0bpyd();
import { createContext, useState, useEffect, useContext } from 'react';
import { fetchAuthSession, signOut as amplifySignOut, getCurrentUser as getAmplifyCurrentUser, fetchUserAttributes } from 'aws-amplify/auth';
const AuthContext =
/* istanbul ignore next */
(cov_bt3a0bpyd().s[0]++, /*#__PURE__*/createContext({
  isAuthenticated: false,
  isLoading: true,
  user: null,
  signOut: async () => {
    /* istanbul ignore next */
    cov_bt3a0bpyd().f[0]++;
    cov_bt3a0bpyd().s[1]++;
    return false;
  },
  hasRole: () => {
    /* istanbul ignore next */
    cov_bt3a0bpyd().f[1]++;
    cov_bt3a0bpyd().s[2]++;
    return false;
  }
}));
export function AuthProvider({
  children
}) {
  /* istanbul ignore next */
  cov_bt3a0bpyd().f[2]++;
  const [isAuthenticated, setAuthState] =
  /* istanbul ignore next */
  (cov_bt3a0bpyd().s[3]++, useState(false));
  const [isLoading, setIsLoading] =
  /* istanbul ignore next */
  (cov_bt3a0bpyd().s[4]++, useState(true));
  const [user, setUser] =
  /* istanbul ignore next */
  (cov_bt3a0bpyd().s[5]++, useState(null));

  // Get current user from Amplify
  /* istanbul ignore next */
  cov_bt3a0bpyd().s[6]++;
  const getCurrentUser = async () => {
    /* istanbul ignore next */
    cov_bt3a0bpyd().f[3]++;
    cov_bt3a0bpyd().s[7]++;
    try {
      const currentUser =
      /* istanbul ignore next */
      (cov_bt3a0bpyd().s[8]++, await getAmplifyCurrentUser());
      const attributes =
      /* istanbul ignore next */
      (cov_bt3a0bpyd().s[9]++, await fetchUserAttributes());
      /* istanbul ignore next */
      cov_bt3a0bpyd().s[10]++;
      return {
        id: currentUser.userId,
        email:
        /* istanbul ignore next */
        (cov_bt3a0bpyd().b[0][0]++, attributes.email) ||
        /* istanbul ignore next */
        (cov_bt3a0bpyd().b[0][1]++, ''),
        name: attributes.name,
        given_name: attributes.given_name,
        family_name: attributes.family_name,
        roles: attributes['custom:roles'] ?
        /* istanbul ignore next */
        (cov_bt3a0bpyd().b[1][0]++, JSON.parse(attributes['custom:roles'])) :
        /* istanbul ignore next */
        (cov_bt3a0bpyd().b[1][1]++, [])
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_bt3a0bpyd().s[11]++;
      console.error('Error getting current user:', error);
      /* istanbul ignore next */
      cov_bt3a0bpyd().s[12]++;
      return null;
    }
  };
  /* istanbul ignore next */
  cov_bt3a0bpyd().s[13]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_bt3a0bpyd().f[4]++;
    let isMounted =
    /* istanbul ignore next */
    (cov_bt3a0bpyd().s[14]++, true);
    /* istanbul ignore next */
    cov_bt3a0bpyd().s[15]++;
    const checkAuth = async () => {
      /* istanbul ignore next */
      cov_bt3a0bpyd().f[5]++;
      cov_bt3a0bpyd().s[16]++;
      if (!isMounted) {
        /* istanbul ignore next */
        cov_bt3a0bpyd().b[2][0]++;
        cov_bt3a0bpyd().s[17]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_bt3a0bpyd().b[2][1]++;
      }
      cov_bt3a0bpyd().s[18]++;
      try {
        /* istanbul ignore next */
        cov_bt3a0bpyd().s[19]++;
        console.log('🔍 [AUTH-CONTEXT] Starting authentication check...');

        // Check if this is an OAuth callback
        const urlParams =
        /* istanbul ignore next */
        (cov_bt3a0bpyd().s[20]++, new URLSearchParams(window.location.search));
        const hasOAuthCode =
        /* istanbul ignore next */
        (cov_bt3a0bpyd().s[21]++, urlParams.has('code'));
        /* istanbul ignore next */
        cov_bt3a0bpyd().s[22]++;
        if (hasOAuthCode) {
          /* istanbul ignore next */
          cov_bt3a0bpyd().b[3][0]++;
          cov_bt3a0bpyd().s[23]++;
          console.log('🔄 [AUTH-CONTEXT] OAuth callback detected, giving Amplify extra time...');
          // Give Amplify more time to process OAuth callback
          /* istanbul ignore next */
          cov_bt3a0bpyd().s[24]++;
          await new Promise(resolve => {
            /* istanbul ignore next */
            cov_bt3a0bpyd().f[6]++;
            cov_bt3a0bpyd().s[25]++;
            return setTimeout(resolve, 5000);
          });
        } else {
          /* istanbul ignore next */
          cov_bt3a0bpyd().b[3][1]++;
          cov_bt3a0bpyd().s[26]++;
          // Small delay to ensure Amplify has processed any OAuth callbacks
          await new Promise(resolve => {
            /* istanbul ignore next */
            cov_bt3a0bpyd().f[7]++;
            cov_bt3a0bpyd().s[27]++;
            return setTimeout(resolve, 1000);
          });
        }

        // Check if authenticated with Amplify
        const session =
        /* istanbul ignore next */
        (cov_bt3a0bpyd().s[28]++, await fetchAuthSession({
          forceRefresh: false
        }));
        const authenticated =
        /* istanbul ignore next */
        (cov_bt3a0bpyd().s[29]++, !!session?.tokens?.idToken);
        /* istanbul ignore next */
        cov_bt3a0bpyd().s[30]++;
        console.log('🔍 [AUTH-CONTEXT] Session details:', {
          hasSession: !!session,
          hasTokens: !!session?.tokens,
          hasIdToken: !!session?.tokens?.idToken,
          hasAccessToken: !!session?.tokens?.accessToken,
          authenticated
        });
        /* istanbul ignore next */
        cov_bt3a0bpyd().s[31]++;
        if (authenticated) {
          /* istanbul ignore next */
          cov_bt3a0bpyd().b[4][0]++;
          cov_bt3a0bpyd().s[32]++;
          console.log('✅ [AUTH-CONTEXT] Session is authenticated, getting user info...');
          /* istanbul ignore next */
          cov_bt3a0bpyd().s[33]++;
          try {
            const userInfo =
            /* istanbul ignore next */
            (cov_bt3a0bpyd().s[34]++, await getCurrentUser());
            /* istanbul ignore next */
            cov_bt3a0bpyd().s[35]++;
            console.log('✅ [AUTH-CONTEXT] User info retrieved:', userInfo);
            /* istanbul ignore next */
            cov_bt3a0bpyd().s[36]++;
            if (
            /* istanbul ignore next */
            (cov_bt3a0bpyd().b[6][0]++, isMounted) &&
            /* istanbul ignore next */
            (cov_bt3a0bpyd().b[6][1]++, userInfo)) {
              /* istanbul ignore next */
              cov_bt3a0bpyd().b[5][0]++;
              cov_bt3a0bpyd().s[37]++;
              setUser(userInfo);
              /* istanbul ignore next */
              cov_bt3a0bpyd().s[38]++;
              setAuthState(true);
              /* istanbul ignore next */
              cov_bt3a0bpyd().s[39]++;
              console.log('✅ [AUTH-CONTEXT] User state updated successfully');
            } else {
              /* istanbul ignore next */
              cov_bt3a0bpyd().b[5][1]++;
              cov_bt3a0bpyd().s[40]++;
              if (isMounted) {
                /* istanbul ignore next */
                cov_bt3a0bpyd().b[7][0]++;
                cov_bt3a0bpyd().s[41]++;
                console.log('❌ [AUTH-CONTEXT] Component unmounted or no user info');
                /* istanbul ignore next */
                cov_bt3a0bpyd().s[42]++;
                setUser(null);
                /* istanbul ignore next */
                cov_bt3a0bpyd().s[43]++;
                setAuthState(false);
              } else
              /* istanbul ignore next */
              {
                cov_bt3a0bpyd().b[7][1]++;
              }
            }
          } catch (userError) {
            /* istanbul ignore next */
            cov_bt3a0bpyd().s[44]++;
            console.error('❌ [AUTH-CONTEXT] Error getting user info:', userError);
            /* istanbul ignore next */
            cov_bt3a0bpyd().s[45]++;
            if (isMounted) {
              /* istanbul ignore next */
              cov_bt3a0bpyd().b[8][0]++;
              cov_bt3a0bpyd().s[46]++;
              setUser(null);
              /* istanbul ignore next */
              cov_bt3a0bpyd().s[47]++;
              setAuthState(false);
            } else
            /* istanbul ignore next */
            {
              cov_bt3a0bpyd().b[8][1]++;
            }
          }
        } else {
          /* istanbul ignore next */
          cov_bt3a0bpyd().b[4][1]++;
          cov_bt3a0bpyd().s[48]++;
          console.log('❌ [AUTH-CONTEXT] No valid session found');
          /* istanbul ignore next */
          cov_bt3a0bpyd().s[49]++;
          if (isMounted) {
            /* istanbul ignore next */
            cov_bt3a0bpyd().b[9][0]++;
            cov_bt3a0bpyd().s[50]++;
            setUser(null);
            /* istanbul ignore next */
            cov_bt3a0bpyd().s[51]++;
            setAuthState(false);
          } else
          /* istanbul ignore next */
          {
            cov_bt3a0bpyd().b[9][1]++;
          }
        }
      } catch (error) {
        /* istanbul ignore next */
        cov_bt3a0bpyd().s[52]++;
        console.error('❌ [AUTH-CONTEXT] Auth check error:', error);
        /* istanbul ignore next */
        cov_bt3a0bpyd().s[53]++;
        if (isMounted) {
          /* istanbul ignore next */
          cov_bt3a0bpyd().b[10][0]++;
          cov_bt3a0bpyd().s[54]++;
          setUser(null);
          /* istanbul ignore next */
          cov_bt3a0bpyd().s[55]++;
          setAuthState(false);
        } else
        /* istanbul ignore next */
        {
          cov_bt3a0bpyd().b[10][1]++;
        }
      } finally {
        /* istanbul ignore next */
        cov_bt3a0bpyd().s[56]++;
        if (isMounted) {
          /* istanbul ignore next */
          cov_bt3a0bpyd().b[11][0]++;
          cov_bt3a0bpyd().s[57]++;
          setIsLoading(false);
          /* istanbul ignore next */
          cov_bt3a0bpyd().s[58]++;
          console.log('🏁 [AUTH-CONTEXT] Auth check complete. Final state:', {
            isAuthenticated,
            hasUser: !!user,
            isLoading: false
          });
        } else
        /* istanbul ignore next */
        {
          cov_bt3a0bpyd().b[11][1]++;
        }
      }
    };
    /* istanbul ignore next */
    cov_bt3a0bpyd().s[59]++;
    checkAuth();

    // Listen for storage events to sync auth state across tabs
    /* istanbul ignore next */
    cov_bt3a0bpyd().s[60]++;
    const handleStorageChange = e => {
      /* istanbul ignore next */
      cov_bt3a0bpyd().f[8]++;
      cov_bt3a0bpyd().s[61]++;
      if (e.key === 'isAuthenticated') {
        /* istanbul ignore next */
        cov_bt3a0bpyd().b[12][0]++;
        cov_bt3a0bpyd().s[62]++;
        console.log('AuthContext: Storage event detected, rechecking auth...');
        /* istanbul ignore next */
        cov_bt3a0bpyd().s[63]++;
        checkAuth();
      } else
      /* istanbul ignore next */
      {
        cov_bt3a0bpyd().b[12][1]++;
      }
    };
    /* istanbul ignore next */
    cov_bt3a0bpyd().s[64]++;
    window.addEventListener('storage', handleStorageChange);
    /* istanbul ignore next */
    cov_bt3a0bpyd().s[65]++;
    return () => {
      /* istanbul ignore next */
      cov_bt3a0bpyd().f[9]++;
      cov_bt3a0bpyd().s[66]++;
      isMounted = false;
      /* istanbul ignore next */
      cov_bt3a0bpyd().s[67]++;
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // Role checking utility
  /* istanbul ignore next */
  cov_bt3a0bpyd().s[68]++;
  const hasRole = role => {
    /* istanbul ignore next */
    cov_bt3a0bpyd().f[10]++;
    cov_bt3a0bpyd().s[69]++;
    return /* istanbul ignore next */(cov_bt3a0bpyd().b[13][0]++, user?.roles?.includes(role)) ||
    /* istanbul ignore next */
    (cov_bt3a0bpyd().b[13][1]++, false);
  };

  // Sign out function
  /* istanbul ignore next */
  cov_bt3a0bpyd().s[70]++;
  const signOut = async () => {
    /* istanbul ignore next */
    cov_bt3a0bpyd().f[11]++;
    cov_bt3a0bpyd().s[71]++;
    try {
      /* istanbul ignore next */
      cov_bt3a0bpyd().s[72]++;
      await amplifySignOut({
        global: true
      });

      // Clear localStorage flag
      /* istanbul ignore next */
      cov_bt3a0bpyd().s[73]++;
      localStorage.removeItem('isAuthenticated');

      // Clear the auth cookie
      /* istanbul ignore next */
      cov_bt3a0bpyd().s[74]++;
      document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';

      // Update state
      /* istanbul ignore next */
      cov_bt3a0bpyd().s[75]++;
      setUser(null);
      /* istanbul ignore next */
      cov_bt3a0bpyd().s[76]++;
      setAuthState(false);
      /* istanbul ignore next */
      cov_bt3a0bpyd().s[77]++;
      return true;
    } catch (error) {
      /* istanbul ignore next */
      cov_bt3a0bpyd().s[78]++;
      console.error('Error signing out:', error);

      // Even if Amplify signOut fails, clear local auth state
      /* istanbul ignore next */
      cov_bt3a0bpyd().s[79]++;
      localStorage.removeItem('isAuthenticated');
      /* istanbul ignore next */
      cov_bt3a0bpyd().s[80]++;
      document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';

      // Update state
      /* istanbul ignore next */
      cov_bt3a0bpyd().s[81]++;
      setUser(null);
      /* istanbul ignore next */
      cov_bt3a0bpyd().s[82]++;
      setAuthState(false);
      /* istanbul ignore next */
      cov_bt3a0bpyd().s[83]++;
      return true; // Return true anyway to allow navigation to login page
    }
  };
  /* istanbul ignore next */
  cov_bt3a0bpyd().s[84]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  AuthContext.Provider,
  /* istanbul ignore next */
  {
    value: {
      isAuthenticated,
      isLoading,
      user,
      signOut,
      hasRole
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 189,
      columnNumber: 5
    }
  }, children);
}

// Custom hook for using auth context
/* istanbul ignore next */
cov_bt3a0bpyd().s[85]++;
export const useAuth = () => {
  /* istanbul ignore next */
  cov_bt3a0bpyd().f[12]++;
  cov_bt3a0bpyd().s[86]++;
  return useContext(AuthContext);
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfYnQzYTBicHlkIiwiYWN0dWFsQ292ZXJhZ2UiLCJjcmVhdGVDb250ZXh0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDb250ZXh0IiwiZmV0Y2hBdXRoU2Vzc2lvbiIsInNpZ25PdXQiLCJhbXBsaWZ5U2lnbk91dCIsImdldEN1cnJlbnRVc2VyIiwiZ2V0QW1wbGlmeUN1cnJlbnRVc2VyIiwiZmV0Y2hVc2VyQXR0cmlidXRlcyIsIkF1dGhDb250ZXh0IiwicyIsImlzQXV0aGVudGljYXRlZCIsImlzTG9hZGluZyIsInVzZXIiLCJmIiwiaGFzUm9sZSIsIkF1dGhQcm92aWRlciIsImNoaWxkcmVuIiwic2V0QXV0aFN0YXRlIiwic2V0SXNMb2FkaW5nIiwic2V0VXNlciIsImN1cnJlbnRVc2VyIiwiYXR0cmlidXRlcyIsImlkIiwidXNlcklkIiwiZW1haWwiLCJiIiwibmFtZSIsImdpdmVuX25hbWUiLCJmYW1pbHlfbmFtZSIsInJvbGVzIiwiSlNPTiIsInBhcnNlIiwiZXJyb3IiLCJjb25zb2xlIiwiaXNNb3VudGVkIiwiY2hlY2tBdXRoIiwibG9nIiwidXJsUGFyYW1zIiwiVVJMU2VhcmNoUGFyYW1zIiwid2luZG93IiwibG9jYXRpb24iLCJzZWFyY2giLCJoYXNPQXV0aENvZGUiLCJoYXMiLCJQcm9taXNlIiwicmVzb2x2ZSIsInNldFRpbWVvdXQiLCJzZXNzaW9uIiwiZm9yY2VSZWZyZXNoIiwiYXV0aGVudGljYXRlZCIsInRva2VucyIsImlkVG9rZW4iLCJoYXNTZXNzaW9uIiwiaGFzVG9rZW5zIiwiaGFzSWRUb2tlbiIsImhhc0FjY2Vzc1Rva2VuIiwiYWNjZXNzVG9rZW4iLCJ1c2VySW5mbyIsInVzZXJFcnJvciIsImhhc1VzZXIiLCJoYW5kbGVTdG9yYWdlQ2hhbmdlIiwiZSIsImtleSIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwicm9sZSIsImluY2x1ZGVzIiwiZ2xvYmFsIiwibG9jYWxTdG9yYWdlIiwicmVtb3ZlSXRlbSIsImRvY3VtZW50IiwiY29va2llIiwiX19qc3giLCJQcm92aWRlciIsInZhbHVlIiwiX19zZWxmIiwiX19zb3VyY2UiLCJmaWxlTmFtZSIsIl9qc3hGaWxlTmFtZSIsImxpbmVOdW1iZXIiLCJjb2x1bW5OdW1iZXIiLCJ1c2VBdXRoIl0sInNvdXJjZXMiOlsiQXV0aENvbnRleHQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlxyXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDb250ZXh0LCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7XHJcbiAgZmV0Y2hBdXRoU2Vzc2lvbixcclxuICBzaWduT3V0IGFzIGFtcGxpZnlTaWduT3V0LFxyXG4gIGdldEN1cnJlbnRVc2VyIGFzIGdldEFtcGxpZnlDdXJyZW50VXNlcixcclxuICBmZXRjaFVzZXJBdHRyaWJ1dGVzXHJcbn0gZnJvbSAnYXdzLWFtcGxpZnkvYXV0aCc7XHJcbmltcG9ydCB7IFVzZXIgfSBmcm9tICdAL3R5cGVzL3VzZXInO1xyXG5cclxuaW50ZXJmYWNlIEF1dGhDb250ZXh0VHlwZSB7XHJcbiAgaXNBdXRoZW50aWNhdGVkOiBib29sZWFuO1xyXG4gIGlzTG9hZGluZzogYm9vbGVhbjtcclxuICB1c2VyOiBVc2VyIHwgbnVsbDtcclxuICBzaWduT3V0OiAoKSA9PiBQcm9taXNlPGJvb2xlYW4+O1xyXG4gIGhhc1JvbGU6IChyb2xlOiBzdHJpbmcpID0+IGJvb2xlYW47XHJcbn1cclxuXHJcbmNvbnN0IEF1dGhDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxBdXRoQ29udGV4dFR5cGU+KHtcclxuICBpc0F1dGhlbnRpY2F0ZWQ6IGZhbHNlLFxyXG4gIGlzTG9hZGluZzogdHJ1ZSxcclxuICB1c2VyOiBudWxsLFxyXG4gIHNpZ25PdXQ6IGFzeW5jICgpID0+IGZhbHNlLFxyXG4gIGhhc1JvbGU6ICgpID0+IGZhbHNlLFxyXG59KTtcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBBdXRoUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdE5vZGUgfSkge1xyXG4gIGNvbnN0IFtpc0F1dGhlbnRpY2F0ZWQsIHNldEF1dGhTdGF0ZV0gPSB1c2VTdGF0ZTxib29sZWFuPihmYWxzZSk7XHJcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlPGJvb2xlYW4+KHRydWUpO1xyXG4gIGNvbnN0IFt1c2VyLCBzZXRVc2VyXSA9IHVzZVN0YXRlPFVzZXIgfCBudWxsPihudWxsKTtcclxuXHJcbiAgLy8gR2V0IGN1cnJlbnQgdXNlciBmcm9tIEFtcGxpZnlcclxuICBjb25zdCBnZXRDdXJyZW50VXNlciA9IGFzeW5jICgpOiBQcm9taXNlPFVzZXIgfCBudWxsPiA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBjdXJyZW50VXNlciA9IGF3YWl0IGdldEFtcGxpZnlDdXJyZW50VXNlcigpO1xyXG4gICAgICBjb25zdCBhdHRyaWJ1dGVzID0gYXdhaXQgZmV0Y2hVc2VyQXR0cmlidXRlcygpO1xyXG4gICAgICBcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBpZDogY3VycmVudFVzZXIudXNlcklkLFxyXG4gICAgICAgIGVtYWlsOiBhdHRyaWJ1dGVzLmVtYWlsIHx8ICcnLFxyXG4gICAgICAgIG5hbWU6IGF0dHJpYnV0ZXMubmFtZSxcclxuICAgICAgICBnaXZlbl9uYW1lOiBhdHRyaWJ1dGVzLmdpdmVuX25hbWUsXHJcbiAgICAgICAgZmFtaWx5X25hbWU6IGF0dHJpYnV0ZXMuZmFtaWx5X25hbWUsXHJcbiAgICAgICAgcm9sZXM6IGF0dHJpYnV0ZXNbJ2N1c3RvbTpyb2xlcyddID8gSlNPTi5wYXJzZShhdHRyaWJ1dGVzWydjdXN0b206cm9sZXMnXSkgOiBbXVxyXG4gICAgICB9O1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2V0dGluZyBjdXJyZW50IHVzZXI6JywgZXJyb3IpO1xyXG4gICAgICByZXR1cm4gbnVsbDtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgbGV0IGlzTW91bnRlZCA9IHRydWU7XHJcblxyXG4gICAgY29uc3QgY2hlY2tBdXRoID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICBpZiAoIWlzTW91bnRlZCkgcmV0dXJuO1xyXG5cclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbQVVUSC1DT05URVhUXSBTdGFydGluZyBhdXRoZW50aWNhdGlvbiBjaGVjay4uLicpO1xyXG5cclxuICAgICAgICAvLyBDaGVjayBpZiB0aGlzIGlzIGFuIE9BdXRoIGNhbGxiYWNrXHJcbiAgICAgICAgY29uc3QgdXJsUGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyh3aW5kb3cubG9jYXRpb24uc2VhcmNoKVxyXG4gICAgICAgIGNvbnN0IGhhc09BdXRoQ29kZSA9IHVybFBhcmFtcy5oYXMoJ2NvZGUnKVxyXG5cclxuICAgICAgICBpZiAoaGFzT0F1dGhDb2RlKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+UhCBbQVVUSC1DT05URVhUXSBPQXV0aCBjYWxsYmFjayBkZXRlY3RlZCwgZ2l2aW5nIEFtcGxpZnkgZXh0cmEgdGltZS4uLicpO1xyXG4gICAgICAgICAgLy8gR2l2ZSBBbXBsaWZ5IG1vcmUgdGltZSB0byBwcm9jZXNzIE9BdXRoIGNhbGxiYWNrXHJcbiAgICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgNTAwMCkpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAvLyBTbWFsbCBkZWxheSB0byBlbnN1cmUgQW1wbGlmeSBoYXMgcHJvY2Vzc2VkIGFueSBPQXV0aCBjYWxsYmFja3NcclxuICAgICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDAwKSk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBDaGVjayBpZiBhdXRoZW50aWNhdGVkIHdpdGggQW1wbGlmeVxyXG4gICAgICAgIGNvbnN0IHNlc3Npb24gPSBhd2FpdCBmZXRjaEF1dGhTZXNzaW9uKHsgZm9yY2VSZWZyZXNoOiBmYWxzZSB9KTtcclxuICAgICAgICBjb25zdCBhdXRoZW50aWNhdGVkID0gISFzZXNzaW9uPy50b2tlbnM/LmlkVG9rZW47XHJcblxyXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtBVVRILUNPTlRFWFRdIFNlc3Npb24gZGV0YWlsczonLCB7XHJcbiAgICAgICAgICBoYXNTZXNzaW9uOiAhIXNlc3Npb24sXHJcbiAgICAgICAgICBoYXNUb2tlbnM6ICEhc2Vzc2lvbj8udG9rZW5zLFxyXG4gICAgICAgICAgaGFzSWRUb2tlbjogISFzZXNzaW9uPy50b2tlbnM/LmlkVG9rZW4sXHJcbiAgICAgICAgICBoYXNBY2Nlc3NUb2tlbjogISFzZXNzaW9uPy50b2tlbnM/LmFjY2Vzc1Rva2VuLFxyXG4gICAgICAgICAgYXV0aGVudGljYXRlZFxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICBpZiAoYXV0aGVudGljYXRlZCkge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ+KchSBbQVVUSC1DT05URVhUXSBTZXNzaW9uIGlzIGF1dGhlbnRpY2F0ZWQsIGdldHRpbmcgdXNlciBpbmZvLi4uJyk7XHJcblxyXG4gICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgY29uc3QgdXNlckluZm8gPSBhd2FpdCBnZXRDdXJyZW50VXNlcigpO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIFtBVVRILUNPTlRFWFRdIFVzZXIgaW5mbyByZXRyaWV2ZWQ6JywgdXNlckluZm8pO1xyXG5cclxuICAgICAgICAgICAgaWYgKGlzTW91bnRlZCAmJiB1c2VySW5mbykge1xyXG4gICAgICAgICAgICAgIHNldFVzZXIodXNlckluZm8pO1xyXG4gICAgICAgICAgICAgIHNldEF1dGhTdGF0ZSh0cnVlKTtcclxuICAgICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIFtBVVRILUNPTlRFWFRdIFVzZXIgc3RhdGUgdXBkYXRlZCBzdWNjZXNzZnVsbHknKTtcclxuICAgICAgICAgICAgfSBlbHNlIGlmIChpc01vdW50ZWQpIHtcclxuICAgICAgICAgICAgICBjb25zb2xlLmxvZygn4p2MIFtBVVRILUNPTlRFWFRdIENvbXBvbmVudCB1bm1vdW50ZWQgb3Igbm8gdXNlciBpbmZvJyk7XHJcbiAgICAgICAgICAgICAgc2V0VXNlcihudWxsKTtcclxuICAgICAgICAgICAgICBzZXRBdXRoU3RhdGUoZmFsc2UpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9IGNhdGNoICh1c2VyRXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFtBVVRILUNPTlRFWFRdIEVycm9yIGdldHRpbmcgdXNlciBpbmZvOicsIHVzZXJFcnJvcik7XHJcbiAgICAgICAgICAgIGlmIChpc01vdW50ZWQpIHtcclxuICAgICAgICAgICAgICBzZXRVc2VyKG51bGwpO1xyXG4gICAgICAgICAgICAgIHNldEF1dGhTdGF0ZShmYWxzZSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ+KdjCBbQVVUSC1DT05URVhUXSBObyB2YWxpZCBzZXNzaW9uIGZvdW5kJyk7XHJcbiAgICAgICAgICBpZiAoaXNNb3VudGVkKSB7XHJcbiAgICAgICAgICAgIHNldFVzZXIobnVsbCk7XHJcbiAgICAgICAgICAgIHNldEF1dGhTdGF0ZShmYWxzZSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBbQVVUSC1DT05URVhUXSBBdXRoIGNoZWNrIGVycm9yOicsIGVycm9yKTtcclxuICAgICAgICBpZiAoaXNNb3VudGVkKSB7XHJcbiAgICAgICAgICBzZXRVc2VyKG51bGwpO1xyXG4gICAgICAgICAgc2V0QXV0aFN0YXRlKGZhbHNlKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0gZmluYWxseSB7XHJcbiAgICAgICAgaWYgKGlzTW91bnRlZCkge1xyXG4gICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn4+BIFtBVVRILUNPTlRFWFRdIEF1dGggY2hlY2sgY29tcGxldGUuIEZpbmFsIHN0YXRlOicsIHtcclxuICAgICAgICAgICAgaXNBdXRoZW50aWNhdGVkLFxyXG4gICAgICAgICAgICBoYXNVc2VyOiAhIXVzZXIsXHJcbiAgICAgICAgICAgIGlzTG9hZGluZzogZmFsc2VcclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBjaGVja0F1dGgoKTtcclxuICAgIFxyXG4gICAgLy8gTGlzdGVuIGZvciBzdG9yYWdlIGV2ZW50cyB0byBzeW5jIGF1dGggc3RhdGUgYWNyb3NzIHRhYnNcclxuICAgIGNvbnN0IGhhbmRsZVN0b3JhZ2VDaGFuZ2UgPSAoZTogU3RvcmFnZUV2ZW50KSA9PiB7XHJcbiAgICAgIGlmIChlLmtleSA9PT0gJ2lzQXV0aGVudGljYXRlZCcpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnQXV0aENvbnRleHQ6IFN0b3JhZ2UgZXZlbnQgZGV0ZWN0ZWQsIHJlY2hlY2tpbmcgYXV0aC4uLicpO1xyXG4gICAgICAgIGNoZWNrQXV0aCgpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gICAgXHJcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignc3RvcmFnZScsIGhhbmRsZVN0b3JhZ2VDaGFuZ2UpO1xyXG4gICAgXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBpc01vdW50ZWQgPSBmYWxzZTtcclxuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3N0b3JhZ2UnLCBoYW5kbGVTdG9yYWdlQ2hhbmdlKTtcclxuICAgIH07XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyBSb2xlIGNoZWNraW5nIHV0aWxpdHlcclxuICBjb25zdCBoYXNSb2xlID0gKHJvbGU6IHN0cmluZykgPT4ge1xyXG4gICAgcmV0dXJuIHVzZXI/LnJvbGVzPy5pbmNsdWRlcyhyb2xlKSB8fCBmYWxzZTtcclxuICB9O1xyXG5cclxuICAvLyBTaWduIG91dCBmdW5jdGlvblxyXG4gIGNvbnN0IHNpZ25PdXQgPSBhc3luYyAoKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBhd2FpdCBhbXBsaWZ5U2lnbk91dCh7IGdsb2JhbDogdHJ1ZSB9KTtcclxuICAgICAgXHJcbiAgICAgIC8vIENsZWFyIGxvY2FsU3RvcmFnZSBmbGFnXHJcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdpc0F1dGhlbnRpY2F0ZWQnKTtcclxuICAgICAgXHJcbiAgICAgIC8vIENsZWFyIHRoZSBhdXRoIGNvb2tpZVxyXG4gICAgICBkb2N1bWVudC5jb29raWUgPSAnaWRUb2tlbj07IHBhdGg9LzsgbWF4LWFnZT0wOyBTYW1lU2l0ZT1MYXgnO1xyXG4gICAgICBcclxuICAgICAgLy8gVXBkYXRlIHN0YXRlXHJcbiAgICAgIHNldFVzZXIobnVsbCk7XHJcbiAgICAgIHNldEF1dGhTdGF0ZShmYWxzZSk7XHJcbiAgICAgIFxyXG4gICAgICByZXR1cm4gdHJ1ZTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNpZ25pbmcgb3V0OicsIGVycm9yKTtcclxuICAgICAgXHJcbiAgICAgIC8vIEV2ZW4gaWYgQW1wbGlmeSBzaWduT3V0IGZhaWxzLCBjbGVhciBsb2NhbCBhdXRoIHN0YXRlXHJcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdpc0F1dGhlbnRpY2F0ZWQnKTtcclxuICAgICAgZG9jdW1lbnQuY29va2llID0gJ2lkVG9rZW49OyBwYXRoPS87IG1heC1hZ2U9MDsgU2FtZVNpdGU9TGF4JztcclxuICAgICAgXHJcbiAgICAgIC8vIFVwZGF0ZSBzdGF0ZVxyXG4gICAgICBzZXRVc2VyKG51bGwpO1xyXG4gICAgICBzZXRBdXRoU3RhdGUoZmFsc2UpO1xyXG4gICAgICBcclxuICAgICAgcmV0dXJuIHRydWU7IC8vIFJldHVybiB0cnVlIGFueXdheSB0byBhbGxvdyBuYXZpZ2F0aW9uIHRvIGxvZ2luIHBhZ2VcclxuICAgIH1cclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPEF1dGhDb250ZXh0LlByb3ZpZGVyXHJcbiAgICAgIHZhbHVlPXt7XHJcbiAgICAgICAgaXNBdXRoZW50aWNhdGVkLFxyXG4gICAgICAgIGlzTG9hZGluZyxcclxuICAgICAgICB1c2VyLFxyXG4gICAgICAgIHNpZ25PdXQsXHJcbiAgICAgICAgaGFzUm9sZSxcclxuICAgICAgfX1cclxuICAgID5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9BdXRoQ29udGV4dC5Qcm92aWRlcj5cclxuICApO1xyXG59XHJcblxyXG4vLyBDdXN0b20gaG9vayBmb3IgdXNpbmcgYXV0aCBjb250ZXh0XHJcbmV4cG9ydCBjb25zdCB1c2VBdXRoID0gKCkgPT4gdXNlQ29udGV4dChBdXRoQ29udGV4dCk7XHJcblxyXG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBZVk7SUFBQUEsYUFBQSxZQUFBQSxDQUFBO01BQUEsT0FBQUMsY0FBQTtJQUFBO0VBQUE7RUFBQSxPQUFBQSxjQUFBO0FBQUE7QUFBQUQsYUFBQTtBQWRaLFNBQVNFLGFBQWEsRUFBRUMsUUFBUSxFQUFFQyxTQUFTLEVBQUVDLFVBQVUsUUFBbUIsT0FBTztBQUNqRixTQUNFQyxnQkFBZ0IsRUFDaEJDLE9BQU8sSUFBSUMsY0FBYyxFQUN6QkMsY0FBYyxJQUFJQyxxQkFBcUIsRUFDdkNDLG1CQUFtQixRQUNkLGtCQUFrQjtBQVd6QixNQUFNQyxXQUFXO0FBQUE7QUFBQSxDQUFBWixhQUFBLEdBQUFhLENBQUEsb0JBQUdYLGFBQWEsQ0FBa0I7RUFDakRZLGVBQWUsRUFBRSxLQUFLO0VBQ3RCQyxTQUFTLEVBQUUsSUFBSTtFQUNmQyxJQUFJLEVBQUUsSUFBSTtFQUNWVCxPQUFPLEVBQUUsTUFBQUEsQ0FBQSxLQUFZO0lBQUE7SUFBQVAsYUFBQSxHQUFBaUIsQ0FBQTtJQUFBakIsYUFBQSxHQUFBYSxDQUFBO0lBQUEsWUFBSztFQUFELENBQUM7RUFDMUJLLE9BQU8sRUFBRUEsQ0FBQSxLQUFNO0lBQUE7SUFBQWxCLGFBQUEsR0FBQWlCLENBQUE7SUFBQWpCLGFBQUEsR0FBQWEsQ0FBQTtJQUFBLFlBQUs7RUFBRDtBQUNyQixDQUFDLENBQUM7QUFFRixPQUFPLFNBQVNNLFlBQVlBLENBQUM7RUFBRUM7QUFBa0MsQ0FBQyxFQUFFO0VBQUE7RUFBQXBCLGFBQUEsR0FBQWlCLENBQUE7RUFDbEUsTUFBTSxDQUFDSCxlQUFlLEVBQUVPLFlBQVksQ0FBQztFQUFBO0VBQUEsQ0FBQXJCLGFBQUEsR0FBQWEsQ0FBQSxPQUFHVixRQUFRLENBQVUsS0FBSyxDQUFDO0VBQ2hFLE1BQU0sQ0FBQ1ksU0FBUyxFQUFFTyxZQUFZLENBQUM7RUFBQTtFQUFBLENBQUF0QixhQUFBLEdBQUFhLENBQUEsT0FBR1YsUUFBUSxDQUFVLElBQUksQ0FBQztFQUN6RCxNQUFNLENBQUNhLElBQUksRUFBRU8sT0FBTyxDQUFDO0VBQUE7RUFBQSxDQUFBdkIsYUFBQSxHQUFBYSxDQUFBLE9BQUdWLFFBQVEsQ0FBYyxJQUFJLENBQUM7O0VBRW5EO0VBQUE7RUFBQUgsYUFBQSxHQUFBYSxDQUFBO0VBQ0EsTUFBTUosY0FBYyxHQUFHLE1BQUFBLENBQUEsS0FBa0M7SUFBQTtJQUFBVCxhQUFBLEdBQUFpQixDQUFBO0lBQUFqQixhQUFBLEdBQUFhLENBQUE7SUFDdkQsSUFBSTtNQUNGLE1BQU1XLFdBQVc7TUFBQTtNQUFBLENBQUF4QixhQUFBLEdBQUFhLENBQUEsT0FBRyxNQUFNSCxxQkFBcUIsQ0FBQyxDQUFDO01BQ2pELE1BQU1lLFVBQVU7TUFBQTtNQUFBLENBQUF6QixhQUFBLEdBQUFhLENBQUEsT0FBRyxNQUFNRixtQkFBbUIsQ0FBQyxDQUFDO01BQUM7TUFBQVgsYUFBQSxHQUFBYSxDQUFBO01BRS9DLE9BQU87UUFDTGEsRUFBRSxFQUFFRixXQUFXLENBQUNHLE1BQU07UUFDdEJDLEtBQUs7UUFBRTtRQUFBLENBQUE1QixhQUFBLEdBQUE2QixDQUFBLFVBQUFKLFVBQVUsQ0FBQ0csS0FBSztRQUFBO1FBQUEsQ0FBQTVCLGFBQUEsR0FBQTZCLENBQUEsVUFBSSxFQUFFO1FBQzdCQyxJQUFJLEVBQUVMLFVBQVUsQ0FBQ0ssSUFBSTtRQUNyQkMsVUFBVSxFQUFFTixVQUFVLENBQUNNLFVBQVU7UUFDakNDLFdBQVcsRUFBRVAsVUFBVSxDQUFDTyxXQUFXO1FBQ25DQyxLQUFLLEVBQUVSLFVBQVUsQ0FBQyxjQUFjLENBQUM7UUFBQTtRQUFBLENBQUF6QixhQUFBLEdBQUE2QixDQUFBLFVBQUdLLElBQUksQ0FBQ0MsS0FBSyxDQUFDVixVQUFVLENBQUMsY0FBYyxDQUFDLENBQUM7UUFBQTtRQUFBLENBQUF6QixhQUFBLEdBQUE2QixDQUFBLFVBQUcsRUFBRTtNQUNqRixDQUFDO0lBQ0gsQ0FBQyxDQUFDLE9BQU9PLEtBQUssRUFBRTtNQUFBO01BQUFwQyxhQUFBLEdBQUFhLENBQUE7TUFDZHdCLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLDZCQUE2QixFQUFFQSxLQUFLLENBQUM7TUFBQztNQUFBcEMsYUFBQSxHQUFBYSxDQUFBO01BQ3BELE9BQU8sSUFBSTtJQUNiO0VBQ0YsQ0FBQztFQUFDO0VBQUFiLGFBQUEsR0FBQWEsQ0FBQTtFQUVGVCxTQUFTLENBQUMsTUFBTTtJQUFBO0lBQUFKLGFBQUEsR0FBQWlCLENBQUE7SUFDZCxJQUFJcUIsU0FBUztJQUFBO0lBQUEsQ0FBQXRDLGFBQUEsR0FBQWEsQ0FBQSxRQUFHLElBQUk7SUFBQztJQUFBYixhQUFBLEdBQUFhLENBQUE7SUFFckIsTUFBTTBCLFNBQVMsR0FBRyxNQUFBQSxDQUFBLEtBQVk7TUFBQTtNQUFBdkMsYUFBQSxHQUFBaUIsQ0FBQTtNQUFBakIsYUFBQSxHQUFBYSxDQUFBO01BQzVCLElBQUksQ0FBQ3lCLFNBQVMsRUFBRTtRQUFBO1FBQUF0QyxhQUFBLEdBQUE2QixDQUFBO1FBQUE3QixhQUFBLEdBQUFhLENBQUE7UUFBQTtNQUFNLENBQUM7TUFBQTtNQUFBO1FBQUFiLGFBQUEsR0FBQTZCLENBQUE7TUFBQTtNQUFBN0IsYUFBQSxHQUFBYSxDQUFBO01BRXZCLElBQUk7UUFBQTtRQUFBYixhQUFBLEdBQUFhLENBQUE7UUFDRndCLE9BQU8sQ0FBQ0csR0FBRyxDQUFDLG9EQUFvRCxDQUFDOztRQUVqRTtRQUNBLE1BQU1DLFNBQVM7UUFBQTtRQUFBLENBQUF6QyxhQUFBLEdBQUFhLENBQUEsUUFBRyxJQUFJNkIsZUFBZSxDQUFDQyxNQUFNLENBQUNDLFFBQVEsQ0FBQ0MsTUFBTSxDQUFDO1FBQzdELE1BQU1DLFlBQVk7UUFBQTtRQUFBLENBQUE5QyxhQUFBLEdBQUFhLENBQUEsUUFBRzRCLFNBQVMsQ0FBQ00sR0FBRyxDQUFDLE1BQU0sQ0FBQztRQUFBO1FBQUEvQyxhQUFBLEdBQUFhLENBQUE7UUFFMUMsSUFBSWlDLFlBQVksRUFBRTtVQUFBO1VBQUE5QyxhQUFBLEdBQUE2QixDQUFBO1VBQUE3QixhQUFBLEdBQUFhLENBQUE7VUFDaEJ3QixPQUFPLENBQUNHLEdBQUcsQ0FBQyx5RUFBeUUsQ0FBQztVQUN0RjtVQUFBO1VBQUF4QyxhQUFBLEdBQUFhLENBQUE7VUFDQSxNQUFNLElBQUltQyxPQUFPLENBQUNDLE9BQU8sSUFBSTtZQUFBO1lBQUFqRCxhQUFBLEdBQUFpQixDQUFBO1lBQUFqQixhQUFBLEdBQUFhLENBQUE7WUFBQSxPQUFBcUMsVUFBVSxDQUFDRCxPQUFPLEVBQUUsSUFBSSxDQUFDO1VBQUQsQ0FBQyxDQUFDO1FBQ3pELENBQUMsTUFBTTtVQUFBO1VBQUFqRCxhQUFBLEdBQUE2QixDQUFBO1VBQUE3QixhQUFBLEdBQUFhLENBQUE7VUFDTDtVQUNBLE1BQU0sSUFBSW1DLE9BQU8sQ0FBQ0MsT0FBTyxJQUFJO1lBQUE7WUFBQWpELGFBQUEsR0FBQWlCLENBQUE7WUFBQWpCLGFBQUEsR0FBQWEsQ0FBQTtZQUFBLE9BQUFxQyxVQUFVLENBQUNELE9BQU8sRUFBRSxJQUFJLENBQUM7VUFBRCxDQUFDLENBQUM7UUFDekQ7O1FBRUE7UUFDQSxNQUFNRSxPQUFPO1FBQUE7UUFBQSxDQUFBbkQsYUFBQSxHQUFBYSxDQUFBLFFBQUcsTUFBTVAsZ0JBQWdCLENBQUM7VUFBRThDLFlBQVksRUFBRTtRQUFNLENBQUMsQ0FBQztRQUMvRCxNQUFNQyxhQUFhO1FBQUE7UUFBQSxDQUFBckQsYUFBQSxHQUFBYSxDQUFBLFFBQUcsQ0FBQyxDQUFDc0MsT0FBTyxFQUFFRyxNQUFNLEVBQUVDLE9BQU87UUFBQztRQUFBdkQsYUFBQSxHQUFBYSxDQUFBO1FBRWpEd0IsT0FBTyxDQUFDRyxHQUFHLENBQUMsb0NBQW9DLEVBQUU7VUFDaERnQixVQUFVLEVBQUUsQ0FBQyxDQUFDTCxPQUFPO1VBQ3JCTSxTQUFTLEVBQUUsQ0FBQyxDQUFDTixPQUFPLEVBQUVHLE1BQU07VUFDNUJJLFVBQVUsRUFBRSxDQUFDLENBQUNQLE9BQU8sRUFBRUcsTUFBTSxFQUFFQyxPQUFPO1VBQ3RDSSxjQUFjLEVBQUUsQ0FBQyxDQUFDUixPQUFPLEVBQUVHLE1BQU0sRUFBRU0sV0FBVztVQUM5Q1A7UUFDRixDQUFDLENBQUM7UUFBQztRQUFBckQsYUFBQSxHQUFBYSxDQUFBO1FBRUgsSUFBSXdDLGFBQWEsRUFBRTtVQUFBO1VBQUFyRCxhQUFBLEdBQUE2QixDQUFBO1VBQUE3QixhQUFBLEdBQUFhLENBQUE7VUFDakJ3QixPQUFPLENBQUNHLEdBQUcsQ0FBQyxpRUFBaUUsQ0FBQztVQUFDO1VBQUF4QyxhQUFBLEdBQUFhLENBQUE7VUFFL0UsSUFBSTtZQUNGLE1BQU1nRCxRQUFRO1lBQUE7WUFBQSxDQUFBN0QsYUFBQSxHQUFBYSxDQUFBLFFBQUcsTUFBTUosY0FBYyxDQUFDLENBQUM7WUFBQztZQUFBVCxhQUFBLEdBQUFhLENBQUE7WUFDeEN3QixPQUFPLENBQUNHLEdBQUcsQ0FBQyx1Q0FBdUMsRUFBRXFCLFFBQVEsQ0FBQztZQUFDO1lBQUE3RCxhQUFBLEdBQUFhLENBQUE7WUFFL0Q7WUFBSTtZQUFBLENBQUFiLGFBQUEsR0FBQTZCLENBQUEsVUFBQVMsU0FBUztZQUFBO1lBQUEsQ0FBQXRDLGFBQUEsR0FBQTZCLENBQUEsVUFBSWdDLFFBQVEsR0FBRTtjQUFBO2NBQUE3RCxhQUFBLEdBQUE2QixDQUFBO2NBQUE3QixhQUFBLEdBQUFhLENBQUE7Y0FDekJVLE9BQU8sQ0FBQ3NDLFFBQVEsQ0FBQztjQUFDO2NBQUE3RCxhQUFBLEdBQUFhLENBQUE7Y0FDbEJRLFlBQVksQ0FBQyxJQUFJLENBQUM7Y0FBQztjQUFBckIsYUFBQSxHQUFBYSxDQUFBO2NBQ25Cd0IsT0FBTyxDQUFDRyxHQUFHLENBQUMsa0RBQWtELENBQUM7WUFDakUsQ0FBQyxNQUFNO2NBQUE7Y0FBQXhDLGFBQUEsR0FBQTZCLENBQUE7Y0FBQTdCLGFBQUEsR0FBQWEsQ0FBQTtjQUFBLElBQUl5QixTQUFTLEVBQUU7Z0JBQUE7Z0JBQUF0QyxhQUFBLEdBQUE2QixDQUFBO2dCQUFBN0IsYUFBQSxHQUFBYSxDQUFBO2dCQUNwQndCLE9BQU8sQ0FBQ0csR0FBRyxDQUFDLHNEQUFzRCxDQUFDO2dCQUFDO2dCQUFBeEMsYUFBQSxHQUFBYSxDQUFBO2dCQUNwRVUsT0FBTyxDQUFDLElBQUksQ0FBQztnQkFBQztnQkFBQXZCLGFBQUEsR0FBQWEsQ0FBQTtnQkFDZFEsWUFBWSxDQUFDLEtBQUssQ0FBQztjQUNyQixDQUFDO2NBQUE7Y0FBQTtnQkFBQXJCLGFBQUEsR0FBQTZCLENBQUE7Y0FBQTtZQUFEO1VBQ0YsQ0FBQyxDQUFDLE9BQU9pQyxTQUFTLEVBQUU7WUFBQTtZQUFBOUQsYUFBQSxHQUFBYSxDQUFBO1lBQ2xCd0IsT0FBTyxDQUFDRCxLQUFLLENBQUMsMkNBQTJDLEVBQUUwQixTQUFTLENBQUM7WUFBQztZQUFBOUQsYUFBQSxHQUFBYSxDQUFBO1lBQ3RFLElBQUl5QixTQUFTLEVBQUU7Y0FBQTtjQUFBdEMsYUFBQSxHQUFBNkIsQ0FBQTtjQUFBN0IsYUFBQSxHQUFBYSxDQUFBO2NBQ2JVLE9BQU8sQ0FBQyxJQUFJLENBQUM7Y0FBQztjQUFBdkIsYUFBQSxHQUFBYSxDQUFBO2NBQ2RRLFlBQVksQ0FBQyxLQUFLLENBQUM7WUFDckIsQ0FBQztZQUFBO1lBQUE7Y0FBQXJCLGFBQUEsR0FBQTZCLENBQUE7WUFBQTtVQUNIO1FBQ0YsQ0FBQyxNQUFNO1VBQUE7VUFBQTdCLGFBQUEsR0FBQTZCLENBQUE7VUFBQTdCLGFBQUEsR0FBQWEsQ0FBQTtVQUNMd0IsT0FBTyxDQUFDRyxHQUFHLENBQUMseUNBQXlDLENBQUM7VUFBQztVQUFBeEMsYUFBQSxHQUFBYSxDQUFBO1VBQ3ZELElBQUl5QixTQUFTLEVBQUU7WUFBQTtZQUFBdEMsYUFBQSxHQUFBNkIsQ0FBQTtZQUFBN0IsYUFBQSxHQUFBYSxDQUFBO1lBQ2JVLE9BQU8sQ0FBQyxJQUFJLENBQUM7WUFBQztZQUFBdkIsYUFBQSxHQUFBYSxDQUFBO1lBQ2RRLFlBQVksQ0FBQyxLQUFLLENBQUM7VUFDckIsQ0FBQztVQUFBO1VBQUE7WUFBQXJCLGFBQUEsR0FBQTZCLENBQUE7VUFBQTtRQUNIO01BQ0YsQ0FBQyxDQUFDLE9BQU9PLEtBQUssRUFBRTtRQUFBO1FBQUFwQyxhQUFBLEdBQUFhLENBQUE7UUFDZHdCLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLG9DQUFvQyxFQUFFQSxLQUFLLENBQUM7UUFBQztRQUFBcEMsYUFBQSxHQUFBYSxDQUFBO1FBQzNELElBQUl5QixTQUFTLEVBQUU7VUFBQTtVQUFBdEMsYUFBQSxHQUFBNkIsQ0FBQTtVQUFBN0IsYUFBQSxHQUFBYSxDQUFBO1VBQ2JVLE9BQU8sQ0FBQyxJQUFJLENBQUM7VUFBQztVQUFBdkIsYUFBQSxHQUFBYSxDQUFBO1VBQ2RRLFlBQVksQ0FBQyxLQUFLLENBQUM7UUFDckIsQ0FBQztRQUFBO1FBQUE7VUFBQXJCLGFBQUEsR0FBQTZCLENBQUE7UUFBQTtNQUNILENBQUMsU0FBUztRQUFBO1FBQUE3QixhQUFBLEdBQUFhLENBQUE7UUFDUixJQUFJeUIsU0FBUyxFQUFFO1VBQUE7VUFBQXRDLGFBQUEsR0FBQTZCLENBQUE7VUFBQTdCLGFBQUEsR0FBQWEsQ0FBQTtVQUNiUyxZQUFZLENBQUMsS0FBSyxDQUFDO1VBQUM7VUFBQXRCLGFBQUEsR0FBQWEsQ0FBQTtVQUNwQndCLE9BQU8sQ0FBQ0csR0FBRyxDQUFDLHFEQUFxRCxFQUFFO1lBQ2pFMUIsZUFBZTtZQUNmaUQsT0FBTyxFQUFFLENBQUMsQ0FBQy9DLElBQUk7WUFDZkQsU0FBUyxFQUFFO1VBQ2IsQ0FBQyxDQUFDO1FBQ0osQ0FBQztRQUFBO1FBQUE7VUFBQWYsYUFBQSxHQUFBNkIsQ0FBQTtRQUFBO01BQ0g7SUFDRixDQUFDO0lBQUM7SUFBQTdCLGFBQUEsR0FBQWEsQ0FBQTtJQUVGMEIsU0FBUyxDQUFDLENBQUM7O0lBRVg7SUFBQTtJQUFBdkMsYUFBQSxHQUFBYSxDQUFBO0lBQ0EsTUFBTW1ELG1CQUFtQixHQUFJQyxDQUFlLElBQUs7TUFBQTtNQUFBakUsYUFBQSxHQUFBaUIsQ0FBQTtNQUFBakIsYUFBQSxHQUFBYSxDQUFBO01BQy9DLElBQUlvRCxDQUFDLENBQUNDLEdBQUcsS0FBSyxpQkFBaUIsRUFBRTtRQUFBO1FBQUFsRSxhQUFBLEdBQUE2QixDQUFBO1FBQUE3QixhQUFBLEdBQUFhLENBQUE7UUFDL0J3QixPQUFPLENBQUNHLEdBQUcsQ0FBQyx5REFBeUQsQ0FBQztRQUFDO1FBQUF4QyxhQUFBLEdBQUFhLENBQUE7UUFDdkUwQixTQUFTLENBQUMsQ0FBQztNQUNiLENBQUM7TUFBQTtNQUFBO1FBQUF2QyxhQUFBLEdBQUE2QixDQUFBO01BQUE7SUFDSCxDQUFDO0lBQUM7SUFBQTdCLGFBQUEsR0FBQWEsQ0FBQTtJQUVGOEIsTUFBTSxDQUFDd0IsZ0JBQWdCLENBQUMsU0FBUyxFQUFFSCxtQkFBbUIsQ0FBQztJQUFDO0lBQUFoRSxhQUFBLEdBQUFhLENBQUE7SUFFeEQsT0FBTyxNQUFNO01BQUE7TUFBQWIsYUFBQSxHQUFBaUIsQ0FBQTtNQUFBakIsYUFBQSxHQUFBYSxDQUFBO01BQ1h5QixTQUFTLEdBQUcsS0FBSztNQUFDO01BQUF0QyxhQUFBLEdBQUFhLENBQUE7TUFDbEI4QixNQUFNLENBQUN5QixtQkFBbUIsQ0FBQyxTQUFTLEVBQUVKLG1CQUFtQixDQUFDO0lBQzVELENBQUM7RUFDSCxDQUFDLEVBQUUsRUFBRSxDQUFDOztFQUVOO0VBQUE7RUFBQWhFLGFBQUEsR0FBQWEsQ0FBQTtFQUNBLE1BQU1LLE9BQU8sR0FBSW1ELElBQVksSUFBSztJQUFBO0lBQUFyRSxhQUFBLEdBQUFpQixDQUFBO0lBQUFqQixhQUFBLEdBQUFhLENBQUE7SUFDaEMsT0FBTywyQkFBQWIsYUFBQSxHQUFBNkIsQ0FBQSxXQUFBYixJQUFJLEVBQUVpQixLQUFLLEVBQUVxQyxRQUFRLENBQUNELElBQUksQ0FBQztJQUFBO0lBQUEsQ0FBQXJFLGFBQUEsR0FBQTZCLENBQUEsV0FBSSxLQUFLO0VBQzdDLENBQUM7O0VBRUQ7RUFBQTtFQUFBN0IsYUFBQSxHQUFBYSxDQUFBO0VBQ0EsTUFBTU4sT0FBTyxHQUFHLE1BQUFBLENBQUEsS0FBOEI7SUFBQTtJQUFBUCxhQUFBLEdBQUFpQixDQUFBO0lBQUFqQixhQUFBLEdBQUFhLENBQUE7SUFDNUMsSUFBSTtNQUFBO01BQUFiLGFBQUEsR0FBQWEsQ0FBQTtNQUNGLE1BQU1MLGNBQWMsQ0FBQztRQUFFK0QsTUFBTSxFQUFFO01BQUssQ0FBQyxDQUFDOztNQUV0QztNQUFBO01BQUF2RSxhQUFBLEdBQUFhLENBQUE7TUFDQTJELFlBQVksQ0FBQ0MsVUFBVSxDQUFDLGlCQUFpQixDQUFDOztNQUUxQztNQUFBO01BQUF6RSxhQUFBLEdBQUFhLENBQUE7TUFDQTZELFFBQVEsQ0FBQ0MsTUFBTSxHQUFHLDJDQUEyQzs7TUFFN0Q7TUFBQTtNQUFBM0UsYUFBQSxHQUFBYSxDQUFBO01BQ0FVLE9BQU8sQ0FBQyxJQUFJLENBQUM7TUFBQztNQUFBdkIsYUFBQSxHQUFBYSxDQUFBO01BQ2RRLFlBQVksQ0FBQyxLQUFLLENBQUM7TUFBQztNQUFBckIsYUFBQSxHQUFBYSxDQUFBO01BRXBCLE9BQU8sSUFBSTtJQUNiLENBQUMsQ0FBQyxPQUFPdUIsS0FBSyxFQUFFO01BQUE7TUFBQXBDLGFBQUEsR0FBQWEsQ0FBQTtNQUNkd0IsT0FBTyxDQUFDRCxLQUFLLENBQUMsb0JBQW9CLEVBQUVBLEtBQUssQ0FBQzs7TUFFMUM7TUFBQTtNQUFBcEMsYUFBQSxHQUFBYSxDQUFBO01BQ0EyRCxZQUFZLENBQUNDLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQztNQUFDO01BQUF6RSxhQUFBLEdBQUFhLENBQUE7TUFDM0M2RCxRQUFRLENBQUNDLE1BQU0sR0FBRywyQ0FBMkM7O01BRTdEO01BQUE7TUFBQTNFLGFBQUEsR0FBQWEsQ0FBQTtNQUNBVSxPQUFPLENBQUMsSUFBSSxDQUFDO01BQUM7TUFBQXZCLGFBQUEsR0FBQWEsQ0FBQTtNQUNkUSxZQUFZLENBQUMsS0FBSyxDQUFDO01BQUM7TUFBQXJCLGFBQUEsR0FBQWEsQ0FBQTtNQUVwQixPQUFPLElBQUksQ0FBQyxDQUFDO0lBQ2Y7RUFDRixDQUFDO0VBQUM7RUFBQWIsYUFBQSxHQUFBYSxDQUFBO0VBRUYsT0FDRSwwQkFBQStELEtBQUE7RUFBQTtFQUFDaEUsV0FBVyxDQUFDaUUsUUFBUTtFQUFBO0VBQUE7SUFDbkJDLEtBQUssRUFBRTtNQUNMaEUsZUFBZTtNQUNmQyxTQUFTO01BQ1RDLElBQUk7TUFDSlQsT0FBTztNQUNQVztJQUNGLENBQUU7SUFBQTZELE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUFDLFlBQUE7TUFBQUMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQSxHQUVEaEUsUUFDbUIsQ0FBQztBQUUzQjs7QUFFQTtBQUFBO0FBQUFwQixhQUFBLEdBQUFhLENBQUE7QUFDQSxPQUFPLE1BQU13RSxPQUFPLEdBQUdBLENBQUEsS0FBTTtFQUFBO0VBQUFyRixhQUFBLEdBQUFpQixDQUFBO0VBQUFqQixhQUFBLEdBQUFhLENBQUE7RUFBQSxPQUFBUixVQUFVLENBQUNPLFdBQVcsQ0FBQztBQUFELENBQUMiLCJpZ25vcmVMaXN0IjpbXX0=