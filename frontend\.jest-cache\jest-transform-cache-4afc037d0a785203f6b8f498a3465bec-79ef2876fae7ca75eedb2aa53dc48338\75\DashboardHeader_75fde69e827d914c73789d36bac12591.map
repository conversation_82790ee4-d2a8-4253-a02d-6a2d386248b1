{"version": 3, "names": ["_jsxFileName", "__jsx", "React", "createElement", "cov_22o17h8if", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "s", "f", "b", "_coverageSchema", "coverage", "actualCoverage", "useState", "DashboardHeader", "memo", "clientName", "onSearch", "onAddRenewal", "searchPlaceholder", "className", "testId", "searchQuery", "setSearch<PERSON>uery", "handleSearchChange", "e", "query", "target", "value", "handleSearchSubmit", "preventDefault", "handleAddRenewal", "__self", "__source", "fileName", "lineNumber", "columnNumber", "onSubmit", "placeholder", "onChange", "onClick"], "sources": ["DashboardHeader.tsx"], "sourcesContent": ["/**\n * Dashboard Header Component\n * \n * Contains the dashboard title, subtitle, search functionality, and action buttons.\n * Focused responsibility: Header section with search and actions.\n */\n\n'use client'\n\nimport React, { useState } from 'react'\nimport { BaseComponentProps } from '@/lib/types'\n\ninterface DashboardHeaderProps extends BaseComponentProps {\n  clientName?: string\n  onSearch?: (query: string) => void\n  onAddRenewal?: () => void\n  searchPlaceholder?: string\n}\n\nconst DashboardHeader = React.memo(function DashboardHeader({\n  clientName = 'Unknown Client',\n  onSearch,\n  onAddRenewal,\n  searchPlaceholder = 'Search renewals...',\n  className = '',\n  'data-testid': testId\n}: DashboardHeaderProps) {\n  const [searchQuery, setSearchQuery] = useState('')\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const query = e.target.value\n    setSearchQuery(query)\n    onSearch?.(query)\n  }\n\n  const handleSearchSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    onSearch?.(searchQuery)\n  }\n\n  const handleAddRenewal = () => {\n    onAddRenewal?.()\n  }\n\n  return (\n    <div \n      className={`dashboard-header ${className}`}\n      data-testid={testId}\n    >\n      <div className=\"dashboard-title-section\">\n        <h1 className=\"dashboard-title\">\n          Dashboard - {clientName}\n        </h1>\n        <p className=\"dashboard-subtitle\">\n          Manage your subscriptions, maintenance, support and warranties\n        </p>\n      </div>\n      \n      <div className=\"dashboard-actions\">\n        <form onSubmit={handleSearchSubmit} className=\"search-container\">\n          <input\n            type=\"text\"\n            placeholder={searchPlaceholder}\n            className=\"search-input\"\n            value={searchQuery}\n            onChange={handleSearchChange}\n            aria-label=\"Search renewals\"\n          />\n          <button \n            type=\"submit\"\n            className=\"search-icon\"\n            aria-label=\"Submit search\"\n          >\n            🔍\n          </button>\n        </form>\n        \n        <button \n          className=\"btn btn-primary\"\n          onClick={handleAddRenewal}\n          aria-label=\"Add new renewal\"\n        >\n          + Add Renewal\n        </button>\n      </div>\n    </div>\n  )\n})\n\nexport default DashboardHeader\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAAA;AAAA,IAAAA,YAAA;AAAA,IAAAC,KAAA,GAAAC,KAAA,CAAAC,aAAA;AAAA,SAAAC,cAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAU,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,eAAA;IAAApB,IAAA;EAAA;EAAA,IAAAqB,QAAA,GAAApB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAkB,QAAA,CAAAtB,IAAA,KAAAsB,QAAA,CAAAtB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAqB,QAAA,CAAAtB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAkB,cAAA,GAAAD,QAAA,CAAAtB,IAAA;EAAA;IAQA;IAAAD,aAAA,YAAAA,CAAA;MAAA,OAAAwB,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAxB,aAAA;AANZ,OAAOF,KAAK,IAAI2B,QAAQ,QAAQ,OAAO;AAUvC,MAAMC,eAAe;AAAA;AAAA,CAAA1B,aAAA,GAAAmB,CAAA,oBAAGrB,KAAK,CAAC6B,IAAI,CAAC,SAASD,eAAeA,CAAC;EAC1DE,UAAU;EAAA;EAAA,CAAA5B,aAAA,GAAAqB,CAAA,UAAG,gBAAgB;EAC7BQ,QAAQ;EACRC,YAAY;EACZC,iBAAiB;EAAA;EAAA,CAAA/B,aAAA,GAAAqB,CAAA,UAAG,oBAAoB;EACxCW,SAAS;EAAA;EAAA,CAAAhC,aAAA,GAAAqB,CAAA,UAAG,EAAE;EACd,aAAa,EAAEY;AACK,CAAC,EAAE;EAAA;EAAAjC,aAAA,GAAAoB,CAAA;EACvB,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC;EAAA;EAAA,CAAAnC,aAAA,GAAAmB,CAAA,OAAGM,QAAQ,CAAC,EAAE,CAAC;EAAA;EAAAzB,aAAA,GAAAmB,CAAA;EAElD,MAAMiB,kBAAkB,GAAIC,CAAsC,IAAK;IAAA;IAAArC,aAAA,GAAAoB,CAAA;IACrE,MAAMkB,KAAK;IAAA;IAAA,CAAAtC,aAAA,GAAAmB,CAAA,OAAGkB,CAAC,CAACE,MAAM,CAACC,KAAK;IAAA;IAAAxC,aAAA,GAAAmB,CAAA;IAC5BgB,cAAc,CAACG,KAAK,CAAC;IAAA;IAAAtC,aAAA,GAAAmB,CAAA;IACrBU,QAAQ,GAAGS,KAAK,CAAC;EACnB,CAAC;EAAA;EAAAtC,aAAA,GAAAmB,CAAA;EAED,MAAMsB,kBAAkB,GAAIJ,CAAkB,IAAK;IAAA;IAAArC,aAAA,GAAAoB,CAAA;IAAApB,aAAA,GAAAmB,CAAA;IACjDkB,CAAC,CAACK,cAAc,CAAC,CAAC;IAAA;IAAA1C,aAAA,GAAAmB,CAAA;IAClBU,QAAQ,GAAGK,WAAW,CAAC;EACzB,CAAC;EAAA;EAAAlC,aAAA,GAAAmB,CAAA;EAED,MAAMwB,gBAAgB,GAAGA,CAAA,KAAM;IAAA;IAAA3C,aAAA,GAAAoB,CAAA;IAAApB,aAAA,GAAAmB,CAAA;IAC7BW,YAAY,GAAG,CAAC;EAClB,CAAC;EAAA;EAAA9B,aAAA,GAAAmB,CAAA;EAED,OACE,0BAAAtB,KAAA;EAAA;EAAA;EAAA;EAAA;IACEmC,SAAS,EAAE,oBAAoBA,SAAS,EAAG;IAC3C;IAAA,eAAaC,MAAO;IAAAW,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAlD,YAAA;MAAAmD,UAAA;MAAAC,YAAA;IAAA;EAAA;EAEpB;EAAAnD,KAAA;EAAA;EAAA;EAAA;EAAA;IAAKmC,SAAS,EAAC,yBAAyB;IAAAY,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAlD,YAAA;MAAAmD,UAAA;MAAAC,YAAA;IAAA;EAAA;EACtC;EAAAnD,KAAA;EAAA;EAAA;EAAA;EAAA;IAAImC,SAAS,EAAC,iBAAiB;IAAAY,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAlD,YAAA;MAAAmD,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAClB,EAACpB,UACX,CAAC;EACL;EAAA/B,KAAA;EAAA;EAAA;EAAA;EAAA;IAAGmC,SAAS,EAAC,oBAAoB;IAAAY,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAlD,YAAA;MAAAmD,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gEAE/B,CACA,CAAC;EAEN;EAAAnD,KAAA;EAAA;EAAA;EAAA;EAAA;IAAKmC,SAAS,EAAC,mBAAmB;IAAAY,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAlD,YAAA;MAAAmD,UAAA;MAAAC,YAAA;IAAA;EAAA;EAChC;EAAAnD,KAAA;EAAA;EAAA;EAAA;EAAA;IAAMoD,QAAQ,EAAER,kBAAmB;IAACT,SAAS,EAAC,kBAAkB;IAAAY,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAlD,YAAA;MAAAmD,UAAA;MAAAC,YAAA;IAAA;EAAA;EAC9D;EAAAnD,KAAA;EAAA;EAAA;EAAA;EAAA;IACEoB,IAAI,EAAC,MAAM;IACXiC,WAAW,EAAEnB,iBAAkB;IAC/BC,SAAS,EAAC,cAAc;IACxBQ,KAAK,EAAEN,WAAY;IACnBiB,QAAQ,EAAEf,kBAAmB;IAC7B;IAAA,cAAW,iBAAiB;IAAAQ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAlD,YAAA;MAAAmD,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC7B,CAAC;EACF;EAAAnD,KAAA;EAAA;EAAA;EAAA;EAAA;IACEoB,IAAI,EAAC,QAAQ;IACbe,SAAS,EAAC,aAAa;IACvB;IAAA,cAAW,eAAe;IAAAY,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAlD,YAAA;MAAAmD,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3B,cAEO,CACJ,CAAC;EAEP;EAAAnD,KAAA;EAAA;EAAA;EAAA;EAAA;IACEmC,SAAS,EAAC,iBAAiB;IAC3BoB,OAAO,EAAET,gBAAiB;IAC1B;IAAA,cAAW,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAlD,YAAA;MAAAmD,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7B,eAEO,CACL,CACF,CAAC;AAEV,CAAC,CAAC;AAEF,eAAetB,eAAe", "ignoreList": []}