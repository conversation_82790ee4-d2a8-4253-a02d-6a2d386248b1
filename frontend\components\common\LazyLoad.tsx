/**
 * Lazy Loading Components
 * 
 * Provides various lazy loading strategies for optimizing performance
 * including intersection observer, virtual scrolling, and code splitting.
 */

'use client'

import React, { Suspense, lazy, memo, useState, useEffect, useRef } from 'react'
import { useIntersectionObserver } from '@/lib/performance'
import { LoadingSkeleton } from './LoadingStates'

interface LazyComponentProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  threshold?: number
  rootMargin?: string
  triggerOnce?: boolean
  className?: string
}

interface LazyImageProps {
  src: string
  alt: string
  className?: string
  placeholder?: string
  onLoad?: () => void
  onError?: () => void
}

interface VirtualListProps<T> {
  items: T[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: T, index: number) => React.ReactNode
  overscan?: number
  className?: string
}

// Lazy component that loads when it enters the viewport
export const LazyComponent = memo(function LazyComponent({
  children,
  fallback = <LoadingSkeleton lines={3} />,
  threshold = 0.1,
  rootMargin = '50px',
  triggerOnce = true,
  className = ''
}: LazyComponentProps) {
  const [ref, isIntersecting] = useIntersectionObserver({
    threshold,
    rootMargin,
  })
  
  const [hasLoaded, setHasLoaded] = useState(false)

  useEffect(() => {
    if (isIntersecting && !hasLoaded) {
      setHasLoaded(true)
    }
  }, [isIntersecting, hasLoaded])

  const shouldRender = triggerOnce ? hasLoaded : isIntersecting

  return (
    <div ref={ref} className={className}>
      {shouldRender ? children : fallback}
    </div>
  )
})

// Lazy image with progressive loading
export const LazyImage = memo(function LazyImage({
  src,
  alt,
  className = '',
  placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2VlZSIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1zaXplPSIxOCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iIGZpbGw9IiNhYWEiPkxvYWRpbmc8L3RleHQ+PC9zdmc+',
  onLoad,
  onError
}: LazyImageProps) {
  const [ref, isIntersecting] = useIntersectionObserver({
    threshold: 0.1,
    rootMargin: '50px',
  })
  
  const [isLoaded, setIsLoaded] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [imageSrc, setImageSrc] = useState(placeholder)

  useEffect(() => {
    if (isIntersecting && !isLoaded && !hasError) {
      const img = new Image()
      
      img.onload = () => {
        setImageSrc(src)
        setIsLoaded(true)
        onLoad?.()
      }
      
      img.onerror = () => {
        setHasError(true)
        onError?.()
      }
      
      img.src = src
    }
  }, [isIntersecting, src, isLoaded, hasError, onLoad, onError])

  return (
    <div ref={ref} className={`relative ${className}`}>
      <img
        src={imageSrc}
        alt={alt}
        className={`transition-opacity duration-300 ${
          isLoaded ? 'opacity-100' : 'opacity-70'
        } ${className}`}
        loading="lazy"
      />
      {!isLoaded && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="animate-pulse text-gray-400">Loading...</div>
        </div>
      )}
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="text-gray-400">Failed to load</div>
        </div>
      )}
    </div>
  )
})

// Virtual list for large datasets
export function VirtualList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className = ''
}: VirtualListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
  const endIndex = Math.min(
    items.length - 1,
    Math.floor((scrollTop + containerHeight) / itemHeight) + overscan
  )

  const visibleItems = items.slice(startIndex, endIndex + 1)
  const totalHeight = items.length * itemHeight
  const offsetY = startIndex * itemHeight

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }

  return (
    <div
      ref={containerRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item, index) => (
            <div
              key={startIndex + index}
              style={{ height: itemHeight }}
            >
              {renderItem(item, startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Code splitting wrapper
export function createLazyRoute(
  importFn: () => Promise<{ default: React.ComponentType<any> }>,
  fallback?: React.ComponentType
) {
  const LazyComponent = lazy(importFn)
  
  return memo(function LazyRoute(props: any) {
    return (
      <Suspense fallback={fallback ? <fallback /> : <LoadingSkeleton lines={5} />}>
        <LazyComponent {...props} />
      </Suspense>
    )
  })
}

// Progressive enhancement wrapper
export const ProgressiveEnhancement = memo(function ProgressiveEnhancement({
  children,
  fallback,
  condition = true,
  delay = 0
}: {
  children: React.ReactNode
  fallback?: React.ReactNode
  condition?: boolean
  delay?: number
}) {
  const [isReady, setIsReady] = useState(delay === 0 && condition)

  useEffect(() => {
    if (condition && delay > 0) {
      const timer = setTimeout(() => {
        setIsReady(true)
      }, delay)
      
      return () => clearTimeout(timer)
    } else if (condition) {
      setIsReady(true)
    }
  }, [condition, delay])

  if (!isReady) {
    return fallback ? <>{fallback}</> : null
  }

  return <>{children}</>
})

// Lazy section that loads content when scrolled into view
export const LazySection = memo(function LazySection({
  children,
  placeholder,
  className = '',
  threshold = 0.1,
  rootMargin = '100px'
}: {
  children: React.ReactNode
  placeholder?: React.ReactNode
  className?: string
  threshold?: number
  rootMargin?: string
}) {
  const [ref, isIntersecting] = useIntersectionObserver({
    threshold,
    rootMargin,
  })
  
  const [hasLoaded, setHasLoaded] = useState(false)

  useEffect(() => {
    if (isIntersecting) {
      setHasLoaded(true)
    }
  }, [isIntersecting])

  return (
    <section ref={ref} className={className}>
      {hasLoaded ? (
        children
      ) : (
        placeholder || (
          <div className="py-8">
            <LoadingSkeleton lines={4} />
          </div>
        )
      )}
    </section>
  )
})

// Lazy tabs that only render active content
export const LazyTabs = memo(function LazyTabs({
  tabs,
  activeTab,
  onTabChange,
  className = ''
}: {
  tabs: Array<{
    id: string
    label: string
    content: React.ReactNode
  }>
  activeTab: string
  onTabChange: (tabId: string) => void
  className?: string
}) {
  const [loadedTabs, setLoadedTabs] = useState(new Set([activeTab]))

  useEffect(() => {
    setLoadedTabs(prev => new Set([...prev, activeTab]))
  }, [activeTab])

  return (
    <div className={className}>
      <div className="flex border-b">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`px-4 py-2 font-medium text-sm ${
              activeTab === tab.id
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>
      
      <div className="mt-4">
        {tabs.map(tab => (
          <div
            key={tab.id}
            className={activeTab === tab.id ? 'block' : 'hidden'}
          >
            {loadedTabs.has(tab.id) ? (
              tab.content
            ) : (
              <LoadingSkeleton lines={3} />
            )}
          </div>
        ))}
      </div>
    </div>
  )
})
