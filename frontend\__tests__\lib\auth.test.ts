/**
 * Authentication Library Tests
 * 
 * Tests for the authentication utilities and JWT validation
 */

import { jest } from '@jest/globals'
import { validateJwtToken } from '@/lib/jwt-validator'
import { testUtils } from '../utils/test-utils'

// Mock jose library
jest.mock('jose', () => ({
  jwtVerify: jest.fn(),
  createRemoteJWKSet: jest.fn(),
}))

describe('Authentication Library', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('JWT Validation', () => {
    const mockJWKS = jest.fn()
    const mockJwtVerify = jest.fn()

    beforeEach(() => {
      const jose = require('jose')
      jose.createRemoteJWKSet.mockReturnValue(mockJWKS)
      jose.jwtVerify = mockJwtVerify
    })

    it('should validate a valid JWT token', async () => {
      const mockPayload = {
        sub: 'test-user-id',
        email: '<EMAIL>',
        aud: 'test-client-id',
        iss: 'https://cognito-idp.ca-central-1.amazonaws.com/ca-central-1_test',
        exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
      }

      mockJwtVerify.mockResolvedValue({
        payload: mockPayload,
        protectedHeader: { alg: 'RS256' },
      })

      const result = await validateJwtToken(
        'valid-jwt-token',
        'ca-central-1_test',
        'test-client-id'
      )

      expect(result).toEqual(mockPayload)
      expect(mockJwtVerify).toHaveBeenCalledWith(
        'valid-jwt-token',
        mockJWKS,
        expect.objectContaining({
          issuer: 'https://cognito-idp.ca-central-1.amazonaws.com/ca-central-1_test',
          audience: 'test-client-id',
        })
      )
    })

    it('should reject an expired JWT token', async () => {
      mockJwtVerify.mockRejectedValue(new Error('JWT expired'))

      await expect(
        validateJwtToken(
          'expired-jwt-token',
          'ca-central-1_test',
          'test-client-id'
        )
      ).rejects.toThrow('JWT expired')
    })

    it('should reject a JWT token with invalid audience', async () => {
      mockJwtVerify.mockRejectedValue(new Error('Invalid audience'))

      await expect(
        validateJwtToken(
          'invalid-audience-token',
          'ca-central-1_test',
          'wrong-client-id'
        )
      ).rejects.toThrow('Invalid audience')
    })

    it('should reject a JWT token with invalid issuer', async () => {
      mockJwtVerify.mockRejectedValue(new Error('Invalid issuer'))

      await expect(
        validateJwtToken(
          'invalid-issuer-token',
          'wrong-pool-id',
          'test-client-id'
        )
      ).rejects.toThrow('Invalid issuer')
    })

    it('should handle malformed JWT tokens', async () => {
      mockJwtVerify.mockRejectedValue(new Error('Malformed token'))

      await expect(
        validateJwtToken(
          'malformed.token',
          'ca-central-1_test',
          'test-client-id'
        )
      ).rejects.toThrow('Malformed token')
    })

    it('should handle network errors when fetching JWKS', async () => {
      mockJwtVerify.mockRejectedValue(new Error('Network error'))

      await expect(
        validateJwtToken(
          'valid-token',
          'ca-central-1_test',
          'test-client-id'
        )
      ).rejects.toThrow('Network error')
    })
  })

  describe('Authentication Middleware', () => {
    let mockRequest: any
    let mockResponse: any

    beforeEach(() => {
      mockRequest = {
        headers: {},
        cookies: {},
      }
      mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn().mockReturnThis(),
      }
    })

    it('should extract token from Authorization header', () => {
      mockRequest.headers.authorization = 'Bearer valid-token'
      
      // Test token extraction logic
      const authHeader = mockRequest.headers.authorization
      const token = authHeader?.startsWith('Bearer ') 
        ? authHeader.substring(7) 
        : null

      expect(token).toBe('valid-token')
    })

    it('should extract token from cookies', () => {
      mockRequest.cookies.idToken = 'cookie-token'
      
      const token = mockRequest.cookies.idToken
      expect(token).toBe('cookie-token')
    })

    it('should handle missing authentication', () => {
      // No token in headers or cookies
      const authHeader = mockRequest.headers.authorization
      const cookieToken = mockRequest.cookies.idToken
      
      expect(authHeader).toBeUndefined()
      expect(cookieToken).toBeUndefined()
    })
  })

  describe('Session Management', () => {
    it('should create a valid session object', () => {
      const mockUser = testUtils.generateTestData.user()
      const session = {
        user: mockUser,
        isAuthenticated: true,
        expiresAt: new Date(Date.now() + 3600000), // 1 hour from now
      }

      expect(session.user).toEqual(mockUser)
      expect(session.isAuthenticated).toBe(true)
      expect(session.expiresAt).toBeInstanceOf(Date)
    })

    it('should handle session expiration', () => {
      const expiredSession = {
        user: null,
        isAuthenticated: false,
        expiresAt: new Date(Date.now() - 3600000), // 1 hour ago
      }

      const isExpired = expiredSession.expiresAt < new Date()
      expect(isExpired).toBe(true)
      expect(expiredSession.isAuthenticated).toBe(false)
    })
  })

  describe('Error Handling', () => {
    it('should handle authentication errors gracefully', () => {
      const authError = {
        code: 'UNAUTHORIZED',
        message: 'Invalid credentials',
        status: 401,
      }

      expect(authError.code).toBe('UNAUTHORIZED')
      expect(authError.status).toBe(401)
    })

    it('should handle token refresh errors', () => {
      const refreshError = {
        code: 'TOKEN_REFRESH_FAILED',
        message: 'Unable to refresh token',
        status: 401,
      }

      expect(refreshError.code).toBe('TOKEN_REFRESH_FAILED')
      expect(refreshError.status).toBe(401)
    })
  })

  describe('Security Validations', () => {
    it('should validate token format', () => {
      const validTokenFormat = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/
      
      expect('valid.jwt.token').toMatch(validTokenFormat)
      expect('invalid-token').not.toMatch(validTokenFormat)
      expect('').not.toMatch(validTokenFormat)
    })

    it('should validate user permissions', () => {
      const user = {
        id: 'test-user',
        roles: ['user', 'admin'],
        permissions: ['read', 'write', 'delete'],
      }

      const hasPermission = (permission: string) => 
        user.permissions.includes(permission)

      expect(hasPermission('read')).toBe(true)
      expect(hasPermission('write')).toBe(true)
      expect(hasPermission('admin')).toBe(false)
    })

    it('should validate tenant access', () => {
      const user = {
        id: 'test-user',
        tenantId: 'tenant-123',
      }

      const requestedTenantId = 'tenant-123'
      const hasAccess = user.tenantId === requestedTenantId

      expect(hasAccess).toBe(true)
    })
  })
})
