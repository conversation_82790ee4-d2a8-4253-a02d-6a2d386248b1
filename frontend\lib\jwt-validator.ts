/**
 * Secure JWT Token Validation
 * 
 * This module provides secure JWT token validation with proper signature verification
 * using AWS Cognito's JWKS (JSON Web Key Set).
 */

import { jwtVerify, createRemoteJWKSet, JWTPayload } from 'jose';
import { publicConfig } from './config';

// Cognito JWT payload interface
export interface CognitoJwtPayload extends JWTPayload {
  sub: string;
  email: string;
  email_verified?: boolean;
  name?: string;
  given_name?: string;
  family_name?: string;
  'cognito:groups'?: string[];
  'cognito:username'?: string;
  token_use: 'id' | 'access';
  auth_time: number;
  iat: number;
  exp: number;
  aud: string;
  iss: string;
}

// Cache for JWKS
let jwksCache: ReturnType<typeof createRemoteJWKSet> | null = null;

/**
 * Get JWKS URL for the Cognito User Pool
 */
function getJwksUrl(): string {
  const { region, userPoolId } = publicConfig.aws;
  return `https://cognito-idp.${region}.amazonaws.com/${userPoolId}/.well-known/jwks.json`;
}

/**
 * Get or create JWKS instance
 */
function getJwks() {
  if (!jwksCache) {
    const jwksUrl = getJwksUrl();
    jwksCache = createRemoteJWKSet(new URL(jwksUrl));
  }
  return jwksCache;
}

/**
 * Validate JWT token with proper signature verification
 */
export async function validateJwtToken(token: string): Promise<CognitoJwtPayload | null> {
  try {
    const jwks = getJwks();
    const { region, userPoolId, userPoolClientId } = publicConfig.aws;
    
    // Expected issuer
    const expectedIssuer = `https://cognito-idp.${region}.amazonaws.com/${userPoolId}`;
    
    // Verify the JWT
    const { payload } = await jwtVerify(token, jwks, {
      issuer: expectedIssuer,
      audience: userPoolClientId,
    });

    // Additional validation
    const cognitoPayload = payload as CognitoJwtPayload;
    
    // Validate token type (should be 'id' for ID tokens)
    if (cognitoPayload.token_use !== 'id') {
      console.warn('Invalid token type:', cognitoPayload.token_use);
      return null;
    }

    // Validate email is present and verified (if available)
    if (!cognitoPayload.email) {
      console.warn('Token missing email claim');
      return null;
    }

    // Check if email is verified (optional but recommended)
    if (cognitoPayload.email_verified === false) {
      console.warn('Email not verified for user:', cognitoPayload.email);
      // You might want to reject unverified emails in production
    }

    return cognitoPayload;
  } catch (error) {
    console.error('JWT validation failed:', error);
    return null;
  }
}

/**
 * Extract and validate JWT from cookie string
 */
export async function validateAuthCookie(cookieValue: string): Promise<CognitoJwtPayload | null> {
  if (!cookieValue) {
    return null;
  }

  // Remove any potential cookie prefix/suffix
  const token = cookieValue.trim();
  
  // Basic JWT format check
  if (!token.includes('.') || token.split('.').length !== 3) {
    console.warn('Invalid JWT format in cookie');
    return null;
  }

  return validateJwtToken(token);
}

/**
 * Fallback JWT parsing (INSECURE - only for development/debugging)
 * This should NOT be used in production
 */
export function parseJwtUnsafe(token: string): CognitoJwtPayload | null {
  if (publicConfig.app.isProduction) {
    throw new Error('Unsafe JWT parsing is not allowed in production');
  }

  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
    
    // Check expiration
    const currentTime = Math.floor(Date.now() / 1000);
    if (payload.exp && payload.exp < currentTime) {
      console.warn('Token has expired');
      return null;
    }

    return payload as CognitoJwtPayload;
  } catch (error) {
    console.error('Failed to parse JWT:', error);
    return null;
  }
}

/**
 * Validate token expiration
 */
export function isTokenExpired(payload: CognitoJwtPayload): boolean {
  const currentTime = Math.floor(Date.now() / 1000);
  return payload.exp < currentTime;
}

/**
 * Get token expiration time in milliseconds
 */
export function getTokenExpirationTime(payload: CognitoJwtPayload): number {
  return payload.exp * 1000;
}

/**
 * Check if token expires within the specified minutes
 */
export function isTokenExpiringWithin(payload: CognitoJwtPayload, minutes: number): boolean {
  const expirationTime = getTokenExpirationTime(payload);
  const warningTime = Date.now() + (minutes * 60 * 1000);
  return expirationTime <= warningTime;
}
