1a7d6fd92fa622a235df531f5ce56f45
/* istanbul ignore next */
function cov_1z5bmcajii() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\clients\\route.ts";
  var hash = "0ea0b48004ca232fb2f2cb96375caf02687e77b4";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\clients\\route.ts",
    statementMap: {
      "0": {
        start: {
          line: 7,
          column: 27
        },
        end: {
          line: 15,
          column: 2
        }
      },
      "1": {
        start: {
          line: 18,
          column: 27
        },
        end: {
          line: 26,
          column: 2
        }
      },
      "2": {
        start: {
          line: 30,
          column: 18
        },
        end: {
          line: 30,
          column: 39
        }
      },
      "3": {
        start: {
          line: 31,
          column: 2
        },
        end: {
          line: 33,
          column: 3
        }
      },
      "4": {
        start: {
          line: 32,
          column: 4
        },
        end: {
          line: 32,
          column: 73
        }
      },
      "5": {
        start: {
          line: 35,
          column: 2
        },
        end: {
          line: 59,
          column: 3
        }
      },
      "6": {
        start: {
          line: 36,
          column: 17
        },
        end: {
          line: 36,
          column: 37
        }
      },
      "7": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 46,
          column: 5
        }
      },
      "8": {
        start: {
          line: 40,
          column: 6
        },
        end: {
          line: 40,
          column: 37
        }
      },
      "9": {
        start: {
          line: 42,
          column: 6
        },
        end: {
          line: 45,
          column: 26
        }
      },
      "10": {
        start: {
          line: 48,
          column: 19
        },
        end: {
          line: 53,
          column: 6
        }
      },
      "11": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 55,
          column: 54
        }
      },
      "12": {
        start: {
          line: 57,
          column: 4
        },
        end: {
          line: 57,
          column: 51
        }
      },
      "13": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 58,
          column: 84
        }
      }
    },
    fnMap: {
      "0": {
        name: "POST",
        decl: {
          start: {
            line: 28,
            column: 22
          },
          end: {
            line: 28,
            column: 26
          }
        },
        loc: {
          start: {
            line: 28,
            column: 49
          },
          end: {
            line: 60,
            column: 1
          }
        },
        line: 28
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 31,
            column: 2
          },
          end: {
            line: 33,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 2
          },
          end: {
            line: 33,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "1": {
        loc: {
          start: {
            line: 31,
            column: 6
          },
          end: {
            line: 31,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 31,
            column: 6
          },
          end: {
            line: 31,
            column: 14
          }
        }, {
          start: {
            line: 31,
            column: 18
          },
          end: {
            line: 31,
            column: 50
          }
        }],
        line: 31
      },
      "2": {
        loc: {
          start: {
            line: 44,
            column: 17
          },
          end: {
            line: 44,
            column: 76
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 44,
            column: 42
          },
          end: {
            line: 44,
            column: 55
          }
        }, {
          start: {
            line: 44,
            column: 58
          },
          end: {
            line: 44,
            column: 76
          }
        }],
        line: 44
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0ea0b48004ca232fb2f2cb96375caf02687e77b4"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1z5bmcajii = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1z5bmcajii();
import { NextResponse } from 'next/server';
import { createClient } from '../../../lib/clients';
import { verifySession } from '../../../lib/dal';
import { z } from 'zod';

// Define schema for client creation
const createClientSchema =
/* istanbul ignore next */
(cov_1z5bmcajii().s[0]++, z.object({
  name: z.string().min(2).max(255),
  domain: z.string().min(3).max(255).regex(/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/, 'Invalid domain format'),
  status: z.enum(['active', 'inactive', 'pending']).optional(),
  settings: z.record(z.unknown()).optional()
}));

// Define schema for client updates
const updateClientSchema =
/* istanbul ignore next */
(cov_1z5bmcajii().s[1]++, z.object({
  name: z.string().min(2).max(255).optional(),
  domain: z.string().min(3).max(255).regex(/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/, 'Invalid domain format').optional(),
  status: z.enum(['active', 'inactive', 'pending']).optional(),
  settings: z.record(z.unknown()).optional()
}));
export async function POST(request) {
  /* istanbul ignore next */
  cov_1z5bmcajii().f[0]++;
  // Verify authentication and authorization
  const session =
  /* istanbul ignore next */
  (cov_1z5bmcajii().s[2]++, await verifySession());
  /* istanbul ignore next */
  cov_1z5bmcajii().s[3]++;
  if (
  /* istanbul ignore next */
  (cov_1z5bmcajii().b[1][0]++, !session) ||
  /* istanbul ignore next */
  (cov_1z5bmcajii().b[1][1]++, !session.roles.includes('admin'))) {
    /* istanbul ignore next */
    cov_1z5bmcajii().b[0][0]++;
    cov_1z5bmcajii().s[4]++;
    return NextResponse.json({
      error: 'Unauthorized'
    }, {
      status: 401
    });
  } else
  /* istanbul ignore next */
  {
    cov_1z5bmcajii().b[0][1]++;
  }
  cov_1z5bmcajii().s[5]++;
  try {
    const body =
    /* istanbul ignore next */
    (cov_1z5bmcajii().s[6]++, await request.json());

    // Validate input
    /* istanbul ignore next */
    cov_1z5bmcajii().s[7]++;
    try {
      /* istanbul ignore next */
      cov_1z5bmcajii().s[8]++;
      createClientSchema.parse(body);
    } catch (error) {
      /* istanbul ignore next */
      cov_1z5bmcajii().s[9]++;
      return NextResponse.json({
        error: 'Invalid client data',
        details: error instanceof Error ?
        /* istanbul ignore next */
        (cov_1z5bmcajii().b[2][0]++, error.message) :
        /* istanbul ignore next */
        (cov_1z5bmcajii().b[2][1]++, 'Validation error')
      }, {
        status: 400
      });
    }
    const client =
    /* istanbul ignore next */
    (cov_1z5bmcajii().s[10]++, await createClient({
      name: body.name,
      domain: body.domain,
      status: body.status,
      settings: body.settings
    }));
    /* istanbul ignore next */
    cov_1z5bmcajii().s[11]++;
    return NextResponse.json(client, {
      status: 201
    });
  } catch (error) {
    /* istanbul ignore next */
    cov_1z5bmcajii().s[12]++;
    console.error('Error creating client:', error);
    /* istanbul ignore next */
    cov_1z5bmcajii().s[13]++;
    return NextResponse.json({
      error: 'Failed to create client'
    }, {
      status: 500
    });
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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