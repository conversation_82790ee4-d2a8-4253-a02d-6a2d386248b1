ed1fdffba542dde6ea38809fd6a217ae
/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\layout.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_1cp5k7oyks() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\layout.tsx";
  var hash = "4a4c3a96d05ba1cfd2b9fae36ae9e4872956b0ac";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\layout.tsx",
    statementMap: {
      "0": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 18
        }
      },
      "1": {
        start: {
          line: 12,
          column: 14
        },
        end: {
          line: 16,
          column: 2
        }
      },
      "2": {
        start: {
          line: 18,
          column: 24
        },
        end: {
          line: 26,
          column: 1
        }
      },
      "3": {
        start: {
          line: 33,
          column: 2
        },
        end: {
          line: 51,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "RootLayout",
        decl: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 34
          }
        },
        loc: {
          start: {
            line: 32,
            column: 3
          },
          end: {
            line: 52,
            column: 1
          }
        },
        line: 32
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    f: {
      "0": 0
    },
    b: {},
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "4a4c3a96d05ba1cfd2b9fae36ae9e4872956b0ac"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1cp5k7oyks = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1cp5k7oyks();
import { Inter } from 'next/font/google';
import '../styles/globals.css';
import { configureAmplify } from '../lib/amplify-config';
import { Providers } from '@/components/providers';
import PerformanceMonitor from '@/components/common/PerformanceMonitor';

// Configure Amplify at the application root
/* istanbul ignore next */
cov_1cp5k7oyks().s[0]++;
configureAmplify();

// Optimize font loading with performance settings
const inter =
/* istanbul ignore next */
(cov_1cp5k7oyks().s[1]++, Inter({
  subsets: ['latin'],
  display: 'swap',
  // Improve font loading performance
  preload: true
}));
export const metadata =
/* istanbul ignore next */
(cov_1cp5k7oyks().s[2]++, {
  title: 'RenewTrack',
  description: 'Track and manage your renewals',
  // Performance and SEO optimizations
  keywords: 'renewals, software, tracking, management',
  authors: [{
    name: 'RenewTrack Team'
  }],
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#000000'
});
export default function RootLayout({
  children
}) {
  /* istanbul ignore next */
  cov_1cp5k7oyks().f[0]++;
  cov_1cp5k7oyks().s[3]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "html",
  /* istanbul ignore next */
  {
    lang: "en",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 34,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "head",
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 35,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "link",
  /* istanbul ignore next */
  {
    rel: "dns-prefetch",
    href: "//fonts.googleapis.com",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 37,
      columnNumber: 9
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "link",
  /* istanbul ignore next */
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 38,
      columnNumber: 9
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "link",
  /* istanbul ignore next */
  {
    rel: "preload",
    href: "/api/auth/session",
    as: "fetch",
    crossOrigin: "anonymous",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 41,
      columnNumber: 9
    }
  })),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "body",
  /* istanbul ignore next */
  {
    className: inter.className,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 43,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(Providers,
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 44,
      columnNumber: 9
    }
  }, children,
  /* istanbul ignore next */
  __jsx(PerformanceMonitor,
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 47,
      columnNumber: 11
    }
  }))));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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