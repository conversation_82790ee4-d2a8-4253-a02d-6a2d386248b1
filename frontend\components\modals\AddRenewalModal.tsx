/**
 * Add Renewal Modal Component
 * 
 * Two-step modal for adding new renewals:
 * 1. Renewal Details
 * 2. Set Up Alerts
 */

'use client'

import React, { useState, useCallback } from 'react'
import { BaseComponentProps } from '@/lib/types'
import RenewalDetailsStep from './steps/RenewalDetailsStep'
import SetupAlertsStep from './steps/SetupAlertsStep'

export interface RenewalFormData {
  // Step 1: Renewal Details
  productName: string
  version: string
  vendor: string
  renewalTypeId: number | null // Store ID, not name
  department: string
  purchaseTypeId: number | null // Store ID, not name
  licensedDate: string
  renewalDate: string
  associatedEmails: string[]
  reseller: string
  currencyId: string // Store ID, not name
  cost: number
  costCode: string
  licenseCount: number
  description: string
  notes: string
}

export interface AlertFormData {
  daysBeforeRenewal: number
  emailRecipients: string[]
  customMessage: string
  enabled: boolean
}

interface AddRenewalModalProps extends BaseComponentProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (renewalData: RenewalFormData, alertData: AlertFormData[]) => Promise<void>
}

const AddRenewalModal: React.FC<AddRenewalModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  className = '',
  'data-testid': testId
}) => {
  const [currentStep, setCurrentStep] = useState<1 | 2>(1)
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  // Form data state
  const [renewalData, setRenewalData] = useState<RenewalFormData>({
    productName: '',
    version: '',
    vendor: '',
    renewalTypeId: null,
    department: '',
    purchaseTypeId: null,
    licensedDate: '',
    renewalDate: '',
    associatedEmails: [],
    reseller: '',
    currencyId: '',
    cost: 0,
    costCode: '',
    licenseCount: 0,
    description: '',
    notes: ''
  })

  const [alertsData, setAlertsData] = useState<AlertFormData[]>([
    {
      daysBeforeRenewal: 30,
      emailRecipients: [],
      customMessage: '',
      enabled: true
    }
  ])

  // Handle step navigation
  const handleNextStep = useCallback(() => {
    if (currentStep === 1) {
      setCurrentStep(2)
    }
  }, [currentStep])

  const handleBackToDetails = useCallback(() => {
    setCurrentStep(1)
  }, [])

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    setIsSubmitting(true)
    try {
      await onSubmit(renewalData, alertsData)
      onClose()
      // Reset form
      setCurrentStep(1)
      setRenewalData({
        productName: '',
        version: '',
        vendor: '',
        renewalTypeId: null,
        department: '',
        purchaseTypeId: null,
        licensedDate: '',
        renewalDate: '',
        associatedEmails: [],
        reseller: '',
        currencyId: '',
        cost: 0,
        costCode: '',
        licenseCount: 0,
        description: '',
        notes: ''
      })
      setAlertsData([{
        daysBeforeRenewal: 30,
        emailRecipients: [],
        customMessage: '',
        enabled: true
      }])
    } catch (error) {
      console.error('Error submitting renewal:', error)
    } finally {
      setIsSubmitting(false)
    }
  }, [renewalData, alertsData, onSubmit, onClose])

  // Handle modal close
  const handleClose = useCallback(() => {
    if (!isSubmitting) {
      onClose()
      setCurrentStep(1)
    }
  }, [isSubmitting, onClose])

  if (!isOpen) return null

  return (
    <div 
      className="modal-overlay"
      data-testid={testId}
      onClick={(e) => e.target === e.currentTarget && handleClose()}
    >
      <div className={`modal-content renewal-modal ${className}`}>
        {/* Modal Header */}
        <div className="modal-header">
          <h2 className="modal-title">
            {currentStep === 1 ? 'Add New Renewal' : 'Edit Renewal'}
          </h2>
          <p className="modal-subtitle">
            {currentStep === 1 
              ? 'Enter the details of the renewal you want to track.'
              : 'Edit the details of this renewal.'
            }
          </p>
          <button 
            className="modal-close"
            onClick={handleClose}
            disabled={isSubmitting}
            aria-label="Close modal"
          >
            ×
          </button>
        </div>

        {/* Step Indicator */}
        <div className="step-indicator">
          <div className={`step ${currentStep === 1 ? 'active' : 'completed'}`}>
            <span className="step-number">1</span>
            <span className="step-label">Renewal Details</span>
          </div>
          <div className={`step ${currentStep === 2 ? 'active' : ''}`}>
            <span className="step-number">2</span>
            <span className="step-label">Set Up Alerts</span>
          </div>
        </div>

        {/* Modal Body */}
        <div className="modal-body">
          {currentStep === 1 ? (
            <RenewalDetailsStep
              data={renewalData}
              onChange={setRenewalData}
              onNext={handleNextStep}
              onCancel={handleClose}
            />
          ) : (
            <SetupAlertsStep
              data={alertsData}
              onChange={setAlertsData}
              onBack={handleBackToDetails}
              onSubmit={handleSubmit}
              isSubmitting={isSubmitting}
            />
          )}
        </div>
      </div>
    </div>
  )
}

export default AddRenewalModal
