import { fetchAuthSession, signOut as amplifySignOut } from 'aws-amplify/auth'
import { configureAmplify } from './amplify-config'
import { clientConfig } from './client-config'
import { validateAuthCookie } from './jwt-validator'

// Get login URL using configuration
export const getLoginUrl = () => {
  const { cognitoDomain, userPoolClientId } = clientConfig.aws;
  const { redirectSignIn } = clientConfig.auth;

  return `https://${cognitoDomain}/login?client_id=${userPoolClientId}&response_type=code&scope=aws.cognito.signin.user.admin+email+openid+profile&redirect_uri=${encodeURIComponent(redirectSignIn)}`;
}

// Set auth cookie
export const setAuthCookie = (idToken: string) => {
  document.cookie = `idToken=${idToken}; path=/; max-age=${60*60*24*7}; SameSite=Lax`;
  console.log('Auth cookie set successfully');
}

// Check if user is authenticated
export const isAuthenticated = async () => {
  try {
    // Ensure Amplify is configured
    configureAmplify();
    
    const session = await fetchAuthSession();
    return !!session?.tokens?.idToken;
  } catch (error) {
    console.error('Auth check error:', error);
    return false;
  }
}

// Sign out
export const signOut = async () => {
  try {
    // Ensure Amplify is configured
    configureAmplify();
    
    // Sign out from Amplify
    await amplifySignOut({ global: true });
    
    // Clear local storage and cookies
    localStorage.removeItem('isAuthenticated');
    document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';
    
    return true;
  } catch (error) {
    console.error('Error signing out:', error);
    
    // Clear local storage and cookies anyway
    localStorage.removeItem('isAuthenticated');
    document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';
    
    return true;
  }
}

// Note: JWT parsing and validation is now handled by the secure jwt-validator module

// Server-side function to verify authentication with secure JWT validation
export const verifyAuth = async (request: Request) => {
  try {
    const authCookie = request.headers.get('Cookie')?.split(';')
      .find(c => c.trim().startsWith('idToken='))
      ?.split('=')[1];

    if (!authCookie) {
      return false;
    }

    // Use secure JWT validation with signature verification
    const payload = await validateAuthCookie(authCookie);
    if (!payload) {
      return false;
    }

    return {
      isAuthenticated: true,
      userId: payload.sub,
      email: payload.email,
      name: payload.name || payload.given_name,
      roles: payload['cognito:groups'] || []
    };
  } catch (error) {
    console.error('Auth verification error:', error);
    return false;
  }
}

























