import { fetchAuthSession, signOut as amplifySignOut } from 'aws-amplify/auth'
import { ensureAmplifyConfigured } from './services/amplify-service'
import { clientConfig } from './client-config'
import { validateAuthCookie } from './jwt-validator'

// Get login URL using configuration
export const getLoginUrl = () => {
  const { cognitoDomain, userPoolClientId } = clientConfig.aws;
  const { redirectSignIn } = clientConfig.auth;

  return `https://${cognitoDomain}/login?client_id=${userPoolClientId}&response_type=code&scope=aws.cognito.signin.user.admin+email+openid+profile&redirect_uri=${encodeURIComponent(redirectSignIn)}`;
}

// Get session configuration from environment variables
const getSessionConfig = () => {
  const cookieMaxAge = process.env.SESSION_COOKIE_MAX_AGE
    ? parseInt(process.env.SESSION_COOKIE_MAX_AGE)
    : 60 * 60 * 24 * 7; // Default: 7 days

  const refreshThreshold = process.env.TOKEN_REFRESH_THRESHOLD
    ? parseInt(process.env.TOKEN_REFRESH_THRESHOLD)
    : 300; // Default: 5 minutes

  const autoRefresh = process.env.AUTO_REFRESH_TOKENS !== 'false'; // Default: true

  return { cookieMaxAge, refreshThreshold, autoRefresh };
};

// Set auth cookie with configurable expiration
export const setAuthCookie = (idToken: string) => {
  const { cookieMaxAge } = getSessionConfig();
  document.cookie = `idToken=${idToken}; path=/; max-age=${cookieMaxAge}; SameSite=Lax; Secure=${location.protocol === 'https:'}`;
  console.log(`Auth cookie set successfully with ${cookieMaxAge} seconds expiration (${Math.round(cookieMaxAge / 86400)} days)`);
}

// Check if token needs refresh based on expiration time
export const shouldRefreshToken = (token: string): boolean => {
  try {
    const { refreshThreshold } = getSessionConfig();

    // Decode JWT payload (without verification - just to check expiration)
    const payload = JSON.parse(atob(token.split('.')[1]));
    const expirationTime = payload.exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();
    const timeUntilExpiration = expirationTime - currentTime;

    // Refresh if token expires within the threshold
    return timeUntilExpiration <= (refreshThreshold * 1000);
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true; // Assume refresh needed if we can't parse the token
  }
};

// Refresh authentication tokens
export const refreshAuthTokens = async (): Promise<boolean> => {
  try {
    const { autoRefresh } = getSessionConfig();

    if (!autoRefresh) {
      console.log('Auto-refresh is disabled');
      return false;
    }

    console.log('Attempting to refresh authentication tokens...');
    await ensureAmplifyConfigured();

    // Force refresh the session
    const session = await fetchAuthSession({ forceRefresh: true });

    if (session?.tokens?.idToken) {
      // Update the cookie with the new token
      setAuthCookie(session.tokens.idToken.toString());
      console.log('✅ Tokens refreshed successfully');
      return true;
    } else {
      console.warn('❌ No ID token found after refresh');
      return false;
    }
  } catch (error) {
    console.error('❌ Token refresh failed:', error);
    return false;
  }
};

// Check if user is authenticated (with automatic token refresh)
export const isAuthenticated = async () => {
  try {
    // Ensure Amplify is configured
    await ensureAmplifyConfigured();

    const session = await fetchAuthSession();
    const idToken = session?.tokens?.idToken?.toString();

    if (!idToken) {
      return false;
    }

    // Check if token needs refresh
    if (shouldRefreshToken(idToken)) {
      console.log('Token is close to expiration, attempting refresh...');
      const refreshed = await refreshAuthTokens();

      if (!refreshed) {
        console.log('Token refresh failed, user needs to re-authenticate');
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Auth check error:', error);
    return false;
  }
}

// Sign out
export const signOut = async () => {
  try {
    // Ensure Amplify is configured
    await ensureAmplifyConfigured();
    
    // Sign out from Amplify
    await amplifySignOut({ global: true });
    
    // Clear local storage and cookies
    localStorage.removeItem('isAuthenticated');
    document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';
    
    return true;
  } catch (error) {
    console.error('Error signing out:', error);
    
    // Clear local storage and cookies anyway
    localStorage.removeItem('isAuthenticated');
    document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';
    
    return true;
  }
}

// Note: JWT parsing and validation is now handled by the secure jwt-validator module

// Server-side function to verify authentication with secure JWT validation
export const verifyAuth = async (request: Request) => {
  try {
    const authCookie = request.headers.get('Cookie')?.split(';')
      .find(c => c.trim().startsWith('idToken='))
      ?.split('=')[1];

    if (!authCookie) {
      return false;
    }

    // Use secure JWT validation with signature verification
    const payload = await validateAuthCookie(authCookie);
    if (!payload) {
      return false;
    }

    return {
      isAuthenticated: true,
      userId: payload.sub,
      email: payload.email,
      name: payload.name || payload.given_name,
      roles: payload['cognito:groups'] || []
    };
  } catch (error) {
    console.error('Auth verification error:', error);
    return false;
  }
}

























