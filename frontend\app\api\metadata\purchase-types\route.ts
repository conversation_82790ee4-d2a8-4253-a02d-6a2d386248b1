/**
 * Purchase Types API Endpoint
 * 
 * Provides access to purchase type metadata
 * GET /api/metadata/purchase-types - Returns active purchase types
 */

import { NextRequest } from 'next/server';
import { requireAuth } from '@/lib/auth-middleware';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  ApiErrorCode, 
  HttpStatus,
  withErrorHandling 
} from '@/lib/api-response';
import { executeQuery } from '@/lib/database';
import { PurchaseType } from '../route';

// GET /api/metadata/purchase-types - Get active purchase types
export const GET = withErrorHandling(async (request: NextRequest) => {
  // Verify authentication
  const authResult = await requireAuth();
  if (!authResult.success) {
    return authResult.response!;
  }

  try {
    // Query purchase types from metadata schema
    const result = await executeQuery<PurchaseType>(
      `SELECT 
        PurchaseTypeID,
        PurchaseTypeName,
        Active,
        DisplayOrder
      FROM metadata.PurchaseTypes 
      WHERE Active = true 
      ORDER BY DisplayOrder ASC, PurchaseTypeName ASC`,
      []
    );

    if (!result.success) {
      console.error('Failed to fetch purchase types:', result.error);
      return createErrorResponse(
        'Failed to fetch purchase types',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    // Log successful fetch
    console.log(`Purchase types fetched successfully: ${result.data?.length || 0} records`);

    return createSuccessResponse(
      result.data || [],
      'Purchase types retrieved successfully'
    );

  } catch (error) {
    console.error('Error fetching purchase types:', error);
    return createErrorResponse(
      'Failed to fetch purchase types',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});
