{"version": 3, "names": ["cov_44vatrfhc", "actualCoverage", "z", "envSchema", "s", "object", "NEXT_PUBLIC_AWS_REGION", "string", "min", "NEXT_PUBLIC_AWS_USER_POOLS_ID", "NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID", "NEXT_PUBLIC_AWS_COGNITO_DOMAIN", "NEXT_PUBLIC_REDIRECT_SIGN_IN", "url", "NEXT_PUBLIC_REDIRECT_SIGN_OUT", "DB_USER", "optional", "DB_PASSWORD", "DB_HOST", "DB_NAME", "DATABASE_URL", "DATABASE_SSL", "NODE_ENV", "enum", "default", "validateEnv", "f", "parse", "process", "env", "error", "console", "Error", "publicConfig", "aws", "region", "userPoolId", "userPoolClientId", "cognitoDomain", "auth", "redirectSignIn", "redirectSignOut", "app", "environment", "isDevelopment", "isProduction", "serverConfig", "database", "user", "password", "host", "name", "ssl", "getAuthConfig", "getAwsConfig", "getDatabaseConfig", "window", "b", "validateConfig", "requiredPublicVars", "missing", "filter", "key", "length", "join"], "sources": ["config.ts"], "sourcesContent": ["/**\n * Centralized Configuration Management\n * \n * This module provides a secure, centralized way to manage all application configuration.\n * It validates environment variables and provides type-safe access to configuration values.\n */\n\nimport { z } from 'zod';\n\n// Environment validation schema\nconst envSchema = z.object({\n  // AWS Configuration\n  NEXT_PUBLIC_AWS_REGION: z.string().min(1, 'AWS region is required'),\n  NEXT_PUBLIC_AWS_USER_POOLS_ID: z.string().min(1, 'User pool ID is required'),\n  NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID: z.string().min(1, 'User pool client ID is required'),\n  NEXT_PUBLIC_AWS_COGNITO_DOMAIN: z.string().min(1, 'Cognito domain is required'),\n  \n  // Redirect URLs\n  NEXT_PUBLIC_REDIRECT_SIGN_IN: z.string().url('Invalid sign-in redirect URL'),\n  NEXT_PUBLIC_REDIRECT_SIGN_OUT: z.string().url('Invalid sign-out redirect URL'),\n  \n  // Database Configuration (server-side only)\n  DB_USER: z.string().optional(),\n  DB_PASSWORD: z.string().optional(),\n  DB_HOST: z.string().optional(),\n  DB_NAME: z.string().optional(),\n  DATABASE_URL: z.string().optional(),\n  DATABASE_SSL: z.string().optional(),\n  \n  // Environment\n  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),\n});\n\n// Validate environment variables\nfunction validateEnv() {\n  try {\n    return envSchema.parse(process.env);\n  } catch (error) {\n    console.error('❌ Invalid environment configuration:', error);\n    throw new Error('Environment validation failed. Please check your .env files.');\n  }\n}\n\n// Get validated environment variables\nconst env = validateEnv();\n\n// Public configuration (safe to expose to client)\nexport const publicConfig = {\n  aws: {\n    region: env.NEXT_PUBLIC_AWS_REGION,\n    userPoolId: env.NEXT_PUBLIC_AWS_USER_POOLS_ID,\n    userPoolClientId: env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID,\n    cognitoDomain: env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN,\n  },\n  auth: {\n    redirectSignIn: env.NEXT_PUBLIC_REDIRECT_SIGN_IN,\n    redirectSignOut: env.NEXT_PUBLIC_REDIRECT_SIGN_OUT,\n  },\n  app: {\n    environment: env.NODE_ENV,\n    isDevelopment: env.NODE_ENV === 'development',\n    isProduction: env.NODE_ENV === 'production',\n  },\n} as const;\n\n// Server-side configuration (never expose to client)\nexport const serverConfig = {\n  database: {\n    user: env.DB_USER,\n    password: env.DB_PASSWORD,\n    host: env.DB_HOST,\n    name: env.DB_NAME,\n    url: env.DATABASE_URL,\n    ssl: env.DATABASE_SSL === 'true',\n  },\n} as const;\n\n// Type exports for better TypeScript support\nexport type PublicConfig = typeof publicConfig;\nexport type ServerConfig = typeof serverConfig;\n\n// Utility functions\nexport const getAuthConfig = () => publicConfig.auth;\nexport const getAwsConfig = () => publicConfig.aws;\nexport const getDatabaseConfig = () => {\n  if (typeof window !== 'undefined') {\n    throw new Error('Database configuration is not available on the client side');\n  }\n  return serverConfig.database;\n};\n\n// Configuration validation for runtime checks\nexport const validateConfig = () => {\n  const requiredPublicVars = [\n    'NEXT_PUBLIC_AWS_REGION',\n    'NEXT_PUBLIC_AWS_USER_POOLS_ID',\n    'NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID',\n    'NEXT_PUBLIC_AWS_COGNITO_DOMAIN',\n  ];\n\n  const missing = requiredPublicVars.filter(key => !process.env[key]);\n  \n  if (missing.length > 0) {\n    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);\n  }\n\n  return true;\n};\n\n// Export environment for backward compatibility (to be removed)\nexport { env };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAfZ;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASE,CAAC,QAAQ,KAAK;;AAEvB;AACA,MAAMC,SAAS;AAAA;AAAA,CAAAH,aAAA,GAAAI,CAAA,OAAGF,CAAC,CAACG,MAAM,CAAC;EACzB;EACAC,sBAAsB,EAAEJ,CAAC,CAACK,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC;EACnEC,6BAA6B,EAAEP,CAAC,CAACK,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,0BAA0B,CAAC;EAC5EE,wCAAwC,EAAER,CAAC,CAACK,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,iCAAiC,CAAC;EAC9FG,8BAA8B,EAAET,CAAC,CAACK,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,4BAA4B,CAAC;EAE/E;EACAI,4BAA4B,EAAEV,CAAC,CAACK,MAAM,CAAC,CAAC,CAACM,GAAG,CAAC,8BAA8B,CAAC;EAC5EC,6BAA6B,EAAEZ,CAAC,CAACK,MAAM,CAAC,CAAC,CAACM,GAAG,CAAC,+BAA+B,CAAC;EAE9E;EACAE,OAAO,EAAEb,CAAC,CAACK,MAAM,CAAC,CAAC,CAACS,QAAQ,CAAC,CAAC;EAC9BC,WAAW,EAAEf,CAAC,CAACK,MAAM,CAAC,CAAC,CAACS,QAAQ,CAAC,CAAC;EAClCE,OAAO,EAAEhB,CAAC,CAACK,MAAM,CAAC,CAAC,CAACS,QAAQ,CAAC,CAAC;EAC9BG,OAAO,EAAEjB,CAAC,CAACK,MAAM,CAAC,CAAC,CAACS,QAAQ,CAAC,CAAC;EAC9BI,YAAY,EAAElB,CAAC,CAACK,MAAM,CAAC,CAAC,CAACS,QAAQ,CAAC,CAAC;EACnCK,YAAY,EAAEnB,CAAC,CAACK,MAAM,CAAC,CAAC,CAACS,QAAQ,CAAC,CAAC;EAEnC;EACAM,QAAQ,EAAEpB,CAAC,CAACqB,IAAI,CAAC,CAAC,aAAa,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,aAAa;AAC/E,CAAC,CAAC;;AAEF;AACA,SAASC,WAAWA,CAAA,EAAG;EAAA;EAAAzB,aAAA,GAAA0B,CAAA;EAAA1B,aAAA,GAAAI,CAAA;EACrB,IAAI;IAAA;IAAAJ,aAAA,GAAAI,CAAA;IACF,OAAOD,SAAS,CAACwB,KAAK,CAACC,OAAO,CAACC,GAAG,CAAC;EACrC,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA;IAAA9B,aAAA,GAAAI,CAAA;IACd2B,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAAC;IAAA9B,aAAA,GAAAI,CAAA;IAC7D,MAAM,IAAI4B,KAAK,CAAC,8DAA8D,CAAC;EACjF;AACF;;AAEA;AACA,MAAMH,GAAG;AAAA;AAAA,CAAA7B,aAAA,GAAAI,CAAA,OAAGqB,WAAW,CAAC,CAAC;;AAEzB;AACA,OAAO,MAAMQ,YAAY;AAAA;AAAA,CAAAjC,aAAA,GAAAI,CAAA,OAAG;EAC1B8B,GAAG,EAAE;IACHC,MAAM,EAAEN,GAAG,CAACvB,sBAAsB;IAClC8B,UAAU,EAAEP,GAAG,CAACpB,6BAA6B;IAC7C4B,gBAAgB,EAAER,GAAG,CAACnB,wCAAwC;IAC9D4B,aAAa,EAAET,GAAG,CAAClB;EACrB,CAAC;EACD4B,IAAI,EAAE;IACJC,cAAc,EAAEX,GAAG,CAACjB,4BAA4B;IAChD6B,eAAe,EAAEZ,GAAG,CAACf;EACvB,CAAC;EACD4B,GAAG,EAAE;IACHC,WAAW,EAAEd,GAAG,CAACP,QAAQ;IACzBsB,aAAa,EAAEf,GAAG,CAACP,QAAQ,KAAK,aAAa;IAC7CuB,YAAY,EAAEhB,GAAG,CAACP,QAAQ,KAAK;EACjC;AACF,CAAC,CAAS;;AAEV;AACA,OAAO,MAAMwB,YAAY;AAAA;AAAA,CAAA9C,aAAA,GAAAI,CAAA,OAAG;EAC1B2C,QAAQ,EAAE;IACRC,IAAI,EAAEnB,GAAG,CAACd,OAAO;IACjBkC,QAAQ,EAAEpB,GAAG,CAACZ,WAAW;IACzBiC,IAAI,EAAErB,GAAG,CAACX,OAAO;IACjBiC,IAAI,EAAEtB,GAAG,CAACV,OAAO;IACjBN,GAAG,EAAEgB,GAAG,CAACT,YAAY;IACrBgC,GAAG,EAAEvB,GAAG,CAACR,YAAY,KAAK;EAC5B;AACF,CAAC,CAAS;;AAEV;AAAA;AAIA;AAAArB,aAAA,GAAAI,CAAA;AACA,OAAO,MAAMiD,aAAa,GAAGA,CAAA,KAAM;EAAA;EAAArD,aAAA,GAAA0B,CAAA;EAAA1B,aAAA,GAAAI,CAAA;EAAA,OAAA6B,YAAY,CAACM,IAAI;AAAD,CAAC;AAAC;AAAAvC,aAAA,GAAAI,CAAA;AACrD,OAAO,MAAMkD,YAAY,GAAGA,CAAA,KAAM;EAAA;EAAAtD,aAAA,GAAA0B,CAAA;EAAA1B,aAAA,GAAAI,CAAA;EAAA,OAAA6B,YAAY,CAACC,GAAG;AAAD,CAAC;AAAC;AAAAlC,aAAA,GAAAI,CAAA;AACnD,OAAO,MAAMmD,iBAAiB,GAAGA,CAAA,KAAM;EAAA;EAAAvD,aAAA,GAAA0B,CAAA;EAAA1B,aAAA,GAAAI,CAAA;EACrC,IAAI,OAAOoD,MAAM,KAAK,WAAW,EAAE;IAAA;IAAAxD,aAAA,GAAAyD,CAAA;IAAAzD,aAAA,GAAAI,CAAA;IACjC,MAAM,IAAI4B,KAAK,CAAC,4DAA4D,CAAC;EAC/E,CAAC;EAAA;EAAA;IAAAhC,aAAA,GAAAyD,CAAA;EAAA;EAAAzD,aAAA,GAAAI,CAAA;EACD,OAAO0C,YAAY,CAACC,QAAQ;AAC9B,CAAC;;AAED;AAAA;AAAA/C,aAAA,GAAAI,CAAA;AACA,OAAO,MAAMsD,cAAc,GAAGA,CAAA,KAAM;EAAA;EAAA1D,aAAA,GAAA0B,CAAA;EAClC,MAAMiC,kBAAkB;EAAA;EAAA,CAAA3D,aAAA,GAAAI,CAAA,QAAG,CACzB,wBAAwB,EACxB,+BAA+B,EAC/B,0CAA0C,EAC1C,gCAAgC,CACjC;EAED,MAAMwD,OAAO;EAAA;EAAA,CAAA5D,aAAA,GAAAI,CAAA,QAAGuD,kBAAkB,CAACE,MAAM,CAACC,GAAG,IAAI;IAAA;IAAA9D,aAAA,GAAA0B,CAAA;IAAA1B,aAAA,GAAAI,CAAA;IAAA,QAACwB,OAAO,CAACC,GAAG,CAACiC,GAAG,CAAC;EAAD,CAAC,CAAC;EAAC;EAAA9D,aAAA,GAAAI,CAAA;EAEpE,IAAIwD,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;IAAA;IAAA/D,aAAA,GAAAyD,CAAA;IAAAzD,aAAA,GAAAI,CAAA;IACtB,MAAM,IAAI4B,KAAK,CAAC,2CAA2C4B,OAAO,CAACI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EAClF,CAAC;EAAA;EAAA;IAAAhE,aAAA,GAAAyD,CAAA;EAAA;EAAAzD,aAAA,GAAAI,CAAA;EAED,OAAO,IAAI;AACb,CAAC;;AAED;AACA,SAASyB,GAAG", "ignoreList": []}