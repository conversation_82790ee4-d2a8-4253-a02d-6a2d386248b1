da7d894d5d2c7577db478483c19a061e
/* istanbul ignore next */
function cov_1vq3bzo45z() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\auth.ts";
  var hash = "8d80eee1719c12d36e9deaa8e24bde8ac25864f4";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\auth.ts",
    statementMap: {
      "0": {
        start: {
          line: 7,
          column: 27
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 8,
          column: 46
        },
        end: {
          line: 8,
          column: 62
        }
      },
      "2": {
        start: {
          line: 9,
          column: 29
        },
        end: {
          line: 9,
          column: 46
        }
      },
      "3": {
        start: {
          line: 11,
          column: 2
        },
        end: {
          line: 11,
          column: 199
        }
      },
      "4": {
        start: {
          line: 15,
          column: 29
        },
        end: {
          line: 18,
          column: 1
        }
      },
      "5": {
        start: {
          line: 16,
          column: 2
        },
        end: {
          line: 16,
          column: 86
        }
      },
      "6": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 17,
          column: 46
        }
      },
      "7": {
        start: {
          line: 21,
          column: 31
        },
        end: {
          line: 32,
          column: 1
        }
      },
      "8": {
        start: {
          line: 22,
          column: 2
        },
        end: {
          line: 31,
          column: 3
        }
      },
      "9": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 23
        }
      },
      "10": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 44
        }
      },
      "11": {
        start: {
          line: 27,
          column: 4
        },
        end: {
          line: 27,
          column: 38
        }
      },
      "12": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 29,
          column: 46
        }
      },
      "13": {
        start: {
          line: 30,
          column: 4
        },
        end: {
          line: 30,
          column: 17
        }
      },
      "14": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 57,
          column: 1
        }
      },
      "15": {
        start: {
          line: 36,
          column: 2
        },
        end: {
          line: 56,
          column: 3
        }
      },
      "16": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 38,
          column: 23
        }
      },
      "17": {
        start: {
          line: 41,
          column: 4
        },
        end: {
          line: 41,
          column: 43
        }
      },
      "18": {
        start: {
          line: 44,
          column: 4
        },
        end: {
          line: 44,
          column: 47
        }
      },
      "19": {
        start: {
          line: 45,
          column: 4
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "20": {
        start: {
          line: 47,
          column: 4
        },
        end: {
          line: 47,
          column: 16
        }
      },
      "21": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 49,
          column: 47
        }
      },
      "22": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 52,
          column: 47
        }
      },
      "23": {
        start: {
          line: 53,
          column: 4
        },
        end: {
          line: 53,
          column: 66
        }
      },
      "24": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 55,
          column: 16
        }
      },
      "25": {
        start: {
          line: 62,
          column: 26
        },
        end: {
          line: 89,
          column: 1
        }
      },
      "26": {
        start: {
          line: 63,
          column: 2
        },
        end: {
          line: 88,
          column: 3
        }
      },
      "27": {
        start: {
          line: 64,
          column: 23
        },
        end: {
          line: 66,
          column: 21
        }
      },
      "28": {
        start: {
          line: 65,
          column: 17
        },
        end: {
          line: 65,
          column: 48
        }
      },
      "29": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 70,
          column: 5
        }
      },
      "30": {
        start: {
          line: 69,
          column: 6
        },
        end: {
          line: 69,
          column: 19
        }
      },
      "31": {
        start: {
          line: 73,
          column: 20
        },
        end: {
          line: 73,
          column: 56
        }
      },
      "32": {
        start: {
          line: 74,
          column: 4
        },
        end: {
          line: 76,
          column: 5
        }
      },
      "33": {
        start: {
          line: 75,
          column: 6
        },
        end: {
          line: 75,
          column: 19
        }
      },
      "34": {
        start: {
          line: 78,
          column: 4
        },
        end: {
          line: 84,
          column: 6
        }
      },
      "35": {
        start: {
          line: 86,
          column: 4
        },
        end: {
          line: 86,
          column: 53
        }
      },
      "36": {
        start: {
          line: 87,
          column: 4
        },
        end: {
          line: 87,
          column: 17
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 7,
            column: 27
          },
          end: {
            line: 7,
            column: 28
          }
        },
        loc: {
          start: {
            line: 7,
            column: 33
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 7
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 15,
            column: 29
          },
          end: {
            line: 15,
            column: 30
          }
        },
        loc: {
          start: {
            line: 15,
            column: 50
          },
          end: {
            line: 18,
            column: 1
          }
        },
        line: 15
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 21,
            column: 31
          },
          end: {
            line: 21,
            column: 32
          }
        },
        loc: {
          start: {
            line: 21,
            column: 43
          },
          end: {
            line: 32,
            column: 1
          }
        },
        line: 21
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 35,
            column: 23
          },
          end: {
            line: 35,
            column: 24
          }
        },
        loc: {
          start: {
            line: 35,
            column: 35
          },
          end: {
            line: 57,
            column: 1
          }
        },
        line: 35
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 62,
            column: 26
          },
          end: {
            line: 62,
            column: 27
          }
        },
        loc: {
          start: {
            line: 62,
            column: 54
          },
          end: {
            line: 89,
            column: 1
          }
        },
        line: 62
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 65,
            column: 12
          },
          end: {
            line: 65,
            column: 13
          }
        },
        loc: {
          start: {
            line: 65,
            column: 17
          },
          end: {
            line: 65,
            column: 48
          }
        },
        line: 65
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 68,
            column: 4
          },
          end: {
            line: 70,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 68,
            column: 4
          },
          end: {
            line: 70,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 68
      },
      "1": {
        loc: {
          start: {
            line: 74,
            column: 4
          },
          end: {
            line: 76,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 4
          },
          end: {
            line: 76,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 74
      },
      "2": {
        loc: {
          start: {
            line: 82,
            column: 12
          },
          end: {
            line: 82,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 82,
            column: 12
          },
          end: {
            line: 82,
            column: 24
          }
        }, {
          start: {
            line: 82,
            column: 28
          },
          end: {
            line: 82,
            column: 46
          }
        }],
        line: 82
      },
      "3": {
        loc: {
          start: {
            line: 83,
            column: 13
          },
          end: {
            line: 83,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 83,
            column: 13
          },
          end: {
            line: 83,
            column: 38
          }
        }, {
          start: {
            line: 83,
            column: 42
          },
          end: {
            line: 83,
            column: 44
          }
        }],
        line: 83
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "8d80eee1719c12d36e9deaa8e24bde8ac25864f4"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1vq3bzo45z = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1vq3bzo45z();
import { fetchAuthSession, signOut as amplifySignOut } from 'aws-amplify/auth';
import { configureAmplify } from './amplify-config';
import { clientConfig } from './client-config';
import { validateAuthCookie } from './jwt-validator';

// Get login URL using configuration
/* istanbul ignore next */
cov_1vq3bzo45z().s[0]++;
export const getLoginUrl = () => {
  /* istanbul ignore next */
  cov_1vq3bzo45z().f[0]++;
  const {
    cognitoDomain,
    userPoolClientId
  } =
  /* istanbul ignore next */
  (cov_1vq3bzo45z().s[1]++, clientConfig.aws);
  const {
    redirectSignIn
  } =
  /* istanbul ignore next */
  (cov_1vq3bzo45z().s[2]++, clientConfig.auth);
  /* istanbul ignore next */
  cov_1vq3bzo45z().s[3]++;
  return `https://${cognitoDomain}/login?client_id=${userPoolClientId}&response_type=code&scope=aws.cognito.signin.user.admin+email+openid+profile&redirect_uri=${encodeURIComponent(redirectSignIn)}`;
};

// Set auth cookie
/* istanbul ignore next */
cov_1vq3bzo45z().s[4]++;
export const setAuthCookie = idToken => {
  /* istanbul ignore next */
  cov_1vq3bzo45z().f[1]++;
  cov_1vq3bzo45z().s[5]++;
  document.cookie = `idToken=${idToken}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Lax`;
  /* istanbul ignore next */
  cov_1vq3bzo45z().s[6]++;
  console.log('Auth cookie set successfully');
};

// Check if user is authenticated
/* istanbul ignore next */
cov_1vq3bzo45z().s[7]++;
export const isAuthenticated = async () => {
  /* istanbul ignore next */
  cov_1vq3bzo45z().f[2]++;
  cov_1vq3bzo45z().s[8]++;
  try {
    /* istanbul ignore next */
    cov_1vq3bzo45z().s[9]++;
    // Ensure Amplify is configured
    configureAmplify();
    const session =
    /* istanbul ignore next */
    (cov_1vq3bzo45z().s[10]++, await fetchAuthSession());
    /* istanbul ignore next */
    cov_1vq3bzo45z().s[11]++;
    return !!session?.tokens?.idToken;
  } catch (error) {
    /* istanbul ignore next */
    cov_1vq3bzo45z().s[12]++;
    console.error('Auth check error:', error);
    /* istanbul ignore next */
    cov_1vq3bzo45z().s[13]++;
    return false;
  }
};

// Sign out
/* istanbul ignore next */
cov_1vq3bzo45z().s[14]++;
export const signOut = async () => {
  /* istanbul ignore next */
  cov_1vq3bzo45z().f[3]++;
  cov_1vq3bzo45z().s[15]++;
  try {
    /* istanbul ignore next */
    cov_1vq3bzo45z().s[16]++;
    // Ensure Amplify is configured
    configureAmplify();

    // Sign out from Amplify
    /* istanbul ignore next */
    cov_1vq3bzo45z().s[17]++;
    await amplifySignOut({
      global: true
    });

    // Clear local storage and cookies
    /* istanbul ignore next */
    cov_1vq3bzo45z().s[18]++;
    localStorage.removeItem('isAuthenticated');
    /* istanbul ignore next */
    cov_1vq3bzo45z().s[19]++;
    document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';
    /* istanbul ignore next */
    cov_1vq3bzo45z().s[20]++;
    return true;
  } catch (error) {
    /* istanbul ignore next */
    cov_1vq3bzo45z().s[21]++;
    console.error('Error signing out:', error);

    // Clear local storage and cookies anyway
    /* istanbul ignore next */
    cov_1vq3bzo45z().s[22]++;
    localStorage.removeItem('isAuthenticated');
    /* istanbul ignore next */
    cov_1vq3bzo45z().s[23]++;
    document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';
    /* istanbul ignore next */
    cov_1vq3bzo45z().s[24]++;
    return true;
  }
};

// Note: JWT parsing and validation is now handled by the secure jwt-validator module

// Server-side function to verify authentication with secure JWT validation
/* istanbul ignore next */
cov_1vq3bzo45z().s[25]++;
export const verifyAuth = async request => {
  /* istanbul ignore next */
  cov_1vq3bzo45z().f[4]++;
  cov_1vq3bzo45z().s[26]++;
  try {
    const authCookie =
    /* istanbul ignore next */
    (cov_1vq3bzo45z().s[27]++, request.headers.get('Cookie')?.split(';').find(c => {
      /* istanbul ignore next */
      cov_1vq3bzo45z().f[5]++;
      cov_1vq3bzo45z().s[28]++;
      return c.trim().startsWith('idToken=');
    })?.split('=')[1]);
    /* istanbul ignore next */
    cov_1vq3bzo45z().s[29]++;
    if (!authCookie) {
      /* istanbul ignore next */
      cov_1vq3bzo45z().b[0][0]++;
      cov_1vq3bzo45z().s[30]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_1vq3bzo45z().b[0][1]++;
    }

    // Use secure JWT validation with signature verification
    const payload =
    /* istanbul ignore next */
    (cov_1vq3bzo45z().s[31]++, await validateAuthCookie(authCookie));
    /* istanbul ignore next */
    cov_1vq3bzo45z().s[32]++;
    if (!payload) {
      /* istanbul ignore next */
      cov_1vq3bzo45z().b[1][0]++;
      cov_1vq3bzo45z().s[33]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_1vq3bzo45z().b[1][1]++;
    }
    cov_1vq3bzo45z().s[34]++;
    return {
      isAuthenticated: true,
      userId: payload.sub,
      email: payload.email,
      name:
      /* istanbul ignore next */
      (cov_1vq3bzo45z().b[2][0]++, payload.name) ||
      /* istanbul ignore next */
      (cov_1vq3bzo45z().b[2][1]++, payload.given_name),
      roles:
      /* istanbul ignore next */
      (cov_1vq3bzo45z().b[3][0]++, payload['cognito:groups']) ||
      /* istanbul ignore next */
      (cov_1vq3bzo45z().b[3][1]++, [])
    };
  } catch (error) {
    /* istanbul ignore next */
    cov_1vq3bzo45z().s[35]++;
    console.error('Auth verification error:', error);
    /* istanbul ignore next */
    cov_1vq3bzo45z().s[36]++;
    return false;
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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