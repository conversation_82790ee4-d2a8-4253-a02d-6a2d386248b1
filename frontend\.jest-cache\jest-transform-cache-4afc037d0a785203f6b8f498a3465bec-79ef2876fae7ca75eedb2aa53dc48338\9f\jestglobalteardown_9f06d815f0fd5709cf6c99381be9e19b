df328b2e4ac2e88c372ff912bee840c5
"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
/**
 * Jest Global Teardown
 * 
 * Runs once after all tests complete.
 * Used for global test environment cleanup.
 */
var _default = async () => {
  console.log('🧹 Cleaning up test environment...');

  // Clean up any global resources
  // This could include closing database connections,
  // cleaning up temporary files, etc.

  // Reset environment variables
  delete process.env.NODE_ENV;
  delete process.env.TZ;
  delete process.env.AWS_REGION;
  delete process.env.AWS_ACCESS_KEY_ID;
  delete process.env.AWS_SECRET_ACCESS_KEY;
  delete process.env.DATABASE_URL;
  delete process.env.JWT_SECRET;
  console.log('✅ Test environment cleanup complete');
};
exports.default = _default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfZGVmYXVsdCIsImNvbnNvbGUiLCJsb2ciLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJUWiIsIkFXU19SRUdJT04iLCJBV1NfQUNDRVNTX0tFWV9JRCIsIkFXU19TRUNSRVRfQUNDRVNTX0tFWSIsIkRBVEFCQVNFX1VSTCIsIkpXVF9TRUNSRVQiLCJleHBvcnRzIiwiZGVmYXVsdCJdLCJzb3VyY2VzIjpbImplc3QuZ2xvYmFsLXRlYXJkb3duLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogSmVzdCBHbG9iYWwgVGVhcmRvd25cbiAqIFxuICogUnVucyBvbmNlIGFmdGVyIGFsbCB0ZXN0cyBjb21wbGV0ZS5cbiAqIFVzZWQgZm9yIGdsb2JhbCB0ZXN0IGVudmlyb25tZW50IGNsZWFudXAuXG4gKi9cblxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgKCkgPT4ge1xuICBjb25zb2xlLmxvZygn8J+nuSBDbGVhbmluZyB1cCB0ZXN0IGVudmlyb25tZW50Li4uJylcbiAgXG4gIC8vIENsZWFuIHVwIGFueSBnbG9iYWwgcmVzb3VyY2VzXG4gIC8vIFRoaXMgY291bGQgaW5jbHVkZSBjbG9zaW5nIGRhdGFiYXNlIGNvbm5lY3Rpb25zLFxuICAvLyBjbGVhbmluZyB1cCB0ZW1wb3JhcnkgZmlsZXMsIGV0Yy5cbiAgXG4gIC8vIFJlc2V0IGVudmlyb25tZW50IHZhcmlhYmxlc1xuICBkZWxldGUgcHJvY2Vzcy5lbnYuTk9ERV9FTlZcbiAgZGVsZXRlIHByb2Nlc3MuZW52LlRaXG4gIGRlbGV0ZSBwcm9jZXNzLmVudi5BV1NfUkVHSU9OXG4gIGRlbGV0ZSBwcm9jZXNzLmVudi5BV1NfQUNDRVNTX0tFWV9JRFxuICBkZWxldGUgcHJvY2Vzcy5lbnYuQVdTX1NFQ1JFVF9BQ0NFU1NfS0VZXG4gIGRlbGV0ZSBwcm9jZXNzLmVudi5EQVRBQkFTRV9VUkxcbiAgZGVsZXRlIHByb2Nlc3MuZW52LkpXVF9TRUNSRVRcbiAgXG4gIGNvbnNvbGUubG9nKCfinIUgVGVzdCBlbnZpcm9ubWVudCBjbGVhbnVwIGNvbXBsZXRlJylcbn1cbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBTEEsSUFBQUEsUUFBQSxHQU9lLE1BQUFBLENBQUEsS0FBWTtFQUN6QkMsT0FBTyxDQUFDQyxHQUFHLENBQUMsb0NBQW9DLENBQUM7O0VBRWpEO0VBQ0E7RUFDQTs7RUFFQTtFQUNBLE9BQU9DLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDQyxRQUFRO0VBQzNCLE9BQU9GLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDRSxFQUFFO0VBQ3JCLE9BQU9ILE9BQU8sQ0FBQ0MsR0FBRyxDQUFDRyxVQUFVO0VBQzdCLE9BQU9KLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDSSxpQkFBaUI7RUFDcEMsT0FBT0wsT0FBTyxDQUFDQyxHQUFHLENBQUNLLHFCQUFxQjtFQUN4QyxPQUFPTixPQUFPLENBQUNDLEdBQUcsQ0FBQ00sWUFBWTtFQUMvQixPQUFPUCxPQUFPLENBQUNDLEdBQUcsQ0FBQ08sVUFBVTtFQUU3QlYsT0FBTyxDQUFDQyxHQUFHLENBQUMscUNBQXFDLENBQUM7QUFDcEQsQ0FBQztBQUFBVSxPQUFBLENBQUFDLE9BQUEsR0FBQWIsUUFBQSIsImlnbm9yZUxpc3QiOltdfQ==