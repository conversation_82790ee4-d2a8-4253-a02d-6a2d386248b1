/**
 * Upcoming Renewals Component
 * 
 * Displays renewals that are due soon with proper empty and loading states.
 * Focused responsibility: Rendering upcoming renewals section only.
 */

'use client'

import { Renewal, BaseComponentProps } from '@/lib/types'

interface UpcomingRenewalsProps extends BaseComponentProps {
  renewals: Renewal[]
  isLoading?: boolean
  onRenewalClick?: (renewal: Renewal) => void
  daysThreshold?: number
}

interface UpcomingRenewalItemProps {
  renewal: Renewal
  onClick?: (renewal: Renewal) => void
  daysUntilDue: number
}

function UpcomingRenewalItem({ renewal, onClick, daysUntilDue }: UpcomingRenewalItemProps) {
  const handleClick = () => {
    onClick?.(renewal)
  }

  const getUrgencyColor = (days: number) => {
    if (days <= 7) return 'text-red-600 bg-red-50'
    if (days <= 14) return 'text-orange-600 bg-orange-50'
    if (days <= 30) return 'text-yellow-600 bg-yellow-50'
    return 'text-blue-600 bg-blue-50'
  }

  const getUrgencyText = (days: number) => {
    if (days < 0) return 'Overdue'
    if (days === 0) return 'Due today'
    if (days === 1) return 'Due tomorrow'
    return `Due in ${days} days`
  }

  return (
    <div 
      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
      onClick={handleClick}
      role="button"
      tabIndex={0}
      aria-label={`View details for ${renewal.name}`}
    >
      <div className="flex items-center space-x-3">
        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
          <span className="text-blue-600">📄</span>
        </div>
        <div>
          <h3 className="font-medium text-sm">{renewal.name}</h3>
          <p className="text-xs text-secondary">{renewal.vendor}</p>
          {renewal.annual_cost && (
            <p className="text-xs text-secondary">
              ${renewal.annual_cost.toLocaleString()}/year
            </p>
          )}
        </div>
      </div>
      <div className="text-right">
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(daysUntilDue)}`}>
          {getUrgencyText(daysUntilDue)}
        </span>
        {renewal.due_date && (
          <p className="text-xs text-secondary mt-1">
            {new Date(renewal.due_date).toLocaleDateString()}
          </p>
        )}
      </div>
    </div>
  )
}

function LoadingSkeleton() {
  return (
    <div className="space-y-4">
      {[...Array(3)].map((_, index) => (
        <div key={index} className="flex items-center justify-between p-4 border rounded-lg animate-pulse">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
            <div>
              <div className="h-4 bg-gray-200 rounded w-32 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-24 mb-1"></div>
              <div className="h-3 bg-gray-200 rounded w-20"></div>
            </div>
          </div>
          <div className="text-right">
            <div className="h-6 bg-gray-200 rounded-full w-20 mb-1"></div>
            <div className="h-3 bg-gray-200 rounded w-16"></div>
          </div>
        </div>
      ))}
    </div>
  )
}

function EmptyState() {
  return (
    <div className="text-center py-8">
      <div className="text-6xl mb-4">📄</div>
      <h3 className="text-lg font-medium mb-2">No upcoming renewals</h3>
      <p className="text-secondary">
        There are no software renewals due in the next 30 days.
      </p>
    </div>
  )
}

// Helper function to calculate days until due
function getDaysUntilDue(dueDate: Date): number {
  const today = new Date()
  const due = new Date(dueDate)
  const diffTime = due.getTime() - today.getTime()
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

// Helper function to filter renewals by days threshold
function getUpcomingRenewals(renewals: Renewal[], daysThreshold: number): Renewal[] {
  const now = new Date()
  return renewals.filter(renewal => {
    if (!renewal.due_date) return false
    const dueDate = new Date(renewal.due_date)
    const daysUntilDue = getDaysUntilDue(dueDate)
    return daysUntilDue >= -7 && daysUntilDue <= daysThreshold // Include overdue up to 7 days
  }).sort((a, b) => {
    // Sort by due date, earliest first
    if (!a.due_date || !b.due_date) return 0
    return new Date(a.due_date).getTime() - new Date(b.due_date).getTime()
  })
}

export default function UpcomingRenewals({
  renewals,
  isLoading = false,
  onRenewalClick,
  daysThreshold = 30,
  className = '',
  'data-testid': testId
}: UpcomingRenewalsProps) {
  const upcomingRenewals = getUpcomingRenewals(renewals, daysThreshold)

  return (
    <div 
      className={`card ${className}`}
      data-testid={testId}
      style={{ marginBottom: '24px' }}
    >
      <div className="card-header">
        <h2 className="text-lg font-semibold">Upcoming Renewals</h2>
        {upcomingRenewals.length > 0 && (
          <span className="text-sm text-secondary">
            {upcomingRenewals.length} renewal{upcomingRenewals.length !== 1 ? 's' : ''} due
          </span>
        )}
      </div>
      <div className="card-content">
        {isLoading ? (
          <LoadingSkeleton />
        ) : upcomingRenewals.length > 0 ? (
          <div className="space-y-3">
            {upcomingRenewals.map((renewal) => (
              <UpcomingRenewalItem
                key={renewal.id}
                renewal={renewal}
                onClick={onRenewalClick}
                daysUntilDue={renewal.due_date ? getDaysUntilDue(new Date(renewal.due_date)) : 0}
              />
            ))}
          </div>
        ) : (
          <EmptyState />
        )}
      </div>
    </div>
  )
}
