00fd69973dca1941f23963c1c99507f2
/* istanbul ignore next */
function cov_jg567abuj() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\auth-middleware.ts";
  var hash = "bc58f8907d68b71213dccd59773aeda8f20cf150";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\auth-middleware.ts",
    statementMap: {
      "0": {
        start: {
          line: 30,
          column: 2
        },
        end: {
          line: 50,
          column: 3
        }
      },
      "1": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 41
        }
      },
      "2": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 38,
          column: 5
        }
      },
      "3": {
        start: {
          line: 34,
          column: 6
        },
        end: {
          line: 37,
          column: 8
        }
      },
      "4": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 43,
          column: 6
        }
      },
      "5": {
        start: {
          line: 45,
          column: 4
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "6": {
        start: {
          line: 46,
          column: 4
        },
        end: {
          line: 49,
          column: 6
        }
      },
      "7": {
        start: {
          line: 57,
          column: 21
        },
        end: {
          line: 57,
          column: 40
        }
      },
      "8": {
        start: {
          line: 59,
          column: 2
        },
        end: {
          line: 61,
          column: 3
        }
      },
      "9": {
        start: {
          line: 60,
          column: 4
        },
        end: {
          line: 60,
          column: 22
        }
      },
      "10": {
        start: {
          line: 63,
          column: 16
        },
        end: {
          line: 63,
          column: 78
        }
      },
      "11": {
        start: {
          line: 64,
          column: 20
        },
        end: {
          line: 64,
          column: 45
        }
      },
      "12": {
        start: {
          line: 66,
          column: 26
        },
        end: {
          line: 66,
          column: 70
        }
      },
      "13": {
        start: {
          line: 66,
          column: 45
        },
        end: {
          line: 66,
          column: 69
        }
      },
      "14": {
        start: {
          line: 68,
          column: 2
        },
        end: {
          line: 73,
          column: 3
        }
      },
      "15": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 72,
          column: 6
        }
      },
      "16": {
        start: {
          line: 75,
          column: 2
        },
        end: {
          line: 75,
          column: 20
        }
      },
      "17": {
        start: {
          line: 82,
          column: 2
        },
        end: {
          line: 82,
          column: 47
        }
      },
      "18": {
        start: {
          line: 89,
          column: 2
        },
        end: {
          line: 89,
          column: 62
        }
      },
      "19": {
        start: {
          line: 89,
          column: 36
        },
        end: {
          line: 89,
          column: 60
        }
      },
      "20": {
        start: {
          line: 96,
          column: 2
        },
        end: {
          line: 96,
          column: 63
        }
      },
      "21": {
        start: {
          line: 96,
          column: 37
        },
        end: {
          line: 96,
          column: 61
        }
      },
      "22": {
        start: {
          line: 103,
          column: 2
        },
        end: {
          line: 103,
          column: 24
        }
      },
      "23": {
        start: {
          line: 110,
          column: 2
        },
        end: {
          line: 110,
          column: 23
        }
      },
      "24": {
        start: {
          line: 117,
          column: 2
        },
        end: {
          line: 117,
          column: 44
        }
      },
      "25": {
        start: {
          line: 126,
          column: 2
        },
        end: {
          line: 134,
          column: 4
        }
      },
      "26": {
        start: {
          line: 127,
          column: 23
        },
        end: {
          line: 127,
          column: 42
        }
      },
      "27": {
        start: {
          line: 129,
          column: 4
        },
        end: {
          line: 131,
          column: 5
        }
      },
      "28": {
        start: {
          line: 130,
          column: 6
        },
        end: {
          line: 130,
          column: 34
        }
      },
      "29": {
        start: {
          line: 133,
          column: 4
        },
        end: {
          line: 133,
          column: 49
        }
      },
      "30": {
        start: {
          line: 144,
          column: 2
        },
        end: {
          line: 152,
          column: 4
        }
      },
      "31": {
        start: {
          line: 145,
          column: 23
        },
        end: {
          line: 145,
          column: 55
        }
      },
      "32": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 149,
          column: 5
        }
      },
      "33": {
        start: {
          line: 148,
          column: 6
        },
        end: {
          line: 148,
          column: 34
        }
      },
      "34": {
        start: {
          line: 151,
          column: 4
        },
        end: {
          line: 151,
          column: 49
        }
      },
      "35": {
        start: {
          line: 161,
          column: 2
        },
        end: {
          line: 161,
          column: 53
        }
      },
      "36": {
        start: {
          line: 167,
          column: 21
        },
        end: {
          line: 167,
          column: 76
        }
      },
      "37": {
        start: {
          line: 174,
          column: 14
        },
        end: {
          line: 174,
          column: 24
        }
      },
      "38": {
        start: {
          line: 175,
          column: 22
        },
        end: {
          line: 175,
          column: 36
        }
      },
      "39": {
        start: {
          line: 178,
          column: 2
        },
        end: {
          line: 182,
          column: 3
        }
      },
      "40": {
        start: {
          line: 179,
          column: 4
        },
        end: {
          line: 181,
          column: 5
        }
      },
      "41": {
        start: {
          line: 180,
          column: 6
        },
        end: {
          line: 180,
          column: 31
        }
      },
      "42": {
        start: {
          line: 184,
          column: 18
        },
        end: {
          line: 184,
          column: 46
        }
      },
      "43": {
        start: {
          line: 186,
          column: 2
        },
        end: {
          line: 190,
          column: 3
        }
      },
      "44": {
        start: {
          line: 188,
          column: 4
        },
        end: {
          line: 188,
          column: 74
        }
      },
      "45": {
        start: {
          line: 189,
          column: 4
        },
        end: {
          line: 189,
          column: 57
        }
      },
      "46": {
        start: {
          line: 192,
          column: 2
        },
        end: {
          line: 194,
          column: 3
        }
      },
      "47": {
        start: {
          line: 193,
          column: 4
        },
        end: {
          line: 193,
          column: 44
        }
      },
      "48": {
        start: {
          line: 196,
          column: 2
        },
        end: {
          line: 196,
          column: 18
        }
      },
      "49": {
        start: {
          line: 197,
          column: 2
        },
        end: {
          line: 197,
          column: 67
        }
      },
      "50": {
        start: {
          line: 205,
          column: 2
        },
        end: {
          line: 207,
          column: 3
        }
      },
      "51": {
        start: {
          line: 206,
          column: 4
        },
        end: {
          line: 206,
          column: 36
        }
      },
      "52": {
        start: {
          line: 210,
          column: 20
        },
        end: {
          line: 210,
          column: 58
        }
      },
      "53": {
        start: {
          line: 211,
          column: 17
        },
        end: {
          line: 211,
          column: 49
        }
      },
      "54": {
        start: {
          line: 212,
          column: 13
        },
        end: {
          line: 212,
          column: 60
        }
      },
      "55": {
        start: {
          line: 214,
          column: 2
        },
        end: {
          line: 214,
          column: 20
        }
      }
    },
    fnMap: {
      "0": {
        name: "requireAuth",
        decl: {
          start: {
            line: 29,
            column: 22
          },
          end: {
            line: 29,
            column: 33
          }
        },
        loc: {
          start: {
            line: 29,
            column: 57
          },
          end: {
            line: 51,
            column: 1
          }
        },
        line: 29
      },
      "1": {
        name: "requireRole",
        decl: {
          start: {
            line: 56,
            column: 22
          },
          end: {
            line: 56,
            column: 33
          }
        },
        loc: {
          start: {
            line: 56,
            column: 89
          },
          end: {
            line: 76,
            column: 1
          }
        },
        line: 56
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 66,
            column: 37
          },
          end: {
            line: 66,
            column: 38
          }
        },
        loc: {
          start: {
            line: 66,
            column: 45
          },
          end: {
            line: 66,
            column: 69
          }
        },
        line: 66
      },
      "3": {
        name: "requireAdmin",
        decl: {
          start: {
            line: 81,
            column: 22
          },
          end: {
            line: 81,
            column: 34
          }
        },
        loc: {
          start: {
            line: 81,
            column: 58
          },
          end: {
            line: 83,
            column: 1
          }
        },
        line: 81
      },
      "4": {
        name: "hasAnyRole",
        decl: {
          start: {
            line: 88,
            column: 16
          },
          end: {
            line: 88,
            column: 26
          }
        },
        loc: {
          start: {
            line: 88,
            column: 82
          },
          end: {
            line: 90,
            column: 1
          }
        },
        line: 88
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 89,
            column: 28
          },
          end: {
            line: 89,
            column: 29
          }
        },
        loc: {
          start: {
            line: 89,
            column: 36
          },
          end: {
            line: 89,
            column: 60
          }
        },
        line: 89
      },
      "6": {
        name: "hasAllRoles",
        decl: {
          start: {
            line: 95,
            column: 16
          },
          end: {
            line: 95,
            column: 27
          }
        },
        loc: {
          start: {
            line: 95,
            column: 83
          },
          end: {
            line: 97,
            column: 1
          }
        },
        line: 95
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 96,
            column: 29
          },
          end: {
            line: 96,
            column: 30
          }
        },
        loc: {
          start: {
            line: 96,
            column: 37
          },
          end: {
            line: 96,
            column: 61
          }
        },
        line: 96
      },
      "8": {
        name: "getUserId",
        decl: {
          start: {
            line: 102,
            column: 16
          },
          end: {
            line: 102,
            column: 25
          }
        },
        loc: {
          start: {
            line: 102,
            column: 56
          },
          end: {
            line: 104,
            column: 1
          }
        },
        line: 102
      },
      "9": {
        name: "getUserEmail",
        decl: {
          start: {
            line: 109,
            column: 16
          },
          end: {
            line: 109,
            column: 28
          }
        },
        loc: {
          start: {
            line: 109,
            column: 59
          },
          end: {
            line: 111,
            column: 1
          }
        },
        line: 109
      },
      "10": {
        name: "isAuthenticated",
        decl: {
          start: {
            line: 116,
            column: 16
          },
          end: {
            line: 116,
            column: 31
          }
        },
        loc: {
          start: {
            line: 116,
            column: 85
          },
          end: {
            line: 118,
            column: 1
          }
        },
        line: 116
      },
      "11": {
        name: "withAuth",
        decl: {
          start: {
            line: 123,
            column: 16
          },
          end: {
            line: 123,
            column: 24
          }
        },
        loc: {
          start: {
            line: 125,
            column: 2
          },
          end: {
            line: 135,
            column: 1
          }
        },
        line: 125
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 126,
            column: 9
          },
          end: {
            line: 126,
            column: 10
          }
        },
        loc: {
          start: {
            line: 126,
            column: 71
          },
          end: {
            line: 134,
            column: 3
          }
        },
        line: 126
      },
      "13": {
        name: "withRole",
        decl: {
          start: {
            line: 140,
            column: 16
          },
          end: {
            line: 140,
            column: 24
          }
        },
        loc: {
          start: {
            line: 143,
            column: 2
          },
          end: {
            line: 153,
            column: 1
          }
        },
        line: 143
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 144,
            column: 9
          },
          end: {
            line: 144,
            column: 10
          }
        },
        loc: {
          start: {
            line: 144,
            column: 71
          },
          end: {
            line: 152,
            column: 3
          }
        },
        line: 144
      },
      "15": {
        name: "withAdmin",
        decl: {
          start: {
            line: 158,
            column: 16
          },
          end: {
            line: 158,
            column: 25
          }
        },
        loc: {
          start: {
            line: 160,
            column: 2
          },
          end: {
            line: 162,
            column: 1
          }
        },
        line: 160
      },
      "16": {
        name: "checkRateLimit",
        decl: {
          start: {
            line: 169,
            column: 16
          },
          end: {
            line: 169,
            column: 30
          }
        },
        loc: {
          start: {
            line: 173,
            column: 43
          },
          end: {
            line: 198,
            column: 1
          }
        },
        line: 173
      },
      "17": {
        name: "getRateLimitIdentifier",
        decl: {
          start: {
            line: 203,
            column: 16
          },
          end: {
            line: 203,
            column: 38
          }
        },
        loc: {
          start: {
            line: 203,
            column: 92
          },
          end: {
            line: 215,
            column: 1
          }
        },
        line: 203
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 33,
            column: 4
          },
          end: {
            line: 38,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 4
          },
          end: {
            line: 38,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "1": {
        loc: {
          start: {
            line: 59,
            column: 2
          },
          end: {
            line: 61,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 59,
            column: 2
          },
          end: {
            line: 61,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 59
      },
      "2": {
        loc: {
          start: {
            line: 63,
            column: 16
          },
          end: {
            line: 63,
            column: 78
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 63,
            column: 47
          },
          end: {
            line: 63,
            column: 60
          }
        }, {
          start: {
            line: 63,
            column: 63
          },
          end: {
            line: 63,
            column: 78
          }
        }],
        line: 63
      },
      "3": {
        loc: {
          start: {
            line: 68,
            column: 2
          },
          end: {
            line: 73,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 68,
            column: 2
          },
          end: {
            line: 73,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 68
      },
      "4": {
        loc: {
          start: {
            line: 117,
            column: 9
          },
          end: {
            line: 117,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 117,
            column: 9
          },
          end: {
            line: 117,
            column: 25
          }
        }, {
          start: {
            line: 117,
            column: 29
          },
          end: {
            line: 117,
            column: 43
          }
        }],
        line: 117
      },
      "5": {
        loc: {
          start: {
            line: 129,
            column: 4
          },
          end: {
            line: 131,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 129,
            column: 4
          },
          end: {
            line: 131,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 129
      },
      "6": {
        loc: {
          start: {
            line: 147,
            column: 4
          },
          end: {
            line: 149,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 147,
            column: 4
          },
          end: {
            line: 149,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 147
      },
      "7": {
        loc: {
          start: {
            line: 171,
            column: 2
          },
          end: {
            line: 171,
            column: 27
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 171,
            column: 24
          },
          end: {
            line: 171,
            column: 27
          }
        }],
        line: 171
      },
      "8": {
        loc: {
          start: {
            line: 172,
            column: 2
          },
          end: {
            line: 172,
            column: 26
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 172,
            column: 21
          },
          end: {
            line: 172,
            column: 26
          }
        }],
        line: 172
      },
      "9": {
        loc: {
          start: {
            line: 179,
            column: 4
          },
          end: {
            line: 181,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 179,
            column: 4
          },
          end: {
            line: 181,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 179
      },
      "10": {
        loc: {
          start: {
            line: 186,
            column: 2
          },
          end: {
            line: 190,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 186,
            column: 2
          },
          end: {
            line: 190,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 186
      },
      "11": {
        loc: {
          start: {
            line: 186,
            column: 6
          },
          end: {
            line: 186,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 186,
            column: 6
          },
          end: {
            line: 186,
            column: 14
          }
        }, {
          start: {
            line: 186,
            column: 18
          },
          end: {
            line: 186,
            column: 49
          }
        }],
        line: 186
      },
      "12": {
        loc: {
          start: {
            line: 192,
            column: 2
          },
          end: {
            line: 194,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 192,
            column: 2
          },
          end: {
            line: 194,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 192
      },
      "13": {
        loc: {
          start: {
            line: 205,
            column: 2
          },
          end: {
            line: 207,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 205,
            column: 2
          },
          end: {
            line: 207,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 205
      },
      "14": {
        loc: {
          start: {
            line: 212,
            column: 13
          },
          end: {
            line: 212,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 212,
            column: 13
          },
          end: {
            line: 212,
            column: 37
          }
        }, {
          start: {
            line: 212,
            column: 41
          },
          end: {
            line: 212,
            column: 47
          }
        }, {
          start: {
            line: 212,
            column: 51
          },
          end: {
            line: 212,
            column: 60
          }
        }],
        line: 212
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0],
      "8": [0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "bc58f8907d68b71213dccd59773aeda8f20cf150"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_jg567abuj = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_jg567abuj();
/**
 * Authentication Middleware
 * 
 * This module provides reusable authentication middleware for API routes.
 */

import { verifySession } from './dal';
import { createUnauthorizedResponse, createForbiddenResponse } from './api-response';

// Session interface

// Authentication result

/**
 * Verify authentication for API routes
 */
export async function requireAuth() {
  /* istanbul ignore next */
  cov_jg567abuj().f[0]++;
  cov_jg567abuj().s[0]++;
  try {
    const session =
    /* istanbul ignore next */
    (cov_jg567abuj().s[1]++, await verifySession());
    /* istanbul ignore next */
    cov_jg567abuj().s[2]++;
    if (!session) {
      /* istanbul ignore next */
      cov_jg567abuj().b[0][0]++;
      cov_jg567abuj().s[3]++;
      return {
        success: false,
        response: createUnauthorizedResponse('Authentication required')
      };
    } else
    /* istanbul ignore next */
    {
      cov_jg567abuj().b[0][1]++;
    }
    cov_jg567abuj().s[4]++;
    return {
      success: true,
      session
    };
  } catch (error) {
    /* istanbul ignore next */
    cov_jg567abuj().s[5]++;
    console.error('Authentication verification failed:', error);
    /* istanbul ignore next */
    cov_jg567abuj().s[6]++;
    return {
      success: false,
      response: createUnauthorizedResponse('Authentication verification failed')
    };
  }
}

/**
 * Verify authentication and check for specific roles
 */
export async function requireRole(requiredRoles) {
  /* istanbul ignore next */
  cov_jg567abuj().f[1]++;
  const authResult =
  /* istanbul ignore next */
  (cov_jg567abuj().s[7]++, await requireAuth());
  /* istanbul ignore next */
  cov_jg567abuj().s[8]++;
  if (!authResult.success) {
    /* istanbul ignore next */
    cov_jg567abuj().b[1][0]++;
    cov_jg567abuj().s[9]++;
    return authResult;
  } else
  /* istanbul ignore next */
  {
    cov_jg567abuj().b[1][1]++;
  }
  const roles =
  /* istanbul ignore next */
  (cov_jg567abuj().s[10]++, Array.isArray(requiredRoles) ?
  /* istanbul ignore next */
  (cov_jg567abuj().b[2][0]++, requiredRoles) :
  /* istanbul ignore next */
  (cov_jg567abuj().b[2][1]++, [requiredRoles]));
  const userRoles =
  /* istanbul ignore next */
  (cov_jg567abuj().s[11]++, authResult.session.roles);
  const hasRequiredRole =
  /* istanbul ignore next */
  (cov_jg567abuj().s[12]++, roles.some(role => {
    /* istanbul ignore next */
    cov_jg567abuj().f[2]++;
    cov_jg567abuj().s[13]++;
    return userRoles.includes(role);
  }));
  /* istanbul ignore next */
  cov_jg567abuj().s[14]++;
  if (!hasRequiredRole) {
    /* istanbul ignore next */
    cov_jg567abuj().b[3][0]++;
    cov_jg567abuj().s[15]++;
    return {
      success: false,
      response: createForbiddenResponse(`Required role(s): ${roles.join(', ')}`)
    };
  } else
  /* istanbul ignore next */
  {
    cov_jg567abuj().b[3][1]++;
  }
  cov_jg567abuj().s[16]++;
  return authResult;
}

/**
 * Verify authentication for admin operations
 */
export async function requireAdmin() {
  /* istanbul ignore next */
  cov_jg567abuj().f[3]++;
  cov_jg567abuj().s[17]++;
  return requireRole(['admin', 'super_admin']);
}

/**
 * Check if user has any of the specified roles
 */
export function hasAnyRole(userRoles, requiredRoles) {
  /* istanbul ignore next */
  cov_jg567abuj().f[4]++;
  cov_jg567abuj().s[18]++;
  return requiredRoles.some(role => {
    /* istanbul ignore next */
    cov_jg567abuj().f[5]++;
    cov_jg567abuj().s[19]++;
    return userRoles.includes(role);
  });
}

/**
 * Check if user has all of the specified roles
 */
export function hasAllRoles(userRoles, requiredRoles) {
  /* istanbul ignore next */
  cov_jg567abuj().f[6]++;
  cov_jg567abuj().s[20]++;
  return requiredRoles.every(role => {
    /* istanbul ignore next */
    cov_jg567abuj().f[7]++;
    cov_jg567abuj().s[21]++;
    return userRoles.includes(role);
  });
}

/**
 * Extract user ID from session
 */
export function getUserId(session) {
  /* istanbul ignore next */
  cov_jg567abuj().f[8]++;
  cov_jg567abuj().s[22]++;
  return session.userId;
}

/**
 * Extract user email from session
 */
export function getUserEmail(session) {
  /* istanbul ignore next */
  cov_jg567abuj().f[9]++;
  cov_jg567abuj().s[23]++;
  return session.email;
}

/**
 * Check if user is authenticated
 */
export function isAuthenticated(session) {
  /* istanbul ignore next */
  cov_jg567abuj().f[10]++;
  cov_jg567abuj().s[24]++;
  return /* istanbul ignore next */(cov_jg567abuj().b[4][0]++, session !== null) &&
  /* istanbul ignore next */
  (cov_jg567abuj().b[4][1]++, session.isAuth);
}

/**
 * Higher-order function to wrap API handlers with authentication
 */
export function withAuth(handler) {
  /* istanbul ignore next */
  cov_jg567abuj().f[11]++;
  cov_jg567abuj().s[25]++;
  return async (...args) => {
    /* istanbul ignore next */
    cov_jg567abuj().f[12]++;
    const authResult =
    /* istanbul ignore next */
    (cov_jg567abuj().s[26]++, await requireAuth());
    /* istanbul ignore next */
    cov_jg567abuj().s[27]++;
    if (!authResult.success) {
      /* istanbul ignore next */
      cov_jg567abuj().b[5][0]++;
      cov_jg567abuj().s[28]++;
      return authResult.response;
    } else
    /* istanbul ignore next */
    {
      cov_jg567abuj().b[5][1]++;
    }
    cov_jg567abuj().s[29]++;
    return handler(authResult.session, ...args);
  };
}

/**
 * Higher-order function to wrap API handlers with role-based authentication
 */
export function withRole(requiredRoles, handler) {
  /* istanbul ignore next */
  cov_jg567abuj().f[13]++;
  cov_jg567abuj().s[30]++;
  return async (...args) => {
    /* istanbul ignore next */
    cov_jg567abuj().f[14]++;
    const authResult =
    /* istanbul ignore next */
    (cov_jg567abuj().s[31]++, await requireRole(requiredRoles));
    /* istanbul ignore next */
    cov_jg567abuj().s[32]++;
    if (!authResult.success) {
      /* istanbul ignore next */
      cov_jg567abuj().b[6][0]++;
      cov_jg567abuj().s[33]++;
      return authResult.response;
    } else
    /* istanbul ignore next */
    {
      cov_jg567abuj().b[6][1]++;
    }
    cov_jg567abuj().s[34]++;
    return handler(authResult.session, ...args);
  };
}

/**
 * Higher-order function to wrap API handlers with admin authentication
 */
export function withAdmin(handler) {
  /* istanbul ignore next */
  cov_jg567abuj().f[15]++;
  cov_jg567abuj().s[35]++;
  return withRole(['admin', 'super_admin'], handler);
}

/**
 * Rate limiting helper (basic implementation)
 */
const rateLimitMap =
/* istanbul ignore next */
(cov_jg567abuj().s[36]++, new Map());
export function checkRateLimit(identifier, maxRequests =
/* istanbul ignore next */
(cov_jg567abuj().b[7][0]++, 100), windowMs =
/* istanbul ignore next */
(cov_jg567abuj().b[8][0]++, 60000)) {
  /* istanbul ignore next */
  cov_jg567abuj().f[16]++;
  const now =
  /* istanbul ignore next */
  (cov_jg567abuj().s[37]++, Date.now());
  const windowStart =
  /* istanbul ignore next */
  (cov_jg567abuj().s[38]++, now - windowMs);

  // Clean up old entries
  /* istanbul ignore next */
  cov_jg567abuj().s[39]++;
  for (const [key, value] of rateLimitMap.entries()) {
    /* istanbul ignore next */
    cov_jg567abuj().s[40]++;
    if (value.resetTime < windowStart) {
      /* istanbul ignore next */
      cov_jg567abuj().b[9][0]++;
      cov_jg567abuj().s[41]++;
      rateLimitMap.delete(key);
    } else
    /* istanbul ignore next */
    {
      cov_jg567abuj().b[9][1]++;
    }
  }
  const current =
  /* istanbul ignore next */
  (cov_jg567abuj().s[42]++, rateLimitMap.get(identifier));
  /* istanbul ignore next */
  cov_jg567abuj().s[43]++;
  if (
  /* istanbul ignore next */
  (cov_jg567abuj().b[11][0]++, !current) ||
  /* istanbul ignore next */
  (cov_jg567abuj().b[11][1]++, current.resetTime < windowStart)) {
    /* istanbul ignore next */
    cov_jg567abuj().b[10][0]++;
    cov_jg567abuj().s[44]++;
    // First request in window or window expired
    rateLimitMap.set(identifier, {
      count: 1,
      resetTime: now + windowMs
    });
    /* istanbul ignore next */
    cov_jg567abuj().s[45]++;
    return {
      allowed: true,
      remaining: maxRequests - 1
    };
  } else
  /* istanbul ignore next */
  {
    cov_jg567abuj().b[10][1]++;
  }
  cov_jg567abuj().s[46]++;
  if (current.count >= maxRequests) {
    /* istanbul ignore next */
    cov_jg567abuj().b[12][0]++;
    cov_jg567abuj().s[47]++;
    return {
      allowed: false,
      remaining: 0
    };
  } else
  /* istanbul ignore next */
  {
    cov_jg567abuj().b[12][1]++;
  }
  cov_jg567abuj().s[48]++;
  current.count++;
  /* istanbul ignore next */
  cov_jg567abuj().s[49]++;
  return {
    allowed: true,
    remaining: maxRequests - current.count
  };
}

/**
 * Get rate limit identifier from request
 */
export function getRateLimitIdentifier(request, session) {
  /* istanbul ignore next */
  cov_jg567abuj().f[17]++;
  cov_jg567abuj().s[50]++;
  // Use user ID if authenticated, otherwise use IP
  if (session) {
    /* istanbul ignore next */
    cov_jg567abuj().b[13][0]++;
    cov_jg567abuj().s[51]++;
    return `user:${session.userId}`;
  } else
  /* istanbul ignore next */
  {
    cov_jg567abuj().b[13][1]++;
  }

  // Get IP from various headers
  const forwarded =
  /* istanbul ignore next */
  (cov_jg567abuj().s[52]++, request.headers.get('x-forwarded-for'));
  const realIp =
  /* istanbul ignore next */
  (cov_jg567abuj().s[53]++, request.headers.get('x-real-ip'));
  const ip =
  /* istanbul ignore next */
  (cov_jg567abuj().s[54]++,
  /* istanbul ignore next */
  (cov_jg567abuj().b[14][0]++, forwarded?.split(',')[0]) ||
  /* istanbul ignore next */
  (cov_jg567abuj().b[14][1]++, realIp) ||
  /* istanbul ignore next */
  (cov_jg567abuj().b[14][2]++, 'unknown'));
  /* istanbul ignore next */
  cov_jg567abuj().s[55]++;
  return `ip:${ip}`;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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