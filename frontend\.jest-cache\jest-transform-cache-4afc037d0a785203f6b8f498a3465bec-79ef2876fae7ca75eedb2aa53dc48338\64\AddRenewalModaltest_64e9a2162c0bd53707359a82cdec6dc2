fc0ecdfcb9ae9c86a40bfc4aab638303
"use strict";

var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\__tests__\\components\\modals\\AddRenewalModal.test.tsx";
// Mock the step components
_getJestObj().mock('@/components/modals/steps/RenewalDetailsStep', () => {
  return function MockRenewalDetailsStep({
    data,
    onChange,
    onNext,
    onCancel
  }) {
    return __jsx("div", {
      "data-testid": "renewal-details-step",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 14,
        columnNumber: 7
      }
    }, __jsx("input", {
      "data-testid": "product-name",
      value: data.productName,
      onChange: e => onChange(_objectSpread(_objectSpread({}, data), {}, {
        productName: e.target.value
      })),
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 15,
        columnNumber: 9
      }
    }), __jsx("button", {
      "data-testid": "next-button",
      onClick: onNext,
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 20,
        columnNumber: 9
      }
    }, "Next"), __jsx("button", {
      "data-testid": "cancel-button",
      onClick: onCancel,
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 23,
        columnNumber: 9
      }
    }, "Cancel"));
  };
});
_getJestObj().mock('@/components/modals/steps/SetupAlertsStep', () => {
  return function MockSetupAlertsStep({
    data,
    onChange,
    onBack,
    onSubmit,
    isSubmitting
  }) {
    return __jsx("div", {
      "data-testid": "setup-alerts-step",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 34,
        columnNumber: 7
      }
    }, __jsx("input", {
      "data-testid": "days-before",
      type: "number",
      value: data[0]?.daysBeforeRenewal || 30,
      onChange: e => {
        const newData = [...data];
        newData[0] = _objectSpread(_objectSpread({}, newData[0]), {}, {
          daysBeforeRenewal: parseInt(e.target.value)
        });
        onChange(newData);
      },
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 35,
        columnNumber: 9
      }
    }), __jsx("button", {
      "data-testid": "back-button",
      onClick: onBack,
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 45,
        columnNumber: 9
      }
    }, "Back"), __jsx("button", {
      "data-testid": "submit-button",
      onClick: onSubmit,
      disabled: isSubmitting,
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 48,
        columnNumber: 9
      }
    }, isSubmitting ? 'Saving...' : 'Save & Finish'));
  };
});
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _react = _interopRequireDefault(require("react"));
var _react2 = require("@testing-library/react");
var _userEvent = _interopRequireDefault(require("@testing-library/user-event"));
var _AddRenewalModal = _interopRequireDefault(require("@/components/modals/AddRenewalModal"));
/**
 * AddRenewalModal Component Tests
 */
var __jsx = _react.default.createElement;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
describe('AddRenewalModal', () => {
  const mockOnClose = jest.fn();
  const mockOnSubmit = jest.fn();
  const defaultProps = {
    isOpen: true,
    onClose: mockOnClose,
    onSubmit: mockOnSubmit
  };
  beforeEach(() => {
    jest.clearAllMocks();
  });
  it('renders the modal when open', () => {
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 71,
        columnNumber: 12
      }
    })));
    expect(_react2.screen.getByText('Add New Renewal')).toBeInTheDocument();
    expect(_react2.screen.getByText('Enter the details of the renewal you want to track.')).toBeInTheDocument();
  });
  it('does not render when closed', () => {
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      isOpen: false,
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 78,
        columnNumber: 12
      }
    })));
    expect(_react2.screen.queryByText('Add New Renewal')).not.toBeInTheDocument();
  });
  it('shows step 1 initially', () => {
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 84,
        columnNumber: 12
      }
    })));
    expect(_react2.screen.getByTestId('renewal-details-step')).toBeInTheDocument();
    expect(_react2.screen.queryByTestId('setup-alerts-step')).not.toBeInTheDocument();
  });
  it('navigates to step 2 when next is clicked', async () => {
    const user = _userEvent.default.setup();
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 92,
        columnNumber: 12
      }
    })));
    const nextButton = _react2.screen.getByTestId('next-button');
    await user.click(nextButton);
    expect(_react2.screen.getByTestId('setup-alerts-step')).toBeInTheDocument();
    expect(_react2.screen.queryByTestId('renewal-details-step')).not.toBeInTheDocument();
    expect(_react2.screen.getByText('Edit Renewal')).toBeInTheDocument();
  });
  it('navigates back to step 1 from step 2', async () => {
    const user = _userEvent.default.setup();
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 104,
        columnNumber: 12
      }
    })));

    // Go to step 2
    await user.click(_react2.screen.getByTestId('next-button'));

    // Go back to step 1
    await user.click(_react2.screen.getByTestId('back-button'));
    expect(_react2.screen.getByTestId('renewal-details-step')).toBeInTheDocument();
    expect(_react2.screen.queryByTestId('setup-alerts-step')).not.toBeInTheDocument();
    expect(_react2.screen.getByText('Add New Renewal')).toBeInTheDocument();
  });
  it('calls onClose when cancel is clicked', async () => {
    const user = _userEvent.default.setup();
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 119,
        columnNumber: 12
      }
    })));
    await user.click(_react2.screen.getByTestId('cancel-button'));
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });
  it('calls onClose when close button is clicked', async () => {
    const user = _userEvent.default.setup();
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 128,
        columnNumber: 12
      }
    })));
    const closeButton = _react2.screen.getByLabelText('Close modal');
    await user.click(closeButton);
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });
  it('calls onSubmit with correct data when form is submitted', async () => {
    const user = _userEvent.default.setup();
    mockOnSubmit.mockResolvedValue(undefined);
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 140,
        columnNumber: 12
      }
    })));

    // Fill in some data in step 1
    const productNameInput = _react2.screen.getByTestId('product-name');
    await user.type(productNameInput, 'Test Product');

    // Go to step 2
    await user.click(_react2.screen.getByTestId('next-button'));

    // Modify alert data
    const daysBeforeInput = _react2.screen.getByTestId('days-before');
    await user.clear(daysBeforeInput);
    await user.type(daysBeforeInput, '60');

    // Submit
    await user.click(_react2.screen.getByTestId('submit-button'));
    await (0, _react2.waitFor)(() => {
      expect(mockOnSubmit).toHaveBeenCalledTimes(1);
    });
    const [renewalData, alertsData] = mockOnSubmit.mock.calls[0];
    expect(renewalData.productName).toBe('Test Product');
    expect(alertsData[0].daysBeforeRenewal).toBe(60);
  });
  it('shows loading state during submission', async () => {
    const user = _userEvent.default.setup();
    mockOnSubmit.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)));
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 170,
        columnNumber: 12
      }
    })));

    // Go to step 2
    await user.click(_react2.screen.getByTestId('next-button'));

    // Submit
    await user.click(_react2.screen.getByTestId('submit-button'));
    expect(_react2.screen.getByText('Saving...')).toBeInTheDocument();
    expect(_react2.screen.getByTestId('submit-button')).toBeDisabled();
  });
  it('resets form data after successful submission', async () => {
    const user = _userEvent.default.setup();
    mockOnSubmit.mockResolvedValue(undefined);
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 186,
        columnNumber: 12
      }
    })));

    // Fill in data
    const productNameInput = _react2.screen.getByTestId('product-name');
    await user.type(productNameInput, 'Test Product');

    // Go to step 2 and submit
    await user.click(_react2.screen.getByTestId('next-button'));
    await user.click(_react2.screen.getByTestId('submit-button'));
    await (0, _react2.waitFor)(() => {
      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });
  });
  it('handles submission errors gracefully', async () => {
    const user = _userEvent.default.setup();
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    mockOnSubmit.mockRejectedValue(new Error('Submission failed'));
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 206,
        columnNumber: 12
      }
    })));

    // Go to step 2 and submit
    await user.click(_react2.screen.getByTestId('next-button'));
    await user.click(_react2.screen.getByTestId('submit-button'));
    await (0, _react2.waitFor)(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error submitting renewal:', expect.any(Error));
    });

    // Modal should still be open
    expect(_react2.screen.getByTestId('setup-alerts-step')).toBeInTheDocument();
    consoleErrorSpy.mockRestore();
  });
  it('shows correct step indicators', async () => {
    const user = _userEvent.default.setup();
    (0, _react2.render)(__jsx(_AddRenewalModal.default, (0, _extends2.default)({}, defaultProps, {
      __self: void 0,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 224,
        columnNumber: 12
      }
    })));

    // Step 1 should be active
    const step1 = _react2.screen.getByText('1');
    const step2 = _react2.screen.getByText('2');
    expect(step1.closest('.step')).toHaveClass('active');
    expect(step2.closest('.step')).not.toHaveClass('active');

    // Go to step 2
    await user.click(_react2.screen.getByTestId('next-button'));
    expect(step1.closest('.step')).toHaveClass('completed');
    expect(step2.closest('.step')).toHaveClass('active');
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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