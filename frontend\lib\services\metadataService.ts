/**
 * Metadata Service
 * 
 * Handles API calls for metadata tables (PurchaseTypes, RenewalTypes, Currencies)
 * Provides dropdown options for form fields
 */

export interface PurchaseType {
  PurchaseTypeID: number
  PurchaseTypeName: string
  Active: boolean
  DisplayOrder: number
}

export interface RenewalType {
  RenewalTypeID: number
  RenewalTypeName: string
  Active: boolean
  DisplayOrder: number
}

export interface Currency {
  CurrencyID: string
  CurrencyName: string
  CurrencySymbol: string
  Active: boolean
  DisplayOrder: number
}

export interface MetadataOptions {
  purchaseTypes: PurchaseType[]
  renewalTypes: RenewalType[]
  currencies: Currency[]
}

/**
 * Fetch all metadata options for form dropdowns
 *
 * @returns Promise with all metadata options
 */
export async function getMetadataOptions(): Promise<MetadataOptions> {
  try {
    console.log('Fetching metadata options from API...')

    // Use the combined metadata endpoint for better performance
    const response = await fetch('/api/metadata', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies for authentication
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))

      // If authentication fails, redirect to login
      if (response.status === 401) {
        console.log('Authentication failed, redirecting to login...')
        window.location.href = '/login'
        throw new Error('Authentication required')
      }

      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (!data.success) {
      throw new Error(data.error || 'API returned unsuccessful response')
    }

    // Validate response structure
    if (!data.data || typeof data.data !== 'object') {
      throw new Error('Invalid response format: missing data object')
    }

    const { purchaseTypes, renewalTypes, currencies } = data.data

    // Validate each array
    if (!Array.isArray(purchaseTypes)) {
      throw new Error('Invalid response format: purchaseTypes is not an array')
    }
    if (!Array.isArray(renewalTypes)) {
      throw new Error('Invalid response format: renewalTypes is not an array')
    }
    if (!Array.isArray(currencies)) {
      throw new Error('Invalid response format: currencies is not an array')
    }

    console.log('Metadata options fetched successfully:', {
      purchaseTypes: purchaseTypes.length,
      renewalTypes: renewalTypes.length,
      currencies: currencies.length
    })

    return {
      purchaseTypes,
      renewalTypes,
      currencies
    }

  } catch (error) {
    console.error('Error fetching metadata options:', error)
    throw new Error(error instanceof Error ? error.message : 'Failed to fetch metadata options')
  }
}

/**
 * Fetch purchase types from metadata.PurchaseTypes
 *
 * @returns Promise with active purchase types
 */
export async function getPurchaseTypes(): Promise<PurchaseType[]> {
  try {
    console.log('Fetching purchase types from API...')

    const response = await fetch('/api/metadata/purchase-types', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies for authentication
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (!data.success) {
      throw new Error(data.error || 'API returned unsuccessful response')
    }

    if (!Array.isArray(data.data)) {
      throw new Error('Invalid response format: data is not an array')
    }

    console.log(`Purchase types fetched successfully: ${data.data.length} records`)
    return data.data

  } catch (error) {
    console.error('Error fetching purchase types:', error)
    throw new Error(error instanceof Error ? error.message : 'Failed to fetch purchase types')
  }
}

/**
 * Fetch renewal types from metadata.RenewalTypes
 *
 * @returns Promise with active renewal types
 */
export async function getRenewalTypes(): Promise<RenewalType[]> {
  try {
    console.log('Fetching renewal types from API...')

    const response = await fetch('/api/metadata/renewal-types', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies for authentication
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (!data.success) {
      throw new Error(data.error || 'API returned unsuccessful response')
    }

    if (!Array.isArray(data.data)) {
      throw new Error('Invalid response format: data is not an array')
    }

    console.log(`Renewal types fetched successfully: ${data.data.length} records`)
    return data.data

  } catch (error) {
    console.error('Error fetching renewal types:', error)
    throw new Error(error instanceof Error ? error.message : 'Failed to fetch renewal types')
  }
}

/**
 * Fetch currencies from metadata.Currencies
 *
 * @returns Promise with active currencies
 */
export async function getCurrencies(): Promise<Currency[]> {
  try {
    console.log('Fetching currencies from API...')

    const response = await fetch('/api/metadata/currencies', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies for authentication
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (!data.success) {
      throw new Error(data.error || 'API returned unsuccessful response')
    }

    if (!Array.isArray(data.data)) {
      throw new Error('Invalid response format: data is not an array')
    }

    console.log(`Currencies fetched successfully: ${data.data.length} records`)
    return data.data

  } catch (error) {
    console.error('Error fetching currencies:', error)
    throw new Error(error instanceof Error ? error.message : 'Failed to fetch currencies')
  }
}

/**
 * Format currency display text as "CurrencyID (CurrencyName)"
 *
 * @param currency - Currency object
 * @returns Formatted display text
 */
export function formatCurrencyDisplay(currency: Currency): string {
  return `${currency.CurrencyID} (${currency.CurrencyName})`
}

/**
 * Error handling utility for API calls
 *
 * @param error - Error object or unknown error
 * @param context - Context string for logging
 * @returns Formatted error message
 */
function handleApiError(error: unknown, context: string): string {
  console.error(`${context}:`, error)

  if (error instanceof Error) {
    return error.message
  }

  if (typeof error === 'string') {
    return error
  }

  return `Failed to ${context.toLowerCase()}`
}

/**
 * Database Schema Information
 * 
 * The following metadata tables should exist:
 * 
 * metadata.PurchaseTypes:
 * - PurchaseTypeID (integer, Primary Key)
 * - PurchaseTypeName (text)
 * - Active (boolean)
 * 
 * metadata.RenewalTypes:
 * - RenewalTypeID (integer, Primary Key)
 * - RenewalTypeName (text)
 * - Active (boolean)
 * 
 * metadata.Currencies:
 * - CurrencyID (character(3), Primary Key)
 * - CurrencyName (text)
 * - Active (boolean)
 */
