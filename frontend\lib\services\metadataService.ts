/**
 * Metadata Service
 * 
 * Handles API calls for metadata tables (PurchaseTypes, RenewalTypes, Currencies)
 * Provides dropdown options for form fields
 */

export interface PurchaseType {
  PurchaseTypeID: number
  PurchaseTypeName: string
  Active: boolean
}

export interface RenewalType {
  RenewalTypeID: number
  RenewalTypeName: string
  Active: boolean
}

export interface Currency {
  CurrencyID: string
  CurrencyName: string
  Active: boolean
}

export interface MetadataOptions {
  purchaseTypes: PurchaseType[]
  renewalTypes: RenewalType[]
  currencies: Currency[]
}

/**
 * Fetch all metadata options for form dropdowns
 * 
 * @returns Promise with all metadata options
 */
export async function getMetadataOptions(): Promise<MetadataOptions> {
  try {
    // TODO: Replace with actual API calls
    // This is a placeholder implementation
    
    console.log('Fetching metadata options...')
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Placeholder for actual API calls:
    /*
    const [purchaseTypesResponse, renewalTypesResponse, currenciesResponse] = await Promise.all([
      fetch('/api/metadata/purchase-types'),
      fetch('/api/metadata/renewal-types'),
      fetch('/api/metadata/currencies')
    ])
    
    const purchaseTypes = await purchaseTypesResponse.json()
    const renewalTypes = await renewalTypesResponse.json()
    const currencies = await currenciesResponse.json()
    */
    
    // Mock data based on the schema provided
    const purchaseTypes: PurchaseType[] = [
      { PurchaseTypeID: 1, PurchaseTypeName: 'Direct From Vendor', Active: true },
      { PurchaseTypeID: 2, PurchaseTypeName: 'Through Reseller', Active: true }
    ]
    
    const renewalTypes: RenewalType[] = [
      { RenewalTypeID: 4, RenewalTypeName: 'Subscription', Active: true },
      { RenewalTypeID: 3, RenewalTypeName: 'Renewal', Active: true },
      { RenewalTypeID: 2, RenewalTypeName: 'Warranty', Active: true },
      { RenewalTypeID: 1, RenewalTypeName: 'CapEx', Active: true }
    ]
    
    const currencies: Currency[] = [
      { CurrencyID: 'EUR', CurrencyName: 'Euro', Active: true },
      { CurrencyID: 'USD', CurrencyName: 'US Dollar', Active: true },
      { CurrencyID: 'CAD', CurrencyName: 'Canadian Dollar', Active: true },
      { CurrencyID: 'GBP', CurrencyName: 'British Pound', Active: true }
    ]
    
    // Filter only active items
    return {
      purchaseTypes: purchaseTypes.filter(item => item.Active),
      renewalTypes: renewalTypes.filter(item => item.Active),
      currencies: currencies.filter(item => item.Active)
    }
    
  } catch (error) {
    console.error('Error fetching metadata options:', error)
    throw new Error(error instanceof Error ? error.message : 'Failed to fetch metadata options')
  }
}

/**
 * Fetch purchase types from metadata.PurchaseTypes
 * 
 * @returns Promise with active purchase types
 */
export async function getPurchaseTypes(): Promise<PurchaseType[]> {
  try {
    console.log('Fetching purchase types...')
    
    // TODO: Replace with actual API call
    /*
    const response = await fetch('/api/metadata/purchase-types')
    if (!response.ok) {
      throw new Error(`Failed to fetch purchase types: ${response.statusText}`)
    }
    return await response.json()
    */
    
    // Mock data
    const purchaseTypes: PurchaseType[] = [
      { PurchaseTypeID: 1, PurchaseTypeName: 'Direct From Vendor', Active: true },
      { PurchaseTypeID: 2, PurchaseTypeName: 'Through Reseller', Active: true }
    ]
    
    return purchaseTypes.filter(item => item.Active)
    
  } catch (error) {
    console.error('Error fetching purchase types:', error)
    throw new Error(error instanceof Error ? error.message : 'Failed to fetch purchase types')
  }
}

/**
 * Fetch renewal types from metadata.RenewalTypes
 * 
 * @returns Promise with active renewal types
 */
export async function getRenewalTypes(): Promise<RenewalType[]> {
  try {
    console.log('Fetching renewal types...')
    
    // TODO: Replace with actual API call
    /*
    const response = await fetch('/api/metadata/renewal-types')
    if (!response.ok) {
      throw new Error(`Failed to fetch renewal types: ${response.statusText}`)
    }
    return await response.json()
    */
    
    // Mock data
    const renewalTypes: RenewalType[] = [
      { RenewalTypeID: 4, RenewalTypeName: 'Subscription', Active: true },
      { RenewalTypeID: 3, RenewalTypeName: 'Renewal', Active: true },
      { RenewalTypeID: 2, RenewalTypeName: 'Warranty', Active: true },
      { RenewalTypeID: 1, RenewalTypeName: 'CapEx', Active: true }
    ]
    
    return renewalTypes.filter(item => item.Active)
    
  } catch (error) {
    console.error('Error fetching renewal types:', error)
    throw new Error(error instanceof Error ? error.message : 'Failed to fetch renewal types')
  }
}

/**
 * Fetch currencies from metadata.Currencies
 * 
 * @returns Promise with active currencies
 */
export async function getCurrencies(): Promise<Currency[]> {
  try {
    console.log('Fetching currencies...')
    
    // TODO: Replace with actual API call
    /*
    const response = await fetch('/api/metadata/currencies')
    if (!response.ok) {
      throw new Error(`Failed to fetch currencies: ${response.statusText}`)
    }
    return await response.json()
    */
    
    // Mock data
    const currencies: Currency[] = [
      { CurrencyID: 'EUR', CurrencyName: 'Euro', Active: true },
      { CurrencyID: 'USD', CurrencyName: 'US Dollar', Active: true },
      { CurrencyID: 'CAD', CurrencyName: 'Canadian Dollar', Active: true },
      { CurrencyID: 'GBP', CurrencyName: 'British Pound', Active: true }
    ]
    
    return currencies.filter(item => item.Active)
    
  } catch (error) {
    console.error('Error fetching currencies:', error)
    throw new Error(error instanceof Error ? error.message : 'Failed to fetch currencies')
  }
}

/**
 * Format currency display text as "CurrencyID (CurrencyName)"
 * 
 * @param currency - Currency object
 * @returns Formatted display text
 */
export function formatCurrencyDisplay(currency: Currency): string {
  return `${currency.CurrencyID} (${currency.CurrencyName})`
}

/**
 * Database Schema Information
 * 
 * The following metadata tables should exist:
 * 
 * metadata.PurchaseTypes:
 * - PurchaseTypeID (integer, Primary Key)
 * - PurchaseTypeName (text)
 * - Active (boolean)
 * 
 * metadata.RenewalTypes:
 * - RenewalTypeID (integer, Primary Key)
 * - RenewalTypeName (text)
 * - Active (boolean)
 * 
 * metadata.Currencies:
 * - CurrencyID (character(3), Primary Key)
 * - CurrencyName (text)
 * - Active (boolean)
 */
