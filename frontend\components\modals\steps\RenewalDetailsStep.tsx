/**
 * Renewal Details Step Component
 * 
 * First step of the Add Renewal modal - collects renewal information
 */

'use client'

import React, { useCallback } from 'react'
import { RenewalFormData } from '../AddRenewalModal'

interface RenewalDetailsStepProps {
  data: RenewalFormData
  onChange: (data: RenewalFormData) => void
  onNext: () => void
  onCancel: () => void
}

const RenewalDetailsStep: React.FC<RenewalDetailsStepProps> = ({
  data,
  onChange,
  onNext,
  onCancel
}) => {
  // Handle form field changes
  const handleChange = useCallback((field: keyof RenewalFormData, value: any) => {
    onChange({
      ...data,
      [field]: value
    })
  }, [data, onChange])

  // Handle email list changes
  const handleEmailsChange = useCallback((emailsString: string) => {
    const emails = emailsString.split(',').map(email => email.trim()).filter(email => email)
    handleChange('associatedEmails', emails)
  }, [handleChange])

  // Validate required fields
  const isValid = data.productName && data.vendor && data.renewalDate

  return (
    <div className="renewal-details-step">
      <div className="form-grid">
        {/* Product Name */}
        <div className="form-group">
          <label htmlFor="productName" className="form-label required">
            Product Name*
          </label>
          <input
            id="productName"
            type="text"
            className="form-input"
            placeholder="Type product name..."
            value={data.productName}
            onChange={(e) => handleChange('productName', e.target.value)}
          />
        </div>

        {/* Version/Edition */}
        <div className="form-group">
          <label htmlFor="version" className="form-label">
            Version/Edition*
          </label>
          <input
            id="version"
            type="text"
            className="form-input"
            placeholder="e.g. 2021 or Enterprise"
            value={data.version}
            onChange={(e) => handleChange('version', e.target.value)}
          />
        </div>

        {/* Vendor */}
        <div className="form-group">
          <label htmlFor="vendor" className="form-label required">
            Vendor*
          </label>
          <input
            id="vendor"
            type="text"
            className="form-input"
            placeholder="Type vendor name..."
            value={data.vendor}
            onChange={(e) => handleChange('vendor', e.target.value)}
          />
        </div>

        {/* Type */}
        <div className="form-group">
          <label htmlFor="type" className="form-label">
            Type*
          </label>
          <select
            id="type"
            className="form-select"
            value={data.type}
            onChange={(e) => handleChange('type', e.target.value)}
          >
            <option value="Subscription">Subscription</option>
            <option value="License">License</option>
            <option value="Support">Support</option>
            <option value="Maintenance">Maintenance</option>
          </select>
        </div>

        {/* Purchase Type */}
        <div className="form-group">
          <label htmlFor="purchaseType" className="form-label">
            Purchase Type*
          </label>
          <select
            id="purchaseType"
            className="form-select"
            value={data.purchaseType}
            onChange={(e) => handleChange('purchaseType', e.target.value)}
          >
            <option value="Direct from Vendor">Direct from Vendor</option>
            <option value="Reseller">Reseller</option>
            <option value="Marketplace">Marketplace</option>
          </select>
        </div>

        {/* Department */}
        <div className="form-group">
          <label htmlFor="department" className="form-label">
            Department*
          </label>
          <input
            id="department"
            type="text"
            className="form-input"
            placeholder="Enter department..."
            value={data.department}
            onChange={(e) => handleChange('department', e.target.value)}
          />
        </div>

        {/* Licensed Date */}
        <div className="form-group">
          <label htmlFor="licensedDate" className="form-label">
            Licensed Date*
          </label>
          <input
            id="licensedDate"
            type="date"
            className="form-input"
            value={data.licensedDate}
            onChange={(e) => handleChange('licensedDate', e.target.value)}
          />
        </div>

        {/* Renewal Date */}
        <div className="form-group">
          <label htmlFor="renewalDate" className="form-label required">
            Renewal Date*
          </label>
          <input
            id="renewalDate"
            type="date"
            className="form-input"
            value={data.renewalDate}
            onChange={(e) => handleChange('renewalDate', e.target.value)}
          />
        </div>
      </div>

      {/* Associated Email Addresses */}
      <div className="form-group full-width">
        <label htmlFor="associatedEmails" className="form-label">
          Associated Email Addresses*
        </label>
        <textarea
          id="associatedEmails"
          className="form-textarea"
          placeholder="Enter email addresses separated by commas"
          rows={3}
          value={data.associatedEmails.join(', ')}
          onChange={(e) => handleEmailsChange(e.target.value)}
        />
        <p className="form-help">
          Email addresses associated with this renewal or subscription
        </p>
      </div>

      {/* Reseller Information */}
      <div className="form-group full-width">
        <label htmlFor="reseller" className="form-label">
          Reseller Information
        </label>
        <input
          id="reseller"
          type="text"
          className="form-input"
          placeholder="Reseller name (if applicable)"
          value={data.reseller}
          onChange={(e) => handleChange('reseller', e.target.value)}
        />
      </div>

      {/* Currency and Cost */}
      <div className="form-grid">
        <div className="form-group">
          <label htmlFor="currency" className="form-label">
            Currency*
          </label>
          <select
            id="currency"
            className="form-select"
            value={data.currency}
            onChange={(e) => handleChange('currency', e.target.value)}
          >
            <option value="USD">USD</option>
            <option value="EUR">EUR</option>
            <option value="GBP">GBP</option>
            <option value="CAD">CAD</option>
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="cost" className="form-label">
            Cost*
          </label>
          <input
            id="cost"
            type="number"
            className="form-input"
            placeholder="Amount in USD"
            min="0"
            step="0.01"
            value={data.cost || ''}
            onChange={(e) => handleChange('cost', parseFloat(e.target.value) || 0)}
          />
        </div>
      </div>

      {/* Cost Code */}
      <div className="form-group full-width">
        <label htmlFor="costCode" className="form-label">
          Cost Code
        </label>
        <input
          id="costCode"
          type="text"
          className="form-input"
          placeholder="Optional cost code for accounting"
          value={data.costCode}
          onChange={(e) => handleChange('costCode', e.target.value)}
        />
        <p className="form-help">
          Enter an optional cost code for internal accounting purposes
        </p>
      </div>

      {/* License Count */}
      <div className="form-group full-width">
        <label htmlFor="licenseCount" className="form-label">
          License Count
        </label>
        <input
          id="licenseCount"
          type="number"
          className="form-input"
          placeholder="Number of licenses (optional)"
          min="0"
          value={data.licenseCount || ''}
          onChange={(e) => handleChange('licenseCount', parseInt(e.target.value) || 0)}
        />
        <p className="form-help">
          Enter the number of licenses included in this renewal (optional)
        </p>
      </div>

      {/* Description */}
      <div className="form-group full-width">
        <label htmlFor="description" className="form-label">
          Description
        </label>
        <textarea
          id="description"
          className="form-textarea"
          placeholder="Brief description of the renewal"
          rows={3}
          value={data.description}
          onChange={(e) => handleChange('description', e.target.value)}
        />
      </div>

      {/* Notes */}
      <div className="form-group full-width">
        <label htmlFor="notes" className="form-label">
          Notes
        </label>
        <textarea
          id="notes"
          className="form-textarea"
          placeholder="Additional information about this renewal"
          rows={3}
          value={data.notes}
          onChange={(e) => handleChange('notes', e.target.value)}
        />
      </div>

      {/* Action Buttons */}
      <div className="modal-actions">
        <button
          type="button"
          className="btn btn-secondary"
          onClick={onCancel}
        >
          Cancel
        </button>
        <button
          type="button"
          className="btn btn-primary"
          onClick={onNext}
          disabled={!isValid}
        >
          Next: Set Up Alerts
        </button>
      </div>
    </div>
  )
}

export default RenewalDetailsStep
