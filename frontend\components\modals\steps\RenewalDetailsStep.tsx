/**
 * Renewal Details Step Component
 *
 * First step of the Add Renewal modal - collects renewal information
 * Uses metadata service for dropdown options and proper form validation
 */

'use client'

import React, { useCallback, useEffect, useState } from 'react'
import { RenewalFormData } from '../AddRenewalModal'
import {
  getMetadataOptions,
  type MetadataOptions,
  type PurchaseType,
  type RenewalType,
  type Currency,
  formatCurrencyDisplay
} from '@/lib/services/metadataService'

interface RenewalDetailsStepProps {
  data: RenewalFormData
  onChange: (data: RenewalFormData) => void
  onNext: () => void
  onCancel: () => void
}

const RenewalDetailsStep: React.FC<RenewalDetailsStepProps> = ({
  data,
  onChange,
  onNext,
  onCancel
}) => {
  const [metadataOptions, setMetadataOptions] = useState<MetadataOptions | null>(null)
  const [isLoadingMetadata, setIsLoadingMetadata] = useState(true)
  const [metadataError, setMetadataError] = useState<string | null>(null)

  // Load metadata options on component mount
  useEffect(() => {
    const loadMetadata = async () => {
      try {
        setIsLoadingMetadata(true)
        setMetadataError(null)
        const options = await getMetadataOptions()
        setMetadataOptions(options)
      } catch (error) {
        console.error('Error loading metadata options:', error)
        setMetadataError(error instanceof Error ? error.message : 'Failed to load dropdown options')
      } finally {
        setIsLoadingMetadata(false)
      }
    }

    loadMetadata()
  }, [])

  // Handle form field changes
  const handleChange = useCallback((field: keyof RenewalFormData, value: any) => {
    onChange({
      ...data,
      [field]: value
    })
  }, [data, onChange])

  // Handle email list changes
  const handleEmailsChange = useCallback((emailsString: string) => {
    const emails = emailsString.split(',').map(email => email.trim()).filter(email => email)
    handleChange('associatedEmails', emails)
  }, [handleChange])

  // Validate required fields
  const isValid = data.productName &&
                  data.version &&
                  data.vendor &&
                  data.renewalTypeId &&
                  data.department &&
                  data.purchaseTypeId &&
                  data.licensedDate &&
                  data.renewalDate &&
                  data.associatedEmails.length > 0 &&
                  data.currencyId &&
                  data.cost > 0

  return (
    <div className="renewal-details-step">
      {/* Loading state */}
      {isLoadingMetadata && (
        <div className="loading-message">
          <p>Loading form options...</p>
        </div>
      )}

      {/* Error state */}
      {metadataError && (
        <div className="error-message">
          <p>Error loading form options. Some dropdowns may not work properly.</p>
          <button
            type="button"
            className="btn btn-link"
            onClick={() => window.location.reload()}
          >
            Refresh Page
          </button>
        </div>
      )}

      <div className="form-grid">
        {/* Product Name */}
        <div className="form-group">
          <label htmlFor="productName" className="form-label required">
            Product Name
          </label>
          <input
            id="productName"
            type="text"
            className="form-input"
            placeholder="Type product name..."
            value={data.productName}
            onChange={(e) => handleChange('productName', e.target.value)}
          />
        </div>

        {/* Version/Edition */}
        <div className="form-group">
          <label htmlFor="version" className="form-label">
            Version/Edition
          </label>
          <input
            id="version"
            type="text"
            className="form-input"
            placeholder="e.g. 2021 or Enterprise"
            value={data.version}
            onChange={(e) => handleChange('version', e.target.value)}
          />
        </div>

        {/* Vendor */}
        <div className="form-group">
          <label htmlFor="vendor" className="form-label required">
            Vendor
          </label>
          <input
            id="vendor"
            type="text"
            className="form-input"
            placeholder="Type vendor name..."
            value={data.vendor}
            onChange={(e) => handleChange('vendor', e.target.value)}
          />
        </div>

        {/* Type */}
        <div className="form-group">
          <label htmlFor="renewalTypeId" className="form-label required">
            Type
          </label>
          <select
            id="renewalTypeId"
            className="form-select"
            value={data.renewalTypeId || ''}
            onChange={(e) => handleChange('renewalTypeId', e.target.value ? parseInt(e.target.value) : null)}
            disabled={isLoadingMetadata}
          >
            <option value="">Select type...</option>
            {metadataOptions?.renewalTypes.map((renewalType) => (
              <option key={renewalType.RenewalTypeID} value={renewalType.RenewalTypeID}>
                {renewalType.RenewalTypeName}
              </option>
            ))}
          </select>
          {metadataError && (
            <p className="form-error">Error loading types: {metadataError}</p>
          )}
        </div>

        {/* Purchase Type */}
        <div className="form-group">
          <label htmlFor="purchaseTypeId" className="form-label required">
            Purchase Type
          </label>
          <select
            id="purchaseTypeId"
            className="form-select"
            value={data.purchaseTypeId || ''}
            onChange={(e) => handleChange('purchaseTypeId', e.target.value ? parseInt(e.target.value) : null)}
            disabled={isLoadingMetadata}
          >
            <option value="">Select purchase type...</option>
            {metadataOptions?.purchaseTypes.map((purchaseType) => (
              <option key={purchaseType.PurchaseTypeID} value={purchaseType.PurchaseTypeID}>
                {purchaseType.PurchaseTypeName}
              </option>
            ))}
          </select>
          {metadataError && (
            <p className="form-error">Error loading purchase types: {metadataError}</p>
          )}
        </div>

        {/* Department */}
        <div className="form-group">
          <label htmlFor="department" className="form-label required">
            Department
          </label>
          <input
            id="department"
            type="text"
            className="form-input"
            placeholder="Enter department..."
            value={data.department}
            onChange={(e) => handleChange('department', e.target.value)}
            required
          />
        </div>

        {/* Licensed Date */}
        <div className="form-group">
          <label htmlFor="licensedDate" className="form-label required">
            Licensed Date
          </label>
          <input
            id="licensedDate"
            type="date"
            className="form-input"
            value={data.licensedDate}
            onChange={(e) => handleChange('licensedDate', e.target.value)}
            required
          />
        </div>

        {/* Renewal Date */}
        <div className="form-group">
          <label htmlFor="renewalDate" className="form-label required">
            Renewal Date
          </label>
          <input
            id="renewalDate"
            type="date"
            className="form-input"
            value={data.renewalDate}
            onChange={(e) => handleChange('renewalDate', e.target.value)}
            required
          />
        </div>
      </div>

      {/* Associated Email Addresses */}
      <div className="form-group full-width">
        <label htmlFor="associatedEmails" className="form-label required">
          Associated Email Addresses
        </label>
        <textarea
          id="associatedEmails"
          className="form-textarea"
          placeholder="Enter email addresses separated by commas"
          rows={3}
          value={data.associatedEmails.join(', ')}
          onChange={(e) => handleEmailsChange(e.target.value)}
          required
        />
        <p className="form-help">
          Email addresses associated with this renewal or subscription
        </p>
      </div>

      {/* Reseller Information */}
      <div className="form-group full-width">
        <label htmlFor="reseller" className="form-label">
          Reseller Information
        </label>
        <input
          id="reseller"
          type="text"
          className="form-input"
          placeholder="Reseller name (if applicable)"
          value={data.reseller}
          onChange={(e) => handleChange('reseller', e.target.value)}
        />
      </div>

      {/* Currency and Cost */}
      <div className="form-grid">
        <div className="form-group">
          <label htmlFor="currencyId" className="form-label required">
            Currency
          </label>
          <select
            id="currencyId"
            className="form-select"
            value={data.currencyId}
            onChange={(e) => handleChange('currencyId', e.target.value)}
            disabled={isLoadingMetadata}
            required
          >
            <option value="">Select currency...</option>
            {metadataOptions?.currencies.map((currency) => (
              <option key={currency.CurrencyID} value={currency.CurrencyID}>
                {formatCurrencyDisplay(currency)}
              </option>
            ))}
          </select>
          {metadataError && (
            <p className="form-error">Error loading currencies: {metadataError}</p>
          )}
        </div>

        <div className="form-group">
          <label htmlFor="cost" className="form-label required">
            Cost
          </label>
          <input
            id="cost"
            type="number"
            className="form-input"
            placeholder="Amount in selected currency"
            min="0"
            step="0.01"
            value={data.cost || ''}
            onChange={(e) => handleChange('cost', parseFloat(e.target.value) || 0)}
            required
          />
        </div>
      </div>

      {/* Cost Code */}
      <div className="form-group full-width">
        <label htmlFor="costCode" className="form-label">
          Cost Code
        </label>
        <input
          id="costCode"
          type="text"
          className="form-input"
          placeholder="Optional cost code for accounting"
          value={data.costCode}
          onChange={(e) => handleChange('costCode', e.target.value)}
        />
        <p className="form-help">
          Enter an optional cost code for internal accounting purposes
        </p>
      </div>

      {/* License Count */}
      <div className="form-group full-width">
        <label htmlFor="licenseCount" className="form-label">
          License Count
        </label>
        <input
          id="licenseCount"
          type="number"
          className="form-input"
          placeholder="Number of licenses (optional)"
          min="0"
          value={data.licenseCount || ''}
          onChange={(e) => handleChange('licenseCount', parseInt(e.target.value) || 0)}
        />
        <p className="form-help">
          Enter the number of licenses included in this renewal (optional)
        </p>
      </div>

      {/* Description */}
      <div className="form-group full-width">
        <label htmlFor="description" className="form-label">
          Description
        </label>
        <textarea
          id="description"
          className="form-textarea"
          placeholder="Brief description of the renewal"
          rows={3}
          value={data.description}
          onChange={(e) => handleChange('description', e.target.value)}
        />
      </div>

      {/* Notes */}
      <div className="form-group full-width">
        <label htmlFor="notes" className="form-label">
          Notes
        </label>
        <textarea
          id="notes"
          className="form-textarea"
          placeholder="Additional information about this renewal"
          rows={3}
          value={data.notes}
          onChange={(e) => handleChange('notes', e.target.value)}
        />
      </div>

      {/* Action Buttons */}
      <div className="modal-actions">
        <button
          type="button"
          className="btn btn-secondary"
          onClick={onCancel}
        >
          Cancel
        </button>
        <button
          type="button"
          className="btn btn-primary"
          onClick={onNext}
          disabled={!isValid}
        >
          Next: Set Up Alerts
        </button>
      </div>
    </div>
  )
}

export default RenewalDetailsStep
