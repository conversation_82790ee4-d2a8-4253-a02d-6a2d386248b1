{"version": 3, "names": ["_defineProperty", "_jsxFileName", "React", "__jsx", "createElement", "cov_18s9p7qc7a", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "_coverageSchema", "coverage", "actualCoverage", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "createContext", "useState", "useEffect", "useContext", "useAuth", "useClient", "UserContext", "user", "isLoading", "updateUserProfile", "updateUserPreferences", "getDisplayName", "hasRole", "UserProvider", "children", "setUser", "setIsLoading", "isAuthenticated", "authUser", "client", "clientId", "id", "preferences", "theme", "notifications", "email", "sms", "displayDensity", "loadUserPreferences", "storedPrefs", "localStorage", "getItem", "parsedPrefs", "JSON", "parse", "prev", "response", "fetch", "ok", "json", "setItem", "stringify", "error", "console", "data", "method", "headers", "body", "Error", "updatedUser", "updatedPreferences", "given_name", "family_name", "isUUID", "str", "test", "split", "substring", "role", "roles", "includes", "Provider", "value", "__self", "__source", "fileName", "lineNumber", "columnNumber", "useUser"], "sources": ["UserContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useState, useEffect, ReactNode, useContext } from 'react'\nimport { useAuth } from './AuthContext'\nimport { useClient } from './ClientContext'\n\nexport interface UserPreferences {\n  theme?: 'light' | 'dark' | 'system'\n  notifications?: {\n    email?: boolean\n    push?: boolean\n    sms?: boolean\n  }\n  displayDensity?: 'comfortable' | 'compact'\n  [key: string]: any\n}\n\nexport interface User {\n  id: string\n  email: string\n  name?: string\n  given_name?: string\n  family_name?: string\n  roles?: string[]\n  clientId?: string\n  preferences?: UserPreferences\n  lastLogin?: Date\n}\n\ninterface UserContextType {\n  user: User | null\n  isLoading: boolean\n  updateUserProfile: (data: Partial<User>) => Promise<boolean>\n  updateUserPreferences: (preferences: Partial<UserPreferences>) => Promise<boolean>\n  getDisplayName: () => string\n  hasRole: (role: string) => boolean\n}\n\n// Create context with default values\nconst UserContext = createContext<UserContextType>({\n  user: null,\n  isLoading: false,\n  updateUserProfile: async () => false,\n  updateUserPreferences: async () => false,\n  getDisplayName: () => 'User',\n  hasRole: () => false\n})\n\nexport function UserProvider({ children }: { children: ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n  const { isAuthenticated, authUser } = useAuth()\n  const { client } = useClient()\n\n  // Update user when auth state changes\n  useEffect(() => {\n    if (isAuthenticated && authUser) {\n      // Initialize user from auth data\n      setUser({\n        ...authUser,\n        clientId: authUser.clientId || client?.id,\n        preferences: user?.preferences || {\n          theme: 'system',\n          notifications: {\n            email: true,\n            push: true,\n            sms: false\n          },\n          displayDensity: 'comfortable'\n        }\n      })\n    } else {\n      setUser(null)\n    }\n  }, [isAuthenticated, authUser, client])\n\n  // Load user preferences from API or localStorage\n  useEffect(() => {\n    if (!user?.id) return\n\n    const loadUserPreferences = async () => {\n      try {\n        setIsLoading(true)\n        \n        // Try to load from localStorage first (for demo/development)\n        const storedPrefs = localStorage.getItem(`user_prefs_${user.id}`)\n        if (storedPrefs) {\n          const parsedPrefs = JSON.parse(storedPrefs)\n          setUser(prev => prev ? { ...prev, preferences: parsedPrefs } : null)\n          return\n        }\n        \n        // If not in localStorage, fetch from API\n        const response = await fetch(`/api/users/${user.id}/preferences`)\n        if (response.ok) {\n          const preferences = await response.json()\n          setUser(prev => prev ? { ...prev, preferences } : null)\n          \n          // Cache in localStorage\n          localStorage.setItem(`user_prefs_${user.id}`, JSON.stringify(preferences))\n        }\n      } catch (error) {\n        console.error('Error loading user preferences:', error)\n      } finally {\n        setIsLoading(false)\n      }\n    }\n\n    loadUserPreferences()\n  }, [user?.id])\n\n  // Update user profile\n  const updateUserProfile = async (data: Partial<User>): Promise<boolean> => {\n    if (!user?.id) return false\n    \n    try {\n      setIsLoading(true)\n      const response = await fetch(`/api/users/${user.id}`, {\n        method: 'PATCH',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(data)\n      })\n      \n      if (!response.ok) {\n        throw new Error('Failed to update profile')\n      }\n      \n      const updatedUser = await response.json()\n      setUser(prev => prev ? { ...prev, ...updatedUser } : null)\n      return true\n    } catch (error) {\n      console.error('Error updating user profile:', error)\n      return false\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // Update user preferences\n  const updateUserPreferences = async (preferences: Partial<UserPreferences>): Promise<boolean> => {\n    if (!user?.id) return false\n    \n    try {\n      setIsLoading(true)\n      \n      // Update in state first for immediate feedback\n      const updatedPreferences = { ...user.preferences, ...preferences }\n      setUser(prev => prev ? { ...prev, preferences: updatedPreferences } : null)\n      \n      // Cache in localStorage\n      localStorage.setItem(`user_prefs_${user.id}`, JSON.stringify(updatedPreferences))\n      \n      // Then update on server\n      const response = await fetch(`/api/users/${user.id}/preferences`, {\n        method: 'PATCH',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(preferences)\n      })\n      \n      if (!response.ok) {\n        throw new Error('Failed to update preferences')\n      }\n      \n      return true\n    } catch (error) {\n      console.error('Error updating user preferences:', error)\n      // Revert to previous state on error\n      const storedPrefs = localStorage.getItem(`user_prefs_${user.id}`)\n      if (storedPrefs) {\n        const parsedPrefs = JSON.parse(storedPrefs)\n        setUser(prev => prev ? { ...prev, preferences: parsedPrefs } : null)\n      }\n      return false\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // Get formatted display name\n  const getDisplayName = (): string => {\n    if (!user) return 'User'\n    \n    if (user.given_name && user.family_name) {\n      return `${user.given_name} ${user.family_name}`\n    }\n    \n    if (user.name) {\n      return user.name\n    }\n    \n    // Check if email looks like a UUID or system-generated value\n    const isUUID = (str: string) => {\n      return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(str)\n    }\n    \n    if (user.email && !isUUID(user.email.split('@')[0])) {\n      return user.email.split('@')[0]\n    }\n    \n    return `User ${user.id.substring(0, 8)}`\n  }\n\n  // Role checking utility\n  const hasRole = (role: string): boolean => {\n    return user?.roles?.includes(role) || false\n  }\n\n  return (\n    <UserContext.Provider\n      value={{\n        user,\n        isLoading,\n        updateUserProfile,\n        updateUserPreferences,\n        getDisplayName,\n        hasRole\n      }}\n    >\n      {children}\n    </UserContext.Provider>\n  )\n}\n\n// Custom hook for using user context\nexport const useUser = () => useContext(UserContext)\n\n\n\n\n\n\n\n"], "mappings": "AAAA,YAAY;;AAAA;AAAA,OAAAA,eAAA;AAAA,IAAAC,YAAA;AAAA,OAAAC,KAAA;AAAA,IAAAC,KAAA,GAAAD,KAAA,CAAAE,aAAA;AAAA,SAAAC,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,eAAA;IAAArB,IAAA;EAAA;EAAA,IAAAsB,QAAA,GAAArB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAmB,QAAA,CAAAvB,IAAA,KAAAuB,QAAA,CAAAvB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAsB,QAAA,CAAAvB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAmB,cAAA,GAAAD,QAAA,CAAAvB,IAAA;EAAA;IAeA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAyB,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAzB,cAAA;AAAA,SAAA0B,QAAAC,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAJ,CAAA,OAAAG,MAAA,CAAAE,qBAAA,QAAAC,CAAA,GAAAH,MAAA,CAAAE,qBAAA,CAAAL,CAAA,GAAAC,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAC,MAAA,WAAAN,CAAA,WAAAE,MAAA,CAAAK,wBAAA,CAAAR,CAAA,EAAAC,CAAA,EAAAQ,UAAA,OAAAP,CAAA,CAAAQ,IAAA,CAAAC,KAAA,CAAAT,CAAA,EAAAI,CAAA,YAAAJ,CAAA;AAAA,SAAAU,cAAAZ,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAY,SAAA,CAAAC,MAAA,EAAAb,CAAA,UAAAC,CAAA,WAAAW,SAAA,CAAAZ,CAAA,IAAAY,SAAA,CAAAZ,CAAA,QAAAA,CAAA,OAAAF,OAAA,CAAAI,MAAA,CAAAD,CAAA,OAAAa,OAAA,WAAAd,CAAA,IAAAjC,eAAA,CAAAgC,CAAA,EAAAC,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAa,yBAAA,GAAAb,MAAA,CAAAc,gBAAA,CAAAjB,CAAA,EAAAG,MAAA,CAAAa,yBAAA,CAAAd,CAAA,KAAAH,OAAA,CAAAI,MAAA,CAAAD,CAAA,GAAAa,OAAA,WAAAd,CAAA,IAAAE,MAAA,CAAAe,cAAA,CAAAlB,CAAA,EAAAC,CAAA,EAAAE,MAAA,CAAAK,wBAAA,CAAAN,CAAA,EAAAD,CAAA,iBAAAD,CAAA;AAbZ,SAASmB,aAAa,EAAEC,QAAQ,EAAEC,SAAS,EAAaC,UAAU,QAAQ,OAAO;AACjF,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,SAAS,QAAQ,iBAAiB;AAkC3C;AACA,MAAMC,WAAW;AAAA;AAAA,CAAApD,cAAA,GAAAoB,CAAA,oBAAG0B,aAAa,CAAkB;EACjDO,IAAI,EAAE,IAAI;EACVC,SAAS,EAAE,KAAK;EAChBC,iBAAiB,EAAE,MAAAA,CAAA,KAAY;IAAA;IAAAvD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAAA,YAAK;EAAD,CAAC;EACpCoC,qBAAqB,EAAE,MAAAA,CAAA,KAAY;IAAA;IAAAxD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAAA,YAAK;EAAD,CAAC;EACxCqC,cAAc,EAAEA,CAAA,KAAM;IAAA;IAAAzD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAAA,aAAM;EAAD,CAAC;EAC5BsC,OAAO,EAAEA,CAAA,KAAM;IAAA;IAAA1D,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAAA,YAAK;EAAD;AACrB,CAAC,CAAC;AAEF,OAAO,SAASuC,YAAYA,CAAC;EAAEC;AAAkC,CAAC,EAAE;EAAA;EAAA5D,cAAA,GAAAqB,CAAA;EAClE,MAAM,CAACgC,IAAI,EAAEQ,OAAO,CAAC;EAAA;EAAA,CAAA7D,cAAA,GAAAoB,CAAA,OAAG2B,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACO,SAAS,EAAEQ,YAAY,CAAC;EAAA;EAAA,CAAA9D,cAAA,GAAAoB,CAAA,OAAG2B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM;IAAEgB,eAAe;IAAEC;EAAS,CAAC;EAAA;EAAA,CAAAhE,cAAA,GAAAoB,CAAA,OAAG8B,OAAO,CAAC,CAAC;EAC/C,MAAM;IAAEe;EAAO,CAAC;EAAA;EAAA,CAAAjE,cAAA,GAAAoB,CAAA,OAAG+B,SAAS,CAAC,CAAC;;EAE9B;EAAA;EAAAnD,cAAA,GAAAoB,CAAA;EACA4B,SAAS,CAAC,MAAM;IAAA;IAAAhD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACd;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,UAAAyC,eAAe;IAAA;IAAA,CAAA/D,cAAA,GAAAsB,CAAA,UAAI0C,QAAQ,GAAE;MAAA;MAAAhE,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC/B;MACAyC,OAAO;MAAA;MAAAtB,aAAA,CAAAA,aAAA,KACFyB,QAAQ;QACXE,QAAQ;QAAE;QAAA,CAAAlE,cAAA,GAAAsB,CAAA,UAAA0C,QAAQ,CAACE,QAAQ;QAAA;QAAA,CAAAlE,cAAA,GAAAsB,CAAA,UAAI2C,MAAM,EAAEE,EAAE;QACzCC,WAAW;QAAE;QAAA,CAAApE,cAAA,GAAAsB,CAAA,UAAA+B,IAAI,EAAEe,WAAW;QAAA;QAAA,CAAApE,cAAA,GAAAsB,CAAA,UAAI;UAChC+C,KAAK,EAAE,QAAQ;UACfC,aAAa,EAAE;YACbC,KAAK,EAAE,IAAI;YACXlC,IAAI,EAAE,IAAI;YACVmC,GAAG,EAAE;UACP,CAAC;UACDC,cAAc,EAAE;QAClB,CAAC;MAAA,EACF,CAAC;IACJ,CAAC,MAAM;MAAA;MAAAzE,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACLyC,OAAO,CAAC,IAAI,CAAC;IACf;EACF,CAAC,EAAE,CAACE,eAAe,EAAEC,QAAQ,EAAEC,MAAM,CAAC,CAAC;;EAEvC;EAAA;EAAAjE,cAAA,GAAAoB,CAAA;EACA4B,SAAS,CAAC,MAAM;IAAA;IAAAhD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACd,IAAI,CAACiC,IAAI,EAAEc,EAAE,EAAE;MAAA;MAAAnE,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA;IAAK,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAErB,MAAMsD,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MAAA;MAAA1E,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MACtC,IAAI;QAAA;QAAApB,cAAA,GAAAoB,CAAA;QACF0C,YAAY,CAAC,IAAI,CAAC;;QAElB;QACA,MAAMa,WAAW;QAAA;QAAA,CAAA3E,cAAA,GAAAoB,CAAA,QAAGwD,YAAY,CAACC,OAAO,CAAC,cAAcxB,IAAI,CAACc,EAAE,EAAE,CAAC;QAAA;QAAAnE,cAAA,GAAAoB,CAAA;QACjE,IAAIuD,WAAW,EAAE;UAAA;UAAA3E,cAAA,GAAAsB,CAAA;UACf,MAAMwD,WAAW;UAAA;UAAA,CAAA9E,cAAA,GAAAoB,CAAA,QAAG2D,IAAI,CAACC,KAAK,CAACL,WAAW,CAAC;UAAA;UAAA3E,cAAA,GAAAoB,CAAA;UAC3CyC,OAAO,CAACoB,IAAI,IAAI;YAAA;YAAAjF,cAAA,GAAAqB,CAAA;YAAArB,cAAA,GAAAoB,CAAA;YAAA,OAAA6D,IAAI;YAAA;YAAA,CAAAjF,cAAA,GAAAsB,CAAA,UAAAiB,aAAA,CAAAA,aAAA,KAAQ0C,IAAI;cAAEb,WAAW,EAAEU;YAAW;YAAA;YAAA,CAAA9E,cAAA,GAAAsB,CAAA,UAAK,IAAI;UAAD,CAAC,CAAC;UAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACpE;QACF,CAAC;QAAA;QAAA;UAAApB,cAAA,GAAAsB,CAAA;QAAA;;QAED;QACA,MAAM4D,QAAQ;QAAA;QAAA,CAAAlF,cAAA,GAAAoB,CAAA,QAAG,MAAM+D,KAAK,CAAC,cAAc9B,IAAI,CAACc,EAAE,cAAc,CAAC;QAAA;QAAAnE,cAAA,GAAAoB,CAAA;QACjE,IAAI8D,QAAQ,CAACE,EAAE,EAAE;UAAA;UAAApF,cAAA,GAAAsB,CAAA;UACf,MAAM8C,WAAW;UAAA;UAAA,CAAApE,cAAA,GAAAoB,CAAA,QAAG,MAAM8D,QAAQ,CAACG,IAAI,CAAC,CAAC;UAAA;UAAArF,cAAA,GAAAoB,CAAA;UACzCyC,OAAO,CAACoB,IAAI,IAAI;YAAA;YAAAjF,cAAA,GAAAqB,CAAA;YAAArB,cAAA,GAAAoB,CAAA;YAAA,OAAA6D,IAAI;YAAA;YAAA,CAAAjF,cAAA,GAAAsB,CAAA,UAAAiB,aAAA,CAAAA,aAAA,KAAQ0C,IAAI;cAAEb;YAAW;YAAA;YAAA,CAAApE,cAAA,GAAAsB,CAAA,UAAK,IAAI;UAAD,CAAC,CAAC;;UAEvD;UAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACAwD,YAAY,CAACU,OAAO,CAAC,cAAcjC,IAAI,CAACc,EAAE,EAAE,EAAEY,IAAI,CAACQ,SAAS,CAACnB,WAAW,CAAC,CAAC;QAC5E,CAAC;QAAA;QAAA;UAAApE,cAAA,GAAAsB,CAAA;QAAA;MACH,CAAC,CAAC,OAAOkE,KAAK,EAAE;QAAA;QAAAxF,cAAA,GAAAoB,CAAA;QACdqE,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD,CAAC,SAAS;QAAA;QAAAxF,cAAA,GAAAoB,CAAA;QACR0C,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAAA;IAAA9D,cAAA,GAAAoB,CAAA;IAEDsD,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACrB,IAAI,EAAEc,EAAE,CAAC,CAAC;;EAEd;EAAA;EAAAnE,cAAA,GAAAoB,CAAA;EACA,MAAMmC,iBAAiB,GAAG,MAAOmC,IAAmB,IAAuB;IAAA;IAAA1F,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACzE,IAAI,CAACiC,IAAI,EAAEc,EAAE,EAAE;MAAA;MAAAnE,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,KAAK;IAAD,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAE3B,IAAI;MAAA;MAAApB,cAAA,GAAAoB,CAAA;MACF0C,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMoB,QAAQ;MAAA;MAAA,CAAAlF,cAAA,GAAAoB,CAAA,QAAG,MAAM+D,KAAK,CAAC,cAAc9B,IAAI,CAACc,EAAE,EAAE,EAAE;QACpDwB,MAAM,EAAE,OAAO;QACfC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEd,IAAI,CAACQ,SAAS,CAACG,IAAI;MAC3B,CAAC,CAAC;MAAA;MAAA1F,cAAA,GAAAoB,CAAA;MAEF,IAAI,CAAC8D,QAAQ,CAACE,EAAE,EAAE;QAAA;QAAApF,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAChB,MAAM,IAAI0E,KAAK,CAAC,0BAA0B,CAAC;MAC7C,CAAC;MAAA;MAAA;QAAA9F,cAAA,GAAAsB,CAAA;MAAA;MAED,MAAMyE,WAAW;MAAA;MAAA,CAAA/F,cAAA,GAAAoB,CAAA,QAAG,MAAM8D,QAAQ,CAACG,IAAI,CAAC,CAAC;MAAA;MAAArF,cAAA,GAAAoB,CAAA;MACzCyC,OAAO,CAACoB,IAAI,IAAI;QAAA;QAAAjF,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAA6D,IAAI;QAAA;QAAA,CAAAjF,cAAA,GAAAsB,CAAA,WAAAiB,aAAA,CAAAA,aAAA,KAAQ0C,IAAI,GAAKc,WAAW;QAAA;QAAA,CAAA/F,cAAA,GAAAsB,CAAA,WAAK,IAAI;MAAD,CAAC,CAAC;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAC1D,OAAO,IAAI;IACb,CAAC,CAAC,OAAOoE,KAAK,EAAE;MAAA;MAAAxF,cAAA,GAAAoB,CAAA;MACdqE,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MAAA;MAAAxF,cAAA,GAAAoB,CAAA;MACpD,OAAO,KAAK;IACd,CAAC,SAAS;MAAA;MAAApB,cAAA,GAAAoB,CAAA;MACR0C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EAAA;EAAA9D,cAAA,GAAAoB,CAAA;EACA,MAAMoC,qBAAqB,GAAG,MAAOY,WAAqC,IAAuB;IAAA;IAAApE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC/F,IAAI,CAACiC,IAAI,EAAEc,EAAE,EAAE;MAAA;MAAAnE,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,KAAK;IAAD,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAE3B,IAAI;MAAA;MAAApB,cAAA,GAAAoB,CAAA;MACF0C,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,MAAMkC,kBAAkB;MAAA;MAAA,CAAAhG,cAAA,GAAAoB,CAAA,QAAAmB,aAAA,CAAAA,aAAA,KAAQc,IAAI,CAACe,WAAW,GAAKA,WAAW,EAAE;MAAA;MAAApE,cAAA,GAAAoB,CAAA;MAClEyC,OAAO,CAACoB,IAAI,IAAI;QAAA;QAAAjF,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAA6D,IAAI;QAAA;QAAA,CAAAjF,cAAA,GAAAsB,CAAA,WAAAiB,aAAA,CAAAA,aAAA,KAAQ0C,IAAI;UAAEb,WAAW,EAAE4B;QAAkB;QAAA;QAAA,CAAAhG,cAAA,GAAAsB,CAAA,WAAK,IAAI;MAAD,CAAC,CAAC;;MAE3E;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACAwD,YAAY,CAACU,OAAO,CAAC,cAAcjC,IAAI,CAACc,EAAE,EAAE,EAAEY,IAAI,CAACQ,SAAS,CAACS,kBAAkB,CAAC,CAAC;;MAEjF;MACA,MAAMd,QAAQ;MAAA;MAAA,CAAAlF,cAAA,GAAAoB,CAAA,QAAG,MAAM+D,KAAK,CAAC,cAAc9B,IAAI,CAACc,EAAE,cAAc,EAAE;QAChEwB,MAAM,EAAE,OAAO;QACfC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEd,IAAI,CAACQ,SAAS,CAACnB,WAAW;MAClC,CAAC,CAAC;MAAA;MAAApE,cAAA,GAAAoB,CAAA;MAEF,IAAI,CAAC8D,QAAQ,CAACE,EAAE,EAAE;QAAA;QAAApF,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAChB,MAAM,IAAI0E,KAAK,CAAC,8BAA8B,CAAC;MACjD,CAAC;MAAA;MAAA;QAAA9F,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED,OAAO,IAAI;IACb,CAAC,CAAC,OAAOoE,KAAK,EAAE;MAAA;MAAAxF,cAAA,GAAAoB,CAAA;MACdqE,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD;MACA,MAAMb,WAAW;MAAA;MAAA,CAAA3E,cAAA,GAAAoB,CAAA,QAAGwD,YAAY,CAACC,OAAO,CAAC,cAAcxB,IAAI,CAACc,EAAE,EAAE,CAAC;MAAA;MAAAnE,cAAA,GAAAoB,CAAA;MACjE,IAAIuD,WAAW,EAAE;QAAA;QAAA3E,cAAA,GAAAsB,CAAA;QACf,MAAMwD,WAAW;QAAA;QAAA,CAAA9E,cAAA,GAAAoB,CAAA,QAAG2D,IAAI,CAACC,KAAK,CAACL,WAAW,CAAC;QAAA;QAAA3E,cAAA,GAAAoB,CAAA;QAC3CyC,OAAO,CAACoB,IAAI,IAAI;UAAA;UAAAjF,cAAA,GAAAqB,CAAA;UAAArB,cAAA,GAAAoB,CAAA;UAAA,OAAA6D,IAAI;UAAA;UAAA,CAAAjF,cAAA,GAAAsB,CAAA,WAAAiB,aAAA,CAAAA,aAAA,KAAQ0C,IAAI;YAAEb,WAAW,EAAEU;UAAW;UAAA;UAAA,CAAA9E,cAAA,GAAAsB,CAAA,WAAK,IAAI;QAAD,CAAC,CAAC;MACtE,CAAC;MAAA;MAAA;QAAAtB,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACD,OAAO,KAAK;IACd,CAAC,SAAS;MAAA;MAAApB,cAAA,GAAAoB,CAAA;MACR0C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EAAA;EAAA9D,cAAA,GAAAoB,CAAA;EACA,MAAMqC,cAAc,GAAGA,CAAA,KAAc;IAAA;IAAAzD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACnC,IAAI,CAACiC,IAAI,EAAE;MAAA;MAAArD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,MAAM;IAAD,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAExB;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA+B,IAAI,CAAC4C,UAAU;IAAA;IAAA,CAAAjG,cAAA,GAAAsB,CAAA,WAAI+B,IAAI,CAAC6C,WAAW,GAAE;MAAA;MAAAlG,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACvC,OAAO,GAAGiC,IAAI,CAAC4C,UAAU,IAAI5C,IAAI,CAAC6C,WAAW,EAAE;IACjD,CAAC;IAAA;IAAA;MAAAlG,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAIiC,IAAI,CAACxC,IAAI,EAAE;MAAA;MAAAb,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACb,OAAOiC,IAAI,CAACxC,IAAI;IAClB,CAAC;IAAA;IAAA;MAAAb,cAAA,GAAAsB,CAAA;IAAA;;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA,MAAM+E,MAAM,GAAIC,GAAW,IAAK;MAAA;MAAApG,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAC9B,OAAO,iEAAiE,CAACiF,IAAI,CAACD,GAAG,CAAC;IACpF,CAAC;IAAA;IAAApG,cAAA,GAAAoB,CAAA;IAED;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAA+B,IAAI,CAACkB,KAAK;IAAA;IAAA,CAAAvE,cAAA,GAAAsB,CAAA,WAAI,CAAC6E,MAAM,CAAC9C,IAAI,CAACkB,KAAK,CAAC+B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAE;MAAA;MAAAtG,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACnD,OAAOiC,IAAI,CAACkB,KAAK,CAAC+B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IAAA;IAAA;MAAAtG,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAO,QAAQiC,IAAI,CAACc,EAAE,CAACoC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EAC1C,CAAC;;EAED;EAAA;EAAAvG,cAAA,GAAAoB,CAAA;EACA,MAAMsC,OAAO,GAAI8C,IAAY,IAAc;IAAA;IAAAxG,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACzC,OAAO,2BAAApB,cAAA,GAAAsB,CAAA,WAAA+B,IAAI,EAAEoD,KAAK,EAAEC,QAAQ,CAACF,IAAI,CAAC;IAAA;IAAA,CAAAxG,cAAA,GAAAsB,CAAA,WAAI,KAAK;EAC7C,CAAC;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EAED,OACE,0BAAAtB,KAAA;EAAA;EAACsD,WAAW,CAACuD,QAAQ;EAAA;EAAA;IACnBC,KAAK,EAAE;MACLvD,IAAI;MACJC,SAAS;MACTC,iBAAiB;MACjBC,qBAAqB;MACrBC,cAAc;MACdC;IACF,CAAE;IAAAmD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAnH,YAAA;MAAAoH,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEDrD,QACmB,CAAC;AAE3B;;AAEA;AAAA;AAAA5D,cAAA,GAAAoB,CAAA;AACA,OAAO,MAAM8F,OAAO,GAAGA,CAAA,KAAM;EAAA;EAAAlH,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EAAA,OAAA6B,UAAU,CAACG,WAAW,CAAC;AAAD,CAAC", "ignoreList": []}