abe73a7236499cad1c0e88d708776aa4
/**
 * Upcoming Renewals Component
 * 
 * Displays renewals that are due soon with proper empty and loading states.
 * Focused responsibility: Rendering upcoming renewals section only.
 */

'use client';

/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\UpcomingRenewals.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_gl32lnkcx() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\UpcomingRenewals.tsx";
  var hash = "3e8ccb1ae6a97a97d27774b44eb935d98a5e8005";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\UpcomingRenewals.tsx",
    statementMap: {
      "0": {
        start: {
          line: 26,
          column: 22
        },
        end: {
          line: 28,
          column: 3
        }
      },
      "1": {
        start: {
          line: 27,
          column: 4
        },
        end: {
          line: 27,
          column: 22
        }
      },
      "2": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 35,
          column: 3
        }
      },
      "3": {
        start: {
          line: 31,
          column: 4
        },
        end: {
          line: 31,
          column: 50
        }
      },
      "4": {
        start: {
          line: 31,
          column: 19
        },
        end: {
          line: 31,
          column: 50
        }
      },
      "5": {
        start: {
          line: 32,
          column: 4
        },
        end: {
          line: 32,
          column: 57
        }
      },
      "6": {
        start: {
          line: 32,
          column: 20
        },
        end: {
          line: 32,
          column: 57
        }
      },
      "7": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 33,
          column: 57
        }
      },
      "8": {
        start: {
          line: 33,
          column: 20
        },
        end: {
          line: 33,
          column: 57
        }
      },
      "9": {
        start: {
          line: 34,
          column: 4
        },
        end: {
          line: 34,
          column: 37
        }
      },
      "10": {
        start: {
          line: 37,
          column: 25
        },
        end: {
          line: 42,
          column: 3
        }
      },
      "11": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 38,
          column: 34
        }
      },
      "12": {
        start: {
          line: 38,
          column: 18
        },
        end: {
          line: 38,
          column: 34
        }
      },
      "13": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 38
        }
      },
      "14": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 38
        }
      },
      "15": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 40,
          column: 41
        }
      },
      "16": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 41
        }
      },
      "17": {
        start: {
          line: 41,
          column: 4
        },
        end: {
          line: 41,
          column: 32
        }
      },
      "18": {
        start: {
          line: 44,
          column: 2
        },
        end: {
          line: 77,
          column: 3
        }
      },
      "19": {
        start: {
          line: 81,
          column: 2
        },
        end: {
          line: 100,
          column: 3
        }
      },
      "20": {
        start: {
          line: 84,
          column: 8
        },
        end: {
          line: 97,
          column: 14
        }
      },
      "21": {
        start: {
          line: 104,
          column: 2
        },
        end: {
          line: 112,
          column: 3
        }
      },
      "22": {
        start: {
          line: 117,
          column: 16
        },
        end: {
          line: 117,
          column: 26
        }
      },
      "23": {
        start: {
          line: 118,
          column: 14
        },
        end: {
          line: 118,
          column: 31
        }
      },
      "24": {
        start: {
          line: 119,
          column: 19
        },
        end: {
          line: 119,
          column: 50
        }
      },
      "25": {
        start: {
          line: 120,
          column: 2
        },
        end: {
          line: 120,
          column: 52
        }
      },
      "26": {
        start: {
          line: 125,
          column: 14
        },
        end: {
          line: 125,
          column: 24
        }
      },
      "27": {
        start: {
          line: 126,
          column: 2
        },
        end: {
          line: 135,
          column: 4
        }
      },
      "28": {
        start: {
          line: 127,
          column: 4
        },
        end: {
          line: 127,
          column: 39
        }
      },
      "29": {
        start: {
          line: 127,
          column: 27
        },
        end: {
          line: 127,
          column: 39
        }
      },
      "30": {
        start: {
          line: 128,
          column: 20
        },
        end: {
          line: 128,
          column: 46
        }
      },
      "31": {
        start: {
          line: 129,
          column: 25
        },
        end: {
          line: 129,
          column: 49
        }
      },
      "32": {
        start: {
          line: 130,
          column: 4
        },
        end: {
          line: 130,
          column: 62
        }
      },
      "33": {
        start: {
          line: 133,
          column: 4
        },
        end: {
          line: 133,
          column: 44
        }
      },
      "34": {
        start: {
          line: 133,
          column: 36
        },
        end: {
          line: 133,
          column: 44
        }
      },
      "35": {
        start: {
          line: 134,
          column: 4
        },
        end: {
          line: 134,
          column: 74
        }
      },
      "36": {
        start: {
          line: 146,
          column: 27
        },
        end: {
          line: 146,
          column: 71
        }
      },
      "37": {
        start: {
          line: 148,
          column: 2
        },
        end: {
          line: 181,
          column: 3
        }
      },
      "38": {
        start: {
          line: 168,
          column: 14
        },
        end: {
          line: 173,
          column: 16
        }
      }
    },
    fnMap: {
      "0": {
        name: "UpcomingRenewalItem",
        decl: {
          start: {
            line: 25,
            column: 9
          },
          end: {
            line: 25,
            column: 28
          }
        },
        loc: {
          start: {
            line: 25,
            column: 91
          },
          end: {
            line: 78,
            column: 1
          }
        },
        line: 25
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 26,
            column: 23
          }
        },
        loc: {
          start: {
            line: 26,
            column: 28
          },
          end: {
            line: 28,
            column: 3
          }
        },
        line: 26
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 30,
            column: 26
          },
          end: {
            line: 30,
            column: 27
          }
        },
        loc: {
          start: {
            line: 30,
            column: 44
          },
          end: {
            line: 35,
            column: 3
          }
        },
        line: 30
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 37,
            column: 25
          },
          end: {
            line: 37,
            column: 26
          }
        },
        loc: {
          start: {
            line: 37,
            column: 43
          },
          end: {
            line: 42,
            column: 3
          }
        },
        line: 37
      },
      "4": {
        name: "LoadingSkeleton",
        decl: {
          start: {
            line: 80,
            column: 9
          },
          end: {
            line: 80,
            column: 24
          }
        },
        loc: {
          start: {
            line: 80,
            column: 27
          },
          end: {
            line: 101,
            column: 1
          }
        },
        line: 80
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 83,
            column: 25
          },
          end: {
            line: 83,
            column: 26
          }
        },
        loc: {
          start: {
            line: 84,
            column: 8
          },
          end: {
            line: 97,
            column: 14
          }
        },
        line: 84
      },
      "6": {
        name: "EmptyState",
        decl: {
          start: {
            line: 103,
            column: 9
          },
          end: {
            line: 103,
            column: 19
          }
        },
        loc: {
          start: {
            line: 103,
            column: 22
          },
          end: {
            line: 113,
            column: 1
          }
        },
        line: 103
      },
      "7": {
        name: "getDaysUntilDue",
        decl: {
          start: {
            line: 116,
            column: 9
          },
          end: {
            line: 116,
            column: 24
          }
        },
        loc: {
          start: {
            line: 116,
            column: 48
          },
          end: {
            line: 121,
            column: 1
          }
        },
        line: 116
      },
      "8": {
        name: "getUpcomingRenewals",
        decl: {
          start: {
            line: 124,
            column: 9
          },
          end: {
            line: 124,
            column: 28
          }
        },
        loc: {
          start: {
            line: 124,
            column: 84
          },
          end: {
            line: 136,
            column: 1
          }
        },
        line: 124
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 126,
            column: 25
          },
          end: {
            line: 126,
            column: 26
          }
        },
        loc: {
          start: {
            line: 126,
            column: 36
          },
          end: {
            line: 131,
            column: 3
          }
        },
        line: 126
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 131,
            column: 10
          },
          end: {
            line: 131,
            column: 11
          }
        },
        loc: {
          start: {
            line: 131,
            column: 20
          },
          end: {
            line: 135,
            column: 3
          }
        },
        line: 131
      },
      "11": {
        name: "UpcomingRenewals",
        decl: {
          start: {
            line: 138,
            column: 24
          },
          end: {
            line: 138,
            column: 40
          }
        },
        loc: {
          start: {
            line: 145,
            column: 26
          },
          end: {
            line: 182,
            column: 1
          }
        },
        line: 145
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 167,
            column: 34
          },
          end: {
            line: 167,
            column: 35
          }
        },
        loc: {
          start: {
            line: 168,
            column: 14
          },
          end: {
            line: 173,
            column: 16
          }
        },
        line: 168
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 31,
            column: 4
          },
          end: {
            line: 31,
            column: 50
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 4
          },
          end: {
            line: 31,
            column: 50
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "1": {
        loc: {
          start: {
            line: 32,
            column: 4
          },
          end: {
            line: 32,
            column: 57
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 4
          },
          end: {
            line: 32,
            column: 57
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      },
      "2": {
        loc: {
          start: {
            line: 33,
            column: 4
          },
          end: {
            line: 33,
            column: 57
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 4
          },
          end: {
            line: 33,
            column: 57
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "3": {
        loc: {
          start: {
            line: 38,
            column: 4
          },
          end: {
            line: 38,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 4
          },
          end: {
            line: 38,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "4": {
        loc: {
          start: {
            line: 39,
            column: 4
          },
          end: {
            line: 39,
            column: 38
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 4
          },
          end: {
            line: 39,
            column: 38
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "5": {
        loc: {
          start: {
            line: 40,
            column: 4
          },
          end: {
            line: 40,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 4
          },
          end: {
            line: 40,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "6": {
        loc: {
          start: {
            line: 59,
            column: 11
          },
          end: {
            line: 63,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 59,
            column: 11
          },
          end: {
            line: 59,
            column: 30
          }
        }, {
          start: {
            line: 60,
            column: 12
          },
          end: {
            line: 62,
            column: 16
          }
        }],
        line: 59
      },
      "7": {
        loc: {
          start: {
            line: 70,
            column: 9
          },
          end: {
            line: 74,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 70,
            column: 9
          },
          end: {
            line: 70,
            column: 25
          }
        }, {
          start: {
            line: 71,
            column: 10
          },
          end: {
            line: 73,
            column: 14
          }
        }],
        line: 70
      },
      "8": {
        loc: {
          start: {
            line: 127,
            column: 4
          },
          end: {
            line: 127,
            column: 39
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 127,
            column: 4
          },
          end: {
            line: 127,
            column: 39
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 127
      },
      "9": {
        loc: {
          start: {
            line: 130,
            column: 11
          },
          end: {
            line: 130,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 130,
            column: 11
          },
          end: {
            line: 130,
            column: 29
          }
        }, {
          start: {
            line: 130,
            column: 33
          },
          end: {
            line: 130,
            column: 62
          }
        }],
        line: 130
      },
      "10": {
        loc: {
          start: {
            line: 133,
            column: 4
          },
          end: {
            line: 133,
            column: 44
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 133,
            column: 4
          },
          end: {
            line: 133,
            column: 44
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 133
      },
      "11": {
        loc: {
          start: {
            line: 133,
            column: 8
          },
          end: {
            line: 133,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 133,
            column: 8
          },
          end: {
            line: 133,
            column: 19
          }
        }, {
          start: {
            line: 133,
            column: 23
          },
          end: {
            line: 133,
            column: 34
          }
        }],
        line: 133
      },
      "12": {
        loc: {
          start: {
            line: 140,
            column: 2
          },
          end: {
            line: 140,
            column: 19
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 140,
            column: 14
          },
          end: {
            line: 140,
            column: 19
          }
        }],
        line: 140
      },
      "13": {
        loc: {
          start: {
            line: 142,
            column: 2
          },
          end: {
            line: 142,
            column: 20
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 142,
            column: 18
          },
          end: {
            line: 142,
            column: 20
          }
        }],
        line: 142
      },
      "14": {
        loc: {
          start: {
            line: 143,
            column: 2
          },
          end: {
            line: 143,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 143,
            column: 14
          },
          end: {
            line: 143,
            column: 16
          }
        }],
        line: 143
      },
      "15": {
        loc: {
          start: {
            line: 156,
            column: 9
          },
          end: {
            line: 160,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 156,
            column: 9
          },
          end: {
            line: 156,
            column: 36
          }
        }, {
          start: {
            line: 157,
            column: 10
          },
          end: {
            line: 159,
            column: 17
          }
        }],
        line: 156
      },
      "16": {
        loc: {
          start: {
            line: 158,
            column: 46
          },
          end: {
            line: 158,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 158,
            column: 78
          },
          end: {
            line: 158,
            column: 81
          }
        }, {
          start: {
            line: 158,
            column: 84
          },
          end: {
            line: 158,
            column: 86
          }
        }],
        line: 158
      },
      "17": {
        loc: {
          start: {
            line: 163,
            column: 9
          },
          end: {
            line: 178,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 164,
            column: 10
          },
          end: {
            line: 164,
            column: 29
          }
        }, {
          start: {
            line: 165,
            column: 12
          },
          end: {
            line: 178,
            column: 9
          }
        }],
        line: 163
      },
      "18": {
        loc: {
          start: {
            line: 165,
            column: 12
          },
          end: {
            line: 178,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 166,
            column: 10
          },
          end: {
            line: 175,
            column: 16
          }
        }, {
          start: {
            line: 177,
            column: 10
          },
          end: {
            line: 177,
            column: 24
          }
        }],
        line: 165
      },
      "19": {
        loc: {
          start: {
            line: 172,
            column: 30
          },
          end: {
            line: 172,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 172,
            column: 49
          },
          end: {
            line: 172,
            column: 92
          }
        }, {
          start: {
            line: 172,
            column: 95
          },
          end: {
            line: 172,
            column: 96
          }
        }],
        line: 172
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0],
      "13": [0],
      "14": [0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "3e8ccb1ae6a97a97d27774b44eb935d98a5e8005"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_gl32lnkcx = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_gl32lnkcx();
function UpcomingRenewalItem({
  renewal,
  onClick,
  daysUntilDue
}) {
  /* istanbul ignore next */
  cov_gl32lnkcx().f[0]++;
  cov_gl32lnkcx().s[0]++;
  const handleClick = () => {
    /* istanbul ignore next */
    cov_gl32lnkcx().f[1]++;
    cov_gl32lnkcx().s[1]++;
    onClick?.(renewal);
  };
  /* istanbul ignore next */
  cov_gl32lnkcx().s[2]++;
  const getUrgencyColor = days => {
    /* istanbul ignore next */
    cov_gl32lnkcx().f[2]++;
    cov_gl32lnkcx().s[3]++;
    if (days <= 7) {
      /* istanbul ignore next */
      cov_gl32lnkcx().b[0][0]++;
      cov_gl32lnkcx().s[4]++;
      return 'text-red-600 bg-red-50';
    } else
    /* istanbul ignore next */
    {
      cov_gl32lnkcx().b[0][1]++;
    }
    cov_gl32lnkcx().s[5]++;
    if (days <= 14) {
      /* istanbul ignore next */
      cov_gl32lnkcx().b[1][0]++;
      cov_gl32lnkcx().s[6]++;
      return 'text-orange-600 bg-orange-50';
    } else
    /* istanbul ignore next */
    {
      cov_gl32lnkcx().b[1][1]++;
    }
    cov_gl32lnkcx().s[7]++;
    if (days <= 30) {
      /* istanbul ignore next */
      cov_gl32lnkcx().b[2][0]++;
      cov_gl32lnkcx().s[8]++;
      return 'text-yellow-600 bg-yellow-50';
    } else
    /* istanbul ignore next */
    {
      cov_gl32lnkcx().b[2][1]++;
    }
    cov_gl32lnkcx().s[9]++;
    return 'text-blue-600 bg-blue-50';
  };
  /* istanbul ignore next */
  cov_gl32lnkcx().s[10]++;
  const getUrgencyText = days => {
    /* istanbul ignore next */
    cov_gl32lnkcx().f[3]++;
    cov_gl32lnkcx().s[11]++;
    if (days < 0) {
      /* istanbul ignore next */
      cov_gl32lnkcx().b[3][0]++;
      cov_gl32lnkcx().s[12]++;
      return 'Overdue';
    } else
    /* istanbul ignore next */
    {
      cov_gl32lnkcx().b[3][1]++;
    }
    cov_gl32lnkcx().s[13]++;
    if (days === 0) {
      /* istanbul ignore next */
      cov_gl32lnkcx().b[4][0]++;
      cov_gl32lnkcx().s[14]++;
      return 'Due today';
    } else
    /* istanbul ignore next */
    {
      cov_gl32lnkcx().b[4][1]++;
    }
    cov_gl32lnkcx().s[15]++;
    if (days === 1) {
      /* istanbul ignore next */
      cov_gl32lnkcx().b[5][0]++;
      cov_gl32lnkcx().s[16]++;
      return 'Due tomorrow';
    } else
    /* istanbul ignore next */
    {
      cov_gl32lnkcx().b[5][1]++;
    }
    cov_gl32lnkcx().s[17]++;
    return `Due in ${days} days`;
  };
  /* istanbul ignore next */
  cov_gl32lnkcx().s[18]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",
    onClick: handleClick,
    role: "button",
    tabIndex: 0,
    /* istanbul ignore next */
    "aria-label": `View details for ${renewal.name}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 45,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex items-center space-x-3",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 52,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 53,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    className: "text-blue-600",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 54,
      columnNumber: 11
    }
  }, "\uD83D\uDCC4")),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 56,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h3",
  /* istanbul ignore next */
  {
    className: "font-medium text-sm",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 57,
      columnNumber: 11
    }
  }, renewal.name),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-xs text-secondary",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 58,
      columnNumber: 11
    }
  }, renewal.vendor),
  /* istanbul ignore next */
  (cov_gl32lnkcx().b[6][0]++, renewal.annual_cost) &&
  /* istanbul ignore next */
  (cov_gl32lnkcx().b[6][1]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-xs text-secondary",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 60,
      columnNumber: 13
    }
  }, "$", renewal.annual_cost.toLocaleString(), "/year")))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-right",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 66,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    className: `px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(daysUntilDue)}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 67,
      columnNumber: 9
    }
  }, getUrgencyText(daysUntilDue)),
  /* istanbul ignore next */
  (cov_gl32lnkcx().b[7][0]++, renewal.due_date) &&
  /* istanbul ignore next */
  (cov_gl32lnkcx().b[7][1]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-xs text-secondary mt-1",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 71,
      columnNumber: 11
    }
  }, new Date(renewal.due_date).toLocaleDateString()))));
}
function LoadingSkeleton() {
  /* istanbul ignore next */
  cov_gl32lnkcx().f[4]++;
  cov_gl32lnkcx().s[19]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "space-y-4",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 82,
      columnNumber: 5
    }
  }, [...Array(3)].map((_, index) => {
    /* istanbul ignore next */
    cov_gl32lnkcx().f[5]++;
    cov_gl32lnkcx().s[20]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      key: index,
      className: "flex items-center justify-between p-4 border rounded-lg animate-pulse",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 84,
        columnNumber: 9
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "flex items-center space-x-3",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 85,
        columnNumber: 11
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "w-10 h-10 bg-gray-200 rounded-lg",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 86,
        columnNumber: 13
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 87,
        columnNumber: 13
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-4 bg-gray-200 rounded w-32 mb-2",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 88,
        columnNumber: 15
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-3 bg-gray-200 rounded w-24 mb-1",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 89,
        columnNumber: 15
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-3 bg-gray-200 rounded w-20",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 90,
        columnNumber: 15
      }
    }))),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "text-right",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 93,
        columnNumber: 11
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-6 bg-gray-200 rounded-full w-20 mb-1",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 94,
        columnNumber: 13
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-3 bg-gray-200 rounded w-16",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 95,
        columnNumber: 13
      }
    })));
  }));
}
function EmptyState() {
  /* istanbul ignore next */
  cov_gl32lnkcx().f[6]++;
  cov_gl32lnkcx().s[21]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-center py-8",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 105,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-6xl mb-4",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 106,
      columnNumber: 7
    }
  }, "\uD83D\uDCC4"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h3",
  /* istanbul ignore next */
  {
    className: "text-lg font-medium mb-2",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 107,
      columnNumber: 7
    }
  }, "No upcoming renewals"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-secondary",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 108,
      columnNumber: 7
    }
  }, "There are no software renewals due in the next 30 days."));
}

// Helper function to calculate days until due
function getDaysUntilDue(dueDate) {
  /* istanbul ignore next */
  cov_gl32lnkcx().f[7]++;
  const today =
  /* istanbul ignore next */
  (cov_gl32lnkcx().s[22]++, new Date());
  const due =
  /* istanbul ignore next */
  (cov_gl32lnkcx().s[23]++, new Date(dueDate));
  const diffTime =
  /* istanbul ignore next */
  (cov_gl32lnkcx().s[24]++, due.getTime() - today.getTime());
  /* istanbul ignore next */
  cov_gl32lnkcx().s[25]++;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

// Helper function to filter renewals by days threshold
function getUpcomingRenewals(renewals, daysThreshold) {
  /* istanbul ignore next */
  cov_gl32lnkcx().f[8]++;
  const now =
  /* istanbul ignore next */
  (cov_gl32lnkcx().s[26]++, new Date());
  /* istanbul ignore next */
  cov_gl32lnkcx().s[27]++;
  return renewals.filter(renewal => {
    /* istanbul ignore next */
    cov_gl32lnkcx().f[9]++;
    cov_gl32lnkcx().s[28]++;
    if (!renewal.due_date) {
      /* istanbul ignore next */
      cov_gl32lnkcx().b[8][0]++;
      cov_gl32lnkcx().s[29]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_gl32lnkcx().b[8][1]++;
    }
    const dueDate =
    /* istanbul ignore next */
    (cov_gl32lnkcx().s[30]++, new Date(renewal.due_date));
    const daysUntilDue =
    /* istanbul ignore next */
    (cov_gl32lnkcx().s[31]++, getDaysUntilDue(dueDate));
    /* istanbul ignore next */
    cov_gl32lnkcx().s[32]++;
    return /* istanbul ignore next */(cov_gl32lnkcx().b[9][0]++, daysUntilDue >= -7) &&
    /* istanbul ignore next */
    (cov_gl32lnkcx().b[9][1]++, daysUntilDue <= daysThreshold); // Include overdue up to 7 days
  }).sort((a, b) => {
    /* istanbul ignore next */
    cov_gl32lnkcx().f[10]++;
    cov_gl32lnkcx().s[33]++;
    // Sort by due date, earliest first
    if (
    /* istanbul ignore next */
    (cov_gl32lnkcx().b[11][0]++, !a.due_date) ||
    /* istanbul ignore next */
    (cov_gl32lnkcx().b[11][1]++, !b.due_date)) {
      /* istanbul ignore next */
      cov_gl32lnkcx().b[10][0]++;
      cov_gl32lnkcx().s[34]++;
      return 0;
    } else
    /* istanbul ignore next */
    {
      cov_gl32lnkcx().b[10][1]++;
    }
    cov_gl32lnkcx().s[35]++;
    return new Date(a.due_date).getTime() - new Date(b.due_date).getTime();
  });
}
export default function UpcomingRenewals({
  renewals,
  isLoading =
  /* istanbul ignore next */
  (cov_gl32lnkcx().b[12][0]++, false),
  onRenewalClick,
  daysThreshold =
  /* istanbul ignore next */
  (cov_gl32lnkcx().b[13][0]++, 30),
  className =
  /* istanbul ignore next */
  (cov_gl32lnkcx().b[14][0]++, ''),
  'data-testid': testId
}) {
  /* istanbul ignore next */
  cov_gl32lnkcx().f[11]++;
  const upcomingRenewals =
  /* istanbul ignore next */
  (cov_gl32lnkcx().s[36]++, getUpcomingRenewals(renewals, daysThreshold));
  /* istanbul ignore next */
  cov_gl32lnkcx().s[37]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: `card ${className}`,
    /* istanbul ignore next */
    "data-testid": testId,
    style: {
      marginBottom: '24px'
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 149,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "card-header",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 154,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h2",
  /* istanbul ignore next */
  {
    className: "text-lg font-semibold",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 155,
      columnNumber: 9
    }
  }, "Upcoming Renewals"),
  /* istanbul ignore next */
  (cov_gl32lnkcx().b[15][0]++, upcomingRenewals.length > 0) &&
  /* istanbul ignore next */
  (cov_gl32lnkcx().b[15][1]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    className: "text-sm text-secondary",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 157,
      columnNumber: 11
    }
  }, upcomingRenewals.length, " renewal", upcomingRenewals.length !== 1 ?
  /* istanbul ignore next */
  (cov_gl32lnkcx().b[16][0]++, 's') :
  /* istanbul ignore next */
  (cov_gl32lnkcx().b[16][1]++, ''), " due"))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "card-content",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 162,
      columnNumber: 7
    }
  }, isLoading ?
  /* istanbul ignore next */
  (cov_gl32lnkcx().b[17][0]++,
  /* istanbul ignore next */
  __jsx(LoadingSkeleton,
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 164,
      columnNumber: 11
    }
  })) :
  /* istanbul ignore next */
  (cov_gl32lnkcx().b[17][1]++, upcomingRenewals.length > 0 ?
  /* istanbul ignore next */
  (cov_gl32lnkcx().b[18][0]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "space-y-3",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 166,
      columnNumber: 11
    }
  }, upcomingRenewals.map(renewal => {
    /* istanbul ignore next */
    cov_gl32lnkcx().f[12]++;
    cov_gl32lnkcx().s[38]++;
    return /* istanbul ignore next */__jsx(UpcomingRenewalItem,
    /* istanbul ignore next */
    {
      key: renewal.id,
      renewal: renewal,
      onClick: onRenewalClick,
      daysUntilDue: renewal.due_date ?
      /* istanbul ignore next */
      (cov_gl32lnkcx().b[19][0]++, getDaysUntilDue(new Date(renewal.due_date))) :
      /* istanbul ignore next */
      (cov_gl32lnkcx().b[19][1]++, 0),
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 168,
        columnNumber: 15
      }
    });
  }))) :
  /* istanbul ignore next */
  (cov_gl32lnkcx().b[18][1]++,
  /* istanbul ignore next */
  __jsx(EmptyState,
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 177,
      columnNumber: 11
    }
  })))));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfanN4RmlsZU5hbWUiLCJSZWFjdCIsIl9fanN4IiwiY3JlYXRlRWxlbWVudCIsImNvdl9nbDMybG5rY3giLCJwYXRoIiwiaGFzaCIsImdsb2JhbCIsIkZ1bmN0aW9uIiwiZ2N2IiwiY292ZXJhZ2VEYXRhIiwic3RhdGVtZW50TWFwIiwic3RhcnQiLCJsaW5lIiwiY29sdW1uIiwiZW5kIiwiZm5NYXAiLCJuYW1lIiwiZGVjbCIsImxvYyIsImJyYW5jaE1hcCIsInR5cGUiLCJsb2NhdGlvbnMiLCJ1bmRlZmluZWQiLCJzIiwiZiIsImIiLCJfY292ZXJhZ2VTY2hlbWEiLCJjb3ZlcmFnZSIsImFjdHVhbENvdmVyYWdlIiwiVXBjb21pbmdSZW5ld2FsSXRlbSIsInJlbmV3YWwiLCJvbkNsaWNrIiwiZGF5c1VudGlsRHVlIiwiaGFuZGxlQ2xpY2siLCJnZXRVcmdlbmN5Q29sb3IiLCJkYXlzIiwiZ2V0VXJnZW5jeVRleHQiLCJjbGFzc05hbWUiLCJyb2xlIiwidGFiSW5kZXgiLCJfX3NlbGYiLCJfX3NvdXJjZSIsImZpbGVOYW1lIiwibGluZU51bWJlciIsImNvbHVtbk51bWJlciIsInZlbmRvciIsImFubnVhbF9jb3N0IiwidG9Mb2NhbGVTdHJpbmciLCJkdWVfZGF0ZSIsIkRhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJMb2FkaW5nU2tlbGV0b24iLCJBcnJheSIsIm1hcCIsIl8iLCJpbmRleCIsImtleSIsIkVtcHR5U3RhdGUiLCJnZXREYXlzVW50aWxEdWUiLCJkdWVEYXRlIiwidG9kYXkiLCJkdWUiLCJkaWZmVGltZSIsImdldFRpbWUiLCJNYXRoIiwiY2VpbCIsImdldFVwY29taW5nUmVuZXdhbHMiLCJyZW5ld2FscyIsImRheXNUaHJlc2hvbGQiLCJub3ciLCJmaWx0ZXIiLCJzb3J0IiwiYSIsIlVwY29taW5nUmVuZXdhbHMiLCJpc0xvYWRpbmciLCJvblJlbmV3YWxDbGljayIsInRlc3RJZCIsInVwY29taW5nUmVuZXdhbHMiLCJzdHlsZSIsIm1hcmdpbkJvdHRvbSIsImxlbmd0aCIsImlkIl0sInNvdXJjZXMiOlsiVXBjb21pbmdSZW5ld2Fscy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBVcGNvbWluZyBSZW5ld2FscyBDb21wb25lbnRcbiAqIFxuICogRGlzcGxheXMgcmVuZXdhbHMgdGhhdCBhcmUgZHVlIHNvb24gd2l0aCBwcm9wZXIgZW1wdHkgYW5kIGxvYWRpbmcgc3RhdGVzLlxuICogRm9jdXNlZCByZXNwb25zaWJpbGl0eTogUmVuZGVyaW5nIHVwY29taW5nIHJlbmV3YWxzIHNlY3Rpb24gb25seS5cbiAqL1xuXG4ndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgUmVuZXdhbCwgQmFzZUNvbXBvbmVudFByb3BzIH0gZnJvbSAnQC9saWIvdHlwZXMnXG5cbmludGVyZmFjZSBVcGNvbWluZ1JlbmV3YWxzUHJvcHMgZXh0ZW5kcyBCYXNlQ29tcG9uZW50UHJvcHMge1xuICByZW5ld2FsczogUmVuZXdhbFtdXG4gIGlzTG9hZGluZz86IGJvb2xlYW5cbiAgb25SZW5ld2FsQ2xpY2s/OiAocmVuZXdhbDogUmVuZXdhbCkgPT4gdm9pZFxuICBkYXlzVGhyZXNob2xkPzogbnVtYmVyXG59XG5cbmludGVyZmFjZSBVcGNvbWluZ1JlbmV3YWxJdGVtUHJvcHMge1xuICByZW5ld2FsOiBSZW5ld2FsXG4gIG9uQ2xpY2s/OiAocmVuZXdhbDogUmVuZXdhbCkgPT4gdm9pZFxuICBkYXlzVW50aWxEdWU6IG51bWJlclxufVxuXG5mdW5jdGlvbiBVcGNvbWluZ1JlbmV3YWxJdGVtKHsgcmVuZXdhbCwgb25DbGljaywgZGF5c1VudGlsRHVlIH06IFVwY29taW5nUmVuZXdhbEl0ZW1Qcm9wcykge1xuICBjb25zdCBoYW5kbGVDbGljayA9ICgpID0+IHtcbiAgICBvbkNsaWNrPy4ocmVuZXdhbClcbiAgfVxuXG4gIGNvbnN0IGdldFVyZ2VuY3lDb2xvciA9IChkYXlzOiBudW1iZXIpID0+IHtcbiAgICBpZiAoZGF5cyA8PSA3KSByZXR1cm4gJ3RleHQtcmVkLTYwMCBiZy1yZWQtNTAnXG4gICAgaWYgKGRheXMgPD0gMTQpIHJldHVybiAndGV4dC1vcmFuZ2UtNjAwIGJnLW9yYW5nZS01MCdcbiAgICBpZiAoZGF5cyA8PSAzMCkgcmV0dXJuICd0ZXh0LXllbGxvdy02MDAgYmcteWVsbG93LTUwJ1xuICAgIHJldHVybiAndGV4dC1ibHVlLTYwMCBiZy1ibHVlLTUwJ1xuICB9XG5cbiAgY29uc3QgZ2V0VXJnZW5jeVRleHQgPSAoZGF5czogbnVtYmVyKSA9PiB7XG4gICAgaWYgKGRheXMgPCAwKSByZXR1cm4gJ092ZXJkdWUnXG4gICAgaWYgKGRheXMgPT09IDApIHJldHVybiAnRHVlIHRvZGF5J1xuICAgIGlmIChkYXlzID09PSAxKSByZXR1cm4gJ0R1ZSB0b21vcnJvdydcbiAgICByZXR1cm4gYER1ZSBpbiAke2RheXN9IGRheXNgXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgXG4gICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC00IGJvcmRlciByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktNTAgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgb25DbGljaz17aGFuZGxlQ2xpY2t9XG4gICAgICByb2xlPVwiYnV0dG9uXCJcbiAgICAgIHRhYkluZGV4PXswfVxuICAgICAgYXJpYS1sYWJlbD17YFZpZXcgZGV0YWlscyBmb3IgJHtyZW5ld2FsLm5hbWV9YH1cbiAgICA+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1ibHVlLTEwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMFwiPvCfk4Q8L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtXCI+e3JlbmV3YWwubmFtZX08L2gzPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1zZWNvbmRhcnlcIj57cmVuZXdhbC52ZW5kb3J9PC9wPlxuICAgICAgICAgIHtyZW5ld2FsLmFubnVhbF9jb3N0ICYmIChcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1zZWNvbmRhcnlcIj5cbiAgICAgICAgICAgICAgJHtyZW5ld2FsLmFubnVhbF9jb3N0LnRvTG9jYWxlU3RyaW5nKCl9L3llYXJcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSAke2dldFVyZ2VuY3lDb2xvcihkYXlzVW50aWxEdWUpfWB9PlxuICAgICAgICAgIHtnZXRVcmdlbmN5VGV4dChkYXlzVW50aWxEdWUpfVxuICAgICAgICA8L3NwYW4+XG4gICAgICAgIHtyZW5ld2FsLmR1ZV9kYXRlICYmIChcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2Vjb25kYXJ5IG10LTFcIj5cbiAgICAgICAgICAgIHtuZXcgRGF0ZShyZW5ld2FsLmR1ZV9kYXRlKS50b0xvY2FsZURhdGVTdHJpbmcoKX1cbiAgICAgICAgICA8L3A+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG5mdW5jdGlvbiBMb2FkaW5nU2tlbGV0b24oKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgIHtbLi4uQXJyYXkoMyldLm1hcCgoXywgaW5kZXgpID0+IChcbiAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC00IGJvcmRlciByb3VuZGVkLWxnIGFuaW1hdGUtcHVsc2VcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgYmctZ3JheS0yMDAgcm91bmRlZC1sZ1wiPjwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQgYmctZ3JheS0yMDAgcm91bmRlZCB3LTMyIG1iLTJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTMgYmctZ3JheS0yMDAgcm91bmRlZCB3LTI0IG1iLTFcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTMgYmctZ3JheS0yMDAgcm91bmRlZCB3LTIwXCI+PC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC02IGJnLWdyYXktMjAwIHJvdW5kZWQtZnVsbCB3LTIwIG1iLTFcIj48L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0zIGJnLWdyYXktMjAwIHJvdW5kZWQgdy0xNlwiPjwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICkpfVxuICAgIDwvZGl2PlxuICApXG59XG5cbmZ1bmN0aW9uIEVtcHR5U3RhdGUoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtNnhsIG1iLTRcIj7wn5OEPC9kaXY+XG4gICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSBtYi0yXCI+Tm8gdXBjb21pbmcgcmVuZXdhbHM8L2gzPlxuICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zZWNvbmRhcnlcIj5cbiAgICAgICAgVGhlcmUgYXJlIG5vIHNvZnR3YXJlIHJlbmV3YWxzIGR1ZSBpbiB0aGUgbmV4dCAzMCBkYXlzLlxuICAgICAgPC9wPlxuICAgIDwvZGl2PlxuICApXG59XG5cbi8vIEhlbHBlciBmdW5jdGlvbiB0byBjYWxjdWxhdGUgZGF5cyB1bnRpbCBkdWVcbmZ1bmN0aW9uIGdldERheXNVbnRpbER1ZShkdWVEYXRlOiBEYXRlKTogbnVtYmVyIHtcbiAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpXG4gIGNvbnN0IGR1ZSA9IG5ldyBEYXRlKGR1ZURhdGUpXG4gIGNvbnN0IGRpZmZUaW1lID0gZHVlLmdldFRpbWUoKSAtIHRvZGF5LmdldFRpbWUoKVxuICByZXR1cm4gTWF0aC5jZWlsKGRpZmZUaW1lIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpKVxufVxuXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gZmlsdGVyIHJlbmV3YWxzIGJ5IGRheXMgdGhyZXNob2xkXG5mdW5jdGlvbiBnZXRVcGNvbWluZ1JlbmV3YWxzKHJlbmV3YWxzOiBSZW5ld2FsW10sIGRheXNUaHJlc2hvbGQ6IG51bWJlcik6IFJlbmV3YWxbXSB7XG4gIGNvbnN0IG5vdyA9IG5ldyBEYXRlKClcbiAgcmV0dXJuIHJlbmV3YWxzLmZpbHRlcihyZW5ld2FsID0+IHtcbiAgICBpZiAoIXJlbmV3YWwuZHVlX2RhdGUpIHJldHVybiBmYWxzZVxuICAgIGNvbnN0IGR1ZURhdGUgPSBuZXcgRGF0ZShyZW5ld2FsLmR1ZV9kYXRlKVxuICAgIGNvbnN0IGRheXNVbnRpbER1ZSA9IGdldERheXNVbnRpbER1ZShkdWVEYXRlKVxuICAgIHJldHVybiBkYXlzVW50aWxEdWUgPj0gLTcgJiYgZGF5c1VudGlsRHVlIDw9IGRheXNUaHJlc2hvbGQgLy8gSW5jbHVkZSBvdmVyZHVlIHVwIHRvIDcgZGF5c1xuICB9KS5zb3J0KChhLCBiKSA9PiB7XG4gICAgLy8gU29ydCBieSBkdWUgZGF0ZSwgZWFybGllc3QgZmlyc3RcbiAgICBpZiAoIWEuZHVlX2RhdGUgfHwgIWIuZHVlX2RhdGUpIHJldHVybiAwXG4gICAgcmV0dXJuIG5ldyBEYXRlKGEuZHVlX2RhdGUpLmdldFRpbWUoKSAtIG5ldyBEYXRlKGIuZHVlX2RhdGUpLmdldFRpbWUoKVxuICB9KVxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBVcGNvbWluZ1JlbmV3YWxzKHtcbiAgcmVuZXdhbHMsXG4gIGlzTG9hZGluZyA9IGZhbHNlLFxuICBvblJlbmV3YWxDbGljayxcbiAgZGF5c1RocmVzaG9sZCA9IDMwLFxuICBjbGFzc05hbWUgPSAnJyxcbiAgJ2RhdGEtdGVzdGlkJzogdGVzdElkXG59OiBVcGNvbWluZ1JlbmV3YWxzUHJvcHMpIHtcbiAgY29uc3QgdXBjb21pbmdSZW5ld2FscyA9IGdldFVwY29taW5nUmVuZXdhbHMocmVuZXdhbHMsIGRheXNUaHJlc2hvbGQpXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IFxuICAgICAgY2xhc3NOYW1lPXtgY2FyZCAke2NsYXNzTmFtZX1gfVxuICAgICAgZGF0YS10ZXN0aWQ9e3Rlc3RJZH1cbiAgICAgIHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogJzI0cHgnIH19XG4gICAgPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkLWhlYWRlclwiPlxuICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkXCI+VXBjb21pbmcgUmVuZXdhbHM8L2gyPlxuICAgICAgICB7dXBjb21pbmdSZW5ld2Fscy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtc2Vjb25kYXJ5XCI+XG4gICAgICAgICAgICB7dXBjb21pbmdSZW5ld2Fscy5sZW5ndGh9IHJlbmV3YWx7dXBjb21pbmdSZW5ld2Fscy5sZW5ndGggIT09IDEgPyAncycgOiAnJ30gZHVlXG4gICAgICAgICAgPC9zcGFuPlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtY29udGVudFwiPlxuICAgICAgICB7aXNMb2FkaW5nID8gKFxuICAgICAgICAgIDxMb2FkaW5nU2tlbGV0b24gLz5cbiAgICAgICAgKSA6IHVwY29taW5nUmVuZXdhbHMubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAge3VwY29taW5nUmVuZXdhbHMubWFwKChyZW5ld2FsKSA9PiAoXG4gICAgICAgICAgICAgIDxVcGNvbWluZ1JlbmV3YWxJdGVtXG4gICAgICAgICAgICAgICAga2V5PXtyZW5ld2FsLmlkfVxuICAgICAgICAgICAgICAgIHJlbmV3YWw9e3JlbmV3YWx9XG4gICAgICAgICAgICAgICAgb25DbGljaz17b25SZW5ld2FsQ2xpY2t9XG4gICAgICAgICAgICAgICAgZGF5c1VudGlsRHVlPXtyZW5ld2FsLmR1ZV9kYXRlID8gZ2V0RGF5c1VudGlsRHVlKG5ldyBEYXRlKHJlbmV3YWwuZHVlX2RhdGUpKSA6IDB9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8RW1wdHlTdGF0ZSAvPlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLFlBQVk7O0FBQUE7QUFBQSxJQUFBQSxZQUFBO0FBQUEsT0FBQUMsS0FBQTtBQUFBLElBQUFDLEtBQUEsR0FBQUQsS0FBQSxDQUFBRSxhQUFBO0FBQUEsU0FBQUMsY0FBQTtFQUFBLElBQUFDLElBQUE7RUFBQSxJQUFBQyxJQUFBO0VBQUEsSUFBQUMsTUFBQSxPQUFBQyxRQUFBO0VBQUEsSUFBQUMsR0FBQTtFQUFBLElBQUFDLFlBQUE7SUFBQUwsSUFBQTtJQUFBTSxZQUFBO01BQUE7UUFBQUMsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO0lBQUE7SUFBQUUsS0FBQTtNQUFBO1FBQUFDLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtJQUFBO0lBQUFPLFNBQUE7TUFBQTtRQUFBRCxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtJQUFBO0lBQUFXLENBQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7SUFBQTtJQUFBQyxDQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7SUFBQTtJQUFBQyxDQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtJQUFBO0lBQUFDLGVBQUE7SUFBQXJCLElBQUE7RUFBQTtFQUFBLElBQUFzQixRQUFBLEdBQUFyQixNQUFBLENBQUFFLEdBQUEsTUFBQUYsTUFBQSxDQUFBRSxHQUFBO0VBQUEsS0FBQW1CLFFBQUEsQ0FBQXZCLElBQUEsS0FBQXVCLFFBQUEsQ0FBQXZCLElBQUEsRUFBQUMsSUFBQSxLQUFBQSxJQUFBO0lBQUFzQixRQUFBLENBQUF2QixJQUFBLElBQUFLLFlBQUE7RUFBQTtFQUFBLElBQUFtQixjQUFBLEdBQUFELFFBQUEsQ0FBQXZCLElBQUE7RUFBQTtJQVFBO0lBQUFELGFBQUEsWUFBQUEsQ0FBQTtNQUFBLE9BQUF5QixjQUFBO0lBQUE7RUFBQTtFQUFBLE9BQUFBLGNBQUE7QUFBQTtBQUFBekIsYUFBQTtBQVNaLFNBQVMwQixtQkFBbUJBLENBQUM7RUFBRUMsT0FBTztFQUFFQyxPQUFPO0VBQUVDO0FBQXVDLENBQUMsRUFBRTtFQUFBO0VBQUE3QixhQUFBLEdBQUFxQixDQUFBO0VBQUFyQixhQUFBLEdBQUFvQixDQUFBO0VBQ3pGLE1BQU1VLFdBQVcsR0FBR0EsQ0FBQSxLQUFNO0lBQUE7SUFBQTlCLGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFDeEJRLE9BQU8sR0FBR0QsT0FBTyxDQUFDO0VBQ3BCLENBQUM7RUFBQTtFQUFBM0IsYUFBQSxHQUFBb0IsQ0FBQTtFQUVELE1BQU1XLGVBQWUsR0FBSUMsSUFBWSxJQUFLO0lBQUE7SUFBQWhDLGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFDeEMsSUFBSVksSUFBSSxJQUFJLENBQUMsRUFBRTtNQUFBO01BQUFoQyxhQUFBLEdBQUFzQixDQUFBO01BQUF0QixhQUFBLEdBQUFvQixDQUFBO01BQUEsT0FBTyx3QkFBd0I7SUFBRCxDQUFDO0lBQUE7SUFBQTtNQUFBcEIsYUFBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUF0QixhQUFBLEdBQUFvQixDQUFBO0lBQzlDLElBQUlZLElBQUksSUFBSSxFQUFFLEVBQUU7TUFBQTtNQUFBaEMsYUFBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtNQUFBLE9BQU8sOEJBQThCO0lBQUQsQ0FBQztJQUFBO0lBQUE7TUFBQXBCLGFBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtJQUNyRCxJQUFJWSxJQUFJLElBQUksRUFBRSxFQUFFO01BQUE7TUFBQWhDLGFBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7TUFBQSxPQUFPLDhCQUE4QjtJQUFELENBQUM7SUFBQTtJQUFBO01BQUFwQixhQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7SUFDckQsT0FBTywwQkFBMEI7RUFDbkMsQ0FBQztFQUFBO0VBQUFwQixhQUFBLEdBQUFvQixDQUFBO0VBRUQsTUFBTWEsY0FBYyxHQUFJRCxJQUFZLElBQUs7SUFBQTtJQUFBaEMsYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUN2QyxJQUFJWSxJQUFJLEdBQUcsQ0FBQyxFQUFFO01BQUE7TUFBQWhDLGFBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7TUFBQSxPQUFPLFNBQVM7SUFBRCxDQUFDO0lBQUE7SUFBQTtNQUFBcEIsYUFBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUF0QixhQUFBLEdBQUFvQixDQUFBO0lBQzlCLElBQUlZLElBQUksS0FBSyxDQUFDLEVBQUU7TUFBQTtNQUFBaEMsYUFBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtNQUFBLE9BQU8sV0FBVztJQUFELENBQUM7SUFBQTtJQUFBO01BQUFwQixhQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7SUFDbEMsSUFBSVksSUFBSSxLQUFLLENBQUMsRUFBRTtNQUFBO01BQUFoQyxhQUFBLEdBQUFzQixDQUFBO01BQUF0QixhQUFBLEdBQUFvQixDQUFBO01BQUEsT0FBTyxjQUFjO0lBQUQsQ0FBQztJQUFBO0lBQUE7TUFBQXBCLGFBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtJQUNyQyxPQUFPLFVBQVVZLElBQUksT0FBTztFQUM5QixDQUFDO0VBQUE7RUFBQWhDLGFBQUEsR0FBQW9CLENBQUE7RUFFRCxPQUNFLDBCQUFBdEIsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQ0VvQyxTQUFTLEVBQUMsMkdBQTJHO0lBQ3JITixPQUFPLEVBQUVFLFdBQVk7SUFDckJLLElBQUksRUFBQyxRQUFRO0lBQ2JDLFFBQVEsRUFBRSxDQUFFO0lBQ1o7SUFBQSxjQUFZLG9CQUFvQlQsT0FBTyxDQUFDZCxJQUFJLEVBQUc7SUFBQXdCLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUEzQyxZQUFBO01BQUE0QyxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBO0VBRS9DO0VBQUEzQyxLQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFBS29DLFNBQVMsRUFBQyw2QkFBNkI7SUFBQUcsTUFBQTtJQUFBQyxRQUFBO01BQUFDLFFBQUEsRUFBQTNDLFlBQUE7TUFBQTRDLFVBQUE7TUFBQUMsWUFBQTtJQUFBO0VBQUE7RUFDMUM7RUFBQTNDLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUFLb0MsU0FBUyxFQUFDLG1FQUFtRTtJQUFBRyxNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBM0MsWUFBQTtNQUFBNEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQTtFQUNoRjtFQUFBM0MsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQU1vQyxTQUFTLEVBQUMsZUFBZTtJQUFBRyxNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBM0MsWUFBQTtNQUFBNEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQSxHQUFDLGNBQVEsQ0FDckMsQ0FBQztFQUNOO0VBQUEzQyxLQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFBQXVDLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUEzQyxZQUFBO01BQUE0QyxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBO0VBQ0U7RUFBQTNDLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUFJb0MsU0FBUyxFQUFDLHFCQUFxQjtJQUFBRyxNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBM0MsWUFBQTtNQUFBNEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQSxHQUFFZCxPQUFPLENBQUNkLElBQVMsQ0FBQztFQUN2RDtFQUFBZixLQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFBR29DLFNBQVMsRUFBQyx3QkFBd0I7SUFBQUcsTUFBQTtJQUFBQyxRQUFBO01BQUFDLFFBQUEsRUFBQTNDLFlBQUE7TUFBQTRDLFVBQUE7TUFBQUMsWUFBQTtJQUFBO0VBQUEsR0FBRWQsT0FBTyxDQUFDZSxNQUFVLENBQUM7RUFDekQ7RUFBQSxDQUFBMUMsYUFBQSxHQUFBc0IsQ0FBQSxVQUFBSyxPQUFPLENBQUNnQixXQUFXO0VBQUE7RUFBQSxDQUFBM0MsYUFBQSxHQUFBc0IsQ0FBQTtFQUNsQjtFQUFBeEIsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQUdvQyxTQUFTLEVBQUMsd0JBQXdCO0lBQUFHLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUEzQyxZQUFBO01BQUE0QyxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBLEdBQUMsR0FDbkMsRUFBQ2QsT0FBTyxDQUFDZ0IsV0FBVyxDQUFDQyxjQUFjLENBQUMsQ0FBQyxFQUFDLE9BQ3RDLENBQUMsQ0FFSCxDQUNGLENBQUM7RUFDTjtFQUFBOUMsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQUtvQyxTQUFTLEVBQUMsWUFBWTtJQUFBRyxNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBM0MsWUFBQTtNQUFBNEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQTtFQUN6QjtFQUFBM0MsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQU1vQyxTQUFTLEVBQUUsOENBQThDSCxlQUFlLENBQUNGLFlBQVksQ0FBQyxFQUFHO0lBQUFRLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUEzQyxZQUFBO01BQUE0QyxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBLEdBQzVGUixjQUFjLENBQUNKLFlBQVksQ0FDeEIsQ0FBQztFQUNOO0VBQUEsQ0FBQTdCLGFBQUEsR0FBQXNCLENBQUEsVUFBQUssT0FBTyxDQUFDa0IsUUFBUTtFQUFBO0VBQUEsQ0FBQTdDLGFBQUEsR0FBQXNCLENBQUE7RUFDZjtFQUFBeEIsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQUdvQyxTQUFTLEVBQUMsNkJBQTZCO0lBQUFHLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUEzQyxZQUFBO01BQUE0QyxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBLEdBQ3ZDLElBQUlLLElBQUksQ0FBQ25CLE9BQU8sQ0FBQ2tCLFFBQVEsQ0FBQyxDQUFDRSxrQkFBa0IsQ0FBQyxDQUM5QyxDQUFDLENBRUgsQ0FDRixDQUFDO0FBRVY7QUFFQSxTQUFTQyxlQUFlQSxDQUFBLEVBQUc7RUFBQTtFQUFBaEQsYUFBQSxHQUFBcUIsQ0FBQTtFQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtFQUN6QixPQUNFLDBCQUFBdEIsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQUtvQyxTQUFTLEVBQUMsV0FBVztJQUFBRyxNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBM0MsWUFBQTtNQUFBNEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQSxHQUN2QixDQUFDLEdBQUdRLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDQyxHQUFHLENBQUMsQ0FBQ0MsQ0FBQyxFQUFFQyxLQUFLLEtBQzFCO0lBQUE7SUFBQXBELGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFBQSxpQ0FBQXRCLEtBQUE7SUFBQTtJQUFBO0lBQUE7SUFBQTtNQUFLdUQsR0FBRyxFQUFFRCxLQUFNO01BQUNsQixTQUFTLEVBQUMsdUVBQXVFO01BQUFHLE1BQUE7TUFBQUMsUUFBQTtRQUFBQyxRQUFBLEVBQUEzQyxZQUFBO1FBQUE0QyxVQUFBO1FBQUFDLFlBQUE7TUFBQTtJQUFBO0lBQ2hHO0lBQUEzQyxLQUFBO0lBQUE7SUFBQTtJQUFBO0lBQUE7TUFBS29DLFNBQVMsRUFBQyw2QkFBNkI7TUFBQUcsTUFBQTtNQUFBQyxRQUFBO1FBQUFDLFFBQUEsRUFBQTNDLFlBQUE7UUFBQTRDLFVBQUE7UUFBQUMsWUFBQTtNQUFBO0lBQUE7SUFDMUM7SUFBQTNDLEtBQUE7SUFBQTtJQUFBO0lBQUE7SUFBQTtNQUFLb0MsU0FBUyxFQUFDLGtDQUFrQztNQUFBRyxNQUFBO01BQUFDLFFBQUE7UUFBQUMsUUFBQSxFQUFBM0MsWUFBQTtRQUFBNEMsVUFBQTtRQUFBQyxZQUFBO01BQUE7SUFBQSxDQUFNLENBQUM7SUFDeEQ7SUFBQTNDLEtBQUE7SUFBQTtJQUFBO0lBQUE7SUFBQTtNQUFBdUMsTUFBQTtNQUFBQyxRQUFBO1FBQUFDLFFBQUEsRUFBQTNDLFlBQUE7UUFBQTRDLFVBQUE7UUFBQUMsWUFBQTtNQUFBO0lBQUE7SUFDRTtJQUFBM0MsS0FBQTtJQUFBO0lBQUE7SUFBQTtJQUFBO01BQUtvQyxTQUFTLEVBQUMsbUNBQW1DO01BQUFHLE1BQUE7TUFBQUMsUUFBQTtRQUFBQyxRQUFBLEVBQUEzQyxZQUFBO1FBQUE0QyxVQUFBO1FBQUFDLFlBQUE7TUFBQTtJQUFBLENBQU0sQ0FBQztJQUN6RDtJQUFBM0MsS0FBQTtJQUFBO0lBQUE7SUFBQTtJQUFBO01BQUtvQyxTQUFTLEVBQUMsbUNBQW1DO01BQUFHLE1BQUE7TUFBQUMsUUFBQTtRQUFBQyxRQUFBLEVBQUEzQyxZQUFBO1FBQUE0QyxVQUFBO1FBQUFDLFlBQUE7TUFBQTtJQUFBLENBQU0sQ0FBQztJQUN6RDtJQUFBM0MsS0FBQTtJQUFBO0lBQUE7SUFBQTtJQUFBO01BQUtvQyxTQUFTLEVBQUMsOEJBQThCO01BQUFHLE1BQUE7TUFBQUMsUUFBQTtRQUFBQyxRQUFBLEVBQUEzQyxZQUFBO1FBQUE0QyxVQUFBO1FBQUFDLFlBQUE7TUFBQTtJQUFBLENBQU0sQ0FDaEQsQ0FDRixDQUFDO0lBQ047SUFBQTNDLEtBQUE7SUFBQTtJQUFBO0lBQUE7SUFBQTtNQUFLb0MsU0FBUyxFQUFDLFlBQVk7TUFBQUcsTUFBQTtNQUFBQyxRQUFBO1FBQUFDLFFBQUEsRUFBQTNDLFlBQUE7UUFBQTRDLFVBQUE7UUFBQUMsWUFBQTtNQUFBO0lBQUE7SUFDekI7SUFBQTNDLEtBQUE7SUFBQTtJQUFBO0lBQUE7SUFBQTtNQUFLb0MsU0FBUyxFQUFDLHdDQUF3QztNQUFBRyxNQUFBO01BQUFDLFFBQUE7UUFBQUMsUUFBQSxFQUFBM0MsWUFBQTtRQUFBNEMsVUFBQTtRQUFBQyxZQUFBO01BQUE7SUFBQSxDQUFNLENBQUM7SUFDOUQ7SUFBQTNDLEtBQUE7SUFBQTtJQUFBO0lBQUE7SUFBQTtNQUFLb0MsU0FBUyxFQUFDLDhCQUE4QjtNQUFBRyxNQUFBO01BQUFDLFFBQUE7UUFBQUMsUUFBQSxFQUFBM0MsWUFBQTtRQUFBNEMsVUFBQTtRQUFBQyxZQUFBO01BQUE7SUFBQSxDQUFNLENBQ2hELENBQ0YsQ0FBQztFQUFELENBQ04sQ0FDRSxDQUFDO0FBRVY7QUFFQSxTQUFTYSxVQUFVQSxDQUFBLEVBQUc7RUFBQTtFQUFBdEQsYUFBQSxHQUFBcUIsQ0FBQTtFQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtFQUNwQixPQUNFLDBCQUFBdEIsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQUtvQyxTQUFTLEVBQUMsa0JBQWtCO0lBQUFHLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUEzQyxZQUFBO01BQUE0QyxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBO0VBQy9CO0VBQUEzQyxLQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFBS29DLFNBQVMsRUFBQyxlQUFlO0lBQUFHLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUEzQyxZQUFBO01BQUE0QyxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBLEdBQUMsY0FBTyxDQUFDO0VBQ3ZDO0VBQUEzQyxLQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFBSW9DLFNBQVMsRUFBQywwQkFBMEI7SUFBQUcsTUFBQTtJQUFBQyxRQUFBO01BQUFDLFFBQUEsRUFBQTNDLFlBQUE7TUFBQTRDLFVBQUE7TUFBQUMsWUFBQTtJQUFBO0VBQUEsR0FBQyxzQkFBd0IsQ0FBQztFQUNsRTtFQUFBM0MsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQUdvQyxTQUFTLEVBQUMsZ0JBQWdCO0lBQUFHLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUEzQyxZQUFBO01BQUE0QyxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBLEdBQUMseURBRTNCLENBQ0EsQ0FBQztBQUVWOztBQUVBO0FBQ0EsU0FBU2MsZUFBZUEsQ0FBQ0MsT0FBYSxFQUFVO0VBQUE7RUFBQXhELGFBQUEsR0FBQXFCLENBQUE7RUFDOUMsTUFBTW9DLEtBQUs7RUFBQTtFQUFBLENBQUF6RCxhQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSTBCLElBQUksQ0FBQyxDQUFDO0VBQ3hCLE1BQU1ZLEdBQUc7RUFBQTtFQUFBLENBQUExRCxhQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSTBCLElBQUksQ0FBQ1UsT0FBTyxDQUFDO0VBQzdCLE1BQU1HLFFBQVE7RUFBQTtFQUFBLENBQUEzRCxhQUFBLEdBQUFvQixDQUFBLFFBQUdzQyxHQUFHLENBQUNFLE9BQU8sQ0FBQyxDQUFDLEdBQUdILEtBQUssQ0FBQ0csT0FBTyxDQUFDLENBQUM7RUFBQTtFQUFBNUQsYUFBQSxHQUFBb0IsQ0FBQTtFQUNoRCxPQUFPeUMsSUFBSSxDQUFDQyxJQUFJLENBQUNILFFBQVEsSUFBSSxJQUFJLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLENBQUMsQ0FBQztBQUNwRDs7QUFFQTtBQUNBLFNBQVNJLG1CQUFtQkEsQ0FBQ0MsUUFBbUIsRUFBRUMsYUFBcUIsRUFBYTtFQUFBO0VBQUFqRSxhQUFBLEdBQUFxQixDQUFBO0VBQ2xGLE1BQU02QyxHQUFHO0VBQUE7RUFBQSxDQUFBbEUsYUFBQSxHQUFBb0IsQ0FBQSxRQUFHLElBQUkwQixJQUFJLENBQUMsQ0FBQztFQUFBO0VBQUE5QyxhQUFBLEdBQUFvQixDQUFBO0VBQ3RCLE9BQU80QyxRQUFRLENBQUNHLE1BQU0sQ0FBQ3hDLE9BQU8sSUFBSTtJQUFBO0lBQUEzQixhQUFBLEdBQUFxQixDQUFBO0lBQUFyQixhQUFBLEdBQUFvQixDQUFBO0lBQ2hDLElBQUksQ0FBQ08sT0FBTyxDQUFDa0IsUUFBUSxFQUFFO01BQUE7TUFBQTdDLGFBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7TUFBQSxPQUFPLEtBQUs7SUFBRCxDQUFDO0lBQUE7SUFBQTtNQUFBcEIsYUFBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQ25DLE1BQU1rQyxPQUFPO0lBQUE7SUFBQSxDQUFBeEQsYUFBQSxHQUFBb0IsQ0FBQSxRQUFHLElBQUkwQixJQUFJLENBQUNuQixPQUFPLENBQUNrQixRQUFRLENBQUM7SUFDMUMsTUFBTWhCLFlBQVk7SUFBQTtJQUFBLENBQUE3QixhQUFBLEdBQUFvQixDQUFBLFFBQUdtQyxlQUFlLENBQUNDLE9BQU8sQ0FBQztJQUFBO0lBQUF4RCxhQUFBLEdBQUFvQixDQUFBO0lBQzdDLE9BQU8sMkJBQUFwQixhQUFBLEdBQUFzQixDQUFBLFVBQUFPLFlBQVksSUFBSSxDQUFDLENBQUM7SUFBQTtJQUFBLENBQUE3QixhQUFBLEdBQUFzQixDQUFBLFVBQUlPLFlBQVksSUFBSW9DLGFBQWEsR0FBQztFQUM3RCxDQUFDLENBQUMsQ0FBQ0csSUFBSSxDQUFDLENBQUNDLENBQUMsRUFBRS9DLENBQUMsS0FBSztJQUFBO0lBQUF0QixhQUFBLEdBQUFxQixDQUFBO0lBQUFyQixhQUFBLEdBQUFvQixDQUFBO0lBQ2hCO0lBQ0E7SUFBSTtJQUFBLENBQUFwQixhQUFBLEdBQUFzQixDQUFBLFlBQUMrQyxDQUFDLENBQUN4QixRQUFRO0lBQUE7SUFBQSxDQUFBN0MsYUFBQSxHQUFBc0IsQ0FBQSxXQUFJLENBQUNBLENBQUMsQ0FBQ3VCLFFBQVEsR0FBRTtNQUFBO01BQUE3QyxhQUFBLEdBQUFzQixDQUFBO01BQUF0QixhQUFBLEdBQUFvQixDQUFBO01BQUEsT0FBTyxDQUFDO0lBQUQsQ0FBQztJQUFBO0lBQUE7TUFBQXBCLGFBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtJQUN4QyxPQUFPLElBQUkwQixJQUFJLENBQUN1QixDQUFDLENBQUN4QixRQUFRLENBQUMsQ0FBQ2UsT0FBTyxDQUFDLENBQUMsR0FBRyxJQUFJZCxJQUFJLENBQUN4QixDQUFDLENBQUN1QixRQUFRLENBQUMsQ0FBQ2UsT0FBTyxDQUFDLENBQUM7RUFDeEUsQ0FBQyxDQUFDO0FBQ0o7QUFFQSxlQUFlLFNBQVNVLGdCQUFnQkEsQ0FBQztFQUN2Q04sUUFBUTtFQUNSTyxTQUFTO0VBQUE7RUFBQSxDQUFBdkUsYUFBQSxHQUFBc0IsQ0FBQSxXQUFHLEtBQUs7RUFDakJrRCxjQUFjO0VBQ2RQLGFBQWE7RUFBQTtFQUFBLENBQUFqRSxhQUFBLEdBQUFzQixDQUFBLFdBQUcsRUFBRTtFQUNsQlksU0FBUztFQUFBO0VBQUEsQ0FBQWxDLGFBQUEsR0FBQXNCLENBQUEsV0FBRyxFQUFFO0VBQ2QsYUFBYSxFQUFFbUQ7QUFDTSxDQUFDLEVBQUU7RUFBQTtFQUFBekUsYUFBQSxHQUFBcUIsQ0FBQTtFQUN4QixNQUFNcUQsZ0JBQWdCO0VBQUE7RUFBQSxDQUFBMUUsYUFBQSxHQUFBb0IsQ0FBQSxRQUFHMkMsbUJBQW1CLENBQUNDLFFBQVEsRUFBRUMsYUFBYSxDQUFDO0VBQUE7RUFBQWpFLGFBQUEsR0FBQW9CLENBQUE7RUFFckUsT0FDRSwwQkFBQXRCLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUNFb0MsU0FBUyxFQUFFLFFBQVFBLFNBQVMsRUFBRztJQUMvQjtJQUFBLGVBQWF1QyxNQUFPO0lBQ3BCRSxLQUFLLEVBQUU7TUFBRUMsWUFBWSxFQUFFO0lBQU8sQ0FBRTtJQUFBdkMsTUFBQTtJQUFBQyxRQUFBO01BQUFDLFFBQUEsRUFBQTNDLFlBQUE7TUFBQTRDLFVBQUE7TUFBQUMsWUFBQTtJQUFBO0VBQUE7RUFFaEM7RUFBQTNDLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUFLb0MsU0FBUyxFQUFDLGFBQWE7SUFBQUcsTUFBQTtJQUFBQyxRQUFBO01BQUFDLFFBQUEsRUFBQTNDLFlBQUE7TUFBQTRDLFVBQUE7TUFBQUMsWUFBQTtJQUFBO0VBQUE7RUFDMUI7RUFBQTNDLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUFJb0MsU0FBUyxFQUFDLHVCQUF1QjtJQUFBRyxNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBM0MsWUFBQTtNQUFBNEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQSxHQUFDLG1CQUFxQixDQUFDO0VBQzNEO0VBQUEsQ0FBQXpDLGFBQUEsR0FBQXNCLENBQUEsV0FBQW9ELGdCQUFnQixDQUFDRyxNQUFNLEdBQUcsQ0FBQztFQUFBO0VBQUEsQ0FBQTdFLGFBQUEsR0FBQXNCLENBQUE7RUFDMUI7RUFBQXhCLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUFNb0MsU0FBUyxFQUFDLHdCQUF3QjtJQUFBRyxNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBM0MsWUFBQTtNQUFBNEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQSxHQUNyQ2lDLGdCQUFnQixDQUFDRyxNQUFNLEVBQUMsVUFBUSxFQUFDSCxnQkFBZ0IsQ0FBQ0csTUFBTSxLQUFLLENBQUM7RUFBQTtFQUFBLENBQUE3RSxhQUFBLEdBQUFzQixDQUFBLFdBQUcsR0FBRztFQUFBO0VBQUEsQ0FBQXRCLGFBQUEsR0FBQXNCLENBQUEsV0FBRyxFQUFFLEdBQUMsTUFDdkUsQ0FBQyxDQUVOLENBQUM7RUFDTjtFQUFBeEIsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQUtvQyxTQUFTLEVBQUMsY0FBYztJQUFBRyxNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBM0MsWUFBQTtNQUFBNEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQSxHQUMxQjhCLFNBQVM7RUFBQTtFQUFBLENBQUF2RSxhQUFBLEdBQUFzQixDQUFBO0VBQ1I7RUFBQXhCLEtBQUEsQ0FBQ2tELGVBQWU7RUFBQTtFQUFBO0lBQUFYLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUEzQyxZQUFBO01BQUE0QyxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBLENBQUUsQ0FBQztFQUFBO0VBQUEsQ0FBQXpDLGFBQUEsR0FBQXNCLENBQUEsV0FDakJvRCxnQkFBZ0IsQ0FBQ0csTUFBTSxHQUFHLENBQUM7RUFBQTtFQUFBLENBQUE3RSxhQUFBLEdBQUFzQixDQUFBO0VBQzdCO0VBQUF4QixLQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFBS29DLFNBQVMsRUFBQyxXQUFXO0lBQUFHLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUEzQyxZQUFBO01BQUE0QyxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBLEdBQ3ZCaUMsZ0JBQWdCLENBQUN4QixHQUFHLENBQUV2QixPQUFPLElBQzVCO0lBQUE7SUFBQTNCLGFBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7SUFBQSxpQ0FBQXRCLEtBQUEsQ0FBQzRCLG1CQUFtQjtJQUFBO0lBQUE7TUFDbEIyQixHQUFHLEVBQUUxQixPQUFPLENBQUNtRCxFQUFHO01BQ2hCbkQsT0FBTyxFQUFFQSxPQUFRO01BQ2pCQyxPQUFPLEVBQUU0QyxjQUFlO01BQ3hCM0MsWUFBWSxFQUFFRixPQUFPLENBQUNrQixRQUFRO01BQUE7TUFBQSxDQUFBN0MsYUFBQSxHQUFBc0IsQ0FBQSxXQUFHaUMsZUFBZSxDQUFDLElBQUlULElBQUksQ0FBQ25CLE9BQU8sQ0FBQ2tCLFFBQVEsQ0FBQyxDQUFDO01BQUE7TUFBQSxDQUFBN0MsYUFBQSxHQUFBc0IsQ0FBQSxXQUFHLENBQUMsQ0FBQztNQUFBZSxNQUFBO01BQUFDLFFBQUE7UUFBQUMsUUFBQSxFQUFBM0MsWUFBQTtRQUFBNEMsVUFBQTtRQUFBQyxZQUFBO01BQUE7SUFBQSxDQUNsRixDQUFDO0VBQUQsQ0FDRixDQUNFLENBQUM7RUFBQTtFQUFBLENBQUF6QyxhQUFBLEdBQUFzQixDQUFBO0VBRU47RUFBQXhCLEtBQUEsQ0FBQ3dELFVBQVU7RUFBQTtFQUFBO0lBQUFqQixNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBM0MsWUFBQTtNQUFBNEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQSxDQUFFLENBQUMsQ0FDZixDQUNFLENBQ0YsQ0FBQztBQUVWIiwiaWdub3JlTGlzdCI6W119