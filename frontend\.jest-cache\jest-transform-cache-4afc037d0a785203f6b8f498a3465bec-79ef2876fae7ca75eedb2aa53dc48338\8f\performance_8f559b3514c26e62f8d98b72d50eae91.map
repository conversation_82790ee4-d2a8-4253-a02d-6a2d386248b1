{"version": 3, "names": ["cov_2j7xd0qkwm", "actualCoverage", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "useCallback", "useEffect", "useMemo", "useRef", "useState", "useDebounce", "value", "delay", "f", "debounced<PERSON><PERSON><PERSON>", "setDebouncedValue", "s", "handler", "setTimeout", "clearTimeout", "useThrottle", "callback", "lastRun", "Date", "now", "args", "current", "b", "useOptimizedCallback", "deps", "useDeepMemo", "factory", "ref", "areEqual", "a", "i", "is", "usePerformanceMonitor", "name", "startTime", "renderCount", "process", "env", "NODE_ENV", "performance", "endTime", "duration", "console", "warn", "toFixed", "useIntersectionObserver", "options", "isIntersecting", "setIsIntersecting", "element", "observer", "IntersectionObserver", "entry", "threshold", "observe", "unobserve", "useVirtualScrolling", "items", "itemHeight", "containerHeight", "scrollTop", "setScrollTop", "visibleItems", "startIndex", "Math", "floor", "endIndex", "min", "ceil", "slice", "totalHeight", "offsetY", "handleScroll", "currentTarget", "useBatchedState", "initialState", "state", "setState", "pendingUpdates", "timeoutRef", "batchedSetState", "updater", "prevState", "newState", "update", "useMemoryMonitor", "componentName", "checkMemory", "memory", "usedJSHeapSize", "jsHeapSizeLimit", "used", "limit", "interval", "setInterval", "clearInterval", "createOptimizedEventHandler", "event", "passive", "useRenderOptimization", "props", "previousProps", "changedProps", "key", "log", "performanceUtils", "mark", "measure", "entries", "getEntriesByName", "clear", "clearMarks", "clearMeasures"], "sources": ["performance.ts"], "sourcesContent": ["/**\n * Performance Optimization Utilities\n * \n * This module provides utilities for optimizing React performance including\n * memoization helpers, debouncing, throttling, and performance monitoring.\n */\n\nimport React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'\n\n// Debounce hook for expensive operations\nexport function useDebounce<T>(value: T, delay: number): T {\n  const [debouncedValue, setDebouncedValue] = useState<T>(value)\n\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value)\n    }, delay)\n\n    return () => {\n      clearTimeout(handler)\n    }\n  }, [value, delay])\n\n  return debouncedValue\n}\n\n// Throttle hook for frequent events\nexport function useThrottle<T extends (...args: any[]) => any>(\n  callback: T,\n  delay: number\n): T {\n  const lastRun = useRef(Date.now())\n\n  return useCallback(\n    ((...args) => {\n      if (Date.now() - lastRun.current >= delay) {\n        callback(...args)\n        lastRun.current = Date.now()\n      }\n    }) as T,\n    [callback, delay]\n  )\n}\n\n// Memoized callback with dependency optimization\nexport function useOptimizedCallback<T extends (...args: any[]) => any>(\n  callback: T,\n  deps: React.DependencyList\n): T {\n  return useCallback(callback, deps)\n}\n\n// Memoized value with deep comparison\nexport function useDeepMemo<T>(factory: () => T, deps: React.DependencyList): T {\n  const ref = useRef<{ deps: React.DependencyList; value: T }>()\n\n  if (!ref.current || !areEqual(ref.current.deps, deps)) {\n    ref.current = { deps, value: factory() }\n  }\n\n  return ref.current.value\n}\n\n// Deep equality check for dependencies\nfunction areEqual(a: React.DependencyList, b: React.DependencyList): boolean {\n  if (a.length !== b.length) return false\n  \n  for (let i = 0; i < a.length; i++) {\n    if (!Object.is(a[i], b[i])) {\n      return false\n    }\n  }\n  \n  return true\n}\n\n// Performance monitoring hook\nexport function usePerformanceMonitor(name: string) {\n  const startTime = useRef<number>()\n  const renderCount = useRef(0)\n\n  useEffect(() => {\n    renderCount.current += 1\n    \n    if (process.env.NODE_ENV === 'development') {\n      if (!startTime.current) {\n        startTime.current = performance.now()\n      }\n      \n      const endTime = performance.now()\n      const duration = endTime - startTime.current\n      \n      if (duration > 16) { // More than one frame (16ms)\n        console.warn(`Slow render detected in ${name}: ${duration.toFixed(2)}ms (render #${renderCount.current})`)\n      }\n      \n      startTime.current = endTime\n    }\n  })\n\n  return { renderCount: renderCount.current }\n}\n\n// Intersection Observer hook for lazy loading\nexport function useIntersectionObserver(\n  options: IntersectionObserverInit = {}\n): [React.RefObject<HTMLElement>, boolean] {\n  const [isIntersecting, setIsIntersecting] = useState(false)\n  const ref = useRef<HTMLElement>(null)\n\n  useEffect(() => {\n    const element = ref.current\n    if (!element) return\n\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        setIsIntersecting(entry.isIntersecting)\n      },\n      {\n        threshold: 0.1,\n        ...options,\n      }\n    )\n\n    observer.observe(element)\n\n    return () => {\n      observer.unobserve(element)\n    }\n  }, [options])\n\n  return [ref, isIntersecting]\n}\n\n// Virtual scrolling hook for large lists\nexport function useVirtualScrolling<T>(\n  items: T[],\n  itemHeight: number,\n  containerHeight: number\n) {\n  const [scrollTop, setScrollTop] = useState(0)\n\n  const visibleItems = useMemo(() => {\n    const startIndex = Math.floor(scrollTop / itemHeight)\n    const endIndex = Math.min(\n      startIndex + Math.ceil(containerHeight / itemHeight) + 1,\n      items.length\n    )\n\n    return {\n      startIndex,\n      endIndex,\n      items: items.slice(startIndex, endIndex),\n      totalHeight: items.length * itemHeight,\n      offsetY: startIndex * itemHeight,\n    }\n  }, [items, itemHeight, containerHeight, scrollTop])\n\n  const handleScroll = useCallback((e: React.UIEvent<HTMLElement>) => {\n    setScrollTop(e.currentTarget.scrollTop)\n  }, [])\n\n  return {\n    ...visibleItems,\n    handleScroll,\n  }\n}\n\n// Optimized state updater that batches updates\nexport function useBatchedState<T>(\n  initialState: T\n): [T, (updater: (prev: T) => T) => void] {\n  const [state, setState] = useState(initialState)\n  const pendingUpdates = useRef<Array<(prev: T) => T>>([])\n  const timeoutRef = useRef<NodeJS.Timeout>()\n\n  const batchedSetState = useCallback((updater: (prev: T) => T) => {\n    pendingUpdates.current.push(updater)\n\n    if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current)\n    }\n\n    timeoutRef.current = setTimeout(() => {\n      setState((prevState) => {\n        let newState = prevState\n        for (const update of pendingUpdates.current) {\n          newState = update(newState)\n        }\n        pendingUpdates.current = []\n        return newState\n      })\n    }, 0)\n  }, [])\n\n  useEffect(() => {\n    return () => {\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current)\n      }\n    }\n  }, [])\n\n  return [state, batchedSetState]\n}\n\n// Memory usage monitoring\nexport function useMemoryMonitor(componentName: string) {\n  useEffect(() => {\n    if (process.env.NODE_ENV === 'development' && 'memory' in performance) {\n      const checkMemory = () => {\n        const memory = (performance as any).memory\n        if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {\n          console.warn(`High memory usage detected in ${componentName}:`, {\n            used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,\n            limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`,\n          })\n        }\n      }\n\n      const interval = setInterval(checkMemory, 5000)\n      return () => clearInterval(interval)\n    }\n  }, [componentName])\n}\n\n// Optimized event handler creator\nexport function createOptimizedEventHandler<T extends Event>(\n  handler: (event: T) => void,\n  options: { passive?: boolean; capture?: boolean } = {}\n) {\n  return useCallback(\n    (event: T) => {\n      if (options.passive !== false) {\n        // For passive events, we can optimize by not calling preventDefault\n        handler(event)\n      } else {\n        handler(event)\n      }\n    },\n    [handler, options]\n  )\n}\n\n// Component render optimization checker\nexport function useRenderOptimization(componentName: string, props: Record<string, any>) {\n  const previousProps = useRef<Record<string, any>>()\n  const renderCount = useRef(0)\n\n  useEffect(() => {\n    renderCount.current += 1\n\n    if (process.env.NODE_ENV === 'development') {\n      if (previousProps.current) {\n        const changedProps = Object.keys(props).filter(\n          key => !Object.is(props[key], previousProps.current![key])\n        )\n\n        if (changedProps.length === 0) {\n          console.warn(`Unnecessary re-render in ${componentName} (render #${renderCount.current})`)\n        } else if (changedProps.length > 0) {\n          console.log(`${componentName} re-rendered due to:`, changedProps)\n        }\n      }\n\n      previousProps.current = { ...props }\n    }\n  })\n\n  return renderCount.current\n}\n\n\n\n// Performance timing utilities\nexport const performanceUtils = {\n  // Mark the start of a performance measurement\n  mark: (name: string) => {\n    if (typeof performance !== 'undefined' && performance.mark) {\n      performance.mark(`${name}-start`)\n    }\n  },\n\n  // Mark the end and measure performance\n  measure: (name: string) => {\n    if (typeof performance !== 'undefined' && performance.mark && performance.measure) {\n      performance.mark(`${name}-end`)\n      performance.measure(name, `${name}-start`, `${name}-end`)\n      \n      const entries = performance.getEntriesByName(name)\n      if (entries.length > 0) {\n        const duration = entries[entries.length - 1].duration\n        if (duration > 100) { // Log if operation takes more than 100ms\n          console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`)\n        }\n      }\n    }\n  },\n\n  // Clear performance marks and measures\n  clear: (name: string) => {\n    if (typeof performance !== 'undefined') {\n      performance.clearMarks?.(`${name}-start`)\n      performance.clearMarks?.(`${name}-end`)\n      performance.clearMeasures?.(name)\n    }\n  },\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAA,SAAAE,QAAAC,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAJ,CAAA,OAAAG,MAAA,CAAAE,qBAAA,QAAAC,CAAA,GAAAH,MAAA,CAAAE,qBAAA,CAAAL,CAAA,GAAAC,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAC,MAAA,WAAAN,CAAA,WAAAE,MAAA,CAAAK,wBAAA,CAAAR,CAAA,EAAAC,CAAA,EAAAQ,UAAA,OAAAP,CAAA,CAAAQ,IAAA,CAAAC,KAAA,CAAAT,CAAA,EAAAI,CAAA,YAAAJ,CAAA;AAAA,SAAAU,cAAAZ,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAY,SAAA,CAAAC,MAAA,EAAAb,CAAA,UAAAC,CAAA,WAAAW,SAAA,CAAAZ,CAAA,IAAAY,SAAA,CAAAZ,CAAA,QAAAA,CAAA,OAAAF,OAAA,CAAAI,MAAA,CAAAD,CAAA,OAAAa,OAAA,WAAAd,CAAA,IAAAe,eAAA,CAAAhB,CAAA,EAAAC,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAc,yBAAA,GAAAd,MAAA,CAAAe,gBAAA,CAAAlB,CAAA,EAAAG,MAAA,CAAAc,yBAAA,CAAAf,CAAA,KAAAH,OAAA,CAAAI,MAAA,CAAAD,CAAA,GAAAa,OAAA,WAAAd,CAAA,IAAAE,MAAA,CAAAgB,cAAA,CAAAnB,CAAA,EAAAC,CAAA,EAAAE,MAAA,CAAAK,wBAAA,CAAAN,CAAA,EAAAD,CAAA,iBAAAD,CAAA;AAfZ;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAgBoB,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;;AAEhF;AACA,OAAO,SAASC,WAAWA,CAAIC,KAAQ,EAAEC,KAAa,EAAK;EAAA;EAAA9B,cAAA,GAAA+B,CAAA;EACzD,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC;EAAA;EAAA,CAAAjC,cAAA,GAAAkC,CAAA,OAAGP,QAAQ,CAAIE,KAAK,CAAC;EAAA;EAAA7B,cAAA,GAAAkC,CAAA;EAE9DV,SAAS,CAAC,MAAM;IAAA;IAAAxB,cAAA,GAAA+B,CAAA;IACd,MAAMI,OAAO;IAAA;IAAA,CAAAnC,cAAA,GAAAkC,CAAA,OAAGE,UAAU,CAAC,MAAM;MAAA;MAAApC,cAAA,GAAA+B,CAAA;MAAA/B,cAAA,GAAAkC,CAAA;MAC/BD,iBAAiB,CAACJ,KAAK,CAAC;IAC1B,CAAC,EAAEC,KAAK,CAAC;IAAA;IAAA9B,cAAA,GAAAkC,CAAA;IAET,OAAO,MAAM;MAAA;MAAAlC,cAAA,GAAA+B,CAAA;MAAA/B,cAAA,GAAAkC,CAAA;MACXG,YAAY,CAACF,OAAO,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACN,KAAK,EAAEC,KAAK,CAAC,CAAC;EAAA;EAAA9B,cAAA,GAAAkC,CAAA;EAElB,OAAOF,cAAc;AACvB;;AAEA;AACA,OAAO,SAASM,WAAWA,CACzBC,QAAW,EACXT,KAAa,EACV;EAAA;EAAA9B,cAAA,GAAA+B,CAAA;EACH,MAAMS,OAAO;EAAA;EAAA,CAAAxC,cAAA,GAAAkC,CAAA,OAAGR,MAAM,CAACe,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAAA;EAAA1C,cAAA,GAAAkC,CAAA;EAElC,OAAOX,WAAW,CACf,CAAC,GAAGoB,IAAI,KAAK;IAAA;IAAA3C,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAkC,CAAA;IACZ,IAAIO,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,OAAO,CAACI,OAAO,IAAId,KAAK,EAAE;MAAA;MAAA9B,cAAA,GAAA6C,CAAA;MAAA7C,cAAA,GAAAkC,CAAA;MACzCK,QAAQ,CAAC,GAAGI,IAAI,CAAC;MAAA;MAAA3C,cAAA,GAAAkC,CAAA;MACjBM,OAAO,CAACI,OAAO,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAAA;IAAA;MAAA1C,cAAA,GAAA6C,CAAA;IAAA;EACH,CAAC,EACD,CAACN,QAAQ,EAAET,KAAK,CAClB,CAAC;AACH;;AAEA;AACA,OAAO,SAASgB,oBAAoBA,CAClCP,QAAW,EACXQ,IAA0B,EACvB;EAAA;EAAA/C,cAAA,GAAA+B,CAAA;EAAA/B,cAAA,GAAAkC,CAAA;EACH,OAAOX,WAAW,CAACgB,QAAQ,EAAEQ,IAAI,CAAC;AACpC;;AAEA;AACA,OAAO,SAASC,WAAWA,CAAIC,OAAgB,EAAEF,IAA0B,EAAK;EAAA;EAAA/C,cAAA,GAAA+B,CAAA;EAC9E,MAAMmB,GAAG;EAAA;EAAA,CAAAlD,cAAA,GAAAkC,CAAA,QAAGR,MAAM,CAA2C,CAAC;EAAA;EAAA1B,cAAA,GAAAkC,CAAA;EAE9D;EAAI;EAAA,CAAAlC,cAAA,GAAA6C,CAAA,WAACK,GAAG,CAACN,OAAO;EAAA;EAAA,CAAA5C,cAAA,GAAA6C,CAAA,UAAI,CAACM,QAAQ,CAACD,GAAG,CAACN,OAAO,CAACG,IAAI,EAAEA,IAAI,CAAC,GAAE;IAAA;IAAA/C,cAAA,GAAA6C,CAAA;IAAA7C,cAAA,GAAAkC,CAAA;IACrDgB,GAAG,CAACN,OAAO,GAAG;MAAEG,IAAI;MAAElB,KAAK,EAAEoB,OAAO,CAAC;IAAE,CAAC;EAC1C,CAAC;EAAA;EAAA;IAAAjD,cAAA,GAAA6C,CAAA;EAAA;EAAA7C,cAAA,GAAAkC,CAAA;EAED,OAAOgB,GAAG,CAACN,OAAO,CAACf,KAAK;AAC1B;;AAEA;AACA,SAASsB,QAAQA,CAACC,CAAuB,EAAEP,CAAuB,EAAW;EAAA;EAAA7C,cAAA,GAAA+B,CAAA;EAAA/B,cAAA,GAAAkC,CAAA;EAC3E,IAAIkB,CAAC,CAACnC,MAAM,KAAK4B,CAAC,CAAC5B,MAAM,EAAE;IAAA;IAAAjB,cAAA,GAAA6C,CAAA;IAAA7C,cAAA,GAAAkC,CAAA;IAAA,OAAO,KAAK;EAAD,CAAC;EAAA;EAAA;IAAAlC,cAAA,GAAA6C,CAAA;EAAA;EAAA7C,cAAA,GAAAkC,CAAA;EAEvC,KAAK,IAAImB,CAAC;EAAA;EAAA,CAAArD,cAAA,GAAAkC,CAAA,QAAG,CAAC,GAAEmB,CAAC,GAAGD,CAAC,CAACnC,MAAM,EAAEoC,CAAC,EAAE,EAAE;IAAA;IAAArD,cAAA,GAAAkC,CAAA;IACjC,IAAI,CAAC5B,MAAM,CAACgD,EAAE,CAACF,CAAC,CAACC,CAAC,CAAC,EAAER,CAAC,CAACQ,CAAC,CAAC,CAAC,EAAE;MAAA;MAAArD,cAAA,GAAA6C,CAAA;MAAA7C,cAAA,GAAAkC,CAAA;MAC1B,OAAO,KAAK;IACd,CAAC;IAAA;IAAA;MAAAlC,cAAA,GAAA6C,CAAA;IAAA;EACH;EAAC;EAAA7C,cAAA,GAAAkC,CAAA;EAED,OAAO,IAAI;AACb;;AAEA;AACA,OAAO,SAASqB,qBAAqBA,CAACC,IAAY,EAAE;EAAA;EAAAxD,cAAA,GAAA+B,CAAA;EAClD,MAAM0B,SAAS;EAAA;EAAA,CAAAzD,cAAA,GAAAkC,CAAA,QAAGR,MAAM,CAAS,CAAC;EAClC,MAAMgC,WAAW;EAAA;EAAA,CAAA1D,cAAA,GAAAkC,CAAA,QAAGR,MAAM,CAAC,CAAC,CAAC;EAAA;EAAA1B,cAAA,GAAAkC,CAAA;EAE7BV,SAAS,CAAC,MAAM;IAAA;IAAAxB,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAkC,CAAA;IACdwB,WAAW,CAACd,OAAO,IAAI,CAAC;IAAA;IAAA5C,cAAA,GAAAkC,CAAA;IAExB,IAAIyB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;MAAA;MAAA7D,cAAA,GAAA6C,CAAA;MAAA7C,cAAA,GAAAkC,CAAA;MAC1C,IAAI,CAACuB,SAAS,CAACb,OAAO,EAAE;QAAA;QAAA5C,cAAA,GAAA6C,CAAA;QAAA7C,cAAA,GAAAkC,CAAA;QACtBuB,SAAS,CAACb,OAAO,GAAGkB,WAAW,CAACpB,GAAG,CAAC,CAAC;MACvC,CAAC;MAAA;MAAA;QAAA1C,cAAA,GAAA6C,CAAA;MAAA;MAED,MAAMkB,OAAO;MAAA;MAAA,CAAA/D,cAAA,GAAAkC,CAAA,QAAG4B,WAAW,CAACpB,GAAG,CAAC,CAAC;MACjC,MAAMsB,QAAQ;MAAA;MAAA,CAAAhE,cAAA,GAAAkC,CAAA,QAAG6B,OAAO,GAAGN,SAAS,CAACb,OAAO;MAAA;MAAA5C,cAAA,GAAAkC,CAAA;MAE5C,IAAI8B,QAAQ,GAAG,EAAE,EAAE;QAAA;QAAAhE,cAAA,GAAA6C,CAAA;QAAA7C,cAAA,GAAAkC,CAAA;QAAE;QACnB+B,OAAO,CAACC,IAAI,CAAC,2BAA2BV,IAAI,KAAKQ,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,eAAeT,WAAW,CAACd,OAAO,GAAG,CAAC;MAC5G,CAAC;MAAA;MAAA;QAAA5C,cAAA,GAAA6C,CAAA;MAAA;MAAA7C,cAAA,GAAAkC,CAAA;MAEDuB,SAAS,CAACb,OAAO,GAAGmB,OAAO;IAC7B,CAAC;IAAA;IAAA;MAAA/D,cAAA,GAAA6C,CAAA;IAAA;EACH,CAAC,CAAC;EAAA;EAAA7C,cAAA,GAAAkC,CAAA;EAEF,OAAO;IAAEwB,WAAW,EAAEA,WAAW,CAACd;EAAQ,CAAC;AAC7C;;AAEA;AACA,OAAO,SAASwB,uBAAuBA,CACrCC,OAAiC;AAAA;AAAA,CAAArE,cAAA,GAAA6C,CAAA,UAAG,CAAC,CAAC,GACG;EAAA;EAAA7C,cAAA,GAAA+B,CAAA;EACzC,MAAM,CAACuC,cAAc,EAAEC,iBAAiB,CAAC;EAAA;EAAA,CAAAvE,cAAA,GAAAkC,CAAA,QAAGP,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMuB,GAAG;EAAA;EAAA,CAAAlD,cAAA,GAAAkC,CAAA,QAAGR,MAAM,CAAc,IAAI,CAAC;EAAA;EAAA1B,cAAA,GAAAkC,CAAA;EAErCV,SAAS,CAAC,MAAM;IAAA;IAAAxB,cAAA,GAAA+B,CAAA;IACd,MAAMyC,OAAO;IAAA;IAAA,CAAAxE,cAAA,GAAAkC,CAAA,QAAGgB,GAAG,CAACN,OAAO;IAAA;IAAA5C,cAAA,GAAAkC,CAAA;IAC3B,IAAI,CAACsC,OAAO,EAAE;MAAA;MAAAxE,cAAA,GAAA6C,CAAA;MAAA7C,cAAA,GAAAkC,CAAA;MAAA;IAAK,CAAC;IAAA;IAAA;MAAAlC,cAAA,GAAA6C,CAAA;IAAA;IAEpB,MAAM4B,QAAQ;IAAA;IAAA,CAAAzE,cAAA,GAAAkC,CAAA,QAAG,IAAIwC,oBAAoB,CACvC,CAAC,CAACC,KAAK,CAAC,KAAK;MAAA;MAAA3E,cAAA,GAAA+B,CAAA;MAAA/B,cAAA,GAAAkC,CAAA;MACXqC,iBAAiB,CAACI,KAAK,CAACL,cAAc,CAAC;IACzC,CAAC;IAAA;IAAAvD,aAAA;MAEC6D,SAAS,EAAE;IAAG,GACXP,OAAO,CAEd,CAAC;IAAA;IAAArE,cAAA,GAAAkC,CAAA;IAEDuC,QAAQ,CAACI,OAAO,CAACL,OAAO,CAAC;IAAA;IAAAxE,cAAA,GAAAkC,CAAA;IAEzB,OAAO,MAAM;MAAA;MAAAlC,cAAA,GAAA+B,CAAA;MAAA/B,cAAA,GAAAkC,CAAA;MACXuC,QAAQ,CAACK,SAAS,CAACN,OAAO,CAAC;IAC7B,CAAC;EACH,CAAC,EAAE,CAACH,OAAO,CAAC,CAAC;EAAA;EAAArE,cAAA,GAAAkC,CAAA;EAEb,OAAO,CAACgB,GAAG,EAAEoB,cAAc,CAAC;AAC9B;;AAEA;AACA,OAAO,SAASS,mBAAmBA,CACjCC,KAAU,EACVC,UAAkB,EAClBC,eAAuB,EACvB;EAAA;EAAAlF,cAAA,GAAA+B,CAAA;EACA,MAAM,CAACoD,SAAS,EAAEC,YAAY,CAAC;EAAA;EAAA,CAAApF,cAAA,GAAAkC,CAAA,QAAGP,QAAQ,CAAC,CAAC,CAAC;EAE7C,MAAM0D,YAAY;EAAA;EAAA,CAAArF,cAAA,GAAAkC,CAAA,QAAGT,OAAO,CAAC,MAAM;IAAA;IAAAzB,cAAA,GAAA+B,CAAA;IACjC,MAAMuD,UAAU;IAAA;IAAA,CAAAtF,cAAA,GAAAkC,CAAA,QAAGqD,IAAI,CAACC,KAAK,CAACL,SAAS,GAAGF,UAAU,CAAC;IACrD,MAAMQ,QAAQ;IAAA;IAAA,CAAAzF,cAAA,GAAAkC,CAAA,QAAGqD,IAAI,CAACG,GAAG,CACvBJ,UAAU,GAAGC,IAAI,CAACI,IAAI,CAACT,eAAe,GAAGD,UAAU,CAAC,GAAG,CAAC,EACxDD,KAAK,CAAC/D,MACR,CAAC;IAAA;IAAAjB,cAAA,GAAAkC,CAAA;IAED,OAAO;MACLoD,UAAU;MACVG,QAAQ;MACRT,KAAK,EAAEA,KAAK,CAACY,KAAK,CAACN,UAAU,EAAEG,QAAQ,CAAC;MACxCI,WAAW,EAAEb,KAAK,CAAC/D,MAAM,GAAGgE,UAAU;MACtCa,OAAO,EAAER,UAAU,GAAGL;IACxB,CAAC;EACH,CAAC,EAAE,CAACD,KAAK,EAAEC,UAAU,EAAEC,eAAe,EAAEC,SAAS,CAAC,CAAC;EAEnD,MAAMY,YAAY;EAAA;EAAA,CAAA/F,cAAA,GAAAkC,CAAA,QAAGX,WAAW,CAAEpB,CAA6B,IAAK;IAAA;IAAAH,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAkC,CAAA;IAClEkD,YAAY,CAACjF,CAAC,CAAC6F,aAAa,CAACb,SAAS,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;EAAA;EAAAnF,cAAA,GAAAkC,CAAA;EAEN,iCAAAnB,aAAA,CAAAA,aAAA,KACKsE,YAAY;IACfU;EAAY;AAEhB;;AAEA;AACA,OAAO,SAASE,eAAeA,CAC7BC,YAAe,EACyB;EAAA;EAAAlG,cAAA,GAAA+B,CAAA;EACxC,MAAM,CAACoE,KAAK,EAAEC,QAAQ,CAAC;EAAA;EAAA,CAAApG,cAAA,GAAAkC,CAAA,QAAGP,QAAQ,CAACuE,YAAY,CAAC;EAChD,MAAMG,cAAc;EAAA;EAAA,CAAArG,cAAA,GAAAkC,CAAA,QAAGR,MAAM,CAAwB,EAAE,CAAC;EACxD,MAAM4E,UAAU;EAAA;EAAA,CAAAtG,cAAA,GAAAkC,CAAA,QAAGR,MAAM,CAAiB,CAAC;EAE3C,MAAM6E,eAAe;EAAA;EAAA,CAAAvG,cAAA,GAAAkC,CAAA,QAAGX,WAAW,CAAEiF,OAAuB,IAAK;IAAA;IAAAxG,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAkC,CAAA;IAC/DmE,cAAc,CAACzD,OAAO,CAAC/B,IAAI,CAAC2F,OAAO,CAAC;IAAA;IAAAxG,cAAA,GAAAkC,CAAA;IAEpC,IAAIoE,UAAU,CAAC1D,OAAO,EAAE;MAAA;MAAA5C,cAAA,GAAA6C,CAAA;MAAA7C,cAAA,GAAAkC,CAAA;MACtBG,YAAY,CAACiE,UAAU,CAAC1D,OAAO,CAAC;IAClC,CAAC;IAAA;IAAA;MAAA5C,cAAA,GAAA6C,CAAA;IAAA;IAAA7C,cAAA,GAAAkC,CAAA;IAEDoE,UAAU,CAAC1D,OAAO,GAAGR,UAAU,CAAC,MAAM;MAAA;MAAApC,cAAA,GAAA+B,CAAA;MAAA/B,cAAA,GAAAkC,CAAA;MACpCkE,QAAQ,CAAEK,SAAS,IAAK;QAAA;QAAAzG,cAAA,GAAA+B,CAAA;QACtB,IAAI2E,QAAQ;QAAA;QAAA,CAAA1G,cAAA,GAAAkC,CAAA,QAAGuE,SAAS;QAAA;QAAAzG,cAAA,GAAAkC,CAAA;QACxB,KAAK,MAAMyE,MAAM,IAAIN,cAAc,CAACzD,OAAO,EAAE;UAAA;UAAA5C,cAAA,GAAAkC,CAAA;UAC3CwE,QAAQ,GAAGC,MAAM,CAACD,QAAQ,CAAC;QAC7B;QAAC;QAAA1G,cAAA,GAAAkC,CAAA;QACDmE,cAAc,CAACzD,OAAO,GAAG,EAAE;QAAA;QAAA5C,cAAA,GAAAkC,CAAA;QAC3B,OAAOwE,QAAQ;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,EAAE,CAAC;EAAA;EAAA1G,cAAA,GAAAkC,CAAA;EAENV,SAAS,CAAC,MAAM;IAAA;IAAAxB,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAkC,CAAA;IACd,OAAO,MAAM;MAAA;MAAAlC,cAAA,GAAA+B,CAAA;MAAA/B,cAAA,GAAAkC,CAAA;MACX,IAAIoE,UAAU,CAAC1D,OAAO,EAAE;QAAA;QAAA5C,cAAA,GAAA6C,CAAA;QAAA7C,cAAA,GAAAkC,CAAA;QACtBG,YAAY,CAACiE,UAAU,CAAC1D,OAAO,CAAC;MAClC,CAAC;MAAA;MAAA;QAAA5C,cAAA,GAAA6C,CAAA;MAAA;IACH,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAAA;EAAA7C,cAAA,GAAAkC,CAAA;EAEN,OAAO,CAACiE,KAAK,EAAEI,eAAe,CAAC;AACjC;;AAEA;AACA,OAAO,SAASK,gBAAgBA,CAACC,aAAqB,EAAE;EAAA;EAAA7G,cAAA,GAAA+B,CAAA;EAAA/B,cAAA,GAAAkC,CAAA;EACtDV,SAAS,CAAC,MAAM;IAAA;IAAAxB,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAkC,CAAA;IACd;IAAI;IAAA,CAAAlC,cAAA,GAAA6C,CAAA,WAAAc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa;IAAA;IAAA,CAAA7D,cAAA,GAAA6C,CAAA,WAAI,QAAQ,IAAIiB,WAAW,GAAE;MAAA;MAAA9D,cAAA,GAAA6C,CAAA;MAAA7C,cAAA,GAAAkC,CAAA;MACrE,MAAM4E,WAAW,GAAGA,CAAA,KAAM;QAAA;QAAA9G,cAAA,GAAA+B,CAAA;QACxB,MAAMgF,MAAM;QAAA;QAAA,CAAA/G,cAAA,GAAAkC,CAAA,QAAI4B,WAAW,CAASiD,MAAM;QAAA;QAAA/G,cAAA,GAAAkC,CAAA;QAC1C,IAAI6E,MAAM,CAACC,cAAc,GAAGD,MAAM,CAACE,eAAe,GAAG,GAAG,EAAE;UAAA;UAAAjH,cAAA,GAAA6C,CAAA;UAAA7C,cAAA,GAAAkC,CAAA;UACxD+B,OAAO,CAACC,IAAI,CAAC,iCAAiC2C,aAAa,GAAG,EAAE;YAC9DK,IAAI,EAAE,GAAG,CAACH,MAAM,CAACC,cAAc,GAAG,IAAI,GAAG,IAAI,EAAE7C,OAAO,CAAC,CAAC,CAAC,IAAI;YAC7DgD,KAAK,EAAE,GAAG,CAACJ,MAAM,CAACE,eAAe,GAAG,IAAI,GAAG,IAAI,EAAE9C,OAAO,CAAC,CAAC,CAAC;UAC7D,CAAC,CAAC;QACJ,CAAC;QAAA;QAAA;UAAAnE,cAAA,GAAA6C,CAAA;QAAA;MACH,CAAC;MAED,MAAMuE,QAAQ;MAAA;MAAA,CAAApH,cAAA,GAAAkC,CAAA,QAAGmF,WAAW,CAACP,WAAW,EAAE,IAAI,CAAC;MAAA;MAAA9G,cAAA,GAAAkC,CAAA;MAC/C,OAAO,MAAM;QAAA;QAAAlC,cAAA,GAAA+B,CAAA;QAAA/B,cAAA,GAAAkC,CAAA;QAAA,OAAAoF,aAAa,CAACF,QAAQ,CAAC;MAAD,CAAC;IACtC,CAAC;IAAA;IAAA;MAAApH,cAAA,GAAA6C,CAAA;IAAA;EACH,CAAC,EAAE,CAACgE,aAAa,CAAC,CAAC;AACrB;;AAEA;AACA,OAAO,SAASU,2BAA2BA,CACzCpF,OAA2B,EAC3BkC,OAAiD;AAAA;AAAA,CAAArE,cAAA,GAAA6C,CAAA,WAAG,CAAC,CAAC,GACtD;EAAA;EAAA7C,cAAA,GAAA+B,CAAA;EAAA/B,cAAA,GAAAkC,CAAA;EACA,OAAOX,WAAW,CACfiG,KAAQ,IAAK;IAAA;IAAAxH,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAkC,CAAA;IACZ,IAAImC,OAAO,CAACoD,OAAO,KAAK,KAAK,EAAE;MAAA;MAAAzH,cAAA,GAAA6C,CAAA;MAAA7C,cAAA,GAAAkC,CAAA;MAC7B;MACAC,OAAO,CAACqF,KAAK,CAAC;IAChB,CAAC,MAAM;MAAA;MAAAxH,cAAA,GAAA6C,CAAA;MAAA7C,cAAA,GAAAkC,CAAA;MACLC,OAAO,CAACqF,KAAK,CAAC;IAChB;EACF,CAAC,EACD,CAACrF,OAAO,EAAEkC,OAAO,CACnB,CAAC;AACH;;AAEA;AACA,OAAO,SAASqD,qBAAqBA,CAACb,aAAqB,EAAEc,KAA0B,EAAE;EAAA;EAAA3H,cAAA,GAAA+B,CAAA;EACvF,MAAM6F,aAAa;EAAA;EAAA,CAAA5H,cAAA,GAAAkC,CAAA,QAAGR,MAAM,CAAsB,CAAC;EACnD,MAAMgC,WAAW;EAAA;EAAA,CAAA1D,cAAA,GAAAkC,CAAA,QAAGR,MAAM,CAAC,CAAC,CAAC;EAAA;EAAA1B,cAAA,GAAAkC,CAAA;EAE7BV,SAAS,CAAC,MAAM;IAAA;IAAAxB,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAkC,CAAA;IACdwB,WAAW,CAACd,OAAO,IAAI,CAAC;IAAA;IAAA5C,cAAA,GAAAkC,CAAA;IAExB,IAAIyB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;MAAA;MAAA7D,cAAA,GAAA6C,CAAA;MAAA7C,cAAA,GAAAkC,CAAA;MAC1C,IAAI0F,aAAa,CAAChF,OAAO,EAAE;QAAA;QAAA5C,cAAA,GAAA6C,CAAA;QACzB,MAAMgF,YAAY;QAAA;QAAA,CAAA7H,cAAA,GAAAkC,CAAA,QAAG5B,MAAM,CAACC,IAAI,CAACoH,KAAK,CAAC,CAACjH,MAAM,CAC5CoH,GAAG,IAAI;UAAA;UAAA9H,cAAA,GAAA+B,CAAA;UAAA/B,cAAA,GAAAkC,CAAA;UAAA,QAAC5B,MAAM,CAACgD,EAAE,CAACqE,KAAK,CAACG,GAAG,CAAC,EAAEF,aAAa,CAAChF,OAAO,CAAEkF,GAAG,CAAC,CAAC;QAAD,CAC3D,CAAC;QAAA;QAAA9H,cAAA,GAAAkC,CAAA;QAED,IAAI2F,YAAY,CAAC5G,MAAM,KAAK,CAAC,EAAE;UAAA;UAAAjB,cAAA,GAAA6C,CAAA;UAAA7C,cAAA,GAAAkC,CAAA;UAC7B+B,OAAO,CAACC,IAAI,CAAC,4BAA4B2C,aAAa,aAAanD,WAAW,CAACd,OAAO,GAAG,CAAC;QAC5F,CAAC,MAAM;UAAA;UAAA5C,cAAA,GAAA6C,CAAA;UAAA7C,cAAA,GAAAkC,CAAA;UAAA,IAAI2F,YAAY,CAAC5G,MAAM,GAAG,CAAC,EAAE;YAAA;YAAAjB,cAAA,GAAA6C,CAAA;YAAA7C,cAAA,GAAAkC,CAAA;YAClC+B,OAAO,CAAC8D,GAAG,CAAC,GAAGlB,aAAa,sBAAsB,EAAEgB,YAAY,CAAC;UACnE,CAAC;UAAA;UAAA;YAAA7H,cAAA,GAAA6C,CAAA;UAAA;QAAD;MACF,CAAC;MAAA;MAAA;QAAA7C,cAAA,GAAA6C,CAAA;MAAA;MAAA7C,cAAA,GAAAkC,CAAA;MAED0F,aAAa,CAAChF,OAAO;MAAA;MAAA7B,aAAA,KAAQ4G,KAAK,CAAE;IACtC,CAAC;IAAA;IAAA;MAAA3H,cAAA,GAAA6C,CAAA;IAAA;EACH,CAAC,CAAC;EAAA;EAAA7C,cAAA,GAAAkC,CAAA;EAEF,OAAOwB,WAAW,CAACd,OAAO;AAC5B;;AAIA;AACA,OAAO,MAAMoF,gBAAgB;AAAA;AAAA,CAAAhI,cAAA,GAAAkC,CAAA,SAAG;EAC9B;EACA+F,IAAI,EAAGzE,IAAY,IAAK;IAAA;IAAAxD,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAkC,CAAA;IACtB;IAAI;IAAA,CAAAlC,cAAA,GAAA6C,CAAA,kBAAOiB,WAAW,KAAK,WAAW;IAAA;IAAA,CAAA9D,cAAA,GAAA6C,CAAA,WAAIiB,WAAW,CAACmE,IAAI,GAAE;MAAA;MAAAjI,cAAA,GAAA6C,CAAA;MAAA7C,cAAA,GAAAkC,CAAA;MAC1D4B,WAAW,CAACmE,IAAI,CAAC,GAAGzE,IAAI,QAAQ,CAAC;IACnC,CAAC;IAAA;IAAA;MAAAxD,cAAA,GAAA6C,CAAA;IAAA;EACH,CAAC;EAED;EACAqF,OAAO,EAAG1E,IAAY,IAAK;IAAA;IAAAxD,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAkC,CAAA;IACzB;IAAI;IAAA,CAAAlC,cAAA,GAAA6C,CAAA,kBAAOiB,WAAW,KAAK,WAAW;IAAA;IAAA,CAAA9D,cAAA,GAAA6C,CAAA,WAAIiB,WAAW,CAACmE,IAAI;IAAA;IAAA,CAAAjI,cAAA,GAAA6C,CAAA,WAAIiB,WAAW,CAACoE,OAAO,GAAE;MAAA;MAAAlI,cAAA,GAAA6C,CAAA;MAAA7C,cAAA,GAAAkC,CAAA;MACjF4B,WAAW,CAACmE,IAAI,CAAC,GAAGzE,IAAI,MAAM,CAAC;MAAA;MAAAxD,cAAA,GAAAkC,CAAA;MAC/B4B,WAAW,CAACoE,OAAO,CAAC1E,IAAI,EAAE,GAAGA,IAAI,QAAQ,EAAE,GAAGA,IAAI,MAAM,CAAC;MAEzD,MAAM2E,OAAO;MAAA;MAAA,CAAAnI,cAAA,GAAAkC,CAAA,SAAG4B,WAAW,CAACsE,gBAAgB,CAAC5E,IAAI,CAAC;MAAA;MAAAxD,cAAA,GAAAkC,CAAA;MAClD,IAAIiG,OAAO,CAAClH,MAAM,GAAG,CAAC,EAAE;QAAA;QAAAjB,cAAA,GAAA6C,CAAA;QACtB,MAAMmB,QAAQ;QAAA;QAAA,CAAAhE,cAAA,GAAAkC,CAAA,SAAGiG,OAAO,CAACA,OAAO,CAAClH,MAAM,GAAG,CAAC,CAAC,CAAC+C,QAAQ;QAAA;QAAAhE,cAAA,GAAAkC,CAAA;QACrD,IAAI8B,QAAQ,GAAG,GAAG,EAAE;UAAA;UAAAhE,cAAA,GAAA6C,CAAA;UAAA7C,cAAA,GAAAkC,CAAA;UAAE;UACpB+B,OAAO,CAAC8D,GAAG,CAAC,gBAAgBvE,IAAI,SAASQ,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;QACnE,CAAC;QAAA;QAAA;UAAAnE,cAAA,GAAA6C,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAA7C,cAAA,GAAA6C,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAA7C,cAAA,GAAA6C,CAAA;IAAA;EACH,CAAC;EAED;EACAwF,KAAK,EAAG7E,IAAY,IAAK;IAAA;IAAAxD,cAAA,GAAA+B,CAAA;IAAA/B,cAAA,GAAAkC,CAAA;IACvB,IAAI,OAAO4B,WAAW,KAAK,WAAW,EAAE;MAAA;MAAA9D,cAAA,GAAA6C,CAAA;MAAA7C,cAAA,GAAAkC,CAAA;MACtC4B,WAAW,CAACwE,UAAU,GAAG,GAAG9E,IAAI,QAAQ,CAAC;MAAA;MAAAxD,cAAA,GAAAkC,CAAA;MACzC4B,WAAW,CAACwE,UAAU,GAAG,GAAG9E,IAAI,MAAM,CAAC;MAAA;MAAAxD,cAAA,GAAAkC,CAAA;MACvC4B,WAAW,CAACyE,aAAa,GAAG/E,IAAI,CAAC;IACnC,CAAC;IAAA;IAAA;MAAAxD,cAAA,GAAA6C,CAAA;IAAA;EACH;AACF,CAAC", "ignoreList": []}