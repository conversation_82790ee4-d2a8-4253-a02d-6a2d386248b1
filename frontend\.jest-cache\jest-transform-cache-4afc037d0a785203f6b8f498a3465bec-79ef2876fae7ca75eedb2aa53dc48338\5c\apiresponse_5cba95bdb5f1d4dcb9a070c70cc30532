53b8bb1e8b4235cf7aca10618abf739a
/* istanbul ignore next */
function cov_1d32atkwd8() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\api-response.ts";
  var hash = "06f3146d983008b02566a2067bb934c9f6b7e3f2";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\api-response.ts",
    statementMap: {
      "0": {
        start: {
          line: 56,
          column: 35
        },
        end: {
          line: 61,
          column: 3
        }
      },
      "1": {
        start: {
          line: 63,
          column: 2
        },
        end: {
          line: 63,
          column: 49
        }
      },
      "2": {
        start: {
          line: 75,
          column: 32
        },
        end: {
          line: 80,
          column: 3
        }
      },
      "3": {
        start: {
          line: 83,
          column: 2
        },
        end: {
          line: 85,
          column: 3
        }
      },
      "4": {
        start: {
          line: 84,
          column: 4
        },
        end: {
          line: 84,
          column: 40
        }
      },
      "5": {
        start: {
          line: 87,
          column: 2
        },
        end: {
          line: 87,
          column: 49
        }
      },
      "6": {
        start: {
          line: 94,
          column: 23
        },
        end: {
          line: 94,
          column: 98
        }
      },
      "7": {
        start: {
          line: 94,
          column: 47
        },
        end: {
          line: 94,
          column: 86
        }
      },
      "8": {
        start: {
          line: 96,
          column: 2
        },
        end: {
          line: 101,
          column: 4
        }
      },
      "9": {
        start: {
          line: 108,
          column: 2
        },
        end: {
          line: 108,
          column: 42
        }
      },
      "10": {
        start: {
          line: 111,
          column: 18
        },
        end: {
          line: 113,
          column: 33
        }
      },
      "11": {
        start: {
          line: 115,
          column: 2
        },
        end: {
          line: 120,
          column: 4
        }
      },
      "12": {
        start: {
          line: 127,
          column: 2
        },
        end: {
          line: 131,
          column: 4
        }
      },
      "13": {
        start: {
          line: 138,
          column: 2
        },
        end: {
          line: 142,
          column: 4
        }
      },
      "14": {
        start: {
          line: 149,
          column: 2
        },
        end: {
          line: 153,
          column: 4
        }
      },
      "15": {
        start: {
          line: 160,
          column: 19
        },
        end: {
          line: 164,
          column: 3
        }
      },
      "16": {
        start: {
          line: 167,
          column: 2
        },
        end: {
          line: 167,
          column: 44
        }
      },
      "17": {
        start: {
          line: 168,
          column: 2
        },
        end: {
          line: 168,
          column: 51
        }
      },
      "18": {
        start: {
          line: 169,
          column: 2
        },
        end: {
          line: 169,
          column: 53
        }
      },
      "19": {
        start: {
          line: 171,
          column: 2
        },
        end: {
          line: 171,
          column: 18
        }
      },
      "20": {
        start: {
          line: 178,
          column: 2
        },
        end: {
          line: 178,
          column: 37
        }
      },
      "21": {
        start: {
          line: 181,
          column: 2
        },
        end: {
          line: 183,
          column: 3
        }
      },
      "22": {
        start: {
          line: 182,
          column: 4
        },
        end: {
          line: 182,
          column: 40
        }
      },
      "23": {
        start: {
          line: 186,
          column: 2
        },
        end: {
          line: 188,
          column: 3
        }
      },
      "24": {
        start: {
          line: 187,
          column: 4
        },
        end: {
          line: 187,
          column: 38
        }
      },
      "25": {
        start: {
          line: 191,
          column: 18
        },
        end: {
          line: 193,
          column: 29
        }
      },
      "26": {
        start: {
          line: 195,
          column: 2
        },
        end: {
          line: 200,
          column: 4
        }
      },
      "27": {
        start: {
          line: 207,
          column: 2
        },
        end: {
          line: 207,
          column: 60
        }
      },
      "28": {
        start: {
          line: 208,
          column: 2
        },
        end: {
          line: 208,
          column: 50
        }
      },
      "29": {
        start: {
          line: 209,
          column: 2
        },
        end: {
          line: 209,
          column: 60
        }
      },
      "30": {
        start: {
          line: 210,
          column: 2
        },
        end: {
          line: 210,
          column: 77
        }
      },
      "31": {
        start: {
          line: 212,
          column: 2
        },
        end: {
          line: 212,
          column: 18
        }
      },
      "32": {
        start: {
          line: 221,
          column: 2
        },
        end: {
          line: 229,
          column: 4
        }
      },
      "33": {
        start: {
          line: 222,
          column: 4
        },
        end: {
          line: 228,
          column: 5
        }
      },
      "34": {
        start: {
          line: 223,
          column: 23
        },
        end: {
          line: 223,
          column: 45
        }
      },
      "35": {
        start: {
          line: 224,
          column: 6
        },
        end: {
          line: 224,
          column: 42
        }
      },
      "36": {
        start: {
          line: 226,
          column: 28
        },
        end: {
          line: 226,
          column: 49
        }
      },
      "37": {
        start: {
          line: 227,
          column: 6
        },
        end: {
          line: 227,
          column: 47
        }
      }
    },
    fnMap: {
      "0": {
        name: "createSuccessResponse",
        decl: {
          start: {
            line: 51,
            column: 16
          },
          end: {
            line: 51,
            column: 37
          }
        },
        loc: {
          start: {
            line: 55,
            column: 32
          },
          end: {
            line: 64,
            column: 1
          }
        },
        line: 55
      },
      "1": {
        name: "createErrorResponse",
        decl: {
          start: {
            line: 69,
            column: 16
          },
          end: {
            line: 69,
            column: 35
          }
        },
        loc: {
          start: {
            line: 74,
            column: 29
          },
          end: {
            line: 88,
            column: 1
          }
        },
        line: 74
      },
      "2": {
        name: "handleValidationError",
        decl: {
          start: {
            line: 93,
            column: 16
          },
          end: {
            line: 93,
            column: 37
          }
        },
        loc: {
          start: {
            line: 93,
            column: 82
          },
          end: {
            line: 102,
            column: 1
          }
        },
        line: 93
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 94,
            column: 40
          },
          end: {
            line: 94,
            column: 41
          }
        },
        loc: {
          start: {
            line: 94,
            column: 47
          },
          end: {
            line: 94,
            column: 86
          }
        },
        line: 94
      },
      "4": {
        name: "handleDatabaseError",
        decl: {
          start: {
            line: 107,
            column: 16
          },
          end: {
            line: 107,
            column: 35
          }
        },
        loc: {
          start: {
            line: 107,
            column: 75
          },
          end: {
            line: 121,
            column: 1
          }
        },
        line: 107
      },
      "5": {
        name: "createUnauthorizedResponse",
        decl: {
          start: {
            line: 126,
            column: 16
          },
          end: {
            line: 126,
            column: 42
          }
        },
        loc: {
          start: {
            line: 126,
            column: 96
          },
          end: {
            line: 132,
            column: 1
          }
        },
        line: 126
      },
      "6": {
        name: "createForbiddenResponse",
        decl: {
          start: {
            line: 137,
            column: 16
          },
          end: {
            line: 137,
            column: 39
          }
        },
        loc: {
          start: {
            line: 137,
            column: 90
          },
          end: {
            line: 143,
            column: 1
          }
        },
        line: 137
      },
      "7": {
        name: "createNotFoundResponse",
        decl: {
          start: {
            line: 148,
            column: 16
          },
          end: {
            line: 148,
            column: 38
          }
        },
        loc: {
          start: {
            line: 148,
            column: 89
          },
          end: {
            line: 154,
            column: 1
          }
        },
        line: 148
      },
      "8": {
        name: "createRateLimitResponse",
        decl: {
          start: {
            line: 159,
            column: 16
          },
          end: {
            line: 159,
            column: 39
          }
        },
        loc: {
          start: {
            line: 159,
            column: 69
          },
          end: {
            line: 172,
            column: 1
          }
        },
        line: 159
      },
      "9": {
        name: "handleApiError",
        decl: {
          start: {
            line: 177,
            column: 16
          },
          end: {
            line: 177,
            column: 30
          }
        },
        loc: {
          start: {
            line: 177,
            column: 70
          },
          end: {
            line: 201,
            column: 1
          }
        },
        line: 177
      },
      "10": {
        name: "addSecurityHeaders",
        decl: {
          start: {
            line: 206,
            column: 16
          },
          end: {
            line: 206,
            column: 34
          }
        },
        loc: {
          start: {
            line: 206,
            column: 73
          },
          end: {
            line: 213,
            column: 1
          }
        },
        line: 206
      },
      "11": {
        name: "withErrorHandling",
        decl: {
          start: {
            line: 218,
            column: 16
          },
          end: {
            line: 218,
            column: 33
          }
        },
        loc: {
          start: {
            line: 220,
            column: 2
          },
          end: {
            line: 230,
            column: 1
          }
        },
        line: 220
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 221,
            column: 9
          },
          end: {
            line: 221,
            column: 10
          }
        },
        loc: {
          start: {
            line: 221,
            column: 70
          },
          end: {
            line: 229,
            column: 3
          }
        },
        line: 221
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 54,
            column: 2
          },
          end: {
            line: 54,
            column: 36
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 54,
            column: 23
          },
          end: {
            line: 54,
            column: 36
          }
        }],
        line: 54
      },
      "1": {
        loc: {
          start: {
            line: 83,
            column: 2
          },
          end: {
            line: 85,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 83,
            column: 2
          },
          end: {
            line: 85,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 83
      },
      "2": {
        loc: {
          start: {
            line: 83,
            column: 6
          },
          end: {
            line: 83,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 83,
            column: 6
          },
          end: {
            line: 83,
            column: 44
          }
        }, {
          start: {
            line: 83,
            column: 48
          },
          end: {
            line: 83,
            column: 55
          }
        }],
        line: 83
      },
      "3": {
        loc: {
          start: {
            line: 111,
            column: 18
          },
          end: {
            line: 113,
            column: 33
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 112,
            column: 6
          },
          end: {
            line: 112,
            column: 40
          }
        }, {
          start: {
            line: 113,
            column: 6
          },
          end: {
            line: 113,
            column: 33
          }
        }],
        line: 111
      },
      "4": {
        loc: {
          start: {
            line: 119,
            column: 4
          },
          end: {
            line: 119,
            column: 62
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 119,
            column: 45
          },
          end: {
            line: 119,
            column: 50
          }
        }, {
          start: {
            line: 119,
            column: 53
          },
          end: {
            line: 119,
            column: 62
          }
        }],
        line: 119
      },
      "5": {
        loc: {
          start: {
            line: 126,
            column: 43
          },
          end: {
            line: 126,
            column: 67
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 126,
            column: 53
          },
          end: {
            line: 126,
            column: 67
          }
        }],
        line: 126
      },
      "6": {
        loc: {
          start: {
            line: 137,
            column: 40
          },
          end: {
            line: 137,
            column: 61
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 137,
            column: 50
          },
          end: {
            line: 137,
            column: 61
          }
        }],
        line: 137
      },
      "7": {
        loc: {
          start: {
            line: 148,
            column: 39
          },
          end: {
            line: 148,
            column: 60
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 148,
            column: 50
          },
          end: {
            line: 148,
            column: 60
          }
        }],
        line: 148
      },
      "8": {
        loc: {
          start: {
            line: 181,
            column: 2
          },
          end: {
            line: 183,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 181,
            column: 2
          },
          end: {
            line: 183,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 181
      },
      "9": {
        loc: {
          start: {
            line: 186,
            column: 2
          },
          end: {
            line: 188,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 186,
            column: 2
          },
          end: {
            line: 188,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 186
      },
      "10": {
        loc: {
          start: {
            line: 186,
            column: 6
          },
          end: {
            line: 186,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 186,
            column: 6
          },
          end: {
            line: 186,
            column: 16
          }
        }, {
          start: {
            line: 186,
            column: 21
          },
          end: {
            line: 186,
            column: 48
          }
        }, {
          start: {
            line: 186,
            column: 52
          },
          end: {
            line: 186,
            column: 79
          }
        }],
        line: 186
      },
      "11": {
        loc: {
          start: {
            line: 191,
            column: 18
          },
          end: {
            line: 193,
            column: 29
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 192,
            column: 6
          },
          end: {
            line: 192,
            column: 46
          }
        }, {
          start: {
            line: 193,
            column: 6
          },
          end: {
            line: 193,
            column: 29
          }
        }],
        line: 191
      },
      "12": {
        loc: {
          start: {
            line: 192,
            column: 6
          },
          end: {
            line: 192,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 192,
            column: 6
          },
          end: {
            line: 192,
            column: 19
          }
        }, {
          start: {
            line: 192,
            column: 23
          },
          end: {
            line: 192,
            column: 46
          }
        }],
        line: 192
      },
      "13": {
        loc: {
          start: {
            line: 199,
            column: 4
          },
          end: {
            line: 199,
            column: 62
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 199,
            column: 45
          },
          end: {
            line: 199,
            column: 50
          }
        }, {
          start: {
            line: 199,
            column: 53
          },
          end: {
            line: 199,
            column: 62
          }
        }],
        line: 199
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0],
      "6": [0],
      "7": [0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "06f3146d983008b02566a2067bb934c9f6b7e3f2"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1d32atkwd8 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1d32atkwd8();
/**
 * Standardized API Response Utilities
 * 
 * This module provides consistent response formatting and error handling
 * for all API routes in the application.
 */

import { NextResponse } from 'next/server';
import { ZodError } from 'zod';

// Standard API response interface

// Error types for consistent error handling
export
/* istanbul ignore next */
let ApiErrorCode = /*#__PURE__*/function (ApiErrorCode) {
  ApiErrorCode["UNAUTHORIZED"] = "UNAUTHORIZED";
  ApiErrorCode["FORBIDDEN"] = "FORBIDDEN";
  ApiErrorCode["NOT_FOUND"] = "NOT_FOUND";
  ApiErrorCode["VALIDATION_ERROR"] = "VALIDATION_ERROR";
  ApiErrorCode["DATABASE_ERROR"] = "DATABASE_ERROR";
  ApiErrorCode["INTERNAL_ERROR"] = "INTERNAL_ERROR";
  ApiErrorCode["RATE_LIMITED"] = "RATE_LIMITED";
  ApiErrorCode["INVALID_INPUT"] = "INVALID_INPUT";
  ApiErrorCode["MULTIPLE_FOUND"] = "MULTIPLE_FOUND";
  return ApiErrorCode;
}({});

// HTTP status codes
export
/* istanbul ignore next */
let HttpStatus = /*#__PURE__*/function (HttpStatus) {
  HttpStatus[HttpStatus["OK"] = 200] = "OK";
  HttpStatus[HttpStatus["CREATED"] = 201] = "CREATED";
  HttpStatus[HttpStatus["BAD_REQUEST"] = 400] = "BAD_REQUEST";
  HttpStatus[HttpStatus["UNAUTHORIZED"] = 401] = "UNAUTHORIZED";
  HttpStatus[HttpStatus["FORBIDDEN"] = 403] = "FORBIDDEN";
  HttpStatus[HttpStatus["NOT_FOUND"] = 404] = "NOT_FOUND";
  HttpStatus[HttpStatus["CONFLICT"] = 409] = "CONFLICT";
  HttpStatus[HttpStatus["UNPROCESSABLE_ENTITY"] = 422] = "UNPROCESSABLE_ENTITY";
  HttpStatus[HttpStatus["TOO_MANY_REQUESTS"] = 429] = "TOO_MANY_REQUESTS";
  HttpStatus[HttpStatus["INTERNAL_SERVER_ERROR"] = 500] = "INTERNAL_SERVER_ERROR";
  return HttpStatus;
}({});

/**
 * Create a successful API response
 */
export function createSuccessResponse(data, message, status =
/* istanbul ignore next */
(cov_1d32atkwd8().b[0][0]++, HttpStatus.OK)) {
  /* istanbul ignore next */
  cov_1d32atkwd8().f[0]++;
  const response =
  /* istanbul ignore next */
  (cov_1d32atkwd8().s[0]++, {
    success: true,
    data,
    message,
    timestamp: new Date().toISOString()
  });
  /* istanbul ignore next */
  cov_1d32atkwd8().s[1]++;
  return NextResponse.json(response, {
    status
  });
}

/**
 * Create an error API response
 */
export function createErrorResponse(error, errorCode, status, details) {
  /* istanbul ignore next */
  cov_1d32atkwd8().f[1]++;
  const response =
  /* istanbul ignore next */
  (cov_1d32atkwd8().s[2]++, {
    success: false,
    error,
    errorCode,
    timestamp: new Date().toISOString()
  });

  // Add details in development mode only
  /* istanbul ignore next */
  cov_1d32atkwd8().s[3]++;
  if (
  /* istanbul ignore next */
  (cov_1d32atkwd8().b[2][0]++, process.env.NODE_ENV === 'development') &&
  /* istanbul ignore next */
  (cov_1d32atkwd8().b[2][1]++, details)) {
    /* istanbul ignore next */
    cov_1d32atkwd8().b[1][0]++;
    cov_1d32atkwd8().s[4]++;
    response.details = details;
  } else
  /* istanbul ignore next */
  {
    cov_1d32atkwd8().b[1][1]++;
  }
  cov_1d32atkwd8().s[5]++;
  return NextResponse.json(response, {
    status
  });
}

/**
 * Handle validation errors from Zod
 */
export function handleValidationError(error) {
  /* istanbul ignore next */
  cov_1d32atkwd8().f[2]++;
  const errorMessage =
  /* istanbul ignore next */
  (cov_1d32atkwd8().s[6]++, error.errors.map(err => {
    /* istanbul ignore next */
    cov_1d32atkwd8().f[3]++;
    cov_1d32atkwd8().s[7]++;
    return `${err.path.join('.')}: ${err.message}`;
  }).join(', '));
  /* istanbul ignore next */
  cov_1d32atkwd8().s[8]++;
  return createErrorResponse(`Validation failed: ${errorMessage}`, ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST, error.errors);
}

/**
 * Handle database errors
 */
export function handleDatabaseError(error) {
  /* istanbul ignore next */
  cov_1d32atkwd8().f[4]++;
  cov_1d32atkwd8().s[9]++;
  console.error('Database error:', error);

  // Don't expose internal database errors in production
  const message =
  /* istanbul ignore next */
  (cov_1d32atkwd8().s[10]++, process.env.NODE_ENV === 'development' ?
  /* istanbul ignore next */
  (cov_1d32atkwd8().b[3][0]++, `Database error: ${error.message}`) :
  /* istanbul ignore next */
  (cov_1d32atkwd8().b[3][1]++, 'A database error occurred'));
  /* istanbul ignore next */
  cov_1d32atkwd8().s[11]++;
  return createErrorResponse(message, ApiErrorCode.DATABASE_ERROR, HttpStatus.INTERNAL_SERVER_ERROR, process.env.NODE_ENV === 'development' ?
  /* istanbul ignore next */
  (cov_1d32atkwd8().b[4][0]++, error) :
  /* istanbul ignore next */
  (cov_1d32atkwd8().b[4][1]++, undefined));
}

/**
 * Handle unauthorized access
 */
export function createUnauthorizedResponse(message =
/* istanbul ignore next */
(cov_1d32atkwd8().b[5][0]++, 'Unauthorized')) {
  /* istanbul ignore next */
  cov_1d32atkwd8().f[5]++;
  cov_1d32atkwd8().s[12]++;
  return createErrorResponse(message, ApiErrorCode.UNAUTHORIZED, HttpStatus.UNAUTHORIZED);
}

/**
 * Handle forbidden access
 */
export function createForbiddenResponse(message =
/* istanbul ignore next */
(cov_1d32atkwd8().b[6][0]++, 'Forbidden')) {
  /* istanbul ignore next */
  cov_1d32atkwd8().f[6]++;
  cov_1d32atkwd8().s[13]++;
  return createErrorResponse(message, ApiErrorCode.FORBIDDEN, HttpStatus.FORBIDDEN);
}

/**
 * Handle not found errors
 */
export function createNotFoundResponse(resource =
/* istanbul ignore next */
(cov_1d32atkwd8().b[7][0]++, 'Resource')) {
  /* istanbul ignore next */
  cov_1d32atkwd8().f[7]++;
  cov_1d32atkwd8().s[14]++;
  return createErrorResponse(`${resource} not found`, ApiErrorCode.NOT_FOUND, HttpStatus.NOT_FOUND);
}

/**
 * Handle rate limiting
 */
export function createRateLimitResponse() {
  /* istanbul ignore next */
  cov_1d32atkwd8().f[8]++;
  const response =
  /* istanbul ignore next */
  (cov_1d32atkwd8().s[15]++, createErrorResponse('Too many requests', ApiErrorCode.RATE_LIMITED, HttpStatus.TOO_MANY_REQUESTS));

  // Add rate limit headers
  /* istanbul ignore next */
  cov_1d32atkwd8().s[16]++;
  response.headers.set('Retry-After', '60');
  /* istanbul ignore next */
  cov_1d32atkwd8().s[17]++;
  response.headers.set('X-RateLimit-Limit', '100');
  /* istanbul ignore next */
  cov_1d32atkwd8().s[18]++;
  response.headers.set('X-RateLimit-Remaining', '0');
  /* istanbul ignore next */
  cov_1d32atkwd8().s[19]++;
  return response;
}

/**
 * Generic error handler for API routes
 */
export function handleApiError(error) {
  /* istanbul ignore next */
  cov_1d32atkwd8().f[9]++;
  cov_1d32atkwd8().s[20]++;
  console.error('API Error:', error);

  // Handle specific error types
  /* istanbul ignore next */
  cov_1d32atkwd8().s[21]++;
  if (error instanceof ZodError) {
    /* istanbul ignore next */
    cov_1d32atkwd8().b[8][0]++;
    cov_1d32atkwd8().s[22]++;
    return handleValidationError(error);
  } else
  /* istanbul ignore next */
  {
    cov_1d32atkwd8().b[8][1]++;
  }

  // Handle database errors (you might want to check for specific database error types)
  cov_1d32atkwd8().s[23]++;
  if (
  /* istanbul ignore next */
  (cov_1d32atkwd8().b[10][0]++, error.code) && (
  /* istanbul ignore next */
  (cov_1d32atkwd8().b[10][1]++, error.code.startsWith('23')) ||
  /* istanbul ignore next */
  (cov_1d32atkwd8().b[10][2]++, error.code.startsWith('42')))) {
    /* istanbul ignore next */
    cov_1d32atkwd8().b[9][0]++;
    cov_1d32atkwd8().s[24]++;
    return handleDatabaseError(error);
  } else
  /* istanbul ignore next */
  {
    cov_1d32atkwd8().b[9][1]++;
  }

  // Generic internal server error
  const message =
  /* istanbul ignore next */
  (cov_1d32atkwd8().s[25]++, process.env.NODE_ENV === 'development' ?
  /* istanbul ignore next */
  (cov_1d32atkwd8().b[11][0]++,
  /* istanbul ignore next */
  (cov_1d32atkwd8().b[12][0]++, error.message) ||
  /* istanbul ignore next */
  (cov_1d32atkwd8().b[12][1]++, 'Internal server error')) :
  /* istanbul ignore next */
  (cov_1d32atkwd8().b[11][1]++, 'Internal server error'));
  /* istanbul ignore next */
  cov_1d32atkwd8().s[26]++;
  return createErrorResponse(message, ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR, process.env.NODE_ENV === 'development' ?
  /* istanbul ignore next */
  (cov_1d32atkwd8().b[13][0]++, error) :
  /* istanbul ignore next */
  (cov_1d32atkwd8().b[13][1]++, undefined));
}

/**
 * Add security headers to response
 */
export function addSecurityHeaders(response) {
  /* istanbul ignore next */
  cov_1d32atkwd8().f[10]++;
  cov_1d32atkwd8().s[27]++;
  response.headers.set('X-Content-Type-Options', 'nosniff');
  /* istanbul ignore next */
  cov_1d32atkwd8().s[28]++;
  response.headers.set('X-Frame-Options', 'DENY');
  /* istanbul ignore next */
  cov_1d32atkwd8().s[29]++;
  response.headers.set('X-XSS-Protection', '1; mode=block');
  /* istanbul ignore next */
  cov_1d32atkwd8().s[30]++;
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  /* istanbul ignore next */
  cov_1d32atkwd8().s[31]++;
  return response;
}

/**
 * Wrapper for API route handlers with error handling
 */
export function withErrorHandling(handler) {
  /* istanbul ignore next */
  cov_1d32atkwd8().f[11]++;
  cov_1d32atkwd8().s[32]++;
  return async (...args) => {
    /* istanbul ignore next */
    cov_1d32atkwd8().f[12]++;
    cov_1d32atkwd8().s[33]++;
    try {
      const response =
      /* istanbul ignore next */
      (cov_1d32atkwd8().s[34]++, await handler(...args));
      /* istanbul ignore next */
      cov_1d32atkwd8().s[35]++;
      return addSecurityHeaders(response);
    } catch (error) {
      const errorResponse =
      /* istanbul ignore next */
      (cov_1d32atkwd8().s[36]++, handleApiError(error));
      /* istanbul ignore next */
      cov_1d32atkwd8().s[37]++;
      return addSecurityHeaders(errorResponse);
    }
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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