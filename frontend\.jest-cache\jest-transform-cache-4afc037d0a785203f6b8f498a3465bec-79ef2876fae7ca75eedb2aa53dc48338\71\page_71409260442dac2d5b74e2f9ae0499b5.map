{"version": 3, "names": ["_jsxFileName", "React", "__jsx", "createElement", "cov_1m3coxoxpf", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "_coverageSchema", "coverage", "actualCoverage", "useEffect", "useState", "useRouter", "useSearchParams", "fetchAuthSession", "signInWithRedirect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "configureAmplify", "CallbackPage", "router", "searchParams", "error", "setError", "status", "setStatus", "isMounted", "handleCallback", "console", "log", "code", "get", "Error", "substring", "existingSession", "tokens", "idToken", "toString", "localStorage", "setItem", "push", "session", "forceRefresh", "message", "includes", "setTimeout", "className", "__self", "__source", "fileName", "lineNumber", "columnNumber"], "sources": ["page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useEffect, useState } from 'react'\r\nimport { useRouter, useSearchParams } from 'next/navigation'\r\nimport { fetchAuthSession, signInWithRedirect } from 'aws-amplify/auth'\r\nimport { setAuthCookie } from '@/lib/auth'\r\nimport { configureAmplify } from '@/lib/amplify-config'\r\n\r\nexport default function CallbackPage() {\r\n  const router = useRouter()\r\n  const searchParams = useSearchParams()\r\n  const [error, setError] = useState<string | null>(null)\r\n  const [status, setStatus] = useState('Completing sign-in...')\r\n\r\n  useEffect(() => {\r\n    let isMounted = true\r\n\r\n    async function handleCallback() {\r\n      try {\r\n        console.log('Callback page loaded, handling OAuth callback...')\r\n\r\n        // Ensure Amplify is configured\r\n        configureAmplify()\r\n\r\n        // Get the code from URL\r\n        const code = searchParams.get('code')\r\n        if (!code) {\r\n          throw new Error('No authorization code found in URL')\r\n        }\r\n\r\n        console.log('Authorization code found:', code.substring(0, 10) + '...')\r\n\r\n        try {\r\n          // Try to fetch the session first to see if we're already authenticated\r\n          const existingSession = await fetchAuthSession();\r\n          if (existingSession?.tokens?.idToken) {\r\n            console.log('User is already signed in, using existing session');\r\n            setAuthCookie(existingSession.tokens.idToken.toString());\r\n            localStorage.setItem('isAuthenticated', 'true');\r\n            router.push('/dashboard');\r\n            return;\r\n          }\r\n\r\n          // If not already authenticated, complete the sign-in\r\n          await signInWithRedirect();\r\n          console.log('Redirect handled successfully');\r\n\r\n          // Then fetch the session\r\n          const session = await fetchAuthSession({ forceRefresh: true });\r\n          console.log('Session fetched after handling redirect:', session ? 'success' : 'failed');\r\n\r\n          if (session?.tokens?.idToken) {\r\n            console.log('ID token found in session');\r\n            // Set the cookie with the token\r\n            setAuthCookie(session.tokens.idToken.toString());\r\n\r\n            // Set flag in localStorage for cross-tab auth state\r\n            localStorage.setItem('isAuthenticated', 'true');\r\n\r\n            // Redirect to dashboard\r\n            router.push('/dashboard');\r\n          } else {\r\n            throw new Error('No ID token found in session after handling redirect');\r\n          }\r\n        } catch (error: any) {\r\n          console.error('Error handling redirect:', error);\r\n          // If the error is because user is already signed in, redirect to dashboard\r\n          if (error.message && error.message.includes('already signed in')) {\r\n            console.log('User is already signed in, redirecting to dashboard');\r\n            // Try to get the session directly\r\n            const session = await fetchAuthSession({ forceRefresh: true });\r\n            if (session?.tokens?.idToken) {\r\n              setAuthCookie(session.tokens.idToken.toString());\r\n              localStorage.setItem('isAuthenticated', 'true');\r\n            }\r\n            router.push('/dashboard');\r\n            return;\r\n          }\r\n          throw error;\r\n        }\r\n      } catch (error: any) {\r\n        console.error('Authentication error:', error)\r\n        if (isMounted) {\r\n          setError(`Authentication failed: ${error.message}`)\r\n          setTimeout(() => {\r\n            router.push('/login')\r\n          }, 2000)\r\n        }\r\n      }\r\n    }\r\n\r\n    handleCallback()\r\n\r\n    return () => {\r\n      isMounted = false\r\n    }\r\n  }, [router, searchParams])\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center justify-center h-screen p-4\">\r\n      {error ? (\r\n        <div className=\"p-4 bg-red-100 border border-red-400 rounded max-w-md w-full\">\r\n          <p className=\"text-red-700\">{error}</p>\r\n          <p className=\"text-sm text-gray-600 mt-2\">Redirecting to login page...</p>\r\n        </div>\r\n      ) : (\r\n        <div className=\"p-4 text-center\">\r\n          <h1 className=\"text-2xl font-bold mb-4\">Welcome Back!</h1>\r\n          <p className=\"mb-4\">{status}</p>\r\n          <div className=\"w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin mx-auto\"></div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n\r\n"], "mappings": "AAAA,YAAY;;AAAA;AAAA,IAAAA,YAAA;AAAA,OAAAC,KAAA;AAAA,IAAAC,KAAA,GAAAD,KAAA,CAAAE,aAAA;AAAA,SAAAC,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,eAAA;IAAArB,IAAA;EAAA;EAAA,IAAAsB,QAAA,GAAArB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAmB,QAAA,CAAAvB,IAAA,KAAAuB,QAAA,CAAAvB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAsB,QAAA,CAAAvB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAmB,cAAA,GAAAD,QAAA,CAAAvB,IAAA;EAAA;IAeA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAyB,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAzB,cAAA;AAbZ,SAAS0B,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,SAAS,EAAEC,eAAe,QAAQ,iBAAiB;AAC5D,SAASC,gBAAgB,EAAEC,kBAAkB,QAAQ,kBAAkB;AACvE,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,gBAAgB,QAAQ,sBAAsB;AAEvD,eAAe,SAASC,YAAYA,CAAA,EAAG;EAAA;EAAAlC,cAAA,GAAAqB,CAAA;EACrC,MAAMc,MAAM;EAAA;EAAA,CAAAnC,cAAA,GAAAoB,CAAA,OAAGQ,SAAS,CAAC,CAAC;EAC1B,MAAMQ,YAAY;EAAA;EAAA,CAAApC,cAAA,GAAAoB,CAAA,OAAGS,eAAe,CAAC,CAAC;EACtC,MAAM,CAACQ,KAAK,EAAEC,QAAQ,CAAC;EAAA;EAAA,CAAAtC,cAAA,GAAAoB,CAAA,OAAGO,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC;EAAA;EAAA,CAAAxC,cAAA,GAAAoB,CAAA,OAAGO,QAAQ,CAAC,uBAAuB,CAAC;EAAA;EAAA3B,cAAA,GAAAoB,CAAA;EAE7DM,SAAS,CAAC,MAAM;IAAA;IAAA1B,cAAA,GAAAqB,CAAA;IACd,IAAIoB,SAAS;IAAA;IAAA,CAAAzC,cAAA,GAAAoB,CAAA,OAAG,IAAI;IAEpB,eAAesB,cAAcA,CAAA,EAAG;MAAA;MAAA1C,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAC9B,IAAI;QAAA;QAAApB,cAAA,GAAAoB,CAAA;QACFuB,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;;QAE/D;QAAA;QAAA5C,cAAA,GAAAoB,CAAA;QACAa,gBAAgB,CAAC,CAAC;;QAElB;QACA,MAAMY,IAAI;QAAA;QAAA,CAAA7C,cAAA,GAAAoB,CAAA,OAAGgB,YAAY,CAACU,GAAG,CAAC,MAAM,CAAC;QAAA;QAAA9C,cAAA,GAAAoB,CAAA;QACrC,IAAI,CAACyB,IAAI,EAAE;UAAA;UAAA7C,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACT,MAAM,IAAI2B,KAAK,CAAC,oCAAoC,CAAC;QACvD,CAAC;QAAA;QAAA;UAAA/C,cAAA,GAAAsB,CAAA;QAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAEDuB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEC,IAAI,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;QAAA;QAAAhD,cAAA,GAAAoB,CAAA;QAEvE,IAAI;UACF;UACA,MAAM6B,eAAe;UAAA;UAAA,CAAAjD,cAAA,GAAAoB,CAAA,QAAG,MAAMU,gBAAgB,CAAC,CAAC;UAAC;UAAA9B,cAAA,GAAAoB,CAAA;UACjD,IAAI6B,eAAe,EAAEC,MAAM,EAAEC,OAAO,EAAE;YAAA;YAAAnD,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACpCuB,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;YAAC;YAAA5C,cAAA,GAAAoB,CAAA;YACjEY,aAAa,CAACiB,eAAe,CAACC,MAAM,CAACC,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC;YAAC;YAAApD,cAAA,GAAAoB,CAAA;YACzDiC,YAAY,CAACC,OAAO,CAAC,iBAAiB,EAAE,MAAM,CAAC;YAAC;YAAAtD,cAAA,GAAAoB,CAAA;YAChDe,MAAM,CAACoB,IAAI,CAAC,YAAY,CAAC;YAAC;YAAAvD,cAAA,GAAAoB,CAAA;YAC1B;UACF,CAAC;UAAA;UAAA;YAAApB,cAAA,GAAAsB,CAAA;UAAA;;UAED;UAAAtB,cAAA,GAAAoB,CAAA;UACA,MAAMW,kBAAkB,CAAC,CAAC;UAAC;UAAA/B,cAAA,GAAAoB,CAAA;UAC3BuB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;;UAE5C;UACA,MAAMY,OAAO;UAAA;UAAA,CAAAxD,cAAA,GAAAoB,CAAA,QAAG,MAAMU,gBAAgB,CAAC;YAAE2B,YAAY,EAAE;UAAK,CAAC,CAAC;UAAC;UAAAzD,cAAA,GAAAoB,CAAA;UAC/DuB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEY,OAAO;UAAA;UAAA,CAAAxD,cAAA,GAAAsB,CAAA,UAAG,SAAS;UAAA;UAAA,CAAAtB,cAAA,GAAAsB,CAAA,UAAG,QAAQ,EAAC;UAAC;UAAAtB,cAAA,GAAAoB,CAAA;UAExF,IAAIoC,OAAO,EAAEN,MAAM,EAAEC,OAAO,EAAE;YAAA;YAAAnD,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAC5BuB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;YACxC;YAAA;YAAA5C,cAAA,GAAAoB,CAAA;YACAY,aAAa,CAACwB,OAAO,CAACN,MAAM,CAACC,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC;;YAEhD;YAAA;YAAApD,cAAA,GAAAoB,CAAA;YACAiC,YAAY,CAACC,OAAO,CAAC,iBAAiB,EAAE,MAAM,CAAC;;YAE/C;YAAA;YAAAtD,cAAA,GAAAoB,CAAA;YACAe,MAAM,CAACoB,IAAI,CAAC,YAAY,CAAC;UAC3B,CAAC,MAAM;YAAA;YAAAvD,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACL,MAAM,IAAI2B,KAAK,CAAC,sDAAsD,CAAC;UACzE;QACF,CAAC,CAAC,OAAOV,KAAU,EAAE;UAAA;UAAArC,cAAA,GAAAoB,CAAA;UACnBuB,OAAO,CAACN,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChD;UAAA;UAAArC,cAAA,GAAAoB,CAAA;UACA;UAAI;UAAA,CAAApB,cAAA,GAAAsB,CAAA,UAAAe,KAAK,CAACqB,OAAO;UAAA;UAAA,CAAA1D,cAAA,GAAAsB,CAAA,UAAIe,KAAK,CAACqB,OAAO,CAACC,QAAQ,CAAC,mBAAmB,CAAC,GAAE;YAAA;YAAA3D,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAChEuB,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;YAClE;YACA,MAAMY,OAAO;YAAA;YAAA,CAAAxD,cAAA,GAAAoB,CAAA,QAAG,MAAMU,gBAAgB,CAAC;cAAE2B,YAAY,EAAE;YAAK,CAAC,CAAC;YAAC;YAAAzD,cAAA,GAAAoB,CAAA;YAC/D,IAAIoC,OAAO,EAAEN,MAAM,EAAEC,OAAO,EAAE;cAAA;cAAAnD,cAAA,GAAAsB,CAAA;cAAAtB,cAAA,GAAAoB,CAAA;cAC5BY,aAAa,CAACwB,OAAO,CAACN,MAAM,CAACC,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC;cAAC;cAAApD,cAAA,GAAAoB,CAAA;cACjDiC,YAAY,CAACC,OAAO,CAAC,iBAAiB,EAAE,MAAM,CAAC;YACjD,CAAC;YAAA;YAAA;cAAAtD,cAAA,GAAAsB,CAAA;YAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACDe,MAAM,CAACoB,IAAI,CAAC,YAAY,CAAC;YAAC;YAAAvD,cAAA,GAAAoB,CAAA;YAC1B;UACF,CAAC;UAAA;UAAA;YAAApB,cAAA,GAAAsB,CAAA;UAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACD,MAAMiB,KAAK;QACb;MACF,CAAC,CAAC,OAAOA,KAAU,EAAE;QAAA;QAAArC,cAAA,GAAAoB,CAAA;QACnBuB,OAAO,CAACN,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAAA;QAAArC,cAAA,GAAAoB,CAAA;QAC7C,IAAIqB,SAAS,EAAE;UAAA;UAAAzC,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACbkB,QAAQ,CAAC,0BAA0BD,KAAK,CAACqB,OAAO,EAAE,CAAC;UAAA;UAAA1D,cAAA,GAAAoB,CAAA;UACnDwC,UAAU,CAAC,MAAM;YAAA;YAAA5D,cAAA,GAAAqB,CAAA;YAAArB,cAAA,GAAAoB,CAAA;YACfe,MAAM,CAACoB,IAAI,CAAC,QAAQ,CAAC;UACvB,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QAAA;QAAA;UAAAvD,cAAA,GAAAsB,CAAA;QAAA;MACH;IACF;IAAC;IAAAtB,cAAA,GAAAoB,CAAA;IAEDsB,cAAc,CAAC,CAAC;IAAA;IAAA1C,cAAA,GAAAoB,CAAA;IAEhB,OAAO,MAAM;MAAA;MAAApB,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MACXqB,SAAS,GAAG,KAAK;IACnB,CAAC;EACH,CAAC,EAAE,CAACN,MAAM,EAAEC,YAAY,CAAC,CAAC;EAAA;EAAApC,cAAA,GAAAoB,CAAA;EAE1B,OACE,0BAAAtB,KAAA;EAAA;EAAA;EAAA;EAAA;IAAK+D,SAAS,EAAC,wDAAwD;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAApE,YAAA;MAAAqE,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpE7B,KAAK;EAAA;EAAA,CAAArC,cAAA,GAAAsB,CAAA;EACJ;EAAAxB,KAAA;EAAA;EAAA;EAAA;EAAA;IAAK+D,SAAS,EAAC,8DAA8D;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAApE,YAAA;MAAAqE,UAAA;MAAAC,YAAA;IAAA;EAAA;EAC3E;EAAApE,KAAA;EAAA;EAAA;EAAA;EAAA;IAAG+D,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAApE,YAAA;MAAAqE,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE7B,KAAS,CAAC;EACvC;EAAAvC,KAAA;EAAA;EAAA;EAAA;EAAA;IAAG+D,SAAS,EAAC,4BAA4B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAApE,YAAA;MAAAqE,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,8BAA+B,CACtE,CAAC;EAAA;EAAA,CAAAlE,cAAA,GAAAsB,CAAA;EAEN;EAAAxB,KAAA;EAAA;EAAA;EAAA;EAAA;IAAK+D,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAApE,YAAA;MAAAqE,UAAA;MAAAC,YAAA;IAAA;EAAA;EAC9B;EAAApE,KAAA;EAAA;EAAA;EAAA;EAAA;IAAI+D,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAApE,YAAA;MAAAqE,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAAiB,CAAC;EAC1D;EAAApE,KAAA;EAAA;EAAA;EAAA;EAAA;IAAG+D,SAAS,EAAC,MAAM;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAApE,YAAA;MAAAqE,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE3B,MAAU,CAAC;EAChC;EAAAzC,KAAA;EAAA;EAAA;EAAA;EAAA;IAAK+D,SAAS,EAAC,qFAAqF;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAApE,YAAA;MAAAqE,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAM,CACvG,CAAC,CAEL,CAAC;AAEV", "ignoreList": []}