{"version": 3, "names": ["describe", "it", "expect", "toBe", "result", "Promise", "resolve", "obj", "name", "value", "toEqual"], "sources": ["basic.test.js"], "sourcesContent": ["/**\n * Basic Test\n * \n * Simple test to verify Jest setup is working\n */\n\ndescribe('Basic Test Suite', () => {\n  it('should run basic tests', () => {\n    expect(1 + 1).toBe(2)\n  })\n\n  it('should handle async operations', async () => {\n    const result = await Promise.resolve('test')\n    expect(result).toBe('test')\n  })\n\n  it('should work with objects', () => {\n    const obj = { name: 'test', value: 123 }\n    expect(obj).toEqual({ name: 'test', value: 123 })\n  })\n})\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEAA,QAAQ,CAAC,kBAAkB,EAAE,MAAM;EACjCC,EAAE,CAAC,wBAAwB,EAAE,MAAM;IACjCC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC;EAEFF,EAAE,CAAC,gCAAgC,EAAE,YAAY;IAC/C,MAAMG,MAAM,GAAG,MAAMC,OAAO,CAACC,OAAO,CAAC,MAAM,CAAC;IAC5CJ,MAAM,CAACE,MAAM,CAAC,CAACD,IAAI,CAAC,MAAM,CAAC;EAC7B,CAAC,CAAC;EAEFF,EAAE,CAAC,0BAA0B,EAAE,MAAM;IACnC,MAAMM,GAAG,GAAG;MAAEC,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAI,CAAC;IACxCP,MAAM,CAACK,GAAG,CAAC,CAACG,OAAO,CAAC;MAAEF,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAI,CAAC,CAAC;EACnD,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}