6e60ec4168f8ef5a98f00341de5ff664
/**
 * Dashboard Header Component
 * 
 * Contains the dashboard title, subtitle, search functionality, and action buttons.
 * Focused responsibility: Header section with search and actions.
 */

'use client';

/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\DashboardHeader.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_22o17h8if() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\DashboardHeader.tsx";
  var hash = "bdcdf8593918a19de21620478f3d26a8d70083c6";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\DashboardHeader.tsx",
    statementMap: {
      "0": {
        start: {
          line: 28,
          column: 40
        },
        end: {
          line: 28,
          column: 52
        }
      },
      "1": {
        start: {
          line: 30,
          column: 29
        },
        end: {
          line: 34,
          column: 3
        }
      },
      "2": {
        start: {
          line: 31,
          column: 18
        },
        end: {
          line: 31,
          column: 32
        }
      },
      "3": {
        start: {
          line: 32,
          column: 4
        },
        end: {
          line: 32,
          column: 25
        }
      },
      "4": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 33,
          column: 21
        }
      },
      "5": {
        start: {
          line: 36,
          column: 29
        },
        end: {
          line: 39,
          column: 3
        }
      },
      "6": {
        start: {
          line: 37,
          column: 4
        },
        end: {
          line: 37,
          column: 22
        }
      },
      "7": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 38,
          column: 27
        }
      },
      "8": {
        start: {
          line: 41,
          column: 27
        },
        end: {
          line: 43,
          column: 3
        }
      },
      "9": {
        start: {
          line: 42,
          column: 4
        },
        end: {
          line: 42,
          column: 20
        }
      },
      "10": {
        start: {
          line: 45,
          column: 2
        },
        end: {
          line: 87,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "DashboardHeader",
        decl: {
          start: {
            line: 20,
            column: 24
          },
          end: {
            line: 20,
            column: 39
          }
        },
        loc: {
          start: {
            line: 27,
            column: 25
          },
          end: {
            line: 88,
            column: 1
          }
        },
        line: 27
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 30,
            column: 29
          },
          end: {
            line: 30,
            column: 30
          }
        },
        loc: {
          start: {
            line: 30,
            column: 73
          },
          end: {
            line: 34,
            column: 3
          }
        },
        line: 30
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 36,
            column: 29
          },
          end: {
            line: 36,
            column: 30
          }
        },
        loc: {
          start: {
            line: 36,
            column: 53
          },
          end: {
            line: 39,
            column: 3
          }
        },
        line: 36
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 41,
            column: 27
          },
          end: {
            line: 41,
            column: 28
          }
        },
        loc: {
          start: {
            line: 41,
            column: 33
          },
          end: {
            line: 43,
            column: 3
          }
        },
        line: 41
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 21,
            column: 2
          },
          end: {
            line: 21,
            column: 31
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 21,
            column: 15
          },
          end: {
            line: 21,
            column: 31
          }
        }],
        line: 21
      },
      "1": {
        loc: {
          start: {
            line: 24,
            column: 2
          },
          end: {
            line: 24,
            column: 42
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 24,
            column: 22
          },
          end: {
            line: 24,
            column: 42
          }
        }],
        line: 24
      },
      "2": {
        loc: {
          start: {
            line: 25,
            column: 2
          },
          end: {
            line: 25,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 25,
            column: 14
          },
          end: {
            line: 25,
            column: 16
          }
        }],
        line: 25
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "bdcdf8593918a19de21620478f3d26a8d70083c6"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_22o17h8if = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_22o17h8if();
import { useState } from 'react';
export default function DashboardHeader({
  clientName =
  /* istanbul ignore next */
  (cov_22o17h8if().b[0][0]++, 'Unknown Client'),
  onSearch,
  onAddRenewal,
  searchPlaceholder =
  /* istanbul ignore next */
  (cov_22o17h8if().b[1][0]++, 'Search renewals...'),
  className =
  /* istanbul ignore next */
  (cov_22o17h8if().b[2][0]++, ''),
  'data-testid': testId
}) {
  /* istanbul ignore next */
  cov_22o17h8if().f[0]++;
  const [searchQuery, setSearchQuery] =
  /* istanbul ignore next */
  (cov_22o17h8if().s[0]++, useState(''));
  /* istanbul ignore next */
  cov_22o17h8if().s[1]++;
  const handleSearchChange = e => {
    /* istanbul ignore next */
    cov_22o17h8if().f[1]++;
    const query =
    /* istanbul ignore next */
    (cov_22o17h8if().s[2]++, e.target.value);
    /* istanbul ignore next */
    cov_22o17h8if().s[3]++;
    setSearchQuery(query);
    /* istanbul ignore next */
    cov_22o17h8if().s[4]++;
    onSearch?.(query);
  };
  /* istanbul ignore next */
  cov_22o17h8if().s[5]++;
  const handleSearchSubmit = e => {
    /* istanbul ignore next */
    cov_22o17h8if().f[2]++;
    cov_22o17h8if().s[6]++;
    e.preventDefault();
    /* istanbul ignore next */
    cov_22o17h8if().s[7]++;
    onSearch?.(searchQuery);
  };
  /* istanbul ignore next */
  cov_22o17h8if().s[8]++;
  const handleAddRenewal = () => {
    /* istanbul ignore next */
    cov_22o17h8if().f[3]++;
    cov_22o17h8if().s[9]++;
    onAddRenewal?.();
  };
  /* istanbul ignore next */
  cov_22o17h8if().s[10]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: `dashboard-header ${className}`,
    /* istanbul ignore next */
    "data-testid": testId,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 46,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "dashboard-title-section",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 50,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h1",
  /* istanbul ignore next */
  {
    className: "dashboard-title",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 51,
      columnNumber: 9
    }
  }, "Dashboard - ", clientName),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "dashboard-subtitle",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 54,
      columnNumber: 9
    }
  }, "Manage your subscriptions, maintenance, support and warranties")),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "dashboard-actions",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 59,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "form",
  /* istanbul ignore next */
  {
    onSubmit: handleSearchSubmit,
    className: "search-container",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 60,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "input",
  /* istanbul ignore next */
  {
    type: "text",
    placeholder: searchPlaceholder,
    className: "search-input",
    value: searchQuery,
    onChange: handleSearchChange,
    /* istanbul ignore next */
    "aria-label": "Search renewals",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 61,
      columnNumber: 11
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  {
    type: "submit",
    className: "search-icon",
    /* istanbul ignore next */
    "aria-label": "Submit search",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 69,
      columnNumber: 11
    }
  }, "\uD83D\uDD0D")),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  {
    className: "btn btn-primary",
    onClick: handleAddRenewal,
    /* istanbul ignore next */
    "aria-label": "Add new renewal",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 78,
      columnNumber: 9
    }
  }, "+ Add Renewal")));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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