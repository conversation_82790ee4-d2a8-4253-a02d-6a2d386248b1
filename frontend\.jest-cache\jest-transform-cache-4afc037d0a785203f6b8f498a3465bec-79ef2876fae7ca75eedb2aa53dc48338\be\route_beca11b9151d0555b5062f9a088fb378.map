{"version": 3, "names": ["cov_2bw1277yma", "actualCoverage", "requireAuth", "requireRole", "createSuccessResponse", "createErrorResponse", "createNotFoundResponse", "ApiErrorCode", "HttpStatus", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validateRequestBody", "validatePathParams", "userUpdateSchema", "idParamSchema", "executeQuerySingle", "GET", "s", "request", "params", "f", "authResult", "success", "b", "response", "session", "paramValidation", "id", "data", "isAdmin", "roles", "includes", "isOwnData", "userId", "FORBIDDEN", "query", "result", "DATABASE_ERROR", "INTERNAL_SERVER_ERROR", "userData", "last_login", "PUT", "roleResult", "bodyValidation", "updateData", "currentUser<PERSON><PERSON><PERSON>", "currentUser", "updateFields", "values", "paramIndex", "name", "undefined", "push", "given_name", "family_name", "JSON", "stringify", "length", "VALIDATION_ERROR", "BAD_REQUEST", "version", "updateQuery", "join", "updateResult", "CONFLICT", "console", "log", "email", "DELETE", "deleteQuery", "deleteResult"], "sources": ["route.ts"], "sourcesContent": ["/**\n * Individual User API Route\n * \n * This route demonstrates CRUD operations for individual users with:\n * - Path parameter validation\n * - Role-based access control\n * - Optimistic concurrency control\n * - Audit logging\n */\n\nimport { NextRequest } from 'next/server';\nimport { requireAuth, requireRole } from '@/lib/auth-middleware';\nimport { \n  createSuccessResponse, \n  createErrorResponse, \n  createNotFoundResponse,\n  ApiErrorCode, \n  HttpStatus,\n  withErrorHandling \n} from '@/lib/api-response';\nimport { \n  validateRequestBody, \n  validatePathParams,\n  userUpdateSchema,\n  idParamSchema,\n  UserUpdateData,\n  IdParam \n} from '@/lib/validation';\nimport { executeQuerySingle, executeTransaction } from '@/lib/database';\n\ninterface User {\n  id: string;\n  email: string;\n  name?: string;\n  given_name?: string;\n  family_name?: string;\n  roles: string[];\n  created_at: Date;\n  updated_at?: Date;\n  last_login?: Date;\n  version: number; // For optimistic concurrency control\n}\n\n// GET /api/users/[id] - Get specific user\nexport const GET = withErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) => {\n  // Verify authentication\n  const authResult = await requireAuth();\n  if (!authResult.success) {\n    return authResult.response!;\n  }\n\n  const session = authResult.session!;\n\n  // Validate path parameters\n  const paramValidation = validatePathParams(params, idParamSchema);\n  if (!paramValidation.success) {\n    return paramValidation.response;\n  }\n\n  const { id }: IdParam = paramValidation.data;\n\n  // Check if user can access this resource\n  // Users can access their own data, admins can access any user\n  const isAdmin = session.roles.includes('admin') || session.roles.includes('user_manager');\n  const isOwnData = session.userId === id;\n\n  if (!isAdmin && !isOwnData) {\n    return createErrorResponse(\n      'Access denied',\n      ApiErrorCode.FORBIDDEN,\n      HttpStatus.FORBIDDEN\n    );\n  }\n\n  // Fetch user data\n  const query = `\n    SELECT \n      id,\n      email,\n      name,\n      given_name,\n      family_name,\n      roles,\n      created_at,\n      updated_at,\n      last_login,\n      version\n    FROM users \n    WHERE id = $1\n  `;\n\n  const result = await executeQuerySingle<User>(query, [id]);\n\n  if (!result.success) {\n    return createErrorResponse(\n      'Failed to fetch user',\n      ApiErrorCode.DATABASE_ERROR,\n      HttpStatus.INTERNAL_SERVER_ERROR\n    );\n  }\n\n  if (!result.data) {\n    return createNotFoundResponse('User');\n  }\n\n  // Remove sensitive data if not admin and not own data\n  const userData = result.data;\n  if (!isAdmin && !isOwnData) {\n    // Remove sensitive fields for non-admin users\n    delete (userData as any).roles;\n    delete (userData as any).last_login;\n  }\n\n  return createSuccessResponse(userData, 'User retrieved successfully');\n});\n\n// PUT /api/users/[id] - Update specific user\nexport const PUT = withErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) => {\n  // Verify authentication and role\n  const roleResult = await requireRole(['admin', 'user_manager']);\n  if (!roleResult.success) {\n    return roleResult.response!;\n  }\n\n  const session = roleResult.session!;\n\n  // Validate path parameters\n  const paramValidation = validatePathParams(params, idParamSchema);\n  if (!paramValidation.success) {\n    return paramValidation.response;\n  }\n\n  const { id }: IdParam = paramValidation.data;\n\n  // Validate request body\n  const bodyValidation = await validateRequestBody(request, userUpdateSchema);\n  if (!bodyValidation.success) {\n    return bodyValidation.response;\n  }\n\n  const updateData: UserUpdateData = bodyValidation.data;\n\n  // Check if user exists and get current version\n  const currentUserQuery = 'SELECT id, version FROM users WHERE id = $1';\n  const currentUser = await executeQuerySingle<{ id: string; version: number }>(\n    currentUserQuery, \n    [id]\n  );\n\n  if (!currentUser.success) {\n    return createErrorResponse(\n      'Failed to fetch user',\n      ApiErrorCode.DATABASE_ERROR,\n      HttpStatus.INTERNAL_SERVER_ERROR\n    );\n  }\n\n  if (!currentUser.data) {\n    return createNotFoundResponse('User');\n  }\n\n  // Build dynamic update query\n  const updateFields: string[] = [];\n  const values: any[] = [];\n  let paramIndex = 1;\n\n  if (updateData.name !== undefined) {\n    updateFields.push(`name = $${paramIndex++}`);\n    values.push(updateData.name);\n  }\n\n  if (updateData.given_name !== undefined) {\n    updateFields.push(`given_name = $${paramIndex++}`);\n    values.push(updateData.given_name);\n  }\n\n  if (updateData.family_name !== undefined) {\n    updateFields.push(`family_name = $${paramIndex++}`);\n    values.push(updateData.family_name);\n  }\n\n  if (updateData.roles !== undefined) {\n    updateFields.push(`roles = $${paramIndex++}`);\n    values.push(JSON.stringify(updateData.roles));\n  }\n\n  if (updateFields.length === 0) {\n    return createErrorResponse(\n      'No valid fields to update',\n      ApiErrorCode.VALIDATION_ERROR,\n      HttpStatus.BAD_REQUEST\n    );\n  }\n\n  // Add updated_at, version increment, and WHERE clause\n  updateFields.push(`updated_at = CURRENT_TIMESTAMP`);\n  updateFields.push(`version = version + 1`);\n  \n  // Add WHERE conditions\n  values.push(id, currentUser.data.version);\n\n  const updateQuery = `\n    UPDATE users \n    SET ${updateFields.join(', ')}\n    WHERE id = $${paramIndex++} AND version = $${paramIndex++}\n    RETURNING \n      id,\n      email,\n      name,\n      given_name,\n      family_name,\n      roles,\n      created_at,\n      updated_at,\n      version\n  `;\n\n  const updateResult = await executeQuerySingle<User>(updateQuery, values);\n\n  if (!updateResult.success) {\n    return createErrorResponse(\n      'Failed to update user',\n      ApiErrorCode.DATABASE_ERROR,\n      HttpStatus.INTERNAL_SERVER_ERROR\n    );\n  }\n\n  if (!updateResult.data) {\n    return createErrorResponse(\n      'User was modified by another process. Please refresh and try again.',\n      ApiErrorCode.VALIDATION_ERROR,\n      HttpStatus.CONFLICT\n    );\n  }\n\n  // Log user update for audit\n  console.log(`User updated: ${id} by ${session.email}`, updateData);\n\n  return createSuccessResponse(updateResult.data, 'User updated successfully');\n});\n\n// DELETE /api/users/[id] - Delete specific user\nexport const DELETE = withErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) => {\n  // Verify authentication and admin role\n  const roleResult = await requireRole(['admin']);\n  if (!roleResult.success) {\n    return roleResult.response!;\n  }\n\n  const session = roleResult.session!;\n\n  // Validate path parameters\n  const paramValidation = validatePathParams(params, idParamSchema);\n  if (!paramValidation.success) {\n    return paramValidation.response;\n  }\n\n  const { id }: IdParam = paramValidation.data;\n\n  // Prevent self-deletion\n  if (session.userId === id) {\n    return createErrorResponse(\n      'Cannot delete your own account',\n      ApiErrorCode.VALIDATION_ERROR,\n      HttpStatus.BAD_REQUEST\n    );\n  }\n\n  // Soft delete user (mark as inactive instead of hard delete)\n  const deleteQuery = `\n    UPDATE users \n    SET \n      status = 'deleted',\n      updated_at = CURRENT_TIMESTAMP,\n      version = version + 1\n    WHERE id = $1\n    RETURNING id, email\n  `;\n\n  const deleteResult = await executeQuerySingle<{ id: string; email: string }>(\n    deleteQuery, \n    [id]\n  );\n\n  if (!deleteResult.success) {\n    return createErrorResponse(\n      'Failed to delete user',\n      ApiErrorCode.DATABASE_ERROR,\n      HttpStatus.INTERNAL_SERVER_ERROR\n    );\n  }\n\n  if (!deleteResult.data) {\n    return createNotFoundResponse('User');\n  }\n\n  // Log user deletion for audit\n  console.log(`User deleted: ${deleteResult.data.email} by ${session.email}`);\n\n  return createSuccessResponse(\n    { id: deleteResult.data.id },\n    'User deleted successfully'\n  );\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASE,WAAW,EAAEC,WAAW,QAAQ,uBAAuB;AAChE,SACEC,qBAAqB,EACrBC,mBAAmB,EACnBC,sBAAsB,EACtBC,YAAY,EACZC,UAAU,EACVC,iBAAiB,QACZ,oBAAoB;AAC3B,SACEC,mBAAmB,EACnBC,kBAAkB,EAClBC,gBAAgB,EAChBC,aAAa,QAGR,kBAAkB;AACzB,SAASC,kBAAkB,QAA4B,gBAAgB;AAevE;AACA,OAAO,MAAMC,GAAG;AAAA;AAAA,CAAAf,cAAA,GAAAgB,CAAA,OAAGP,iBAAiB,CAAC,OACnCQ,OAAoB,EACpB;EAAEC;AAAmC,CAAC,KACnC;EAAA;EAAAlB,cAAA,GAAAmB,CAAA;EACH;EACA,MAAMC,UAAU;EAAA;EAAA,CAAApB,cAAA,GAAAgB,CAAA,OAAG,MAAMd,WAAW,CAAC,CAAC;EAAC;EAAAF,cAAA,GAAAgB,CAAA;EACvC,IAAI,CAACI,UAAU,CAACC,OAAO,EAAE;IAAA;IAAArB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IACvB,OAAOI,UAAU,CAACG,QAAQ;EAC5B,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED,MAAME,OAAO;EAAA;EAAA,CAAAxB,cAAA,GAAAgB,CAAA,OAAGI,UAAU,CAACI,OAAO,CAAC;;EAEnC;EACA,MAAMC,eAAe;EAAA;EAAA,CAAAzB,cAAA,GAAAgB,CAAA,OAAGL,kBAAkB,CAACO,MAAM,EAAEL,aAAa,CAAC;EAAC;EAAAb,cAAA,GAAAgB,CAAA;EAClE,IAAI,CAACS,eAAe,CAACJ,OAAO,EAAE;IAAA;IAAArB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IAC5B,OAAOS,eAAe,CAACF,QAAQ;EACjC,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED,MAAM;IAAEI;EAAY,CAAC;EAAA;EAAA,CAAA1B,cAAA,GAAAgB,CAAA,OAAGS,eAAe,CAACE,IAAI;;EAE5C;EACA;EACA,MAAMC,OAAO;EAAA;EAAA,CAAA5B,cAAA,GAAAgB,CAAA;EAAG;EAAA,CAAAhB,cAAA,GAAAsB,CAAA,UAAAE,OAAO,CAACK,KAAK,CAACC,QAAQ,CAAC,OAAO,CAAC;EAAA;EAAA,CAAA9B,cAAA,GAAAsB,CAAA,UAAIE,OAAO,CAACK,KAAK,CAACC,QAAQ,CAAC,cAAc,CAAC;EACzF,MAAMC,SAAS;EAAA;EAAA,CAAA/B,cAAA,GAAAgB,CAAA,QAAGQ,OAAO,CAACQ,MAAM,KAAKN,EAAE;EAAC;EAAA1B,cAAA,GAAAgB,CAAA;EAExC;EAAI;EAAA,CAAAhB,cAAA,GAAAsB,CAAA,WAACM,OAAO;EAAA;EAAA,CAAA5B,cAAA,GAAAsB,CAAA,UAAI,CAACS,SAAS,GAAE;IAAA;IAAA/B,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IAC1B,OAAOX,mBAAmB,CACxB,eAAe,EACfE,YAAY,CAAC0B,SAAS,EACtBzB,UAAU,CAACyB,SACb,CAAC;EACH,CAAC;EAAA;EAAA;IAAAjC,cAAA,GAAAsB,CAAA;EAAA;;EAED;EACA,MAAMY,KAAK;EAAA;EAAA,CAAAlC,cAAA,GAAAgB,CAAA,QAAG;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;EAED,MAAMmB,MAAM;EAAA;EAAA,CAAAnC,cAAA,GAAAgB,CAAA,QAAG,MAAMF,kBAAkB,CAAOoB,KAAK,EAAE,CAACR,EAAE,CAAC,CAAC;EAAC;EAAA1B,cAAA,GAAAgB,CAAA;EAE3D,IAAI,CAACmB,MAAM,CAACd,OAAO,EAAE;IAAA;IAAArB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IACnB,OAAOX,mBAAmB,CACxB,sBAAsB,EACtBE,YAAY,CAAC6B,cAAc,EAC3B5B,UAAU,CAAC6B,qBACb,CAAC;EACH,CAAC;EAAA;EAAA;IAAArC,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAgB,CAAA;EAED,IAAI,CAACmB,MAAM,CAACR,IAAI,EAAE;IAAA;IAAA3B,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IAChB,OAAOV,sBAAsB,CAAC,MAAM,CAAC;EACvC,CAAC;EAAA;EAAA;IAAAN,cAAA,GAAAsB,CAAA;EAAA;;EAED;EACA,MAAMgB,QAAQ;EAAA;EAAA,CAAAtC,cAAA,GAAAgB,CAAA,QAAGmB,MAAM,CAACR,IAAI;EAAC;EAAA3B,cAAA,GAAAgB,CAAA;EAC7B;EAAI;EAAA,CAAAhB,cAAA,GAAAsB,CAAA,WAACM,OAAO;EAAA;EAAA,CAAA5B,cAAA,GAAAsB,CAAA,UAAI,CAACS,SAAS,GAAE;IAAA;IAAA/B,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IAC1B;IACA,OAAQsB,QAAQ,CAAST,KAAK;IAAC;IAAA7B,cAAA,GAAAgB,CAAA;IAC/B,OAAQsB,QAAQ,CAASC,UAAU;EACrC,CAAC;EAAA;EAAA;IAAAvC,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAgB,CAAA;EAED,OAAOZ,qBAAqB,CAACkC,QAAQ,EAAE,6BAA6B,CAAC;AACvE,CAAC,CAAC;;AAEF;AACA,OAAO,MAAME,GAAG;AAAA;AAAA,CAAAxC,cAAA,GAAAgB,CAAA,QAAGP,iBAAiB,CAAC,OACnCQ,OAAoB,EACpB;EAAEC;AAAmC,CAAC,KACnC;EAAA;EAAAlB,cAAA,GAAAmB,CAAA;EACH;EACA,MAAMsB,UAAU;EAAA;EAAA,CAAAzC,cAAA,GAAAgB,CAAA,QAAG,MAAMb,WAAW,CAAC,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;EAAC;EAAAH,cAAA,GAAAgB,CAAA;EAChE,IAAI,CAACyB,UAAU,CAACpB,OAAO,EAAE;IAAA;IAAArB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IACvB,OAAOyB,UAAU,CAAClB,QAAQ;EAC5B,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED,MAAME,OAAO;EAAA;EAAA,CAAAxB,cAAA,GAAAgB,CAAA,QAAGyB,UAAU,CAACjB,OAAO,CAAC;;EAEnC;EACA,MAAMC,eAAe;EAAA;EAAA,CAAAzB,cAAA,GAAAgB,CAAA,QAAGL,kBAAkB,CAACO,MAAM,EAAEL,aAAa,CAAC;EAAC;EAAAb,cAAA,GAAAgB,CAAA;EAClE,IAAI,CAACS,eAAe,CAACJ,OAAO,EAAE;IAAA;IAAArB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IAC5B,OAAOS,eAAe,CAACF,QAAQ;EACjC,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED,MAAM;IAAEI;EAAY,CAAC;EAAA;EAAA,CAAA1B,cAAA,GAAAgB,CAAA,QAAGS,eAAe,CAACE,IAAI;;EAE5C;EACA,MAAMe,cAAc;EAAA;EAAA,CAAA1C,cAAA,GAAAgB,CAAA,QAAG,MAAMN,mBAAmB,CAACO,OAAO,EAAEL,gBAAgB,CAAC;EAAC;EAAAZ,cAAA,GAAAgB,CAAA;EAC5E,IAAI,CAAC0B,cAAc,CAACrB,OAAO,EAAE;IAAA;IAAArB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IAC3B,OAAO0B,cAAc,CAACnB,QAAQ;EAChC,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED,MAAMqB,UAA0B;EAAA;EAAA,CAAA3C,cAAA,GAAAgB,CAAA,QAAG0B,cAAc,CAACf,IAAI;;EAEtD;EACA,MAAMiB,gBAAgB;EAAA;EAAA,CAAA5C,cAAA,GAAAgB,CAAA,QAAG,6CAA6C;EACtE,MAAM6B,WAAW;EAAA;EAAA,CAAA7C,cAAA,GAAAgB,CAAA,QAAG,MAAMF,kBAAkB,CAC1C8B,gBAAgB,EAChB,CAAClB,EAAE,CACL,CAAC;EAAC;EAAA1B,cAAA,GAAAgB,CAAA;EAEF,IAAI,CAAC6B,WAAW,CAACxB,OAAO,EAAE;IAAA;IAAArB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IACxB,OAAOX,mBAAmB,CACxB,sBAAsB,EACtBE,YAAY,CAAC6B,cAAc,EAC3B5B,UAAU,CAAC6B,qBACb,CAAC;EACH,CAAC;EAAA;EAAA;IAAArC,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAgB,CAAA;EAED,IAAI,CAAC6B,WAAW,CAAClB,IAAI,EAAE;IAAA;IAAA3B,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IACrB,OAAOV,sBAAsB,CAAC,MAAM,CAAC;EACvC,CAAC;EAAA;EAAA;IAAAN,cAAA,GAAAsB,CAAA;EAAA;;EAED;EACA,MAAMwB,YAAsB;EAAA;EAAA,CAAA9C,cAAA,GAAAgB,CAAA,QAAG,EAAE;EACjC,MAAM+B,MAAa;EAAA;EAAA,CAAA/C,cAAA,GAAAgB,CAAA,QAAG,EAAE;EACxB,IAAIgC,UAAU;EAAA;EAAA,CAAAhD,cAAA,GAAAgB,CAAA,QAAG,CAAC;EAAC;EAAAhB,cAAA,GAAAgB,CAAA;EAEnB,IAAI2B,UAAU,CAACM,IAAI,KAAKC,SAAS,EAAE;IAAA;IAAAlD,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IACjC8B,YAAY,CAACK,IAAI,CAAC,WAAWH,UAAU,EAAE,EAAE,CAAC;IAAC;IAAAhD,cAAA,GAAAgB,CAAA;IAC7C+B,MAAM,CAACI,IAAI,CAACR,UAAU,CAACM,IAAI,CAAC;EAC9B,CAAC;EAAA;EAAA;IAAAjD,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAgB,CAAA;EAED,IAAI2B,UAAU,CAACS,UAAU,KAAKF,SAAS,EAAE;IAAA;IAAAlD,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IACvC8B,YAAY,CAACK,IAAI,CAAC,iBAAiBH,UAAU,EAAE,EAAE,CAAC;IAAC;IAAAhD,cAAA,GAAAgB,CAAA;IACnD+B,MAAM,CAACI,IAAI,CAACR,UAAU,CAACS,UAAU,CAAC;EACpC,CAAC;EAAA;EAAA;IAAApD,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAgB,CAAA;EAED,IAAI2B,UAAU,CAACU,WAAW,KAAKH,SAAS,EAAE;IAAA;IAAAlD,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IACxC8B,YAAY,CAACK,IAAI,CAAC,kBAAkBH,UAAU,EAAE,EAAE,CAAC;IAAC;IAAAhD,cAAA,GAAAgB,CAAA;IACpD+B,MAAM,CAACI,IAAI,CAACR,UAAU,CAACU,WAAW,CAAC;EACrC,CAAC;EAAA;EAAA;IAAArD,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAgB,CAAA;EAED,IAAI2B,UAAU,CAACd,KAAK,KAAKqB,SAAS,EAAE;IAAA;IAAAlD,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IAClC8B,YAAY,CAACK,IAAI,CAAC,YAAYH,UAAU,EAAE,EAAE,CAAC;IAAC;IAAAhD,cAAA,GAAAgB,CAAA;IAC9C+B,MAAM,CAACI,IAAI,CAACG,IAAI,CAACC,SAAS,CAACZ,UAAU,CAACd,KAAK,CAAC,CAAC;EAC/C,CAAC;EAAA;EAAA;IAAA7B,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAgB,CAAA;EAED,IAAI8B,YAAY,CAACU,MAAM,KAAK,CAAC,EAAE;IAAA;IAAAxD,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IAC7B,OAAOX,mBAAmB,CACxB,2BAA2B,EAC3BE,YAAY,CAACkD,gBAAgB,EAC7BjD,UAAU,CAACkD,WACb,CAAC;EACH,CAAC;EAAA;EAAA;IAAA1D,cAAA,GAAAsB,CAAA;EAAA;;EAED;EAAAtB,cAAA,GAAAgB,CAAA;EACA8B,YAAY,CAACK,IAAI,CAAC,gCAAgC,CAAC;EAAC;EAAAnD,cAAA,GAAAgB,CAAA;EACpD8B,YAAY,CAACK,IAAI,CAAC,uBAAuB,CAAC;;EAE1C;EAAA;EAAAnD,cAAA,GAAAgB,CAAA;EACA+B,MAAM,CAACI,IAAI,CAACzB,EAAE,EAAEmB,WAAW,CAAClB,IAAI,CAACgC,OAAO,CAAC;EAEzC,MAAMC,WAAW;EAAA;EAAA,CAAA5D,cAAA,GAAAgB,CAAA,QAAG;AACtB;AACA,UAAU8B,YAAY,CAACe,IAAI,CAAC,IAAI,CAAC;AACjC,kBAAkBb,UAAU,EAAE,mBAAmBA,UAAU,EAAE;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;EAED,MAAMc,YAAY;EAAA;EAAA,CAAA9D,cAAA,GAAAgB,CAAA,QAAG,MAAMF,kBAAkB,CAAO8C,WAAW,EAAEb,MAAM,CAAC;EAAC;EAAA/C,cAAA,GAAAgB,CAAA;EAEzE,IAAI,CAAC8C,YAAY,CAACzC,OAAO,EAAE;IAAA;IAAArB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IACzB,OAAOX,mBAAmB,CACxB,uBAAuB,EACvBE,YAAY,CAAC6B,cAAc,EAC3B5B,UAAU,CAAC6B,qBACb,CAAC;EACH,CAAC;EAAA;EAAA;IAAArC,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAgB,CAAA;EAED,IAAI,CAAC8C,YAAY,CAACnC,IAAI,EAAE;IAAA;IAAA3B,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IACtB,OAAOX,mBAAmB,CACxB,qEAAqE,EACrEE,YAAY,CAACkD,gBAAgB,EAC7BjD,UAAU,CAACuD,QACb,CAAC;EACH,CAAC;EAAA;EAAA;IAAA/D,cAAA,GAAAsB,CAAA;EAAA;;EAED;EAAAtB,cAAA,GAAAgB,CAAA;EACAgD,OAAO,CAACC,GAAG,CAAC,iBAAiBvC,EAAE,OAAOF,OAAO,CAAC0C,KAAK,EAAE,EAAEvB,UAAU,CAAC;EAAC;EAAA3C,cAAA,GAAAgB,CAAA;EAEnE,OAAOZ,qBAAqB,CAAC0D,YAAY,CAACnC,IAAI,EAAE,2BAA2B,CAAC;AAC9E,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMwC,MAAM;AAAA;AAAA,CAAAnE,cAAA,GAAAgB,CAAA,QAAGP,iBAAiB,CAAC,OACtCQ,OAAoB,EACpB;EAAEC;AAAmC,CAAC,KACnC;EAAA;EAAAlB,cAAA,GAAAmB,CAAA;EACH;EACA,MAAMsB,UAAU;EAAA;EAAA,CAAAzC,cAAA,GAAAgB,CAAA,QAAG,MAAMb,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC;EAAC;EAAAH,cAAA,GAAAgB,CAAA;EAChD,IAAI,CAACyB,UAAU,CAACpB,OAAO,EAAE;IAAA;IAAArB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IACvB,OAAOyB,UAAU,CAAClB,QAAQ;EAC5B,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED,MAAME,OAAO;EAAA;EAAA,CAAAxB,cAAA,GAAAgB,CAAA,QAAGyB,UAAU,CAACjB,OAAO,CAAC;;EAEnC;EACA,MAAMC,eAAe;EAAA;EAAA,CAAAzB,cAAA,GAAAgB,CAAA,QAAGL,kBAAkB,CAACO,MAAM,EAAEL,aAAa,CAAC;EAAC;EAAAb,cAAA,GAAAgB,CAAA;EAClE,IAAI,CAACS,eAAe,CAACJ,OAAO,EAAE;IAAA;IAAArB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IAC5B,OAAOS,eAAe,CAACF,QAAQ;EACjC,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAsB,CAAA;EAAA;EAED,MAAM;IAAEI;EAAY,CAAC;EAAA;EAAA,CAAA1B,cAAA,GAAAgB,CAAA,QAAGS,eAAe,CAACE,IAAI;;EAE5C;EAAA;EAAA3B,cAAA,GAAAgB,CAAA;EACA,IAAIQ,OAAO,CAACQ,MAAM,KAAKN,EAAE,EAAE;IAAA;IAAA1B,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IACzB,OAAOX,mBAAmB,CACxB,gCAAgC,EAChCE,YAAY,CAACkD,gBAAgB,EAC7BjD,UAAU,CAACkD,WACb,CAAC;EACH,CAAC;EAAA;EAAA;IAAA1D,cAAA,GAAAsB,CAAA;EAAA;;EAED;EACA,MAAM8C,WAAW;EAAA;EAAA,CAAApE,cAAA,GAAAgB,CAAA,QAAG;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;EAED,MAAMqD,YAAY;EAAA;EAAA,CAAArE,cAAA,GAAAgB,CAAA,QAAG,MAAMF,kBAAkB,CAC3CsD,WAAW,EACX,CAAC1C,EAAE,CACL,CAAC;EAAC;EAAA1B,cAAA,GAAAgB,CAAA;EAEF,IAAI,CAACqD,YAAY,CAAChD,OAAO,EAAE;IAAA;IAAArB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IACzB,OAAOX,mBAAmB,CACxB,uBAAuB,EACvBE,YAAY,CAAC6B,cAAc,EAC3B5B,UAAU,CAAC6B,qBACb,CAAC;EACH,CAAC;EAAA;EAAA;IAAArC,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAgB,CAAA;EAED,IAAI,CAACqD,YAAY,CAAC1C,IAAI,EAAE;IAAA;IAAA3B,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAgB,CAAA;IACtB,OAAOV,sBAAsB,CAAC,MAAM,CAAC;EACvC,CAAC;EAAA;EAAA;IAAAN,cAAA,GAAAsB,CAAA;EAAA;;EAED;EAAAtB,cAAA,GAAAgB,CAAA;EACAgD,OAAO,CAACC,GAAG,CAAC,iBAAiBI,YAAY,CAAC1C,IAAI,CAACuC,KAAK,OAAO1C,OAAO,CAAC0C,KAAK,EAAE,CAAC;EAAC;EAAAlE,cAAA,GAAAgB,CAAA;EAE5E,OAAOZ,qBAAqB,CAC1B;IAAEsB,EAAE,EAAE2C,YAAY,CAAC1C,IAAI,CAACD;EAAG,CAAC,EAC5B,2BACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}