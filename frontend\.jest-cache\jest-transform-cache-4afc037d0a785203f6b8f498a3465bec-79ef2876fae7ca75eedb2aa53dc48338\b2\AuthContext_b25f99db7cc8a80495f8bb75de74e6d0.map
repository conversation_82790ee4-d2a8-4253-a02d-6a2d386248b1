{"version": 3, "names": ["cov_bt3a0bpyd", "actualCoverage", "createContext", "useState", "useEffect", "useContext", "fetchAuthSession", "signOut", "amplifySignOut", "getCurrentUser", "getAmplifyCurrentUser", "fetchUserAttributes", "AuthContext", "s", "isAuthenticated", "isLoading", "user", "f", "hasRole", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "setAuthState", "setIsLoading", "setUser", "currentUser", "attributes", "id", "userId", "email", "b", "name", "given_name", "family_name", "roles", "JSON", "parse", "error", "console", "isMounted", "checkAuth", "log", "urlParams", "URLSearchParams", "window", "location", "search", "hasOAuthCode", "has", "Promise", "resolve", "setTimeout", "session", "forceRefresh", "authenticated", "tokens", "idToken", "hasSession", "hasTokens", "hasIdToken", "hasAccessToken", "accessToken", "userInfo", "userError", "<PERSON><PERSON>ser", "handleStorageChange", "e", "key", "addEventListener", "removeEventListener", "role", "includes", "global", "localStorage", "removeItem", "document", "cookie", "__jsx", "Provider", "value", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "useAuth"], "sources": ["AuthContext.tsx"], "sourcesContent": ["\r\nimport { createContext, useState, useEffect, useContext, ReactNode } from 'react';\r\nimport {\r\n  fetchAuthSession,\r\n  signOut as amplifySignOut,\r\n  getCurrentUser as getAmplifyCurrentUser,\r\n  fetchUserAttributes\r\n} from 'aws-amplify/auth';\r\nimport { User } from '@/types/user';\r\n\r\ninterface AuthContextType {\r\n  isAuthenticated: boolean;\r\n  isLoading: boolean;\r\n  user: User | null;\r\n  signOut: () => Promise<boolean>;\r\n  hasRole: (role: string) => boolean;\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType>({\r\n  isAuthenticated: false,\r\n  isLoading: true,\r\n  user: null,\r\n  signOut: async () => false,\r\n  hasRole: () => false,\r\n});\r\n\r\nexport function AuthProvider({ children }: { children: ReactNode }) {\r\n  const [isAuthenticated, setAuthState] = useState<boolean>(false);\r\n  const [isLoading, setIsLoading] = useState<boolean>(true);\r\n  const [user, setUser] = useState<User | null>(null);\r\n\r\n  // Get current user from Amplify\r\n  const getCurrentUser = async (): Promise<User | null> => {\r\n    try {\r\n      const currentUser = await getAmplifyCurrentUser();\r\n      const attributes = await fetchUserAttributes();\r\n      \r\n      return {\r\n        id: currentUser.userId,\r\n        email: attributes.email || '',\r\n        name: attributes.name,\r\n        given_name: attributes.given_name,\r\n        family_name: attributes.family_name,\r\n        roles: attributes['custom:roles'] ? JSON.parse(attributes['custom:roles']) : []\r\n      };\r\n    } catch (error) {\r\n      console.error('Error getting current user:', error);\r\n      return null;\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    let isMounted = true;\r\n\r\n    const checkAuth = async () => {\r\n      if (!isMounted) return;\r\n\r\n      try {\r\n        console.log('🔍 [AUTH-CONTEXT] Starting authentication check...');\r\n\r\n        // Check if this is an OAuth callback\r\n        const urlParams = new URLSearchParams(window.location.search)\r\n        const hasOAuthCode = urlParams.has('code')\r\n\r\n        if (hasOAuthCode) {\r\n          console.log('🔄 [AUTH-CONTEXT] OAuth callback detected, giving Amplify extra time...');\r\n          // Give Amplify more time to process OAuth callback\r\n          await new Promise(resolve => setTimeout(resolve, 5000));\r\n        } else {\r\n          // Small delay to ensure Amplify has processed any OAuth callbacks\r\n          await new Promise(resolve => setTimeout(resolve, 1000));\r\n        }\r\n\r\n        // Check if authenticated with Amplify\r\n        const session = await fetchAuthSession({ forceRefresh: false });\r\n        const authenticated = !!session?.tokens?.idToken;\r\n\r\n        console.log('🔍 [AUTH-CONTEXT] Session details:', {\r\n          hasSession: !!session,\r\n          hasTokens: !!session?.tokens,\r\n          hasIdToken: !!session?.tokens?.idToken,\r\n          hasAccessToken: !!session?.tokens?.accessToken,\r\n          authenticated\r\n        });\r\n\r\n        if (authenticated) {\r\n          console.log('✅ [AUTH-CONTEXT] Session is authenticated, getting user info...');\r\n\r\n          try {\r\n            const userInfo = await getCurrentUser();\r\n            console.log('✅ [AUTH-CONTEXT] User info retrieved:', userInfo);\r\n\r\n            if (isMounted && userInfo) {\r\n              setUser(userInfo);\r\n              setAuthState(true);\r\n              console.log('✅ [AUTH-CONTEXT] User state updated successfully');\r\n            } else if (isMounted) {\r\n              console.log('❌ [AUTH-CONTEXT] Component unmounted or no user info');\r\n              setUser(null);\r\n              setAuthState(false);\r\n            }\r\n          } catch (userError) {\r\n            console.error('❌ [AUTH-CONTEXT] Error getting user info:', userError);\r\n            if (isMounted) {\r\n              setUser(null);\r\n              setAuthState(false);\r\n            }\r\n          }\r\n        } else {\r\n          console.log('❌ [AUTH-CONTEXT] No valid session found');\r\n          if (isMounted) {\r\n            setUser(null);\r\n            setAuthState(false);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('❌ [AUTH-CONTEXT] Auth check error:', error);\r\n        if (isMounted) {\r\n          setUser(null);\r\n          setAuthState(false);\r\n        }\r\n      } finally {\r\n        if (isMounted) {\r\n          setIsLoading(false);\r\n          console.log('🏁 [AUTH-CONTEXT] Auth check complete. Final state:', {\r\n            isAuthenticated,\r\n            hasUser: !!user,\r\n            isLoading: false\r\n          });\r\n        }\r\n      }\r\n    };\r\n\r\n    checkAuth();\r\n    \r\n    // Listen for storage events to sync auth state across tabs\r\n    const handleStorageChange = (e: StorageEvent) => {\r\n      if (e.key === 'isAuthenticated') {\r\n        console.log('AuthContext: Storage event detected, rechecking auth...');\r\n        checkAuth();\r\n      }\r\n    };\r\n    \r\n    window.addEventListener('storage', handleStorageChange);\r\n    \r\n    return () => {\r\n      isMounted = false;\r\n      window.removeEventListener('storage', handleStorageChange);\r\n    };\r\n  }, []);\r\n\r\n  // Role checking utility\r\n  const hasRole = (role: string) => {\r\n    return user?.roles?.includes(role) || false;\r\n  };\r\n\r\n  // Sign out function\r\n  const signOut = async (): Promise<boolean> => {\r\n    try {\r\n      await amplifySignOut({ global: true });\r\n      \r\n      // Clear localStorage flag\r\n      localStorage.removeItem('isAuthenticated');\r\n      \r\n      // Clear the auth cookie\r\n      document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';\r\n      \r\n      // Update state\r\n      setUser(null);\r\n      setAuthState(false);\r\n      \r\n      return true;\r\n    } catch (error) {\r\n      console.error('Error signing out:', error);\r\n      \r\n      // Even if Amplify signOut fails, clear local auth state\r\n      localStorage.removeItem('isAuthenticated');\r\n      document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';\r\n      \r\n      // Update state\r\n      setUser(null);\r\n      setAuthState(false);\r\n      \r\n      return true; // Return true anyway to allow navigation to login page\r\n    }\r\n  };\r\n\r\n  return (\r\n    <AuthContext.Provider\r\n      value={{\r\n        isAuthenticated,\r\n        isLoading,\r\n        user,\r\n        signOut,\r\n        hasRole,\r\n      }}\r\n    >\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n}\r\n\r\n// Custom hook for using auth context\r\nexport const useAuth = () => useContext(AuthContext);\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAdZ,SAASE,aAAa,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAmB,OAAO;AACjF,SACEC,gBAAgB,EAChBC,OAAO,IAAIC,cAAc,EACzBC,cAAc,IAAIC,qBAAqB,EACvCC,mBAAmB,QACd,kBAAkB;AAWzB,MAAMC,WAAW;AAAA;AAAA,CAAAZ,aAAA,GAAAa,CAAA,oBAAGX,aAAa,CAAkB;EACjDY,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,IAAI;EACfC,IAAI,EAAE,IAAI;EACVT,OAAO,EAAE,MAAAA,CAAA,KAAY;IAAA;IAAAP,aAAA,GAAAiB,CAAA;IAAAjB,aAAA,GAAAa,CAAA;IAAA,YAAK;EAAD,CAAC;EAC1BK,OAAO,EAAEA,CAAA,KAAM;IAAA;IAAAlB,aAAA,GAAAiB,CAAA;IAAAjB,aAAA,GAAAa,CAAA;IAAA,YAAK;EAAD;AACrB,CAAC,CAAC;AAEF,OAAO,SAASM,YAAYA,CAAC;EAAEC;AAAkC,CAAC,EAAE;EAAA;EAAApB,aAAA,GAAAiB,CAAA;EAClE,MAAM,CAACH,eAAe,EAAEO,YAAY,CAAC;EAAA;EAAA,CAAArB,aAAA,GAAAa,CAAA,OAAGV,QAAQ,CAAU,KAAK,CAAC;EAChE,MAAM,CAACY,SAAS,EAAEO,YAAY,CAAC;EAAA;EAAA,CAAAtB,aAAA,GAAAa,CAAA,OAAGV,QAAQ,CAAU,IAAI,CAAC;EACzD,MAAM,CAACa,IAAI,EAAEO,OAAO,CAAC;EAAA;EAAA,CAAAvB,aAAA,GAAAa,CAAA,OAAGV,QAAQ,CAAc,IAAI,CAAC;;EAEnD;EAAA;EAAAH,aAAA,GAAAa,CAAA;EACA,MAAMJ,cAAc,GAAG,MAAAA,CAAA,KAAkC;IAAA;IAAAT,aAAA,GAAAiB,CAAA;IAAAjB,aAAA,GAAAa,CAAA;IACvD,IAAI;MACF,MAAMW,WAAW;MAAA;MAAA,CAAAxB,aAAA,GAAAa,CAAA,OAAG,MAAMH,qBAAqB,CAAC,CAAC;MACjD,MAAMe,UAAU;MAAA;MAAA,CAAAzB,aAAA,GAAAa,CAAA,OAAG,MAAMF,mBAAmB,CAAC,CAAC;MAAC;MAAAX,aAAA,GAAAa,CAAA;MAE/C,OAAO;QACLa,EAAE,EAAEF,WAAW,CAACG,MAAM;QACtBC,KAAK;QAAE;QAAA,CAAA5B,aAAA,GAAA6B,CAAA,UAAAJ,UAAU,CAACG,KAAK;QAAA;QAAA,CAAA5B,aAAA,GAAA6B,CAAA,UAAI,EAAE;QAC7BC,IAAI,EAAEL,UAAU,CAACK,IAAI;QACrBC,UAAU,EAAEN,UAAU,CAACM,UAAU;QACjCC,WAAW,EAAEP,UAAU,CAACO,WAAW;QACnCC,KAAK,EAAER,UAAU,CAAC,cAAc,CAAC;QAAA;QAAA,CAAAzB,aAAA,GAAA6B,CAAA,UAAGK,IAAI,CAACC,KAAK,CAACV,UAAU,CAAC,cAAc,CAAC,CAAC;QAAA;QAAA,CAAAzB,aAAA,GAAA6B,CAAA,UAAG,EAAE;MACjF,CAAC;IACH,CAAC,CAAC,OAAOO,KAAK,EAAE;MAAA;MAAApC,aAAA,GAAAa,CAAA;MACdwB,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MAAC;MAAApC,aAAA,GAAAa,CAAA;MACpD,OAAO,IAAI;IACb;EACF,CAAC;EAAC;EAAAb,aAAA,GAAAa,CAAA;EAEFT,SAAS,CAAC,MAAM;IAAA;IAAAJ,aAAA,GAAAiB,CAAA;IACd,IAAIqB,SAAS;IAAA;IAAA,CAAAtC,aAAA,GAAAa,CAAA,QAAG,IAAI;IAAC;IAAAb,aAAA,GAAAa,CAAA;IAErB,MAAM0B,SAAS,GAAG,MAAAA,CAAA,KAAY;MAAA;MAAAvC,aAAA,GAAAiB,CAAA;MAAAjB,aAAA,GAAAa,CAAA;MAC5B,IAAI,CAACyB,SAAS,EAAE;QAAA;QAAAtC,aAAA,GAAA6B,CAAA;QAAA7B,aAAA,GAAAa,CAAA;QAAA;MAAM,CAAC;MAAA;MAAA;QAAAb,aAAA,GAAA6B,CAAA;MAAA;MAAA7B,aAAA,GAAAa,CAAA;MAEvB,IAAI;QAAA;QAAAb,aAAA,GAAAa,CAAA;QACFwB,OAAO,CAACG,GAAG,CAAC,oDAAoD,CAAC;;QAEjE;QACA,MAAMC,SAAS;QAAA;QAAA,CAAAzC,aAAA,GAAAa,CAAA,QAAG,IAAI6B,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;QAC7D,MAAMC,YAAY;QAAA;QAAA,CAAA9C,aAAA,GAAAa,CAAA,QAAG4B,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC;QAAA;QAAA/C,aAAA,GAAAa,CAAA;QAE1C,IAAIiC,YAAY,EAAE;UAAA;UAAA9C,aAAA,GAAA6B,CAAA;UAAA7B,aAAA,GAAAa,CAAA;UAChBwB,OAAO,CAACG,GAAG,CAAC,yEAAyE,CAAC;UACtF;UAAA;UAAAxC,aAAA,GAAAa,CAAA;UACA,MAAM,IAAImC,OAAO,CAACC,OAAO,IAAI;YAAA;YAAAjD,aAAA,GAAAiB,CAAA;YAAAjB,aAAA,GAAAa,CAAA;YAAA,OAAAqC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;UAAD,CAAC,CAAC;QACzD,CAAC,MAAM;UAAA;UAAAjD,aAAA,GAAA6B,CAAA;UAAA7B,aAAA,GAAAa,CAAA;UACL;UACA,MAAM,IAAImC,OAAO,CAACC,OAAO,IAAI;YAAA;YAAAjD,aAAA,GAAAiB,CAAA;YAAAjB,aAAA,GAAAa,CAAA;YAAA,OAAAqC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;UAAD,CAAC,CAAC;QACzD;;QAEA;QACA,MAAME,OAAO;QAAA;QAAA,CAAAnD,aAAA,GAAAa,CAAA,QAAG,MAAMP,gBAAgB,CAAC;UAAE8C,YAAY,EAAE;QAAM,CAAC,CAAC;QAC/D,MAAMC,aAAa;QAAA;QAAA,CAAArD,aAAA,GAAAa,CAAA,QAAG,CAAC,CAACsC,OAAO,EAAEG,MAAM,EAAEC,OAAO;QAAC;QAAAvD,aAAA,GAAAa,CAAA;QAEjDwB,OAAO,CAACG,GAAG,CAAC,oCAAoC,EAAE;UAChDgB,UAAU,EAAE,CAAC,CAACL,OAAO;UACrBM,SAAS,EAAE,CAAC,CAACN,OAAO,EAAEG,MAAM;UAC5BI,UAAU,EAAE,CAAC,CAACP,OAAO,EAAEG,MAAM,EAAEC,OAAO;UACtCI,cAAc,EAAE,CAAC,CAACR,OAAO,EAAEG,MAAM,EAAEM,WAAW;UAC9CP;QACF,CAAC,CAAC;QAAC;QAAArD,aAAA,GAAAa,CAAA;QAEH,IAAIwC,aAAa,EAAE;UAAA;UAAArD,aAAA,GAAA6B,CAAA;UAAA7B,aAAA,GAAAa,CAAA;UACjBwB,OAAO,CAACG,GAAG,CAAC,iEAAiE,CAAC;UAAC;UAAAxC,aAAA,GAAAa,CAAA;UAE/E,IAAI;YACF,MAAMgD,QAAQ;YAAA;YAAA,CAAA7D,aAAA,GAAAa,CAAA,QAAG,MAAMJ,cAAc,CAAC,CAAC;YAAC;YAAAT,aAAA,GAAAa,CAAA;YACxCwB,OAAO,CAACG,GAAG,CAAC,uCAAuC,EAAEqB,QAAQ,CAAC;YAAC;YAAA7D,aAAA,GAAAa,CAAA;YAE/D;YAAI;YAAA,CAAAb,aAAA,GAAA6B,CAAA,UAAAS,SAAS;YAAA;YAAA,CAAAtC,aAAA,GAAA6B,CAAA,UAAIgC,QAAQ,GAAE;cAAA;cAAA7D,aAAA,GAAA6B,CAAA;cAAA7B,aAAA,GAAAa,CAAA;cACzBU,OAAO,CAACsC,QAAQ,CAAC;cAAC;cAAA7D,aAAA,GAAAa,CAAA;cAClBQ,YAAY,CAAC,IAAI,CAAC;cAAC;cAAArB,aAAA,GAAAa,CAAA;cACnBwB,OAAO,CAACG,GAAG,CAAC,kDAAkD,CAAC;YACjE,CAAC,MAAM;cAAA;cAAAxC,aAAA,GAAA6B,CAAA;cAAA7B,aAAA,GAAAa,CAAA;cAAA,IAAIyB,SAAS,EAAE;gBAAA;gBAAAtC,aAAA,GAAA6B,CAAA;gBAAA7B,aAAA,GAAAa,CAAA;gBACpBwB,OAAO,CAACG,GAAG,CAAC,sDAAsD,CAAC;gBAAC;gBAAAxC,aAAA,GAAAa,CAAA;gBACpEU,OAAO,CAAC,IAAI,CAAC;gBAAC;gBAAAvB,aAAA,GAAAa,CAAA;gBACdQ,YAAY,CAAC,KAAK,CAAC;cACrB,CAAC;cAAA;cAAA;gBAAArB,aAAA,GAAA6B,CAAA;cAAA;YAAD;UACF,CAAC,CAAC,OAAOiC,SAAS,EAAE;YAAA;YAAA9D,aAAA,GAAAa,CAAA;YAClBwB,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAE0B,SAAS,CAAC;YAAC;YAAA9D,aAAA,GAAAa,CAAA;YACtE,IAAIyB,SAAS,EAAE;cAAA;cAAAtC,aAAA,GAAA6B,CAAA;cAAA7B,aAAA,GAAAa,CAAA;cACbU,OAAO,CAAC,IAAI,CAAC;cAAC;cAAAvB,aAAA,GAAAa,CAAA;cACdQ,YAAY,CAAC,KAAK,CAAC;YACrB,CAAC;YAAA;YAAA;cAAArB,aAAA,GAAA6B,CAAA;YAAA;UACH;QACF,CAAC,MAAM;UAAA;UAAA7B,aAAA,GAAA6B,CAAA;UAAA7B,aAAA,GAAAa,CAAA;UACLwB,OAAO,CAACG,GAAG,CAAC,yCAAyC,CAAC;UAAC;UAAAxC,aAAA,GAAAa,CAAA;UACvD,IAAIyB,SAAS,EAAE;YAAA;YAAAtC,aAAA,GAAA6B,CAAA;YAAA7B,aAAA,GAAAa,CAAA;YACbU,OAAO,CAAC,IAAI,CAAC;YAAC;YAAAvB,aAAA,GAAAa,CAAA;YACdQ,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC;UAAA;UAAA;YAAArB,aAAA,GAAA6B,CAAA;UAAA;QACH;MACF,CAAC,CAAC,OAAOO,KAAK,EAAE;QAAA;QAAApC,aAAA,GAAAa,CAAA;QACdwB,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAAC;QAAApC,aAAA,GAAAa,CAAA;QAC3D,IAAIyB,SAAS,EAAE;UAAA;UAAAtC,aAAA,GAAA6B,CAAA;UAAA7B,aAAA,GAAAa,CAAA;UACbU,OAAO,CAAC,IAAI,CAAC;UAAC;UAAAvB,aAAA,GAAAa,CAAA;UACdQ,YAAY,CAAC,KAAK,CAAC;QACrB,CAAC;QAAA;QAAA;UAAArB,aAAA,GAAA6B,CAAA;QAAA;MACH,CAAC,SAAS;QAAA;QAAA7B,aAAA,GAAAa,CAAA;QACR,IAAIyB,SAAS,EAAE;UAAA;UAAAtC,aAAA,GAAA6B,CAAA;UAAA7B,aAAA,GAAAa,CAAA;UACbS,YAAY,CAAC,KAAK,CAAC;UAAC;UAAAtB,aAAA,GAAAa,CAAA;UACpBwB,OAAO,CAACG,GAAG,CAAC,qDAAqD,EAAE;YACjE1B,eAAe;YACfiD,OAAO,EAAE,CAAC,CAAC/C,IAAI;YACfD,SAAS,EAAE;UACb,CAAC,CAAC;QACJ,CAAC;QAAA;QAAA;UAAAf,aAAA,GAAA6B,CAAA;QAAA;MACH;IACF,CAAC;IAAC;IAAA7B,aAAA,GAAAa,CAAA;IAEF0B,SAAS,CAAC,CAAC;;IAEX;IAAA;IAAAvC,aAAA,GAAAa,CAAA;IACA,MAAMmD,mBAAmB,GAAIC,CAAe,IAAK;MAAA;MAAAjE,aAAA,GAAAiB,CAAA;MAAAjB,aAAA,GAAAa,CAAA;MAC/C,IAAIoD,CAAC,CAACC,GAAG,KAAK,iBAAiB,EAAE;QAAA;QAAAlE,aAAA,GAAA6B,CAAA;QAAA7B,aAAA,GAAAa,CAAA;QAC/BwB,OAAO,CAACG,GAAG,CAAC,yDAAyD,CAAC;QAAC;QAAAxC,aAAA,GAAAa,CAAA;QACvE0B,SAAS,CAAC,CAAC;MACb,CAAC;MAAA;MAAA;QAAAvC,aAAA,GAAA6B,CAAA;MAAA;IACH,CAAC;IAAC;IAAA7B,aAAA,GAAAa,CAAA;IAEF8B,MAAM,CAACwB,gBAAgB,CAAC,SAAS,EAAEH,mBAAmB,CAAC;IAAC;IAAAhE,aAAA,GAAAa,CAAA;IAExD,OAAO,MAAM;MAAA;MAAAb,aAAA,GAAAiB,CAAA;MAAAjB,aAAA,GAAAa,CAAA;MACXyB,SAAS,GAAG,KAAK;MAAC;MAAAtC,aAAA,GAAAa,CAAA;MAClB8B,MAAM,CAACyB,mBAAmB,CAAC,SAAS,EAAEJ,mBAAmB,CAAC;IAC5D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EAAA;EAAAhE,aAAA,GAAAa,CAAA;EACA,MAAMK,OAAO,GAAImD,IAAY,IAAK;IAAA;IAAArE,aAAA,GAAAiB,CAAA;IAAAjB,aAAA,GAAAa,CAAA;IAChC,OAAO,2BAAAb,aAAA,GAAA6B,CAAA,WAAAb,IAAI,EAAEiB,KAAK,EAAEqC,QAAQ,CAACD,IAAI,CAAC;IAAA;IAAA,CAAArE,aAAA,GAAA6B,CAAA,WAAI,KAAK;EAC7C,CAAC;;EAED;EAAA;EAAA7B,aAAA,GAAAa,CAAA;EACA,MAAMN,OAAO,GAAG,MAAAA,CAAA,KAA8B;IAAA;IAAAP,aAAA,GAAAiB,CAAA;IAAAjB,aAAA,GAAAa,CAAA;IAC5C,IAAI;MAAA;MAAAb,aAAA,GAAAa,CAAA;MACF,MAAML,cAAc,CAAC;QAAE+D,MAAM,EAAE;MAAK,CAAC,CAAC;;MAEtC;MAAA;MAAAvE,aAAA,GAAAa,CAAA;MACA2D,YAAY,CAACC,UAAU,CAAC,iBAAiB,CAAC;;MAE1C;MAAA;MAAAzE,aAAA,GAAAa,CAAA;MACA6D,QAAQ,CAACC,MAAM,GAAG,2CAA2C;;MAE7D;MAAA;MAAA3E,aAAA,GAAAa,CAAA;MACAU,OAAO,CAAC,IAAI,CAAC;MAAC;MAAAvB,aAAA,GAAAa,CAAA;MACdQ,YAAY,CAAC,KAAK,CAAC;MAAC;MAAArB,aAAA,GAAAa,CAAA;MAEpB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOuB,KAAK,EAAE;MAAA;MAAApC,aAAA,GAAAa,CAAA;MACdwB,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;;MAE1C;MAAA;MAAApC,aAAA,GAAAa,CAAA;MACA2D,YAAY,CAACC,UAAU,CAAC,iBAAiB,CAAC;MAAC;MAAAzE,aAAA,GAAAa,CAAA;MAC3C6D,QAAQ,CAACC,MAAM,GAAG,2CAA2C;;MAE7D;MAAA;MAAA3E,aAAA,GAAAa,CAAA;MACAU,OAAO,CAAC,IAAI,CAAC;MAAC;MAAAvB,aAAA,GAAAa,CAAA;MACdQ,YAAY,CAAC,KAAK,CAAC;MAAC;MAAArB,aAAA,GAAAa,CAAA;MAEpB,OAAO,IAAI,CAAC,CAAC;IACf;EACF,CAAC;EAAC;EAAAb,aAAA,GAAAa,CAAA;EAEF,OACE,0BAAA+D,KAAA;EAAA;EAAChE,WAAW,CAACiE,QAAQ;EAAA;EAAA;IACnBC,KAAK,EAAE;MACLhE,eAAe;MACfC,SAAS;MACTC,IAAI;MACJT,OAAO;MACPW;IACF,CAAE;IAAA6D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEDhE,QACmB,CAAC;AAE3B;;AAEA;AAAA;AAAApB,aAAA,GAAAa,CAAA;AACA,OAAO,MAAMwE,OAAO,GAAGA,CAAA,KAAM;EAAA;EAAArF,aAAA,GAAAiB,CAAA;EAAAjB,aAAA,GAAAa,CAAA;EAAA,OAAAR,UAAU,CAACO,WAAW,CAAC;AAAD,CAAC", "ignoreList": []}