/**
 * Dashboard Stats Component Tests
 * 
 * Tests for the DashboardStats component including loading states,
 * data display, and performance optimizations
 */

import React from 'react'
import { render, screen } from '../utils/test-utils'
import DashboardStats from '@/components/dashboard/DashboardStats'
import { DashboardStats as DashboardStatsType } from '@/lib/types'

describe('DashboardStats Component', () => {
  const mockStats: DashboardStatsType = {
    totalRenewals: 25,
    renewalsDue: 5,
    vendors: 12,
    annualSpend: '$125,000',
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Rendering', () => {
    it('should render all stat cards with correct data', () => {
      render(<DashboardStats stats={mockStats} />)

      // Check if all stat cards are rendered
      expect(screen.getByText('Total Renewals')).toBeInTheDocument()
      expect(screen.getByText('Renewals Due')).toBeInTheDocument()
      expect(screen.getByText('Vendors')).toBeInTheDocument()
      expect(screen.getByText('Annual Spend')).toBeInTheDocument()

      // Check if values are displayed correctly
      expect(screen.getByText('25')).toBeInTheDocument()
      expect(screen.getByText('5')).toBeInTheDocument()
      expect(screen.getByText('12')).toBeInTheDocument()
      expect(screen.getByText('$125,000')).toBeInTheDocument()
    })

    it('should render with custom className', () => {
      const { container } = render(
        <DashboardStats stats={mockStats} className="custom-class" />
      )

      const statsGrid = container.querySelector('.stats-grid')
      expect(statsGrid).toHaveClass('custom-class')
    })

    it('should render with data-testid', () => {
      render(
        <DashboardStats 
          stats={mockStats} 
          data-testid="dashboard-stats-test" 
        />
      )

      expect(screen.getByTestId('dashboard-stats-test')).toBeInTheDocument()
    })

    it('should have proper accessibility attributes', () => {
      render(<DashboardStats stats={mockStats} />)

      const statsContainer = screen.getByRole('region')
      expect(statsContainer).toHaveAttribute('aria-label', 'Dashboard Statistics')
    })
  })

  describe('Loading States', () => {
    it('should show loading skeletons when isLoading is true', () => {
      render(<DashboardStats stats={mockStats} isLoading={true} />)

      // Check for loading indicators
      const loadingElements = screen.getAllByText('⏳')
      expect(loadingElements).toHaveLength(4) // One for each stat card

      // Check for skeleton loading elements
      const skeletons = document.querySelectorAll('.animate-pulse')
      expect(skeletons.length).toBeGreaterThan(0)
    })

    it('should hide loading skeletons when isLoading is false', () => {
      render(<DashboardStats stats={mockStats} isLoading={false} />)

      // Should not show loading indicators
      expect(screen.queryByText('⏳')).not.toBeInTheDocument()

      // Should show actual data
      expect(screen.getByText('25')).toBeInTheDocument()
    })

    it('should handle transition from loading to loaded state', () => {
      const { rerender } = render(
        <DashboardStats stats={mockStats} isLoading={true} />
      )

      // Initially loading
      expect(screen.getAllByText('⏳')).toHaveLength(4)

      // Rerender with loaded state
      rerender(<DashboardStats stats={mockStats} isLoading={false} />)

      // Should show data now
      expect(screen.queryByText('⏳')).not.toBeInTheDocument()
      expect(screen.getByText('25')).toBeInTheDocument()
    })
  })

  describe('Data Handling', () => {
    it('should handle zero values correctly', () => {
      const zeroStats: DashboardStatsType = {
        totalRenewals: 0,
        renewalsDue: 0,
        vendors: 0,
        annualSpend: '$0',
      }

      render(<DashboardStats stats={zeroStats} />)

      expect(screen.getAllByText('0')).toHaveLength(3)
      expect(screen.getByText('$0')).toBeInTheDocument()
    })

    it('should handle large numbers correctly', () => {
      const largeStats: DashboardStatsType = {
        totalRenewals: 1000,
        renewalsDue: 250,
        vendors: 500,
        annualSpend: '$1,000,000',
      }

      render(<DashboardStats stats={largeStats} />)

      expect(screen.getByText('1000')).toBeInTheDocument()
      expect(screen.getByText('250')).toBeInTheDocument()
      expect(screen.getByText('500')).toBeInTheDocument()
      expect(screen.getByText('$1,000,000')).toBeInTheDocument()
    })

    it('should handle string values for annual spend', () => {
      const stringStats: DashboardStatsType = {
        totalRenewals: 25,
        renewalsDue: 5,
        vendors: 12,
        annualSpend: 'N/A',
      }

      render(<DashboardStats stats={stringStats} />)

      expect(screen.getByText('N/A')).toBeInTheDocument()
    })
  })

  describe('Performance', () => {
    it('should memoize stats configuration', () => {
      const { rerender } = render(<DashboardStats stats={mockStats} />)

      // Rerender with same stats - should not cause unnecessary recalculation
      rerender(<DashboardStats stats={mockStats} />)

      // Component should still render correctly
      expect(screen.getByText('Total Renewals')).toBeInTheDocument()
    })

    it('should only re-render when stats change', () => {
      const { rerender } = render(<DashboardStats stats={mockStats} />)

      // Rerender with different props but same stats
      rerender(<DashboardStats stats={mockStats} className="new-class" />)

      // Should still show same data
      expect(screen.getByText('25')).toBeInTheDocument()
    })

    it('should update when stats change', () => {
      const { rerender } = render(<DashboardStats stats={mockStats} />)

      const newStats: DashboardStatsType = {
        totalRenewals: 30,
        renewalsDue: 8,
        vendors: 15,
        annualSpend: '$150,000',
      }

      rerender(<DashboardStats stats={newStats} />)

      // Should show updated data
      expect(screen.getByText('30')).toBeInTheDocument()
      expect(screen.getByText('8')).toBeInTheDocument()
      expect(screen.getByText('15')).toBeInTheDocument()
      expect(screen.getByText('$150,000')).toBeInTheDocument()
    })
  })

  describe('Icons and Visual Elements', () => {
    it('should display correct icons for each stat', () => {
      render(<DashboardStats stats={mockStats} />)

      // Check for emoji icons (these might need adjustment based on actual implementation)
      expect(screen.getByText('📊')).toBeInTheDocument() // Total Renewals
      expect(screen.getByText('⚠️')).toBeInTheDocument() // Renewals Due
      expect(screen.getByText('🏢')).toBeInTheDocument() // Vendors
      expect(screen.getByText('💰')).toBeInTheDocument() // Annual Spend
    })

    it('should maintain consistent layout structure', () => {
      const { container } = render(<DashboardStats stats={mockStats} />)

      const statsGrid = container.querySelector('.stats-grid')
      expect(statsGrid).toBeInTheDocument()

      const statCards = container.querySelectorAll('.stat-card')
      expect(statCards).toHaveLength(4)
    })
  })

  describe('Error Handling', () => {
    it('should handle undefined stats gracefully', () => {
      // This test ensures the component doesn't crash with undefined stats
      const undefinedStats = {
        totalRenewals: undefined as any,
        renewalsDue: undefined as any,
        vendors: undefined as any,
        annualSpend: undefined as any,
      }

      expect(() => {
        render(<DashboardStats stats={undefinedStats} />)
      }).not.toThrow()
    })

    it('should handle null stats gracefully', () => {
      const nullStats = {
        totalRenewals: null as any,
        renewalsDue: null as any,
        vendors: null as any,
        annualSpend: null as any,
      }

      expect(() => {
        render(<DashboardStats stats={nullStats} />)
      }).not.toThrow()
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<DashboardStats stats={mockStats} />)

      const region = screen.getByRole('region')
      expect(region).toHaveAttribute('aria-label', 'Dashboard Statistics')
    })

    it('should be keyboard navigable', () => {
      render(<DashboardStats stats={mockStats} />)

      // The component should not interfere with keyboard navigation
      // This is more of a structural test
      const statsContainer = screen.getByRole('region')
      expect(statsContainer).toBeInTheDocument()
    })
  })
})
