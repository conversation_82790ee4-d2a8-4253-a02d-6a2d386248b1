c08a39e3224c886895ebeb0ec61de0e6
/**
 * Dashboard Statistics Component
 * 
 * Displays key metrics in a grid layout with proper loading and error states.
 * Focused responsibility: Rendering statistics cards only.
 */

'use client';

/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\DashboardStats.tsx";
var __jsx = React.createElement;
function cov_1cpmqwip5t() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\DashboardStats.tsx";
  var hash = "e66bdaa55a7d41ad9fc85f1df544deedc601df75";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\DashboardStats.tsx",
    statementMap: {
      "0": {
        start: {
          line: 27,
          column: 17
        },
        end: {
          line: 47,
          column: 2
        }
      },
      "1": {
        start: {
          line: 28,
          column: 2
        },
        end: {
          line: 38,
          column: 3
        }
      },
      "2": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 37,
          column: 5
        }
      },
      "3": {
        start: {
          line: 40,
          column: 2
        },
        end: {
          line: 46,
          column: 3
        }
      },
      "4": {
        start: {
          line: 49,
          column: 23
        },
        end: {
          line: 104,
          column: 2
        }
      },
      "5": {
        start: {
          line: 56,
          column: 2
        },
        end: {
          line: 56,
          column: 41
        }
      },
      "6": {
        start: {
          line: 59,
          column: 22
        },
        end: {
          line: 84,
          column: 80
        }
      },
      "7": {
        start: {
          line: 59,
          column: 42
        },
        end: {
          line: 84,
          column: 3
        }
      },
      "8": {
        start: {
          line: 86,
          column: 2
        },
        end: {
          line: 103,
          column: 3
        }
      },
      "9": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 100,
          column: 10
        }
      }
    },
    fnMap: {
      "0": {
        name: "StatCard",
        decl: {
          start: {
            line: 27,
            column: 31
          },
          end: {
            line: 27,
            column: 39
          }
        },
        loc: {
          start: {
            line: 27,
            column: 90
          },
          end: {
            line: 47,
            column: 1
          }
        },
        line: 27
      },
      "1": {
        name: "DashboardStats",
        decl: {
          start: {
            line: 49,
            column: 37
          },
          end: {
            line: 49,
            column: 51
          }
        },
        loc: {
          start: {
            line: 54,
            column: 24
          },
          end: {
            line: 104,
            column: 1
          }
        },
        line: 54
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 59,
            column: 36
          },
          end: {
            line: 59,
            column: 37
          }
        },
        loc: {
          start: {
            line: 59,
            column: 42
          },
          end: {
            line: 84,
            column: 3
          }
        },
        line: 59
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 93,
            column: 23
          },
          end: {
            line: 93,
            column: 24
          }
        },
        loc: {
          start: {
            line: 94,
            column: 8
          },
          end: {
            line: 100,
            column: 10
          }
        },
        line: 94
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 28,
            column: 2
          },
          end: {
            line: 38,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 2
          },
          end: {
            line: 38,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "1": {
        loc: {
          start: {
            line: 51,
            column: 2
          },
          end: {
            line: 51,
            column: 19
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 51,
            column: 14
          },
          end: {
            line: 51,
            column: 19
          }
        }],
        line: 51
      },
      "2": {
        loc: {
          start: {
            line: 52,
            column: 2
          },
          end: {
            line: 52,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 52,
            column: 14
          },
          end: {
            line: 52,
            column: 16
          }
        }],
        line: 52
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    b: {
      "0": [0, 0],
      "1": [0],
      "2": [0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e66bdaa55a7d41ad9fc85f1df544deedc601df75"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1cpmqwip5t = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1cpmqwip5t();
import React, { memo } from 'react';
import { usePerformanceMonitor } from '@/lib/performance';
const StatCard =
/* istanbul ignore next */
(cov_1cpmqwip5t().s[0]++, /*#__PURE__*/memo(function StatCard({
  icon,
  title,
  value,
  isLoading
}) {
  /* istanbul ignore next */
  cov_1cpmqwip5t().f[0]++;
  cov_1cpmqwip5t().s[1]++;
  if (isLoading) {
    /* istanbul ignore next */
    cov_1cpmqwip5t().b[0][0]++;
    cov_1cpmqwip5t().s[2]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "stat-card",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 30,
        columnNumber: 7
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "stat-icon animate-pulse",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 31,
        columnNumber: 9
      }
    }, "\u23F3"),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "h3",
    /* istanbul ignore next */
    {
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 32,
        columnNumber: 9
      }
    }, title),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "stat-value",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 33,
        columnNumber: 9
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "animate-pulse bg-gray-200 h-6 w-16 rounded",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 34,
        columnNumber: 11
      }
    })));
  } else
  /* istanbul ignore next */
  {
    cov_1cpmqwip5t().b[0][1]++;
  }
  cov_1cpmqwip5t().s[3]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "stat-card",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 41,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "stat-icon",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 42,
      columnNumber: 7
    }
  }, icon),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h3",
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 43,
      columnNumber: 7
    }
  }, title),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "stat-value",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 44,
      columnNumber: 7
    }
  }, value));
}));
const DashboardStats =
/* istanbul ignore next */
(cov_1cpmqwip5t().s[4]++, /*#__PURE__*/memo(function DashboardStats({
  stats,
  isLoading =
  /* istanbul ignore next */
  (cov_1cpmqwip5t().b[1][0]++, false),
  className =
  /* istanbul ignore next */
  (cov_1cpmqwip5t().b[2][0]++, ''),
  'data-testid': testId
}) {
  /* istanbul ignore next */
  cov_1cpmqwip5t().f[1]++;
  cov_1cpmqwip5t().s[5]++;
  // Performance monitoring in development
  usePerformanceMonitor('DashboardStats');

  // Memoize stats configuration to prevent recreation on every render
  const statsConfig =
  /* istanbul ignore next */
  (cov_1cpmqwip5t().s[6]++, React.useMemo(() => {
    /* istanbul ignore next */
    cov_1cpmqwip5t().f[2]++;
    cov_1cpmqwip5t().s[7]++;
    return [{
      icon: '📊',
      title: 'Total Renewals',
      value: stats.totalRenewals,
      key: 'totalRenewals'
    }, {
      icon: '⚠️',
      title: 'Renewals Due',
      value: stats.renewalsDue,
      key: 'renewalsDue'
    }, {
      icon: '🏢',
      title: 'Vendors',
      value: stats.vendors,
      key: 'vendors'
    }, {
      icon: '💰',
      title: 'Annual Spend',
      value: stats.annualSpend,
      key: 'annualSpend'
    }];
  }, [stats.totalRenewals, stats.renewalsDue, stats.vendors, stats.annualSpend]));
  /* istanbul ignore next */
  cov_1cpmqwip5t().s[8]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: `stats-grid ${className}`,
    /* istanbul ignore next */
    "data-testid": testId,
    role: "region",
    /* istanbul ignore next */
    "aria-label": "Dashboard Statistics",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 87,
      columnNumber: 5
    }
  }, statsConfig.map(stat => {
    /* istanbul ignore next */
    cov_1cpmqwip5t().f[3]++;
    cov_1cpmqwip5t().s[9]++;
    return /* istanbul ignore next */__jsx(StatCard,
    /* istanbul ignore next */
    {
      key: stat.key,
      icon: stat.icon,
      title: stat.title,
      value: stat.value,
      isLoading: isLoading,
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 94,
        columnNumber: 9
      }
    });
  }));
}));
export default DashboardStats;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfanN4RmlsZU5hbWUiLCJfX2pzeCIsIlJlYWN0IiwiY3JlYXRlRWxlbWVudCIsImNvdl8xY3BtcXdpcDV0IiwicGF0aCIsImhhc2giLCJnbG9iYWwiLCJGdW5jdGlvbiIsImdjdiIsImNvdmVyYWdlRGF0YSIsInN0YXRlbWVudE1hcCIsInN0YXJ0IiwibGluZSIsImNvbHVtbiIsImVuZCIsImZuTWFwIiwibmFtZSIsImRlY2wiLCJsb2MiLCJicmFuY2hNYXAiLCJ0eXBlIiwibG9jYXRpb25zIiwidW5kZWZpbmVkIiwicyIsImYiLCJiIiwiX2NvdmVyYWdlU2NoZW1hIiwiY292ZXJhZ2UiLCJhY3R1YWxDb3ZlcmFnZSIsIm1lbW8iLCJ1c2VQZXJmb3JtYW5jZU1vbml0b3IiLCJTdGF0Q2FyZCIsImljb24iLCJ0aXRsZSIsInZhbHVlIiwiaXNMb2FkaW5nIiwiY2xhc3NOYW1lIiwiX19zZWxmIiwiX19zb3VyY2UiLCJmaWxlTmFtZSIsImxpbmVOdW1iZXIiLCJjb2x1bW5OdW1iZXIiLCJEYXNoYm9hcmRTdGF0cyIsInN0YXRzIiwidGVzdElkIiwic3RhdHNDb25maWciLCJ1c2VNZW1vIiwidG90YWxSZW5ld2FscyIsImtleSIsInJlbmV3YWxzRHVlIiwidmVuZG9ycyIsImFubnVhbFNwZW5kIiwicm9sZSIsIm1hcCIsInN0YXQiXSwic291cmNlcyI6WyJEYXNoYm9hcmRTdGF0cy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBEYXNoYm9hcmQgU3RhdGlzdGljcyBDb21wb25lbnRcbiAqIFxuICogRGlzcGxheXMga2V5IG1ldHJpY3MgaW4gYSBncmlkIGxheW91dCB3aXRoIHByb3BlciBsb2FkaW5nIGFuZCBlcnJvciBzdGF0ZXMuXG4gKiBGb2N1c2VkIHJlc3BvbnNpYmlsaXR5OiBSZW5kZXJpbmcgc3RhdGlzdGljcyBjYXJkcyBvbmx5LlxuICovXG5cbid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QsIHsgbWVtbyB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgRGFzaGJvYXJkU3RhdHMgYXMgRGFzaGJvYXJkU3RhdHNUeXBlIH0gZnJvbSAnQC9saWIvdHlwZXMnXG5pbXBvcnQgeyBCYXNlQ29tcG9uZW50UHJvcHMgfSBmcm9tICdAL2xpYi90eXBlcydcbmltcG9ydCB7IHVzZVBlcmZvcm1hbmNlTW9uaXRvciB9IGZyb20gJ0AvbGliL3BlcmZvcm1hbmNlJ1xuXG5pbnRlcmZhY2UgRGFzaGJvYXJkU3RhdHNQcm9wcyBleHRlbmRzIEJhc2VDb21wb25lbnRQcm9wcyB7XG4gIHN0YXRzOiBEYXNoYm9hcmRTdGF0c1R5cGVcbiAgaXNMb2FkaW5nPzogYm9vbGVhblxufVxuXG5pbnRlcmZhY2UgU3RhdENhcmRQcm9wcyB7XG4gIGljb246IHN0cmluZ1xuICB0aXRsZTogc3RyaW5nXG4gIHZhbHVlOiBzdHJpbmcgfCBudW1iZXJcbiAgaXNMb2FkaW5nPzogYm9vbGVhblxufVxuXG5jb25zdCBTdGF0Q2FyZCA9IG1lbW8oZnVuY3Rpb24gU3RhdENhcmQoeyBpY29uLCB0aXRsZSwgdmFsdWUsIGlzTG9hZGluZyB9OiBTdGF0Q2FyZFByb3BzKSB7XG4gIGlmIChpc0xvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzdGF0LWNhcmRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzdGF0LWljb24gYW5pbWF0ZS1wdWxzZVwiPuKPszwvZGl2PlxuICAgICAgICA8aDM+e3RpdGxlfTwvaDM+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3RhdC12YWx1ZVwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1wdWxzZSBiZy1ncmF5LTIwMCBoLTYgdy0xNiByb3VuZGVkXCI+PC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInN0YXQtY2FyZFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzdGF0LWljb25cIj57aWNvbn08L2Rpdj5cbiAgICAgIDxoMz57dGl0bGV9PC9oMz5cbiAgICAgIDxwIGNsYXNzTmFtZT1cInN0YXQtdmFsdWVcIj57dmFsdWV9PC9wPlxuICAgIDwvZGl2PlxuICApXG59KVxuXG5jb25zdCBEYXNoYm9hcmRTdGF0cyA9IG1lbW8oZnVuY3Rpb24gRGFzaGJvYXJkU3RhdHMoe1xuICBzdGF0cyxcbiAgaXNMb2FkaW5nID0gZmFsc2UsXG4gIGNsYXNzTmFtZSA9ICcnLFxuICAnZGF0YS10ZXN0aWQnOiB0ZXN0SWRcbn06IERhc2hib2FyZFN0YXRzUHJvcHMpIHtcbiAgLy8gUGVyZm9ybWFuY2UgbW9uaXRvcmluZyBpbiBkZXZlbG9wbWVudFxuICB1c2VQZXJmb3JtYW5jZU1vbml0b3IoJ0Rhc2hib2FyZFN0YXRzJylcblxuICAvLyBNZW1vaXplIHN0YXRzIGNvbmZpZ3VyYXRpb24gdG8gcHJldmVudCByZWNyZWF0aW9uIG9uIGV2ZXJ5IHJlbmRlclxuICBjb25zdCBzdGF0c0NvbmZpZyA9IFJlYWN0LnVzZU1lbW8oKCkgPT4gW1xuICAgIHtcbiAgICAgIGljb246ICfwn5OKJyxcbiAgICAgIHRpdGxlOiAnVG90YWwgUmVuZXdhbHMnLFxuICAgICAgdmFsdWU6IHN0YXRzLnRvdGFsUmVuZXdhbHMsXG4gICAgICBrZXk6ICd0b3RhbFJlbmV3YWxzJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWNvbjogJ+KaoO+4jycsXG4gICAgICB0aXRsZTogJ1JlbmV3YWxzIER1ZScsXG4gICAgICB2YWx1ZTogc3RhdHMucmVuZXdhbHNEdWUsXG4gICAgICBrZXk6ICdyZW5ld2Fsc0R1ZSdcbiAgICB9LFxuICAgIHtcbiAgICAgIGljb246ICfwn4+iJyxcbiAgICAgIHRpdGxlOiAnVmVuZG9ycycsXG4gICAgICB2YWx1ZTogc3RhdHMudmVuZG9ycyxcbiAgICAgIGtleTogJ3ZlbmRvcnMnXG4gICAgfSxcbiAgICB7XG4gICAgICBpY29uOiAn8J+SsCcsXG4gICAgICB0aXRsZTogJ0FubnVhbCBTcGVuZCcsXG4gICAgICB2YWx1ZTogc3RhdHMuYW5udWFsU3BlbmQsXG4gICAgICBrZXk6ICdhbm51YWxTcGVuZCdcbiAgICB9XG4gIF0sIFtzdGF0cy50b3RhbFJlbmV3YWxzLCBzdGF0cy5yZW5ld2Fsc0R1ZSwgc3RhdHMudmVuZG9ycywgc3RhdHMuYW5udWFsU3BlbmRdKVxuXG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgY2xhc3NOYW1lPXtgc3RhdHMtZ3JpZCAke2NsYXNzTmFtZX1gfVxuICAgICAgZGF0YS10ZXN0aWQ9e3Rlc3RJZH1cbiAgICAgIHJvbGU9XCJyZWdpb25cIlxuICAgICAgYXJpYS1sYWJlbD1cIkRhc2hib2FyZCBTdGF0aXN0aWNzXCJcbiAgICA+XG4gICAgICB7c3RhdHNDb25maWcubWFwKChzdGF0KSA9PiAoXG4gICAgICAgIDxTdGF0Q2FyZFxuICAgICAgICAgIGtleT17c3RhdC5rZXl9XG4gICAgICAgICAgaWNvbj17c3RhdC5pY29ufVxuICAgICAgICAgIHRpdGxlPXtzdGF0LnRpdGxlfVxuICAgICAgICAgIHZhbHVlPXtzdGF0LnZhbHVlfVxuICAgICAgICAgIGlzTG9hZGluZz17aXNMb2FkaW5nfVxuICAgICAgICAvPlxuICAgICAgKSl9XG4gICAgPC9kaXY+XG4gIClcbn0pXG5cbmV4cG9ydCBkZWZhdWx0IERhc2hib2FyZFN0YXRzXG4iXSwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxZQUFZOztBQUFBO0FBQUEsSUFBQUEsWUFBQTtBQUFBLElBQUFDLEtBQUEsR0FBQUMsS0FBQSxDQUFBQyxhQUFBO0FBQUEsU0FBQUMsZUFBQTtFQUFBLElBQUFDLElBQUE7RUFBQSxJQUFBQyxJQUFBO0VBQUEsSUFBQUMsTUFBQSxPQUFBQyxRQUFBO0VBQUEsSUFBQUMsR0FBQTtFQUFBLElBQUFDLFlBQUE7SUFBQUwsSUFBQTtJQUFBTSxZQUFBO01BQUE7UUFBQUMsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7SUFBQTtJQUFBRSxLQUFBO01BQUE7UUFBQUMsSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQUksSUFBQTtRQUFBQyxJQUFBO1VBQUFOLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFLLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO0lBQUE7SUFBQU8sU0FBQTtNQUFBO1FBQUFELEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7SUFBQTtJQUFBVyxDQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7SUFBQTtJQUFBQyxDQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7SUFBQTtJQUFBQyxDQUFBO01BQUE7TUFBQTtNQUFBO0lBQUE7SUFBQUMsZUFBQTtJQUFBckIsSUFBQTtFQUFBO0VBQUEsSUFBQXNCLFFBQUEsR0FBQXJCLE1BQUEsQ0FBQUUsR0FBQSxNQUFBRixNQUFBLENBQUFFLEdBQUE7RUFBQSxLQUFBbUIsUUFBQSxDQUFBdkIsSUFBQSxLQUFBdUIsUUFBQSxDQUFBdkIsSUFBQSxFQUFBQyxJQUFBLEtBQUFBLElBQUE7SUFBQXNCLFFBQUEsQ0FBQXZCLElBQUEsSUFBQUssWUFBQTtFQUFBO0VBQUEsSUFBQW1CLGNBQUEsR0FBQUQsUUFBQSxDQUFBdkIsSUFBQTtFQUFBO0lBUUE7SUFBQUQsY0FBQSxZQUFBQSxDQUFBO01BQUEsT0FBQXlCLGNBQUE7SUFBQTtFQUFBO0VBQUEsT0FBQUEsY0FBQTtBQUFBO0FBQUF6QixjQUFBO0FBTlosT0FBT0YsS0FBSyxJQUFJNEIsSUFBSSxRQUFRLE9BQU87QUFHbkMsU0FBU0MscUJBQXFCLFFBQVEsbUJBQW1CO0FBY3pELE1BQU1DLFFBQVE7QUFBQTtBQUFBLENBQUE1QixjQUFBLEdBQUFvQixDQUFBLG9CQUFHTSxJQUFJLENBQUMsU0FBU0UsUUFBUUEsQ0FBQztFQUFFQyxJQUFJO0VBQUVDLEtBQUs7RUFBRUMsS0FBSztFQUFFQztBQUF5QixDQUFDLEVBQUU7RUFBQTtFQUFBaEMsY0FBQSxHQUFBcUIsQ0FBQTtFQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtFQUN4RixJQUFJWSxTQUFTLEVBQUU7SUFBQTtJQUFBaEMsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUNiLE9BQ0UsMEJBQUF2QixLQUFBO0lBQUE7SUFBQTtJQUFBO0lBQUE7TUFBS29DLFNBQVMsRUFBQyxXQUFXO01BQUFDLE1BQUE7TUFBQUMsUUFBQTtRQUFBQyxRQUFBLEVBQUF4QyxZQUFBO1FBQUF5QyxVQUFBO1FBQUFDLFlBQUE7TUFBQTtJQUFBO0lBQ3hCO0lBQUF6QyxLQUFBO0lBQUE7SUFBQTtJQUFBO0lBQUE7TUFBS29DLFNBQVMsRUFBQyx5QkFBeUI7TUFBQUMsTUFBQTtNQUFBQyxRQUFBO1FBQUFDLFFBQUEsRUFBQXhDLFlBQUE7UUFBQXlDLFVBQUE7UUFBQUMsWUFBQTtNQUFBO0lBQUEsR0FBQyxRQUFNLENBQUM7SUFDaEQ7SUFBQXpDLEtBQUE7SUFBQTtJQUFBO0lBQUE7SUFBQTtNQUFBcUMsTUFBQTtNQUFBQyxRQUFBO1FBQUFDLFFBQUEsRUFBQXhDLFlBQUE7UUFBQXlDLFVBQUE7UUFBQUMsWUFBQTtNQUFBO0lBQUEsR0FBS1IsS0FBVSxDQUFDO0lBQ2hCO0lBQUFqQyxLQUFBO0lBQUE7SUFBQTtJQUFBO0lBQUE7TUFBS29DLFNBQVMsRUFBQyxZQUFZO01BQUFDLE1BQUE7TUFBQUMsUUFBQTtRQUFBQyxRQUFBLEVBQUF4QyxZQUFBO1FBQUF5QyxVQUFBO1FBQUFDLFlBQUE7TUFBQTtJQUFBO0lBQ3pCO0lBQUF6QyxLQUFBO0lBQUE7SUFBQTtJQUFBO0lBQUE7TUFBS29DLFNBQVMsRUFBQyw0Q0FBNEM7TUFBQUMsTUFBQTtNQUFBQyxRQUFBO1FBQUFDLFFBQUEsRUFBQXhDLFlBQUE7UUFBQXlDLFVBQUE7UUFBQUMsWUFBQTtNQUFBO0lBQUEsQ0FBTSxDQUM5RCxDQUNGLENBQUM7RUFFVixDQUFDO0VBQUE7RUFBQTtJQUFBdEMsY0FBQSxHQUFBc0IsQ0FBQTtFQUFBO0VBQUF0QixjQUFBLEdBQUFvQixDQUFBO0VBRUQsT0FDRSwwQkFBQXZCLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUFLb0MsU0FBUyxFQUFDLFdBQVc7SUFBQUMsTUFBQTtJQUFBQyxRQUFBO01BQUFDLFFBQUEsRUFBQXhDLFlBQUE7TUFBQXlDLFVBQUE7TUFBQUMsWUFBQTtJQUFBO0VBQUE7RUFDeEI7RUFBQXpDLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUFLb0MsU0FBUyxFQUFDLFdBQVc7SUFBQUMsTUFBQTtJQUFBQyxRQUFBO01BQUFDLFFBQUEsRUFBQXhDLFlBQUE7TUFBQXlDLFVBQUE7TUFBQUMsWUFBQTtJQUFBO0VBQUEsR0FBRVQsSUFBVSxDQUFDO0VBQ3ZDO0VBQUFoQyxLQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFBQXFDLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUF4QyxZQUFBO01BQUF5QyxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBLEdBQUtSLEtBQVUsQ0FBQztFQUNoQjtFQUFBakMsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQUdvQyxTQUFTLEVBQUMsWUFBWTtJQUFBQyxNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBeEMsWUFBQTtNQUFBeUMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQSxHQUFFUCxLQUFTLENBQ2pDLENBQUM7QUFFVixDQUFDLENBQUM7QUFFRixNQUFNUSxjQUFjO0FBQUE7QUFBQSxDQUFBdkMsY0FBQSxHQUFBb0IsQ0FBQSxvQkFBR00sSUFBSSxDQUFDLFNBQVNhLGNBQWNBLENBQUM7RUFDbERDLEtBQUs7RUFDTFIsU0FBUztFQUFBO0VBQUEsQ0FBQWhDLGNBQUEsR0FBQXNCLENBQUEsVUFBRyxLQUFLO0VBQ2pCVyxTQUFTO0VBQUE7RUFBQSxDQUFBakMsY0FBQSxHQUFBc0IsQ0FBQSxVQUFHLEVBQUU7RUFDZCxhQUFhLEVBQUVtQjtBQUNJLENBQUMsRUFBRTtFQUFBO0VBQUF6QyxjQUFBLEdBQUFxQixDQUFBO0VBQUFyQixjQUFBLEdBQUFvQixDQUFBO0VBQ3RCO0VBQ0FPLHFCQUFxQixDQUFDLGdCQUFnQixDQUFDOztFQUV2QztFQUNBLE1BQU1lLFdBQVc7RUFBQTtFQUFBLENBQUExQyxjQUFBLEdBQUFvQixDQUFBLE9BQUd0QixLQUFLLENBQUM2QyxPQUFPLENBQUMsTUFBTTtJQUFBO0lBQUEzQyxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQUEsUUFDdEM7TUFDRVMsSUFBSSxFQUFFLElBQUk7TUFDVkMsS0FBSyxFQUFFLGdCQUFnQjtNQUN2QkMsS0FBSyxFQUFFUyxLQUFLLENBQUNJLGFBQWE7TUFDMUJDLEdBQUcsRUFBRTtJQUNQLENBQUMsRUFDRDtNQUNFaEIsSUFBSSxFQUFFLElBQUk7TUFDVkMsS0FBSyxFQUFFLGNBQWM7TUFDckJDLEtBQUssRUFBRVMsS0FBSyxDQUFDTSxXQUFXO01BQ3hCRCxHQUFHLEVBQUU7SUFDUCxDQUFDLEVBQ0Q7TUFDRWhCLElBQUksRUFBRSxJQUFJO01BQ1ZDLEtBQUssRUFBRSxTQUFTO01BQ2hCQyxLQUFLLEVBQUVTLEtBQUssQ0FBQ08sT0FBTztNQUNwQkYsR0FBRyxFQUFFO0lBQ1AsQ0FBQyxFQUNEO01BQ0VoQixJQUFJLEVBQUUsSUFBSTtNQUNWQyxLQUFLLEVBQUUsY0FBYztNQUNyQkMsS0FBSyxFQUFFUyxLQUFLLENBQUNRLFdBQVc7TUFDeEJILEdBQUcsRUFBRTtJQUNQLENBQUMsQ0FDRjtFQUFELENBQUMsRUFBRSxDQUFDTCxLQUFLLENBQUNJLGFBQWEsRUFBRUosS0FBSyxDQUFDTSxXQUFXLEVBQUVOLEtBQUssQ0FBQ08sT0FBTyxFQUFFUCxLQUFLLENBQUNRLFdBQVcsQ0FBQyxDQUFDO0VBQUE7RUFBQWhELGNBQUEsR0FBQW9CLENBQUE7RUFFOUUsT0FDRSwwQkFBQXZCLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUNFb0MsU0FBUyxFQUFFLGNBQWNBLFNBQVMsRUFBRztJQUNyQztJQUFBLGVBQWFRLE1BQU87SUFDcEJRLElBQUksRUFBQyxRQUFRO0lBQ2I7SUFBQSxjQUFXLHNCQUFzQjtJQUFBZixNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBeEMsWUFBQTtNQUFBeUMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQSxHQUVoQ0ksV0FBVyxDQUFDUSxHQUFHLENBQUVDLElBQUksSUFDcEI7SUFBQTtJQUFBbkQsY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtJQUFBLGlDQUFBdkIsS0FBQSxDQUFDK0IsUUFBUTtJQUFBO0lBQUE7TUFDUGlCLEdBQUcsRUFBRU0sSUFBSSxDQUFDTixHQUFJO01BQ2RoQixJQUFJLEVBQUVzQixJQUFJLENBQUN0QixJQUFLO01BQ2hCQyxLQUFLLEVBQUVxQixJQUFJLENBQUNyQixLQUFNO01BQ2xCQyxLQUFLLEVBQUVvQixJQUFJLENBQUNwQixLQUFNO01BQ2xCQyxTQUFTLEVBQUVBLFNBQVU7TUFBQUUsTUFBQTtNQUFBQyxRQUFBO1FBQUFDLFFBQUEsRUFBQXhDLFlBQUE7UUFBQXlDLFVBQUE7UUFBQUMsWUFBQTtNQUFBO0lBQUEsQ0FDdEIsQ0FBQztFQUFELENBQ0YsQ0FDRSxDQUFDO0FBRVYsQ0FBQyxDQUFDO0FBRUYsZUFBZUMsY0FBYyIsImlnbm9yZUxpc3QiOltdfQ==