7f2d674a7ae2ec9f698b8c81ecc15c4a
/* istanbul ignore next */
"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.isApiResponse = isApiResponse;
exports.isAuthSession = isAuthSession;
exports.isClient = isClient;
exports.isCognitoJwtPayload = isCognitoJwtPayload;
exports.isPaginatedResponse = isPaginatedResponse;
exports.isRenewal = isRenewal;
exports.isTenantContext = isTenantContext;
exports.isUser = isUser;
function cov_1emzugtcs7() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\types.ts";
  var hash = "1cd7c37b8011d91a5cb51b2ff00d4d404405c81e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\types.ts",
    statementMap: {
      "0": {
        start: {
          line: 214,
          column: 2
        },
        end: {
          line: 221,
          column: 4
        }
      },
      "1": {
        start: {
          line: 225,
          column: 2
        },
        end: {
          line: 232,
          column: 4
        }
      },
      "2": {
        start: {
          line: 236,
          column: 2
        },
        end: {
          line: 245,
          column: 4
        }
      },
      "3": {
        start: {
          line: 249,
          column: 2
        },
        end: {
          line: 256,
          column: 4
        }
      },
      "4": {
        start: {
          line: 260,
          column: 2
        },
        end: {
          line: 265,
          column: 4
        }
      },
      "5": {
        start: {
          line: 269,
          column: 2
        },
        end: {
          line: 276,
          column: 4
        }
      },
      "6": {
        start: {
          line: 280,
          column: 2
        },
        end: {
          line: 287,
          column: 4
        }
      },
      "7": {
        start: {
          line: 291,
          column: 2
        },
        end: {
          line: 299,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "isUser",
        decl: {
          start: {
            line: 213,
            column: 16
          },
          end: {
            line: 213,
            column: 22
          }
        },
        loc: {
          start: {
            line: 213,
            column: 46
          },
          end: {
            line: 222,
            column: 1
          }
        },
        line: 213
      },
      "1": {
        name: "isClient",
        decl: {
          start: {
            line: 224,
            column: 16
          },
          end: {
            line: 224,
            column: 24
          }
        },
        loc: {
          start: {
            line: 224,
            column: 50
          },
          end: {
            line: 233,
            column: 1
          }
        },
        line: 224
      },
      "2": {
        name: "isTenantContext",
        decl: {
          start: {
            line: 235,
            column: 16
          },
          end: {
            line: 235,
            column: 31
          }
        },
        loc: {
          start: {
            line: 235,
            column: 64
          },
          end: {
            line: 246,
            column: 1
          }
        },
        line: 235
      },
      "3": {
        name: "isRenewal",
        decl: {
          start: {
            line: 248,
            column: 16
          },
          end: {
            line: 248,
            column: 25
          }
        },
        loc: {
          start: {
            line: 248,
            column: 52
          },
          end: {
            line: 257,
            column: 1
          }
        },
        line: 248
      },
      "4": {
        name: "isApiResponse",
        decl: {
          start: {
            line: 259,
            column: 16
          },
          end: {
            line: 259,
            column: 29
          }
        },
        loc: {
          start: {
            line: 259,
            column: 66
          },
          end: {
            line: 266,
            column: 1
          }
        },
        line: 259
      },
      "5": {
        name: "isPaginatedResponse",
        decl: {
          start: {
            line: 268,
            column: 16
          },
          end: {
            line: 268,
            column: 35
          }
        },
        loc: {
          start: {
            line: 268,
            column: 78
          },
          end: {
            line: 277,
            column: 1
          }
        },
        line: 268
      },
      "6": {
        name: "isAuthSession",
        decl: {
          start: {
            line: 279,
            column: 16
          },
          end: {
            line: 279,
            column: 29
          }
        },
        loc: {
          start: {
            line: 279,
            column: 60
          },
          end: {
            line: 288,
            column: 1
          }
        },
        line: 279
      },
      "7": {
        name: "isCognitoJwtPayload",
        decl: {
          start: {
            line: 290,
            column: 16
          },
          end: {
            line: 290,
            column: 35
          }
        },
        loc: {
          start: {
            line: 290,
            column: 72
          },
          end: {
            line: 300,
            column: 1
          }
        },
        line: 290
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 215,
            column: 4
          },
          end: {
            line: 220,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 215,
            column: 4
          },
          end: {
            line: 215,
            column: 7
          }
        }, {
          start: {
            line: 216,
            column: 4
          },
          end: {
            line: 216,
            column: 27
          }
        }, {
          start: {
            line: 217,
            column: 4
          },
          end: {
            line: 217,
            column: 30
          }
        }, {
          start: {
            line: 218,
            column: 4
          },
          end: {
            line: 218,
            column: 33
          }
        }, {
          start: {
            line: 219,
            column: 4
          },
          end: {
            line: 219,
            column: 28
          }
        }, {
          start: {
            line: 220,
            column: 4
          },
          end: {
            line: 220,
            column: 71
          }
        }],
        line: 215
      },
      "1": {
        loc: {
          start: {
            line: 226,
            column: 4
          },
          end: {
            line: 231,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 226,
            column: 4
          },
          end: {
            line: 226,
            column: 7
          }
        }, {
          start: {
            line: 227,
            column: 4
          },
          end: {
            line: 227,
            column: 27
          }
        }, {
          start: {
            line: 228,
            column: 4
          },
          end: {
            line: 228,
            column: 30
          }
        }, {
          start: {
            line: 229,
            column: 4
          },
          end: {
            line: 229,
            column: 32
          }
        }, {
          start: {
            line: 230,
            column: 4
          },
          end: {
            line: 230,
            column: 34
          }
        }, {
          start: {
            line: 231,
            column: 4
          },
          end: {
            line: 231,
            column: 60
          }
        }],
        line: 226
      },
      "2": {
        loc: {
          start: {
            line: 237,
            column: 4
          },
          end: {
            line: 244,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 237,
            column: 4
          },
          end: {
            line: 237,
            column: 7
          }
        }, {
          start: {
            line: 238,
            column: 4
          },
          end: {
            line: 238,
            column: 27
          }
        }, {
          start: {
            line: 239,
            column: 4
          },
          end: {
            line: 239,
            column: 36
          }
        }, {
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 240,
            column: 38
          }
        }, {
          start: {
            line: 241,
            column: 4
          },
          end: {
            line: 241,
            column: 36
          }
        }, {
          start: {
            line: 242,
            column: 4
          },
          end: {
            line: 242,
            column: 40
          }
        }, {
          start: {
            line: 243,
            column: 4
          },
          end: {
            line: 243,
            column: 30
          }
        }, {
          start: {
            line: 244,
            column: 4
          },
          end: {
            line: 244,
            column: 37
          }
        }],
        line: 237
      },
      "3": {
        loc: {
          start: {
            line: 250,
            column: 4
          },
          end: {
            line: 255,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 250,
            column: 4
          },
          end: {
            line: 250,
            column: 7
          }
        }, {
          start: {
            line: 251,
            column: 4
          },
          end: {
            line: 251,
            column: 27
          }
        }, {
          start: {
            line: 252,
            column: 4
          },
          end: {
            line: 252,
            column: 30
          }
        }, {
          start: {
            line: 253,
            column: 4
          },
          end: {
            line: 253,
            column: 32
          }
        }, {
          start: {
            line: 254,
            column: 4
          },
          end: {
            line: 254,
            column: 34
          }
        }, {
          start: {
            line: 255,
            column: 4
          },
          end: {
            line: 255,
            column: 69
          }
        }],
        line: 250
      },
      "4": {
        loc: {
          start: {
            line: 261,
            column: 4
          },
          end: {
            line: 264,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 261,
            column: 4
          },
          end: {
            line: 261,
            column: 7
          }
        }, {
          start: {
            line: 262,
            column: 4
          },
          end: {
            line: 262,
            column: 27
          }
        }, {
          start: {
            line: 263,
            column: 4
          },
          end: {
            line: 263,
            column: 36
          }
        }, {
          start: {
            line: 264,
            column: 4
          },
          end: {
            line: 264,
            column: 37
          }
        }],
        line: 261
      },
      "5": {
        loc: {
          start: {
            line: 270,
            column: 4
          },
          end: {
            line: 275,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 270,
            column: 4
          },
          end: {
            line: 270,
            column: 22
          }
        }, {
          start: {
            line: 271,
            column: 4
          },
          end: {
            line: 271,
            column: 18
          }
        }, {
          start: {
            line: 272,
            column: 4
          },
          end: {
            line: 272,
            column: 38
          }
        }, {
          start: {
            line: 273,
            column: 4
          },
          end: {
            line: 273,
            column: 43
          }
        }, {
          start: {
            line: 274,
            column: 4
          },
          end: {
            line: 274,
            column: 44
          }
        }, {
          start: {
            line: 275,
            column: 4
          },
          end: {
            line: 275,
            column: 49
          }
        }],
        line: 270
      },
      "6": {
        loc: {
          start: {
            line: 281,
            column: 4
          },
          end: {
            line: 286,
            column: 28
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 281,
            column: 4
          },
          end: {
            line: 281,
            column: 7
          }
        }, {
          start: {
            line: 282,
            column: 4
          },
          end: {
            line: 282,
            column: 27
          }
        }, {
          start: {
            line: 283,
            column: 4
          },
          end: {
            line: 283,
            column: 35
          }
        }, {
          start: {
            line: 284,
            column: 4
          },
          end: {
            line: 284,
            column: 34
          }
        }, {
          start: {
            line: 285,
            column: 4
          },
          end: {
            line: 285,
            column: 33
          }
        }, {
          start: {
            line: 286,
            column: 4
          },
          end: {
            line: 286,
            column: 28
          }
        }],
        line: 281
      },
      "7": {
        loc: {
          start: {
            line: 292,
            column: 4
          },
          end: {
            line: 298,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 292,
            column: 4
          },
          end: {
            line: 292,
            column: 7
          }
        }, {
          start: {
            line: 293,
            column: 4
          },
          end: {
            line: 293,
            column: 27
          }
        }, {
          start: {
            line: 294,
            column: 4
          },
          end: {
            line: 294,
            column: 31
          }
        }, {
          start: {
            line: 295,
            column: 4
          },
          end: {
            line: 295,
            column: 33
          }
        }, {
          start: {
            line: 296,
            column: 4
          },
          end: {
            line: 296,
            column: 31
          }
        }, {
          start: {
            line: 297,
            column: 4
          },
          end: {
            line: 297,
            column: 31
          }
        }, {
          start: {
            line: 298,
            column: 4
          },
          end: {
            line: 298,
            column: 44
          }
        }],
        line: 292
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0
    },
    b: {
      "0": [0, 0, 0, 0, 0, 0],
      "1": [0, 0, 0, 0, 0, 0],
      "2": [0, 0, 0, 0, 0, 0, 0, 0],
      "3": [0, 0, 0, 0, 0, 0],
      "4": [0, 0, 0, 0],
      "5": [0, 0, 0, 0, 0, 0],
      "6": [0, 0, 0, 0, 0, 0],
      "7": [0, 0, 0, 0, 0, 0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1cd7c37b8011d91a5cb51b2ff00d4d404405c81e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1emzugtcs7 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1emzugtcs7();
/**
 * Comprehensive Type Definitions and Type Guards
 * 
 * This module provides centralized type definitions and type guard functions
 * to ensure type safety throughout the application.
 */

// Base entity interface

// User types

// Client/Tenant types

// Renewal types

// Vendor types

// API Response types

// Authentication types

// Database types

// Form types

// Component prop types

// Type Guards
function isUser(obj) {
  /* istanbul ignore next */
  cov_1emzugtcs7().f[0]++;
  cov_1emzugtcs7().s[0]++;
  return /* istanbul ignore next */(cov_1emzugtcs7().b[0][0]++, obj) &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[0][1]++, typeof obj === 'object') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[0][2]++, typeof obj.id === 'string') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[0][3]++, typeof obj.email === 'string') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[0][4]++, Array.isArray(obj.roles)) &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[0][5]++, ['active', 'inactive', 'suspended', 'deleted'].includes(obj.status));
}
function isClient(obj) {
  /* istanbul ignore next */
  cov_1emzugtcs7().f[1]++;
  cov_1emzugtcs7().s[1]++;
  return /* istanbul ignore next */(cov_1emzugtcs7().b[1][0]++, obj) &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[1][1]++, typeof obj === 'object') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[1][2]++, typeof obj.id === 'string') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[1][3]++, typeof obj.name === 'string') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[1][4]++, typeof obj.domain === 'string') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[1][5]++, ['active', 'inactive', 'suspended'].includes(obj.status));
}
function isTenantContext(obj) {
  /* istanbul ignore next */
  cov_1emzugtcs7().f[2]++;
  cov_1emzugtcs7().s[2]++;
  return /* istanbul ignore next */(cov_1emzugtcs7().b[2][0]++, obj) &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[2][1]++, typeof obj === 'object') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[2][2]++, typeof obj.clientId === 'string') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[2][3]++, typeof obj.clientName === 'string') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[2][4]++, typeof obj.tenantId === 'string') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[2][5]++, typeof obj.tenantSchema === 'string') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[2][6]++, Array.isArray(obj.domains)) &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[2][7]++, typeof obj.isActive === 'boolean');
}
function isRenewal(obj) {
  /* istanbul ignore next */
  cov_1emzugtcs7().f[3]++;
  cov_1emzugtcs7().s[3]++;
  return /* istanbul ignore next */(cov_1emzugtcs7().b[3][0]++, obj) &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[3][1]++, typeof obj === 'object') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[3][2]++, typeof obj.id === 'string') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[3][3]++, typeof obj.name === 'string') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[3][4]++, typeof obj.vendor === 'string') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[3][5]++, ['active', 'inactive', 'pending', 'expired'].includes(obj.status));
}
function isApiResponse(obj) {
  /* istanbul ignore next */
  cov_1emzugtcs7().f[4]++;
  cov_1emzugtcs7().s[4]++;
  return /* istanbul ignore next */(cov_1emzugtcs7().b[4][0]++, obj) &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[4][1]++, typeof obj === 'object') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[4][2]++, typeof obj.success === 'boolean') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[4][3]++, typeof obj.timestamp === 'string');
}
function isPaginatedResponse(obj) {
  /* istanbul ignore next */
  cov_1emzugtcs7().f[5]++;
  cov_1emzugtcs7().s[5]++;
  return /* istanbul ignore next */(cov_1emzugtcs7().b[5][0]++, isApiResponse(obj)) &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[5][1]++, obj.pagination) &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[5][2]++, typeof obj.pagination === 'object') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[5][3]++, typeof obj.pagination.page === 'number') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[5][4]++, typeof obj.pagination.limit === 'number') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[5][5]++, typeof obj.pagination.totalCount === 'number');
}
function isAuthSession(obj) {
  /* istanbul ignore next */
  cov_1emzugtcs7().f[6]++;
  cov_1emzugtcs7().s[6]++;
  return /* istanbul ignore next */(cov_1emzugtcs7().b[6][0]++, obj) &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[6][1]++, typeof obj === 'object') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[6][2]++, typeof obj.isAuth === 'boolean') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[6][3]++, typeof obj.userId === 'string') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[6][4]++, typeof obj.email === 'string') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[6][5]++, Array.isArray(obj.roles));
}
function isCognitoJwtPayload(obj) {
  /* istanbul ignore next */
  cov_1emzugtcs7().f[7]++;
  cov_1emzugtcs7().s[7]++;
  return /* istanbul ignore next */(cov_1emzugtcs7().b[7][0]++, obj) &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[7][1]++, typeof obj === 'object') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[7][2]++, typeof obj.sub === 'string') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[7][3]++, typeof obj.email === 'string') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[7][4]++, typeof obj.exp === 'number') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[7][5]++, typeof obj.iat === 'number') &&
  /* istanbul ignore next */
  (cov_1emzugtcs7().b[7][6]++, ['id', 'access'].includes(obj.token_use));
}

// Utility types

// Event types

// Error types

// Configuration types

// Dashboard specific types

// Export all types for easy importing
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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