import { getDbPool } from '@/lib/db-config';
import { Client, ClientSettings } from '@/types/client';

// Get tenant by email domain
export async function getTenantByDomain(domain: string) {
  try {
    const pool = await getDbPool();
    const query = `
      SELECT t.tenant_id, t.tenant_name, t.schema_name, t.subdomain, t.status, t.created_at
      FROM tenant_management.tenants t
      JOIN tenant_management.domains d ON t.tenant_id = d.tenant_id
      WHERE d.domain_name = $1 AND t.status = 'active'
      LIMIT 1
    `;
    
    const result = await pool.query(query, [domain]);
    return result.rows.length > 0 ? result.rows[0] : null;
  } catch (error) {
    console.error('Error fetching tenant by domain:', error);
    return null;
  }
}

// Get client by domain (replacing DynamoDB with PostgreSQL)
export async function getClientByDomain(domain: string) {
  if (!domain) {
    throw new Error('Domain parameter is required');
  }
  
  try {
    const pool = await getDbPool();
    const query = `
      SELECT 
        client_id as id, 
        name, 
        domain, 
        status, 
        settings, 
        created_at, 
        updated_at
      FROM tenant_management.clients
      WHERE domain = $1 AND status = 'active'
      LIMIT 1
    `;
    
    const result = await pool.query(query, [domain]);
    
    if (result.rows.length === 0) {
      return null;
    }
    
    // Parse JSON settings if needed
    const client = result.rows[0];
    if (client.settings && typeof client.settings === 'string') {
      client.settings = JSON.parse(client.settings);
    }
    
    return client;
  } catch (error) {
    // More detailed error logging
    console.error('Error fetching client by domain:', {
      domain,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    
    // Rethrow with a cleaner message for API consumers
    throw new Error('Failed to retrieve client information');
  }
}

// Get client by email domain
export async function getClientByEmailDomain(email: string) {
  if (!email) return null;

  try {
    const domain = email.split('@')[1];
    if (!domain) return null;

    const pool = await getDbPool();
    const query = `
      SELECT
        "ClientID" as id,
        "ClientName" as name,
        "ClientDomain" as domain,
        "Active" as status,
        '{}' as settings,
        "CreatedOn" as created_at,
        "ChangedOn" as updated_at
      FROM metadata."Clients"
      WHERE $1 = ANY("ClientDomain") AND "Active" = true
      LIMIT 1
    `;

    const result = await pool.query(query, [domain]);

    if (result.rows.length > 0) {
      // Parse JSON settings if needed
      const client = result.rows[0];
      if (client.settings && typeof client.settings === 'string') {
        client.settings = JSON.parse(client.settings);
      }

      return client;
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error fetching client by email domain:', {
      email,
      error: error instanceof Error ? error.message : String(error)
    });
    return null;
  }
}

// Create a new client
export async function createClient(clientData: {
  name: string;
  domain: string;
  status?: string;
  settings?: Record<string, any>;
}) {
  try {
    const pool = await getDbPool();
    const query = `
      INSERT INTO tenant_management.clients (
        name, 
        domain, 
        status, 
        settings
      ) 
      VALUES ($1, $2, $3, $4)
      RETURNING 
        client_id as id, 
        name, 
        domain, 
        status, 
        settings, 
        created_at, 
        updated_at
    `;
    
    const values = [
      clientData.name,
      clientData.domain,
      clientData.status || 'active',
      clientData.settings ? JSON.stringify(clientData.settings) : '{}'
    ];
    
    const result = await pool.query(query, values);
    return result.rows[0];
  } catch (error) {
    console.error('Error creating client:', error);
    throw new Error('Failed to create client');
  }
}

// Update an existing client
export async function updateClient(
  clientId: string,
  updates: Partial<{
    name: string;
    domain: string;
    status: string;
    settings: Record<string, any>;
  }>
) {
  try {
    const pool = await getDbPool();
    
    // Build dynamic update query
    const updateFields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;
    
    if (updates.name !== undefined) {
      updateFields.push(`name = $${paramIndex++}`);
      values.push(updates.name);
    }
    
    if (updates.domain !== undefined) {
      updateFields.push(`domain = $${paramIndex++}`);
      values.push(updates.domain);
    }
    
    if (updates.status !== undefined) {
      updateFields.push(`status = $${paramIndex++}`);
      values.push(updates.status);
    }
    
    if (updates.settings !== undefined) {
      updateFields.push(`settings = $${paramIndex++}`);
      values.push(JSON.stringify(updates.settings));
    }
    
    if (updateFields.length === 0) {
      return null; // Nothing to update
    }
    
    // Add client_id to values array
    values.push(clientId);
    
    const query = `
      UPDATE tenant_management.clients
      SET ${updateFields.join(', ')}
      WHERE client_id = $${paramIndex}
      RETURNING 
        client_id as id, 
        name, 
        domain, 
        status, 
        settings, 
        created_at, 
        updated_at
    `;
    
    const result = await pool.query(query, values);
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return result.rows[0];
  } catch (error) {
    console.error('Error updating client:', error);
    throw new Error('Failed to update client');
  }
}




