5c9afdbcdc95e04778d5f7c87df68e9f
/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\ClientContext.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_1zkraysw0w() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\ClientContext.tsx";
  var hash = "5c94e99c8d6083b7fd08cfe31cc116892dd52b50";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\ClientContext.tsx",
    statementMap: {
      "0": {
        start: {
          line: 15,
          column: 22
        },
        end: {
          line: 22,
          column: 2
        }
      },
      "1": {
        start: {
          line: 18,
          column: 33
        },
        end: {
          line: 18,
          column: 37
        }
      },
      "2": {
        start: {
          line: 19,
          column: 29
        },
        end: {
          line: 19,
          column: 33
        }
      },
      "3": {
        start: {
          line: 20,
          column: 32
        },
        end: {
          line: 20,
          column: 36
        }
      },
      "4": {
        start: {
          line: 21,
          column: 36
        },
        end: {
          line: 21,
          column: 41
        }
      },
      "5": {
        start: {
          line: 25,
          column: 30
        },
        end: {
          line: 25,
          column: 59
        }
      },
      "6": {
        start: {
          line: 26,
          column: 36
        },
        end: {
          line: 26,
          column: 51
        }
      },
      "7": {
        start: {
          line: 27,
          column: 19
        },
        end: {
          line: 27,
          column: 28
        }
      },
      "8": {
        start: {
          line: 30,
          column: 28
        },
        end: {
          line: 53,
          column: 8
        }
      },
      "9": {
        start: {
          line: 31,
          column: 4
        },
        end: {
          line: 31,
          column: 29
        }
      },
      "10": {
        start: {
          line: 31,
          column: 17
        },
        end: {
          line: 31,
          column: 29
        }
      },
      "11": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 52,
          column: 5
        }
      },
      "12": {
        start: {
          line: 34,
          column: 6
        },
        end: {
          line: 34,
          column: 25
        }
      },
      "13": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 94
        }
      },
      "14": {
        start: {
          line: 37,
          column: 6
        },
        end: {
          line: 42,
          column: 7
        }
      },
      "15": {
        start: {
          line: 38,
          column: 8
        },
        end: {
          line: 40,
          column: 9
        }
      },
      "16": {
        start: {
          line: 39,
          column: 10
        },
        end: {
          line: 39,
          column: 73
        }
      },
      "17": {
        start: {
          line: 41,
          column: 8
        },
        end: {
          line: 41,
          column: 20
        }
      },
      "18": {
        start: {
          line: 44,
          column: 25
        },
        end: {
          line: 44,
          column: 46
        }
      },
      "19": {
        start: {
          line: 45,
          column: 6
        },
        end: {
          line: 45,
          column: 28
        }
      },
      "20": {
        start: {
          line: 46,
          column: 6
        },
        end: {
          line: 46,
          column: 24
        }
      },
      "21": {
        start: {
          line: 48,
          column: 6
        },
        end: {
          line: 48,
          column: 57
        }
      },
      "22": {
        start: {
          line: 49,
          column: 6
        },
        end: {
          line: 49,
          column: 18
        }
      },
      "23": {
        start: {
          line: 51,
          column: 6
        },
        end: {
          line: 51,
          column: 26
        }
      },
      "24": {
        start: {
          line: 56,
          column: 24
        },
        end: {
          line: 79,
          column: 8
        }
      },
      "25": {
        start: {
          line: 57,
          column: 4
        },
        end: {
          line: 57,
          column: 25
        }
      },
      "26": {
        start: {
          line: 57,
          column: 13
        },
        end: {
          line: 57,
          column: 25
        }
      },
      "27": {
        start: {
          line: 59,
          column: 4
        },
        end: {
          line: 78,
          column: 5
        }
      },
      "28": {
        start: {
          line: 60,
          column: 6
        },
        end: {
          line: 60,
          column: 25
        }
      },
      "29": {
        start: {
          line: 61,
          column: 23
        },
        end: {
          line: 61,
          column: 76
        }
      },
      "30": {
        start: {
          line: 63,
          column: 6
        },
        end: {
          line: 68,
          column: 7
        }
      },
      "31": {
        start: {
          line: 64,
          column: 8
        },
        end: {
          line: 66,
          column: 9
        }
      },
      "32": {
        start: {
          line: 65,
          column: 10
        },
        end: {
          line: 65,
          column: 73
        }
      },
      "33": {
        start: {
          line: 67,
          column: 8
        },
        end: {
          line: 67,
          column: 20
        }
      },
      "34": {
        start: {
          line: 70,
          column: 25
        },
        end: {
          line: 70,
          column: 46
        }
      },
      "35": {
        start: {
          line: 71,
          column: 6
        },
        end: {
          line: 71,
          column: 28
        }
      },
      "36": {
        start: {
          line: 72,
          column: 6
        },
        end: {
          line: 72,
          column: 24
        }
      },
      "37": {
        start: {
          line: 74,
          column: 6
        },
        end: {
          line: 74,
          column: 57
        }
      },
      "38": {
        start: {
          line: 75,
          column: 6
        },
        end: {
          line: 75,
          column: 18
        }
      },
      "39": {
        start: {
          line: 77,
          column: 6
        },
        end: {
          line: 77,
          column: 26
        }
      },
      "40": {
        start: {
          line: 82,
          column: 31
        },
        end: {
          line: 109,
          column: 18
        }
      },
      "41": {
        start: {
          line: 83,
          column: 4
        },
        end: {
          line: 83,
          column: 34
        }
      },
      "42": {
        start: {
          line: 83,
          column: 21
        },
        end: {
          line: 83,
          column: 34
        }
      },
      "43": {
        start: {
          line: 85,
          column: 4
        },
        end: {
          line: 108,
          column: 5
        }
      },
      "44": {
        start: {
          line: 86,
          column: 6
        },
        end: {
          line: 86,
          column: 25
        }
      },
      "45": {
        start: {
          line: 87,
          column: 23
        },
        end: {
          line: 93,
          column: 8
        }
      },
      "46": {
        start: {
          line: 95,
          column: 6
        },
        end: {
          line: 98,
          column: 7
        }
      },
      "47": {
        start: {
          line: 96,
          column: 8
        },
        end: {
          line: 96,
          column: 80
        }
      },
      "48": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 97,
          column: 21
        }
      },
      "49": {
        start: {
          line: 100,
          column: 28
        },
        end: {
          line: 100,
          column: 49
        }
      },
      "50": {
        start: {
          line: 101,
          column: 6
        },
        end: {
          line: 101,
          column: 31
        }
      },
      "51": {
        start: {
          line: 102,
          column: 6
        },
        end: {
          line: 102,
          column: 18
        }
      },
      "52": {
        start: {
          line: 104,
          column: 6
        },
        end: {
          line: 104,
          column: 62
        }
      },
      "53": {
        start: {
          line: 105,
          column: 6
        },
        end: {
          line: 105,
          column: 19
        }
      },
      "54": {
        start: {
          line: 107,
          column: 6
        },
        end: {
          line: 107,
          column: 26
        }
      },
      "55": {
        start: {
          line: 112,
          column: 27
        },
        end: {
          line: 135,
          column: 8
        }
      },
      "56": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 113,
          column: 28
        }
      },
      "57": {
        start: {
          line: 113,
          column: 16
        },
        end: {
          line: 113,
          column: 28
        }
      },
      "58": {
        start: {
          line: 115,
          column: 4
        },
        end: {
          line: 134,
          column: 5
        }
      },
      "59": {
        start: {
          line: 116,
          column: 6
        },
        end: {
          line: 116,
          column: 25
        }
      },
      "60": {
        start: {
          line: 117,
          column: 23
        },
        end: {
          line: 117,
          column: 92
        }
      },
      "61": {
        start: {
          line: 119,
          column: 6
        },
        end: {
          line: 124,
          column: 7
        }
      },
      "62": {
        start: {
          line: 120,
          column: 8
        },
        end: {
          line: 122,
          column: 9
        }
      },
      "63": {
        start: {
          line: 121,
          column: 10
        },
        end: {
          line: 121,
          column: 73
        }
      },
      "64": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 123,
          column: 20
        }
      },
      "65": {
        start: {
          line: 126,
          column: 25
        },
        end: {
          line: 126,
          column: 46
        }
      },
      "66": {
        start: {
          line: 127,
          column: 6
        },
        end: {
          line: 127,
          column: 28
        }
      },
      "67": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 24
        }
      },
      "68": {
        start: {
          line: 130,
          column: 6
        },
        end: {
          line: 130,
          column: 66
        }
      },
      "69": {
        start: {
          line: 131,
          column: 6
        },
        end: {
          line: 131,
          column: 18
        }
      },
      "70": {
        start: {
          line: 133,
          column: 6
        },
        end: {
          line: 133,
          column: 26
        }
      },
      "71": {
        start: {
          line: 137,
          column: 2
        },
        end: {
          line: 150,
          column: 4
        }
      },
      "72": {
        start: {
          line: 154,
          column: 25
        },
        end: {
          line: 154,
          column: 56
        }
      },
      "73": {
        start: {
          line: 154,
          column: 31
        },
        end: {
          line: 154,
          column: 56
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 18,
            column: 21
          },
          end: {
            line: 18,
            column: 22
          }
        },
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 37
          }
        },
        line: 18
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 19,
            column: 17
          },
          end: {
            line: 19,
            column: 18
          }
        },
        loc: {
          start: {
            line: 19,
            column: 29
          },
          end: {
            line: 19,
            column: 33
          }
        },
        line: 19
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 20,
            column: 20
          },
          end: {
            line: 20,
            column: 21
          }
        },
        loc: {
          start: {
            line: 20,
            column: 32
          },
          end: {
            line: 20,
            column: 36
          }
        },
        line: 20
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 25
          }
        },
        loc: {
          start: {
            line: 21,
            column: 36
          },
          end: {
            line: 21,
            column: 41
          }
        },
        line: 21
      },
      "4": {
        name: "ClientProvider",
        decl: {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 30
          }
        },
        loc: {
          start: {
            line: 24,
            column: 70
          },
          end: {
            line: 151,
            column: 1
          }
        },
        line: 24
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 30,
            column: 40
          },
          end: {
            line: 30,
            column: 41
          }
        },
        loc: {
          start: {
            line: 30,
            column: 90
          },
          end: {
            line: 53,
            column: 3
          }
        },
        line: 30
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 56,
            column: 36
          },
          end: {
            line: 56,
            column: 37
          }
        },
        loc: {
          start: {
            line: 56,
            column: 82
          },
          end: {
            line: 79,
            column: 3
          }
        },
        line: 56
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 82,
            column: 43
          },
          end: {
            line: 82,
            column: 44
          }
        },
        loc: {
          start: {
            line: 82,
            column: 106
          },
          end: {
            line: 109,
            column: 3
          }
        },
        line: 82
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 112,
            column: 39
          },
          end: {
            line: 112,
            column: 40
          }
        },
        loc: {
          start: {
            line: 112,
            column: 88
          },
          end: {
            line: 135,
            column: 3
          }
        },
        line: 112
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 154,
            column: 25
          },
          end: {
            line: 154,
            column: 26
          }
        },
        loc: {
          start: {
            line: 154,
            column: 31
          },
          end: {
            line: 154,
            column: 56
          }
        },
        line: 154
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 31,
            column: 4
          },
          end: {
            line: 31,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 4
          },
          end: {
            line: 31,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "1": {
        loc: {
          start: {
            line: 37,
            column: 6
          },
          end: {
            line: 42,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 6
          },
          end: {
            line: 42,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "2": {
        loc: {
          start: {
            line: 38,
            column: 8
          },
          end: {
            line: 40,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 8
          },
          end: {
            line: 40,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "3": {
        loc: {
          start: {
            line: 57,
            column: 4
          },
          end: {
            line: 57,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 57,
            column: 4
          },
          end: {
            line: 57,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 57
      },
      "4": {
        loc: {
          start: {
            line: 63,
            column: 6
          },
          end: {
            line: 68,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 63,
            column: 6
          },
          end: {
            line: 68,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 63
      },
      "5": {
        loc: {
          start: {
            line: 64,
            column: 8
          },
          end: {
            line: 66,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 64,
            column: 8
          },
          end: {
            line: 66,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 64
      },
      "6": {
        loc: {
          start: {
            line: 83,
            column: 4
          },
          end: {
            line: 83,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 83,
            column: 4
          },
          end: {
            line: 83,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 83
      },
      "7": {
        loc: {
          start: {
            line: 95,
            column: 6
          },
          end: {
            line: 98,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 95,
            column: 6
          },
          end: {
            line: 98,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 95
      },
      "8": {
        loc: {
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 113,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 113,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 113
      },
      "9": {
        loc: {
          start: {
            line: 119,
            column: 6
          },
          end: {
            line: 124,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 119,
            column: 6
          },
          end: {
            line: 124,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 119
      },
      "10": {
        loc: {
          start: {
            line: 120,
            column: 8
          },
          end: {
            line: 122,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 120,
            column: 8
          },
          end: {
            line: 122,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 120
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5c94e99c8d6083b7fd08cfe31cc116892dd52b50"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1zkraysw0w = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1zkraysw0w();
import { createContext, useState, useContext, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
// Create context with default values
const ClientContext =
/* istanbul ignore next */
(cov_1zkraysw0w().s[0]++, /*#__PURE__*/createContext({
  client: null,
  isLoading: false,
  getClientByDomain: async () => {
    /* istanbul ignore next */
    cov_1zkraysw0w().f[0]++;
    cov_1zkraysw0w().s[1]++;
    return null;
  },
  getClientById: async () => {
    /* istanbul ignore next */
    cov_1zkraysw0w().f[1]++;
    cov_1zkraysw0w().s[2]++;
    return null;
  },
  getClientByEmail: async () => {
    /* istanbul ignore next */
    cov_1zkraysw0w().f[2]++;
    cov_1zkraysw0w().s[3]++;
    return null;
  },
  updateClientSettings: async () => {
    /* istanbul ignore next */
    cov_1zkraysw0w().f[3]++;
    cov_1zkraysw0w().s[4]++;
    return false;
  }
}));
export function ClientProvider({
  children
}) {
  /* istanbul ignore next */
  cov_1zkraysw0w().f[4]++;
  const [client, setClient] =
  /* istanbul ignore next */
  (cov_1zkraysw0w().s[5]++, useState(null));
  const [isLoading, setIsLoading] =
  /* istanbul ignore next */
  (cov_1zkraysw0w().s[6]++, useState(false));
  const {
    user
  } =
  /* istanbul ignore next */
  (cov_1zkraysw0w().s[7]++, useAuth());

  // Function to get client info from email domain
  const getClientByDomain =
  /* istanbul ignore next */
  (cov_1zkraysw0w().s[8]++, useCallback(async domain => {
    /* istanbul ignore next */
    cov_1zkraysw0w().f[5]++;
    cov_1zkraysw0w().s[9]++;
    if (!domain) {
      /* istanbul ignore next */
      cov_1zkraysw0w().b[0][0]++;
      cov_1zkraysw0w().s[10]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_1zkraysw0w().b[0][1]++;
    }
    cov_1zkraysw0w().s[11]++;
    try {
      /* istanbul ignore next */
      cov_1zkraysw0w().s[12]++;
      setIsLoading(true);
      const response =
      /* istanbul ignore next */
      (cov_1zkraysw0w().s[13]++, await fetch(`/api/clients/domain?domain=${encodeURIComponent(domain)}`));
      /* istanbul ignore next */
      cov_1zkraysw0w().s[14]++;
      if (!response.ok) {
        /* istanbul ignore next */
        cov_1zkraysw0w().b[1][0]++;
        cov_1zkraysw0w().s[15]++;
        if (response.status !== 404) {
          /* istanbul ignore next */
          cov_1zkraysw0w().b[2][0]++;
          cov_1zkraysw0w().s[16]++;
          console.error('Error fetching client:', await response.text());
        } else
        /* istanbul ignore next */
        {
          cov_1zkraysw0w().b[2][1]++;
        }
        cov_1zkraysw0w().s[17]++;
        return null;
      } else
      /* istanbul ignore next */
      {
        cov_1zkraysw0w().b[1][1]++;
      }
      const clientData =
      /* istanbul ignore next */
      (cov_1zkraysw0w().s[18]++, await response.json());
      /* istanbul ignore next */
      cov_1zkraysw0w().s[19]++;
      setClient(clientData);
      /* istanbul ignore next */
      cov_1zkraysw0w().s[20]++;
      return clientData;
    } catch (error) {
      /* istanbul ignore next */
      cov_1zkraysw0w().s[21]++;
      console.error('Error getting client info:', error);
      /* istanbul ignore next */
      cov_1zkraysw0w().s[22]++;
      return null;
    } finally {
      /* istanbul ignore next */
      cov_1zkraysw0w().s[23]++;
      setIsLoading(false);
    }
  }, []));

  // Function to get client by ID
  const getClientById =
  /* istanbul ignore next */
  (cov_1zkraysw0w().s[24]++, useCallback(async id => {
    /* istanbul ignore next */
    cov_1zkraysw0w().f[6]++;
    cov_1zkraysw0w().s[25]++;
    if (!id) {
      /* istanbul ignore next */
      cov_1zkraysw0w().b[3][0]++;
      cov_1zkraysw0w().s[26]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_1zkraysw0w().b[3][1]++;
    }
    cov_1zkraysw0w().s[27]++;
    try {
      /* istanbul ignore next */
      cov_1zkraysw0w().s[28]++;
      setIsLoading(true);
      const response =
      /* istanbul ignore next */
      (cov_1zkraysw0w().s[29]++, await fetch(`/api/clients/${encodeURIComponent(id)}`));
      /* istanbul ignore next */
      cov_1zkraysw0w().s[30]++;
      if (!response.ok) {
        /* istanbul ignore next */
        cov_1zkraysw0w().b[4][0]++;
        cov_1zkraysw0w().s[31]++;
        if (response.status !== 404) {
          /* istanbul ignore next */
          cov_1zkraysw0w().b[5][0]++;
          cov_1zkraysw0w().s[32]++;
          console.error('Error fetching client:', await response.text());
        } else
        /* istanbul ignore next */
        {
          cov_1zkraysw0w().b[5][1]++;
        }
        cov_1zkraysw0w().s[33]++;
        return null;
      } else
      /* istanbul ignore next */
      {
        cov_1zkraysw0w().b[4][1]++;
      }
      const clientData =
      /* istanbul ignore next */
      (cov_1zkraysw0w().s[34]++, await response.json());
      /* istanbul ignore next */
      cov_1zkraysw0w().s[35]++;
      setClient(clientData);
      /* istanbul ignore next */
      cov_1zkraysw0w().s[36]++;
      return clientData;
    } catch (error) {
      /* istanbul ignore next */
      cov_1zkraysw0w().s[37]++;
      console.error('Error getting client info:', error);
      /* istanbul ignore next */
      cov_1zkraysw0w().s[38]++;
      return null;
    } finally {
      /* istanbul ignore next */
      cov_1zkraysw0w().s[39]++;
      setIsLoading(false);
    }
  }, []));

  // Function to update client settings
  const updateClientSettings =
  /* istanbul ignore next */
  (cov_1zkraysw0w().s[40]++, useCallback(async settings => {
    /* istanbul ignore next */
    cov_1zkraysw0w().f[7]++;
    cov_1zkraysw0w().s[41]++;
    if (!client?.id) {
      /* istanbul ignore next */
      cov_1zkraysw0w().b[6][0]++;
      cov_1zkraysw0w().s[42]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_1zkraysw0w().b[6][1]++;
    }
    cov_1zkraysw0w().s[43]++;
    try {
      /* istanbul ignore next */
      cov_1zkraysw0w().s[44]++;
      setIsLoading(true);
      const response =
      /* istanbul ignore next */
      (cov_1zkraysw0w().s[45]++, await fetch(`/api/clients/${encodeURIComponent(client.id)}/settings`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
      }));
      /* istanbul ignore next */
      cov_1zkraysw0w().s[46]++;
      if (!response.ok) {
        /* istanbul ignore next */
        cov_1zkraysw0w().b[7][0]++;
        cov_1zkraysw0w().s[47]++;
        console.error('Error updating client settings:', await response.text());
        /* istanbul ignore next */
        cov_1zkraysw0w().s[48]++;
        return false;
      } else
      /* istanbul ignore next */
      {
        cov_1zkraysw0w().b[7][1]++;
      }
      const updatedClient =
      /* istanbul ignore next */
      (cov_1zkraysw0w().s[49]++, await response.json());
      /* istanbul ignore next */
      cov_1zkraysw0w().s[50]++;
      setClient(updatedClient);
      /* istanbul ignore next */
      cov_1zkraysw0w().s[51]++;
      return true;
    } catch (error) {
      /* istanbul ignore next */
      cov_1zkraysw0w().s[52]++;
      console.error('Error updating client settings:', error);
      /* istanbul ignore next */
      cov_1zkraysw0w().s[53]++;
      return false;
    } finally {
      /* istanbul ignore next */
      cov_1zkraysw0w().s[54]++;
      setIsLoading(false);
    }
  }, [client?.id]));

  // Function to get client by email
  const getClientByEmail =
  /* istanbul ignore next */
  (cov_1zkraysw0w().s[55]++, useCallback(async email => {
    /* istanbul ignore next */
    cov_1zkraysw0w().f[8]++;
    cov_1zkraysw0w().s[56]++;
    if (!email) {
      /* istanbul ignore next */
      cov_1zkraysw0w().b[8][0]++;
      cov_1zkraysw0w().s[57]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_1zkraysw0w().b[8][1]++;
    }
    cov_1zkraysw0w().s[58]++;
    try {
      /* istanbul ignore next */
      cov_1zkraysw0w().s[59]++;
      setIsLoading(true);
      const response =
      /* istanbul ignore next */
      (cov_1zkraysw0w().s[60]++, await fetch(`/api/clients/domain?email=${encodeURIComponent(email)}`));
      /* istanbul ignore next */
      cov_1zkraysw0w().s[61]++;
      if (!response.ok) {
        /* istanbul ignore next */
        cov_1zkraysw0w().b[9][0]++;
        cov_1zkraysw0w().s[62]++;
        if (response.status !== 404) {
          /* istanbul ignore next */
          cov_1zkraysw0w().b[10][0]++;
          cov_1zkraysw0w().s[63]++;
          console.error('Error fetching client:', await response.text());
        } else
        /* istanbul ignore next */
        {
          cov_1zkraysw0w().b[10][1]++;
        }
        cov_1zkraysw0w().s[64]++;
        return null;
      } else
      /* istanbul ignore next */
      {
        cov_1zkraysw0w().b[9][1]++;
      }
      const clientData =
      /* istanbul ignore next */
      (cov_1zkraysw0w().s[65]++, await response.json());
      /* istanbul ignore next */
      cov_1zkraysw0w().s[66]++;
      setClient(clientData);
      /* istanbul ignore next */
      cov_1zkraysw0w().s[67]++;
      return clientData;
    } catch (error) {
      /* istanbul ignore next */
      cov_1zkraysw0w().s[68]++;
      console.error('Error getting client info by email:', error);
      /* istanbul ignore next */
      cov_1zkraysw0w().s[69]++;
      return null;
    } finally {
      /* istanbul ignore next */
      cov_1zkraysw0w().s[70]++;
      setIsLoading(false);
    }
  }, []));
  /* istanbul ignore next */
  cov_1zkraysw0w().s[71]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  ClientContext.Provider,
  /* istanbul ignore next */
  {
    value: {
      client,
      isLoading,
      getClientByDomain,
      getClientById,
      getClientByEmail,
      updateClientSettings
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 138,
      columnNumber: 5
    }
  }, children);
}

// Custom hook for using client context
/* istanbul ignore next */
cov_1zkraysw0w().s[72]++;
export const useClient = () => {
  /* istanbul ignore next */
  cov_1zkraysw0w().f[9]++;
  cov_1zkraysw0w().s[73]++;
  return useContext(ClientContext);
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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