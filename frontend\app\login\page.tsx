'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { getLoginUrl } from '@/lib/auth'
import { fetchAuthSession } from 'aws-amplify/auth'

export default function LoginPage() {
  const router = useRouter()

  useEffect(() => {
    async function checkAuthAndRedirect() {
      try {
        // Clear any existing invalid cookies first
        document.cookie = 'idToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'

        // Try to get the session from Amplify
        try {
          const session = await fetchAuthSession()
          if (session?.tokens?.idToken) {
            console.log('Valid Amplify session found, redirecting to dashboard')
            router.push('/dashboard')
            return
          }
        } catch (error) {
          console.log('No valid Amplify session found')
        }

        // No valid session, redirect to Cognito login
        console.log('Redirecting to Cognito managed login')
        window.location.href = getLoginUrl()

      } catch (error) {
        console.error('Error in login page:', error)
        // On error, redirect to Cognito login
        window.location.href = getLoginUrl()
      }
    }

    checkAuthAndRedirect()
  }, [router])

  // Show loading spinner while redirecting
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h2 className="text-lg font-medium text-gray-900 mb-2">
          Redirecting to Login...
        </h2>
        <p className="text-sm text-gray-600">
          Please wait while we redirect you to the secure login page.
        </p>
        <div className="mt-4 text-xs text-gray-500">
          If you're not redirected automatically, please refresh the page.
        </div>
      </div>
    </div>
  )
}



