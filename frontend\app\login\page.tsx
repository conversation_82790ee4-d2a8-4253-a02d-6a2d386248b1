'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { getLoginUrl } from '@/lib/auth'
import { fetchAuthSession } from 'aws-amplify/auth'

export default function LoginPage() {
  const router = useRouter()

  useEffect(() => {
    async function checkAuthAndRedirect() {
      try {
        // Check if we have an idToken cookie
        const hasCookie = document.cookie.includes('idToken=')

        if (hasCookie) {
          // If we have a cookie, redirect to dashboard
          console.log('Auth cookie found, redirecting to dashboard')
          router.push('/dashboard')
        } else {
          // Try to get the session from Amplify
          try {
            const session = await fetchAuthSession()
            if (session?.tokens?.idToken) {
              console.log('Amplify session found, redirecting to dashboard')
              router.push('/dashboard')
              return
            }
          } catch (error) {
            console.log('No Amplify session found')
          }

          // No cookie or Amplify session, redirect to Cognito login
          console.log('No auth found, redirecting to Cognito login')
          window.location.href = getLoginUrl()
        }
      } catch (error) {
        console.error('Error in login page:', error)
        // On error, redirect to Cognito login
        window.location.href = getLoginUrl()
      }
    }

    checkAuthAndRedirect()
  }, [router])

  // Show loading spinner while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin"></div>
    </div>
  )
}



