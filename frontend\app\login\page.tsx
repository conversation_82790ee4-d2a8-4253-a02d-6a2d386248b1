'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { getLoginUrl } from '@/lib/auth'
import { fetchAuthSession } from 'aws-amplify/auth'

export default function LoginPage() {
  const router = useRouter()

  useEffect(() => {
    async function checkAuthAndRedirect() {
      try {
        // Clear any existing invalid cookies first
        document.cookie = 'idToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'

        // Try to get the session from Amplify
        try {
          const session = await fetchAuthSession()
          if (session?.tokens?.idToken) {
            console.log('Valid Amplify session found, redirecting to dashboard')
            router.push('/dashboard')
            return
          }
        } catch (error) {
          console.log('No valid Amplify session found')
        }

        // No valid session, redirect to Cognito login
        console.log('Redirecting to Cognito managed login')
        window.location.href = getLoginUrl()
      }
      } catch (error) {
        console.error('Error in login page:', error)
        // On error, redirect to Cognito login
        window.location.href = getLoginUrl()
      }
    }

    checkAuthAndRedirect()
  }, [router])

  // Show loading spinner while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin"></div>
    </div>
  )
}



