/**
 * Centralized Configuration Management
 * 
 * This module provides a secure, centralized way to manage all application configuration.
 * It validates environment variables and provides type-safe access to configuration values.
 */

import { z } from 'zod';

// Environment validation schema
const envSchema = z.object({
  // AWS Configuration
  NEXT_PUBLIC_AWS_REGION: z.string().min(1, 'AWS region is required'),
  NEXT_PUBLIC_AWS_USER_POOLS_ID: z.string().min(1, 'User pool ID is required'),
  NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID: z.string().min(1, 'User pool client ID is required'),
  NEXT_PUBLIC_AWS_COGNITO_DOMAIN: z.string().min(1, 'Cognito domain is required'),
  
  // Redirect URLs
  NEXT_PUBLIC_REDIRECT_SIGN_IN: z.string().url('Invalid sign-in redirect URL'),
  NEXT_PUBLIC_REDIRECT_SIGN_OUT: z.string().url('Invalid sign-out redirect URL'),
  
  // Database Configuration (server-side only)
  DB_USER: z.string().optional(),
  DB_PASSWORD: z.string().optional(),
  DB_HOST: z.string().optional(),
  DB_NAME: z.string().optional(),
  DATABASE_URL: z.string().optional(),
  DATABASE_SSL: z.string().optional(),
  
  // Environment
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
});

// Validate environment variables
function validateEnv() {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    console.error('❌ Invalid environment configuration:', error);

    // If it's a Zod error, show detailed validation issues
    if (error instanceof z.ZodError) {
      console.error('Validation errors:');
      error.issues.forEach((issue) => {
        console.error(`  - ${issue.path.join('.')}: ${issue.message}`);
      });
    }

    throw new Error('Environment validation failed. Please check your .env files.');
  }
}

// Lazy validation - only validate when needed
let _env: z.infer<typeof envSchema> | null = null;

function getEnv() {
  if (_env === null) {
    _env = validateEnv();
  }
  return _env;
}

// Public configuration (safe to expose to client) - using getters for lazy evaluation
export const publicConfig = {
  get aws() {
    const env = getEnv();
    return {
      region: env.NEXT_PUBLIC_AWS_REGION,
      userPoolId: env.NEXT_PUBLIC_AWS_USER_POOLS_ID,
      userPoolClientId: env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID,
      cognitoDomain: env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN,
    };
  },
  get auth() {
    const env = getEnv();
    return {
      redirectSignIn: env.NEXT_PUBLIC_REDIRECT_SIGN_IN,
      redirectSignOut: env.NEXT_PUBLIC_REDIRECT_SIGN_OUT,
    };
  },
  get app() {
    const env = getEnv();
    return {
      environment: env.NODE_ENV,
      isDevelopment: env.NODE_ENV === 'development',
      isProduction: env.NODE_ENV === 'production',
    };
  },
} as const;

// Server-side configuration (never expose to client) - using getters for lazy evaluation
export const serverConfig = {
  get database() {
    const env = getEnv();
    return {
      user: env.DB_USER,
      password: env.DB_PASSWORD,
      host: env.DB_HOST,
      name: env.DB_NAME,
      url: env.DATABASE_URL,
      ssl: env.DATABASE_SSL === 'true',
    };
  },
} as const;

// Type exports for better TypeScript support
export type PublicConfig = typeof publicConfig;
export type ServerConfig = typeof serverConfig;

// Utility functions
export const getAuthConfig = () => publicConfig.auth;
export const getAwsConfig = () => publicConfig.aws;
export const getDatabaseConfig = () => {
  if (typeof window !== 'undefined') {
    throw new Error('Database configuration is not available on the client side');
  }
  return serverConfig.database;
};

// Configuration validation for runtime checks
export const validateConfig = () => {
  const requiredPublicVars = [
    'NEXT_PUBLIC_AWS_REGION',
    'NEXT_PUBLIC_AWS_USER_POOLS_ID',
    'NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID',
    'NEXT_PUBLIC_AWS_COGNITO_DOMAIN',
  ];

  const missing = requiredPublicVars.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  return true;
};

// Export environment for backward compatibility (to be removed)
export const env = getEnv;
