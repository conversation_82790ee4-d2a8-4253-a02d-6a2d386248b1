/**
 * Admin API endpoint to test product matching algorithm
 */

import { NextRequest } from 'next/server';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  ApiErrorCode, 
  HttpStatus,
  withErrorHandling 
} from '@/lib/api-response';
import { ProductMatcher } from '@/lib/sync/product-matcher';

export const GET = withErrorHandling(async (request: NextRequest) => {
  try {
    console.log('Testing product matching algorithm...');
    
    // Test the matching algorithm
    const matchResult = await ProductMatcher.testMatching();
    
    console.log('✅ Product matching test completed!');
    console.log('Match result:', JSON.stringify(matchResult, null, 2));
    
    return createSuccessResponse(
      matchResult,
      'Product matching algorithm tested successfully'
    );
    
  } catch (error) {
    console.error('❌ Error testing product matching:', error);
    return createErrorResponse(
      `Failed to test product matching: ${error}`,
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});
