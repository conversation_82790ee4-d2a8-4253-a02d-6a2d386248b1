4d014525657558d18c92e7fe8e1334e5
/* istanbul ignore next */
function cov_1xwmwh2tn6() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\db-config.ts";
  var hash = "8d44f18abe490e088186858f1fb7bce80834bf10";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\db-config.ts",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 24
        },
        end: {
          line: 6,
          column: 28
        }
      },
      "1": {
        start: {
          line: 9,
          column: 22
        },
        end: {
          line: 35,
          column: 1
        }
      },
      "2": {
        start: {
          line: 10,
          column: 33
        },
        end: {
          line: 32,
          column: 3
        }
      },
      "3": {
        start: {
          line: 34,
          column: 2
        },
        end: {
          line: 34,
          column: 20
        }
      },
      "4": {
        start: {
          line: 38,
          column: 2
        },
        end: {
          line: 38,
          column: 24
        }
      },
      "5": {
        start: {
          line: 38,
          column: 12
        },
        end: {
          line: 38,
          column: 24
        }
      },
      "6": {
        start: {
          line: 40,
          column: 19
        },
        end: {
          line: 40,
          column: 38
        }
      },
      "7": {
        start: {
          line: 43,
          column: 2
        },
        end: {
          line: 68,
          column: 3
        }
      },
      "8": {
        start: {
          line: 44,
          column: 4
        },
        end: {
          line: 49,
          column: 7
        }
      },
      "9": {
        start: {
          line: 51,
          column: 23
        },
        end: {
          line: 51,
          column: 46
        }
      },
      "10": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 52,
          column: 32
        }
      },
      "11": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 57,
          column: 7
        }
      },
      "12": {
        start: {
          line: 56,
          column: 6
        },
        end: {
          line: 56,
          column: 60
        }
      },
      "13": {
        start: {
          line: 59,
          column: 4
        },
        end: {
          line: 61,
          column: 7
        }
      },
      "14": {
        start: {
          line: 60,
          column: 6
        },
        end: {
          line: 60,
          column: 54
        }
      },
      "15": {
        start: {
          line: 63,
          column: 4
        },
        end: {
          line: 65,
          column: 7
        }
      },
      "16": {
        start: {
          line: 64,
          column: 6
        },
        end: {
          line: 64,
          column: 46
        }
      },
      "17": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 67,
          column: 16
        }
      },
      "18": {
        start: {
          line: 71,
          column: 2
        },
        end: {
          line: 105,
          column: 3
        }
      },
      "19": {
        start: {
          line: 72,
          column: 19
        },
        end: {
          line: 72,
          column: 130
        }
      },
      "20": {
        start: {
          line: 73,
          column: 19
        },
        end: {
          line: 73,
          column: 67
        }
      },
      "21": {
        start: {
          line: 74,
          column: 19
        },
        end: {
          line: 74,
          column: 48
        }
      },
      "22": {
        start: {
          line: 77,
          column: 19
        },
        end: {
          line: 82,
          column: 6
        }
      },
      "23": {
        start: {
          line: 85,
          column: 18
        },
        end: {
          line: 85,
          column: 45
        }
      },
      "24": {
        start: {
          line: 87,
          column: 4
        },
        end: {
          line: 89,
          column: 5
        }
      },
      "25": {
        start: {
          line: 88,
          column: 6
        },
        end: {
          line: 88,
          column: 57
        }
      },
      "26": {
        start: {
          line: 92,
          column: 4
        },
        end: {
          line: 99,
          column: 7
        }
      },
      "27": {
        start: {
          line: 101,
          column: 4
        },
        end: {
          line: 101,
          column: 16
        }
      },
      "28": {
        start: {
          line: 103,
          column: 4
        },
        end: {
          line: 103,
          column: 68
        }
      },
      "29": {
        start: {
          line: 104,
          column: 4
        },
        end: {
          line: 104,
          column: 16
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 9,
            column: 22
          },
          end: {
            line: 9,
            column: 23
          }
        },
        loc: {
          start: {
            line: 9,
            column: 86
          },
          end: {
            line: 35,
            column: 1
          }
        },
        line: 9
      },
      "1": {
        name: "getDbPool",
        decl: {
          start: {
            line: 37,
            column: 22
          },
          end: {
            line: 37,
            column: 31
          }
        },
        loc: {
          start: {
            line: 37,
            column: 49
          },
          end: {
            line: 106,
            column: 1
          }
        },
        line: 37
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 55,
            column: 21
          },
          end: {
            line: 55,
            column: 22
          }
        },
        loc: {
          start: {
            line: 55,
            column: 30
          },
          end: {
            line: 57,
            column: 5
          }
        },
        line: 55
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 59,
            column: 23
          },
          end: {
            line: 59,
            column: 24
          }
        },
        loc: {
          start: {
            line: 59,
            column: 29
          },
          end: {
            line: 61,
            column: 5
          }
        },
        line: 59
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 63,
            column: 22
          },
          end: {
            line: 63,
            column: 23
          }
        },
        loc: {
          start: {
            line: 63,
            column: 28
          },
          end: {
            line: 65,
            column: 5
          }
        },
        line: 63
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 13,
            column: 10
          },
          end: {
            line: 13,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 10
          },
          end: {
            line: 13,
            column: 23
          }
        }, {
          start: {
            line: 13,
            column: 27
          },
          end: {
            line: 13,
            column: 38
          }
        }],
        line: 13
      },
      "1": {
        loc: {
          start: {
            line: 16,
            column: 9
          },
          end: {
            line: 16,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 16,
            column: 24
          },
          end: {
            line: 16,
            column: 53
          }
        }, {
          start: {
            line: 16,
            column: 56
          },
          end: {
            line: 16,
            column: 61
          }
        }],
        line: 16
      },
      "2": {
        loc: {
          start: {
            line: 38,
            column: 2
          },
          end: {
            line: 38,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 2
          },
          end: {
            line: 38,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "3": {
        loc: {
          start: {
            line: 43,
            column: 2
          },
          end: {
            line: 68,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 2
          },
          end: {
            line: 68,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 43
      },
      "4": {
        loc: {
          start: {
            line: 46,
            column: 12
          },
          end: {
            line: 46,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 46,
            column: 12
          },
          end: {
            line: 46,
            column: 25
          }
        }, {
          start: {
            line: 46,
            column: 29
          },
          end: {
            line: 46,
            column: 40
          }
        }],
        line: 46
      },
      "5": {
        loc: {
          start: {
            line: 72,
            column: 19
          },
          end: {
            line: 72,
            column: 130
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 72,
            column: 19
          },
          end: {
            line: 72,
            column: 32
          }
        }, {
          start: {
            line: 72,
            column: 36
          },
          end: {
            line: 72,
            column: 48
          }
        }, {
          start: {
            line: 72,
            column: 52
          },
          end: {
            line: 72,
            column: 130
          }
        }],
        line: 72
      },
      "6": {
        loc: {
          start: {
            line: 73,
            column: 19
          },
          end: {
            line: 73,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 73,
            column: 19
          },
          end: {
            line: 73,
            column: 42
          }
        }, {
          start: {
            line: 73,
            column: 46
          },
          end: {
            line: 73,
            column: 67
          }
        }],
        line: 73
      },
      "7": {
        loc: {
          start: {
            line: 74,
            column: 19
          },
          end: {
            line: 74,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 74,
            column: 19
          },
          end: {
            line: 74,
            column: 32
          }
        }, {
          start: {
            line: 74,
            column: 36
          },
          end: {
            line: 74,
            column: 48
          }
        }],
        line: 74
      },
      "8": {
        loc: {
          start: {
            line: 87,
            column: 4
          },
          end: {
            line: 89,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 87,
            column: 4
          },
          end: {
            line: 89,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 87
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "8d44f18abe490e088186858f1fb7bce80834bf10"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1xwmwh2tn6 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1xwmwh2tn6();
import { Signer } from "@aws-sdk/rds-signer";
import { Pool } from 'pg';
import { getDatabaseConfig, publicConfig } from './config';

// Connection pool instance
let pool =
/* istanbul ignore next */
(cov_1xwmwh2tn6().s[0]++, null);

// Pool configuration with optimized settings
/* istanbul ignore next */
cov_1xwmwh2tn6().s[1]++;
const getPoolConfig = dbConfig => {
  /* istanbul ignore next */
  cov_1xwmwh2tn6().f[0]++;
  const baseConfig =
  /* istanbul ignore next */
  (cov_1xwmwh2tn6().s[2]++, {
    user: dbConfig.user,
    password: dbConfig.password,
    host:
    /* istanbul ignore next */
    (cov_1xwmwh2tn6().b[0][0]++, dbConfig.host) ||
    /* istanbul ignore next */
    (cov_1xwmwh2tn6().b[0][1]++, '127.0.0.1'),
    port: 5432,
    database: dbConfig.name,
    ssl: dbConfig.ssl ?
    /* istanbul ignore next */
    (cov_1xwmwh2tn6().b[1][0]++, {
      rejectUnauthorized: false
    }) :
    /* istanbul ignore next */
    (cov_1xwmwh2tn6().b[1][1]++, false),
    // Connection pool optimization
    max: 20,
    // Maximum number of clients in the pool
    min: 2,
    // Minimum number of clients in the pool
    idleTimeoutMillis: 30000,
    // Close idle clients after 30 seconds
    connectionTimeoutMillis: 10000,
    // Return error after 10 seconds if connection could not be established
    maxUses: 7500,
    // Close (and replace) a connection after it has been used 7500 times

    // Query timeout
    query_timeout: 30000,
    // 30 seconds
    statement_timeout: 30000,
    // 30 seconds

    // Keep alive settings
    keepAlive: true,
    keepAliveInitialDelayMillis: 10000
  });
  /* istanbul ignore next */
  cov_1xwmwh2tn6().s[3]++;
  return baseConfig;
};
export async function getDbPool() {
  /* istanbul ignore next */
  cov_1xwmwh2tn6().f[1]++;
  cov_1xwmwh2tn6().s[4]++;
  if (pool) {
    /* istanbul ignore next */
    cov_1xwmwh2tn6().b[2][0]++;
    cov_1xwmwh2tn6().s[5]++;
    return pool;
  } else
  /* istanbul ignore next */
  {
    cov_1xwmwh2tn6().b[2][1]++;
  }
  const dbConfig =
  /* istanbul ignore next */
  (cov_1xwmwh2tn6().s[6]++, getDatabaseConfig());

  // For local development, use environment variables
  /* istanbul ignore next */
  cov_1xwmwh2tn6().s[7]++;
  if (!publicConfig.app.isProduction) {
    /* istanbul ignore next */
    cov_1xwmwh2tn6().b[3][0]++;
    cov_1xwmwh2tn6().s[8]++;
    console.log('Database config:', {
      user: dbConfig.user,
      host:
      /* istanbul ignore next */
      (cov_1xwmwh2tn6().b[4][0]++, dbConfig.host) ||
      /* istanbul ignore next */
      (cov_1xwmwh2tn6().b[4][1]++, '127.0.0.1'),
      database: dbConfig.name,
      ssl: dbConfig.ssl
    });
    const poolConfig =
    /* istanbul ignore next */
    (cov_1xwmwh2tn6().s[9]++, getPoolConfig(dbConfig));
    /* istanbul ignore next */
    cov_1xwmwh2tn6().s[10]++;
    pool = new Pool(poolConfig);

    // Add error handling for the pool
    /* istanbul ignore next */
    cov_1xwmwh2tn6().s[11]++;
    pool.on('error', err => {
      /* istanbul ignore next */
      cov_1xwmwh2tn6().f[2]++;
      cov_1xwmwh2tn6().s[12]++;
      console.error('Unexpected error on idle client', err);
    });
    /* istanbul ignore next */
    cov_1xwmwh2tn6().s[13]++;
    pool.on('connect', () => {
      /* istanbul ignore next */
      cov_1xwmwh2tn6().f[3]++;
      cov_1xwmwh2tn6().s[14]++;
      console.log('New client connected to database');
    });
    /* istanbul ignore next */
    cov_1xwmwh2tn6().s[15]++;
    pool.on('remove', () => {
      /* istanbul ignore next */
      cov_1xwmwh2tn6().f[4]++;
      cov_1xwmwh2tn6().s[16]++;
      console.log('Client removed from pool');
    });
    /* istanbul ignore next */
    cov_1xwmwh2tn6().s[17]++;
    return pool;
  } else
  /* istanbul ignore next */
  {
    cov_1xwmwh2tn6().b[3][1]++;
  }

  // For production, use IAM authentication
  cov_1xwmwh2tn6().s[18]++;
  try {
    const dbHost =
    /* istanbul ignore next */
    (cov_1xwmwh2tn6().s[19]++,
    /* istanbul ignore next */
    (cov_1xwmwh2tn6().b[5][0]++, dbConfig.host) ||
    /* istanbul ignore next */
    (cov_1xwmwh2tn6().b[5][1]++, dbConfig.url) ||
    /* istanbul ignore next */
    (cov_1xwmwh2tn6().b[5][2]++, 'renewtrack-prod-ca-central-1-rds.cpy6ukqgy9s3.ca-central-1.rds.amazonaws.com'));
    const dbUser =
    /* istanbul ignore next */
    (cov_1xwmwh2tn6().s[20]++,
    /* istanbul ignore next */
    (cov_1xwmwh2tn6().b[6][0]++, process.env.DB_IAM_USER) ||
    /* istanbul ignore next */
    (cov_1xwmwh2tn6().b[6][1]++, 'renewtrack_iam_user'));
    const dbName =
    /* istanbul ignore next */
    (cov_1xwmwh2tn6().s[21]++,
    /* istanbul ignore next */
    (cov_1xwmwh2tn6().b[7][0]++, dbConfig.name) ||
    /* istanbul ignore next */
    (cov_1xwmwh2tn6().b[7][1]++, 'renewtrack'));

    // Create RDS signer for IAM authentication
    const signer =
    /* istanbul ignore next */
    (cov_1xwmwh2tn6().s[22]++, new Signer({
      hostname: dbHost,
      port: 5432,
      username: dbUser,
      region: publicConfig.aws.region
    }));

    // Get temporary auth token
    const token =
    /* istanbul ignore next */
    (cov_1xwmwh2tn6().s[23]++, await signer.getAuthToken());
    /* istanbul ignore next */
    cov_1xwmwh2tn6().s[24]++;
    if (!token) {
      /* istanbul ignore next */
      cov_1xwmwh2tn6().b[8][0]++;
      cov_1xwmwh2tn6().s[25]++;
      throw new Error('Failed to obtain RDS auth token');
    } else
    /* istanbul ignore next */
    {
      cov_1xwmwh2tn6().b[8][1]++;
    }

    // Create connection pool with IAM token as password
    cov_1xwmwh2tn6().s[26]++;
    pool = new Pool({
      user: dbUser,
      password: token,
      host: dbHost,
      port: 5432,
      database: dbName,
      ssl: {
        rejectUnauthorized: false
      }
    });
    /* istanbul ignore next */
    cov_1xwmwh2tn6().s[27]++;
    return pool;
  } catch (error) {
    /* istanbul ignore next */
    cov_1xwmwh2tn6().s[28]++;
    console.error('Error establishing database connection:', error);
    /* istanbul ignore next */
    cov_1xwmwh2tn6().s[29]++;
    throw error;
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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