4e964141ff5785fcefa550b40340779f
/* istanbul ignore next */
function cov_1jc8q2ba66() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\direct-auth-config.ts";
  var hash = "279bfc50fed83b143f0d55d9885ed8030a5931b4";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\direct-auth-config.ts",
    statementMap: {
      "0": {
        start: {
          line: 5,
          column: 2
        },
        end: {
          line: 5,
          column: 39
        }
      },
      "1": {
        start: {
          line: 5,
          column: 32
        },
        end: {
          line: 5,
          column: 39
        }
      },
      "2": {
        start: {
          line: 8,
          column: 17
        },
        end: {
          line: 14,
          column: 3
        }
      },
      "3": {
        start: {
          line: 16,
          column: 2
        },
        end: {
          line: 39,
          column: 3
        }
      },
      "4": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 35,
          column: 7
        }
      },
      "5": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 36,
          column: 70
        }
      },
      "6": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 38,
          column: 64
        }
      }
    },
    fnMap: {
      "0": {
        name: "configureAmplifyDirectly",
        decl: {
          start: {
            line: 3,
            column: 16
          },
          end: {
            line: 3,
            column: 40
          }
        },
        loc: {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 40,
            column: 1
          }
        },
        line: 3
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 5,
            column: 2
          },
          end: {
            line: 5,
            column: 39
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 5,
            column: 2
          },
          end: {
            line: 5,
            column: 39
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 5
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "279bfc50fed83b143f0d55d9885ed8030a5931b4"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1jc8q2ba66 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1jc8q2ba66();
import { Amplify } from 'aws-amplify';
export function configureAmplifyDirectly() {
  /* istanbul ignore next */
  cov_1jc8q2ba66().f[0]++;
  cov_1jc8q2ba66().s[0]++;
  // If already configured, don't configure again
  if (Amplify.getConfig().Auth) {
    /* istanbul ignore next */
    cov_1jc8q2ba66().b[0][0]++;
    cov_1jc8q2ba66().s[1]++;
    return;
  } else
  /* istanbul ignore next */
  {
    cov_1jc8q2ba66().b[0][1]++;
  }

  // Hard-coded values for testing - replace with your actual values
  const config =
  /* istanbul ignore next */
  (cov_1jc8q2ba66().s[2]++, {
    userPoolId: 'ca-central-1_uwPuGUhLc',
    userPoolClientId: '6fc4ks4poom3mqk5icavr7np1k',
    domain: 'auth.renewtrack.com',
    redirectSignIn: 'http://localhost:3000/callback',
    redirectSignOut: 'http://localhost:3000/signout'
  });
  /* istanbul ignore next */
  cov_1jc8q2ba66().s[3]++;
  try {
    /* istanbul ignore next */
    cov_1jc8q2ba66().s[4]++;
    Amplify.configure({
      Auth: {
        Cognito: {
          userPoolId: config.userPoolId,
          userPoolClientId: config.userPoolClientId,
          loginWith: {
            oauth: {
              domain: config.domain,
              scopes: ["email", "profile", "openid", "aws.cognito.signin.user.admin"],
              redirectSignIn: [config.redirectSignIn],
              redirectSignOut: [config.redirectSignOut],
              responseType: "code"
            }
          }
        }
      }
    }, {
      ssr: true
    });
    /* istanbul ignore next */
    cov_1jc8q2ba66().s[5]++;
    console.log('Amplify configured directly with hard-coded values');
  } catch (error) {
    /* istanbul ignore next */
    cov_1jc8q2ba66().s[6]++;
    console.error('Error configuring Amplify directly:', error);
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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