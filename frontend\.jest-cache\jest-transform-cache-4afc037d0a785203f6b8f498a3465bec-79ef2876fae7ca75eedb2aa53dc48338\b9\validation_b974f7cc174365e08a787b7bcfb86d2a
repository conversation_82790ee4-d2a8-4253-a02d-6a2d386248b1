f7e781ed17f128a85975b646af4e2ced
/* istanbul ignore next */
function cov_a3c688ugh() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\validation.ts";
  var hash = "e6386b9f35de9215c68f5b89150b17a23439b4fa";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\validation.ts",
    statementMap: {
      "0": {
        start: {
          line: 13,
          column: 20
        },
        end: {
          line: 13,
          column: 60
        }
      },
      "1": {
        start: {
          line: 14,
          column: 19
        },
        end: {
          line: 14,
          column: 57
        }
      },
      "2": {
        start: {
          line: 15,
          column: 26
        },
        end: {
          line: 15,
          column: 81
        }
      },
      "3": {
        start: {
          line: 16,
          column: 29
        },
        end: {
          line: 16,
          column: 65
        }
      },
      "4": {
        start: {
          line: 19,
          column: 32
        },
        end: {
          line: 24,
          column: 2
        }
      },
      "5": {
        start: {
          line: 27,
          column: 34
        },
        end: {
          line: 32,
          column: 2
        }
      },
      "6": {
        start: {
          line: 34,
          column: 34
        },
        end: {
          line: 39,
          column: 2
        }
      },
      "7": {
        start: {
          line: 42,
          column: 32
        },
        end: {
          line: 48,
          column: 2
        }
      },
      "8": {
        start: {
          line: 50,
          column: 32
        },
        end: {
          line: 55,
          column: 2
        }
      },
      "9": {
        start: {
          line: 58,
          column: 37
        },
        end: {
          line: 66,
          column: 2
        }
      },
      "10": {
        start: {
          line: 69,
          column: 35
        },
        end: {
          line: 76,
          column: 2
        }
      },
      "11": {
        start: {
          line: 78,
          column: 35
        },
        end: {
          line: 85,
          column: 2
        }
      },
      "12": {
        start: {
          line: 88,
          column: 41
        },
        end: {
          line: 92,
          column: 2
        }
      },
      "13": {
        start: {
          line: 94,
          column: 35
        },
        end: {
          line: 100,
          column: 2
        }
      },
      "14": {
        start: {
          line: 103,
          column: 29
        },
        end: {
          line: 105,
          column: 2
        }
      },
      "15": {
        start: {
          line: 107,
          column: 35
        },
        end: {
          line: 109,
          column: 2
        }
      },
      "16": {
        start: {
          line: 116,
          column: 2
        },
        end: {
          line: 125,
          column: 3
        }
      },
      "17": {
        start: {
          line: 117,
          column: 17
        },
        end: {
          line: 117,
          column: 37
        }
      },
      "18": {
        start: {
          line: 118,
          column: 26
        },
        end: {
          line: 118,
          column: 44
        }
      },
      "19": {
        start: {
          line: 119,
          column: 4
        },
        end: {
          line: 119,
          column: 50
        }
      },
      "20": {
        start: {
          line: 121,
          column: 4
        },
        end: {
          line: 123,
          column: 5
        }
      },
      "21": {
        start: {
          line: 122,
          column: 6
        },
        end: {
          line: 122,
          column: 72
        }
      },
      "22": {
        start: {
          line: 124,
          column: 4
        },
        end: {
          line: 124,
          column: 16
        }
      },
      "23": {
        start: {
          line: 132,
          column: 2
        },
        end: {
          line: 157,
          column: 3
        }
      },
      "24": {
        start: {
          line: 134,
          column: 40
        },
        end: {
          line: 134,
          column: 42
        }
      },
      "25": {
        start: {
          line: 136,
          column: 4
        },
        end: {
          line: 148,
          column: 5
        }
      },
      "26": {
        start: {
          line: 138,
          column: 6
        },
        end: {
          line: 147,
          column: 7
        }
      },
      "27": {
        start: {
          line: 139,
          column: 8
        },
        end: {
          line: 139,
          column: 27
        }
      },
      "28": {
        start: {
          line: 140,
          column: 13
        },
        end: {
          line: 147,
          column: 7
        }
      },
      "29": {
        start: {
          line: 141,
          column: 8
        },
        end: {
          line: 141,
          column: 28
        }
      },
      "30": {
        start: {
          line: 142,
          column: 13
        },
        end: {
          line: 147,
          column: 7
        }
      },
      "31": {
        start: {
          line: 144,
          column: 8
        },
        end: {
          line: 144,
          column: 36
        }
      },
      "32": {
        start: {
          line: 146,
          column: 8
        },
        end: {
          line: 146,
          column: 28
        }
      },
      "33": {
        start: {
          line: 150,
          column: 26
        },
        end: {
          line: 150,
          column: 46
        }
      },
      "34": {
        start: {
          line: 151,
          column: 4
        },
        end: {
          line: 151,
          column: 50
        }
      },
      "35": {
        start: {
          line: 153,
          column: 4
        },
        end: {
          line: 155,
          column: 5
        }
      },
      "36": {
        start: {
          line: 154,
          column: 6
        },
        end: {
          line: 154,
          column: 72
        }
      },
      "37": {
        start: {
          line: 156,
          column: 4
        },
        end: {
          line: 156,
          column: 16
        }
      },
      "38": {
        start: {
          line: 164,
          column: 2
        },
        end: {
          line: 178,
          column: 3
        }
      },
      "39": {
        start: {
          line: 166,
          column: 53
        },
        end: {
          line: 166,
          column: 55
        }
      },
      "40": {
        start: {
          line: 167,
          column: 4
        },
        end: {
          line: 169,
          column: 5
        }
      },
      "41": {
        start: {
          line: 168,
          column: 6
        },
        end: {
          line: 168,
          column: 70
        }
      },
      "42": {
        start: {
          line: 171,
          column: 26
        },
        end: {
          line: 171,
          column: 56
        }
      },
      "43": {
        start: {
          line: 172,
          column: 4
        },
        end: {
          line: 172,
          column: 50
        }
      },
      "44": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 176,
          column: 5
        }
      },
      "45": {
        start: {
          line: 175,
          column: 6
        },
        end: {
          line: 175,
          column: 72
        }
      },
      "46": {
        start: {
          line: 177,
          column: 4
        },
        end: {
          line: 177,
          column: 16
        }
      },
      "47": {
        start: {
          line: 183,
          column: 2
        },
        end: {
          line: 183,
          column: 43
        }
      },
      "48": {
        start: {
          line: 187,
          column: 41
        },
        end: {
          line: 187,
          column: 43
        }
      },
      "49": {
        start: {
          line: 189,
          column: 2
        },
        end: {
          line: 197,
          column: 3
        }
      },
      "50": {
        start: {
          line: 190,
          column: 4
        },
        end: {
          line: 196,
          column: 5
        }
      },
      "51": {
        start: {
          line: 191,
          column: 6
        },
        end: {
          line: 191,
          column: 45
        }
      },
      "52": {
        start: {
          line: 192,
          column: 11
        },
        end: {
          line: 196,
          column: 5
        }
      },
      "53": {
        start: {
          line: 193,
          column: 6
        },
        end: {
          line: 193,
          column: 45
        }
      },
      "54": {
        start: {
          line: 195,
          column: 6
        },
        end: {
          line: 195,
          column: 29
        }
      },
      "55": {
        start: {
          line: 199,
          column: 2
        },
        end: {
          line: 199,
          column: 19
        }
      },
      "56": {
        start: {
          line: 203,
          column: 31
        },
        end: {
          line: 206,
          column: 2
        }
      },
      "57": {
        start: {
          line: 209,
          column: 32
        },
        end: {
          line: 213,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "validateRequestBody",
        decl: {
          start: {
            line: 112,
            column: 22
          },
          end: {
            line: 112,
            column: 41
          }
        },
        loc: {
          start: {
            line: 115,
            column: 80
          },
          end: {
            line: 126,
            column: 1
          }
        },
        line: 115
      },
      "1": {
        name: "validateQueryParams",
        decl: {
          start: {
            line: 128,
            column: 16
          },
          end: {
            line: 128,
            column: 35
          }
        },
        loc: {
          start: {
            line: 131,
            column: 71
          },
          end: {
            line: 158,
            column: 1
          }
        },
        line: 131
      },
      "2": {
        name: "validatePathParams",
        decl: {
          start: {
            line: 160,
            column: 16
          },
          end: {
            line: 160,
            column: 34
          }
        },
        loc: {
          start: {
            line: 163,
            column: 71
          },
          end: {
            line: 179,
            column: 1
          }
        },
        line: 163
      },
      "3": {
        name: "sanitizeString",
        decl: {
          start: {
            line: 182,
            column: 16
          },
          end: {
            line: 182,
            column: 30
          }
        },
        loc: {
          start: {
            line: 182,
            column: 54
          },
          end: {
            line: 184,
            column: 1
          }
        },
        line: 182
      },
      "4": {
        name: "sanitizeObject",
        decl: {
          start: {
            line: 186,
            column: 16
          },
          end: {
            line: 186,
            column: 30
          }
        },
        loc: {
          start: {
            line: 186,
            column: 78
          },
          end: {
            line: 200,
            column: 1
          }
        },
        line: 186
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 121,
            column: 4
          },
          end: {
            line: 123,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 121,
            column: 4
          },
          end: {
            line: 123,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 121
      },
      "1": {
        loc: {
          start: {
            line: 138,
            column: 6
          },
          end: {
            line: 147,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 138,
            column: 6
          },
          end: {
            line: 147,
            column: 7
          }
        }, {
          start: {
            line: 140,
            column: 13
          },
          end: {
            line: 147,
            column: 7
          }
        }],
        line: 138
      },
      "2": {
        loc: {
          start: {
            line: 140,
            column: 13
          },
          end: {
            line: 147,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 140,
            column: 13
          },
          end: {
            line: 147,
            column: 7
          }
        }, {
          start: {
            line: 142,
            column: 13
          },
          end: {
            line: 147,
            column: 7
          }
        }],
        line: 140
      },
      "3": {
        loc: {
          start: {
            line: 142,
            column: 13
          },
          end: {
            line: 147,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 142,
            column: 13
          },
          end: {
            line: 147,
            column: 7
          }
        }, {
          start: {
            line: 145,
            column: 13
          },
          end: {
            line: 147,
            column: 7
          }
        }],
        line: 142
      },
      "4": {
        loc: {
          start: {
            line: 142,
            column: 17
          },
          end: {
            line: 142,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 142,
            column: 17
          },
          end: {
            line: 142,
            column: 38
          }
        }, {
          start: {
            line: 142,
            column: 42
          },
          end: {
            line: 142,
            column: 54
          }
        }],
        line: 142
      },
      "5": {
        loc: {
          start: {
            line: 153,
            column: 4
          },
          end: {
            line: 155,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 153,
            column: 4
          },
          end: {
            line: 155,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 153
      },
      "6": {
        loc: {
          start: {
            line: 168,
            column: 30
          },
          end: {
            line: 168,
            column: 69
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 168,
            column: 53
          },
          end: {
            line: 168,
            column: 61
          }
        }, {
          start: {
            line: 168,
            column: 64
          },
          end: {
            line: 168,
            column: 69
          }
        }],
        line: 168
      },
      "7": {
        loc: {
          start: {
            line: 174,
            column: 4
          },
          end: {
            line: 176,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 174,
            column: 4
          },
          end: {
            line: 176,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 174
      },
      "8": {
        loc: {
          start: {
            line: 190,
            column: 4
          },
          end: {
            line: 196,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 190,
            column: 4
          },
          end: {
            line: 196,
            column: 5
          }
        }, {
          start: {
            line: 192,
            column: 11
          },
          end: {
            line: 196,
            column: 5
          }
        }],
        line: 190
      },
      "9": {
        loc: {
          start: {
            line: 192,
            column: 11
          },
          end: {
            line: 196,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 192,
            column: 11
          },
          end: {
            line: 196,
            column: 5
          }
        }, {
          start: {
            line: 194,
            column: 11
          },
          end: {
            line: 196,
            column: 5
          }
        }],
        line: 192
      },
      "10": {
        loc: {
          start: {
            line: 192,
            column: 15
          },
          end: {
            line: 192,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 192,
            column: 15
          },
          end: {
            line: 192,
            column: 40
          }
        }, {
          start: {
            line: 192,
            column: 44
          },
          end: {
            line: 192,
            column: 58
          }
        }, {
          start: {
            line: 192,
            column: 62
          },
          end: {
            line: 192,
            column: 83
          }
        }],
        line: 192
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e6386b9f35de9215c68f5b89150b17a23439b4fa"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_a3c688ugh = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_a3c688ugh();
/**
 * Input Validation Schemas and Utilities
 * 
 * This module provides comprehensive input validation using Zod schemas
 * for all API endpoints to ensure data integrity and security.
 */

import { z } from 'zod';
import { handleValidationError } from './api-response';

// Common validation patterns
const emailSchema =
/* istanbul ignore next */
(cov_a3c688ugh().s[0]++, z.string().email('Invalid email format'));
const uuidSchema =
/* istanbul ignore next */
(cov_a3c688ugh().s[1]++, z.string().uuid('Invalid UUID format'));
const positiveIntSchema =
/* istanbul ignore next */
(cov_a3c688ugh().s[2]++, z.number().int().positive('Must be a positive integer'));
const nonEmptyStringSchema =
/* istanbul ignore next */
(cov_a3c688ugh().s[3]++, z.string().min(1, 'Cannot be empty'));

// Pagination schema
export const paginationSchema =
/* istanbul ignore next */
(cov_a3c688ugh().s[4]++, z.object({
  page: z.number().int().min(1, 'Page must be at least 1').default(1),
  limit: z.number().int().min(1, 'Limit must be at least 1').max(100, 'Limit cannot exceed 100').default(20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
}));

// Client validation schemas
export const clientCreateSchema =
/* istanbul ignore next */
(cov_a3c688ugh().s[5]++, z.object({
  name: nonEmptyStringSchema.max(255, 'Name cannot exceed 255 characters'),
  domain: z.string().min(1, 'Domain is required').max(255, 'Domain cannot exceed 255 characters'),
  status: z.enum(['active', 'inactive', 'suspended']).default('active'),
  settings: z.record(z.any()).optional()
}));
export const clientUpdateSchema =
/* istanbul ignore next */
(cov_a3c688ugh().s[6]++, z.object({
  name: z.string().max(255, 'Name cannot exceed 255 characters').optional(),
  domain: z.string().max(255, 'Domain cannot exceed 255 characters').optional(),
  status: z.enum(['active', 'inactive', 'suspended']).optional(),
  settings: z.record(z.any()).optional()
}));

// User validation schemas
export const userCreateSchema =
/* istanbul ignore next */
(cov_a3c688ugh().s[7]++, z.object({
  email: emailSchema,
  name: z.string().max(255, 'Name cannot exceed 255 characters').optional(),
  given_name: z.string().max(255, 'Given name cannot exceed 255 characters').optional(),
  family_name: z.string().max(255, 'Family name cannot exceed 255 characters').optional(),
  roles: z.array(z.string()).default([])
}));
export const userUpdateSchema =
/* istanbul ignore next */
(cov_a3c688ugh().s[8]++, z.object({
  name: z.string().max(255, 'Name cannot exceed 255 characters').optional(),
  given_name: z.string().max(255, 'Given name cannot exceed 255 characters').optional(),
  family_name: z.string().max(255, 'Family name cannot exceed 255 characters').optional(),
  roles: z.array(z.string()).optional()
}));

// User preferences validation
export const userPreferencesSchema =
/* istanbul ignore next */
(cov_a3c688ugh().s[9]++, z.object({
  theme: z.enum(['light', 'dark', 'system']).optional(),
  notifications: z.object({
    email: z.boolean().optional(),
    push: z.boolean().optional(),
    sms: z.boolean().optional()
  }).optional(),
  displayDensity: z.enum(['comfortable', 'compact']).optional()
}));

// Renewal validation schemas
export const renewalCreateSchema =
/* istanbul ignore next */
(cov_a3c688ugh().s[10]++, z.object({
  name: nonEmptyStringSchema.max(255, 'Name cannot exceed 255 characters'),
  vendor: nonEmptyStringSchema.max(255, 'Vendor cannot exceed 255 characters'),
  status: z.enum(['active', 'inactive', 'pending', 'expired']).default('active'),
  dueDate: z.string().datetime('Invalid date format').optional(),
  annualCost: z.number().min(0, 'Annual cost cannot be negative').optional(),
  description: z.string().max(1000, 'Description cannot exceed 1000 characters').optional()
}));
export const renewalUpdateSchema =
/* istanbul ignore next */
(cov_a3c688ugh().s[11]++, z.object({
  name: z.string().max(255, 'Name cannot exceed 255 characters').optional(),
  vendor: z.string().max(255, 'Vendor cannot exceed 255 characters').optional(),
  status: z.enum(['active', 'inactive', 'pending', 'expired']).optional(),
  dueDate: z.string().datetime('Invalid date format').optional(),
  annualCost: z.number().min(0, 'Annual cost cannot be negative').optional(),
  description: z.string().max(1000, 'Description cannot exceed 1000 characters').optional()
}));

// Query parameter validation
export const dashboardStatsQuerySchema =
/* istanbul ignore next */
(cov_a3c688ugh().s[12]++, z.object({
  startDate: z.string().datetime('Invalid start date format').optional(),
  endDate: z.string().datetime('Invalid end date format').optional(),
  includeInactive: z.boolean().default(false)
}));
export const renewalsQuerySchema =
/* istanbul ignore next */
(cov_a3c688ugh().s[13]++, paginationSchema.extend({
  status: z.enum(['active', 'inactive', 'pending', 'expired']).optional(),
  vendor: z.string().optional(),
  search: z.string().max(255, 'Search term cannot exceed 255 characters').optional(),
  dueBefore: z.string().datetime('Invalid date format').optional(),
  dueAfter: z.string().datetime('Invalid date format').optional()
}));

// ID parameter validation
export const idParamSchema =
/* istanbul ignore next */
(cov_a3c688ugh().s[14]++, z.object({
  id: uuidSchema
}));
export const clientIdParamSchema =
/* istanbul ignore next */
(cov_a3c688ugh().s[15]++, z.object({
  clientId: z.string().min(1, 'Client ID is required')
}));

// Validation utility functions
export async function validateRequestBody(request, schema) {
  /* istanbul ignore next */
  cov_a3c688ugh().f[0]++;
  cov_a3c688ugh().s[16]++;
  try {
    const body =
    /* istanbul ignore next */
    (cov_a3c688ugh().s[17]++, await request.json());
    const validatedData =
    /* istanbul ignore next */
    (cov_a3c688ugh().s[18]++, schema.parse(body));
    /* istanbul ignore next */
    cov_a3c688ugh().s[19]++;
    return {
      success: true,
      data: validatedData
    };
  } catch (error) {
    /* istanbul ignore next */
    cov_a3c688ugh().s[20]++;
    if (error instanceof z.ZodError) {
      /* istanbul ignore next */
      cov_a3c688ugh().b[0][0]++;
      cov_a3c688ugh().s[21]++;
      return {
        success: false,
        response: handleValidationError(error)
      };
    } else
    /* istanbul ignore next */
    {
      cov_a3c688ugh().b[0][1]++;
    }
    cov_a3c688ugh().s[22]++;
    throw error;
  }
}
export function validateQueryParams(searchParams, schema) {
  /* istanbul ignore next */
  cov_a3c688ugh().f[1]++;
  cov_a3c688ugh().s[23]++;
  try {
    // Convert URLSearchParams to object
    const params =
    /* istanbul ignore next */
    (cov_a3c688ugh().s[24]++, {});
    /* istanbul ignore next */
    cov_a3c688ugh().s[25]++;
    for (const [key, value] of searchParams.entries()) {
      /* istanbul ignore next */
      cov_a3c688ugh().s[26]++;
      // Handle boolean conversion
      if (value === 'true') {
        /* istanbul ignore next */
        cov_a3c688ugh().b[1][0]++;
        cov_a3c688ugh().s[27]++;
        params[key] = true;
      } else {
        /* istanbul ignore next */
        cov_a3c688ugh().b[1][1]++;
        cov_a3c688ugh().s[28]++;
        if (value === 'false') {
          /* istanbul ignore next */
          cov_a3c688ugh().b[2][0]++;
          cov_a3c688ugh().s[29]++;
          params[key] = false;
        } else {
          /* istanbul ignore next */
          cov_a3c688ugh().b[2][1]++;
          cov_a3c688ugh().s[30]++;
          if (
          /* istanbul ignore next */
          (cov_a3c688ugh().b[4][0]++, !isNaN(Number(value))) &&
          /* istanbul ignore next */
          (cov_a3c688ugh().b[4][1]++, value !== '')) {
            /* istanbul ignore next */
            cov_a3c688ugh().b[3][0]++;
            cov_a3c688ugh().s[31]++;
            // Handle number conversion
            params[key] = Number(value);
          } else {
            /* istanbul ignore next */
            cov_a3c688ugh().b[3][1]++;
            cov_a3c688ugh().s[32]++;
            params[key] = value;
          }
        }
      }
    }
    const validatedData =
    /* istanbul ignore next */
    (cov_a3c688ugh().s[33]++, schema.parse(params));
    /* istanbul ignore next */
    cov_a3c688ugh().s[34]++;
    return {
      success: true,
      data: validatedData
    };
  } catch (error) {
    /* istanbul ignore next */
    cov_a3c688ugh().s[35]++;
    if (error instanceof z.ZodError) {
      /* istanbul ignore next */
      cov_a3c688ugh().b[5][0]++;
      cov_a3c688ugh().s[36]++;
      return {
        success: false,
        response: handleValidationError(error)
      };
    } else
    /* istanbul ignore next */
    {
      cov_a3c688ugh().b[5][1]++;
    }
    cov_a3c688ugh().s[37]++;
    throw error;
  }
}
export function validatePathParams(params, schema) {
  /* istanbul ignore next */
  cov_a3c688ugh().f[2]++;
  cov_a3c688ugh().s[38]++;
  try {
    // Convert array values to single strings (take first element)
    const normalizedParams =
    /* istanbul ignore next */
    (cov_a3c688ugh().s[39]++, {});
    /* istanbul ignore next */
    cov_a3c688ugh().s[40]++;
    for (const [key, value] of Object.entries(params)) {
      /* istanbul ignore next */
      cov_a3c688ugh().s[41]++;
      normalizedParams[key] = Array.isArray(value) ?
      /* istanbul ignore next */
      (cov_a3c688ugh().b[6][0]++, value[0]) :
      /* istanbul ignore next */
      (cov_a3c688ugh().b[6][1]++, value);
    }
    const validatedData =
    /* istanbul ignore next */
    (cov_a3c688ugh().s[42]++, schema.parse(normalizedParams));
    /* istanbul ignore next */
    cov_a3c688ugh().s[43]++;
    return {
      success: true,
      data: validatedData
    };
  } catch (error) {
    /* istanbul ignore next */
    cov_a3c688ugh().s[44]++;
    if (error instanceof z.ZodError) {
      /* istanbul ignore next */
      cov_a3c688ugh().b[7][0]++;
      cov_a3c688ugh().s[45]++;
      return {
        success: false,
        response: handleValidationError(error)
      };
    } else
    /* istanbul ignore next */
    {
      cov_a3c688ugh().b[7][1]++;
    }
    cov_a3c688ugh().s[46]++;
    throw error;
  }
}

// Sanitization utilities
export function sanitizeString(input) {
  /* istanbul ignore next */
  cov_a3c688ugh().f[3]++;
  cov_a3c688ugh().s[47]++;
  return input.trim().replace(/[<>]/g, '');
}
export function sanitizeObject(obj) {
  /* istanbul ignore next */
  cov_a3c688ugh().f[4]++;
  const sanitized =
  /* istanbul ignore next */
  (cov_a3c688ugh().s[48]++, {});
  /* istanbul ignore next */
  cov_a3c688ugh().s[49]++;
  for (const [key, value] of Object.entries(obj)) {
    /* istanbul ignore next */
    cov_a3c688ugh().s[50]++;
    if (typeof value === 'string') {
      /* istanbul ignore next */
      cov_a3c688ugh().b[8][0]++;
      cov_a3c688ugh().s[51]++;
      sanitized[key] = sanitizeString(value);
    } else {
      /* istanbul ignore next */
      cov_a3c688ugh().b[8][1]++;
      cov_a3c688ugh().s[52]++;
      if (
      /* istanbul ignore next */
      (cov_a3c688ugh().b[10][0]++, typeof value === 'object') &&
      /* istanbul ignore next */
      (cov_a3c688ugh().b[10][1]++, value !== null) &&
      /* istanbul ignore next */
      (cov_a3c688ugh().b[10][2]++, !Array.isArray(value))) {
        /* istanbul ignore next */
        cov_a3c688ugh().b[9][0]++;
        cov_a3c688ugh().s[53]++;
        sanitized[key] = sanitizeObject(value);
      } else {
        /* istanbul ignore next */
        cov_a3c688ugh().b[9][1]++;
        cov_a3c688ugh().s[54]++;
        sanitized[key] = value;
      }
    }
  }
  /* istanbul ignore next */
  cov_a3c688ugh().s[55]++;
  return sanitized;
}

// Rate limiting validation
export const rateLimitSchema =
/* istanbul ignore next */
(cov_a3c688ugh().s[56]++, z.object({
  maxRequests: z.number().int().min(1).max(1000).default(100),
  windowMs: z.number().int().min(1000).max(3600000).default(60000) // 1 second to 1 hour
}));

// File upload validation
export const fileUploadSchema =
/* istanbul ignore next */
(cov_a3c688ugh().s[57]++, z.object({
  filename: z.string().min(1, 'Filename is required').max(255, 'Filename too long'),
  mimetype: z.string().min(1, 'MIME type is required'),
  size: z.number().int().min(1, 'File size must be positive').max(10485760, 'File size cannot exceed 10MB') // 10MB limit
}));

// Export types for TypeScript
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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