7bc0c744b29e367e763e41ce418e2d88
/* istanbul ignore next */
function cov_xn0pqfcci() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\health\\database\\route.ts";
  var hash = "c785496821ab76da44dffe6c98e4ef81d3990fd6";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\health\\database\\route.ts",
    statementMap: {
      "0": {
        start: {
          line: 18,
          column: 19
        },
        end: {
          line: 49,
          column: 2
        }
      },
      "1": {
        start: {
          line: 20,
          column: 21
        },
        end: {
          line: 20,
          column: 40
        }
      },
      "2": {
        start: {
          line: 21,
          column: 2
        },
        end: {
          line: 23,
          column: 3
        }
      },
      "3": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 22,
          column: 32
        }
      },
      "4": {
        start: {
          line: 26,
          column: 17
        },
        end: {
          line: 26,
          column: 44
        }
      },
      "5": {
        start: {
          line: 28,
          column: 2
        },
        end: {
          line: 34,
          column: 3
        }
      },
      "6": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 33,
          column: 6
        }
      },
      "7": {
        start: {
          line: 36,
          column: 2
        },
        end: {
          line: 48,
          column: 53
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 18,
            column: 37
          },
          end: {
            line: 18,
            column: 38
          }
        },
        loc: {
          start: {
            line: 18,
            column: 49
          },
          end: {
            line: 49,
            column: 1
          }
        },
        line: 18
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 21,
            column: 2
          },
          end: {
            line: 23,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 21,
            column: 2
          },
          end: {
            line: 23,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 21
      },
      "1": {
        loc: {
          start: {
            line: 28,
            column: 2
          },
          end: {
            line: 34,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 2
          },
          end: {
            line: 34,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "2": {
        loc: {
          start: {
            line: 43,
            column: 29
          },
          end: {
            line: 45,
            column: 11
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 44,
            column: 10
          },
          end: {
            line: 44,
            column: 106
          }
        }, {
          start: {
            line: 45,
            column: 10
          },
          end: {
            line: 45,
            column: 11
          }
        }],
        line: 43
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c785496821ab76da44dffe6c98e4ef81d3990fd6"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_xn0pqfcci = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_xn0pqfcci();
/**
 * Database Health Check API
 * 
 * This endpoint provides database connection health status and metrics
 * for monitoring and debugging purposes.
 */

import { getConnectionHealth } from '@/lib/database';
import { requireAuth } from '@/lib/auth-middleware';
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus, withErrorHandling } from '@/lib/api-response';
export const GET =
/* istanbul ignore next */
(cov_xn0pqfcci().s[0]++, withErrorHandling(async () => {
  /* istanbul ignore next */
  cov_xn0pqfcci().f[0]++;
  // Verify authentication (only authenticated users can check health)
  const authResult =
  /* istanbul ignore next */
  (cov_xn0pqfcci().s[1]++, await requireAuth());
  /* istanbul ignore next */
  cov_xn0pqfcci().s[2]++;
  if (!authResult.success) {
    /* istanbul ignore next */
    cov_xn0pqfcci().b[0][0]++;
    cov_xn0pqfcci().s[3]++;
    return authResult.response;
  } else
  /* istanbul ignore next */
  {
    cov_xn0pqfcci().b[0][1]++;
  }

  // Get database health status
  const health =
  /* istanbul ignore next */
  (cov_xn0pqfcci().s[4]++, await getConnectionHealth());
  /* istanbul ignore next */
  cov_xn0pqfcci().s[5]++;
  if (!health.healthy) {
    /* istanbul ignore next */
    cov_xn0pqfcci().b[1][0]++;
    cov_xn0pqfcci().s[6]++;
    return createErrorResponse('Database is unhealthy', ApiErrorCode.DATABASE_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
  } else
  /* istanbul ignore next */
  {
    cov_xn0pqfcci().b[1][1]++;
  }
  cov_xn0pqfcci().s[7]++;
  return createSuccessResponse({
    status: 'healthy',
    database: {
      healthy: health.healthy,
      totalConnections: health.totalConnections,
      idleConnections: health.idleConnections,
      waitingCount: health.waitingCount,
      connectionUtilization: health.totalConnections > 0 ?
      /* istanbul ignore next */
      (cov_xn0pqfcci().b[2][0]++, Math.round((health.totalConnections - health.idleConnections) / health.totalConnections * 100)) :
      /* istanbul ignore next */
      (cov_xn0pqfcci().b[2][1]++, 0)
    },
    timestamp: new Date().toISOString()
  }, 'Database health check completed successfully');
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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