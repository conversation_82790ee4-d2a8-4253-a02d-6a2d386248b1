/**
 * Renewals Filters Component
 * 
 * Filter controls for the renewals table
 */

'use client'

import React, { useMemo } from 'react'
import { BaseComponentProps } from '@/lib/types'

interface Renewal {
  id: string
  name: string
  product: string
  vendor: string
  type: string
  renewalDate: string
  cost: number
  currency: string
  status: string
  alerts: number
}

interface RenewalsFilters {
  search: string
  vendor: string
  type: string
  status: string
}

interface FilterOptions {
  vendors: string[]
  types: string[]
  statuses: string[]
}

interface RenewalsFiltersProps extends BaseComponentProps {
  filters: RenewalsFilters
  onFilterChange: (filterType: keyof RenewalsFilters, value: string) => void
  renewals: Renewal[]
  filterOptions?: FilterOptions
}

const RenewalsFiltersComponent = React.memo(function RenewalsFilters({
  filters,
  onFilterChange,
  renewals,
  filterOptions: propFilterOptions,
  className = '',
  'data-testid': testId
}: RenewalsFiltersProps) {

  // Use provided filter options or extract from renewals as fallback
  const filterOptions = useMemo(() => {
    if (propFilterOptions && propFilterOptions.vendors.length > 0) {
      return propFilterOptions
    }

    // Fallback to extracting from renewals data
    const vendors = [...new Set(renewals.map(r => r.vendor))].filter(Boolean).sort()
    const types = [...new Set(renewals.map(r => r.type))].filter(Boolean).sort()
    const statuses = [...new Set(renewals.map(r => r.status))].filter(Boolean).sort()

    return { vendors, types, statuses }
  }, [renewals, propFilterOptions])

  const handleVendorChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onFilterChange('vendor', e.target.value)
  }

  const handleTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onFilterChange('type', e.target.value)
  }

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onFilterChange('status', e.target.value)
  }

  const clearFilters = () => {
    onFilterChange('vendor', '')
    onFilterChange('type', '')
    onFilterChange('status', '')
  }

  const hasActiveFilters = filters.vendor || filters.type || filters.status

  return (
    <div 
      className={`renewals-filters ${className}`}
      data-testid={testId}
    >
      <div className="filters-row">
        <div className="filter-group">
          <label htmlFor="vendor-filter" className="filter-label">
            All Vendors
          </label>
          <select
            id="vendor-filter"
            value={filters.vendor}
            onChange={handleVendorChange}
            className="filter-select"
          >
            <option value="">All Vendors</option>
            {filterOptions.vendors.map(vendor => (
              <option key={vendor} value={vendor}>
                {vendor}
              </option>
            ))}
          </select>
        </div>

        <div className="filter-group">
          <label htmlFor="type-filter" className="filter-label">
            Type
          </label>
          <select
            id="type-filter"
            value={filters.type}
            onChange={handleTypeChange}
            className="filter-select"
          >
            <option value="">All Types</option>
            {filterOptions.types.map(type => (
              <option key={type} value={type}>
                {type}
              </option>
            ))}
          </select>
        </div>

        <div className="filter-group">
          <label htmlFor="status-filter" className="filter-label">
            Status
          </label>
          <select
            id="status-filter"
            value={filters.status}
            onChange={handleStatusChange}
            className="filter-select"
          >
            <option value="">All Statuses</option>
            {filterOptions.statuses.map(status => (
              <option key={status} value={status}>
                {status}
              </option>
            ))}
          </select>
        </div>

        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="btn btn-secondary btn-sm"
            aria-label="Clear all filters"
          >
            Clear Filters
          </button>
        )}
      </div>
    </div>
  )
})

export default RenewalsFiltersComponent
