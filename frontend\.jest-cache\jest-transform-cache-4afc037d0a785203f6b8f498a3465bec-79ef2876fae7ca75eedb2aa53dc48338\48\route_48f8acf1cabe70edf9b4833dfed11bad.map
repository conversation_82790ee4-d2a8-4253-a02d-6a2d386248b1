{"version": 3, "names": ["cov_20xbgfgwtk", "actualCoverage", "NextResponse", "getTenantByDomain", "z", "requestSchema", "s", "object", "domain", "string", "min", "max", "regex", "GET", "request", "f", "response", "headers", "set", "searchParams", "nextUrl", "get", "parse", "error", "json", "details", "Error", "b", "message", "status", "tenant", "id", "tenant_id", "name", "tenant_name", "subdomain", "console"], "sources": ["route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getTenantByDomain } from '../../../../lib/clients';\nimport { z } from 'zod'; // Add zod for input validation\n\n// Define schema for request validation\nconst requestSchema = z.object({\n  domain: z.string().min(3).max(255).regex(/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\\.[a-zA-Z]{2,})+$/)\n});\n\nexport async function GET(request: NextRequest) {\n  // Rate limiting headers\n  const response = new NextResponse();\n  response.headers.set('X-RateLimit-Limit', '100');\n  response.headers.set('X-RateLimit-Remaining', '99'); // This would be dynamic in a real implementation\n  \n  const searchParams = request.nextUrl.searchParams;\n  const domain = searchParams.get('domain');\n  \n  // Validate input\n  try {\n    requestSchema.parse({ domain });\n  } catch (error) {\n    return NextResponse.json({ \n      error: 'Invalid domain format',\n      details: error instanceof Error ? error.message : 'Validation error'\n    }, { status: 400 });\n  }\n  \n  if (!domain) {\n    return NextResponse.json({ error: 'Domain parameter is required' }, { status: 400 });\n  }\n  \n  try {\n    const tenant = await getTenantByDomain(domain);\n    \n    if (!tenant) {\n      return NextResponse.json({ error: 'Tenant not found for this domain' }, { status: 404 });\n    }\n    \n    // Don't expose internal IDs or sensitive data\n    return NextResponse.json({ \n      id: tenant.tenant_id,\n      name: tenant.tenant_name,\n      subdomain: tenant.subdomain,\n      status: tenant.status,\n      // Don't return schema_name as it's internal implementation detail\n    });\n  } catch (error) {\n    console.error('Error in tenant domain lookup:', error);\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ,SAAsBE,YAAY,QAAQ,aAAa;AACvD,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,CAAC,QAAQ,KAAK,CAAC,CAAC;;AAEzB;AACA,MAAMC,aAAa;AAAA;AAAA,CAAAL,cAAA,GAAAM,CAAA,OAAGF,CAAC,CAACG,MAAM,CAAC;EAC7BC,MAAM,EAAEJ,CAAC,CAACK,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,+DAA+D;AAC1G,CAAC,CAAC;AAEF,OAAO,eAAeC,GAAGA,CAACC,OAAoB,EAAE;EAAA;EAAAd,cAAA,GAAAe,CAAA;EAC9C;EACA,MAAMC,QAAQ;EAAA;EAAA,CAAAhB,cAAA,GAAAM,CAAA,OAAG,IAAIJ,YAAY,CAAC,CAAC;EAAC;EAAAF,cAAA,GAAAM,CAAA;EACpCU,QAAQ,CAACC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,KAAK,CAAC;EAAC;EAAAlB,cAAA,GAAAM,CAAA;EACjDU,QAAQ,CAACC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC,CAAC;;EAErD,MAAMC,YAAY;EAAA;EAAA,CAAAnB,cAAA,GAAAM,CAAA,OAAGQ,OAAO,CAACM,OAAO,CAACD,YAAY;EACjD,MAAMX,MAAM;EAAA;EAAA,CAAAR,cAAA,GAAAM,CAAA,OAAGa,YAAY,CAACE,GAAG,CAAC,QAAQ,CAAC;;EAEzC;EAAA;EAAArB,cAAA,GAAAM,CAAA;EACA,IAAI;IAAA;IAAAN,cAAA,GAAAM,CAAA;IACFD,aAAa,CAACiB,KAAK,CAAC;MAAEd;IAAO,CAAC,CAAC;EACjC,CAAC,CAAC,OAAOe,KAAK,EAAE;IAAA;IAAAvB,cAAA,GAAAM,CAAA;IACd,OAAOJ,YAAY,CAACsB,IAAI,CAAC;MACvBD,KAAK,EAAE,uBAAuB;MAC9BE,OAAO,EAAEF,KAAK,YAAYG,KAAK;MAAA;MAAA,CAAA1B,cAAA,GAAA2B,CAAA,UAAGJ,KAAK,CAACK,OAAO;MAAA;MAAA,CAAA5B,cAAA,GAAA2B,CAAA,UAAG,kBAAkB;IACtE,CAAC,EAAE;MAAEE,MAAM,EAAE;IAAI,CAAC,CAAC;EACrB;EAAC;EAAA7B,cAAA,GAAAM,CAAA;EAED,IAAI,CAACE,MAAM,EAAE;IAAA;IAAAR,cAAA,GAAA2B,CAAA;IAAA3B,cAAA,GAAAM,CAAA;IACX,OAAOJ,YAAY,CAACsB,IAAI,CAAC;MAAED,KAAK,EAAE;IAA+B,CAAC,EAAE;MAAEM,MAAM,EAAE;IAAI,CAAC,CAAC;EACtF,CAAC;EAAA;EAAA;IAAA7B,cAAA,GAAA2B,CAAA;EAAA;EAAA3B,cAAA,GAAAM,CAAA;EAED,IAAI;IACF,MAAMwB,MAAM;IAAA;IAAA,CAAA9B,cAAA,GAAAM,CAAA,QAAG,MAAMH,iBAAiB,CAACK,MAAM,CAAC;IAAC;IAAAR,cAAA,GAAAM,CAAA;IAE/C,IAAI,CAACwB,MAAM,EAAE;MAAA;MAAA9B,cAAA,GAAA2B,CAAA;MAAA3B,cAAA,GAAAM,CAAA;MACX,OAAOJ,YAAY,CAACsB,IAAI,CAAC;QAAED,KAAK,EAAE;MAAmC,CAAC,EAAE;QAAEM,MAAM,EAAE;MAAI,CAAC,CAAC;IAC1F,CAAC;IAAA;IAAA;MAAA7B,cAAA,GAAA2B,CAAA;IAAA;;IAED;IAAA3B,cAAA,GAAAM,CAAA;IACA,OAAOJ,YAAY,CAACsB,IAAI,CAAC;MACvBO,EAAE,EAAED,MAAM,CAACE,SAAS;MACpBC,IAAI,EAAEH,MAAM,CAACI,WAAW;MACxBC,SAAS,EAAEL,MAAM,CAACK,SAAS;MAC3BN,MAAM,EAAEC,MAAM,CAACD;MACf;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAON,KAAK,EAAE;IAAA;IAAAvB,cAAA,GAAAM,CAAA;IACd8B,OAAO,CAACb,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IAAC;IAAAvB,cAAA,GAAAM,CAAA;IACvD,OAAOJ,YAAY,CAACsB,IAAI,CAAC;MAAED,KAAK,EAAE;IAAwB,CAAC,EAAE;MAAEM,MAAM,EAAE;IAAI,CAAC,CAAC;EAC/E;AACF", "ignoreList": []}