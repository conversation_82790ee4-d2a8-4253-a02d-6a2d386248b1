/**
 * Product Matching Algorithm
 * 
 * Implements sophisticated product matching logic with vendor context:
 * 1. GTIN/Barcode Match (confidence: 95%)
 * 2. Manufacturer SKU + Vendor Match (confidence: 90%)
 * 3. Name + Vendor + Category Match (confidence: 70-85%)
 */

import { executeQuery } from '@/lib/database';

// Interfaces for product matching
export interface TenantProduct {
  id: string;
  vendor_id: string;
  name: string;
  description?: string;
  category?: string;
  sku?: string;
  barcode?: string;
  unit_of_measure?: string;
  global_vendor_id?: string; // If vendor is already matched
}

export interface GlobalProduct {
  id: string;
  canonical_name: string;
  product_category?: string;
  vendor_id: string;
  gtin?: string;
  manufacturer_sku?: string;
  product_type: string;
  vendor_name: string;
}

export interface ProductMatch {
  global_product_id: string;
  confidence_score: number;
  match_reasons: string[];
  match_details: {
    gtin_match?: boolean;
    sku_match?: boolean;
    name_similarity?: number;
    category_match?: boolean;
    vendor_match?: boolean;
  };
}

export interface ProductMatchResult {
  tenant_product_id: string;
  matches: ProductMatch[];
  best_match?: ProductMatch;
  recommendation: 'auto_match' | 'manual_review' | 'create_new' | 'vendor_not_matched';
}

export class ProductMatcher {
  // Confidence thresholds
  private static readonly AUTO_MATCH_THRESHOLD = 0.85;
  private static readonly MANUAL_REVIEW_THRESHOLD = 0.50;
  
  /**
   * Find matches for a tenant product against global products
   */
  static async findMatches(tenantProduct: TenantProduct): Promise<ProductMatchResult> {
    // Check if vendor is already matched
    if (!tenantProduct.global_vendor_id) {
      return {
        tenant_product_id: tenantProduct.id,
        matches: [],
        recommendation: 'vendor_not_matched'
      };
    }
    
    const matches: ProductMatch[] = [];
    
    // Get all active global products for the matched vendor
    const globalProductsResult = await executeQuery(`
      SELECT 
        p.id, p.canonical_name, p.product_category, p.vendor_id, 
        p.gtin, p.manufacturer_sku, p.product_type,
        v.canonical_name as vendor_name
      FROM metadata.global_products p
      JOIN metadata.global_vendors v ON p.vendor_id = v.id
      WHERE p.status = 'active' AND p.vendor_id = $1
      ORDER BY p.canonical_name
    `, [tenantProduct.global_vendor_id]);
    
    if (!globalProductsResult.success || !globalProductsResult.data) {
      return {
        tenant_product_id: tenantProduct.id,
        matches: [],
        recommendation: 'create_new'
      };
    }
    
    const globalProducts: GlobalProduct[] = globalProductsResult.data;
    
    // Calculate match scores for each global product
    for (const globalProduct of globalProducts) {
      const match = this.calculateMatchScore(tenantProduct, globalProduct);
      if (match.confidence_score > 0) {
        matches.push(match);
      }
    }
    
    // Sort matches by confidence score (descending)
    matches.sort((a, b) => b.confidence_score - a.confidence_score);
    
    // Determine recommendation
    const bestMatch = matches.length > 0 ? matches[0] : undefined;
    let recommendation: 'auto_match' | 'manual_review' | 'create_new' | 'vendor_not_matched' = 'create_new';
    
    if (bestMatch) {
      if (bestMatch.confidence_score >= this.AUTO_MATCH_THRESHOLD) {
        recommendation = 'auto_match';
      } else if (bestMatch.confidence_score >= this.MANUAL_REVIEW_THRESHOLD) {
        recommendation = 'manual_review';
      }
    }
    
    return {
      tenant_product_id: tenantProduct.id,
      matches: matches.slice(0, 5), // Return top 5 matches
      best_match: bestMatch,
      recommendation
    };
  }
  
  /**
   * Calculate match score between tenant and global product
   */
  private static calculateMatchScore(tenantProduct: TenantProduct, globalProduct: GlobalProduct): ProductMatch {
    const matchReasons: string[] = [];
    const matchDetails: ProductMatch['match_details'] = {};
    let totalScore = 0;
    let weightSum = 0;
    
    // 1. GTIN/Barcode Match (confidence: 95%, weight: 40)
    if (tenantProduct.barcode && globalProduct.gtin) {
      const normalizedTenantBarcode = this.normalizeGtin(tenantProduct.barcode);
      const normalizedGlobalGtin = this.normalizeGtin(globalProduct.gtin);
      
      if (normalizedTenantBarcode === normalizedGlobalGtin) {
        totalScore += 0.95 * 40;
        weightSum += 40;
        matchReasons.push('Exact GTIN/Barcode match');
        matchDetails.gtin_match = true;
      }
    }
    
    // 2. Manufacturer SKU + Vendor Match (confidence: 90%, weight: 35)
    if (tenantProduct.sku && globalProduct.manufacturer_sku) {
      const normalizedTenantSku = this.normalizeSku(tenantProduct.sku);
      const normalizedGlobalSku = this.normalizeSku(globalProduct.manufacturer_sku);
      
      if (normalizedTenantSku === normalizedGlobalSku) {
        totalScore += 0.90 * 35;
        weightSum += 35;
        matchReasons.push('Exact SKU match');
        matchDetails.sku_match = true;
        matchDetails.vendor_match = true; // Implicit since we're only matching within vendor
      }
    }
    
    // 3. Name + Vendor + Category Match (confidence: 70-85%, weight: 25)
    const nameScore = this.calculateNameSimilarity(tenantProduct.name, globalProduct.canonical_name);
    if (nameScore > 0.6) {
      let combinedScore = 0.7; // Base score for name match within same vendor
      let scoreBonus = 0;
      
      // Add bonus for category match
      if (tenantProduct.category && globalProduct.product_category) {
        const categoryMatch = this.calculateCategorySimilarity(tenantProduct.category, globalProduct.product_category);
        if (categoryMatch > 0.7) {
          scoreBonus += 0.1;
          matchDetails.category_match = true;
          matchReasons.push('Category match');
        }
      }
      
      // Add bonus for high name similarity
      if (nameScore > 0.8) {
        scoreBonus += 0.05;
      }
      
      combinedScore = Math.min(0.85, combinedScore + scoreBonus);
      totalScore += combinedScore * 25;
      weightSum += 25;
      matchReasons.push(`Name similarity (${Math.round(nameScore * 100)}%)`);
      matchDetails.name_similarity = nameScore;
      matchDetails.vendor_match = true;
    }
    
    // Calculate final confidence score
    const confidenceScore = weightSum > 0 ? totalScore / weightSum : 0;
    
    return {
      global_product_id: globalProduct.id,
      confidence_score: Math.round(confidenceScore * 10000) / 10000, // Round to 4 decimal places
      match_reasons: matchReasons,
      match_details: matchDetails
    };
  }
  
  /**
   * Normalize GTIN by removing spaces, hyphens, and padding to 14 digits
   */
  private static normalizeGtin(gtin: string): string {
    const cleaned = gtin.replace(/[\s\-]/g, '');
    // Pad with leading zeros to make it 14 digits (GTIN-14 format)
    return cleaned.padStart(14, '0');
  }
  
  /**
   * Normalize SKU by removing spaces and converting to uppercase
   */
  private static normalizeSku(sku: string): string {
    return sku.replace(/\s/g, '').toUpperCase();
  }
  
  /**
   * Calculate name similarity using Levenshtein distance
   */
  private static calculateNameSimilarity(name1: string, name2: string): number {
    const normalized1 = this.normalizeName(name1);
    const normalized2 = this.normalizeName(name2);
    
    if (normalized1 === normalized2) return 1.0;
    
    const distance = this.levenshteinDistance(normalized1, normalized2);
    const maxLength = Math.max(normalized1.length, normalized2.length);
    
    return maxLength > 0 ? 1 - (distance / maxLength) : 0;
  }
  
  /**
   * Normalize name for comparison
   */
  private static normalizeName(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^\w\s]/g, '') // Remove punctuation
      .replace(/\b(v\d+|version|ver|edition|ed)\b/g, '') // Remove version indicators
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }
  
  /**
   * Calculate Levenshtein distance between two strings
   */
  private static levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1, // deletion
          matrix[j - 1][i] + 1, // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }
  
  /**
   * Calculate category similarity
   */
  private static calculateCategorySimilarity(category1: string, category2: string): number {
    const normalized1 = this.normalizeCategory(category1);
    const normalized2 = this.normalizeCategory(category2);
    
    if (normalized1 === normalized2) return 1.0;
    
    // Check for partial matches
    const words1 = normalized1.split(' ');
    const words2 = normalized2.split(' ');
    
    let matchingWords = 0;
    for (const word1 of words1) {
      if (words2.includes(word1)) {
        matchingWords++;
      }
    }
    
    const totalWords = Math.max(words1.length, words2.length);
    return totalWords > 0 ? matchingWords / totalWords : 0;
  }
  
  /**
   * Normalize category for comparison
   */
  private static normalizeCategory(category: string): string {
    return category
      .toLowerCase()
      .replace(/[^\w\s]/g, '') // Remove punctuation
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }
  
  /**
   * Test the product matching algorithm with sample data
   */
  static async testMatching(): Promise<any> {
    // First, get a global vendor ID for Microsoft
    const vendorResult = await executeQuery(`
      SELECT id FROM metadata.global_vendors 
      WHERE canonical_name = 'Microsoft Corporation' 
      LIMIT 1
    `);
    
    if (!vendorResult.success || !vendorResult.data || vendorResult.data.length === 0) {
      return { error: 'Microsoft vendor not found for testing' };
    }
    
    const microsoftVendorId = vendorResult.data[0].id;
    
    // Create a test tenant product
    const testTenantProduct: TenantProduct = {
      id: 'test-tenant-product-1',
      vendor_id: 'tenant-vendor-microsoft',
      name: 'Microsoft 365 Business',
      description: 'Microsoft 365 productivity suite',
      category: 'Productivity Suite',
      sku: 'M365-BUS-001',
      barcode: '123456789012',
      global_vendor_id: microsoftVendorId
    };
    
    return await this.findMatches(testTenantProduct);
  }
}
