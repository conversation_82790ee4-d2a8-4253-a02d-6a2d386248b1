{"version": 3, "names": ["cov_1d32atkwd8", "actualCoverage", "NextResponse", "ZodError", "ApiErrorCode", "HttpStatus", "createSuccessResponse", "data", "message", "status", "b", "OK", "f", "response", "s", "success", "timestamp", "Date", "toISOString", "json", "createErrorResponse", "error", "errorCode", "details", "process", "env", "NODE_ENV", "handleValidationError", "errorMessage", "errors", "map", "err", "path", "join", "VALIDATION_ERROR", "BAD_REQUEST", "handleDatabaseError", "console", "DATABASE_ERROR", "INTERNAL_SERVER_ERROR", "undefined", "createUnauthorizedResponse", "UNAUTHORIZED", "createForbiddenResponse", "FORBIDDEN", "createNotFoundResponse", "resource", "NOT_FOUND", "createRateLimitResponse", "RATE_LIMITED", "TOO_MANY_REQUESTS", "headers", "set", "handleApiError", "code", "startsWith", "INTERNAL_ERROR", "addSecurityHeaders", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handler", "args", "errorResponse"], "sources": ["api-response.ts"], "sourcesContent": ["/**\n * Standardized API Response Utilities\n * \n * This module provides consistent response formatting and error handling\n * for all API routes in the application.\n */\n\nimport { NextResponse } from 'next/server';\nimport { ZodError } from 'zod';\n\n// Standard API response interface\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  errorCode?: string;\n  message?: string;\n  timestamp: string;\n}\n\n// Error types for consistent error handling\nexport enum ApiErrorCode {\n  UNAUTHORIZED = 'UNAUTHORIZED',\n  FORBIDDEN = 'FORBIDDEN',\n  NOT_FOUND = 'NOT_FOUND',\n  VALIDATION_ERROR = 'VALIDATION_ERROR',\n  DATABASE_ERROR = 'DATABASE_ERROR',\n  INTERNAL_ERROR = 'INTERNAL_ERROR',\n  RATE_LIMITED = 'RATE_LIMITED',\n  INVALID_INPUT = 'INVALID_INPUT',\n  MULTIPLE_FOUND = 'MULTIPLE_FOUND',\n}\n\n// HTTP status codes\nexport enum HttpStatus {\n  OK = 200,\n  CREATED = 201,\n  BAD_REQUEST = 400,\n  UNAUTHORIZED = 401,\n  FORBIDDEN = 403,\n  NOT_FOUND = 404,\n  CONFLICT = 409,\n  UNPROCESSABLE_ENTITY = 422,\n  TOO_MANY_REQUESTS = 429,\n  INTERNAL_SERVER_ERROR = 500,\n}\n\n/**\n * Create a successful API response\n */\nexport function createSuccessResponse<T>(\n  data: T,\n  message?: string,\n  status: HttpStatus = HttpStatus.OK\n): NextResponse<ApiResponse<T>> {\n  const response: ApiResponse<T> = {\n    success: true,\n    data,\n    message,\n    timestamp: new Date().toISOString(),\n  };\n\n  return NextResponse.json(response, { status });\n}\n\n/**\n * Create an error API response\n */\nexport function createErrorResponse(\n  error: string,\n  errorCode: ApiErrorCode,\n  status: HttpStatus,\n  details?: any\n): NextResponse<ApiResponse> {\n  const response: ApiResponse = {\n    success: false,\n    error,\n    errorCode,\n    timestamp: new Date().toISOString(),\n  };\n\n  // Add details in development mode only\n  if (process.env.NODE_ENV === 'development' && details) {\n    (response as any).details = details;\n  }\n\n  return NextResponse.json(response, { status });\n}\n\n/**\n * Handle validation errors from Zod\n */\nexport function handleValidationError(error: ZodError): NextResponse<ApiResponse> {\n  const errorMessage = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');\n  \n  return createErrorResponse(\n    `Validation failed: ${errorMessage}`,\n    ApiErrorCode.VALIDATION_ERROR,\n    HttpStatus.BAD_REQUEST,\n    error.errors\n  );\n}\n\n/**\n * Handle database errors\n */\nexport function handleDatabaseError(error: any): NextResponse<ApiResponse> {\n  console.error('Database error:', error);\n  \n  // Don't expose internal database errors in production\n  const message = process.env.NODE_ENV === 'development' \n    ? `Database error: ${error.message}`\n    : 'A database error occurred';\n\n  return createErrorResponse(\n    message,\n    ApiErrorCode.DATABASE_ERROR,\n    HttpStatus.INTERNAL_SERVER_ERROR,\n    process.env.NODE_ENV === 'development' ? error : undefined\n  );\n}\n\n/**\n * Handle unauthorized access\n */\nexport function createUnauthorizedResponse(message = 'Unauthorized'): NextResponse<ApiResponse> {\n  return createErrorResponse(\n    message,\n    ApiErrorCode.UNAUTHORIZED,\n    HttpStatus.UNAUTHORIZED\n  );\n}\n\n/**\n * Handle forbidden access\n */\nexport function createForbiddenResponse(message = 'Forbidden'): NextResponse<ApiResponse> {\n  return createErrorResponse(\n    message,\n    ApiErrorCode.FORBIDDEN,\n    HttpStatus.FORBIDDEN\n  );\n}\n\n/**\n * Handle not found errors\n */\nexport function createNotFoundResponse(resource = 'Resource'): NextResponse<ApiResponse> {\n  return createErrorResponse(\n    `${resource} not found`,\n    ApiErrorCode.NOT_FOUND,\n    HttpStatus.NOT_FOUND\n  );\n}\n\n/**\n * Handle rate limiting\n */\nexport function createRateLimitResponse(): NextResponse<ApiResponse> {\n  const response = createErrorResponse(\n    'Too many requests',\n    ApiErrorCode.RATE_LIMITED,\n    HttpStatus.TOO_MANY_REQUESTS\n  );\n\n  // Add rate limit headers\n  response.headers.set('Retry-After', '60');\n  response.headers.set('X-RateLimit-Limit', '100');\n  response.headers.set('X-RateLimit-Remaining', '0');\n\n  return response;\n}\n\n/**\n * Generic error handler for API routes\n */\nexport function handleApiError(error: any): NextResponse<ApiResponse> {\n  console.error('API Error:', error);\n\n  // Handle specific error types\n  if (error instanceof ZodError) {\n    return handleValidationError(error);\n  }\n\n  // Handle database errors (you might want to check for specific database error types)\n  if (error.code && (error.code.startsWith('23') || error.code.startsWith('42'))) {\n    return handleDatabaseError(error);\n  }\n\n  // Generic internal server error\n  const message = process.env.NODE_ENV === 'development' \n    ? error.message || 'Internal server error'\n    : 'Internal server error';\n\n  return createErrorResponse(\n    message,\n    ApiErrorCode.INTERNAL_ERROR,\n    HttpStatus.INTERNAL_SERVER_ERROR,\n    process.env.NODE_ENV === 'development' ? error : undefined\n  );\n}\n\n/**\n * Add security headers to response\n */\nexport function addSecurityHeaders(response: NextResponse): NextResponse {\n  response.headers.set('X-Content-Type-Options', 'nosniff');\n  response.headers.set('X-Frame-Options', 'DENY');\n  response.headers.set('X-XSS-Protection', '1; mode=block');\n  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');\n  \n  return response;\n}\n\n/**\n * Wrapper for API route handlers with error handling\n */\nexport function withErrorHandling<T extends any[], R>(\n  handler: (...args: T) => Promise<NextResponse<ApiResponse<R>>>\n) {\n  return async (...args: T): Promise<NextResponse<ApiResponse<R>>> => {\n    try {\n      const response = await handler(...args);\n      return addSecurityHeaders(response);\n    } catch (error) {\n      const errorResponse = handleApiError(error);\n      return addSecurityHeaders(errorResponse);\n    }\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASE,YAAY,QAAQ,aAAa;AAC1C,SAASC,QAAQ,QAAQ,KAAK;;AAE9B;;AAUA;AACA;AAAA;AAAA,IAAYC,YAAY,0BAAZA,YAAY;EAAZA,YAAY;EAAZA,YAAY;EAAZA,YAAY;EAAZA,YAAY;EAAZA,YAAY;EAAZA,YAAY;EAAZA,YAAY;EAAZA,YAAY;EAAZA,YAAY;EAAA,OAAZA,YAAY;AAAA;;AAYxB;AACA;AAAA;AAAA,IAAYC,UAAU,0BAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EAAA,OAAVA,UAAU;AAAA;;AAatB;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CACnCC,IAAO,EACPC,OAAgB,EAChBC,MAAkB;AAAA;AAAA,CAAAT,cAAA,GAAAU,CAAA,UAAGL,UAAU,CAACM,EAAE,GACJ;EAAA;EAAAX,cAAA,GAAAY,CAAA;EAC9B,MAAMC,QAAwB;EAAA;EAAA,CAAAb,cAAA,GAAAc,CAAA,OAAG;IAC/BC,OAAO,EAAE,IAAI;IACbR,IAAI;IACJC,OAAO;IACPQ,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EACpC,CAAC;EAAC;EAAAlB,cAAA,GAAAc,CAAA;EAEF,OAAOZ,YAAY,CAACiB,IAAI,CAACN,QAAQ,EAAE;IAAEJ;EAAO,CAAC,CAAC;AAChD;;AAEA;AACA;AACA;AACA,OAAO,SAASW,mBAAmBA,CACjCC,KAAa,EACbC,SAAuB,EACvBb,MAAkB,EAClBc,OAAa,EACc;EAAA;EAAAvB,cAAA,GAAAY,CAAA;EAC3B,MAAMC,QAAqB;EAAA;EAAA,CAAAb,cAAA,GAAAc,CAAA,OAAG;IAC5BC,OAAO,EAAE,KAAK;IACdM,KAAK;IACLC,SAAS;IACTN,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EACpC,CAAC;;EAED;EAAA;EAAAlB,cAAA,GAAAc,CAAA;EACA;EAAI;EAAA,CAAAd,cAAA,GAAAU,CAAA,UAAAc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa;EAAA;EAAA,CAAA1B,cAAA,GAAAU,CAAA,UAAIa,OAAO,GAAE;IAAA;IAAAvB,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAc,CAAA;IACpDD,QAAQ,CAASU,OAAO,GAAGA,OAAO;EACrC,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAU,CAAA;EAAA;EAAAV,cAAA,GAAAc,CAAA;EAED,OAAOZ,YAAY,CAACiB,IAAI,CAACN,QAAQ,EAAE;IAAEJ;EAAO,CAAC,CAAC;AAChD;;AAEA;AACA;AACA;AACA,OAAO,SAASkB,qBAAqBA,CAACN,KAAe,EAA6B;EAAA;EAAArB,cAAA,GAAAY,CAAA;EAChF,MAAMgB,YAAY;EAAA;EAAA,CAAA5B,cAAA,GAAAc,CAAA,OAAGO,KAAK,CAACQ,MAAM,CAACC,GAAG,CAACC,GAAG,IAAI;IAAA;IAAA/B,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAc,CAAA;IAAA,UAAGiB,GAAG,CAACC,IAAI,CAACC,IAAI,CAAC,GAAG,CAAC,KAAKF,GAAG,CAACvB,OAAO,EAAE;EAAD,CAAC,CAAC,CAACyB,IAAI,CAAC,IAAI,CAAC;EAAC;EAAAjC,cAAA,GAAAc,CAAA;EAEjG,OAAOM,mBAAmB,CACxB,sBAAsBQ,YAAY,EAAE,EACpCxB,YAAY,CAAC8B,gBAAgB,EAC7B7B,UAAU,CAAC8B,WAAW,EACtBd,KAAK,CAACQ,MACR,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASO,mBAAmBA,CAACf,KAAU,EAA6B;EAAA;EAAArB,cAAA,GAAAY,CAAA;EAAAZ,cAAA,GAAAc,CAAA;EACzEuB,OAAO,CAAChB,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;;EAEvC;EACA,MAAMb,OAAO;EAAA;EAAA,CAAAR,cAAA,GAAAc,CAAA,QAAGU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa;EAAA;EAAA,CAAA1B,cAAA,GAAAU,CAAA,UAClD,mBAAmBW,KAAK,CAACb,OAAO,EAAE;EAAA;EAAA,CAAAR,cAAA,GAAAU,CAAA,UAClC,2BAA2B;EAAC;EAAAV,cAAA,GAAAc,CAAA;EAEhC,OAAOM,mBAAmB,CACxBZ,OAAO,EACPJ,YAAY,CAACkC,cAAc,EAC3BjC,UAAU,CAACkC,qBAAqB,EAChCf,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa;EAAA;EAAA,CAAA1B,cAAA,GAAAU,CAAA,UAAGW,KAAK;EAAA;EAAA,CAAArB,cAAA,GAAAU,CAAA,UAAG8B,SAAS,CAC5D,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASC,0BAA0BA,CAACjC,OAAO;AAAA;AAAA,CAAAR,cAAA,GAAAU,CAAA,UAAG,cAAc,GAA6B;EAAA;EAAAV,cAAA,GAAAY,CAAA;EAAAZ,cAAA,GAAAc,CAAA;EAC9F,OAAOM,mBAAmB,CACxBZ,OAAO,EACPJ,YAAY,CAACsC,YAAY,EACzBrC,UAAU,CAACqC,YACb,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAACnC,OAAO;AAAA;AAAA,CAAAR,cAAA,GAAAU,CAAA,UAAG,WAAW,GAA6B;EAAA;EAAAV,cAAA,GAAAY,CAAA;EAAAZ,cAAA,GAAAc,CAAA;EACxF,OAAOM,mBAAmB,CACxBZ,OAAO,EACPJ,YAAY,CAACwC,SAAS,EACtBvC,UAAU,CAACuC,SACb,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASC,sBAAsBA,CAACC,QAAQ;AAAA;AAAA,CAAA9C,cAAA,GAAAU,CAAA,UAAG,UAAU,GAA6B;EAAA;EAAAV,cAAA,GAAAY,CAAA;EAAAZ,cAAA,GAAAc,CAAA;EACvF,OAAOM,mBAAmB,CACxB,GAAG0B,QAAQ,YAAY,EACvB1C,YAAY,CAAC2C,SAAS,EACtB1C,UAAU,CAAC0C,SACb,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAAA,EAA8B;EAAA;EAAAhD,cAAA,GAAAY,CAAA;EACnE,MAAMC,QAAQ;EAAA;EAAA,CAAAb,cAAA,GAAAc,CAAA,QAAGM,mBAAmB,CAClC,mBAAmB,EACnBhB,YAAY,CAAC6C,YAAY,EACzB5C,UAAU,CAAC6C,iBACb,CAAC;;EAED;EAAA;EAAAlD,cAAA,GAAAc,CAAA;EACAD,QAAQ,CAACsC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC;EAAC;EAAApD,cAAA,GAAAc,CAAA;EAC1CD,QAAQ,CAACsC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,KAAK,CAAC;EAAC;EAAApD,cAAA,GAAAc,CAAA;EACjDD,QAAQ,CAACsC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,GAAG,CAAC;EAAC;EAAApD,cAAA,GAAAc,CAAA;EAEnD,OAAOD,QAAQ;AACjB;;AAEA;AACA;AACA;AACA,OAAO,SAASwC,cAAcA,CAAChC,KAAU,EAA6B;EAAA;EAAArB,cAAA,GAAAY,CAAA;EAAAZ,cAAA,GAAAc,CAAA;EACpEuB,OAAO,CAAChB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;;EAElC;EAAA;EAAArB,cAAA,GAAAc,CAAA;EACA,IAAIO,KAAK,YAAYlB,QAAQ,EAAE;IAAA;IAAAH,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAc,CAAA;IAC7B,OAAOa,qBAAqB,CAACN,KAAK,CAAC;EACrC,CAAC;EAAA;EAAA;IAAArB,cAAA,GAAAU,CAAA;EAAA;;EAED;EAAAV,cAAA,GAAAc,CAAA;EACA;EAAI;EAAA,CAAAd,cAAA,GAAAU,CAAA,WAAAW,KAAK,CAACiC,IAAI;EAAK;EAAA,CAAAtD,cAAA,GAAAU,CAAA,WAAAW,KAAK,CAACiC,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC;EAAA;EAAA,CAAAvD,cAAA,GAAAU,CAAA,WAAIW,KAAK,CAACiC,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC,EAAC,EAAE;IAAA;IAAAvD,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAc,CAAA;IAC9E,OAAOsB,mBAAmB,CAACf,KAAK,CAAC;EACnC,CAAC;EAAA;EAAA;IAAArB,cAAA,GAAAU,CAAA;EAAA;;EAED;EACA,MAAMF,OAAO;EAAA;EAAA,CAAAR,cAAA,GAAAc,CAAA,QAAGU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa;EAAA;EAAA,CAAA1B,cAAA,GAAAU,CAAA;EAClD;EAAA,CAAAV,cAAA,GAAAU,CAAA,WAAAW,KAAK,CAACb,OAAO;EAAA;EAAA,CAAAR,cAAA,GAAAU,CAAA,WAAI,uBAAuB;EAAA;EAAA,CAAAV,cAAA,GAAAU,CAAA,WACxC,uBAAuB;EAAC;EAAAV,cAAA,GAAAc,CAAA;EAE5B,OAAOM,mBAAmB,CACxBZ,OAAO,EACPJ,YAAY,CAACoD,cAAc,EAC3BnD,UAAU,CAACkC,qBAAqB,EAChCf,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa;EAAA;EAAA,CAAA1B,cAAA,GAAAU,CAAA,WAAGW,KAAK;EAAA;EAAA,CAAArB,cAAA,GAAAU,CAAA,WAAG8B,SAAS,CAC5D,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASiB,kBAAkBA,CAAC5C,QAAsB,EAAgB;EAAA;EAAAb,cAAA,GAAAY,CAAA;EAAAZ,cAAA,GAAAc,CAAA;EACvED,QAAQ,CAACsC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,SAAS,CAAC;EAAC;EAAApD,cAAA,GAAAc,CAAA;EAC1DD,QAAQ,CAACsC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC;EAAC;EAAApD,cAAA,GAAAc,CAAA;EAChDD,QAAQ,CAACsC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,eAAe,CAAC;EAAC;EAAApD,cAAA,GAAAc,CAAA;EAC1DD,QAAQ,CAACsC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,iCAAiC,CAAC;EAAC;EAAApD,cAAA,GAAAc,CAAA;EAE3E,OAAOD,QAAQ;AACjB;;AAEA;AACA;AACA;AACA,OAAO,SAAS6C,iBAAiBA,CAC/BC,OAA8D,EAC9D;EAAA;EAAA3D,cAAA,GAAAY,CAAA;EAAAZ,cAAA,GAAAc,CAAA;EACA,OAAO,OAAO,GAAG8C,IAAO,KAA4C;IAAA;IAAA5D,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAc,CAAA;IAClE,IAAI;MACF,MAAMD,QAAQ;MAAA;MAAA,CAAAb,cAAA,GAAAc,CAAA,QAAG,MAAM6C,OAAO,CAAC,GAAGC,IAAI,CAAC;MAAC;MAAA5D,cAAA,GAAAc,CAAA;MACxC,OAAO2C,kBAAkB,CAAC5C,QAAQ,CAAC;IACrC,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACd,MAAMwC,aAAa;MAAA;MAAA,CAAA7D,cAAA,GAAAc,CAAA,QAAGuC,cAAc,CAAChC,KAAK,CAAC;MAAC;MAAArB,cAAA,GAAAc,CAAA;MAC5C,OAAO2C,kBAAkB,CAACI,aAAa,CAAC;IAC1C;EACF,CAAC;AACH", "ignoreList": []}