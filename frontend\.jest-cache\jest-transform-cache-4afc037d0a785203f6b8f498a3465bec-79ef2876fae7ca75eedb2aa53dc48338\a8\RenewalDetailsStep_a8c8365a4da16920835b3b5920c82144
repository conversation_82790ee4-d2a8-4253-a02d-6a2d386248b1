d5014e778bcd2602c05601741b5146be
/**
 * Renewal Details Step Component
 * 
 * First step of the Add Renewal modal - collects renewal information
 */

'use client';

/* istanbul ignore next */
import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\modals\\steps\\RenewalDetailsStep.tsx";
var __jsx = React.createElement;
function cov_1lfbhaoj97() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\modals\\steps\\RenewalDetailsStep.tsx";
  var hash = "0d900cb1224d2e0d7b29a673937197a81d273372";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\modals\\steps\\RenewalDetailsStep.tsx",
    statementMap: {
      "0": {
        start: {
          line: 19,
          column: 62
        },
        end: {
          line: 325,
          column: 1
        }
      },
      "1": {
        start: {
          line: 26,
          column: 23
        },
        end: {
          line: 31,
          column: 22
        }
      },
      "2": {
        start: {
          line: 27,
          column: 4
        },
        end: {
          line: 30,
          column: 6
        }
      },
      "3": {
        start: {
          line: 34,
          column: 29
        },
        end: {
          line: 37,
          column: 20
        }
      },
      "4": {
        start: {
          line: 35,
          column: 19
        },
        end: {
          line: 35,
          column: 92
        }
      },
      "5": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 68
        }
      },
      "6": {
        start: {
          line: 35,
          column: 86
        },
        end: {
          line: 35,
          column: 91
        }
      },
      "7": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 36,
          column: 44
        }
      },
      "8": {
        start: {
          line: 40,
          column: 18
        },
        end: {
          line: 40,
          column: 69
        }
      },
      "9": {
        start: {
          line: 42,
          column: 2
        },
        end: {
          line: 324,
          column: 3
        }
      },
      "10": {
        start: {
          line: 56,
          column: 29
        },
        end: {
          line: 56,
          column: 72
        }
      },
      "11": {
        start: {
          line: 71,
          column: 29
        },
        end: {
          line: 71,
          column: 68
        }
      },
      "12": {
        start: {
          line: 86,
          column: 29
        },
        end: {
          line: 86,
          column: 67
        }
      },
      "13": {
        start: {
          line: 99,
          column: 29
        },
        end: {
          line: 99,
          column: 65
        }
      },
      "14": {
        start: {
          line: 117,
          column: 29
        },
        end: {
          line: 117,
          column: 73
        }
      },
      "15": {
        start: {
          line: 136,
          column: 29
        },
        end: {
          line: 136,
          column: 71
        }
      },
      "16": {
        start: {
          line: 150,
          column: 29
        },
        end: {
          line: 150,
          column: 73
        }
      },
      "17": {
        start: {
          line: 164,
          column: 29
        },
        end: {
          line: 164,
          column: 72
        }
      },
      "18": {
        start: {
          line: 180,
          column: 27
        },
        end: {
          line: 180,
          column: 61
        }
      },
      "19": {
        start: {
          line: 198,
          column: 27
        },
        end: {
          line: 198,
          column: 67
        }
      },
      "20": {
        start: {
          line: 212,
          column: 29
        },
        end: {
          line: 212,
          column: 69
        }
      },
      "21": {
        start: {
          line: 233,
          column: 29
        },
        end: {
          line: 233,
          column: 82
        }
      },
      "22": {
        start: {
          line: 249,
          column: 27
        },
        end: {
          line: 249,
          column: 67
        }
      },
      "23": {
        start: {
          line: 268,
          column: 27
        },
        end: {
          line: 268,
          column: 86
        }
      },
      "24": {
        start: {
          line: 286,
          column: 27
        },
        end: {
          line: 286,
          column: 70
        }
      },
      "25": {
        start: {
          line: 301,
          column: 27
        },
        end: {
          line: 301,
          column: 64
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 19,
            column: 62
          },
          end: {
            line: 19,
            column: 63
          }
        },
        loc: {
          start: {
            line: 24,
            column: 6
          },
          end: {
            line: 325,
            column: 1
          }
        },
        line: 24
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 26,
            column: 35
          },
          end: {
            line: 26,
            column: 36
          }
        },
        loc: {
          start: {
            line: 26,
            column: 81
          },
          end: {
            line: 31,
            column: 3
          }
        },
        line: 26
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 34,
            column: 41
          },
          end: {
            line: 34,
            column: 42
          }
        },
        loc: {
          start: {
            line: 34,
            column: 67
          },
          end: {
            line: 37,
            column: 3
          }
        },
        line: 34
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 35,
            column: 47
          },
          end: {
            line: 35,
            column: 48
          }
        },
        loc: {
          start: {
            line: 35,
            column: 56
          },
          end: {
            line: 35,
            column: 68
          }
        },
        line: 35
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 35,
            column: 77
          },
          end: {
            line: 35,
            column: 78
          }
        },
        loc: {
          start: {
            line: 35,
            column: 86
          },
          end: {
            line: 35,
            column: 91
          }
        },
        line: 35
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 56,
            column: 22
          },
          end: {
            line: 56,
            column: 23
          }
        },
        loc: {
          start: {
            line: 56,
            column: 29
          },
          end: {
            line: 56,
            column: 72
          }
        },
        line: 56
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 71,
            column: 22
          },
          end: {
            line: 71,
            column: 23
          }
        },
        loc: {
          start: {
            line: 71,
            column: 29
          },
          end: {
            line: 71,
            column: 68
          }
        },
        line: 71
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 86,
            column: 22
          },
          end: {
            line: 86,
            column: 23
          }
        },
        loc: {
          start: {
            line: 86,
            column: 29
          },
          end: {
            line: 86,
            column: 67
          }
        },
        line: 86
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 99,
            column: 22
          },
          end: {
            line: 99,
            column: 23
          }
        },
        loc: {
          start: {
            line: 99,
            column: 29
          },
          end: {
            line: 99,
            column: 65
          }
        },
        line: 99
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 117,
            column: 22
          },
          end: {
            line: 117,
            column: 23
          }
        },
        loc: {
          start: {
            line: 117,
            column: 29
          },
          end: {
            line: 117,
            column: 73
          }
        },
        line: 117
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 136,
            column: 22
          },
          end: {
            line: 136,
            column: 23
          }
        },
        loc: {
          start: {
            line: 136,
            column: 29
          },
          end: {
            line: 136,
            column: 71
          }
        },
        line: 136
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 150,
            column: 22
          },
          end: {
            line: 150,
            column: 23
          }
        },
        loc: {
          start: {
            line: 150,
            column: 29
          },
          end: {
            line: 150,
            column: 73
          }
        },
        line: 150
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 164,
            column: 22
          },
          end: {
            line: 164,
            column: 23
          }
        },
        loc: {
          start: {
            line: 164,
            column: 29
          },
          end: {
            line: 164,
            column: 72
          }
        },
        line: 164
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 180,
            column: 20
          },
          end: {
            line: 180,
            column: 21
          }
        },
        loc: {
          start: {
            line: 180,
            column: 27
          },
          end: {
            line: 180,
            column: 61
          }
        },
        line: 180
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 198,
            column: 20
          },
          end: {
            line: 198,
            column: 21
          }
        },
        loc: {
          start: {
            line: 198,
            column: 27
          },
          end: {
            line: 198,
            column: 67
          }
        },
        line: 198
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 212,
            column: 22
          },
          end: {
            line: 212,
            column: 23
          }
        },
        loc: {
          start: {
            line: 212,
            column: 29
          },
          end: {
            line: 212,
            column: 69
          }
        },
        line: 212
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 233,
            column: 22
          },
          end: {
            line: 233,
            column: 23
          }
        },
        loc: {
          start: {
            line: 233,
            column: 29
          },
          end: {
            line: 233,
            column: 82
          }
        },
        line: 233
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 249,
            column: 20
          },
          end: {
            line: 249,
            column: 21
          }
        },
        loc: {
          start: {
            line: 249,
            column: 27
          },
          end: {
            line: 249,
            column: 67
          }
        },
        line: 249
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 268,
            column: 20
          },
          end: {
            line: 268,
            column: 21
          }
        },
        loc: {
          start: {
            line: 268,
            column: 27
          },
          end: {
            line: 268,
            column: 86
          }
        },
        line: 268
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 286,
            column: 20
          },
          end: {
            line: 286,
            column: 21
          }
        },
        loc: {
          start: {
            line: 286,
            column: 27
          },
          end: {
            line: 286,
            column: 70
          }
        },
        line: 286
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 301,
            column: 20
          },
          end: {
            line: 301,
            column: 21
          }
        },
        loc: {
          start: {
            line: 301,
            column: 27
          },
          end: {
            line: 301,
            column: 64
          }
        },
        line: 301
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 40,
            column: 18
          },
          end: {
            line: 40,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 18
          },
          end: {
            line: 40,
            column: 34
          }
        }, {
          start: {
            line: 40,
            column: 38
          },
          end: {
            line: 40,
            column: 49
          }
        }, {
          start: {
            line: 40,
            column: 53
          },
          end: {
            line: 40,
            column: 69
          }
        }],
        line: 40
      },
      "1": {
        loc: {
          start: {
            line: 232,
            column: 19
          },
          end: {
            line: 232,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 232,
            column: 19
          },
          end: {
            line: 232,
            column: 28
          }
        }, {
          start: {
            line: 232,
            column: 32
          },
          end: {
            line: 232,
            column: 34
          }
        }],
        line: 232
      },
      "2": {
        loc: {
          start: {
            line: 233,
            column: 50
          },
          end: {
            line: 233,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 233,
            column: 50
          },
          end: {
            line: 233,
            column: 76
          }
        }, {
          start: {
            line: 233,
            column: 80
          },
          end: {
            line: 233,
            column: 81
          }
        }],
        line: 233
      },
      "3": {
        loc: {
          start: {
            line: 267,
            column: 17
          },
          end: {
            line: 267,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 267,
            column: 17
          },
          end: {
            line: 267,
            column: 34
          }
        }, {
          start: {
            line: 267,
            column: 38
          },
          end: {
            line: 267,
            column: 40
          }
        }],
        line: 267
      },
      "4": {
        loc: {
          start: {
            line: 268,
            column: 56
          },
          end: {
            line: 268,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 268,
            column: 56
          },
          end: {
            line: 268,
            column: 80
          }
        }, {
          start: {
            line: 268,
            column: 84
          },
          end: {
            line: 268,
            column: 85
          }
        }],
        line: 268
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0d900cb1224d2e0d7b29a673937197a81d273372"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1lfbhaoj97 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1lfbhaoj97();
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
import React, { useCallback } from 'react';
/* istanbul ignore next */
cov_1lfbhaoj97().s[0]++;
const RenewalDetailsStep = ({
  data,
  onChange,
  onNext,
  onCancel
}) => {
  /* istanbul ignore next */
  cov_1lfbhaoj97().f[0]++;
  // Handle form field changes
  const handleChange =
  /* istanbul ignore next */
  (cov_1lfbhaoj97().s[1]++, useCallback((field, value) => {
    /* istanbul ignore next */
    cov_1lfbhaoj97().f[1]++;
    cov_1lfbhaoj97().s[2]++;
    onChange(
    /* istanbul ignore next */
    _objectSpread(_objectSpread({}, data), {}, {
      [field]: value
    }));
  }, [data, onChange]));

  // Handle email list changes
  const handleEmailsChange =
  /* istanbul ignore next */
  (cov_1lfbhaoj97().s[3]++, useCallback(emailsString => {
    /* istanbul ignore next */
    cov_1lfbhaoj97().f[2]++;
    const emails =
    /* istanbul ignore next */
    (cov_1lfbhaoj97().s[4]++, emailsString.split(',').map(email => {
      /* istanbul ignore next */
      cov_1lfbhaoj97().f[3]++;
      cov_1lfbhaoj97().s[5]++;
      return email.trim();
    }).filter(email => {
      /* istanbul ignore next */
      cov_1lfbhaoj97().f[4]++;
      cov_1lfbhaoj97().s[6]++;
      return email;
    }));
    /* istanbul ignore next */
    cov_1lfbhaoj97().s[7]++;
    handleChange('associatedEmails', emails);
  }, [handleChange]));

  // Validate required fields
  const isValid =
  /* istanbul ignore next */
  (cov_1lfbhaoj97().s[8]++,
  /* istanbul ignore next */
  (cov_1lfbhaoj97().b[0][0]++, data.productName) &&
  /* istanbul ignore next */
  (cov_1lfbhaoj97().b[0][1]++, data.vendor) &&
  /* istanbul ignore next */
  (cov_1lfbhaoj97().b[0][2]++, data.renewalDate));
  /* istanbul ignore next */
  cov_1lfbhaoj97().s[9]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "renewal-details-step",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 43,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "form-grid",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 44,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "form-group",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 46,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "label",
  /* istanbul ignore next */
  {
    htmlFor: "productName",
    className: "form-label required",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 47,
      columnNumber: 11
    }
  }, "Product Name*"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "input",
  /* istanbul ignore next */
  {
    id: "productName",
    type: "text",
    className: "form-input",
    placeholder: "Type product name...",
    value: data.productName,
    onChange: e => {
      /* istanbul ignore next */
      cov_1lfbhaoj97().f[5]++;
      cov_1lfbhaoj97().s[10]++;
      return handleChange('productName', e.target.value);
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 50,
      columnNumber: 11
    }
  })),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "form-group",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 61,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "label",
  /* istanbul ignore next */
  {
    htmlFor: "version",
    className: "form-label",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 62,
      columnNumber: 11
    }
  }, "Version/Edition*"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "input",
  /* istanbul ignore next */
  {
    id: "version",
    type: "text",
    className: "form-input",
    placeholder: "e.g. 2021 or Enterprise",
    value: data.version,
    onChange: e => {
      /* istanbul ignore next */
      cov_1lfbhaoj97().f[6]++;
      cov_1lfbhaoj97().s[11]++;
      return handleChange('version', e.target.value);
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 65,
      columnNumber: 11
    }
  })),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "form-group",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 76,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "label",
  /* istanbul ignore next */
  {
    htmlFor: "vendor",
    className: "form-label required",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 77,
      columnNumber: 11
    }
  }, "Vendor*"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "input",
  /* istanbul ignore next */
  {
    id: "vendor",
    type: "text",
    className: "form-input",
    placeholder: "Type vendor name...",
    value: data.vendor,
    onChange: e => {
      /* istanbul ignore next */
      cov_1lfbhaoj97().f[7]++;
      cov_1lfbhaoj97().s[12]++;
      return handleChange('vendor', e.target.value);
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 80,
      columnNumber: 11
    }
  })),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "form-group",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 91,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "label",
  /* istanbul ignore next */
  {
    htmlFor: "type",
    className: "form-label",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 92,
      columnNumber: 11
    }
  }, "Type*"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "select",
  /* istanbul ignore next */
  {
    id: "type",
    className: "form-select",
    value: data.type,
    onChange: e => {
      /* istanbul ignore next */
      cov_1lfbhaoj97().f[8]++;
      cov_1lfbhaoj97().s[13]++;
      return handleChange('type', e.target.value);
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 95,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "option",
  /* istanbul ignore next */
  {
    value: "Subscription",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 101,
      columnNumber: 13
    }
  }, "Subscription"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "option",
  /* istanbul ignore next */
  {
    value: "License",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 102,
      columnNumber: 13
    }
  }, "License"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "option",
  /* istanbul ignore next */
  {
    value: "Support",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 103,
      columnNumber: 13
    }
  }, "Support"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "option",
  /* istanbul ignore next */
  {
    value: "Maintenance",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 104,
      columnNumber: 13
    }
  }, "Maintenance"))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "form-group",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 109,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "label",
  /* istanbul ignore next */
  {
    htmlFor: "purchaseType",
    className: "form-label",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 110,
      columnNumber: 11
    }
  }, "Purchase Type*"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "select",
  /* istanbul ignore next */
  {
    id: "purchaseType",
    className: "form-select",
    value: data.purchaseType,
    onChange: e => {
      /* istanbul ignore next */
      cov_1lfbhaoj97().f[9]++;
      cov_1lfbhaoj97().s[14]++;
      return handleChange('purchaseType', e.target.value);
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 113,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "option",
  /* istanbul ignore next */
  {
    value: "Direct from Vendor",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 119,
      columnNumber: 13
    }
  }, "Direct from Vendor"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "option",
  /* istanbul ignore next */
  {
    value: "Reseller",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 120,
      columnNumber: 13
    }
  }, "Reseller"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "option",
  /* istanbul ignore next */
  {
    value: "Marketplace",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 121,
      columnNumber: 13
    }
  }, "Marketplace"))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "form-group",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 126,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "label",
  /* istanbul ignore next */
  {
    htmlFor: "department",
    className: "form-label",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 127,
      columnNumber: 11
    }
  }, "Department*"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "input",
  /* istanbul ignore next */
  {
    id: "department",
    type: "text",
    className: "form-input",
    placeholder: "Enter department...",
    value: data.department,
    onChange: e => {
      /* istanbul ignore next */
      cov_1lfbhaoj97().f[10]++;
      cov_1lfbhaoj97().s[15]++;
      return handleChange('department', e.target.value);
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 130,
      columnNumber: 11
    }
  })),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "form-group",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 141,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "label",
  /* istanbul ignore next */
  {
    htmlFor: "licensedDate",
    className: "form-label",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 142,
      columnNumber: 11
    }
  }, "Licensed Date*"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "input",
  /* istanbul ignore next */
  {
    id: "licensedDate",
    type: "date",
    className: "form-input",
    value: data.licensedDate,
    onChange: e => {
      /* istanbul ignore next */
      cov_1lfbhaoj97().f[11]++;
      cov_1lfbhaoj97().s[16]++;
      return handleChange('licensedDate', e.target.value);
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 145,
      columnNumber: 11
    }
  })),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "form-group",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 155,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "label",
  /* istanbul ignore next */
  {
    htmlFor: "renewalDate",
    className: "form-label required",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 156,
      columnNumber: 11
    }
  }, "Renewal Date*"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "input",
  /* istanbul ignore next */
  {
    id: "renewalDate",
    type: "date",
    className: "form-input",
    value: data.renewalDate,
    onChange: e => {
      /* istanbul ignore next */
      cov_1lfbhaoj97().f[12]++;
      cov_1lfbhaoj97().s[17]++;
      return handleChange('renewalDate', e.target.value);
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 159,
      columnNumber: 11
    }
  }))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "form-group full-width",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 170,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "label",
  /* istanbul ignore next */
  {
    htmlFor: "associatedEmails",
    className: "form-label",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 171,
      columnNumber: 9
    }
  }, "Associated Email Addresses*"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "textarea",
  /* istanbul ignore next */
  {
    id: "associatedEmails",
    className: "form-textarea",
    placeholder: "Enter email addresses separated by commas",
    rows: 3,
    value: data.associatedEmails.join(', '),
    onChange: e => {
      /* istanbul ignore next */
      cov_1lfbhaoj97().f[13]++;
      cov_1lfbhaoj97().s[18]++;
      return handleEmailsChange(e.target.value);
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 174,
      columnNumber: 9
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "form-help",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 182,
      columnNumber: 9
    }
  }, "Email addresses associated with this renewal or subscription")),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "form-group full-width",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 188,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "label",
  /* istanbul ignore next */
  {
    htmlFor: "reseller",
    className: "form-label",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 189,
      columnNumber: 9
    }
  }, "Reseller Information"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "input",
  /* istanbul ignore next */
  {
    id: "reseller",
    type: "text",
    className: "form-input",
    placeholder: "Reseller name (if applicable)",
    value: data.reseller,
    onChange: e => {
      /* istanbul ignore next */
      cov_1lfbhaoj97().f[14]++;
      cov_1lfbhaoj97().s[19]++;
      return handleChange('reseller', e.target.value);
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 192,
      columnNumber: 9
    }
  })),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "form-grid",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 203,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "form-group",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 204,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "label",
  /* istanbul ignore next */
  {
    htmlFor: "currency",
    className: "form-label",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 205,
      columnNumber: 11
    }
  }, "Currency*"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "select",
  /* istanbul ignore next */
  {
    id: "currency",
    className: "form-select",
    value: data.currency,
    onChange: e => {
      /* istanbul ignore next */
      cov_1lfbhaoj97().f[15]++;
      cov_1lfbhaoj97().s[20]++;
      return handleChange('currency', e.target.value);
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 208,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "option",
  /* istanbul ignore next */
  {
    value: "USD",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 214,
      columnNumber: 13
    }
  }, "USD"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "option",
  /* istanbul ignore next */
  {
    value: "EUR",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 215,
      columnNumber: 13
    }
  }, "EUR"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "option",
  /* istanbul ignore next */
  {
    value: "GBP",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 216,
      columnNumber: 13
    }
  }, "GBP"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "option",
  /* istanbul ignore next */
  {
    value: "CAD",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 217,
      columnNumber: 13
    }
  }, "CAD"))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "form-group",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 221,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "label",
  /* istanbul ignore next */
  {
    htmlFor: "cost",
    className: "form-label",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 222,
      columnNumber: 11
    }
  }, "Cost*"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "input",
  /* istanbul ignore next */
  {
    id: "cost",
    type: "number",
    className: "form-input",
    placeholder: "Amount in USD",
    min: "0",
    step: "0.01",
    value:
    /* istanbul ignore next */
    (cov_1lfbhaoj97().b[1][0]++, data.cost) ||
    /* istanbul ignore next */
    (cov_1lfbhaoj97().b[1][1]++, ''),
    onChange: e => {
      /* istanbul ignore next */
      cov_1lfbhaoj97().f[16]++;
      cov_1lfbhaoj97().s[21]++;
      return handleChange('cost',
      /* istanbul ignore next */
      (cov_1lfbhaoj97().b[2][0]++, parseFloat(e.target.value)) ||
      /* istanbul ignore next */
      (cov_1lfbhaoj97().b[2][1]++, 0));
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 225,
      columnNumber: 11
    }
  }))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "form-group full-width",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 239,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "label",
  /* istanbul ignore next */
  {
    htmlFor: "costCode",
    className: "form-label",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 240,
      columnNumber: 9
    }
  }, "Cost Code"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "input",
  /* istanbul ignore next */
  {
    id: "costCode",
    type: "text",
    className: "form-input",
    placeholder: "Optional cost code for accounting",
    value: data.costCode,
    onChange: e => {
      /* istanbul ignore next */
      cov_1lfbhaoj97().f[17]++;
      cov_1lfbhaoj97().s[22]++;
      return handleChange('costCode', e.target.value);
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 243,
      columnNumber: 9
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "form-help",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 251,
      columnNumber: 9
    }
  }, "Enter an optional cost code for internal accounting purposes")),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "form-group full-width",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 257,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "label",
  /* istanbul ignore next */
  {
    htmlFor: "licenseCount",
    className: "form-label",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 258,
      columnNumber: 9
    }
  }, "License Count"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "input",
  /* istanbul ignore next */
  {
    id: "licenseCount",
    type: "number",
    className: "form-input",
    placeholder: "Number of licenses (optional)",
    min: "0",
    value:
    /* istanbul ignore next */
    (cov_1lfbhaoj97().b[3][0]++, data.licenseCount) ||
    /* istanbul ignore next */
    (cov_1lfbhaoj97().b[3][1]++, ''),
    onChange: e => {
      /* istanbul ignore next */
      cov_1lfbhaoj97().f[18]++;
      cov_1lfbhaoj97().s[23]++;
      return handleChange('licenseCount',
      /* istanbul ignore next */
      (cov_1lfbhaoj97().b[4][0]++, parseInt(e.target.value)) ||
      /* istanbul ignore next */
      (cov_1lfbhaoj97().b[4][1]++, 0));
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 261,
      columnNumber: 9
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "form-help",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 270,
      columnNumber: 9
    }
  }, "Enter the number of licenses included in this renewal (optional)")),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "form-group full-width",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 276,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "label",
  /* istanbul ignore next */
  {
    htmlFor: "description",
    className: "form-label",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 277,
      columnNumber: 9
    }
  }, "Description"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "textarea",
  /* istanbul ignore next */
  {
    id: "description",
    className: "form-textarea",
    placeholder: "Brief description of the renewal",
    rows: 3,
    value: data.description,
    onChange: e => {
      /* istanbul ignore next */
      cov_1lfbhaoj97().f[19]++;
      cov_1lfbhaoj97().s[24]++;
      return handleChange('description', e.target.value);
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 280,
      columnNumber: 9
    }
  })),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "form-group full-width",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 291,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "label",
  /* istanbul ignore next */
  {
    htmlFor: "notes",
    className: "form-label",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 292,
      columnNumber: 9
    }
  }, "Notes"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "textarea",
  /* istanbul ignore next */
  {
    id: "notes",
    className: "form-textarea",
    placeholder: "Additional information about this renewal",
    rows: 3,
    value: data.notes,
    onChange: e => {
      /* istanbul ignore next */
      cov_1lfbhaoj97().f[20]++;
      cov_1lfbhaoj97().s[25]++;
      return handleChange('notes', e.target.value);
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 295,
      columnNumber: 9
    }
  })),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "modal-actions",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 306,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  {
    type: "button",
    className: "btn btn-secondary",
    onClick: onCancel,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 307,
      columnNumber: 9
    }
  }, "Cancel"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  {
    type: "button",
    className: "btn btn-primary",
    onClick: onNext,
    disabled: !isValid,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 314,
      columnNumber: 9
    }
  }, "Next: Set Up Alerts")));
};
export default RenewalDetailsStep;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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