{"version": 3, "names": ["cov_2itgipuc8j", "actualCoverage", "requireAuth", "createSuccessResponse", "createErrorResponse", "ApiErrorCode", "HttpStatus", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "POST", "s", "f", "authResult", "success", "b", "response", "session", "console", "log", "email", "scanId", "Date", "now", "status", "message", "error", "INTERNAL_ERROR", "INTERNAL_SERVER_ERROR"], "sources": ["route.ts"], "sourcesContent": ["import { requireAuth } from '@/lib/auth-middleware';\nimport {\n  createSuccessResponse,\n  createErrorResponse,\n  ApiErrorCode,\n  HttpStatus,\n  withErrorHandling\n} from '@/lib/api-response';\n\nexport const POST = withErrorHandling(async () => {\n  // Verify authentication and get session\n  const authResult = await requireAuth();\n  if (!authResult.success) {\n    return authResult.response!;\n  }\n\n  const session = authResult.session!;\n\n  try {\n    // TODO: Implement actual scan functionality\n    // For now, just return success\n    console.log(`Scan initiated by user: ${session.email}`);\n\n    return createSuccessResponse({\n      scanId: `scan_${Date.now()}`,\n      status: 'initiated',\n      message: '<PERSON><PERSON> started successfully'\n    }, '<PERSON><PERSON> initiated successfully');\n\n  } catch (error) {\n    console.error('Error running scan:', error);\n    return createErrorResponse(\n      'Failed to run scan',\n      ApiErrorCode.INTERNAL_ERROR,\n      HttpStatus.INTERNAL_SERVER_ERROR\n    );\n  }\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ,SAASE,WAAW,QAAQ,uBAAuB;AACnD,SACEC,qBAAqB,EACrBC,mBAAmB,EACnBC,YAAY,EACZC,UAAU,EACVC,iBAAiB,QACZ,oBAAoB;AAE3B,OAAO,MAAMC,IAAI;AAAA;AAAA,CAAAR,cAAA,GAAAS,CAAA,OAAGF,iBAAiB,CAAC,YAAY;EAAA;EAAAP,cAAA,GAAAU,CAAA;EAChD;EACA,MAAMC,UAAU;EAAA;EAAA,CAAAX,cAAA,GAAAS,CAAA,OAAG,MAAMP,WAAW,CAAC,CAAC;EAAC;EAAAF,cAAA,GAAAS,CAAA;EACvC,IAAI,CAACE,UAAU,CAACC,OAAO,EAAE;IAAA;IAAAZ,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAS,CAAA;IACvB,OAAOE,UAAU,CAACG,QAAQ;EAC5B,CAAC;EAAA;EAAA;IAAAd,cAAA,GAAAa,CAAA;EAAA;EAED,MAAME,OAAO;EAAA;EAAA,CAAAf,cAAA,GAAAS,CAAA,OAAGE,UAAU,CAACI,OAAO,CAAC;EAAC;EAAAf,cAAA,GAAAS,CAAA;EAEpC,IAAI;IAAA;IAAAT,cAAA,GAAAS,CAAA;IACF;IACA;IACAO,OAAO,CAACC,GAAG,CAAC,2BAA2BF,OAAO,CAACG,KAAK,EAAE,CAAC;IAAC;IAAAlB,cAAA,GAAAS,CAAA;IAExD,OAAON,qBAAqB,CAAC;MAC3BgB,MAAM,EAAE,QAAQC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MAC5BC,MAAM,EAAE,WAAW;MACnBC,OAAO,EAAE;IACX,CAAC,EAAE,6BAA6B,CAAC;EAEnC,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA;IAAAxB,cAAA,GAAAS,CAAA;IACdO,OAAO,CAACQ,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAAC;IAAAxB,cAAA,GAAAS,CAAA;IAC5C,OAAOL,mBAAmB,CACxB,oBAAoB,EACpBC,YAAY,CAACoB,cAAc,EAC3BnB,UAAU,CAACoB,qBACb,CAAC;EACH;AACF,CAAC,CAAC", "ignoreList": []}