ecc8e261ae20c67784bc62b2ec46e2ca
'use client';

/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\dashboard\\layout.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_2ga3zjmfwz() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\dashboard\\layout.tsx";
  var hash = "fd9074b175c2d728e8afaae3c18aa132ef33f11f";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\dashboard\\layout.tsx",
    statementMap: {
      "0": {
        start: {
          line: 13,
          column: 41
        },
        end: {
          line: 13,
          column: 50
        }
      },
      "1": {
        start: {
          line: 14,
          column: 17
        },
        end: {
          line: 14,
          column: 28
        }
      },
      "2": {
        start: {
          line: 15,
          column: 44
        },
        end: {
          line: 15,
          column: 59
        }
      },
      "3": {
        start: {
          line: 17,
          column: 2
        },
        end: {
          line: 49,
          column: 57
        }
      },
      "4": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 23,
          column: 7
        }
      },
      "5": {
        start: {
          line: 26,
          column: 22
        },
        end: {
          line: 26,
          column: 65
        }
      },
      "6": {
        start: {
          line: 27,
          column: 25
        },
        end: {
          line: 27,
          column: 46
        }
      },
      "7": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 33,
          column: 5
        }
      },
      "8": {
        start: {
          line: 30,
          column: 6
        },
        end: {
          line: 30,
          column: 101
        }
      },
      "9": {
        start: {
          line: 32,
          column: 6
        },
        end: {
          line: 32,
          column: 12
        }
      },
      "10": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 48,
          column: 5
        }
      },
      "11": {
        start: {
          line: 37,
          column: 6
        },
        end: {
          line: 37,
          column: 28
        }
      },
      "12": {
        start: {
          line: 38,
          column: 6
        },
        end: {
          line: 38,
          column: 86
        }
      },
      "13": {
        start: {
          line: 39,
          column: 6
        },
        end: {
          line: 39,
          column: 27
        }
      },
      "14": {
        start: {
          line: 40,
          column: 11
        },
        end: {
          line: 48,
          column: 5
        }
      },
      "15": {
        start: {
          line: 41,
          column: 6
        },
        end: {
          line: 41,
          column: 82
        }
      },
      "16": {
        start: {
          line: 44,
          column: 6
        },
        end: {
          line: 47,
          column: 7
        }
      },
      "17": {
        start: {
          line: 45,
          column: 25
        },
        end: {
          line: 45,
          column: 49
        }
      },
      "18": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 65
        }
      },
      "19": {
        start: {
          line: 52,
          column: 2
        },
        end: {
          line: 59,
          column: 3
        }
      },
      "20": {
        start: {
          line: 53,
          column: 4
        },
        end: {
          line: 58,
          column: 5
        }
      },
      "21": {
        start: {
          line: 62,
          column: 2
        },
        end: {
          line: 69,
          column: 3
        }
      },
      "22": {
        start: {
          line: 63,
          column: 4
        },
        end: {
          line: 68,
          column: 5
        }
      },
      "23": {
        start: {
          line: 71,
          column: 2
        },
        end: {
          line: 78,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "DashboardLayout",
        decl: {
          start: {
            line: 8,
            column: 24
          },
          end: {
            line: 8,
            column: 39
          }
        },
        loc: {
          start: {
            line: 12,
            column: 3
          },
          end: {
            line: 79,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 17,
            column: 12
          },
          end: {
            line: 17,
            column: 13
          }
        },
        loc: {
          start: {
            line: 17,
            column: 18
          },
          end: {
            line: 49,
            column: 3
          }
        },
        line: 17
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 29,
            column: 4
          },
          end: {
            line: 33,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 4
          },
          end: {
            line: 33,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "1": {
        loc: {
          start: {
            line: 36,
            column: 4
          },
          end: {
            line: 48,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 36,
            column: 4
          },
          end: {
            line: 48,
            column: 5
          }
        }, {
          start: {
            line: 40,
            column: 11
          },
          end: {
            line: 48,
            column: 5
          }
        }],
        line: 36
      },
      "2": {
        loc: {
          start: {
            line: 36,
            column: 8
          },
          end: {
            line: 36,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 36,
            column: 8
          },
          end: {
            line: 36,
            column: 18
          }
        }, {
          start: {
            line: 36,
            column: 22
          },
          end: {
            line: 36,
            column: 38
          }
        }, {
          start: {
            line: 36,
            column: 42
          },
          end: {
            line: 36,
            column: 56
          }
        }],
        line: 36
      },
      "3": {
        loc: {
          start: {
            line: 40,
            column: 11
          },
          end: {
            line: 48,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 11
          },
          end: {
            line: 48,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "4": {
        loc: {
          start: {
            line: 40,
            column: 15
          },
          end: {
            line: 40,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 15
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 44
          }
        }],
        line: 40
      },
      "5": {
        loc: {
          start: {
            line: 44,
            column: 6
          },
          end: {
            line: 47,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 6
          },
          end: {
            line: 47,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "6": {
        loc: {
          start: {
            line: 52,
            column: 2
          },
          end: {
            line: 59,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 52,
            column: 2
          },
          end: {
            line: 59,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 52
      },
      "7": {
        loc: {
          start: {
            line: 62,
            column: 2
          },
          end: {
            line: 69,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 62,
            column: 2
          },
          end: {
            line: 69,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 62
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "fd9074b175c2d728e8afaae3c18aa132ef33f11f"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2ga3zjmfwz = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2ga3zjmfwz();
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AppContext';
import Sidebar from '@/components/layout/Sidebar';
export default function DashboardLayout({
  children
}) {
  /* istanbul ignore next */
  cov_2ga3zjmfwz().f[0]++;
  const {
    isAuthenticated,
    isLoading
  } =
  /* istanbul ignore next */
  (cov_2ga3zjmfwz().s[0]++, useAuth());
  const router =
  /* istanbul ignore next */
  (cov_2ga3zjmfwz().s[1]++, useRouter());
  const [isRedirecting, setIsRedirecting] =
  /* istanbul ignore next */
  (cov_2ga3zjmfwz().s[2]++, useState(false));
  /* istanbul ignore next */
  cov_2ga3zjmfwz().s[3]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_2ga3zjmfwz().f[1]++;
    cov_2ga3zjmfwz().s[4]++;
    console.log('🏠 [DASHBOARD-LAYOUT] Auth state changed:', {
      isLoading,
      isAuthenticated,
      isRedirecting,
      timestamp: new Date().toLocaleTimeString()
    });

    // Check if this is an OAuth callback (has code parameter)
    const urlParams =
    /* istanbul ignore next */
    (cov_2ga3zjmfwz().s[5]++, new URLSearchParams(window.location.search));
    const hasOAuthCode =
    /* istanbul ignore next */
    (cov_2ga3zjmfwz().s[6]++, urlParams.has('code'));
    /* istanbul ignore next */
    cov_2ga3zjmfwz().s[7]++;
    if (hasOAuthCode) {
      /* istanbul ignore next */
      cov_2ga3zjmfwz().b[0][0]++;
      cov_2ga3zjmfwz().s[8]++;
      console.log('🔄 [DASHBOARD-LAYOUT] OAuth callback detected, waiting for Amplify to process...');
      // Give Amplify extra time to process OAuth callback
      /* istanbul ignore next */
      cov_2ga3zjmfwz().s[9]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_2ga3zjmfwz().b[0][1]++;
    }

    // Only redirect if authentication check is complete and user is not authenticated
    cov_2ga3zjmfwz().s[10]++;
    if (
    /* istanbul ignore next */
    (cov_2ga3zjmfwz().b[2][0]++, !isLoading) &&
    /* istanbul ignore next */
    (cov_2ga3zjmfwz().b[2][1]++, !isAuthenticated) &&
    /* istanbul ignore next */
    (cov_2ga3zjmfwz().b[2][2]++, !isRedirecting)) {
      /* istanbul ignore next */
      cov_2ga3zjmfwz().b[1][0]++;
      cov_2ga3zjmfwz().s[11]++;
      setIsRedirecting(true);
      /* istanbul ignore next */
      cov_2ga3zjmfwz().s[12]++;
      console.log('❌ [DASHBOARD-LAYOUT] User not authenticated, redirecting to login');
      /* istanbul ignore next */
      cov_2ga3zjmfwz().s[13]++;
      router.push('/login');
    } else {
      /* istanbul ignore next */
      cov_2ga3zjmfwz().b[1][1]++;
      cov_2ga3zjmfwz().s[14]++;
      if (
      /* istanbul ignore next */
      (cov_2ga3zjmfwz().b[4][0]++, !isLoading) &&
      /* istanbul ignore next */
      (cov_2ga3zjmfwz().b[4][1]++, isAuthenticated)) {
        /* istanbul ignore next */
        cov_2ga3zjmfwz().b[3][0]++;
        cov_2ga3zjmfwz().s[15]++;
        console.log('✅ [DASHBOARD-LAYOUT] User is authenticated, showing dashboard');

        // Clean up URL if it has OAuth parameters
        /* istanbul ignore next */
        cov_2ga3zjmfwz().s[16]++;
        if (hasOAuthCode) {
          /* istanbul ignore next */
          cov_2ga3zjmfwz().b[5][0]++;
          const cleanUrl =
          /* istanbul ignore next */
          (cov_2ga3zjmfwz().s[17]++, window.location.pathname);
          /* istanbul ignore next */
          cov_2ga3zjmfwz().s[18]++;
          window.history.replaceState({}, document.title, cleanUrl);
        } else
        /* istanbul ignore next */
        {
          cov_2ga3zjmfwz().b[5][1]++;
        }
      } else
      /* istanbul ignore next */
      {
        cov_2ga3zjmfwz().b[3][1]++;
      }
    }
  }, [isLoading, isAuthenticated, router, isRedirecting]);

  // Show loading state
  /* istanbul ignore next */
  cov_2ga3zjmfwz().s[19]++;
  if (isLoading) {
    /* istanbul ignore next */
    cov_2ga3zjmfwz().b[6][0]++;
    cov_2ga3zjmfwz().s[20]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "flex items-center justify-center h-screen",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 54,
        columnNumber: 7
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 55,
        columnNumber: 9
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "p",
    /* istanbul ignore next */
    {
      className: "ml-4 text-lg",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 56,
        columnNumber: 9
      }
    }, "Loading your dashboard..."));
  } else
  /* istanbul ignore next */
  {
    cov_2ga3zjmfwz().b[6][1]++;
  }

  // Don't render anything while redirecting
  cov_2ga3zjmfwz().s[21]++;
  if (!isAuthenticated) {
    /* istanbul ignore next */
    cov_2ga3zjmfwz().b[7][0]++;
    cov_2ga3zjmfwz().s[22]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "flex items-center justify-center h-screen",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 64,
        columnNumber: 7
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 65,
        columnNumber: 9
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "p",
    /* istanbul ignore next */
    {
      className: "ml-4 text-lg",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 66,
        columnNumber: 9
      }
    }, "Redirecting to login..."));
  } else
  /* istanbul ignore next */
  {
    cov_2ga3zjmfwz().b[7][1]++;
  }
  cov_2ga3zjmfwz().s[23]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex h-screen",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 72,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(Sidebar,
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 73,
      columnNumber: 7
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "main",
  /* istanbul ignore next */
  {
    className: "main-content",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 74,
      columnNumber: 7
    }
  }, children));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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