{"version": 3, "names": ["cov_1zkraysw0w", "actualCoverage", "createContext", "useState", "useContext", "useCallback", "useAuth", "ClientContext", "s", "client", "isLoading", "getClientByDomain", "f", "getClientById", "getClientByEmail", "updateClientSettings", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "setClient", "setIsLoading", "user", "domain", "b", "response", "fetch", "encodeURIComponent", "ok", "status", "console", "error", "text", "clientData", "json", "id", "settings", "method", "headers", "body", "JSON", "stringify", "updatedClient", "email", "__jsx", "Provider", "value", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "useClient"], "sources": ["ClientContext.tsx"], "sourcesContent": ["import { createContext, useState, useContext, ReactNode, useCallback } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Client, ClientSettings } from '@/types/client';\n\ninterface ClientContextType {\n  client: Client | null;\n  isLoading: boolean;\n  getClientByDomain: (domain: string) => Promise<Client | null>;\n  getClientById: (id: string) => Promise<Client | null>;\n  getClientByEmail: (email: string) => Promise<Client | null>;\n  updateClientSettings: (settings: Partial<ClientSettings>) => Promise<boolean>;\n}\n\n// Create context with default values\nconst ClientContext = createContext<ClientContextType>({\n  client: null,\n  isLoading: false,\n  getClientByDomain: async () => null,\n  getClientById: async () => null,\n  getClientByEmail: async () => null,\n  updateClientSettings: async () => false\n});\n\nexport function ClientProvider({ children }: { children: ReactNode }) {\n  const [client, setClient] = useState<Client | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const { user } = useAuth();\n\n  // Function to get client info from email domain\n  const getClientByDomain = useCallback(async (domain: string): Promise<Client | null> => {\n    if (!domain) return null;\n\n    try {\n      setIsLoading(true);\n      const response = await fetch(`/api/clients/domain?domain=${encodeURIComponent(domain)}`);\n\n      if (!response.ok) {\n        if (response.status !== 404) {\n          console.error('Error fetching client:', await response.text());\n        }\n        return null;\n      }\n\n      const clientData = await response.json();\n      setClient(clientData);\n      return clientData;\n    } catch (error) {\n      console.error('Error getting client info:', error);\n      return null;\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  // Function to get client by ID\n  const getClientById = useCallback(async (id: string): Promise<Client | null> => {\n    if (!id) return null;\n\n    try {\n      setIsLoading(true);\n      const response = await fetch(`/api/clients/${encodeURIComponent(id)}`);\n\n      if (!response.ok) {\n        if (response.status !== 404) {\n          console.error('Error fetching client:', await response.text());\n        }\n        return null;\n      }\n\n      const clientData = await response.json();\n      setClient(clientData);\n      return clientData;\n    } catch (error) {\n      console.error('Error getting client info:', error);\n      return null;\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  // Function to update client settings\n  const updateClientSettings = useCallback(async (settings: Partial<ClientSettings>): Promise<boolean> => {\n    if (!client?.id) return false;\n\n    try {\n      setIsLoading(true);\n      const response = await fetch(`/api/clients/${encodeURIComponent(client.id)}/settings`, {\n        method: 'PATCH',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(settings)\n      });\n\n      if (!response.ok) {\n        console.error('Error updating client settings:', await response.text());\n        return false;\n      }\n\n      const updatedClient = await response.json();\n      setClient(updatedClient);\n      return true;\n    } catch (error) {\n      console.error('Error updating client settings:', error);\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  }, [client?.id]);\n\n  // Function to get client by email\n  const getClientByEmail = useCallback(async (email: string): Promise<Client | null> => {\n    if (!email) return null;\n\n    try {\n      setIsLoading(true);\n      const response = await fetch(`/api/clients/domain?email=${encodeURIComponent(email)}`);\n\n      if (!response.ok) {\n        if (response.status !== 404) {\n          console.error('Error fetching client:', await response.text());\n        }\n        return null;\n      }\n\n      const clientData = await response.json();\n      setClient(clientData);\n      return clientData;\n    } catch (error) {\n      console.error('Error getting client info by email:', error);\n      return null;\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  return (\n    <ClientContext.Provider\n      value={{\n        client,\n        isLoading,\n        getClientByDomain,\n        getClientById,\n        getClientByEmail,\n        updateClientSettings\n      }}\n    >\n      {children}\n    </ClientContext.Provider>\n  );\n}\n\n// Custom hook for using client context\nexport const useClient = () => useContext(ClientContext);\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ,SAASE,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAaC,WAAW,QAAQ,OAAO;AACnF,SAASC,OAAO,QAAQ,wBAAwB;AAYhD;AACA,MAAMC,aAAa;AAAA;AAAA,CAAAP,cAAA,GAAAQ,CAAA,oBAAGN,aAAa,CAAoB;EACrDO,MAAM,EAAE,IAAI;EACZC,SAAS,EAAE,KAAK;EAChBC,iBAAiB,EAAE,MAAAA,CAAA,KAAY;IAAA;IAAAX,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAQ,CAAA;IAAA,WAAI;EAAD,CAAC;EACnCK,aAAa,EAAE,MAAAA,CAAA,KAAY;IAAA;IAAAb,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAQ,CAAA;IAAA,WAAI;EAAD,CAAC;EAC/BM,gBAAgB,EAAE,MAAAA,CAAA,KAAY;IAAA;IAAAd,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAQ,CAAA;IAAA,WAAI;EAAD,CAAC;EAClCO,oBAAoB,EAAE,MAAAA,CAAA,KAAY;IAAA;IAAAf,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAQ,CAAA;IAAA,YAAK;EAAD;AACxC,CAAC,CAAC;AAEF,OAAO,SAASQ,cAAcA,CAAC;EAAEC;AAAkC,CAAC,EAAE;EAAA;EAAAjB,cAAA,GAAAY,CAAA;EACpE,MAAM,CAACH,MAAM,EAAES,SAAS,CAAC;EAAA;EAAA,CAAAlB,cAAA,GAAAQ,CAAA,OAAGL,QAAQ,CAAgB,IAAI,CAAC;EACzD,MAAM,CAACO,SAAS,EAAES,YAAY,CAAC;EAAA;EAAA,CAAAnB,cAAA,GAAAQ,CAAA,OAAGL,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM;IAAEiB;EAAK,CAAC;EAAA;EAAA,CAAApB,cAAA,GAAAQ,CAAA,OAAGF,OAAO,CAAC,CAAC;;EAE1B;EACA,MAAMK,iBAAiB;EAAA;EAAA,CAAAX,cAAA,GAAAQ,CAAA,OAAGH,WAAW,CAAC,MAAOgB,MAAc,IAA6B;IAAA;IAAArB,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAQ,CAAA;IACtF,IAAI,CAACa,MAAM,EAAE;MAAA;MAAArB,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAQ,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;IAAA;IAAA;MAAAR,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAQ,CAAA;IAEzB,IAAI;MAAA;MAAAR,cAAA,GAAAQ,CAAA;MACFW,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMI,QAAQ;MAAA;MAAA,CAAAvB,cAAA,GAAAQ,CAAA,QAAG,MAAMgB,KAAK,CAAC,8BAA8BC,kBAAkB,CAACJ,MAAM,CAAC,EAAE,CAAC;MAAC;MAAArB,cAAA,GAAAQ,CAAA;MAEzF,IAAI,CAACe,QAAQ,CAACG,EAAE,EAAE;QAAA;QAAA1B,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAQ,CAAA;QAChB,IAAIe,QAAQ,CAACI,MAAM,KAAK,GAAG,EAAE;UAAA;UAAA3B,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAQ,CAAA;UAC3BoB,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAE,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC,CAAC;QAChE,CAAC;QAAA;QAAA;UAAA9B,cAAA,GAAAsB,CAAA;QAAA;QAAAtB,cAAA,GAAAQ,CAAA;QACD,OAAO,IAAI;MACb,CAAC;MAAA;MAAA;QAAAR,cAAA,GAAAsB,CAAA;MAAA;MAED,MAAMS,UAAU;MAAA;MAAA,CAAA/B,cAAA,GAAAQ,CAAA,QAAG,MAAMe,QAAQ,CAACS,IAAI,CAAC,CAAC;MAAC;MAAAhC,cAAA,GAAAQ,CAAA;MACzCU,SAAS,CAACa,UAAU,CAAC;MAAC;MAAA/B,cAAA,GAAAQ,CAAA;MACtB,OAAOuB,UAAU;IACnB,CAAC,CAAC,OAAOF,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAQ,CAAA;MACdoB,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAAC;MAAA7B,cAAA,GAAAQ,CAAA;MACnD,OAAO,IAAI;IACb,CAAC,SAAS;MAAA;MAAAR,cAAA,GAAAQ,CAAA;MACRW,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMN,aAAa;EAAA;EAAA,CAAAb,cAAA,GAAAQ,CAAA,QAAGH,WAAW,CAAC,MAAO4B,EAAU,IAA6B;IAAA;IAAAjC,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAQ,CAAA;IAC9E,IAAI,CAACyB,EAAE,EAAE;MAAA;MAAAjC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAQ,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;IAAA;IAAA;MAAAR,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAQ,CAAA;IAErB,IAAI;MAAA;MAAAR,cAAA,GAAAQ,CAAA;MACFW,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMI,QAAQ;MAAA;MAAA,CAAAvB,cAAA,GAAAQ,CAAA,QAAG,MAAMgB,KAAK,CAAC,gBAAgBC,kBAAkB,CAACQ,EAAE,CAAC,EAAE,CAAC;MAAC;MAAAjC,cAAA,GAAAQ,CAAA;MAEvE,IAAI,CAACe,QAAQ,CAACG,EAAE,EAAE;QAAA;QAAA1B,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAQ,CAAA;QAChB,IAAIe,QAAQ,CAACI,MAAM,KAAK,GAAG,EAAE;UAAA;UAAA3B,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAQ,CAAA;UAC3BoB,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAE,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC,CAAC;QAChE,CAAC;QAAA;QAAA;UAAA9B,cAAA,GAAAsB,CAAA;QAAA;QAAAtB,cAAA,GAAAQ,CAAA;QACD,OAAO,IAAI;MACb,CAAC;MAAA;MAAA;QAAAR,cAAA,GAAAsB,CAAA;MAAA;MAED,MAAMS,UAAU;MAAA;MAAA,CAAA/B,cAAA,GAAAQ,CAAA,QAAG,MAAMe,QAAQ,CAACS,IAAI,CAAC,CAAC;MAAC;MAAAhC,cAAA,GAAAQ,CAAA;MACzCU,SAAS,CAACa,UAAU,CAAC;MAAC;MAAA/B,cAAA,GAAAQ,CAAA;MACtB,OAAOuB,UAAU;IACnB,CAAC,CAAC,OAAOF,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAQ,CAAA;MACdoB,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAAC;MAAA7B,cAAA,GAAAQ,CAAA;MACnD,OAAO,IAAI;IACb,CAAC,SAAS;MAAA;MAAAR,cAAA,GAAAQ,CAAA;MACRW,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMJ,oBAAoB;EAAA;EAAA,CAAAf,cAAA,GAAAQ,CAAA,QAAGH,WAAW,CAAC,MAAO6B,QAAiC,IAAuB;IAAA;IAAAlC,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAQ,CAAA;IACtG,IAAI,CAACC,MAAM,EAAEwB,EAAE,EAAE;MAAA;MAAAjC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAQ,CAAA;MAAA,OAAO,KAAK;IAAA,CAAC;IAAA;IAAA;MAAAR,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAQ,CAAA;IAE9B,IAAI;MAAA;MAAAR,cAAA,GAAAQ,CAAA;MACFW,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMI,QAAQ;MAAA;MAAA,CAAAvB,cAAA,GAAAQ,CAAA,QAAG,MAAMgB,KAAK,CAAC,gBAAgBC,kBAAkB,CAAChB,MAAM,CAACwB,EAAE,CAAC,WAAW,EAAE;QACrFE,MAAM,EAAE,OAAO;QACfC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACL,QAAQ;MAC/B,CAAC,CAAC;MAAC;MAAAlC,cAAA,GAAAQ,CAAA;MAEH,IAAI,CAACe,QAAQ,CAACG,EAAE,EAAE;QAAA;QAAA1B,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAQ,CAAA;QAChBoB,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAE,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC,CAAC;QAAC;QAAA9B,cAAA,GAAAQ,CAAA;QACxE,OAAO,KAAK;MACd,CAAC;MAAA;MAAA;QAAAR,cAAA,GAAAsB,CAAA;MAAA;MAED,MAAMkB,aAAa;MAAA;MAAA,CAAAxC,cAAA,GAAAQ,CAAA,QAAG,MAAMe,QAAQ,CAACS,IAAI,CAAC,CAAC;MAAC;MAAAhC,cAAA,GAAAQ,CAAA;MAC5CU,SAAS,CAACsB,aAAa,CAAC;MAAC;MAAAxC,cAAA,GAAAQ,CAAA;MACzB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOqB,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAQ,CAAA;MACdoB,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MAAC;MAAA7B,cAAA,GAAAQ,CAAA;MACxD,OAAO,KAAK;IACd,CAAC,SAAS;MAAA;MAAAR,cAAA,GAAAQ,CAAA;MACRW,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACV,MAAM,EAAEwB,EAAE,CAAC,CAAC;;EAEhB;EACA,MAAMnB,gBAAgB;EAAA;EAAA,CAAAd,cAAA,GAAAQ,CAAA,QAAGH,WAAW,CAAC,MAAOoC,KAAa,IAA6B;IAAA;IAAAzC,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAQ,CAAA;IACpF,IAAI,CAACiC,KAAK,EAAE;MAAA;MAAAzC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAQ,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;IAAA;IAAA;MAAAR,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAQ,CAAA;IAExB,IAAI;MAAA;MAAAR,cAAA,GAAAQ,CAAA;MACFW,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMI,QAAQ;MAAA;MAAA,CAAAvB,cAAA,GAAAQ,CAAA,QAAG,MAAMgB,KAAK,CAAC,6BAA6BC,kBAAkB,CAACgB,KAAK,CAAC,EAAE,CAAC;MAAC;MAAAzC,cAAA,GAAAQ,CAAA;MAEvF,IAAI,CAACe,QAAQ,CAACG,EAAE,EAAE;QAAA;QAAA1B,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAQ,CAAA;QAChB,IAAIe,QAAQ,CAACI,MAAM,KAAK,GAAG,EAAE;UAAA;UAAA3B,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAQ,CAAA;UAC3BoB,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAE,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC,CAAC;QAChE,CAAC;QAAA;QAAA;UAAA9B,cAAA,GAAAsB,CAAA;QAAA;QAAAtB,cAAA,GAAAQ,CAAA;QACD,OAAO,IAAI;MACb,CAAC;MAAA;MAAA;QAAAR,cAAA,GAAAsB,CAAA;MAAA;MAED,MAAMS,UAAU;MAAA;MAAA,CAAA/B,cAAA,GAAAQ,CAAA,QAAG,MAAMe,QAAQ,CAACS,IAAI,CAAC,CAAC;MAAC;MAAAhC,cAAA,GAAAQ,CAAA;MACzCU,SAAS,CAACa,UAAU,CAAC;MAAC;MAAA/B,cAAA,GAAAQ,CAAA;MACtB,OAAOuB,UAAU;IACnB,CAAC,CAAC,OAAOF,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAQ,CAAA;MACdoB,OAAO,CAACC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAAC;MAAA7B,cAAA,GAAAQ,CAAA;MAC5D,OAAO,IAAI;IACb,CAAC,SAAS;MAAA;MAAAR,cAAA,GAAAQ,CAAA;MACRW,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;EAAC;EAAAnB,cAAA,GAAAQ,CAAA;EAEP,OACE,0BAAAkC,KAAA;EAAA;EAACnC,aAAa,CAACoC,QAAQ;EAAA;EAAA;IACrBC,KAAK,EAAE;MACLnC,MAAM;MACNC,SAAS;MACTC,iBAAiB;MACjBE,aAAa;MACbC,gBAAgB;MAChBC;IACF,CAAE;IAAA8B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEDjC,QACqB,CAAC;AAE7B;;AAEA;AAAA;AAAAjB,cAAA,GAAAQ,CAAA;AACA,OAAO,MAAM2C,SAAS,GAAGA,CAAA,KAAM;EAAA;EAAAnD,cAAA,GAAAY,CAAA;EAAAZ,cAAA,GAAAQ,CAAA;EAAA,OAAAJ,UAAU,CAACG,aAAa,CAAC;AAAD,CAAC", "ignoreList": []}