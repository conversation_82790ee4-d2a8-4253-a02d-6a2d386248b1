/**
 * Database Service - Connection Pool Manager
 * 
 * Eliminates redundant database connections by providing a centralized
 * connection pool with proper lifecycle management and monitoring.
 */

import { Pool, PoolClient, PoolConfig } from 'pg'
import { databaseConfig, appConfig } from '@/lib/config/app-config'

class DatabaseService {
  private static instance: DatabaseService
  private pool: Pool | null = null
  private isInitialized = false
  private connectionCount = 0

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService()
    }
    return DatabaseService.instance
  }

  /**
   * Initialize database connection pool
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized && this.pool) {
      return
    }

    try {
      const poolConfig: PoolConfig = {
        user: databaseConfig.user,
        host: databaseConfig.host,
        database: databaseConfig.database,
        password: databaseConfig.password,
        port: databaseConfig.port,
        ssl: databaseConfig.ssl,
        max: databaseConfig.max,
        min: databaseConfig.min,
        idleTimeoutMillis: databaseConfig.idleTimeoutMillis,
        connectionTimeoutMillis: databaseConfig.connectionTimeoutMillis
      }

      this.pool = new Pool(poolConfig)

      // Set up event listeners for monitoring
      this.pool.on('connect', (client) => {
        this.connectionCount++
        if (appConfig.isDevelopment) {
          console.log(`New client connected to database (total: ${this.connectionCount})`)
        }
      })

      this.pool.on('remove', (client) => {
        this.connectionCount--
        if (appConfig.isDevelopment) {
          console.log(`Client removed from pool (remaining: ${this.connectionCount})`)
        }
      })

      this.pool.on('error', (err) => {
        console.error('Database pool error:', err)
      })

      // Test the connection
      const client = await this.pool.connect()
      await client.query('SELECT 1')
      client.release()

      this.isInitialized = true

      if (appConfig.isDevelopment) {
        console.log('Database pool initialized successfully:', {
          user: databaseConfig.user,
          host: databaseConfig.host,
          database: databaseConfig.database,
          ssl: databaseConfig.ssl,
          maxConnections: databaseConfig.max
        })
      }

    } catch (error) {
      console.error('Failed to initialize database pool:', error)
      throw new Error('Database initialization failed')
    }
  }

  /**
   * Get a database client from the pool
   */
  public async getClient(): Promise<PoolClient> {
    if (!this.pool) {
      await this.initialize()
    }

    if (!this.pool) {
      throw new Error('Database pool not initialized')
    }

    return this.pool.connect()
  }

  /**
   * Execute a query with automatic client management
   */
  public async query(text: string, params?: any[]): Promise<any> {
    const client = await this.getClient()
    
    try {
      const result = await client.query(text, params)
      return result
    } finally {
      client.release()
    }
  }

  /**
   * Execute a transaction with automatic rollback on error
   */
  public async transaction<T>(
    callback: (client: PoolClient) => Promise<T>
  ): Promise<T> {
    const client = await this.getClient()
    
    try {
      await client.query('BEGIN')
      const result = await callback(client)
      await client.query('COMMIT')
      return result
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  /**
   * Get pool statistics
   */
  public getStats() {
    if (!this.pool) {
      return {
        totalCount: 0,
        idleCount: 0,
        waitingCount: 0,
        connectionCount: this.connectionCount
      }
    }

    return {
      totalCount: this.pool.totalCount,
      idleCount: this.pool.idleCount,
      waitingCount: this.pool.waitingCount,
      connectionCount: this.connectionCount
    }
  }

  /**
   * Check if database is healthy
   */
  public async healthCheck(): Promise<{ healthy: boolean; error?: string }> {
    try {
      if (!this.pool) {
        return { healthy: false, error: 'Pool not initialized' }
      }

      const client = await this.pool.connect()
      try {
        await client.query('SELECT 1')
        return { healthy: true }
      } finally {
        client.release()
      }
    } catch (error) {
      return { 
        healthy: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  /**
   * Gracefully close the database pool
   */
  public async close(): Promise<void> {
    if (this.pool) {
      await this.pool.end()
      this.pool = null
      this.isInitialized = false
      this.connectionCount = 0
      
      if (appConfig.isDevelopment) {
        console.log('Database pool closed')
      }
    }
  }

  /**
   * Check if database is initialized
   */
  public isReady(): boolean {
    return this.isInitialized && this.pool !== null
  }
}

// Export singleton instance
export const databaseService = DatabaseService.getInstance()

// Convenience functions
export async function getDbClient(): Promise<PoolClient> {
  return databaseService.getClient()
}

export async function dbQuery(text: string, params?: any[]): Promise<any> {
  return databaseService.query(text, params)
}

export async function dbTransaction<T>(
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  return databaseService.transaction(callback)
}

// Export the class for testing
export { DatabaseService }
