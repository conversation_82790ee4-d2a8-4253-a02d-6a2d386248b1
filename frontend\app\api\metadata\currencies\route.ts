/**
 * Currencies API Endpoint
 * 
 * Provides access to currency metadata
 * GET /api/metadata/currencies - Returns active currencies
 */

import { NextRequest } from 'next/server';
import { requireAuth } from '@/lib/auth-middleware';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  ApiErrorCode, 
  HttpStatus,
  withErrorHandling 
} from '@/lib/api-response';
import { executeQuery } from '@/lib/database';
import { Currency } from '../route';

// GET /api/metadata/currencies - Get active currencies
export const GET = withErrorHandling(async (request: NextRequest) => {
  // Verify authentication
  const authResult = await requireAuth();
  if (!authResult.success) {
    return authResult.response!;
  }

  try {
    // Query currencies from metadata schema
    const result = await executeQuery<Currency>(
      `SELECT 
        CurrencyID,
        CurrencyName,
        CurrencySymbol,
        Active,
        DisplayOrder
      FROM metadata.Currencies 
      WHERE Active = true 
      ORDER BY DisplayOrder ASC, CurrencyName ASC`,
      []
    );

    if (!result.success) {
      console.error('Failed to fetch currencies:', result.error);
      return createErrorResponse(
        'Failed to fetch currencies',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    // Log successful fetch
    console.log(`Currencies fetched successfully: ${result.data?.length || 0} records`);

    return createSuccessResponse(
      result.data || [],
      'Currencies retrieved successfully'
    );

  } catch (error) {
    console.error('Error fetching currencies:', error);
    return createErrorResponse(
      'Failed to fetch currencies',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});
