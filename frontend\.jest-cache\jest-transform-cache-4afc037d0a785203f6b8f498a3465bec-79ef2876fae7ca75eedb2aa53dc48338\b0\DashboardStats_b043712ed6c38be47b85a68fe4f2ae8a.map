{"version": 3, "names": ["_jsxFileName", "__jsx", "React", "createElement", "cov_1cpmqwip5t", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "_coverageSchema", "coverage", "actualCoverage", "memo", "usePerformanceMonitor", "StatCard", "icon", "title", "value", "isLoading", "className", "__self", "__source", "fileName", "lineNumber", "columnNumber", "DashboardStats", "stats", "testId", "statsConfig", "useMemo", "totalRenewals", "key", "renewalsDue", "vendors", "annualSpend", "role", "map", "stat"], "sources": ["DashboardStats.tsx"], "sourcesContent": ["/**\n * Dashboard Statistics Component\n * \n * Displays key metrics in a grid layout with proper loading and error states.\n * Focused responsibility: Rendering statistics cards only.\n */\n\n'use client'\n\nimport React, { memo } from 'react'\nimport { DashboardStats as DashboardStatsType } from '@/lib/types'\nimport { BaseComponentProps } from '@/lib/types'\nimport { usePerformanceMonitor } from '@/lib/performance'\n\ninterface DashboardStatsProps extends BaseComponentProps {\n  stats: DashboardStatsType\n  isLoading?: boolean\n}\n\ninterface StatCardProps {\n  icon: string\n  title: string\n  value: string | number\n  isLoading?: boolean\n}\n\nconst StatCard = memo(function StatCard({ icon, title, value, isLoading }: StatCardProps) {\n  if (isLoading) {\n    return (\n      <div className=\"stat-card\">\n        <div className=\"stat-icon animate-pulse\">⏳</div>\n        <h3>{title}</h3>\n        <div className=\"stat-value\">\n          <div className=\"animate-pulse bg-gray-200 h-6 w-16 rounded\"></div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"stat-card\">\n      <div className=\"stat-icon\">{icon}</div>\n      <h3>{title}</h3>\n      <p className=\"stat-value\">{value}</p>\n    </div>\n  )\n})\n\nconst DashboardStats = memo(function DashboardStats({\n  stats,\n  isLoading = false,\n  className = '',\n  'data-testid': testId\n}: DashboardStatsProps) {\n  // Performance monitoring in development\n  usePerformanceMonitor('DashboardStats')\n\n  // Memoize stats configuration to prevent recreation on every render\n  const statsConfig = React.useMemo(() => [\n    {\n      icon: '📊',\n      title: 'Total Renewals',\n      value: stats.totalRenewals,\n      key: 'totalRenewals'\n    },\n    {\n      icon: '⚠️',\n      title: 'Renewals Due',\n      value: stats.renewalsDue,\n      key: 'renewalsDue'\n    },\n    {\n      icon: '🏢',\n      title: 'Vendors',\n      value: stats.vendors,\n      key: 'vendors'\n    },\n    {\n      icon: '💰',\n      title: 'Annual Spend',\n      value: stats.annualSpend,\n      key: 'annualSpend'\n    }\n  ], [stats.totalRenewals, stats.renewalsDue, stats.vendors, stats.annualSpend])\n\n  return (\n    <div\n      className={`stats-grid ${className}`}\n      data-testid={testId}\n      role=\"region\"\n      aria-label=\"Dashboard Statistics\"\n    >\n      {statsConfig.map((stat) => (\n        <StatCard\n          key={stat.key}\n          icon={stat.icon}\n          title={stat.title}\n          value={stat.value}\n          isLoading={isLoading}\n        />\n      ))}\n    </div>\n  )\n})\n\nexport default DashboardStats\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAAA;AAAA,IAAAA,YAAA;AAAA,IAAAC,KAAA,GAAAC,KAAA,CAAAC,aAAA;AAAA,SAAAC,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,eAAA;IAAArB,IAAA;EAAA;EAAA,IAAAsB,QAAA,GAAArB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAmB,QAAA,CAAAvB,IAAA,KAAAuB,QAAA,CAAAvB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAsB,QAAA,CAAAvB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAmB,cAAA,GAAAD,QAAA,CAAAvB,IAAA;EAAA;IAQA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAyB,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAzB,cAAA;AANZ,OAAOF,KAAK,IAAI4B,IAAI,QAAQ,OAAO;AAGnC,SAASC,qBAAqB,QAAQ,mBAAmB;AAczD,MAAMC,QAAQ;AAAA;AAAA,CAAA5B,cAAA,GAAAoB,CAAA,oBAAGM,IAAI,CAAC,SAASE,QAAQA,CAAC;EAAEC,IAAI;EAAEC,KAAK;EAAEC,KAAK;EAAEC;AAAyB,CAAC,EAAE;EAAA;EAAAhC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACxF,IAAIY,SAAS,EAAE;IAAA;IAAAhC,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACb,OACE,0BAAAvB,KAAA;IAAA;IAAA;IAAA;IAAA;MAAKoC,SAAS,EAAC,WAAW;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAxC,YAAA;QAAAyC,UAAA;QAAAC,YAAA;MAAA;IAAA;IACxB;IAAAzC,KAAA;IAAA;IAAA;IAAA;IAAA;MAAKoC,SAAS,EAAC,yBAAyB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAxC,YAAA;QAAAyC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,QAAM,CAAC;IAChD;IAAAzC,KAAA;IAAA;IAAA;IAAA;IAAA;MAAAqC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAxC,YAAA;QAAAyC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAKR,KAAU,CAAC;IAChB;IAAAjC,KAAA;IAAA;IAAA;IAAA;IAAA;MAAKoC,SAAS,EAAC,YAAY;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAxC,YAAA;QAAAyC,UAAA;QAAAC,YAAA;MAAA;IAAA;IACzB;IAAAzC,KAAA;IAAA;IAAA;IAAA;IAAA;MAAKoC,SAAS,EAAC,4CAA4C;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAxC,YAAA;QAAAyC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAM,CAC9D,CACF,CAAC;EAEV,CAAC;EAAA;EAAA;IAAAtC,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EAED,OACE,0BAAAvB,KAAA;EAAA;EAAA;EAAA;EAAA;IAAKoC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAxC,YAAA;MAAAyC,UAAA;MAAAC,YAAA;IAAA;EAAA;EACxB;EAAAzC,KAAA;EAAA;EAAA;EAAA;EAAA;IAAKoC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAxC,YAAA;MAAAyC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAET,IAAU,CAAC;EACvC;EAAAhC,KAAA;EAAA;EAAA;EAAA;EAAA;IAAAqC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAxC,YAAA;MAAAyC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKR,KAAU,CAAC;EAChB;EAAAjC,KAAA;EAAA;EAAA;EAAA;EAAA;IAAGoC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAxC,YAAA;MAAAyC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEP,KAAS,CACjC,CAAC;AAEV,CAAC,CAAC;AAEF,MAAMQ,cAAc;AAAA;AAAA,CAAAvC,cAAA,GAAAoB,CAAA,oBAAGM,IAAI,CAAC,SAASa,cAAcA,CAAC;EAClDC,KAAK;EACLR,SAAS;EAAA;EAAA,CAAAhC,cAAA,GAAAsB,CAAA,UAAG,KAAK;EACjBW,SAAS;EAAA;EAAA,CAAAjC,cAAA,GAAAsB,CAAA,UAAG,EAAE;EACd,aAAa,EAAEmB;AACI,CAAC,EAAE;EAAA;EAAAzC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACtB;EACAO,qBAAqB,CAAC,gBAAgB,CAAC;;EAEvC;EACA,MAAMe,WAAW;EAAA;EAAA,CAAA1C,cAAA,GAAAoB,CAAA,OAAGtB,KAAK,CAAC6C,OAAO,CAAC,MAAM;IAAA;IAAA3C,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAAA,QACtC;MACES,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,gBAAgB;MACvBC,KAAK,EAAES,KAAK,CAACI,aAAa;MAC1BC,GAAG,EAAE;IACP,CAAC,EACD;MACEhB,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAES,KAAK,CAACM,WAAW;MACxBD,GAAG,EAAE;IACP,CAAC,EACD;MACEhB,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,SAAS;MAChBC,KAAK,EAAES,KAAK,CAACO,OAAO;MACpBF,GAAG,EAAE;IACP,CAAC,EACD;MACEhB,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAES,KAAK,CAACQ,WAAW;MACxBH,GAAG,EAAE;IACP,CAAC,CACF;EAAD,CAAC,EAAE,CAACL,KAAK,CAACI,aAAa,EAAEJ,KAAK,CAACM,WAAW,EAAEN,KAAK,CAACO,OAAO,EAAEP,KAAK,CAACQ,WAAW,CAAC,CAAC;EAAA;EAAAhD,cAAA,GAAAoB,CAAA;EAE9E,OACE,0BAAAvB,KAAA;EAAA;EAAA;EAAA;EAAA;IACEoC,SAAS,EAAE,cAAcA,SAAS,EAAG;IACrC;IAAA,eAAaQ,MAAO;IACpBQ,IAAI,EAAC,QAAQ;IACb;IAAA,cAAW,sBAAsB;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAxC,YAAA;MAAAyC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEhCI,WAAW,CAACQ,GAAG,CAAEC,IAAI,IACpB;IAAA;IAAAnD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAAA,iCAAAvB,KAAA,CAAC+B,QAAQ;IAAA;IAAA;MACPiB,GAAG,EAAEM,IAAI,CAACN,GAAI;MACdhB,IAAI,EAAEsB,IAAI,CAACtB,IAAK;MAChBC,KAAK,EAAEqB,IAAI,CAACrB,KAAM;MAClBC,KAAK,EAAEoB,IAAI,CAACpB,KAAM;MAClBC,SAAS,EAAEA,SAAU;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAxC,YAAA;QAAAyC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACtB,CAAC;EAAD,CACF,CACE,CAAC;AAEV,CAAC,CAAC;AAEF,eAAeC,cAAc", "ignoreList": []}