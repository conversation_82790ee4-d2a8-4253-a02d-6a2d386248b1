{"version": 3, "names": ["_jsxFileName", "React", "__jsx", "createElement", "cov_1qw5a7plbo", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "_coverageSchema", "coverage", "actualCoverage", "useEffect", "useState", "EnvDebugger", "clientEnvVars", "setClientEnvVars", "serverEnvVars", "NEXT_PUBLIC_AWS_REGION", "process", "env", "NEXT_PUBLIC_AWS_USER_POOLS_ID", "NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID", "NEXT_PUBLIC_AWS_COGNITO_DOMAIN", "vars", "Object", "keys", "for<PERSON>ach", "key", "startsWith", "className", "__self", "__source", "fileName", "lineNumber", "columnNumber", "JSON", "stringify"], "sources": ["EnvDebugger.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\nexport default function EnvDebugger() {\n  const [clientEnvVars, setClientEnvVars] = useState<Record<string, string | undefined>>({})\n  \n  // These are server-side environment variables (available during build/SSR)\n  const serverEnvVars = {\n    NEXT_PUBLIC_AWS_REGION: process.env.NEXT_PUBLIC_AWS_REGION,\n    NEXT_PUBLIC_AWS_USER_POOLS_ID: process.env.NEXT_PUBLIC_AWS_USER_POOLS_ID,\n    NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID: process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID,\n    NEXT_PUBLIC_AWS_COGNITO_DOMAIN: process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN,\n  }\n  \n  useEffect(() => {\n    // These are client-side environment variables (available in browser)\n    const vars: Record<string, string | undefined> = {}\n    Object.keys(process.env).forEach(key => {\n      if (key.startsWith('NEXT_PUBLIC_')) {\n        vars[key] = process.env[key]\n      }\n    })\n    setClientEnvVars(vars)\n  }, [])\n  \n  return (\n    <div className=\"p-4 bg-gray-100 rounded-lg my-4 max-w-2xl mx-auto text-sm\">\n      <h3 className=\"font-bold mb-2\">Environment Variables Debug</h3>\n      \n      <h4 className=\"font-semibold mt-2\">Server-side Environment Variables:</h4>\n      <pre className=\"bg-white p-3 rounded overflow-auto max-h-60\">\n        {JSON.stringify(serverEnvVars, null, 2)}\n      </pre>\n      \n      <h4 className=\"font-semibold mt-4\">Client-side Environment Variables:</h4>\n      <pre className=\"bg-white p-3 rounded overflow-auto max-h-60\">\n        {JSON.stringify(clientEnvVars, null, 2)}\n      </pre>\n    </div>\n  )\n}\n"], "mappings": "AAAA,YAAY;;AAAA;AAAA,IAAAA,YAAA;AAAA,OAAAC,KAAA;AAAA,IAAAC,KAAA,GAAAD,KAAA,CAAAE,aAAA;AAAA,SAAAC,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;IAAA;IAAAC,eAAA;IAAArB,IAAA;EAAA;EAAA,IAAAsB,QAAA,GAAArB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAmB,QAAA,CAAAvB,IAAA,KAAAuB,QAAA,CAAAvB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAsB,QAAA,CAAAvB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAmB,cAAA,GAAAD,QAAA,CAAAvB,IAAA;EAAA;IAeA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAyB,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAzB,cAAA;AAbZ,SAAS0B,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE3C,eAAe,SAASC,WAAWA,CAAA,EAAG;EAAA;EAAA5B,cAAA,GAAAqB,CAAA;EACpC,MAAM,CAACQ,aAAa,EAAEC,gBAAgB,CAAC;EAAA;EAAA,CAAA9B,cAAA,GAAAoB,CAAA,OAAGO,QAAQ,CAAqC,CAAC,CAAC,CAAC;;EAE1F;EACA,MAAMI,aAAa;EAAA;EAAA,CAAA/B,cAAA,GAAAoB,CAAA,OAAG;IACpBY,sBAAsB,EAAEC,OAAO,CAACC,GAAG,CAACF,sBAAsB;IAC1DG,6BAA6B,EAAEF,OAAO,CAACC,GAAG,CAACC,6BAA6B;IACxEC,wCAAwC,EAAEH,OAAO,CAACC,GAAG,CAACE,wCAAwC;IAC9FC,8BAA8B,EAAEJ,OAAO,CAACC,GAAG,CAACG;EAC9C,CAAC;EAAA;EAAArC,cAAA,GAAAoB,CAAA;EAEDM,SAAS,CAAC,MAAM;IAAA;IAAA1B,cAAA,GAAAqB,CAAA;IACd;IACA,MAAMiB,IAAwC;IAAA;IAAA,CAAAtC,cAAA,GAAAoB,CAAA,OAAG,CAAC,CAAC;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IACnDmB,MAAM,CAACC,IAAI,CAACP,OAAO,CAACC,GAAG,CAAC,CAACO,OAAO,CAACC,GAAG,IAAI;MAAA;MAAA1C,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MACtC,IAAIsB,GAAG,CAACC,UAAU,CAAC,cAAc,CAAC,EAAE;QAAA;QAAA3C,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAClCkB,IAAI,CAACI,GAAG,CAAC,GAAGT,OAAO,CAACC,GAAG,CAACQ,GAAG,CAAC;MAC9B,CAAC;MAAA;MAAA;QAAA1C,cAAA,GAAAsB,CAAA;MAAA;IACH,CAAC,CAAC;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACFU,gBAAgB,CAACQ,IAAI,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAAA;EAAAtC,cAAA,GAAAoB,CAAA;EAEN,OACE,0BAAAtB,KAAA;EAAA;EAAA;EAAA;EAAA;IAAK8C,SAAS,EAAC,2DAA2D;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAnD,YAAA;MAAAoD,UAAA;MAAAC,YAAA;IAAA;EAAA;EACxE;EAAAnD,KAAA;EAAA;EAAA;EAAA;EAAA;IAAI8C,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAnD,YAAA;MAAAoD,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,6BAA+B,CAAC;EAE/D;EAAAnD,KAAA;EAAA;EAAA;EAAA;EAAA;IAAI8C,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAnD,YAAA;MAAAoD,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oCAAsC,CAAC;EAC1E;EAAAnD,KAAA;EAAA;EAAA;EAAA;EAAA;IAAK8C,SAAS,EAAC,6CAA6C;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAnD,YAAA;MAAAoD,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzDC,IAAI,CAACC,SAAS,CAACpB,aAAa,EAAE,IAAI,EAAE,CAAC,CACnC,CAAC;EAEN;EAAAjC,KAAA;EAAA;EAAA;EAAA;EAAA;IAAI8C,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAnD,YAAA;MAAAoD,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oCAAsC,CAAC;EAC1E;EAAAnD,KAAA;EAAA;EAAA;EAAA;EAAA;IAAK8C,SAAS,EAAC,6CAA6C;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAnD,YAAA;MAAAoD,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzDC,IAAI,CAACC,SAAS,CAACtB,aAAa,EAAE,IAAI,EAAE,CAAC,CACnC,CACF,CAAC;AAEV", "ignoreList": []}