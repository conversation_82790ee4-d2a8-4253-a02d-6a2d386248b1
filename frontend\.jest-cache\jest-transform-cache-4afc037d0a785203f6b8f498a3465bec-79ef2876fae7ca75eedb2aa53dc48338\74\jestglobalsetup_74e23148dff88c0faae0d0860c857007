7ebfb8acdf54093ba7a1af47eb761986
"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
/**
 * Jest Global Setup
 * 
 * Runs once before all tests start.
 * Used for global test environment setup.
 */
var _default = async () => {
  console.log('🧪 Setting up test environment...');

  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.TZ = 'UTC';

  // Mock AWS configuration
  process.env.AWS_REGION = 'ca-central-1';
  process.env.AWS_ACCESS_KEY_ID = 'test-access-key';
  process.env.AWS_SECRET_ACCESS_KEY = 'test-secret-key';

  // Mock database configuration
  process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_renewtrack';
  process.env.DATABASE_HOST = 'localhost';
  process.env.DATABASE_PORT = '5432';
  process.env.DATABASE_NAME = 'test_renewtrack';
  process.env.DATABASE_USER = 'test';
  process.env.DATABASE_PASSWORD = 'test';

  // Mock JWT configuration
  process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
  process.env.JWT_EXPIRES_IN = '1h';

  // Mock Cognito configuration
  process.env.NEXT_PUBLIC_USER_POOL_ID = 'ca-central-1_testpool';
  process.env.NEXT_PUBLIC_USER_POOL_CLIENT_ID = 'test-client-id';
  process.env.NEXT_PUBLIC_COGNITO_DOMAIN = 'test.auth.renewtrack.com';

  // Mock API configuration
  process.env.API_BASE_URL = 'http://localhost:3000/api';
  process.env.NEXT_PUBLIC_API_URL = 'http://localhost:3000/api';

  // Disable console warnings for tests
  const originalWarn = console.warn;
  console.warn = (...args) => {
    if (typeof args[0] === 'string' && (args[0].includes('Warning:') || args[0].includes('deprecated') || args[0].includes('componentWillReceiveProps'))) {
      return;
    }
    originalWarn.call(console, ...args);
  };
  console.log('✅ Test environment setup complete');
};
exports.default = _default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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