/**
 * Consolidated Application Context
 * 
 * This module consolidates authentication, user, client, and tenant contexts
 * into a single, efficient context provider to eliminate redundancy and
 * improve performance.
 */

'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  fetchAuthSession,
  signOut as amplifySignOut,
  getCurrentUser as getAmplifyCurrentUser,
  fetchUserAttributes
} from 'aws-amplify/auth';
import { TenantContext as TenantContextType } from '@/lib/types';

// Consolidated interfaces
export interface User {
  id: string;
  email: string;
  name?: string;
  given_name?: string;
  family_name?: string;
  roles: string[];
  preferences?: UserPreferences;
  lastLogin?: Date;
}

export interface UserPreferences {
  theme?: 'light' | 'dark' | 'system';
  notifications?: {
    email?: boolean;
    push?: boolean;
    sms?: boolean;
  };
  displayDensity?: 'comfortable' | 'compact';
  [key: string]: any;
}

export interface AppState {
  // Authentication state
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  
  // Tenant/Client state
  tenant: TenantContextType | null;
  tenantLoading: boolean;
  tenantError: string | null;
  
  // Actions
  signOut: () => Promise<boolean>;
  refreshTenant: () => Promise<void>;
  updateUserPreferences: (preferences: Partial<UserPreferences>) => Promise<boolean>;
  hasRole: (role: string) => boolean;
  getDisplayName: () => string;
}

const AppContext = createContext<AppState | undefined>(undefined);

interface AppProviderProps {
  children: ReactNode;
}

export function AppProvider({ children }: AppProviderProps) {
  // Authentication state
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  
  // Tenant state
  const [tenant, setTenant] = useState<TenantContextType | null>(null);
  const [tenantLoading, setTenantLoading] = useState(false);
  const [tenantError, setTenantError] = useState<string | null>(null);

  // Get current user from Amplify
  const getCurrentUser = async (): Promise<User | null> => {
    try {
      const currentUser = await getAmplifyCurrentUser();
      const attributes = await fetchUserAttributes();
      
      return {
        id: currentUser.userId,
        email: attributes.email || '',
        name: attributes.name,
        given_name: attributes.given_name,
        family_name: attributes.family_name,
        roles: attributes['custom:roles'] ? JSON.parse(attributes['custom:roles']) : [],
        preferences: {
          theme: 'system',
          notifications: {
            email: true,
            push: true,
            sms: false
          },
          displayDensity: 'comfortable'
        }
      };
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  };

  // Fetch tenant information
  const fetchTenant = async (retryCount = 0): Promise<void> => {
    try {
      setTenantLoading(true);
      setTenantError(null);
      
      const response = await fetch('/api/clients/domain');
      const data = await response.json();
      
      if (!response.ok) {
        // If unauthorized and we haven't retried much, wait and retry
        if (response.status === 401 && retryCount < 3) {
          console.log(`Tenant fetch failed (401), retrying in ${(retryCount + 1) * 1000}ms... (attempt ${retryCount + 1}/3)`);
          setTimeout(() => {
            fetchTenant(retryCount + 1);
          }, (retryCount + 1) * 1000);
          return;
        }
        throw new Error(data.error || 'Failed to fetch tenant information');
      }
      
      if (data.success && data.data?.client) {
        setTenant(data.data.client);
        console.log('✅ Tenant context loaded successfully:', data.data.client.clientName);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('❌ Tenant fetch error:', errorMessage);
      setTenantError(errorMessage);
      setTenant(null);
    } finally {
      setTenantLoading(false);
    }
  };

  // Authentication check
  useEffect(() => {
    let isMounted = true;

    const checkAuth = async () => {
      if (!isMounted) return;

      try {
        console.log('🔍 [APP-CONTEXT] Starting authentication check...');

        // Check if this is an OAuth callback
        const urlParams = new URLSearchParams(window.location.search);
        const hasOAuthCode = urlParams.has('code');

        if (hasOAuthCode) {
          console.log('🔄 [APP-CONTEXT] OAuth callback detected, giving Amplify extra time...');
          await new Promise(resolve => setTimeout(resolve, 5000));
        } else {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Check if authenticated with Amplify
        const session = await fetchAuthSession({ forceRefresh: false });
        const authenticated = !!session?.tokens?.idToken;

        console.log('🔍 [APP-CONTEXT] Session details:', {
          hasSession: !!session,
          hasTokens: !!session?.tokens,
          hasIdToken: !!session?.tokens?.idToken,
          authenticated
        });

        if (authenticated) {
          console.log('✅ [APP-CONTEXT] Session is authenticated, getting user info...');

          const userInfo = await getCurrentUser();
          console.log('✅ [APP-CONTEXT] User info retrieved:', userInfo);

          if (isMounted && userInfo) {
            setUser(userInfo);
            setIsAuthenticated(true);
            
            // Fetch tenant information after authentication
            setTimeout(() => {
              fetchTenant();
            }, 1000);
          }
        } else {
          console.log('❌ [APP-CONTEXT] No valid session found');
          if (isMounted) {
            setUser(null);
            setIsAuthenticated(false);
            setTenant(null);
          }
        }
      } catch (error) {
        console.error('❌ [APP-CONTEXT] Auth check error:', error);
        if (isMounted) {
          setUser(null);
          setIsAuthenticated(false);
          setTenant(null);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    checkAuth();
    
    return () => {
      isMounted = false;
    };
  }, []);

  // Sign out function
  const signOut = async (): Promise<boolean> => {
    try {
      await amplifySignOut({ global: true });
      
      // Clear localStorage
      localStorage.removeItem('isAuthenticated');
      
      // Clear the auth cookie
      document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';
      
      // Update state
      setUser(null);
      setIsAuthenticated(false);
      setTenant(null);
      
      return true;
    } catch (error) {
      console.error('Error signing out:', error);
      
      // Clear state anyway
      setUser(null);
      setIsAuthenticated(false);
      setTenant(null);
      
      return true;
    }
  };

  // Refresh tenant information
  const refreshTenant = async (): Promise<void> => {
    await fetchTenant();
  };

  // Update user preferences
  const updateUserPreferences = async (preferences: Partial<UserPreferences>): Promise<boolean> => {
    if (!user?.id) return false;
    
    try {
      // Update in state first for immediate feedback
      const updatedPreferences = { ...user.preferences, ...preferences };
      setUser(prev => prev ? { ...prev, preferences: updatedPreferences } : null);
      
      // Cache in localStorage
      localStorage.setItem(`user_prefs_${user.id}`, JSON.stringify(updatedPreferences));
      
      return true;
    } catch (error) {
      console.error('Error updating user preferences:', error);
      return false;
    }
  };

  // Role checking utility
  const hasRole = (role: string): boolean => {
    return user?.roles?.includes(role) || false;
  };

  // Get formatted display name
  const getDisplayName = (): string => {
    if (!user) return 'User';
    
    if (user.given_name && user.family_name) {
      return `${user.given_name} ${user.family_name}`;
    }
    
    if (user.name) {
      return user.name;
    }
    
    if (user.email) {
      return user.email.split('@')[0];
    }
    
    return `User ${user.id.substring(0, 8)}`;
  };

  const value: AppState = {
    isAuthenticated,
    isLoading,
    user,
    tenant,
    tenantLoading,
    tenantError,
    signOut,
    refreshTenant,
    updateUserPreferences,
    hasRole,
    getDisplayName,
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
}

// Custom hook for using app context
export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

// Backward compatibility hooks
export const useAuth = () => {
  const { isAuthenticated, isLoading, user, signOut, hasRole } = useApp();
  return { isAuthenticated, isLoading, user, signOut, hasRole };
};

export const useTenant = () => {
  const { tenant, tenantLoading, tenantError, refreshTenant } = useApp();
  return { tenant, loading: tenantLoading, error: tenantError, refreshTenant };
};

export const useUser = () => {
  const { user, isLoading, updateUserPreferences, getDisplayName, hasRole } = useApp();
  return { user, isLoading, updateUserPreferences, getDisplayName, hasRole };
};
