b821ecb14a5b17633a27784a91374d38
/* istanbul ignore next */
function cov_w3tbhl4ew() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\dashboard\\stats\\route.ts";
  var hash = "39bd647540f7c72ae341fc2d2e90e281228fb398";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\dashboard\\stats\\route.ts",
    statementMap: {
      "0": {
        start: {
          line: 20,
          column: 19
        },
        end: {
          line: 111,
          column: 2
        }
      },
      "1": {
        start: {
          line: 22,
          column: 21
        },
        end: {
          line: 22,
          column: 40
        }
      },
      "2": {
        start: {
          line: 23,
          column: 2
        },
        end: {
          line: 25,
          column: 3
        }
      },
      "3": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 32
        }
      },
      "4": {
        start: {
          line: 27,
          column: 18
        },
        end: {
          line: 27,
          column: 37
        }
      },
      "5": {
        start: {
          line: 30,
          column: 23
        },
        end: {
          line: 30,
          column: 66
        }
      },
      "6": {
        start: {
          line: 32,
          column: 2
        },
        end: {
          line: 41,
          column: 3
        }
      },
      "7": {
        start: {
          line: 33,
          column: 23
        },
        end: {
          line: 33,
          column: 119
        }
      },
      "8": {
        start: {
          line: 34,
          column: 25
        },
        end: {
          line: 34,
          column: 118
        }
      },
      "9": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 40,
          column: 6
        }
      },
      "10": {
        start: {
          line: 43,
          column: 17
        },
        end: {
          line: 43,
          column: 37
        }
      },
      "11": {
        start: {
          line: 46,
          column: 22
        },
        end: {
          line: 46,
          column: 61
        }
      },
      "12": {
        start: {
          line: 48,
          column: 30
        },
        end: {
          line: 53,
          column: 3
        }
      },
      "13": {
        start: {
          line: 55,
          column: 2
        },
        end: {
          line: 108,
          column: 3
        }
      },
      "14": {
        start: {
          line: 57,
          column: 20
        },
        end: {
          line: 74,
          column: 5
        }
      },
      "15": {
        start: {
          line: 77,
          column: 4
        },
        end: {
          line: 98,
          column: 5
        }
      },
      "16": {
        start: {
          line: 78,
          column: 21
        },
        end: {
          line: 78,
          column: 83
        }
      },
      "17": {
        start: {
          line: 80,
          column: 6
        },
        end: {
          line: 97,
          column: 7
        }
      },
      "18": {
        start: {
          line: 81,
          column: 22
        },
        end: {
          line: 81,
          column: 36
        }
      },
      "19": {
        start: {
          line: 82,
          column: 8
        },
        end: {
          line: 96,
          column: 9
        }
      },
      "20": {
        start: {
          line: 84,
          column: 12
        },
        end: {
          line: 84,
          column: 72
        }
      },
      "21": {
        start: {
          line: 85,
          column: 12
        },
        end: {
          line: 85,
          column: 18
        }
      },
      "22": {
        start: {
          line: 87,
          column: 12
        },
        end: {
          line: 87,
          column: 68
        }
      },
      "23": {
        start: {
          line: 88,
          column: 12
        },
        end: {
          line: 88,
          column: 18
        }
      },
      "24": {
        start: {
          line: 90,
          column: 12
        },
        end: {
          line: 90,
          column: 59
        }
      },
      "25": {
        start: {
          line: 91,
          column: 12
        },
        end: {
          line: 91,
          column: 18
        }
      },
      "26": {
        start: {
          line: 93,
          column: 27
        },
        end: {
          line: 93,
          column: 64
        }
      },
      "27": {
        start: {
          line: 94,
          column: 12
        },
        end: {
          line: 94,
          column: 62
        }
      },
      "28": {
        start: {
          line: 95,
          column: 12
        },
        end: {
          line: 95,
          column: 18
        }
      },
      "29": {
        start: {
          line: 100,
          column: 4
        },
        end: {
          line: 100,
          column: 87
        }
      },
      "30": {
        start: {
          line: 102,
          column: 4
        },
        end: {
          line: 107,
          column: 6
        }
      },
      "31": {
        start: {
          line: 110,
          column: 2
        },
        end: {
          line: 110,
          column: 85
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 20,
            column: 37
          },
          end: {
            line: 20,
            column: 38
          }
        },
        loc: {
          start: {
            line: 20,
            column: 49
          },
          end: {
            line: 111,
            column: 1
          }
        },
        line: 20
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 23,
            column: 2
          },
          end: {
            line: 25,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 2
          },
          end: {
            line: 25,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "1": {
        loc: {
          start: {
            line: 32,
            column: 2
          },
          end: {
            line: 41,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 2
          },
          end: {
            line: 41,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      },
      "2": {
        loc: {
          start: {
            line: 33,
            column: 23
          },
          end: {
            line: 33,
            column: 119
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 33,
            column: 64
          },
          end: {
            line: 33,
            column: 84
          }
        }, {
          start: {
            line: 33,
            column: 87
          },
          end: {
            line: 33,
            column: 119
          }
        }],
        line: 33
      },
      "3": {
        loc: {
          start: {
            line: 34,
            column: 25
          },
          end: {
            line: 34,
            column: 118
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 34,
            column: 66
          },
          end: {
            line: 34,
            column: 88
          }
        }, {
          start: {
            line: 34,
            column: 91
          },
          end: {
            line: 34,
            column: 118
          }
        }],
        line: 34
      },
      "4": {
        loc: {
          start: {
            line: 37,
            column: 6
          },
          end: {
            line: 37,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 6
          },
          end: {
            line: 37,
            column: 24
          }
        }, {
          start: {
            line: 37,
            column: 28
          },
          end: {
            line: 37,
            column: 64
          }
        }],
        line: 37
      },
      "5": {
        loc: {
          start: {
            line: 55,
            column: 2
          },
          end: {
            line: 108,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 55,
            column: 2
          },
          end: {
            line: 108,
            column: 3
          }
        }, {
          start: {
            line: 99,
            column: 9
          },
          end: {
            line: 108,
            column: 3
          }
        }],
        line: 55
      },
      "6": {
        loc: {
          start: {
            line: 80,
            column: 6
          },
          end: {
            line: 97,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 80,
            column: 6
          },
          end: {
            line: 97,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 80
      },
      "7": {
        loc: {
          start: {
            line: 80,
            column: 10
          },
          end: {
            line: 80,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 80,
            column: 10
          },
          end: {
            line: 80,
            column: 24
          }
        }, {
          start: {
            line: 80,
            column: 28
          },
          end: {
            line: 80,
            column: 39
          }
        }, {
          start: {
            line: 80,
            column: 43
          },
          end: {
            line: 80,
            column: 65
          }
        }],
        line: 80
      },
      "8": {
        loc: {
          start: {
            line: 82,
            column: 8
          },
          end: {
            line: 96,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 83,
            column: 10
          },
          end: {
            line: 85,
            column: 18
          }
        }, {
          start: {
            line: 86,
            column: 10
          },
          end: {
            line: 88,
            column: 18
          }
        }, {
          start: {
            line: 89,
            column: 10
          },
          end: {
            line: 91,
            column: 18
          }
        }, {
          start: {
            line: 92,
            column: 10
          },
          end: {
            line: 95,
            column: 18
          }
        }],
        line: 82
      },
      "9": {
        loc: {
          start: {
            line: 84,
            column: 43
          },
          end: {
            line: 84,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 84,
            column: 43
          },
          end: {
            line: 84,
            column: 63
          }
        }, {
          start: {
            line: 84,
            column: 67
          },
          end: {
            line: 84,
            column: 70
          }
        }],
        line: 84
      },
      "10": {
        loc: {
          start: {
            line: 87,
            column: 41
          },
          end: {
            line: 87,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 87,
            column: 41
          },
          end: {
            line: 87,
            column: 59
          }
        }, {
          start: {
            line: 87,
            column: 63
          },
          end: {
            line: 87,
            column: 66
          }
        }],
        line: 87
      },
      "11": {
        loc: {
          start: {
            line: 90,
            column: 37
          },
          end: {
            line: 90,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 90,
            column: 37
          },
          end: {
            line: 90,
            column: 50
          }
        }, {
          start: {
            line: 90,
            column: 54
          },
          end: {
            line: 90,
            column: 57
          }
        }],
        line: 90
      },
      "12": {
        loc: {
          start: {
            line: 93,
            column: 38
          },
          end: {
            line: 93,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 93,
            column: 38
          },
          end: {
            line: 93,
            column: 56
          }
        }, {
          start: {
            line: 93,
            column: 60
          },
          end: {
            line: 93,
            column: 63
          }
        }],
        line: 93
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0, 0],
      "8": [0, 0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "39bd647540f7c72ae341fc2d2e90e281228fb398"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_w3tbhl4ew = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_w3tbhl4ew();
import { getClientByEmailDomain } from '@/lib/clients';
import { executeQuery, schemaExists } from '@/lib/database';
import { requireAuth } from '@/lib/auth-middleware';
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus, withErrorHandling } from '@/lib/api-response';

// Dashboard stats interface

export const GET =
/* istanbul ignore next */
(cov_w3tbhl4ew().s[0]++, withErrorHandling(async () => {
  /* istanbul ignore next */
  cov_w3tbhl4ew().f[0]++;
  // Verify authentication
  const authResult =
  /* istanbul ignore next */
  (cov_w3tbhl4ew().s[1]++, await requireAuth());
  /* istanbul ignore next */
  cov_w3tbhl4ew().s[2]++;
  if (!authResult.success) {
    /* istanbul ignore next */
    cov_w3tbhl4ew().b[0][0]++;
    cov_w3tbhl4ew().s[3]++;
    return authResult.response;
  } else
  /* istanbul ignore next */
  {
    cov_w3tbhl4ew().b[0][1]++;
  }
  const session =
  /* istanbul ignore next */
  (cov_w3tbhl4ew().s[4]++, authResult.session);

  // Get tenant context using the new secure method
  const clientResult =
  /* istanbul ignore next */
  (cov_w3tbhl4ew().s[5]++, await getClientByEmailDomain(session.email));
  /* istanbul ignore next */
  cov_w3tbhl4ew().s[6]++;
  if (!clientResult.success) {
    /* istanbul ignore next */
    cov_w3tbhl4ew().b[1][0]++;
    const statusCode =
    /* istanbul ignore next */
    (cov_w3tbhl4ew().s[7]++, clientResult.errorCode === 'NOT_FOUND' ?
    /* istanbul ignore next */
    (cov_w3tbhl4ew().b[2][0]++, HttpStatus.NOT_FOUND) :
    /* istanbul ignore next */
    (cov_w3tbhl4ew().b[2][1]++, HttpStatus.INTERNAL_SERVER_ERROR));
    const apiErrorCode =
    /* istanbul ignore next */
    (cov_w3tbhl4ew().s[8]++, clientResult.errorCode === 'NOT_FOUND' ?
    /* istanbul ignore next */
    (cov_w3tbhl4ew().b[3][0]++, ApiErrorCode.NOT_FOUND) :
    /* istanbul ignore next */
    (cov_w3tbhl4ew().b[3][1]++, ApiErrorCode.DATABASE_ERROR));
    /* istanbul ignore next */
    cov_w3tbhl4ew().s[9]++;
    return createErrorResponse(
    /* istanbul ignore next */
    (cov_w3tbhl4ew().b[4][0]++, clientResult.error) ||
    /* istanbul ignore next */
    (cov_w3tbhl4ew().b[4][1]++, 'Failed to fetch tenant information'), apiErrorCode, statusCode);
  } else
  /* istanbul ignore next */
  {
    cov_w3tbhl4ew().b[1][1]++;
  }
  const tenant =
  /* istanbul ignore next */
  (cov_w3tbhl4ew().s[10]++, clientResult.client);

  // Check if tenant schema exists
  const schemaReady =
  /* istanbul ignore next */
  (cov_w3tbhl4ew().s[11]++, await schemaExists(tenant.tenantSchema));
  let stats =
  /* istanbul ignore next */
  (cov_w3tbhl4ew().s[12]++, {
    totalRenewals: 0,
    renewalsDue: 0,
    vendors: 0,
    annualSpend: '$0'
  });
  /* istanbul ignore next */
  cov_w3tbhl4ew().s[13]++;
  if (schemaReady) {
    /* istanbul ignore next */
    cov_w3tbhl4ew().b[5][0]++;
    // Query tenant schema for actual data
    const queries =
    /* istanbul ignore next */
    (cov_w3tbhl4ew().s[14]++, [{
      query: `SELECT COUNT(*) as total_renewals FROM "${tenant.tenantSchema}"."Renewals" WHERE "Active" = true`,
      key: 'totalRenewals'
    }, {
      query: `SELECT COUNT(*) as renewals_due FROM "${tenant.tenantSchema}"."Renewals" WHERE "Active" = true AND "DueDate" <= CURRENT_DATE + INTERVAL '30 days'`,
      key: 'renewalsDue'
    }, {
      query: `SELECT COUNT(DISTINCT "VendorID") as vendors FROM "${tenant.tenantSchema}"."Renewals" WHERE "Active" = true`,
      key: 'vendors'
    }, {
      query: `SELECT COALESCE(SUM("AnnualCost"), 0) as annual_spend FROM "${tenant.tenantSchema}"."Renewals" WHERE "Active" = true`,
      key: 'annualSpend'
    }]);

    // Execute queries with proper error handling
    /* istanbul ignore next */
    cov_w3tbhl4ew().s[15]++;
    for (const {
      query,
      key
    } of queries) {
      const result =
      /* istanbul ignore next */
      (cov_w3tbhl4ew().s[16]++, await executeQuery(query, [], {
        schema: tenant.tenantSchema
      }));
      /* istanbul ignore next */
      cov_w3tbhl4ew().s[17]++;
      if (
      /* istanbul ignore next */
      (cov_w3tbhl4ew().b[7][0]++, result.success) &&
      /* istanbul ignore next */
      (cov_w3tbhl4ew().b[7][1]++, result.data) &&
      /* istanbul ignore next */
      (cov_w3tbhl4ew().b[7][2]++, result.data.length > 0)) {
        /* istanbul ignore next */
        cov_w3tbhl4ew().b[6][0]++;
        const value =
        /* istanbul ignore next */
        (cov_w3tbhl4ew().s[18]++, result.data[0]);
        /* istanbul ignore next */
        cov_w3tbhl4ew().s[19]++;
        switch (key) {
          case 'totalRenewals':
            /* istanbul ignore next */
            cov_w3tbhl4ew().b[8][0]++;
            cov_w3tbhl4ew().s[20]++;
            stats.totalRenewals = parseInt(
            /* istanbul ignore next */
            (cov_w3tbhl4ew().b[9][0]++, value.total_renewals) ||
            /* istanbul ignore next */
            (cov_w3tbhl4ew().b[9][1]++, '0'));
            /* istanbul ignore next */
            cov_w3tbhl4ew().s[21]++;
            break;
          case 'renewalsDue':
            /* istanbul ignore next */
            cov_w3tbhl4ew().b[8][1]++;
            cov_w3tbhl4ew().s[22]++;
            stats.renewalsDue = parseInt(
            /* istanbul ignore next */
            (cov_w3tbhl4ew().b[10][0]++, value.renewals_due) ||
            /* istanbul ignore next */
            (cov_w3tbhl4ew().b[10][1]++, '0'));
            /* istanbul ignore next */
            cov_w3tbhl4ew().s[23]++;
            break;
          case 'vendors':
            /* istanbul ignore next */
            cov_w3tbhl4ew().b[8][2]++;
            cov_w3tbhl4ew().s[24]++;
            stats.vendors = parseInt(
            /* istanbul ignore next */
            (cov_w3tbhl4ew().b[11][0]++, value.vendors) ||
            /* istanbul ignore next */
            (cov_w3tbhl4ew().b[11][1]++, '0'));
            /* istanbul ignore next */
            cov_w3tbhl4ew().s[25]++;
            break;
          case 'annualSpend':
            /* istanbul ignore next */
            cov_w3tbhl4ew().b[8][3]++;
            const amount =
            /* istanbul ignore next */
            (cov_w3tbhl4ew().s[26]++, parseFloat(
            /* istanbul ignore next */
            (cov_w3tbhl4ew().b[12][0]++, value.annual_spend) ||
            /* istanbul ignore next */
            (cov_w3tbhl4ew().b[12][1]++, '0')));
            /* istanbul ignore next */
            cov_w3tbhl4ew().s[27]++;
            stats.annualSpend = `$${amount.toLocaleString()}`;
            /* istanbul ignore next */
            cov_w3tbhl4ew().s[28]++;
            break;
        }
      } else
      /* istanbul ignore next */
      {
        cov_w3tbhl4ew().b[6][1]++;
      }
    }
  } else {
    /* istanbul ignore next */
    cov_w3tbhl4ew().b[5][1]++;
    cov_w3tbhl4ew().s[29]++;
    console.log(`Tenant schema ${tenant.tenantSchema} not ready yet, using mock data`);
    // Return mock data as fallback
    /* istanbul ignore next */
    cov_w3tbhl4ew().s[30]++;
    stats = {
      totalRenewals: 14,
      renewalsDue: 3,
      vendors: 9,
      annualSpend: '$817,340'
    };
  }
  /* istanbul ignore next */
  cov_w3tbhl4ew().s[31]++;
  return createSuccessResponse(stats, 'Dashboard statistics retrieved successfully');
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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