-- Create Global Master Data Tables in metadata schema
-- This implements the lazy synchronization pattern for multi-tenant master data management

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum types for status fields
DO $$ BEGIN
    CREATE TYPE metadata.sync_status AS ENUM ('pending', 'matched', 'manual_review', 'rejected');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE metadata.entity_status AS ENUM ('active', 'merged', 'deprecated');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE metadata.product_type AS ENUM ('physical', 'digital', 'service');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create global_vendors table
CREATE TABLE IF NOT EXISTS metadata.global_vendors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    canonical_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    legal_name <PERSON><PERSON>HA<PERSON>(255),
    tax_id VARCHAR(100),
    country_code VARCHAR(2), -- ISO 3166-1 alpha-2 country code
    domain VARCHAR(255), -- Email domain for matching
    address_hash VARCHAR(64), -- Normalized address hash for deduplication
    phone_normalized VARCHAR(50), -- Normalized phone number
    confidence_score DECIMAL(5,4) DEFAULT 0.0000 CHECK (confidence_score >= 0 AND confidence_score <= 1),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status metadata.entity_status DEFAULT 'active',
    merged_into_id UUID REFERENCES metadata.global_vendors(id),
    
    -- Constraints
    CONSTRAINT global_vendors_canonical_name_unique UNIQUE (canonical_name),
    CONSTRAINT global_vendors_merged_check CHECK (
        (status = 'merged' AND merged_into_id IS NOT NULL) OR 
        (status != 'merged' AND merged_into_id IS NULL)
    )
);

-- Create global_products table
CREATE TABLE IF NOT EXISTS metadata.global_products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    canonical_name VARCHAR(255) NOT NULL,
    product_category VARCHAR(100),
    vendor_id UUID NOT NULL REFERENCES metadata.global_vendors(id),
    gtin VARCHAR(14), -- Global Trade Item Number (GTIN-14 format)
    manufacturer_sku VARCHAR(100),
    product_type metadata.product_type DEFAULT 'physical',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status metadata.entity_status DEFAULT 'active',
    merged_into_id UUID REFERENCES metadata.global_products(id),
    
    -- Constraints
    CONSTRAINT global_products_vendor_name_unique UNIQUE (vendor_id, canonical_name),
    CONSTRAINT global_products_gtin_unique UNIQUE (gtin) WHERE gtin IS NOT NULL,
    CONSTRAINT global_products_merged_check CHECK (
        (status = 'merged' AND merged_into_id IS NOT NULL) OR 
        (status != 'merged' AND merged_into_id IS NULL)
    )
);

-- Create global_product_versions table
CREATE TABLE IF NOT EXISTS metadata.global_product_versions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES metadata.global_products(id),
    version_number VARCHAR(50) NOT NULL,
    release_date DATE,
    end_of_life_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status metadata.entity_status DEFAULT 'active',
    merged_into_id UUID REFERENCES metadata.global_product_versions(id),
    
    -- Constraints
    CONSTRAINT global_product_versions_product_version_unique UNIQUE (product_id, version_number),
    CONSTRAINT global_product_versions_date_check CHECK (
        end_of_life_date IS NULL OR release_date IS NULL OR end_of_life_date >= release_date
    ),
    CONSTRAINT global_product_versions_merged_check CHECK (
        (status = 'merged' AND merged_into_id IS NOT NULL) OR 
        (status != 'merged' AND merged_into_id IS NULL)
    )
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_global_vendors_canonical_name ON metadata.global_vendors(canonical_name);
CREATE INDEX IF NOT EXISTS idx_global_vendors_domain ON metadata.global_vendors(domain) WHERE domain IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_global_vendors_tax_id ON metadata.global_vendors(tax_id) WHERE tax_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_global_vendors_status ON metadata.global_vendors(status);
CREATE INDEX IF NOT EXISTS idx_global_vendors_confidence ON metadata.global_vendors(confidence_score DESC);

CREATE INDEX IF NOT EXISTS idx_global_products_vendor_id ON metadata.global_products(vendor_id);
CREATE INDEX IF NOT EXISTS idx_global_products_canonical_name ON metadata.global_products(canonical_name);
CREATE INDEX IF NOT EXISTS idx_global_products_category ON metadata.global_products(product_category);
CREATE INDEX IF NOT EXISTS idx_global_products_gtin ON metadata.global_products(gtin) WHERE gtin IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_global_products_status ON metadata.global_products(status);

CREATE INDEX IF NOT EXISTS idx_global_product_versions_product_id ON metadata.global_product_versions(product_id);
CREATE INDEX IF NOT EXISTS idx_global_product_versions_version ON metadata.global_product_versions(version_number);
CREATE INDEX IF NOT EXISTS idx_global_product_versions_release_date ON metadata.global_product_versions(release_date);
CREATE INDEX IF NOT EXISTS idx_global_product_versions_status ON metadata.global_product_versions(status);

-- Create update triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION metadata.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_global_vendors_updated_at
    BEFORE UPDATE ON metadata.global_vendors
    FOR EACH ROW
    EXECUTE FUNCTION metadata.update_updated_at_column();

CREATE TRIGGER update_global_products_updated_at
    BEFORE UPDATE ON metadata.global_products
    FOR EACH ROW
    EXECUTE FUNCTION metadata.update_updated_at_column();

CREATE TRIGGER update_global_product_versions_updated_at
    BEFORE UPDATE ON metadata.global_product_versions
    FOR EACH ROW
    EXECUTE FUNCTION metadata.update_updated_at_column();
