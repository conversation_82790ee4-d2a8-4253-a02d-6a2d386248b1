/**
 * Admin API endpoint to test product version matching algorithm
 */

import { NextRequest } from 'next/server';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  ApiErrorCode, 
  HttpStatus,
  withErrorHandling 
} from '@/lib/api-response';
import { ProductVersionMatcher } from '@/lib/sync/product-version-matcher';

export const GET = withErrorHandling(async (request: NextRequest) => {
  try {
    console.log('Testing product version matching algorithm...');
    
    // Test the matching algorithm
    const matchResult = await ProductVersionMatcher.testMatching();
    
    console.log('✅ Product version matching test completed!');
    console.log('Match result:', JSON.stringify(matchResult, null, 2));
    
    return createSuccessResponse(
      matchResult,
      'Product version matching algorithm tested successfully'
    );
    
  } catch (error) {
    console.error('❌ Error testing product version matching:', error);
    return createErrorResponse(
      `Failed to test product version matching: ${error}`,
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});
