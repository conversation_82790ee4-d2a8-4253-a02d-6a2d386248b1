/**
 * Admin API endpoint to populate sample data for master data management
 */

import { NextRequest } from 'next/server';
import { executeQuery } from '@/lib/database';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  ApiErrorCode, 
  HttpStatus,
  withErrorHandling 
} from '@/lib/api-response';

export const POST = withErrorHandling(async (request: NextRequest) => {
  try {
    console.log('Populating sample master data...');
    
    // Insert sample global vendors
    const vendorsData = [
      { name: 'Microsoft Corporation', legal: 'Microsoft Corporation', domain: 'microsoft.com', country: 'US' },
      { name: 'Adobe Inc.', legal: 'Adobe Inc.', domain: 'adobe.com', country: 'US' },
      { name: 'Cisco Systems', legal: 'Cisco Systems, Inc.', domain: 'cisco.com', country: 'US' },
      { name: 'HPE', legal: 'Hewlett Packard Enterprise Company', domain: 'hpe.com', country: 'US' },
      { name: '<PERSON>skover', legal: 'Diskover Data Inc.', domain: 'diskoverdata.com', country: 'US' },
      { name: 'Test Vendor', legal: 'Test Vendor LLC', domain: 'test.com', country: 'US' }
    ];
    
    const vendorIds: { [key: string]: string } = {};
    
    for (const vendor of vendorsData) {
      const result = await executeQuery(`
        INSERT INTO metadata.global_vendors (canonical_name, legal_name, domain, country_code, confidence_score)
        VALUES ($1, $2, $3, $4, 1.0)
        ON CONFLICT (canonical_name) DO UPDATE SET
          legal_name = EXCLUDED.legal_name,
          domain = EXCLUDED.domain,
          country_code = EXCLUDED.country_code,
          updated_at = CURRENT_TIMESTAMP
        RETURNING id
      `, [vendor.name, vendor.legal, vendor.domain, vendor.country]);
      
      if (result.success && result.data && result.data.length > 0) {
        vendorIds[vendor.name] = result.data[0].id;
      }
    }
    console.log('✓ Sample vendors created');
    
    // Insert sample global products
    const productsData = [
      { name: 'Microsoft 365', category: 'Productivity Suite', vendor: 'Microsoft Corporation', type: 'digital' },
      { name: 'Adobe Creative Cloud', category: 'Creative Software', vendor: 'Adobe Inc.', type: 'digital' },
      { name: 'ASA Firewall', category: 'Network Security', vendor: 'Cisco Systems', type: 'physical' },
      { name: 'DL360 Server', category: 'Server Hardware', vendor: 'HPE', type: 'physical' },
      { name: 'Diskover Data Management', category: 'Data Management', vendor: 'Diskover', type: 'service' },
      { name: 'Test Product', category: 'Testing', vendor: 'Test Vendor', type: 'digital' }
    ];
    
    const productIds: { [key: string]: string } = {};
    
    for (const product of productsData) {
      const vendorId = vendorIds[product.vendor];
      if (vendorId) {
        const result = await executeQuery(`
          INSERT INTO metadata.global_products (canonical_name, product_category, vendor_id, product_type)
          VALUES ($1, $2, $3, $4::metadata.product_type)
          ON CONFLICT (vendor_id, canonical_name) DO UPDATE SET
            product_category = EXCLUDED.product_category,
            product_type = EXCLUDED.product_type,
            updated_at = CURRENT_TIMESTAMP
          RETURNING id
        `, [product.name, product.category, vendorId, product.type]);
        
        if (result.success && result.data && result.data.length > 0) {
          productIds[product.name] = result.data[0].id;
        }
      }
    }
    console.log('✓ Sample products created');
    
    // Insert sample product versions
    const versionsData = [
      { product: 'Microsoft 365', version: 'E5', release: '2023-01-01' },
      { product: 'Microsoft 365', version: 'F1', release: '2022-06-01' },
      { product: 'Adobe Creative Cloud', version: '2024', release: '2024-01-01' },
      { product: 'ASA Firewall', version: '9.18', release: '2023-03-15' },
      { product: 'DL360 Server', version: 'Gen10', release: '2022-01-01' },
      { product: 'Diskover Data Management', version: 'v1.0', release: '2023-01-01' },
      { product: 'Test Product', version: 'v1.0', release: '2024-01-01' }
    ];
    
    for (const version of versionsData) {
      const productId = productIds[version.product];
      if (productId) {
        await executeQuery(`
          INSERT INTO metadata.global_product_versions (product_id, version_number, release_date)
          VALUES ($1, $2, $3)
          ON CONFLICT (product_id, version_number) DO UPDATE SET
            release_date = EXCLUDED.release_date,
            updated_at = CURRENT_TIMESTAMP
        `, [productId, version.version, version.release]);
      }
    }
    console.log('✓ Sample product versions created');
    
    // Insert sample tenant vendors (mapping to global vendors)
    const tenantVendorsData = [
      { name: 'Microsoft', display: 'Microsoft Corporation', email: '<EMAIL>', global: 'Microsoft Corporation' },
      { name: 'Adobe', display: 'Adobe Inc.', email: '<EMAIL>', global: 'Adobe Inc.' },
      { name: 'Cisco', display: 'Cisco Systems', email: '<EMAIL>', global: 'Cisco Systems' },
      { name: 'HPE', display: 'Hewlett Packard Enterprise', email: '<EMAIL>', global: 'HPE' },
      { name: 'Diskover', display: 'Diskover Data', email: '<EMAIL>', global: 'Diskover' },
      { name: 'test', display: 'Test Vendor', email: '<EMAIL>', global: 'Test Vendor' }
    ];
    
    const tenantVendorIds: { [key: string]: string } = {};
    
    for (const vendor of tenantVendorsData) {
      const globalVendorId = vendorIds[vendor.global];
      const result = await executeQuery(`
        INSERT INTO "tenant_0000000000000001".tenant_vendors (name, display_name, contact_email, global_vendor_id, sync_status, sync_confidence)
        VALUES ($1, $2, $3, $4, 'matched', 1.0)
        ON CONFLICT (name) DO UPDATE SET
          display_name = EXCLUDED.display_name,
          contact_email = EXCLUDED.contact_email,
          global_vendor_id = EXCLUDED.global_vendor_id,
          sync_status = EXCLUDED.sync_status,
          sync_confidence = EXCLUDED.sync_confidence,
          updated_at = CURRENT_TIMESTAMP
        RETURNING id
      `, [vendor.name, vendor.display, vendor.email, globalVendorId]);
      
      if (result.success && result.data && result.data.length > 0) {
        tenantVendorIds[vendor.name] = result.data[0].id;
      }
    }
    console.log('✓ Sample tenant vendors created');
    
    // Insert sample tenant products
    const tenantProductsData = [
      { name: 'M365', vendor: 'Microsoft', description: 'Microsoft 365 subscription', category: 'Productivity', global: 'Microsoft 365' },
      { name: 'Cloud Suite', vendor: 'Adobe', description: 'Adobe Creative Cloud', category: 'Creative', global: 'Adobe Creative Cloud' },
      { name: 'ASA400', vendor: 'Cisco', description: 'Cisco ASA Firewall', category: 'Security', global: 'ASA Firewall' },
      { name: 'DL360', vendor: 'HPE', description: 'HPE DL360 Server', category: 'Hardware', global: 'DL360 Server' },
      { name: 'Diskover', vendor: 'Diskover', description: 'Data management platform', category: 'Data', global: 'Diskover Data Management' },
      { name: 'Alert Test', vendor: 'test', description: 'Test product for alerts', category: 'Testing', global: 'Test Product' }
    ];
    
    const tenantProductIds: { [key: string]: string } = {};
    
    for (const product of tenantProductsData) {
      const tenantVendorId = tenantVendorIds[product.vendor];
      const globalProductId = productIds[product.global];
      
      if (tenantVendorId) {
        const result = await executeQuery(`
          INSERT INTO "tenant_0000000000000001".tenant_products (name, vendor_id, description, category, global_product_id, sync_status, sync_confidence)
          VALUES ($1, $2, $3, $4, $5, 'matched', 1.0)
          ON CONFLICT (name) DO UPDATE SET
            vendor_id = EXCLUDED.vendor_id,
            description = EXCLUDED.description,
            category = EXCLUDED.category,
            global_product_id = EXCLUDED.global_product_id,
            sync_status = EXCLUDED.sync_status,
            sync_confidence = EXCLUDED.sync_confidence,
            updated_at = CURRENT_TIMESTAMP
          RETURNING id
        `, [product.name, tenantVendorId, product.description, product.category, globalProductId]);
        
        if (result.success && result.data && result.data.length > 0) {
          tenantProductIds[product.name] = result.data[0].id;
        }
      }
    }
    console.log('✓ Sample tenant products created');
    
    // Insert sample tenant product versions
    const tenantVersionsData = [
      { product: 'M365', version: 'v5', notes: 'E5 license', current: false },
      { product: 'M365', version: 'vF1', notes: 'F1 license', current: true },
      { product: 'Cloud Suite', version: 'vent', notes: '2024 version', current: true },
      { product: 'ASA400', version: 'vGat', notes: 'Gateway version', current: true },
      { product: 'DL360', version: 'vG10', notes: 'Gen10 server', current: true },
      { product: 'Diskover', version: 'v1', notes: 'Version 1.0', current: true },
      { product: 'Alert Test', version: 'test', notes: 'Test version', current: true }
    ];
    
    for (const version of tenantVersionsData) {
      const tenantProductId = tenantProductIds[version.product];
      if (tenantProductId) {
        await executeQuery(`
          INSERT INTO "tenant_0000000000000001".tenant_product_versions (product_id, version, notes, is_current, sync_status, sync_confidence)
          VALUES ($1, $2, $3, $4, 'matched', 1.0)
          ON CONFLICT (product_id, version) DO UPDATE SET
            notes = EXCLUDED.notes,
            is_current = EXCLUDED.is_current,
            sync_status = EXCLUDED.sync_status,
            sync_confidence = EXCLUDED.sync_confidence,
            updated_at = CURRENT_TIMESTAMP
        `, [tenantProductId, version.version, version.notes, version.current]);
      }
    }
    console.log('✓ Sample tenant product versions created');
    
    console.log('✅ All sample master data populated successfully!');
    
    return createSuccessResponse(
      { 
        message: 'Sample data populated successfully',
        vendors: Object.keys(vendorIds).length,
        products: Object.keys(productIds).length,
        tenantVendors: Object.keys(tenantVendorIds).length,
        tenantProducts: Object.keys(tenantProductIds).length
      },
      'Sample master data has been populated'
    );
    
  } catch (error) {
    console.error('❌ Error populating sample data:', error);
    return createErrorResponse(
      'Failed to populate sample data',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});
