/**
 * Test Dashboard Fix Script
 * 
 * This script tests the dashboard API endpoints to verify the column name fixes
 * and helps identify any remaining issues with the dashboard rendering.
 */

const { Pool } = require('pg')

// Database configuration
const dbConfig = {
  user: 'postgres',
  host: '127.0.0.1',
  database: 'Renewtrack',
  password: process.env.DB_PASSWORD || '',
  port: 5432,
  ssl: false
}

async function testDashboardAPIs() {
  console.log('🔍 Testing Dashboard API Fixes...\n')

  const pool = new Pool(dbConfig)

  try {
    // Test database connection
    console.log('1. Testing database connection...')
    const client = await pool.connect()
    await client.query('SELECT 1')
    client.release()
    console.log('✅ Database connection successful\n')

    // Test tenant schema existence
    console.log('2. Checking tenant schema...')
    const schemaResult = await pool.query(`
      SELECT schema_name 
      FROM information_schema.schemata 
      WHERE schema_name = 'tenant_0000000000000001'
    `)
    
    if (schemaResult.rows.length === 0) {
      console.log('❌ Tenant schema does not exist')
      return
    }
    console.log('✅ Tenant schema exists\n')

    // Test Renewals table structure
    console.log('3. Checking Renewals table structure...')
    const tableResult = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_schema = 'tenant_0000000000000001' 
        AND table_name = 'Renewals'
      ORDER BY ordinal_position
    `)
    
    if (tableResult.rows.length === 0) {
      console.log('❌ Renewals table does not exist')
      return
    }
    
    console.log('📋 Renewals table columns:')
    tableResult.rows.forEach(row => {
      console.log(`   - ${row.column_name} (${row.data_type})`)
    })
    console.log('')

    // Test the corrected stats query
    console.log('4. Testing corrected stats queries...')
    
    try {
      // Total renewals
      const totalResult = await pool.query(`
        SELECT COUNT(*) as total_renewals 
        FROM "tenant_0000000000000001"."Renewals" 
        WHERE "Active" = true
      `)
      console.log(`✅ Total renewals: ${totalResult.rows[0].total_renewals}`)

      // Renewals due (using correct column name)
      const dueResult = await pool.query(`
        SELECT COUNT(*) as renewals_due 
        FROM "tenant_0000000000000001"."Renewals" 
        WHERE "Active" = true AND "RenewalDate" <= CURRENT_DATE + INTERVAL '30 days'
      `)
      console.log(`✅ Renewals due: ${dueResult.rows[0].renewals_due}`)

      // Vendors (using correct approach)
      const vendorsResult = await pool.query(`
        SELECT COUNT(DISTINCT "VendorName") as vendors 
        FROM "tenant_0000000000000001"."Renewals" 
        WHERE "Active" = true
      `)
      console.log(`✅ Vendors: ${vendorsResult.rows[0].vendors}`)

      // Annual spend (using correct column name)
      const spendResult = await pool.query(`
        SELECT COALESCE(SUM("Cost"), 0) as annual_spend 
        FROM "tenant_0000000000000001"."Renewals" 
        WHERE "Active" = true
      `)
      console.log(`✅ Annual spend: $${spendResult.rows[0].annual_spend}`)

    } catch (error) {
      console.log('❌ Stats query error:', error.message)
    }

    console.log('')

    // Test the corrected renewals query
    console.log('5. Testing corrected renewals query...')
    
    try {
      const renewalsResult = await pool.query(`
        SELECT
          "RenewalID" as id,
          "RenewalName" as name,
          "VendorName" as vendor,
          "Status" as status,
          "RenewalDate" as due_date,
          "CreatedOn" as created_at
        FROM "tenant_0000000000000001"."Renewals"
        WHERE "Active" = true
        ORDER BY "CreatedOn" DESC
        LIMIT 5
      `)
      
      console.log(`✅ Recent renewals query successful (${renewalsResult.rows.length} rows)`)
      if (renewalsResult.rows.length > 0) {
        console.log('📋 Sample renewal:')
        const sample = renewalsResult.rows[0]
        console.log(`   - ID: ${sample.id}`)
        console.log(`   - Name: ${sample.name}`)
        console.log(`   - Vendor: ${sample.vendor}`)
        console.log(`   - Status: ${sample.status}`)
        console.log(`   - Due Date: ${sample.due_date}`)
      }

    } catch (error) {
      console.log('❌ Renewals query error:', error.message)
    }

    console.log('')

    // Test API endpoints
    console.log('6. Testing API endpoints...')
    
    try {
      // Test stats API
      const statsResponse = await fetch('http://localhost:3000/api/dashboard/stats')
      if (statsResponse.ok) {
        const statsData = await statsResponse.json()
        console.log('✅ Stats API working:', statsData)
      } else {
        console.log('❌ Stats API error:', statsResponse.status, statsResponse.statusText)
      }

      // Test renewals API
      const renewalsResponse = await fetch('http://localhost:3000/api/dashboard/renewals')
      if (renewalsResponse.ok) {
        const renewalsData = await renewalsResponse.json()
        console.log('✅ Renewals API working:', renewalsData.data?.length || 0, 'renewals')
      } else {
        console.log('❌ Renewals API error:', renewalsResponse.status, renewalsResponse.statusText)
      }

    } catch (error) {
      console.log('⚠️  API test skipped (server may not be running):', error.message)
    }

  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    await pool.end()
  }

  console.log('\n🎯 Dashboard API test completed!')
}

// Run the test
testDashboardAPIs().catch(console.error)
