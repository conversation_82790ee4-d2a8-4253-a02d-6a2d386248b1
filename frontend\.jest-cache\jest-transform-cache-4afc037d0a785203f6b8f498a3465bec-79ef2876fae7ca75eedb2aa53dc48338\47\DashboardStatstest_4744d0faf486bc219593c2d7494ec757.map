{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_testUtils", "_DashboardStats", "_jsxFileName", "__jsx", "default", "createElement", "describe", "mockStats", "totalRenewals", "renewalsDue", "vendors", "annualSpend", "beforeEach", "jest", "clearAllMocks", "it", "render", "stats", "__self", "__source", "fileName", "lineNumber", "columnNumber", "expect", "screen", "getByText", "toBeInTheDocument", "container", "className", "statsGrid", "querySelector", "toHaveClass", "getByTestId", "stats<PERSON><PERSON><PERSON>", "getByRole", "toHaveAttribute", "isLoading", "loadingElements", "getAllByText", "toHave<PERSON>ength", "skeletons", "document", "querySelectorAll", "length", "toBeGreaterThan", "queryByText", "not", "rerender", "zeroStats", "largeStats", "stringStats", "newStats", "statCards", "undefinedStats", "undefined", "toThrow", "nullStats", "region"], "sources": ["DashboardStats.test.tsx"], "sourcesContent": ["/**\n * Dashboard Stats Component Tests\n * \n * Tests for the DashboardStats component including loading states,\n * data display, and performance optimizations\n */\n\nimport React from 'react'\nimport { render, screen } from '../utils/test-utils'\nimport DashboardStats from '@/components/dashboard/DashboardStats'\nimport { DashboardStats as DashboardStatsType } from '@/lib/types'\n\ndescribe('DashboardStats Component', () => {\n  const mockStats: DashboardStatsType = {\n    totalRenewals: 25,\n    renewalsDue: 5,\n    vendors: 12,\n    annualSpend: '$125,000',\n  }\n\n  beforeEach(() => {\n    jest.clearAllMocks()\n  })\n\n  describe('Rendering', () => {\n    it('should render all stat cards with correct data', () => {\n      render(<DashboardStats stats={mockStats} />)\n\n      // Check if all stat cards are rendered\n      expect(screen.getByText('Total Renewals')).toBeInTheDocument()\n      expect(screen.getByText('Renewals Due')).toBeInTheDocument()\n      expect(screen.getByText('Vendors')).toBeInTheDocument()\n      expect(screen.getByText('Annual Spend')).toBeInTheDocument()\n\n      // Check if values are displayed correctly\n      expect(screen.getByText('25')).toBeInTheDocument()\n      expect(screen.getByText('5')).toBeInTheDocument()\n      expect(screen.getByText('12')).toBeInTheDocument()\n      expect(screen.getByText('$125,000')).toBeInTheDocument()\n    })\n\n    it('should render with custom className', () => {\n      const { container } = render(\n        <DashboardStats stats={mockStats} className=\"custom-class\" />\n      )\n\n      const statsGrid = container.querySelector('.stats-grid')\n      expect(statsGrid).toHaveClass('custom-class')\n    })\n\n    it('should render with data-testid', () => {\n      render(\n        <DashboardStats \n          stats={mockStats} \n          data-testid=\"dashboard-stats-test\" \n        />\n      )\n\n      expect(screen.getByTestId('dashboard-stats-test')).toBeInTheDocument()\n    })\n\n    it('should have proper accessibility attributes', () => {\n      render(<DashboardStats stats={mockStats} />)\n\n      const statsContainer = screen.getByRole('region')\n      expect(statsContainer).toHaveAttribute('aria-label', 'Dashboard Statistics')\n    })\n  })\n\n  describe('Loading States', () => {\n    it('should show loading skeletons when isLoading is true', () => {\n      render(<DashboardStats stats={mockStats} isLoading={true} />)\n\n      // Check for loading indicators\n      const loadingElements = screen.getAllByText('⏳')\n      expect(loadingElements).toHaveLength(4) // One for each stat card\n\n      // Check for skeleton loading elements\n      const skeletons = document.querySelectorAll('.animate-pulse')\n      expect(skeletons.length).toBeGreaterThan(0)\n    })\n\n    it('should hide loading skeletons when isLoading is false', () => {\n      render(<DashboardStats stats={mockStats} isLoading={false} />)\n\n      // Should not show loading indicators\n      expect(screen.queryByText('⏳')).not.toBeInTheDocument()\n\n      // Should show actual data\n      expect(screen.getByText('25')).toBeInTheDocument()\n    })\n\n    it('should handle transition from loading to loaded state', () => {\n      const { rerender } = render(\n        <DashboardStats stats={mockStats} isLoading={true} />\n      )\n\n      // Initially loading\n      expect(screen.getAllByText('⏳')).toHaveLength(4)\n\n      // Rerender with loaded state\n      rerender(<DashboardStats stats={mockStats} isLoading={false} />)\n\n      // Should show data now\n      expect(screen.queryByText('⏳')).not.toBeInTheDocument()\n      expect(screen.getByText('25')).toBeInTheDocument()\n    })\n  })\n\n  describe('Data Handling', () => {\n    it('should handle zero values correctly', () => {\n      const zeroStats: DashboardStatsType = {\n        totalRenewals: 0,\n        renewalsDue: 0,\n        vendors: 0,\n        annualSpend: '$0',\n      }\n\n      render(<DashboardStats stats={zeroStats} />)\n\n      expect(screen.getAllByText('0')).toHaveLength(3)\n      expect(screen.getByText('$0')).toBeInTheDocument()\n    })\n\n    it('should handle large numbers correctly', () => {\n      const largeStats: DashboardStatsType = {\n        totalRenewals: 1000,\n        renewalsDue: 250,\n        vendors: 500,\n        annualSpend: '$1,000,000',\n      }\n\n      render(<DashboardStats stats={largeStats} />)\n\n      expect(screen.getByText('1000')).toBeInTheDocument()\n      expect(screen.getByText('250')).toBeInTheDocument()\n      expect(screen.getByText('500')).toBeInTheDocument()\n      expect(screen.getByText('$1,000,000')).toBeInTheDocument()\n    })\n\n    it('should handle string values for annual spend', () => {\n      const stringStats: DashboardStatsType = {\n        totalRenewals: 25,\n        renewalsDue: 5,\n        vendors: 12,\n        annualSpend: 'N/A',\n      }\n\n      render(<DashboardStats stats={stringStats} />)\n\n      expect(screen.getByText('N/A')).toBeInTheDocument()\n    })\n  })\n\n  describe('Performance', () => {\n    it('should memoize stats configuration', () => {\n      const { rerender } = render(<DashboardStats stats={mockStats} />)\n\n      // Rerender with same stats - should not cause unnecessary recalculation\n      rerender(<DashboardStats stats={mockStats} />)\n\n      // Component should still render correctly\n      expect(screen.getByText('Total Renewals')).toBeInTheDocument()\n    })\n\n    it('should only re-render when stats change', () => {\n      const { rerender } = render(<DashboardStats stats={mockStats} />)\n\n      // Rerender with different props but same stats\n      rerender(<DashboardStats stats={mockStats} className=\"new-class\" />)\n\n      // Should still show same data\n      expect(screen.getByText('25')).toBeInTheDocument()\n    })\n\n    it('should update when stats change', () => {\n      const { rerender } = render(<DashboardStats stats={mockStats} />)\n\n      const newStats: DashboardStatsType = {\n        totalRenewals: 30,\n        renewalsDue: 8,\n        vendors: 15,\n        annualSpend: '$150,000',\n      }\n\n      rerender(<DashboardStats stats={newStats} />)\n\n      // Should show updated data\n      expect(screen.getByText('30')).toBeInTheDocument()\n      expect(screen.getByText('8')).toBeInTheDocument()\n      expect(screen.getByText('15')).toBeInTheDocument()\n      expect(screen.getByText('$150,000')).toBeInTheDocument()\n    })\n  })\n\n  describe('Icons and Visual Elements', () => {\n    it('should display correct icons for each stat', () => {\n      render(<DashboardStats stats={mockStats} />)\n\n      // Check for emoji icons (these might need adjustment based on actual implementation)\n      expect(screen.getByText('📊')).toBeInTheDocument() // Total Renewals\n      expect(screen.getByText('⚠️')).toBeInTheDocument() // Renewals Due\n      expect(screen.getByText('🏢')).toBeInTheDocument() // Vendors\n      expect(screen.getByText('💰')).toBeInTheDocument() // Annual Spend\n    })\n\n    it('should maintain consistent layout structure', () => {\n      const { container } = render(<DashboardStats stats={mockStats} />)\n\n      const statsGrid = container.querySelector('.stats-grid')\n      expect(statsGrid).toBeInTheDocument()\n\n      const statCards = container.querySelectorAll('.stat-card')\n      expect(statCards).toHaveLength(4)\n    })\n  })\n\n  describe('Error Handling', () => {\n    it('should handle undefined stats gracefully', () => {\n      // This test ensures the component doesn't crash with undefined stats\n      const undefinedStats = {\n        totalRenewals: undefined as any,\n        renewalsDue: undefined as any,\n        vendors: undefined as any,\n        annualSpend: undefined as any,\n      }\n\n      expect(() => {\n        render(<DashboardStats stats={undefinedStats} />)\n      }).not.toThrow()\n    })\n\n    it('should handle null stats gracefully', () => {\n      const nullStats = {\n        totalRenewals: null as any,\n        renewalsDue: null as any,\n        vendors: null as any,\n        annualSpend: null as any,\n      }\n\n      expect(() => {\n        render(<DashboardStats stats={nullStats} />)\n      }).not.toThrow()\n    })\n  })\n\n  describe('Accessibility', () => {\n    it('should have proper ARIA labels', () => {\n      render(<DashboardStats stats={mockStats} />)\n\n      const region = screen.getByRole('region')\n      expect(region).toHaveAttribute('aria-label', 'Dashboard Statistics')\n    })\n\n    it('should be keyboard navigable', () => {\n      render(<DashboardStats stats={mockStats} />)\n\n      // The component should not interfere with keyboard navigation\n      // This is more of a structural test\n      const statsContainer = screen.getByRole('region')\n      expect(statsContainer).toBeInTheDocument()\n    })\n  })\n})\n"], "mappings": ";;;AAOA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,eAAA,GAAAH,sBAAA,CAAAC,OAAA;AAAkE,IAAAG,YAAA;AATlE;AACA;AACA;AACA;AACA;AACA;AALA,IAAAC,KAAA,GAAAN,MAAA,CAAAO,OAAA,CAAAC,aAAA;AAYAC,QAAQ,CAAC,0BAA0B,EAAE,MAAM;EACzC,MAAMC,SAA6B,GAAG;IACpCC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,CAAC;IACdC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE;EACf,CAAC;EAEDC,UAAU,CAAC,MAAM;IACfC,IAAI,CAACC,aAAa,CAAC,CAAC;EACtB,CAAC,CAAC;EAEFR,QAAQ,CAAC,WAAW,EAAE,MAAM;IAC1BS,EAAE,CAAC,gDAAgD,EAAE,MAAM;MACzD,IAAAC,iBAAM,EAACb,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEV,SAAU;QAAAW,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,CAAC;;MAE5C;MACAC,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;MAC9DH,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,cAAc,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;MAC5DH,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,SAAS,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;MACvDH,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,cAAc,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;;MAE5D;MACAH,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,IAAI,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;MAClDH,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,GAAG,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;MACjDH,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,IAAI,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;MAClDH,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,UAAU,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;IAC1D,CAAC,CAAC;IAEFX,EAAE,CAAC,qCAAqC,EAAE,MAAM;MAC9C,MAAM;QAAEY;MAAU,CAAC,GAAG,IAAAX,iBAAM,EAC1Bb,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEV,SAAU;QAACqB,SAAS,EAAC,cAAc;QAAAV,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAC9D,CAAC;MAED,MAAMO,SAAS,GAAGF,SAAS,CAACG,aAAa,CAAC,aAAa,CAAC;MACxDP,MAAM,CAACM,SAAS,CAAC,CAACE,WAAW,CAAC,cAAc,CAAC;IAC/C,CAAC,CAAC;IAEFhB,EAAE,CAAC,gCAAgC,EAAE,MAAM;MACzC,IAAAC,iBAAM,EACJb,KAAA,CAACF,eAAA,CAAAG,OAAc;QACba,KAAK,EAAEV,SAAU;QACjB,eAAY,sBAAsB;QAAAW,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CACnC,CACH,CAAC;MAEDC,MAAM,CAACC,iBAAM,CAACQ,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAACN,iBAAiB,CAAC,CAAC;IACxE,CAAC,CAAC;IAEFX,EAAE,CAAC,6CAA6C,EAAE,MAAM;MACtD,IAAAC,iBAAM,EAACb,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEV,SAAU;QAAAW,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,CAAC;MAE5C,MAAMW,cAAc,GAAGT,iBAAM,CAACU,SAAS,CAAC,QAAQ,CAAC;MACjDX,MAAM,CAACU,cAAc,CAAC,CAACE,eAAe,CAAC,YAAY,EAAE,sBAAsB,CAAC;IAC9E,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,gBAAgB,EAAE,MAAM;IAC/BS,EAAE,CAAC,sDAAsD,EAAE,MAAM;MAC/D,IAAAC,iBAAM,EAACb,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEV,SAAU;QAAC6B,SAAS,EAAE,IAAK;QAAAlB,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,CAAC;;MAE7D;MACA,MAAMe,eAAe,GAAGb,iBAAM,CAACc,YAAY,CAAC,GAAG,CAAC;MAChDf,MAAM,CAACc,eAAe,CAAC,CAACE,YAAY,CAAC,CAAC,CAAC,EAAC;;MAExC;MACA,MAAMC,SAAS,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,gBAAgB,CAAC;MAC7DnB,MAAM,CAACiB,SAAS,CAACG,MAAM,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEF7B,EAAE,CAAC,uDAAuD,EAAE,MAAM;MAChE,IAAAC,iBAAM,EAACb,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEV,SAAU;QAAC6B,SAAS,EAAE,KAAM;QAAAlB,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,CAAC;;MAE9D;MACAC,MAAM,CAACC,iBAAM,CAACqB,WAAW,CAAC,GAAG,CAAC,CAAC,CAACC,GAAG,CAACpB,iBAAiB,CAAC,CAAC;;MAEvD;MACAH,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,IAAI,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;IACpD,CAAC,CAAC;IAEFX,EAAE,CAAC,uDAAuD,EAAE,MAAM;MAChE,MAAM;QAAEgC;MAAS,CAAC,GAAG,IAAA/B,iBAAM,EACzBb,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEV,SAAU;QAAC6B,SAAS,EAAE,IAAK;QAAAlB,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CACtD,CAAC;;MAED;MACAC,MAAM,CAACC,iBAAM,CAACc,YAAY,CAAC,GAAG,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;;MAEhD;MACAQ,QAAQ,CAAC5C,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEV,SAAU;QAAC6B,SAAS,EAAE,KAAM;QAAAlB,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,CAAC;;MAEhE;MACAC,MAAM,CAACC,iBAAM,CAACqB,WAAW,CAAC,GAAG,CAAC,CAAC,CAACC,GAAG,CAACpB,iBAAiB,CAAC,CAAC;MACvDH,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,IAAI,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;IACpD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFpB,QAAQ,CAAC,eAAe,EAAE,MAAM;IAC9BS,EAAE,CAAC,qCAAqC,EAAE,MAAM;MAC9C,MAAMiC,SAA6B,GAAG;QACpCxC,aAAa,EAAE,CAAC;QAChBC,WAAW,EAAE,CAAC;QACdC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE;MACf,CAAC;MAED,IAAAK,iBAAM,EAACb,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAE+B,SAAU;QAAA9B,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,CAAC;MAE5CC,MAAM,CAACC,iBAAM,CAACc,YAAY,CAAC,GAAG,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;MAChDhB,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,IAAI,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;IACpD,CAAC,CAAC;IAEFX,EAAE,CAAC,uCAAuC,EAAE,MAAM;MAChD,MAAMkC,UAA8B,GAAG;QACrCzC,aAAa,EAAE,IAAI;QACnBC,WAAW,EAAE,GAAG;QAChBC,OAAO,EAAE,GAAG;QACZC,WAAW,EAAE;MACf,CAAC;MAED,IAAAK,iBAAM,EAACb,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEgC,UAAW;QAAA/B,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,CAAC;MAE7CC,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,MAAM,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;MACpDH,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,KAAK,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;MACnDH,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,KAAK,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;MACnDH,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,YAAY,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;IAC5D,CAAC,CAAC;IAEFX,EAAE,CAAC,8CAA8C,EAAE,MAAM;MACvD,MAAMmC,WAA+B,GAAG;QACtC1C,aAAa,EAAE,EAAE;QACjBC,WAAW,EAAE,CAAC;QACdC,OAAO,EAAE,EAAE;QACXC,WAAW,EAAE;MACf,CAAC;MAED,IAAAK,iBAAM,EAACb,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEiC,WAAY;QAAAhC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,CAAC;MAE9CC,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,KAAK,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;IACrD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFpB,QAAQ,CAAC,aAAa,EAAE,MAAM;IAC5BS,EAAE,CAAC,oCAAoC,EAAE,MAAM;MAC7C,MAAM;QAAEgC;MAAS,CAAC,GAAG,IAAA/B,iBAAM,EAACb,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEV,SAAU;QAAAW,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,CAAC;;MAEjE;MACAyB,QAAQ,CAAC5C,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEV,SAAU;QAAAW,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,CAAC;;MAE9C;MACAC,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;IAChE,CAAC,CAAC;IAEFX,EAAE,CAAC,yCAAyC,EAAE,MAAM;MAClD,MAAM;QAAEgC;MAAS,CAAC,GAAG,IAAA/B,iBAAM,EAACb,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEV,SAAU;QAAAW,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,CAAC;;MAEjE;MACAyB,QAAQ,CAAC5C,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEV,SAAU;QAACqB,SAAS,EAAC,WAAW;QAAAV,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,CAAC;;MAEpE;MACAC,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,IAAI,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;IACpD,CAAC,CAAC;IAEFX,EAAE,CAAC,iCAAiC,EAAE,MAAM;MAC1C,MAAM;QAAEgC;MAAS,CAAC,GAAG,IAAA/B,iBAAM,EAACb,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEV,SAAU;QAAAW,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,CAAC;MAEjE,MAAM6B,QAA4B,GAAG;QACnC3C,aAAa,EAAE,EAAE;QACjBC,WAAW,EAAE,CAAC;QACdC,OAAO,EAAE,EAAE;QACXC,WAAW,EAAE;MACf,CAAC;MAEDoC,QAAQ,CAAC5C,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEkC,QAAS;QAAAjC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,CAAC;;MAE7C;MACAC,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,IAAI,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;MAClDH,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,GAAG,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;MACjDH,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,IAAI,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;MAClDH,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,UAAU,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;IAC1D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFpB,QAAQ,CAAC,2BAA2B,EAAE,MAAM;IAC1CS,EAAE,CAAC,4CAA4C,EAAE,MAAM;MACrD,IAAAC,iBAAM,EAACb,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEV,SAAU;QAAAW,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,CAAC;;MAE5C;MACAC,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,IAAI,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC,EAAC;MACnDH,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,IAAI,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC,EAAC;MACnDH,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,IAAI,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC,EAAC;MACnDH,MAAM,CAACC,iBAAM,CAACC,SAAS,CAAC,IAAI,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC,EAAC;IACrD,CAAC,CAAC;IAEFX,EAAE,CAAC,6CAA6C,EAAE,MAAM;MACtD,MAAM;QAAEY;MAAU,CAAC,GAAG,IAAAX,iBAAM,EAACb,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEV,SAAU;QAAAW,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,CAAC;MAElE,MAAMO,SAAS,GAAGF,SAAS,CAACG,aAAa,CAAC,aAAa,CAAC;MACxDP,MAAM,CAACM,SAAS,CAAC,CAACH,iBAAiB,CAAC,CAAC;MAErC,MAAM0B,SAAS,GAAGzB,SAAS,CAACe,gBAAgB,CAAC,YAAY,CAAC;MAC1DnB,MAAM,CAAC6B,SAAS,CAAC,CAACb,YAAY,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjC,QAAQ,CAAC,gBAAgB,EAAE,MAAM;IAC/BS,EAAE,CAAC,0CAA0C,EAAE,MAAM;MACnD;MACA,MAAMsC,cAAc,GAAG;QACrB7C,aAAa,EAAE8C,SAAgB;QAC/B7C,WAAW,EAAE6C,SAAgB;QAC7B5C,OAAO,EAAE4C,SAAgB;QACzB3C,WAAW,EAAE2C;MACf,CAAC;MAED/B,MAAM,CAAC,MAAM;QACX,IAAAP,iBAAM,EAACb,KAAA,CAACF,eAAA,CAAAG,OAAc;UAACa,KAAK,EAAEoC,cAAe;UAAAnC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAlB,YAAA;YAAAmB,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC,CAAC;MACnD,CAAC,CAAC,CAACwB,GAAG,CAACS,OAAO,CAAC,CAAC;IAClB,CAAC,CAAC;IAEFxC,EAAE,CAAC,qCAAqC,EAAE,MAAM;MAC9C,MAAMyC,SAAS,GAAG;QAChBhD,aAAa,EAAE,IAAW;QAC1BC,WAAW,EAAE,IAAW;QACxBC,OAAO,EAAE,IAAW;QACpBC,WAAW,EAAE;MACf,CAAC;MAEDY,MAAM,CAAC,MAAM;QACX,IAAAP,iBAAM,EAACb,KAAA,CAACF,eAAA,CAAAG,OAAc;UAACa,KAAK,EAAEuC,SAAU;UAAAtC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAlB,YAAA;YAAAmB,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC,CAAC;MAC9C,CAAC,CAAC,CAACwB,GAAG,CAACS,OAAO,CAAC,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjD,QAAQ,CAAC,eAAe,EAAE,MAAM;IAC9BS,EAAE,CAAC,gCAAgC,EAAE,MAAM;MACzC,IAAAC,iBAAM,EAACb,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEV,SAAU;QAAAW,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,CAAC;MAE5C,MAAMmC,MAAM,GAAGjC,iBAAM,CAACU,SAAS,CAAC,QAAQ,CAAC;MACzCX,MAAM,CAACkC,MAAM,CAAC,CAACtB,eAAe,CAAC,YAAY,EAAE,sBAAsB,CAAC;IACtE,CAAC,CAAC;IAEFpB,EAAE,CAAC,8BAA8B,EAAE,MAAM;MACvC,IAAAC,iBAAM,EAACb,KAAA,CAACF,eAAA,CAAAG,OAAc;QAACa,KAAK,EAAEV,SAAU;QAAAW,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAlB,YAAA;UAAAmB,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC,CAAC;;MAE5C;MACA;MACA,MAAMW,cAAc,GAAGT,iBAAM,CAACU,SAAS,CAAC,QAAQ,CAAC;MACjDX,MAAM,CAACU,cAAc,CAAC,CAACP,iBAAiB,CAAC,CAAC;IAC5C,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}