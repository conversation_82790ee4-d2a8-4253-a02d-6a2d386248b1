/**
 * Product Version Matching Algorithm
 * 
 * Implements sophisticated product version matching logic within product context:
 * 1. Version String + Product Match (confidence: 90%)
 * 2. Release Date + Product Match (confidence: 75%)
 */

import { executeQuery } from '@/lib/database';

// Interfaces for product version matching
export interface TenantProductVersion {
  id: string;
  product_id: string;
  version: string;
  release_date?: string;
  notes?: string;
  is_current?: boolean;
  global_product_id?: string; // If product is already matched
}

export interface GlobalProductVersion {
  id: string;
  product_id: string;
  version_number: string;
  release_date?: string;
  product_name: string;
  vendor_name: string;
}

export interface ProductVersionMatch {
  global_product_version_id: string;
  confidence_score: number;
  match_reasons: string[];
  match_details: {
    version_match?: boolean;
    version_similarity?: number;
    release_date_match?: boolean;
    date_proximity_days?: number;
  };
}

export interface ProductVersionMatchResult {
  tenant_product_version_id: string;
  matches: ProductVersionMatch[];
  best_match?: ProductVersionMatch;
  recommendation: 'auto_match' | 'manual_review' | 'create_new' | 'product_not_matched';
}

export class ProductVersionMatcher {
  // Confidence thresholds
  private static readonly AUTO_MATCH_THRESHOLD = 0.85;
  private static readonly MANUAL_REVIEW_THRESHOLD = 0.50;
  
  /**
   * Find matches for a tenant product version against global product versions
   */
  static async findMatches(tenantProductVersion: TenantProductVersion): Promise<ProductVersionMatchResult> {
    // Check if product is already matched
    if (!tenantProductVersion.global_product_id) {
      return {
        tenant_product_version_id: tenantProductVersion.id,
        matches: [],
        recommendation: 'product_not_matched'
      };
    }
    
    const matches: ProductVersionMatch[] = [];
    
    // Get all active global product versions for the matched product
    const globalVersionsResult = await executeQuery(`
      SELECT 
        pv.id, pv.product_id, pv.version_number, pv.release_date,
        p.canonical_name as product_name,
        v.canonical_name as vendor_name
      FROM metadata.global_product_versions pv
      JOIN metadata.global_products p ON pv.product_id = p.id
      JOIN metadata.global_vendors v ON p.vendor_id = v.id
      WHERE pv.status = 'active' AND pv.product_id = $1
      ORDER BY pv.release_date DESC NULLS LAST, pv.version_number
    `, [tenantProductVersion.global_product_id]);
    
    if (!globalVersionsResult.success || !globalVersionsResult.data) {
      return {
        tenant_product_version_id: tenantProductVersion.id,
        matches: [],
        recommendation: 'create_new'
      };
    }
    
    const globalVersions: GlobalProductVersion[] = globalVersionsResult.data;
    
    // Calculate match scores for each global product version
    for (const globalVersion of globalVersions) {
      const match = this.calculateMatchScore(tenantProductVersion, globalVersion);
      if (match.confidence_score > 0) {
        matches.push(match);
      }
    }
    
    // Sort matches by confidence score (descending)
    matches.sort((a, b) => b.confidence_score - a.confidence_score);
    
    // Determine recommendation
    const bestMatch = matches.length > 0 ? matches[0] : undefined;
    let recommendation: 'auto_match' | 'manual_review' | 'create_new' | 'product_not_matched' = 'create_new';
    
    if (bestMatch) {
      if (bestMatch.confidence_score >= this.AUTO_MATCH_THRESHOLD) {
        recommendation = 'auto_match';
      } else if (bestMatch.confidence_score >= this.MANUAL_REVIEW_THRESHOLD) {
        recommendation = 'manual_review';
      }
    }
    
    return {
      tenant_product_version_id: tenantProductVersion.id,
      matches: matches.slice(0, 5), // Return top 5 matches
      best_match: bestMatch,
      recommendation
    };
  }
  
  /**
   * Calculate match score between tenant and global product version
   */
  private static calculateMatchScore(tenantVersion: TenantProductVersion, globalVersion: GlobalProductVersion): ProductVersionMatch {
    const matchReasons: string[] = [];
    const matchDetails: ProductVersionMatch['match_details'] = {};
    let totalScore = 0;
    let weightSum = 0;
    
    // 1. Version String + Product Match (confidence: 90%, weight: 60)
    const versionSimilarity = this.calculateVersionSimilarity(tenantVersion.version, globalVersion.version_number);
    if (versionSimilarity > 0.7) {
      let confidence = 0.9;
      
      // Exact match gets full confidence
      if (versionSimilarity === 1.0) {
        matchReasons.push('Exact version match');
        matchDetails.version_match = true;
      } else {
        // Partial match gets reduced confidence
        confidence = 0.75 + (0.15 * versionSimilarity);
        matchReasons.push(`Version similarity (${Math.round(versionSimilarity * 100)}%)`);
      }
      
      totalScore += confidence * 60;
      weightSum += 60;
      matchDetails.version_similarity = versionSimilarity;
    }
    
    // 2. Release Date + Product Match (confidence: 75%, weight: 40)
    if (tenantVersion.release_date && globalVersion.release_date) {
      const dateProximity = this.calculateDateProximity(tenantVersion.release_date, globalVersion.release_date);
      
      if (dateProximity.isClose) {
        let confidence = 0.75;
        
        // Exact date match gets higher confidence
        if (dateProximity.daysDifference === 0) {
          confidence = 0.85;
          matchReasons.push('Exact release date match');
          matchDetails.release_date_match = true;
        } else {
          // Close dates get reduced confidence
          confidence = Math.max(0.5, 0.75 - (dateProximity.daysDifference / 365) * 0.25);
          matchReasons.push(`Release date proximity (${dateProximity.daysDifference} days)`);
        }
        
        totalScore += confidence * 40;
        weightSum += 40;
        matchDetails.date_proximity_days = dateProximity.daysDifference;
      }
    }
    
    // Calculate final confidence score
    const confidenceScore = weightSum > 0 ? totalScore / weightSum : 0;
    
    return {
      global_product_version_id: globalVersion.id,
      confidence_score: Math.round(confidenceScore * 10000) / 10000, // Round to 4 decimal places
      match_reasons: matchReasons,
      match_details: matchDetails
    };
  }
  
  /**
   * Calculate version similarity using semantic version comparison
   */
  private static calculateVersionSimilarity(version1: string, version2: string): number {
    const normalized1 = this.normalizeVersion(version1);
    const normalized2 = this.normalizeVersion(version2);
    
    if (normalized1 === normalized2) return 1.0;
    
    // Try semantic version comparison
    const semver1 = this.parseSemanticVersion(normalized1);
    const semver2 = this.parseSemanticVersion(normalized2);
    
    if (semver1 && semver2) {
      return this.compareSemanticVersions(semver1, semver2);
    }
    
    // Fall back to string similarity
    return this.calculateStringSimilarity(normalized1, normalized2);
  }
  
  /**
   * Normalize version string for comparison
   */
  private static normalizeVersion(version: string): string {
    return version
      .toLowerCase()
      .replace(/[^\w\d\.]/g, '') // Remove non-alphanumeric except dots
      .replace(/^v/, '') // Remove leading 'v'
      .trim();
  }
  
  /**
   * Parse semantic version (major.minor.patch)
   */
  private static parseSemanticVersion(version: string): { major: number; minor: number; patch: number } | null {
    const match = version.match(/^(\d+)(?:\.(\d+))?(?:\.(\d+))?/);
    if (!match) return null;
    
    return {
      major: parseInt(match[1], 10),
      minor: parseInt(match[2] || '0', 10),
      patch: parseInt(match[3] || '0', 10)
    };
  }
  
  /**
   * Compare semantic versions and return similarity score
   */
  private static compareSemanticVersions(
    v1: { major: number; minor: number; patch: number },
    v2: { major: number; minor: number; patch: number }
  ): number {
    // Exact match
    if (v1.major === v2.major && v1.minor === v2.minor && v1.patch === v2.patch) {
      return 1.0;
    }
    
    // Same major and minor
    if (v1.major === v2.major && v1.minor === v2.minor) {
      return 0.9;
    }
    
    // Same major
    if (v1.major === v2.major) {
      return 0.7;
    }
    
    // Different major versions
    return 0.3;
  }
  
  /**
   * Calculate string similarity using Levenshtein distance
   */
  private static calculateStringSimilarity(str1: string, str2: string): number {
    const distance = this.levenshteinDistance(str1, str2);
    const maxLength = Math.max(str1.length, str2.length);
    
    return maxLength > 0 ? 1 - (distance / maxLength) : 0;
  }
  
  /**
   * Calculate Levenshtein distance between two strings
   */
  private static levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1, // deletion
          matrix[j - 1][i] + 1, // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }
  
  /**
   * Calculate date proximity between two dates
   */
  private static calculateDateProximity(date1: string, date2: string): { isClose: boolean; daysDifference: number } {
    try {
      const d1 = new Date(date1);
      const d2 = new Date(date2);
      
      const timeDifference = Math.abs(d1.getTime() - d2.getTime());
      const daysDifference = Math.ceil(timeDifference / (1000 * 3600 * 24));
      
      // Consider dates close if within 90 days
      const isClose = daysDifference <= 90;
      
      return { isClose, daysDifference };
    } catch {
      return { isClose: false, daysDifference: Infinity };
    }
  }
  
  /**
   * Test the product version matching algorithm with sample data
   */
  static async testMatching(): Promise<any> {
    // First, get a global product ID for Microsoft 365
    const productResult = await executeQuery(`
      SELECT p.id 
      FROM metadata.global_products p
      JOIN metadata.global_vendors v ON p.vendor_id = v.id
      WHERE p.canonical_name = 'Microsoft 365' AND v.canonical_name = 'Microsoft Corporation'
      LIMIT 1
    `);
    
    if (!productResult.success || !productResult.data || productResult.data.length === 0) {
      return { error: 'Microsoft 365 product not found for testing' };
    }
    
    const microsoftProductId = productResult.data[0].id;
    
    // Create a test tenant product version
    const testTenantProductVersion: TenantProductVersion = {
      id: 'test-tenant-product-version-1',
      product_id: 'tenant-product-m365',
      version: 'E5',
      release_date: '2023-01-01',
      notes: 'Enterprise E5 license',
      is_current: true,
      global_product_id: microsoftProductId
    };
    
    return await this.findMatches(testTenantProductVersion);
  }
}
