{"C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\layout.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\layout.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 18}}, "1": {"start": {"line": 12, "column": 14}, "end": {"line": 16, "column": 2}}, "2": {"start": {"line": 18, "column": 24}, "end": {"line": 26, "column": 1}}, "3": {"start": {"line": 33, "column": 2}, "end": {"line": 51, "column": 3}}}, "fnMap": {"0": {"name": "RootLayout", "decl": {"start": {"line": 28, "column": 24}, "end": {"line": 28, "column": 34}}, "loc": {"start": {"line": 32, "column": 3}, "end": {"line": 52, "column": 1}}, "line": 32}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\page.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\page.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": 24}}}, "fnMap": {"0": {"name": "Home", "decl": {"start": {"line": 3, "column": 24}, "end": {"line": 3, "column": 28}}, "loc": {"start": {"line": 3, "column": 31}, "end": {"line": 6, "column": 1}}, "line": 3}}, "branchMap": {}, "s": {"0": 0}, "f": {"0": 0}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\clients\\route.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\clients\\route.ts", "statementMap": {"0": {"start": {"line": 7, "column": 27}, "end": {"line": 15, "column": 2}}, "1": {"start": {"line": 18, "column": 27}, "end": {"line": 26, "column": 2}}, "2": {"start": {"line": 30, "column": 18}, "end": {"line": 30, "column": 39}}, "3": {"start": {"line": 31, "column": 2}, "end": {"line": 33, "column": 3}}, "4": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 73}}, "5": {"start": {"line": 35, "column": 2}, "end": {"line": 59, "column": 3}}, "6": {"start": {"line": 36, "column": 17}, "end": {"line": 36, "column": 37}}, "7": {"start": {"line": 39, "column": 4}, "end": {"line": 46, "column": 5}}, "8": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 37}}, "9": {"start": {"line": 42, "column": 6}, "end": {"line": 45, "column": 26}}, "10": {"start": {"line": 48, "column": 19}, "end": {"line": 53, "column": 6}}, "11": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 54}}, "12": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 51}}, "13": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 84}}}, "fnMap": {"0": {"name": "POST", "decl": {"start": {"line": 28, "column": 22}, "end": {"line": 28, "column": 26}}, "loc": {"start": {"line": 28, "column": 49}, "end": {"line": 60, "column": 1}}, "line": 28}}, "branchMap": {"0": {"loc": {"start": {"line": 31, "column": 2}, "end": {"line": 33, "column": 3}}, "type": "if", "locations": [{"start": {"line": 31, "column": 2}, "end": {"line": 33, "column": 3}}, {"start": {}, "end": {}}], "line": 31}, "1": {"loc": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 14}}, {"start": {"line": 31, "column": 18}, "end": {"line": 31, "column": 50}}], "line": 31}, "2": {"loc": {"start": {"line": 44, "column": 17}, "end": {"line": 44, "column": 76}}, "type": "cond-expr", "locations": [{"start": {"line": 44, "column": 42}, "end": {"line": 44, "column": 55}}, {"start": {"line": 44, "column": 58}, "end": {"line": 44, "column": 76}}], "line": 44}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\clients\\[id]\\route.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\clients\\[id]\\route.ts", "statementMap": {"0": {"start": {"line": 7, "column": 27}, "end": {"line": 15, "column": 2}}, "1": {"start": {"line": 22, "column": 18}, "end": {"line": 22, "column": 39}}, "2": {"start": {"line": 23, "column": 2}, "end": {"line": 25, "column": 3}}, "3": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 73}}, "4": {"start": {"line": 27, "column": 19}, "end": {"line": 27, "column": 28}}, "5": {"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 3}}, "6": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 82}}, "7": {"start": {"line": 32, "column": 2}, "end": {"line": 60, "column": 3}}, "8": {"start": {"line": 33, "column": 17}, "end": {"line": 33, "column": 37}}, "9": {"start": {"line": 36, "column": 4}, "end": {"line": 43, "column": 5}}, "10": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 37}}, "11": {"start": {"line": 39, "column": 6}, "end": {"line": 42, "column": 26}}, "12": {"start": {"line": 45, "column": 26}, "end": {"line": 50, "column": 6}}, "13": {"start": {"line": 52, "column": 4}, "end": {"line": 54, "column": 5}}, "14": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 79}}, "15": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": 44}}, "16": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 51}}, "17": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 84}}}, "fnMap": {"0": {"name": "PATCH", "decl": {"start": {"line": 17, "column": 22}, "end": {"line": 17, "column": 27}}, "loc": {"start": {"line": 20, "column": 2}, "end": {"line": 61, "column": 1}}, "line": 20}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 2}, "end": {"line": 25, "column": 3}}, "type": "if", "locations": [{"start": {"line": 23, "column": 2}, "end": {"line": 25, "column": 3}}, {"start": {}, "end": {}}], "line": 23}, "1": {"loc": {"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": 14}}, {"start": {"line": 23, "column": 18}, "end": {"line": 23, "column": 50}}], "line": 23}, "2": {"loc": {"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 3}}, "type": "if", "locations": [{"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 3}}, {"start": {}, "end": {}}], "line": 28}, "3": {"loc": {"start": {"line": 41, "column": 17}, "end": {"line": 41, "column": 76}}, "type": "cond-expr", "locations": [{"start": {"line": 41, "column": 42}, "end": {"line": 41, "column": 55}}, {"start": {"line": 41, "column": 58}, "end": {"line": 41, "column": 76}}], "line": 41}, "4": {"loc": {"start": {"line": 52, "column": 4}, "end": {"line": 54, "column": 5}}, "type": "if", "locations": [{"start": {"line": 52, "column": 4}, "end": {"line": 54, "column": 5}}, {"start": {}, "end": {}}], "line": 52}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\clients\\domain\\route.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\clients\\domain\\route.ts", "statementMap": {"0": {"start": {"line": 11, "column": 19}, "end": {"line": 47, "column": 2}}, "1": {"start": {"line": 13, "column": 21}, "end": {"line": 13, "column": 40}}, "2": {"start": {"line": 14, "column": 2}, "end": {"line": 16, "column": 3}}, "3": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 32}}, "4": {"start": {"line": 18, "column": 18}, "end": {"line": 18, "column": 37}}, "5": {"start": {"line": 21, "column": 16}, "end": {"line": 21, "column": 29}}, "6": {"start": {"line": 22, "column": 2}, "end": {"line": 28, "column": 3}}, "7": {"start": {"line": 23, "column": 4}, "end": {"line": 27, "column": 6}}, "8": {"start": {"line": 30, "column": 17}, "end": {"line": 30, "column": 52}}, "9": {"start": {"line": 32, "column": 2}, "end": {"line": 41, "column": 3}}, "10": {"start": {"line": 33, "column": 23}, "end": {"line": 33, "column": 113}}, "11": {"start": {"line": 34, "column": 25}, "end": {"line": 34, "column": 112}}, "12": {"start": {"line": 36, "column": 4}, "end": {"line": 40, "column": 6}}, "13": {"start": {"line": 44, "column": 2}, "end": {"line": 46, "column": 50}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 11, "column": 37}, "end": {"line": 11, "column": 38}}, "loc": {"start": {"line": 11, "column": 49}, "end": {"line": 47, "column": 1}}, "line": 11}}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 2}, "end": {"line": 16, "column": 3}}, "type": "if", "locations": [{"start": {"line": 14, "column": 2}, "end": {"line": 16, "column": 3}}, {"start": {}, "end": {}}], "line": 14}, "1": {"loc": {"start": {"line": 22, "column": 2}, "end": {"line": 28, "column": 3}}, "type": "if", "locations": [{"start": {"line": 22, "column": 2}, "end": {"line": 28, "column": 3}}, {"start": {}, "end": {}}], "line": 22}, "2": {"loc": {"start": {"line": 32, "column": 2}, "end": {"line": 41, "column": 3}}, "type": "if", "locations": [{"start": {"line": 32, "column": 2}, "end": {"line": 41, "column": 3}}, {"start": {}, "end": {}}], "line": 32}, "3": {"loc": {"start": {"line": 33, "column": 23}, "end": {"line": 33, "column": 113}}, "type": "cond-expr", "locations": [{"start": {"line": 33, "column": 58}, "end": {"line": 33, "column": 78}}, {"start": {"line": 33, "column": 81}, "end": {"line": 33, "column": 113}}], "line": 33}, "4": {"loc": {"start": {"line": 34, "column": 25}, "end": {"line": 34, "column": 112}}, "type": "cond-expr", "locations": [{"start": {"line": 34, "column": 60}, "end": {"line": 34, "column": 82}}, {"start": {"line": 34, "column": 85}, "end": {"line": 34, "column": 112}}], "line": 34}, "5": {"loc": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 18}}, {"start": {"line": 37, "column": 22}, "end": {"line": 37, "column": 46}}], "line": 37}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\dashboard\\renewals\\route.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\dashboard\\renewals\\route.ts", "statementMap": {"0": {"start": {"line": 22, "column": 19}, "end": {"line": 97, "column": 2}}, "1": {"start": {"line": 24, "column": 21}, "end": {"line": 24, "column": 40}}, "2": {"start": {"line": 25, "column": 2}, "end": {"line": 27, "column": 3}}, "3": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 32}}, "4": {"start": {"line": 29, "column": 18}, "end": {"line": 29, "column": 37}}, "5": {"start": {"line": 32, "column": 23}, "end": {"line": 32, "column": 66}}, "6": {"start": {"line": 34, "column": 2}, "end": {"line": 43, "column": 3}}, "7": {"start": {"line": 35, "column": 23}, "end": {"line": 35, "column": 119}}, "8": {"start": {"line": 36, "column": 25}, "end": {"line": 36, "column": 118}}, "9": {"start": {"line": 38, "column": 4}, "end": {"line": 42, "column": 6}}, "10": {"start": {"line": 45, "column": 17}, "end": {"line": 45, "column": 37}}, "11": {"start": {"line": 48, "column": 22}, "end": {"line": 48, "column": 61}}, "12": {"start": {"line": 50, "column": 28}, "end": {"line": 50, "column": 30}}, "13": {"start": {"line": 52, "column": 2}, "end": {"line": 83, "column": 3}}, "14": {"start": {"line": 54, "column": 26}, "end": {"line": 66, "column": 5}}, "15": {"start": {"line": 68, "column": 19}, "end": {"line": 68, "column": 89}}, "16": {"start": {"line": 70, "column": 4}, "end": {"line": 80, "column": 5}}, "17": {"start": {"line": 72, "column": 6}, "end": {"line": 79, "column": 10}}, "18": {"start": {"line": 72, "column": 48}, "end": {"line": 79, "column": 7}}, "19": {"start": {"line": 82, "column": 4}, "end": {"line": 82, "column": 87}}, "20": {"start": {"line": 86, "column": 2}, "end": {"line": 94, "column": 3}}, "21": {"start": {"line": 87, "column": 4}, "end": {"line": 93, "column": 6}}, "22": {"start": {"line": 96, "column": 2}, "end": {"line": 96, "column": 76}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 22, "column": 37}, "end": {"line": 22, "column": 38}}, "loc": {"start": {"line": 22, "column": 49}, "end": {"line": 97, "column": 1}}, "line": 22}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 72, "column": 33}, "end": {"line": 72, "column": 34}}, "loc": {"start": {"line": 72, "column": 48}, "end": {"line": 79, "column": 7}}, "line": 72}}, "branchMap": {"0": {"loc": {"start": {"line": 25, "column": 2}, "end": {"line": 27, "column": 3}}, "type": "if", "locations": [{"start": {"line": 25, "column": 2}, "end": {"line": 27, "column": 3}}, {"start": {}, "end": {}}], "line": 25}, "1": {"loc": {"start": {"line": 34, "column": 2}, "end": {"line": 43, "column": 3}}, "type": "if", "locations": [{"start": {"line": 34, "column": 2}, "end": {"line": 43, "column": 3}}, {"start": {}, "end": {}}], "line": 34}, "2": {"loc": {"start": {"line": 35, "column": 23}, "end": {"line": 35, "column": 119}}, "type": "cond-expr", "locations": [{"start": {"line": 35, "column": 64}, "end": {"line": 35, "column": 84}}, {"start": {"line": 35, "column": 87}, "end": {"line": 35, "column": 119}}], "line": 35}, "3": {"loc": {"start": {"line": 36, "column": 25}, "end": {"line": 36, "column": 118}}, "type": "cond-expr", "locations": [{"start": {"line": 36, "column": 66}, "end": {"line": 36, "column": 88}}, {"start": {"line": 36, "column": 91}, "end": {"line": 36, "column": 118}}], "line": 36}, "4": {"loc": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 24}}, {"start": {"line": 39, "column": 28}, "end": {"line": 39, "column": 64}}], "line": 39}, "5": {"loc": {"start": {"line": 52, "column": 2}, "end": {"line": 83, "column": 3}}, "type": "if", "locations": [{"start": {"line": 52, "column": 2}, "end": {"line": 83, "column": 3}}, {"start": {"line": 81, "column": 9}, "end": {"line": 83, "column": 3}}], "line": 52}, "6": {"loc": {"start": {"line": 70, "column": 4}, "end": {"line": 80, "column": 5}}, "type": "if", "locations": [{"start": {"line": 70, "column": 4}, "end": {"line": 80, "column": 5}}, {"start": {}, "end": {}}], "line": 70}, "7": {"loc": {"start": {"line": 70, "column": 8}, "end": {"line": 70, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 70, "column": 8}, "end": {"line": 70, "column": 22}}, {"start": {"line": 70, "column": 26}, "end": {"line": 70, "column": 37}}], "line": 70}, "8": {"loc": {"start": {"line": 77, "column": 17}, "end": {"line": 77, "column": 87}}, "type": "cond-expr", "locations": [{"start": {"line": 77, "column": 32}, "end": {"line": 77, "column": 75}}, {"start": {"line": 77, "column": 78}, "end": {"line": 77, "column": 87}}], "line": 77}, "9": {"loc": {"start": {"line": 78, "column": 19}, "end": {"line": 78, "column": 93}}, "type": "cond-expr", "locations": [{"start": {"line": 78, "column": 36}, "end": {"line": 78, "column": 81}}, {"start": {"line": 78, "column": 84}, "end": {"line": 78, "column": 93}}], "line": 78}, "10": {"loc": {"start": {"line": 86, "column": 2}, "end": {"line": 94, "column": 3}}, "type": "if", "locations": [{"start": {"line": 86, "column": 2}, "end": {"line": 94, "column": 3}}, {"start": {}, "end": {}}], "line": 86}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\dashboard\\stats\\route.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\dashboard\\stats\\route.ts", "statementMap": {"0": {"start": {"line": 20, "column": 19}, "end": {"line": 111, "column": 2}}, "1": {"start": {"line": 22, "column": 21}, "end": {"line": 22, "column": 40}}, "2": {"start": {"line": 23, "column": 2}, "end": {"line": 25, "column": 3}}, "3": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 32}}, "4": {"start": {"line": 27, "column": 18}, "end": {"line": 27, "column": 37}}, "5": {"start": {"line": 30, "column": 23}, "end": {"line": 30, "column": 66}}, "6": {"start": {"line": 32, "column": 2}, "end": {"line": 41, "column": 3}}, "7": {"start": {"line": 33, "column": 23}, "end": {"line": 33, "column": 119}}, "8": {"start": {"line": 34, "column": 25}, "end": {"line": 34, "column": 118}}, "9": {"start": {"line": 36, "column": 4}, "end": {"line": 40, "column": 6}}, "10": {"start": {"line": 43, "column": 17}, "end": {"line": 43, "column": 37}}, "11": {"start": {"line": 46, "column": 22}, "end": {"line": 46, "column": 61}}, "12": {"start": {"line": 48, "column": 30}, "end": {"line": 53, "column": 3}}, "13": {"start": {"line": 55, "column": 2}, "end": {"line": 108, "column": 3}}, "14": {"start": {"line": 57, "column": 20}, "end": {"line": 74, "column": 5}}, "15": {"start": {"line": 77, "column": 4}, "end": {"line": 98, "column": 5}}, "16": {"start": {"line": 78, "column": 21}, "end": {"line": 78, "column": 83}}, "17": {"start": {"line": 80, "column": 6}, "end": {"line": 97, "column": 7}}, "18": {"start": {"line": 81, "column": 22}, "end": {"line": 81, "column": 36}}, "19": {"start": {"line": 82, "column": 8}, "end": {"line": 96, "column": 9}}, "20": {"start": {"line": 84, "column": 12}, "end": {"line": 84, "column": 72}}, "21": {"start": {"line": 85, "column": 12}, "end": {"line": 85, "column": 18}}, "22": {"start": {"line": 87, "column": 12}, "end": {"line": 87, "column": 68}}, "23": {"start": {"line": 88, "column": 12}, "end": {"line": 88, "column": 18}}, "24": {"start": {"line": 90, "column": 12}, "end": {"line": 90, "column": 59}}, "25": {"start": {"line": 91, "column": 12}, "end": {"line": 91, "column": 18}}, "26": {"start": {"line": 93, "column": 27}, "end": {"line": 93, "column": 64}}, "27": {"start": {"line": 94, "column": 12}, "end": {"line": 94, "column": 62}}, "28": {"start": {"line": 95, "column": 12}, "end": {"line": 95, "column": 18}}, "29": {"start": {"line": 100, "column": 4}, "end": {"line": 100, "column": 87}}, "30": {"start": {"line": 102, "column": 4}, "end": {"line": 107, "column": 6}}, "31": {"start": {"line": 110, "column": 2}, "end": {"line": 110, "column": 85}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 20, "column": 37}, "end": {"line": 20, "column": 38}}, "loc": {"start": {"line": 20, "column": 49}, "end": {"line": 111, "column": 1}}, "line": 20}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 2}, "end": {"line": 25, "column": 3}}, "type": "if", "locations": [{"start": {"line": 23, "column": 2}, "end": {"line": 25, "column": 3}}, {"start": {}, "end": {}}], "line": 23}, "1": {"loc": {"start": {"line": 32, "column": 2}, "end": {"line": 41, "column": 3}}, "type": "if", "locations": [{"start": {"line": 32, "column": 2}, "end": {"line": 41, "column": 3}}, {"start": {}, "end": {}}], "line": 32}, "2": {"loc": {"start": {"line": 33, "column": 23}, "end": {"line": 33, "column": 119}}, "type": "cond-expr", "locations": [{"start": {"line": 33, "column": 64}, "end": {"line": 33, "column": 84}}, {"start": {"line": 33, "column": 87}, "end": {"line": 33, "column": 119}}], "line": 33}, "3": {"loc": {"start": {"line": 34, "column": 25}, "end": {"line": 34, "column": 118}}, "type": "cond-expr", "locations": [{"start": {"line": 34, "column": 66}, "end": {"line": 34, "column": 88}}, {"start": {"line": 34, "column": 91}, "end": {"line": 34, "column": 118}}], "line": 34}, "4": {"loc": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 24}}, {"start": {"line": 37, "column": 28}, "end": {"line": 37, "column": 64}}], "line": 37}, "5": {"loc": {"start": {"line": 55, "column": 2}, "end": {"line": 108, "column": 3}}, "type": "if", "locations": [{"start": {"line": 55, "column": 2}, "end": {"line": 108, "column": 3}}, {"start": {"line": 99, "column": 9}, "end": {"line": 108, "column": 3}}], "line": 55}, "6": {"loc": {"start": {"line": 80, "column": 6}, "end": {"line": 97, "column": 7}}, "type": "if", "locations": [{"start": {"line": 80, "column": 6}, "end": {"line": 97, "column": 7}}, {"start": {}, "end": {}}], "line": 80}, "7": {"loc": {"start": {"line": 80, "column": 10}, "end": {"line": 80, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 80, "column": 10}, "end": {"line": 80, "column": 24}}, {"start": {"line": 80, "column": 28}, "end": {"line": 80, "column": 39}}, {"start": {"line": 80, "column": 43}, "end": {"line": 80, "column": 65}}], "line": 80}, "8": {"loc": {"start": {"line": 82, "column": 8}, "end": {"line": 96, "column": 9}}, "type": "switch", "locations": [{"start": {"line": 83, "column": 10}, "end": {"line": 85, "column": 18}}, {"start": {"line": 86, "column": 10}, "end": {"line": 88, "column": 18}}, {"start": {"line": 89, "column": 10}, "end": {"line": 91, "column": 18}}, {"start": {"line": 92, "column": 10}, "end": {"line": 95, "column": 18}}], "line": 82}, "9": {"loc": {"start": {"line": 84, "column": 43}, "end": {"line": 84, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 84, "column": 43}, "end": {"line": 84, "column": 63}}, {"start": {"line": 84, "column": 67}, "end": {"line": 84, "column": 70}}], "line": 84}, "10": {"loc": {"start": {"line": 87, "column": 41}, "end": {"line": 87, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 87, "column": 41}, "end": {"line": 87, "column": 59}}, {"start": {"line": 87, "column": 63}, "end": {"line": 87, "column": 66}}], "line": 87}, "11": {"loc": {"start": {"line": 90, "column": 37}, "end": {"line": 90, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 90, "column": 37}, "end": {"line": 90, "column": 50}}, {"start": {"line": 90, "column": 54}, "end": {"line": 90, "column": 57}}], "line": 90}, "12": {"loc": {"start": {"line": 93, "column": 38}, "end": {"line": 93, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 93, "column": 38}, "end": {"line": 93, "column": 56}}, {"start": {"line": 93, "column": 60}, "end": {"line": 93, "column": 63}}], "line": 93}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0, 0], "8": [0, 0, 0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\health\\database\\route.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\health\\database\\route.ts", "statementMap": {"0": {"start": {"line": 18, "column": 19}, "end": {"line": 49, "column": 2}}, "1": {"start": {"line": 20, "column": 21}, "end": {"line": 20, "column": 40}}, "2": {"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 3}}, "3": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 32}}, "4": {"start": {"line": 26, "column": 17}, "end": {"line": 26, "column": 44}}, "5": {"start": {"line": 28, "column": 2}, "end": {"line": 34, "column": 3}}, "6": {"start": {"line": 29, "column": 4}, "end": {"line": 33, "column": 6}}, "7": {"start": {"line": 36, "column": 2}, "end": {"line": 48, "column": 53}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 18, "column": 37}, "end": {"line": 18, "column": 38}}, "loc": {"start": {"line": 18, "column": 49}, "end": {"line": 49, "column": 1}}, "line": 18}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 3}}, "type": "if", "locations": [{"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 3}}, {"start": {}, "end": {}}], "line": 21}, "1": {"loc": {"start": {"line": 28, "column": 2}, "end": {"line": 34, "column": 3}}, "type": "if", "locations": [{"start": {"line": 28, "column": 2}, "end": {"line": 34, "column": 3}}, {"start": {}, "end": {}}], "line": 28}, "2": {"loc": {"start": {"line": 43, "column": 29}, "end": {"line": 45, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 44, "column": 10}, "end": {"line": 44, "column": 106}}, {"start": {"line": 45, "column": 10}, "end": {"line": 45, "column": 11}}], "line": 43}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\tenants\\domain\\route.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\tenants\\domain\\route.ts", "statementMap": {"0": {"start": {"line": 6, "column": 22}, "end": {"line": 8, "column": 2}}, "1": {"start": {"line": 12, "column": 19}, "end": {"line": 12, "column": 37}}, "2": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": 51}}, "3": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 54}}, "4": {"start": {"line": 16, "column": 23}, "end": {"line": 16, "column": 51}}, "5": {"start": {"line": 17, "column": 17}, "end": {"line": 17, "column": 43}}, "6": {"start": {"line": 20, "column": 2}, "end": {"line": 27, "column": 3}}, "7": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 36}}, "8": {"start": {"line": 23, "column": 4}, "end": {"line": 26, "column": 24}}, "9": {"start": {"line": 29, "column": 2}, "end": {"line": 31, "column": 3}}, "10": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": 89}}, "11": {"start": {"line": 33, "column": 2}, "end": {"line": 51, "column": 3}}, "12": {"start": {"line": 34, "column": 19}, "end": {"line": 34, "column": 50}}, "13": {"start": {"line": 36, "column": 4}, "end": {"line": 38, "column": 5}}, "14": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 95}}, "15": {"start": {"line": 41, "column": 4}, "end": {"line": 47, "column": 7}}, "16": {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 59}}, "17": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 82}}}, "fnMap": {"0": {"name": "GET", "decl": {"start": {"line": 10, "column": 22}, "end": {"line": 10, "column": 25}}, "loc": {"start": {"line": 10, "column": 48}, "end": {"line": 52, "column": 1}}, "line": 10}}, "branchMap": {"0": {"loc": {"start": {"line": 25, "column": 15}, "end": {"line": 25, "column": 74}}, "type": "cond-expr", "locations": [{"start": {"line": 25, "column": 40}, "end": {"line": 25, "column": 53}}, {"start": {"line": 25, "column": 56}, "end": {"line": 25, "column": 74}}], "line": 25}, "1": {"loc": {"start": {"line": 29, "column": 2}, "end": {"line": 31, "column": 3}}, "type": "if", "locations": [{"start": {"line": 29, "column": 2}, "end": {"line": 31, "column": 3}}, {"start": {}, "end": {}}], "line": 29}, "2": {"loc": {"start": {"line": 36, "column": 4}, "end": {"line": 38, "column": 5}}, "type": "if", "locations": [{"start": {"line": 36, "column": 4}, "end": {"line": 38, "column": 5}}, {"start": {}, "end": {}}], "line": 36}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\users\\route.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\users\\route.ts", "statementMap": {"0": {"start": {"line": 47, "column": 19}, "end": {"line": 130, "column": 2}}, "1": {"start": {"line": 49, "column": 21}, "end": {"line": 49, "column": 40}}, "2": {"start": {"line": 50, "column": 2}, "end": {"line": 57, "column": 3}}, "3": {"start": {"line": 51, "column": 24}, "end": {"line": 51, "column": 75}}, "4": {"start": {"line": 52, "column": 22}, "end": {"line": 52, "column": 61}}, "5": {"start": {"line": 54, "column": 4}, "end": {"line": 56, "column": 5}}, "6": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 39}}, "7": {"start": {"line": 60, "column": 21}, "end": {"line": 60, "column": 65}}, "8": {"start": {"line": 61, "column": 2}, "end": {"line": 63, "column": 3}}, "9": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": 32}}, "10": {"start": {"line": 65, "column": 18}, "end": {"line": 65, "column": 37}}, "11": {"start": {"line": 68, "column": 27}, "end": {"line": 68, "column": 47}}, "12": {"start": {"line": 69, "column": 26}, "end": {"line": 69, "column": 77}}, "13": {"start": {"line": 71, "column": 2}, "end": {"line": 73, "column": 3}}, "14": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 36}}, "15": {"start": {"line": 75, "column": 63}, "end": {"line": 75, "column": 83}}, "16": {"start": {"line": 78, "column": 17}, "end": {"line": 78, "column": 35}}, "17": {"start": {"line": 81, "column": 28}, "end": {"line": 81, "column": 73}}, "18": {"start": {"line": 82, "column": 20}, "end": {"line": 82, "column": 84}}, "19": {"start": {"line": 84, "column": 16}, "end": {"line": 99, "column": 3}}, "20": {"start": {"line": 101, "column": 17}, "end": {"line": 105, "column": 3}}, "21": {"start": {"line": 107, "column": 2}, "end": {"line": 113, "column": 3}}, "22": {"start": {"line": 108, "column": 4}, "end": {"line": 112, "column": 6}}, "23": {"start": {"line": 115, "column": 16}, "end": {"line": 115, "column": 33}}, "24": {"start": {"line": 116, "column": 21}, "end": {"line": 116, "column": 85}}, "25": {"start": {"line": 117, "column": 21}, "end": {"line": 117, "column": 50}}, "26": {"start": {"line": 119, "column": 2}, "end": {"line": 129, "column": 37}}, "27": {"start": {"line": 120, "column": 51}, "end": {"line": 120, "column": 55}}, "28": {"start": {"line": 133, "column": 20}, "end": {"line": 219, "column": 2}}, "29": {"start": {"line": 135, "column": 21}, "end": {"line": 135, "column": 65}}, "30": {"start": {"line": 136, "column": 2}, "end": {"line": 138, "column": 3}}, "31": {"start": {"line": 137, "column": 4}, "end": {"line": 137, "column": 32}}, "32": {"start": {"line": 140, "column": 18}, "end": {"line": 140, "column": 37}}, "33": {"start": {"line": 143, "column": 22}, "end": {"line": 143, "column": 62}}, "34": {"start": {"line": 144, "column": 20}, "end": {"line": 144, "column": 58}}, "35": {"start": {"line": 146, "column": 2}, "end": {"line": 148, "column": 3}}, "36": {"start": {"line": 147, "column": 4}, "end": {"line": 147, "column": 37}}, "37": {"start": {"line": 151, "column": 25}, "end": {"line": 151, "column": 77}}, "38": {"start": {"line": 153, "column": 2}, "end": {"line": 155, "column": 3}}, "39": {"start": {"line": 154, "column": 4}, "end": {"line": 154, "column": 35}}, "40": {"start": {"line": 157, "column": 35}, "end": {"line": 157, "column": 54}}, "41": {"start": {"line": 160, "column": 28}, "end": {"line": 160, "column": 67}}, "42": {"start": {"line": 161, "column": 23}, "end": {"line": 161, "column": 84}}, "43": {"start": {"line": 163, "column": 2}, "end": {"line": 169, "column": 3}}, "44": {"start": {"line": 164, "column": 4}, "end": {"line": 168, "column": 6}}, "45": {"start": {"line": 172, "column": 22}, "end": {"line": 190, "column": 3}}, "46": {"start": {"line": 192, "column": 23}, "end": {"line": 201, "column": 3}}, "47": {"start": {"line": 203, "column": 2}, "end": {"line": 209, "column": 3}}, "48": {"start": {"line": 204, "column": 4}, "end": {"line": 208, "column": 6}}, "49": {"start": {"line": 212, "column": 2}, "end": {"line": 212, "column": 69}}, "50": {"start": {"line": 214, "column": 2}, "end": {"line": 218, "column": 4}}, "51": {"start": {"line": 223, "column": 2}, "end": {"line": 231, "column": 5}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 47, "column": 37}, "end": {"line": 47, "column": 38}}, "loc": {"start": {"line": 47, "column": 69}, "end": {"line": 130, "column": 1}}, "line": 47}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 120, "column": 21}, "end": {"line": 120, "column": 22}}, "loc": {"start": {"line": 120, "column": 51}, "end": {"line": 120, "column": 55}}, "line": 120}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 133, "column": 38}, "end": {"line": 133, "column": 39}}, "loc": {"start": {"line": 133, "column": 70}, "end": {"line": 219, "column": 1}}, "line": 133}, "3": {"name": "OPTIONS", "decl": {"start": {"line": 222, "column": 22}, "end": {"line": 222, "column": 29}}, "loc": {"start": {"line": 222, "column": 32}, "end": {"line": 232, "column": 1}}, "line": 222}}, "branchMap": {"0": {"loc": {"start": {"line": 50, "column": 2}, "end": {"line": 57, "column": 3}}, "type": "if", "locations": [{"start": {"line": 50, "column": 2}, "end": {"line": 57, "column": 3}}, {"start": {}, "end": {}}], "line": 50}, "1": {"loc": {"start": {"line": 54, "column": 4}, "end": {"line": 56, "column": 5}}, "type": "if", "locations": [{"start": {"line": 54, "column": 4}, "end": {"line": 56, "column": 5}}, {"start": {}, "end": {}}], "line": 54}, "2": {"loc": {"start": {"line": 61, "column": 2}, "end": {"line": 63, "column": 3}}, "type": "if", "locations": [{"start": {"line": 61, "column": 2}, "end": {"line": 63, "column": 3}}, {"start": {}, "end": {}}], "line": 61}, "3": {"loc": {"start": {"line": 71, "column": 2}, "end": {"line": 73, "column": 3}}, "type": "if", "locations": [{"start": {"line": 71, "column": 2}, "end": {"line": 73, "column": 3}}, {"start": {}, "end": {}}], "line": 71}, "4": {"loc": {"start": {"line": 82, "column": 20}, "end": {"line": 82, "column": 84}}, "type": "cond-expr", "locations": [{"start": {"line": 82, "column": 63}, "end": {"line": 82, "column": 69}}, {"start": {"line": 82, "column": 72}, "end": {"line": 82, "column": 84}}], "line": 82}, "5": {"loc": {"start": {"line": 82, "column": 47}, "end": {"line": 82, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 82, "column": 47}, "end": {"line": 82, "column": 53}}, {"start": {"line": 82, "column": 57}, "end": {"line": 82, "column": 59}}], "line": 82}, "6": {"loc": {"start": {"line": 107, "column": 2}, "end": {"line": 113, "column": 3}}, "type": "if", "locations": [{"start": {"line": 107, "column": 2}, "end": {"line": 113, "column": 3}}, {"start": {}, "end": {}}], "line": 107}, "7": {"loc": {"start": {"line": 115, "column": 16}, "end": {"line": 115, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 115, "column": 16}, "end": {"line": 115, "column": 27}}, {"start": {"line": 115, "column": 31}, "end": {"line": 115, "column": 33}}], "line": 115}, "8": {"loc": {"start": {"line": 116, "column": 21}, "end": {"line": 116, "column": 85}}, "type": "cond-expr", "locations": [{"start": {"line": 116, "column": 40}, "end": {"line": 116, "column": 81}}, {"start": {"line": 116, "column": 84}, "end": {"line": 116, "column": 85}}], "line": 116}, "9": {"loc": {"start": {"line": 136, "column": 2}, "end": {"line": 138, "column": 3}}, "type": "if", "locations": [{"start": {"line": 136, "column": 2}, "end": {"line": 138, "column": 3}}, {"start": {}, "end": {}}], "line": 136}, "10": {"loc": {"start": {"line": 146, "column": 2}, "end": {"line": 148, "column": 3}}, "type": "if", "locations": [{"start": {"line": 146, "column": 2}, "end": {"line": 148, "column": 3}}, {"start": {}, "end": {}}], "line": 146}, "11": {"loc": {"start": {"line": 153, "column": 2}, "end": {"line": 155, "column": 3}}, "type": "if", "locations": [{"start": {"line": 153, "column": 2}, "end": {"line": 155, "column": 3}}, {"start": {}, "end": {}}], "line": 153}, "12": {"loc": {"start": {"line": 163, "column": 2}, "end": {"line": 169, "column": 3}}, "type": "if", "locations": [{"start": {"line": 163, "column": 2}, "end": {"line": 169, "column": 3}}, {"start": {}, "end": {}}], "line": 163}, "13": {"loc": {"start": {"line": 163, "column": 6}, "end": {"line": 163, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 163, "column": 6}, "end": {"line": 163, "column": 26}}, {"start": {"line": 163, "column": 30}, "end": {"line": 163, "column": 47}}], "line": 163}, "14": {"loc": {"start": {"line": 196, "column": 6}, "end": {"line": 196, "column": 27}}, "type": "binary-expr", "locations": [{"start": {"line": 196, "column": 6}, "end": {"line": 196, "column": 19}}, {"start": {"line": 196, "column": 23}, "end": {"line": 196, "column": 27}}], "line": 196}, "15": {"loc": {"start": {"line": 197, "column": 6}, "end": {"line": 197, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 197, "column": 6}, "end": {"line": 197, "column": 25}}, {"start": {"line": 197, "column": 29}, "end": {"line": 197, "column": 33}}], "line": 197}, "16": {"loc": {"start": {"line": 198, "column": 6}, "end": {"line": 198, "column": 34}}, "type": "binary-expr", "locations": [{"start": {"line": 198, "column": 6}, "end": {"line": 198, "column": 26}}, {"start": {"line": 198, "column": 30}, "end": {"line": 198, "column": 34}}], "line": 198}, "17": {"loc": {"start": {"line": 203, "column": 2}, "end": {"line": 209, "column": 3}}, "type": "if", "locations": [{"start": {"line": 203, "column": 2}, "end": {"line": 209, "column": 3}}, {"start": {}, "end": {}}], "line": 203}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\users\\[id]\\route.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\users\\[id]\\route.ts", "statementMap": {"0": {"start": {"line": 45, "column": 19}, "end": {"line": 118, "column": 2}}, "1": {"start": {"line": 50, "column": 21}, "end": {"line": 50, "column": 40}}, "2": {"start": {"line": 51, "column": 2}, "end": {"line": 53, "column": 3}}, "3": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 32}}, "4": {"start": {"line": 55, "column": 18}, "end": {"line": 55, "column": 37}}, "5": {"start": {"line": 58, "column": 26}, "end": {"line": 58, "column": 67}}, "6": {"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}, "7": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 36}}, "8": {"start": {"line": 63, "column": 26}, "end": {"line": 63, "column": 46}}, "9": {"start": {"line": 67, "column": 18}, "end": {"line": 67, "column": 91}}, "10": {"start": {"line": 68, "column": 20}, "end": {"line": 68, "column": 41}}, "11": {"start": {"line": 70, "column": 2}, "end": {"line": 76, "column": 3}}, "12": {"start": {"line": 71, "column": 4}, "end": {"line": 75, "column": 6}}, "13": {"start": {"line": 79, "column": 16}, "end": {"line": 93, "column": 3}}, "14": {"start": {"line": 95, "column": 17}, "end": {"line": 95, "column": 60}}, "15": {"start": {"line": 97, "column": 2}, "end": {"line": 103, "column": 3}}, "16": {"start": {"line": 98, "column": 4}, "end": {"line": 102, "column": 6}}, "17": {"start": {"line": 105, "column": 2}, "end": {"line": 107, "column": 3}}, "18": {"start": {"line": 106, "column": 4}, "end": {"line": 106, "column": 42}}, "19": {"start": {"line": 110, "column": 19}, "end": {"line": 110, "column": 30}}, "20": {"start": {"line": 111, "column": 2}, "end": {"line": 115, "column": 3}}, "21": {"start": {"line": 113, "column": 4}, "end": {"line": 113, "column": 35}}, "22": {"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 40}}, "23": {"start": {"line": 117, "column": 2}, "end": {"line": 117, "column": 72}}, "24": {"start": {"line": 121, "column": 19}, "end": {"line": 246, "column": 2}}, "25": {"start": {"line": 126, "column": 21}, "end": {"line": 126, "column": 65}}, "26": {"start": {"line": 127, "column": 2}, "end": {"line": 129, "column": 3}}, "27": {"start": {"line": 128, "column": 4}, "end": {"line": 128, "column": 32}}, "28": {"start": {"line": 131, "column": 18}, "end": {"line": 131, "column": 37}}, "29": {"start": {"line": 134, "column": 26}, "end": {"line": 134, "column": 67}}, "30": {"start": {"line": 135, "column": 2}, "end": {"line": 137, "column": 3}}, "31": {"start": {"line": 136, "column": 4}, "end": {"line": 136, "column": 36}}, "32": {"start": {"line": 139, "column": 26}, "end": {"line": 139, "column": 46}}, "33": {"start": {"line": 142, "column": 25}, "end": {"line": 142, "column": 77}}, "34": {"start": {"line": 143, "column": 2}, "end": {"line": 145, "column": 3}}, "35": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 35}}, "36": {"start": {"line": 147, "column": 37}, "end": {"line": 147, "column": 56}}, "37": {"start": {"line": 150, "column": 27}, "end": {"line": 150, "column": 72}}, "38": {"start": {"line": 151, "column": 22}, "end": {"line": 154, "column": 3}}, "39": {"start": {"line": 156, "column": 2}, "end": {"line": 162, "column": 3}}, "40": {"start": {"line": 157, "column": 4}, "end": {"line": 161, "column": 6}}, "41": {"start": {"line": 164, "column": 2}, "end": {"line": 166, "column": 3}}, "42": {"start": {"line": 165, "column": 4}, "end": {"line": 165, "column": 42}}, "43": {"start": {"line": 169, "column": 33}, "end": {"line": 169, "column": 35}}, "44": {"start": {"line": 170, "column": 24}, "end": {"line": 170, "column": 26}}, "45": {"start": {"line": 171, "column": 19}, "end": {"line": 171, "column": 20}}, "46": {"start": {"line": 173, "column": 2}, "end": {"line": 176, "column": 3}}, "47": {"start": {"line": 174, "column": 4}, "end": {"line": 174, "column": 49}}, "48": {"start": {"line": 175, "column": 4}, "end": {"line": 175, "column": 33}}, "49": {"start": {"line": 178, "column": 2}, "end": {"line": 181, "column": 3}}, "50": {"start": {"line": 179, "column": 4}, "end": {"line": 179, "column": 55}}, "51": {"start": {"line": 180, "column": 4}, "end": {"line": 180, "column": 39}}, "52": {"start": {"line": 183, "column": 2}, "end": {"line": 186, "column": 3}}, "53": {"start": {"line": 184, "column": 4}, "end": {"line": 184, "column": 56}}, "54": {"start": {"line": 185, "column": 4}, "end": {"line": 185, "column": 40}}, "55": {"start": {"line": 188, "column": 2}, "end": {"line": 191, "column": 3}}, "56": {"start": {"line": 189, "column": 4}, "end": {"line": 189, "column": 50}}, "57": {"start": {"line": 190, "column": 4}, "end": {"line": 190, "column": 50}}, "58": {"start": {"line": 193, "column": 2}, "end": {"line": 199, "column": 3}}, "59": {"start": {"line": 194, "column": 4}, "end": {"line": 198, "column": 6}}, "60": {"start": {"line": 202, "column": 2}, "end": {"line": 202, "column": 54}}, "61": {"start": {"line": 203, "column": 2}, "end": {"line": 203, "column": 45}}, "62": {"start": {"line": 206, "column": 2}, "end": {"line": 206, "column": 44}}, "63": {"start": {"line": 208, "column": 22}, "end": {"line": 222, "column": 3}}, "64": {"start": {"line": 224, "column": 23}, "end": {"line": 224, "column": 74}}, "65": {"start": {"line": 226, "column": 2}, "end": {"line": 232, "column": 3}}, "66": {"start": {"line": 227, "column": 4}, "end": {"line": 231, "column": 6}}, "67": {"start": {"line": 234, "column": 2}, "end": {"line": 240, "column": 3}}, "68": {"start": {"line": 235, "column": 4}, "end": {"line": 239, "column": 6}}, "69": {"start": {"line": 243, "column": 2}, "end": {"line": 243, "column": 69}}, "70": {"start": {"line": 245, "column": 2}, "end": {"line": 245, "column": 79}}, "71": {"start": {"line": 249, "column": 22}, "end": {"line": 313, "column": 2}}, "72": {"start": {"line": 254, "column": 21}, "end": {"line": 254, "column": 49}}, "73": {"start": {"line": 255, "column": 2}, "end": {"line": 257, "column": 3}}, "74": {"start": {"line": 256, "column": 4}, "end": {"line": 256, "column": 32}}, "75": {"start": {"line": 259, "column": 18}, "end": {"line": 259, "column": 37}}, "76": {"start": {"line": 262, "column": 26}, "end": {"line": 262, "column": 67}}, "77": {"start": {"line": 263, "column": 2}, "end": {"line": 265, "column": 3}}, "78": {"start": {"line": 264, "column": 4}, "end": {"line": 264, "column": 36}}, "79": {"start": {"line": 267, "column": 26}, "end": {"line": 267, "column": 46}}, "80": {"start": {"line": 270, "column": 2}, "end": {"line": 276, "column": 3}}, "81": {"start": {"line": 271, "column": 4}, "end": {"line": 275, "column": 6}}, "82": {"start": {"line": 279, "column": 22}, "end": {"line": 287, "column": 3}}, "83": {"start": {"line": 289, "column": 23}, "end": {"line": 292, "column": 3}}, "84": {"start": {"line": 294, "column": 2}, "end": {"line": 300, "column": 3}}, "85": {"start": {"line": 295, "column": 4}, "end": {"line": 299, "column": 6}}, "86": {"start": {"line": 302, "column": 2}, "end": {"line": 304, "column": 3}}, "87": {"start": {"line": 303, "column": 4}, "end": {"line": 303, "column": 42}}, "88": {"start": {"line": 307, "column": 2}, "end": {"line": 307, "column": 78}}, "89": {"start": {"line": 309, "column": 2}, "end": {"line": 312, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 45, "column": 37}, "end": {"line": 45, "column": 38}}, "loc": {"start": {"line": 48, "column": 5}, "end": {"line": 118, "column": 1}}, "line": 48}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 121, "column": 37}, "end": {"line": 121, "column": 38}}, "loc": {"start": {"line": 124, "column": 5}, "end": {"line": 246, "column": 1}}, "line": 124}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 249, "column": 40}, "end": {"line": 249, "column": 41}}, "loc": {"start": {"line": 252, "column": 5}, "end": {"line": 313, "column": 1}}, "line": 252}}, "branchMap": {"0": {"loc": {"start": {"line": 51, "column": 2}, "end": {"line": 53, "column": 3}}, "type": "if", "locations": [{"start": {"line": 51, "column": 2}, "end": {"line": 53, "column": 3}}, {"start": {}, "end": {}}], "line": 51}, "1": {"loc": {"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}, "type": "if", "locations": [{"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}, {"start": {}, "end": {}}], "line": 59}, "2": {"loc": {"start": {"line": 67, "column": 18}, "end": {"line": 67, "column": 91}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 18}, "end": {"line": 67, "column": 49}}, {"start": {"line": 67, "column": 53}, "end": {"line": 67, "column": 91}}], "line": 67}, "3": {"loc": {"start": {"line": 70, "column": 2}, "end": {"line": 76, "column": 3}}, "type": "if", "locations": [{"start": {"line": 70, "column": 2}, "end": {"line": 76, "column": 3}}, {"start": {}, "end": {}}], "line": 70}, "4": {"loc": {"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": 14}}, {"start": {"line": 70, "column": 18}, "end": {"line": 70, "column": 28}}], "line": 70}, "5": {"loc": {"start": {"line": 97, "column": 2}, "end": {"line": 103, "column": 3}}, "type": "if", "locations": [{"start": {"line": 97, "column": 2}, "end": {"line": 103, "column": 3}}, {"start": {}, "end": {}}], "line": 97}, "6": {"loc": {"start": {"line": 105, "column": 2}, "end": {"line": 107, "column": 3}}, "type": "if", "locations": [{"start": {"line": 105, "column": 2}, "end": {"line": 107, "column": 3}}, {"start": {}, "end": {}}], "line": 105}, "7": {"loc": {"start": {"line": 111, "column": 2}, "end": {"line": 115, "column": 3}}, "type": "if", "locations": [{"start": {"line": 111, "column": 2}, "end": {"line": 115, "column": 3}}, {"start": {}, "end": {}}], "line": 111}, "8": {"loc": {"start": {"line": 111, "column": 6}, "end": {"line": 111, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 111, "column": 6}, "end": {"line": 111, "column": 14}}, {"start": {"line": 111, "column": 18}, "end": {"line": 111, "column": 28}}], "line": 111}, "9": {"loc": {"start": {"line": 127, "column": 2}, "end": {"line": 129, "column": 3}}, "type": "if", "locations": [{"start": {"line": 127, "column": 2}, "end": {"line": 129, "column": 3}}, {"start": {}, "end": {}}], "line": 127}, "10": {"loc": {"start": {"line": 135, "column": 2}, "end": {"line": 137, "column": 3}}, "type": "if", "locations": [{"start": {"line": 135, "column": 2}, "end": {"line": 137, "column": 3}}, {"start": {}, "end": {}}], "line": 135}, "11": {"loc": {"start": {"line": 143, "column": 2}, "end": {"line": 145, "column": 3}}, "type": "if", "locations": [{"start": {"line": 143, "column": 2}, "end": {"line": 145, "column": 3}}, {"start": {}, "end": {}}], "line": 143}, "12": {"loc": {"start": {"line": 156, "column": 2}, "end": {"line": 162, "column": 3}}, "type": "if", "locations": [{"start": {"line": 156, "column": 2}, "end": {"line": 162, "column": 3}}, {"start": {}, "end": {}}], "line": 156}, "13": {"loc": {"start": {"line": 164, "column": 2}, "end": {"line": 166, "column": 3}}, "type": "if", "locations": [{"start": {"line": 164, "column": 2}, "end": {"line": 166, "column": 3}}, {"start": {}, "end": {}}], "line": 164}, "14": {"loc": {"start": {"line": 173, "column": 2}, "end": {"line": 176, "column": 3}}, "type": "if", "locations": [{"start": {"line": 173, "column": 2}, "end": {"line": 176, "column": 3}}, {"start": {}, "end": {}}], "line": 173}, "15": {"loc": {"start": {"line": 178, "column": 2}, "end": {"line": 181, "column": 3}}, "type": "if", "locations": [{"start": {"line": 178, "column": 2}, "end": {"line": 181, "column": 3}}, {"start": {}, "end": {}}], "line": 178}, "16": {"loc": {"start": {"line": 183, "column": 2}, "end": {"line": 186, "column": 3}}, "type": "if", "locations": [{"start": {"line": 183, "column": 2}, "end": {"line": 186, "column": 3}}, {"start": {}, "end": {}}], "line": 183}, "17": {"loc": {"start": {"line": 188, "column": 2}, "end": {"line": 191, "column": 3}}, "type": "if", "locations": [{"start": {"line": 188, "column": 2}, "end": {"line": 191, "column": 3}}, {"start": {}, "end": {}}], "line": 188}, "18": {"loc": {"start": {"line": 193, "column": 2}, "end": {"line": 199, "column": 3}}, "type": "if", "locations": [{"start": {"line": 193, "column": 2}, "end": {"line": 199, "column": 3}}, {"start": {}, "end": {}}], "line": 193}, "19": {"loc": {"start": {"line": 226, "column": 2}, "end": {"line": 232, "column": 3}}, "type": "if", "locations": [{"start": {"line": 226, "column": 2}, "end": {"line": 232, "column": 3}}, {"start": {}, "end": {}}], "line": 226}, "20": {"loc": {"start": {"line": 234, "column": 2}, "end": {"line": 240, "column": 3}}, "type": "if", "locations": [{"start": {"line": 234, "column": 2}, "end": {"line": 240, "column": 3}}, {"start": {}, "end": {}}], "line": 234}, "21": {"loc": {"start": {"line": 255, "column": 2}, "end": {"line": 257, "column": 3}}, "type": "if", "locations": [{"start": {"line": 255, "column": 2}, "end": {"line": 257, "column": 3}}, {"start": {}, "end": {}}], "line": 255}, "22": {"loc": {"start": {"line": 263, "column": 2}, "end": {"line": 265, "column": 3}}, "type": "if", "locations": [{"start": {"line": 263, "column": 2}, "end": {"line": 265, "column": 3}}, {"start": {}, "end": {}}], "line": 263}, "23": {"loc": {"start": {"line": 270, "column": 2}, "end": {"line": 276, "column": 3}}, "type": "if", "locations": [{"start": {"line": 270, "column": 2}, "end": {"line": 276, "column": 3}}, {"start": {}, "end": {}}], "line": 270}, "24": {"loc": {"start": {"line": 294, "column": 2}, "end": {"line": 300, "column": 3}}, "type": "if", "locations": [{"start": {"line": 294, "column": 2}, "end": {"line": 300, "column": 3}}, {"start": {}, "end": {}}], "line": 294}, "25": {"loc": {"start": {"line": 302, "column": 2}, "end": {"line": 304, "column": 3}}, "type": "if", "locations": [{"start": {"line": 302, "column": 2}, "end": {"line": 304, "column": 3}}, {"start": {}, "end": {}}], "line": 302}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\callback\\page.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\callback\\page.tsx", "statementMap": {"0": {"start": {"line": 10, "column": 17}, "end": {"line": 10, "column": 28}}, "1": {"start": {"line": 11, "column": 23}, "end": {"line": 11, "column": 40}}, "2": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 57}}, "3": {"start": {"line": 13, "column": 30}, "end": {"line": 13, "column": 63}}, "4": {"start": {"line": 15, "column": 2}, "end": {"line": 97, "column": 28}}, "5": {"start": {"line": 16, "column": 20}, "end": {"line": 16, "column": 24}}, "6": {"start": {"line": 19, "column": 6}, "end": {"line": 89, "column": 7}}, "7": {"start": {"line": 20, "column": 8}, "end": {"line": 20, "column": 71}}, "8": {"start": {"line": 23, "column": 8}, "end": {"line": 23, "column": 26}}, "9": {"start": {"line": 26, "column": 21}, "end": {"line": 26, "column": 45}}, "10": {"start": {"line": 27, "column": 8}, "end": {"line": 29, "column": 9}}, "11": {"start": {"line": 28, "column": 10}, "end": {"line": 28, "column": 63}}, "12": {"start": {"line": 31, "column": 8}, "end": {"line": 31, "column": 79}}, "13": {"start": {"line": 33, "column": 8}, "end": {"line": 80, "column": 9}}, "14": {"start": {"line": 35, "column": 34}, "end": {"line": 35, "column": 58}}, "15": {"start": {"line": 36, "column": 10}, "end": {"line": 42, "column": 11}}, "16": {"start": {"line": 37, "column": 12}, "end": {"line": 37, "column": 77}}, "17": {"start": {"line": 38, "column": 12}, "end": {"line": 38, "column": 69}}, "18": {"start": {"line": 39, "column": 12}, "end": {"line": 39, "column": 60}}, "19": {"start": {"line": 40, "column": 12}, "end": {"line": 40, "column": 38}}, "20": {"start": {"line": 41, "column": 12}, "end": {"line": 41, "column": 19}}, "21": {"start": {"line": 45, "column": 10}, "end": {"line": 45, "column": 37}}, "22": {"start": {"line": 46, "column": 10}, "end": {"line": 46, "column": 55}}, "23": {"start": {"line": 49, "column": 26}, "end": {"line": 49, "column": 72}}, "24": {"start": {"line": 50, "column": 10}, "end": {"line": 50, "column": 98}}, "25": {"start": {"line": 52, "column": 10}, "end": {"line": 64, "column": 11}}, "26": {"start": {"line": 53, "column": 12}, "end": {"line": 53, "column": 53}}, "27": {"start": {"line": 55, "column": 12}, "end": {"line": 55, "column": 61}}, "28": {"start": {"line": 58, "column": 12}, "end": {"line": 58, "column": 60}}, "29": {"start": {"line": 61, "column": 12}, "end": {"line": 61, "column": 38}}, "30": {"start": {"line": 63, "column": 12}, "end": {"line": 63, "column": 84}}, "31": {"start": {"line": 66, "column": 10}, "end": {"line": 66, "column": 59}}, "32": {"start": {"line": 68, "column": 10}, "end": {"line": 78, "column": 11}}, "33": {"start": {"line": 69, "column": 12}, "end": {"line": 69, "column": 79}}, "34": {"start": {"line": 71, "column": 28}, "end": {"line": 71, "column": 74}}, "35": {"start": {"line": 72, "column": 12}, "end": {"line": 75, "column": 13}}, "36": {"start": {"line": 73, "column": 14}, "end": {"line": 73, "column": 63}}, "37": {"start": {"line": 74, "column": 14}, "end": {"line": 74, "column": 62}}, "38": {"start": {"line": 76, "column": 12}, "end": {"line": 76, "column": 38}}, "39": {"start": {"line": 77, "column": 12}, "end": {"line": 77, "column": 19}}, "40": {"start": {"line": 79, "column": 10}, "end": {"line": 79, "column": 22}}, "41": {"start": {"line": 82, "column": 8}, "end": {"line": 82, "column": 53}}, "42": {"start": {"line": 83, "column": 8}, "end": {"line": 88, "column": 9}}, "43": {"start": {"line": 84, "column": 10}, "end": {"line": 84, "column": 61}}, "44": {"start": {"line": 85, "column": 10}, "end": {"line": 87, "column": 18}}, "45": {"start": {"line": 86, "column": 12}, "end": {"line": 86, "column": 33}}, "46": {"start": {"line": 92, "column": 4}, "end": {"line": 92, "column": 20}}, "47": {"start": {"line": 94, "column": 4}, "end": {"line": 96, "column": 5}}, "48": {"start": {"line": 95, "column": 6}, "end": {"line": 95, "column": 23}}, "49": {"start": {"line": 99, "column": 2}, "end": {"line": 114, "column": 3}}}, "fnMap": {"0": {"name": "CallbackPage", "decl": {"start": {"line": 9, "column": 24}, "end": {"line": 9, "column": 36}}, "loc": {"start": {"line": 9, "column": 39}, "end": {"line": 115, "column": 1}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 15, "column": 12}, "end": {"line": 15, "column": 13}}, "loc": {"start": {"line": 15, "column": 18}, "end": {"line": 97, "column": 3}}, "line": 15}, "2": {"name": "handleCallback", "decl": {"start": {"line": 18, "column": 19}, "end": {"line": 18, "column": 33}}, "loc": {"start": {"line": 18, "column": 36}, "end": {"line": 90, "column": 5}}, "line": 18}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 85, "column": 21}, "end": {"line": 85, "column": 22}}, "loc": {"start": {"line": 85, "column": 27}, "end": {"line": 87, "column": 11}}, "line": 85}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 94, "column": 11}, "end": {"line": 94, "column": 12}}, "loc": {"start": {"line": 94, "column": 17}, "end": {"line": 96, "column": 5}}, "line": 94}}, "branchMap": {"0": {"loc": {"start": {"line": 27, "column": 8}, "end": {"line": 29, "column": 9}}, "type": "if", "locations": [{"start": {"line": 27, "column": 8}, "end": {"line": 29, "column": 9}}, {"start": {}, "end": {}}], "line": 27}, "1": {"loc": {"start": {"line": 36, "column": 10}, "end": {"line": 42, "column": 11}}, "type": "if", "locations": [{"start": {"line": 36, "column": 10}, "end": {"line": 42, "column": 11}}, {"start": {}, "end": {}}], "line": 36}, "2": {"loc": {"start": {"line": 50, "column": 66}, "end": {"line": 50, "column": 96}}, "type": "cond-expr", "locations": [{"start": {"line": 50, "column": 76}, "end": {"line": 50, "column": 85}}, {"start": {"line": 50, "column": 88}, "end": {"line": 50, "column": 96}}], "line": 50}, "3": {"loc": {"start": {"line": 52, "column": 10}, "end": {"line": 64, "column": 11}}, "type": "if", "locations": [{"start": {"line": 52, "column": 10}, "end": {"line": 64, "column": 11}}, {"start": {"line": 62, "column": 17}, "end": {"line": 64, "column": 11}}], "line": 52}, "4": {"loc": {"start": {"line": 68, "column": 10}, "end": {"line": 78, "column": 11}}, "type": "if", "locations": [{"start": {"line": 68, "column": 10}, "end": {"line": 78, "column": 11}}, {"start": {}, "end": {}}], "line": 68}, "5": {"loc": {"start": {"line": 68, "column": 14}, "end": {"line": 68, "column": 74}}, "type": "binary-expr", "locations": [{"start": {"line": 68, "column": 14}, "end": {"line": 68, "column": 27}}, {"start": {"line": 68, "column": 31}, "end": {"line": 68, "column": 74}}], "line": 68}, "6": {"loc": {"start": {"line": 72, "column": 12}, "end": {"line": 75, "column": 13}}, "type": "if", "locations": [{"start": {"line": 72, "column": 12}, "end": {"line": 75, "column": 13}}, {"start": {}, "end": {}}], "line": 72}, "7": {"loc": {"start": {"line": 83, "column": 8}, "end": {"line": 88, "column": 9}}, "type": "if", "locations": [{"start": {"line": 83, "column": 8}, "end": {"line": 88, "column": 9}}, {"start": {}, "end": {}}], "line": 83}, "8": {"loc": {"start": {"line": 101, "column": 7}, "end": {"line": 112, "column": 7}}, "type": "cond-expr", "locations": [{"start": {"line": 102, "column": 8}, "end": {"line": 105, "column": 14}}, {"start": {"line": 107, "column": 8}, "end": {"line": 111, "column": 14}}], "line": 101}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\dashboard\\layout.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\dashboard\\layout.tsx", "statementMap": {"0": {"start": {"line": 13, "column": 41}, "end": {"line": 13, "column": 50}}, "1": {"start": {"line": 14, "column": 17}, "end": {"line": 14, "column": 28}}, "2": {"start": {"line": 15, "column": 44}, "end": {"line": 15, "column": 59}}, "3": {"start": {"line": 17, "column": 2}, "end": {"line": 49, "column": 57}}, "4": {"start": {"line": 18, "column": 4}, "end": {"line": 23, "column": 7}}, "5": {"start": {"line": 26, "column": 22}, "end": {"line": 26, "column": 65}}, "6": {"start": {"line": 27, "column": 25}, "end": {"line": 27, "column": 46}}, "7": {"start": {"line": 29, "column": 4}, "end": {"line": 33, "column": 5}}, "8": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 101}}, "9": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 12}}, "10": {"start": {"line": 36, "column": 4}, "end": {"line": 48, "column": 5}}, "11": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 28}}, "12": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 86}}, "13": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 27}}, "14": {"start": {"line": 40, "column": 11}, "end": {"line": 48, "column": 5}}, "15": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 82}}, "16": {"start": {"line": 44, "column": 6}, "end": {"line": 47, "column": 7}}, "17": {"start": {"line": 45, "column": 25}, "end": {"line": 45, "column": 49}}, "18": {"start": {"line": 46, "column": 8}, "end": {"line": 46, "column": 65}}, "19": {"start": {"line": 52, "column": 2}, "end": {"line": 59, "column": 3}}, "20": {"start": {"line": 53, "column": 4}, "end": {"line": 58, "column": 5}}, "21": {"start": {"line": 62, "column": 2}, "end": {"line": 69, "column": 3}}, "22": {"start": {"line": 63, "column": 4}, "end": {"line": 68, "column": 5}}, "23": {"start": {"line": 71, "column": 2}, "end": {"line": 78, "column": 3}}}, "fnMap": {"0": {"name": "DashboardLayout", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 39}}, "loc": {"start": {"line": 12, "column": 3}, "end": {"line": 79, "column": 1}}, "line": 12}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 17, "column": 12}, "end": {"line": 17, "column": 13}}, "loc": {"start": {"line": 17, "column": 18}, "end": {"line": 49, "column": 3}}, "line": 17}}, "branchMap": {"0": {"loc": {"start": {"line": 29, "column": 4}, "end": {"line": 33, "column": 5}}, "type": "if", "locations": [{"start": {"line": 29, "column": 4}, "end": {"line": 33, "column": 5}}, {"start": {}, "end": {}}], "line": 29}, "1": {"loc": {"start": {"line": 36, "column": 4}, "end": {"line": 48, "column": 5}}, "type": "if", "locations": [{"start": {"line": 36, "column": 4}, "end": {"line": 48, "column": 5}}, {"start": {"line": 40, "column": 11}, "end": {"line": 48, "column": 5}}], "line": 36}, "2": {"loc": {"start": {"line": 36, "column": 8}, "end": {"line": 36, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 36, "column": 8}, "end": {"line": 36, "column": 18}}, {"start": {"line": 36, "column": 22}, "end": {"line": 36, "column": 38}}, {"start": {"line": 36, "column": 42}, "end": {"line": 36, "column": 56}}], "line": 36}, "3": {"loc": {"start": {"line": 40, "column": 11}, "end": {"line": 48, "column": 5}}, "type": "if", "locations": [{"start": {"line": 40, "column": 11}, "end": {"line": 48, "column": 5}}, {"start": {}, "end": {}}], "line": 40}, "4": {"loc": {"start": {"line": 40, "column": 15}, "end": {"line": 40, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 40, "column": 15}, "end": {"line": 40, "column": 25}}, {"start": {"line": 40, "column": 29}, "end": {"line": 40, "column": 44}}], "line": 40}, "5": {"loc": {"start": {"line": 44, "column": 6}, "end": {"line": 47, "column": 7}}, "type": "if", "locations": [{"start": {"line": 44, "column": 6}, "end": {"line": 47, "column": 7}}, {"start": {}, "end": {}}], "line": 44}, "6": {"loc": {"start": {"line": 52, "column": 2}, "end": {"line": 59, "column": 3}}, "type": "if", "locations": [{"start": {"line": 52, "column": 2}, "end": {"line": 59, "column": 3}}, {"start": {}, "end": {}}], "line": 52}, "7": {"loc": {"start": {"line": 62, "column": 2}, "end": {"line": 69, "column": 3}}, "type": "if", "locations": [{"start": {"line": 62, "column": 2}, "end": {"line": 69, "column": 3}}, {"start": {}, "end": {}}], "line": 62}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\dashboard\\page.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\dashboard\\page.tsx", "statementMap": {"0": {"start": {"line": 38, "column": 65}, "end": {"line": 38, "column": 76}}, "1": {"start": {"line": 39, "column": 46}, "end": {"line": 39, "column": 64}}, "2": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": 40}}, "3": {"start": {"line": 44, "column": 40}, "end": {"line": 44, "column": 52}}, "4": {"start": {"line": 47, "column": 31}, "end": {"line": 47, "column": 60}}, "5": {"start": {"line": 50, "column": 23}, "end": {"line": 54, "column": 8}}, "6": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 25}}, "7": {"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 40}}, "8": {"start": {"line": 56, "column": 27}, "end": {"line": 59, "column": 8}}, "9": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 38}}, "10": {"start": {"line": 61, "column": 29}, "end": {"line": 64, "column": 8}}, "11": {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 44}}, "12": {"start": {"line": 66, "column": 24}, "end": {"line": 69, "column": 8}}, "13": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 35}}, "14": {"start": {"line": 71, "column": 32}, "end": {"line": 74, "column": 8}}, "15": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 47}}, "16": {"start": {"line": 77, "column": 26}, "end": {"line": 102, "column": 8}}, "17": {"start": {"line": 77, "column": 40}, "end": {"line": 102, "column": 3}}, "18": {"start": {"line": 105, "column": 2}, "end": {"line": 113, "column": 3}}, "19": {"start": {"line": 106, "column": 4}, "end": {"line": 112, "column": 5}}, "20": {"start": {"line": 116, "column": 2}, "end": {"line": 132, "column": 3}}, "21": {"start": {"line": 117, "column": 4}, "end": {"line": 131, "column": 5}}, "22": {"start": {"line": 125, "column": 27}, "end": {"line": 125, "column": 36}}, "23": {"start": {"line": 134, "column": 2}, "end": {"line": 198, "column": 3}}, "24": {"start": {"line": 137, "column": 8}, "end": {"line": 137, "column": 59}}}, "fnMap": {"0": {"name": "DashboardPage", "decl": {"start": {"line": 37, "column": 24}, "end": {"line": 37, "column": 37}}, "loc": {"start": {"line": 37, "column": 40}, "end": {"line": 199, "column": 1}}, "line": 37}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 50, "column": 35}, "end": {"line": 50, "column": 36}}, "loc": {"start": {"line": 50, "column": 54}, "end": {"line": 54, "column": 3}}, "line": 50}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 56, "column": 39}, "end": {"line": 56, "column": 40}}, "loc": {"start": {"line": 56, "column": 45}, "end": {"line": 59, "column": 3}}, "line": 56}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 61, "column": 41}, "end": {"line": 61, "column": 42}}, "loc": {"start": {"line": 61, "column": 63}, "end": {"line": 64, "column": 3}}, "line": 61}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 66, "column": 36}, "end": {"line": 66, "column": 37}}, "loc": {"start": {"line": 66, "column": 42}, "end": {"line": 69, "column": 3}}, "line": 66}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 71, "column": 44}, "end": {"line": 71, "column": 45}}, "loc": {"start": {"line": 71, "column": 61}, "end": {"line": 74, "column": 3}}, "line": 71}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 77, "column": 34}, "end": {"line": 77, "column": 35}}, "loc": {"start": {"line": 77, "column": 40}, "end": {"line": 102, "column": 3}}, "line": 77}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 125, "column": 21}, "end": {"line": 125, "column": 22}}, "loc": {"start": {"line": 125, "column": 27}, "end": {"line": 125, "column": 36}}, "line": 125}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 136, "column": 15}, "end": {"line": 136, "column": 16}}, "loc": {"start": {"line": 136, "column": 37}, "end": {"line": 138, "column": 7}}, "line": 136}}, "branchMap": {"0": {"loc": {"start": {"line": 105, "column": 2}, "end": {"line": 113, "column": 3}}, "type": "if", "locations": [{"start": {"line": 105, "column": 2}, "end": {"line": 113, "column": 3}}, {"start": {}, "end": {}}], "line": 105}, "1": {"loc": {"start": {"line": 105, "column": 6}, "end": {"line": 105, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 105, "column": 6}, "end": {"line": 105, "column": 19}}, {"start": {"line": 105, "column": 23}, "end": {"line": 105, "column": 32}}], "line": 105}, "2": {"loc": {"start": {"line": 116, "column": 2}, "end": {"line": 132, "column": 3}}, "type": "if", "locations": [{"start": {"line": 116, "column": 2}, "end": {"line": 132, "column": 3}}, {"start": {}, "end": {}}], "line": 116}, "3": {"loc": {"start": {"line": 116, "column": 6}, "end": {"line": 116, "column": 26}}, "type": "binary-expr", "locations": [{"start": {"line": 116, "column": 6}, "end": {"line": 116, "column": 17}}, {"start": {"line": 116, "column": 21}, "end": {"line": 116, "column": 26}}], "line": 116}, "4": {"loc": {"start": {"line": 122, "column": 46}, "end": {"line": 122, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 122, "column": 46}, "end": {"line": 122, "column": 57}}, {"start": {"line": 122, "column": 61}, "end": {"line": 122, "column": 66}}], "line": 122}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\login\\page.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\login\\page.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 28}}, "1": {"start": {"line": 11, "column": 2}, "end": {"line": 46, "column": 14}}, "2": {"start": {"line": 13, "column": 6}, "end": {"line": 42, "column": 7}}, "3": {"start": {"line": 15, "column": 26}, "end": {"line": 15, "column": 62}}, "4": {"start": {"line": 17, "column": 8}, "end": {"line": 37, "column": 9}}, "5": {"start": {"line": 19, "column": 10}, "end": {"line": 19, "column": 68}}, "6": {"start": {"line": 20, "column": 10}, "end": {"line": 20, "column": 35}}, "7": {"start": {"line": 23, "column": 10}, "end": {"line": 32, "column": 11}}, "8": {"start": {"line": 24, "column": 28}, "end": {"line": 24, "column": 52}}, "9": {"start": {"line": 25, "column": 12}, "end": {"line": 29, "column": 13}}, "10": {"start": {"line": 26, "column": 14}, "end": {"line": 26, "column": 76}}, "11": {"start": {"line": 27, "column": 14}, "end": {"line": 27, "column": 39}}, "12": {"start": {"line": 28, "column": 14}, "end": {"line": 28, "column": 20}}, "13": {"start": {"line": 31, "column": 12}, "end": {"line": 31, "column": 51}}, "14": {"start": {"line": 35, "column": 10}, "end": {"line": 35, "column": 68}}, "15": {"start": {"line": 36, "column": 10}, "end": {"line": 36, "column": 46}}, "16": {"start": {"line": 39, "column": 8}, "end": {"line": 39, "column": 52}}, "17": {"start": {"line": 41, "column": 8}, "end": {"line": 41, "column": 44}}, "18": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 26}}, "19": {"start": {"line": 49, "column": 2}, "end": {"line": 53, "column": 3}}}, "fnMap": {"0": {"name": "LoginPage", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 33}}, "loc": {"start": {"line": 8, "column": 36}, "end": {"line": 54, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 11, "column": 12}, "end": {"line": 11, "column": 13}}, "loc": {"start": {"line": 11, "column": 18}, "end": {"line": 46, "column": 3}}, "line": 11}, "2": {"name": "checkAuthAndRedirect", "decl": {"start": {"line": 12, "column": 19}, "end": {"line": 12, "column": 39}}, "loc": {"start": {"line": 12, "column": 42}, "end": {"line": 43, "column": 5}}, "line": 12}}, "branchMap": {"0": {"loc": {"start": {"line": 17, "column": 8}, "end": {"line": 37, "column": 9}}, "type": "if", "locations": [{"start": {"line": 17, "column": 8}, "end": {"line": 37, "column": 9}}, {"start": {"line": 21, "column": 15}, "end": {"line": 37, "column": 9}}], "line": 17}, "1": {"loc": {"start": {"line": 25, "column": 12}, "end": {"line": 29, "column": 13}}, "type": "if", "locations": [{"start": {"line": 25, "column": 12}, "end": {"line": 29, "column": 13}}, {"start": {}, "end": {}}], "line": 25}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\signout\\page.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\signout\\page.tsx", "statementMap": {"0": {"start": {"line": 8, "column": 17}, "end": {"line": 8, "column": 28}}, "1": {"start": {"line": 10, "column": 2}, "end": {"line": 23, "column": 14}}, "2": {"start": {"line": 12, "column": 6}, "end": {"line": 19, "column": 7}}, "3": {"start": {"line": 13, "column": 8}, "end": {"line": 13, "column": 23}}, "4": {"start": {"line": 14, "column": 8}, "end": {"line": 14, "column": 29}}, "5": {"start": {"line": 16, "column": 8}, "end": {"line": 16, "column": 47}}, "6": {"start": {"line": 18, "column": 8}, "end": {"line": 18, "column": 29}}, "7": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 19}}, "8": {"start": {"line": 25, "column": 2}, "end": {"line": 33, "column": 3}}}, "fnMap": {"0": {"name": "SignOutPage", "decl": {"start": {"line": 7, "column": 24}, "end": {"line": 7, "column": 35}}, "loc": {"start": {"line": 7, "column": 38}, "end": {"line": 34, "column": 1}}, "line": 7}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 10, "column": 12}, "end": {"line": 10, "column": 13}}, "loc": {"start": {"line": 10, "column": 18}, "end": {"line": 23, "column": 3}}, "line": 10}, "2": {"name": "handleSignOut", "decl": {"start": {"line": 11, "column": 19}, "end": {"line": 11, "column": 32}}, "loc": {"start": {"line": 11, "column": 35}, "end": {"line": 20, "column": 5}}, "line": 11}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\EnvDebugger.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\EnvDebugger.tsx", "statementMap": {"0": {"start": {"line": 6, "column": 44}, "end": {"line": 6, "column": 92}}, "1": {"start": {"line": 9, "column": 24}, "end": {"line": 14, "column": 3}}, "2": {"start": {"line": 16, "column": 2}, "end": {"line": 25, "column": 8}}, "3": {"start": {"line": 18, "column": 53}, "end": {"line": 18, "column": 55}}, "4": {"start": {"line": 19, "column": 4}, "end": {"line": 23, "column": 6}}, "5": {"start": {"line": 20, "column": 6}, "end": {"line": 22, "column": 7}}, "6": {"start": {"line": 21, "column": 8}, "end": {"line": 21, "column": 36}}, "7": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 26}}, "8": {"start": {"line": 27, "column": 2}, "end": {"line": 41, "column": 3}}}, "fnMap": {"0": {"name": "EnvDebugger", "decl": {"start": {"line": 5, "column": 24}, "end": {"line": 5, "column": 35}}, "loc": {"start": {"line": 5, "column": 38}, "end": {"line": 42, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 16, "column": 12}, "end": {"line": 16, "column": 13}}, "loc": {"start": {"line": 16, "column": 18}, "end": {"line": 25, "column": 3}}, "line": 16}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 19, "column": 37}, "end": {"line": 19, "column": 38}}, "loc": {"start": {"line": 19, "column": 44}, "end": {"line": 23, "column": 5}}, "line": 19}}, "branchMap": {"0": {"loc": {"start": {"line": 20, "column": 6}, "end": {"line": 22, "column": 7}}, "type": "if", "locations": [{"start": {"line": 20, "column": 6}, "end": {"line": 22, "column": 7}}, {"start": {}, "end": {}}], "line": 20}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\providers.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\providers.tsx", "statementMap": {"0": {"start": {"line": 7, "column": 2}, "end": {"line": 11, "column": 3}}}, "fnMap": {"0": {"name": "Providers", "decl": {"start": {"line": 6, "column": 16}, "end": {"line": 6, "column": 25}}, "loc": {"start": {"line": 6, "column": 65}, "end": {"line": 12, "column": 1}}, "line": 6}}, "branchMap": {}, "s": {"0": 0}, "f": {"0": 0}, "b": {}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\ErrorBoundary.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\ErrorBoundary.tsx", "statementMap": {"0": {"start": {"line": 33, "column": 2}, "end": {"line": 69, "column": 3}}, "1": {"start": {"line": 51, "column": 27}, "end": {"line": 51, "column": 51}}, "2": {"start": {"line": 73, "column": 42}, "end": {"line": 73, "column": 46}}, "3": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": 16}}, "4": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": 36}}, "5": {"start": {"line": 82, "column": 4}, "end": {"line": 82, "column": 36}}, "6": {"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 5}}, "7": {"start": {"line": 88, "column": 6}, "end": {"line": 88, "column": 71}}, "8": {"start": {"line": 92, "column": 4}, "end": {"line": 92, "column": 32}}, "9": {"start": {"line": 95, "column": 4}, "end": {"line": 95, "column": 42}}, "10": {"start": {"line": 98, "column": 4}, "end": {"line": 105, "column": 5}}, "11": {"start": {"line": 100, "column": 6}, "end": {"line": 104, "column": 8}}, "12": {"start": {"line": 109, "column": 46}, "end": {"line": 109, "column": 56}}, "13": {"start": {"line": 110, "column": 25}, "end": {"line": 110, "column": 35}}, "14": {"start": {"line": 113, "column": 4}, "end": {"line": 120, "column": 5}}, "15": {"start": {"line": 114, "column": 28}, "end": {"line": 114, "column": 53}}, "16": {"start": {"line": 115, "column": 33}, "end": {"line": 115, "column": 93}}, "17": {"start": {"line": 115, "column": 64}, "end": {"line": 115, "column": 92}}, "18": {"start": {"line": 117, "column": 6}, "end": {"line": 119, "column": 7}}, "19": {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 25}}, "20": {"start": {"line": 124, "column": 4}, "end": {"line": 126, "column": 5}}, "21": {"start": {"line": 125, "column": 6}, "end": {"line": 125, "column": 39}}, "22": {"start": {"line": 129, "column": 15}, "end": {"line": 131, "column": 3}}, "23": {"start": {"line": 130, "column": 4}, "end": {"line": 130, "column": 78}}, "24": {"start": {"line": 134, "column": 32}, "end": {"line": 134, "column": 42}}, "25": {"start": {"line": 135, "column": 74}, "end": {"line": 135, "column": 84}}, "26": {"start": {"line": 137, "column": 4}, "end": {"line": 154, "column": 5}}, "27": {"start": {"line": 139, "column": 6}, "end": {"line": 145, "column": 7}}, "28": {"start": {"line": 140, "column": 8}, "end": {"line": 144, "column": 9}}, "29": {"start": {"line": 147, "column": 6}, "end": {"line": 153, "column": 7}}, "30": {"start": {"line": 156, "column": 4}, "end": {"line": 156, "column": 19}}, "31": {"start": {"line": 162, "column": 28}, "end": {"line": 162, "column": 62}}, "32": {"start": {"line": 164, "column": 21}, "end": {"line": 166, "column": 8}}, "33": {"start": {"line": 165, "column": 4}, "end": {"line": 165, "column": 18}}, "34": {"start": {"line": 168, "column": 22}, "end": {"line": 170, "column": 8}}, "35": {"start": {"line": 169, "column": 4}, "end": {"line": 169, "column": 19}}, "36": {"start": {"line": 173, "column": 2}, "end": {"line": 175, "column": 3}}, "37": {"start": {"line": 174, "column": 4}, "end": {"line": 174, "column": 15}}, "38": {"start": {"line": 177, "column": 2}, "end": {"line": 177, "column": 36}}, "39": {"start": {"line": 185, "column": 27}, "end": {"line": 189, "column": 3}}, "40": {"start": {"line": 186, "column": 4}, "end": {"line": 188, "column": 20}}, "41": {"start": {"line": 191, "column": 2}, "end": {"line": 191, "column": 96}}, "42": {"start": {"line": 193, "column": 2}, "end": {"line": 193, "column": 25}}}, "fnMap": {"0": {"name": "DefaultError<PERSON><PERSON><PERSON>", "decl": {"start": {"line": 32, "column": 9}, "end": {"line": 32, "column": 29}}, "loc": {"start": {"line": 32, "column": 89}, "end": {"line": 70, "column": 1}}, "line": 32}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 51, "column": 21}, "end": {"line": 51, "column": 22}}, "loc": {"start": {"line": 51, "column": 27}, "end": {"line": 51, "column": 51}}, "line": 51}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 75, "column": 2}, "end": {"line": 75, "column": 3}}, "loc": {"start": {"line": 75, "column": 41}, "end": {"line": 78, "column": 3}}, "line": 75}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 80, "column": 2}, "end": {"line": 80, "column": 3}}, "loc": {"start": {"line": 80, "column": 68}, "end": {"line": 83, "column": 3}}, "line": 80}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 85, "column": 2}, "end": {"line": 85, "column": 3}}, "loc": {"start": {"line": 85, "column": 56}, "end": {"line": 106, "column": 3}}, "line": 85}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 108, "column": 2}, "end": {"line": 108, "column": 3}}, "loc": {"start": {"line": 108, "column": 52}, "end": {"line": 121, "column": 3}}, "line": 108}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 115, "column": 48}, "end": {"line": 115, "column": 49}}, "loc": {"start": {"line": 115, "column": 64}, "end": {"line": 115, "column": 92}}, "line": 115}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 123, "column": 2}, "end": {"line": 123, "column": 3}}, "loc": {"start": {"line": 123, "column": 25}, "end": {"line": 127, "column": 3}}, "line": 123}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 129, "column": 15}, "end": {"line": 129, "column": 16}}, "loc": {"start": {"line": 129, "column": 21}, "end": {"line": 131, "column": 3}}, "line": 129}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 133, "column": 2}, "end": {"line": 133, "column": 3}}, "loc": {"start": {"line": 133, "column": 11}, "end": {"line": 157, "column": 3}}, "line": 133}, "10": {"name": "useErrorHandler", "decl": {"start": {"line": 161, "column": 16}, "end": {"line": 161, "column": 31}}, "loc": {"start": {"line": 161, "column": 34}, "end": {"line": 178, "column": 1}}, "line": 161}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 164, "column": 39}, "end": {"line": 164, "column": 40}}, "loc": {"start": {"line": 164, "column": 45}, "end": {"line": 166, "column": 3}}, "line": 164}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 168, "column": 40}, "end": {"line": 168, "column": 41}}, "loc": {"start": {"line": 168, "column": 58}, "end": {"line": 170, "column": 3}}, "line": 168}, "13": {"name": "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decl": {"start": {"line": 181, "column": 16}, "end": {"line": 181, "column": 33}}, "loc": {"start": {"line": 184, "column": 2}, "end": {"line": 194, "column": 1}}, "line": 184}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 185, "column": 27}, "end": {"line": 185, "column": 28}}, "loc": {"start": {"line": 186, "column": 4}, "end": {"line": 188, "column": 20}}, "line": 186}}, "branchMap": {"0": {"loc": {"start": {"line": 32, "column": 51}, "end": {"line": 32, "column": 65}}, "type": "default-arg", "locations": [{"start": {"line": 32, "column": 63}, "end": {"line": 32, "column": 65}}], "line": 32}, "1": {"loc": {"start": {"line": 39, "column": 11}, "end": {"line": 39, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 39, "column": 11}, "end": {"line": 39, "column": 25}}, {"start": {"line": 39, "column": 29}, "end": {"line": 39, "column": 59}}], "line": 39}, "2": {"loc": {"start": {"line": 57, "column": 9}, "end": {"line": 66, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 57, "column": 9}, "end": {"line": 57, "column": 47}}, {"start": {"line": 57, "column": 51}, "end": {"line": 57, "column": 56}}, {"start": {"line": 58, "column": 10}, "end": {"line": 65, "column": 20}}], "line": 57}, "3": {"loc": {"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 5}}, "type": "if", "locations": [{"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 5}}, {"start": {}, "end": {}}], "line": 87}, "4": {"loc": {"start": {"line": 98, "column": 4}, "end": {"line": 105, "column": 5}}, "type": "if", "locations": [{"start": {"line": 98, "column": 4}, "end": {"line": 105, "column": 5}}, {"start": {}, "end": {}}], "line": 98}, "5": {"loc": {"start": {"line": 113, "column": 4}, "end": {"line": 120, "column": 5}}, "type": "if", "locations": [{"start": {"line": 113, "column": 4}, "end": {"line": 120, "column": 5}}, {"start": {}, "end": {}}], "line": 113}, "6": {"loc": {"start": {"line": 113, "column": 8}, "end": {"line": 113, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 113, "column": 8}, "end": {"line": 113, "column": 16}}, {"start": {"line": 113, "column": 20}, "end": {"line": 113, "column": 38}}, {"start": {"line": 113, "column": 42}, "end": {"line": 113, "column": 51}}], "line": 113}, "7": {"loc": {"start": {"line": 114, "column": 28}, "end": {"line": 114, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 114, "column": 28}, "end": {"line": 114, "column": 47}}, {"start": {"line": 114, "column": 51}, "end": {"line": 114, "column": 53}}], "line": 114}, "8": {"loc": {"start": {"line": 117, "column": 6}, "end": {"line": 119, "column": 7}}, "type": "if", "locations": [{"start": {"line": 117, "column": 6}, "end": {"line": 119, "column": 7}}, {"start": {}, "end": {}}], "line": 117}, "9": {"loc": {"start": {"line": 124, "column": 4}, "end": {"line": 126, "column": 5}}, "type": "if", "locations": [{"start": {"line": 124, "column": 4}, "end": {"line": 126, "column": 5}}, {"start": {}, "end": {}}], "line": 124}, "10": {"loc": {"start": {"line": 135, "column": 32}, "end": {"line": 135, "column": 46}}, "type": "default-arg", "locations": [{"start": {"line": 135, "column": 44}, "end": {"line": 135, "column": 46}}], "line": 135}, "11": {"loc": {"start": {"line": 137, "column": 4}, "end": {"line": 154, "column": 5}}, "type": "if", "locations": [{"start": {"line": 137, "column": 4}, "end": {"line": 154, "column": 5}}, {"start": {}, "end": {}}], "line": 137}, "12": {"loc": {"start": {"line": 139, "column": 6}, "end": {"line": 145, "column": 7}}, "type": "if", "locations": [{"start": {"line": 139, "column": 6}, "end": {"line": 145, "column": 7}}, {"start": {}, "end": {}}], "line": 139}, "13": {"loc": {"start": {"line": 173, "column": 2}, "end": {"line": 175, "column": 3}}, "type": "if", "locations": [{"start": {"line": 173, "column": 2}, "end": {"line": 175, "column": 3}}, {"start": {}, "end": {}}], "line": 173}, "14": {"loc": {"start": {"line": 191, "column": 54}, "end": {"line": 191, "column": 93}}, "type": "binary-expr", "locations": [{"start": {"line": 191, "column": 54}, "end": {"line": 191, "column": 75}}, {"start": {"line": 191, "column": 79}, "end": {"line": 191, "column": 93}}], "line": 191}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\LazyLoad.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\LazyLoad.tsx", "statementMap": {"0": {"start": {"line": 42, "column": 29}, "end": {"line": 70, "column": 2}}, "1": {"start": {"line": 50, "column": 32}, "end": {"line": 53, "column": 4}}, "2": {"start": {"line": 55, "column": 36}, "end": {"line": 55, "column": 51}}, "3": {"start": {"line": 57, "column": 2}, "end": {"line": 61, "column": 33}}, "4": {"start": {"line": 58, "column": 4}, "end": {"line": 60, "column": 5}}, "5": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 24}}, "6": {"start": {"line": 63, "column": 23}, "end": {"line": 63, "column": 63}}, "7": {"start": {"line": 65, "column": 2}, "end": {"line": 69, "column": 3}}, "8": {"start": {"line": 73, "column": 25}, "end": {"line": 131, "column": 2}}, "9": {"start": {"line": 81, "column": 32}, "end": {"line": 84, "column": 4}}, "10": {"start": {"line": 86, "column": 34}, "end": {"line": 86, "column": 49}}, "11": {"start": {"line": 87, "column": 34}, "end": {"line": 87, "column": 49}}, "12": {"start": {"line": 88, "column": 34}, "end": {"line": 88, "column": 55}}, "13": {"start": {"line": 90, "column": 2}, "end": {"line": 107, "column": 64}}, "14": {"start": {"line": 91, "column": 4}, "end": {"line": 106, "column": 5}}, "15": {"start": {"line": 92, "column": 18}, "end": {"line": 92, "column": 29}}, "16": {"start": {"line": 94, "column": 6}, "end": {"line": 98, "column": 7}}, "17": {"start": {"line": 95, "column": 8}, "end": {"line": 95, "column": 24}}, "18": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": 25}}, "19": {"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 18}}, "20": {"start": {"line": 100, "column": 6}, "end": {"line": 103, "column": 7}}, "21": {"start": {"line": 101, "column": 8}, "end": {"line": 101, "column": 25}}, "22": {"start": {"line": 102, "column": 8}, "end": {"line": 102, "column": 19}}, "23": {"start": {"line": 105, "column": 6}, "end": {"line": 105, "column": 19}}, "24": {"start": {"line": 109, "column": 2}, "end": {"line": 130, "column": 3}}, "25": {"start": {"line": 142, "column": 36}, "end": {"line": 142, "column": 47}}, "26": {"start": {"line": 143, "column": 23}, "end": {"line": 143, "column": 51}}, "27": {"start": {"line": 145, "column": 21}, "end": {"line": 145, "column": 79}}, "28": {"start": {"line": 146, "column": 19}, "end": {"line": 149, "column": 3}}, "29": {"start": {"line": 151, "column": 23}, "end": {"line": 151, "column": 60}}, "30": {"start": {"line": 152, "column": 22}, "end": {"line": 152, "column": 47}}, "31": {"start": {"line": 153, "column": 18}, "end": {"line": 153, "column": 41}}, "32": {"start": {"line": 155, "column": 23}, "end": {"line": 157, "column": 3}}, "33": {"start": {"line": 156, "column": 4}, "end": {"line": 156, "column": 43}}, "34": {"start": {"line": 159, "column": 2}, "end": {"line": 179, "column": 3}}, "35": {"start": {"line": 169, "column": 12}, "end": {"line": 174, "column": 18}}, "36": {"start": {"line": 187, "column": 24}, "end": {"line": 187, "column": 38}}, "37": {"start": {"line": 189, "column": 2}, "end": {"line": 195, "column": 4}}, "38": {"start": {"line": 190, "column": 4}, "end": {"line": 194, "column": 5}}, "39": {"start": {"line": 199, "column": 38}, "end": {"line": 229, "column": 2}}, "40": {"start": {"line": 210, "column": 32}, "end": {"line": 210, "column": 66}}, "41": {"start": {"line": 212, "column": 2}, "end": {"line": 222, "column": 24}}, "42": {"start": {"line": 213, "column": 4}, "end": {"line": 221, "column": 5}}, "43": {"start": {"line": 214, "column": 20}, "end": {"line": 216, "column": 15}}, "44": {"start": {"line": 215, "column": 8}, "end": {"line": 215, "column": 24}}, "45": {"start": {"line": 218, "column": 6}, "end": {"line": 218, "column": 38}}, "46": {"start": {"line": 218, "column": 19}, "end": {"line": 218, "column": 38}}, "47": {"start": {"line": 219, "column": 11}, "end": {"line": 221, "column": 5}}, "48": {"start": {"line": 220, "column": 6}, "end": {"line": 220, "column": 22}}, "49": {"start": {"line": 224, "column": 2}, "end": {"line": 226, "column": 3}}, "50": {"start": {"line": 225, "column": 4}, "end": {"line": 225, "column": 44}}, "51": {"start": {"line": 228, "column": 2}, "end": {"line": 228, "column": 24}}, "52": {"start": {"line": 232, "column": 27}, "end": {"line": 271, "column": 2}}, "53": {"start": {"line": 245, "column": 32}, "end": {"line": 248, "column": 4}}, "54": {"start": {"line": 250, "column": 36}, "end": {"line": 250, "column": 51}}, "55": {"start": {"line": 252, "column": 2}, "end": {"line": 256, "column": 22}}, "56": {"start": {"line": 253, "column": 4}, "end": {"line": 255, "column": 5}}, "57": {"start": {"line": 254, "column": 6}, "end": {"line": 254, "column": 24}}, "58": {"start": {"line": 258, "column": 2}, "end": {"line": 270, "column": 3}}, "59": {"start": {"line": 274, "column": 24}, "end": {"line": 329, "column": 2}}, "60": {"start": {"line": 289, "column": 38}, "end": {"line": 289, "column": 68}}, "61": {"start": {"line": 291, "column": 2}, "end": {"line": 293, "column": 17}}, "62": {"start": {"line": 292, "column": 4}, "end": {"line": 292, "column": 56}}, "63": {"start": {"line": 292, "column": 26}, "end": {"line": 292, "column": 55}}, "64": {"start": {"line": 295, "column": 2}, "end": {"line": 328, "column": 3}}, "65": {"start": {"line": 299, "column": 10}, "end": {"line": 309, "column": 19}}, "66": {"start": {"line": 301, "column": 27}, "end": {"line": 301, "column": 46}}, "67": {"start": {"line": 315, "column": 10}, "end": {"line": 324, "column": 16}}}, "fnMap": {"0": {"name": "LazyComponent", "decl": {"start": {"line": 42, "column": 43}, "end": {"line": 42, "column": 56}}, "loc": {"start": {"line": 49, "column": 23}, "end": {"line": 70, "column": 1}}, "line": 49}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 57, "column": 12}, "end": {"line": 57, "column": 13}}, "loc": {"start": {"line": 57, "column": 18}, "end": {"line": 61, "column": 3}}, "line": 57}, "2": {"name": "LazyImage", "decl": {"start": {"line": 73, "column": 39}, "end": {"line": 73, "column": 48}}, "loc": {"start": {"line": 80, "column": 19}, "end": {"line": 131, "column": 1}}, "line": 80}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 90, "column": 12}, "end": {"line": 90, "column": 13}}, "loc": {"start": {"line": 90, "column": 18}, "end": {"line": 107, "column": 3}}, "line": 90}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 94, "column": 19}, "end": {"line": 94, "column": 20}}, "loc": {"start": {"line": 94, "column": 25}, "end": {"line": 98, "column": 7}}, "line": 94}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 100, "column": 20}, "end": {"line": 100, "column": 21}}, "loc": {"start": {"line": 100, "column": 26}, "end": {"line": 103, "column": 7}}, "line": 100}, "6": {"name": "VirtualList", "decl": {"start": {"line": 134, "column": 16}, "end": {"line": 134, "column": 27}}, "loc": {"start": {"line": 141, "column": 24}, "end": {"line": 180, "column": 1}}, "line": 141}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 155, "column": 23}, "end": {"line": 155, "column": 24}}, "loc": {"start": {"line": 155, "column": 61}, "end": {"line": 157, "column": 3}}, "line": 155}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 168, "column": 28}, "end": {"line": 168, "column": 29}}, "loc": {"start": {"line": 169, "column": 12}, "end": {"line": 174, "column": 18}}, "line": 169}, "9": {"name": "createLazyRoute", "decl": {"start": {"line": 183, "column": 16}, "end": {"line": 183, "column": 31}}, "loc": {"start": {"line": 186, "column": 2}, "end": {"line": 196, "column": 1}}, "line": 186}, "10": {"name": "LazyRoute", "decl": {"start": {"line": 189, "column": 23}, "end": {"line": 189, "column": 32}}, "loc": {"start": {"line": 189, "column": 45}, "end": {"line": 195, "column": 3}}, "line": 189}, "11": {"name": "ProgressiveEnhancement", "decl": {"start": {"line": 199, "column": 52}, "end": {"line": 199, "column": 74}}, "loc": {"start": {"line": 209, "column": 3}, "end": {"line": 229, "column": 1}}, "line": 209}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 212, "column": 12}, "end": {"line": 212, "column": 13}}, "loc": {"start": {"line": 212, "column": 18}, "end": {"line": 222, "column": 3}}, "line": 212}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 214, "column": 31}, "end": {"line": 214, "column": 32}}, "loc": {"start": {"line": 214, "column": 37}, "end": {"line": 216, "column": 7}}, "line": 214}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 218, "column": 13}, "end": {"line": 218, "column": 14}}, "loc": {"start": {"line": 218, "column": 19}, "end": {"line": 218, "column": 38}}, "line": 218}, "15": {"name": "LazySection", "decl": {"start": {"line": 232, "column": 41}, "end": {"line": 232, "column": 52}}, "loc": {"start": {"line": 244, "column": 3}, "end": {"line": 271, "column": 1}}, "line": 244}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 252, "column": 12}, "end": {"line": 252, "column": 13}}, "loc": {"start": {"line": 252, "column": 18}, "end": {"line": 256, "column": 3}}, "line": 252}, "17": {"name": "LazyTabs", "decl": {"start": {"line": 274, "column": 38}, "end": {"line": 274, "column": 46}}, "loc": {"start": {"line": 288, "column": 3}, "end": {"line": 329, "column": 1}}, "line": 288}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 291, "column": 12}, "end": {"line": 291, "column": 13}}, "loc": {"start": {"line": 291, "column": 18}, "end": {"line": 293, "column": 3}}, "line": 291}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 292, "column": 18}, "end": {"line": 292, "column": 19}}, "loc": {"start": {"line": 292, "column": 26}, "end": {"line": 292, "column": 55}}, "line": 292}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 298, "column": 18}, "end": {"line": 298, "column": 19}}, "loc": {"start": {"line": 299, "column": 10}, "end": {"line": 309, "column": 19}}, "line": 299}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 301, "column": 21}, "end": {"line": 301, "column": 22}}, "loc": {"start": {"line": 301, "column": 27}, "end": {"line": 301, "column": 46}}, "line": 301}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 314, "column": 18}, "end": {"line": 314, "column": 19}}, "loc": {"start": {"line": 315, "column": 10}, "end": {"line": 324, "column": 16}}, "line": 315}}, "branchMap": {"0": {"loc": {"start": {"line": 44, "column": 2}, "end": {"line": 44, "column": 42}}, "type": "default-arg", "locations": [{"start": {"line": 44, "column": 13}, "end": {"line": 44, "column": 42}}], "line": 44}, "1": {"loc": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 17}}, "type": "default-arg", "locations": [{"start": {"line": 45, "column": 14}, "end": {"line": 45, "column": 17}}], "line": 45}, "2": {"loc": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 21}}, "type": "default-arg", "locations": [{"start": {"line": 46, "column": 15}, "end": {"line": 46, "column": 21}}], "line": 46}, "3": {"loc": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 47, "column": 16}, "end": {"line": 47, "column": 20}}], "line": 47}, "4": {"loc": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 48, "column": 14}, "end": {"line": 48, "column": 16}}], "line": 48}, "5": {"loc": {"start": {"line": 58, "column": 4}, "end": {"line": 60, "column": 5}}, "type": "if", "locations": [{"start": {"line": 58, "column": 4}, "end": {"line": 60, "column": 5}}, {"start": {}, "end": {}}], "line": 58}, "6": {"loc": {"start": {"line": 58, "column": 8}, "end": {"line": 58, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 58, "column": 8}, "end": {"line": 58, "column": 22}}, {"start": {"line": 58, "column": 26}, "end": {"line": 58, "column": 36}}], "line": 58}, "7": {"loc": {"start": {"line": 63, "column": 23}, "end": {"line": 63, "column": 63}}, "type": "cond-expr", "locations": [{"start": {"line": 63, "column": 37}, "end": {"line": 63, "column": 46}}, {"start": {"line": 63, "column": 49}, "end": {"line": 63, "column": 63}}], "line": 63}, "8": {"loc": {"start": {"line": 67, "column": 7}, "end": {"line": 67, "column": 41}}, "type": "cond-expr", "locations": [{"start": {"line": 67, "column": 22}, "end": {"line": 67, "column": 30}}, {"start": {"line": 67, "column": 33}, "end": {"line": 67, "column": 41}}], "line": 67}, "9": {"loc": {"start": {"line": 76, "column": 2}, "end": {"line": 76, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 76, "column": 14}, "end": {"line": 76, "column": 16}}], "line": 76}, "10": {"loc": {"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 320}}, "type": "default-arg", "locations": [{"start": {"line": 77, "column": 16}, "end": {"line": 77, "column": 320}}], "line": 77}, "11": {"loc": {"start": {"line": 91, "column": 4}, "end": {"line": 106, "column": 5}}, "type": "if", "locations": [{"start": {"line": 91, "column": 4}, "end": {"line": 106, "column": 5}}, {"start": {}, "end": {}}], "line": 91}, "12": {"loc": {"start": {"line": 91, "column": 8}, "end": {"line": 91, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 91, "column": 8}, "end": {"line": 91, "column": 22}}, {"start": {"line": 91, "column": 26}, "end": {"line": 91, "column": 35}}, {"start": {"line": 91, "column": 39}, "end": {"line": 91, "column": 48}}], "line": 91}, "13": {"loc": {"start": {"line": 115, "column": 10}, "end": {"line": 115, "column": 49}}, "type": "cond-expr", "locations": [{"start": {"line": 115, "column": 21}, "end": {"line": 115, "column": 34}}, {"start": {"line": 115, "column": 37}, "end": {"line": 115, "column": 49}}], "line": 115}, "14": {"loc": {"start": {"line": 119, "column": 7}, "end": {"line": 123, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 119, "column": 7}, "end": {"line": 119, "column": 16}}, {"start": {"line": 119, "column": 20}, "end": {"line": 119, "column": 29}}, {"start": {"line": 120, "column": 8}, "end": {"line": 122, "column": 14}}], "line": 119}, "15": {"loc": {"start": {"line": 124, "column": 7}, "end": {"line": 128, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 124, "column": 7}, "end": {"line": 124, "column": 15}}, {"start": {"line": 125, "column": 8}, "end": {"line": 127, "column": 14}}], "line": 124}, "16": {"loc": {"start": {"line": 139, "column": 2}, "end": {"line": 139, "column": 14}}, "type": "default-arg", "locations": [{"start": {"line": 139, "column": 13}, "end": {"line": 139, "column": 14}}], "line": 139}, "17": {"loc": {"start": {"line": 140, "column": 2}, "end": {"line": 140, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 140, "column": 14}, "end": {"line": 140, "column": 16}}], "line": 140}, "18": {"loc": {"start": {"line": 191, "column": 26}, "end": {"line": 191, "column": 81}}, "type": "cond-expr", "locations": [{"start": {"line": 191, "column": 37}, "end": {"line": 191, "column": 49}}, {"start": {"line": 191, "column": 52}, "end": {"line": 191, "column": 81}}], "line": 191}, "19": {"loc": {"start": {"line": 202, "column": 2}, "end": {"line": 202, "column": 18}}, "type": "default-arg", "locations": [{"start": {"line": 202, "column": 14}, "end": {"line": 202, "column": 18}}], "line": 202}, "20": {"loc": {"start": {"line": 203, "column": 2}, "end": {"line": 203, "column": 11}}, "type": "default-arg", "locations": [{"start": {"line": 203, "column": 10}, "end": {"line": 203, "column": 11}}], "line": 203}, "21": {"loc": {"start": {"line": 210, "column": 41}, "end": {"line": 210, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 210, "column": 41}, "end": {"line": 210, "column": 52}}, {"start": {"line": 210, "column": 56}, "end": {"line": 210, "column": 65}}], "line": 210}, "22": {"loc": {"start": {"line": 213, "column": 4}, "end": {"line": 221, "column": 5}}, "type": "if", "locations": [{"start": {"line": 213, "column": 4}, "end": {"line": 221, "column": 5}}, {"start": {"line": 219, "column": 11}, "end": {"line": 221, "column": 5}}], "line": 213}, "23": {"loc": {"start": {"line": 213, "column": 8}, "end": {"line": 213, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 213, "column": 8}, "end": {"line": 213, "column": 17}}, {"start": {"line": 213, "column": 21}, "end": {"line": 213, "column": 30}}], "line": 213}, "24": {"loc": {"start": {"line": 219, "column": 11}, "end": {"line": 221, "column": 5}}, "type": "if", "locations": [{"start": {"line": 219, "column": 11}, "end": {"line": 221, "column": 5}}, {"start": {}, "end": {}}], "line": 219}, "25": {"loc": {"start": {"line": 224, "column": 2}, "end": {"line": 226, "column": 3}}, "type": "if", "locations": [{"start": {"line": 224, "column": 2}, "end": {"line": 226, "column": 3}}, {"start": {}, "end": {}}], "line": 224}, "26": {"loc": {"start": {"line": 225, "column": 11}, "end": {"line": 225, "column": 44}}, "type": "cond-expr", "locations": [{"start": {"line": 225, "column": 22}, "end": {"line": 225, "column": 37}}, {"start": {"line": 225, "column": 40}, "end": {"line": 225, "column": 44}}], "line": 225}, "27": {"loc": {"start": {"line": 235, "column": 2}, "end": {"line": 235, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 235, "column": 14}, "end": {"line": 235, "column": 16}}], "line": 235}, "28": {"loc": {"start": {"line": 236, "column": 2}, "end": {"line": 236, "column": 17}}, "type": "default-arg", "locations": [{"start": {"line": 236, "column": 14}, "end": {"line": 236, "column": 17}}], "line": 236}, "29": {"loc": {"start": {"line": 237, "column": 2}, "end": {"line": 237, "column": 22}}, "type": "default-arg", "locations": [{"start": {"line": 237, "column": 15}, "end": {"line": 237, "column": 22}}], "line": 237}, "30": {"loc": {"start": {"line": 253, "column": 4}, "end": {"line": 255, "column": 5}}, "type": "if", "locations": [{"start": {"line": 253, "column": 4}, "end": {"line": 255, "column": 5}}, {"start": {}, "end": {}}], "line": 253}, "31": {"loc": {"start": {"line": 260, "column": 7}, "end": {"line": 268, "column": 7}}, "type": "cond-expr", "locations": [{"start": {"line": 261, "column": 8}, "end": {"line": 261, "column": 16}}, {"start": {"line": 263, "column": 8}, "end": {"line": 267, "column": 9}}], "line": 260}, "32": {"loc": {"start": {"line": 263, "column": 8}, "end": {"line": 267, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 263, "column": 8}, "end": {"line": 263, "column": 19}}, {"start": {"line": 264, "column": 10}, "end": {"line": 266, "column": 16}}], "line": 263}, "33": {"loc": {"start": {"line": 278, "column": 2}, "end": {"line": 278, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 278, "column": 14}, "end": {"line": 278, "column": 16}}], "line": 278}, "34": {"loc": {"start": {"line": 303, "column": 14}, "end": {"line": 305, "column": 53}}, "type": "cond-expr", "locations": [{"start": {"line": 304, "column": 18}, "end": {"line": 304, "column": 60}}, {"start": {"line": 305, "column": 18}, "end": {"line": 305, "column": 53}}], "line": 303}, "35": {"loc": {"start": {"line": 317, "column": 23}, "end": {"line": 317, "column": 64}}, "type": "cond-expr", "locations": [{"start": {"line": 317, "column": 46}, "end": {"line": 317, "column": 53}}, {"start": {"line": 317, "column": 56}, "end": {"line": 317, "column": 64}}], "line": 317}, "36": {"loc": {"start": {"line": 319, "column": 13}, "end": {"line": 323, "column": 13}}, "type": "cond-expr", "locations": [{"start": {"line": 320, "column": 14}, "end": {"line": 320, "column": 25}}, {"start": {"line": 322, "column": 14}, "end": {"line": 322, "column": 43}}], "line": 319}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0], "10": [0], "11": [0, 0], "12": [0, 0, 0], "13": [0, 0], "14": [0, 0, 0], "15": [0, 0], "16": [0], "17": [0], "18": [0, 0], "19": [0], "20": [0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0], "28": [0], "29": [0], "30": [0, 0], "31": [0, 0], "32": [0, 0], "33": [0], "34": [0, 0], "35": [0, 0], "36": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\LoadingStates.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\LoadingStates.tsx", "statementMap": {"0": {"start": {"line": 44, "column": 22}, "end": {"line": 49, "column": 3}}, "1": {"start": {"line": 51, "column": 23}, "end": {"line": 55, "column": 3}}, "2": {"start": {"line": 57, "column": 2}, "end": {"line": 88, "column": 3}}, "3": {"start": {"line": 100, "column": 25}, "end": {"line": 100, "column": 55}}, "4": {"start": {"line": 102, "column": 2}, "end": {"line": 112, "column": 3}}, "5": {"start": {"line": 103, "column": 4}, "end": {"line": 111, "column": 5}}, "6": {"start": {"line": 114, "column": 2}, "end": {"line": 129, "column": 3}}, "7": {"start": {"line": 117, "column": 8}, "end": {"line": 126, "column": 10}}, "8": {"start": {"line": 140, "column": 2}, "end": {"line": 158, "column": 3}}, "9": {"start": {"line": 168, "column": 2}, "end": {"line": 182, "column": 3}}, "10": {"start": {"line": 201, "column": 2}, "end": {"line": 217, "column": 3}}, "11": {"start": {"line": 232, "column": 2}, "end": {"line": 232, "column": 29}}, "12": {"start": {"line": 232, "column": 18}, "end": {"line": 232, "column": 29}}, "13": {"start": {"line": 234, "column": 2}, "end": {"line": 246, "column": 3}}, "14": {"start": {"line": 261, "column": 2}, "end": {"line": 278, "column": 3}}, "15": {"start": {"line": 264, "column": 8}, "end": {"line": 275, "column": 14}}, "16": {"start": {"line": 295, "column": 2}, "end": {"line": 325, "column": 3}}, "17": {"start": {"line": 302, "column": 16}, "end": {"line": 304, "column": 21}}, "18": {"start": {"line": 311, "column": 12}, "end": {"line": 320, "column": 17}}, "19": {"start": {"line": 313, "column": 16}, "end": {"line": 318, "column": 21}}}, "fnMap": {"0": {"name": "LoadingSpinner", "decl": {"start": {"line": 37, "column": 16}, "end": {"line": 37, "column": 30}}, "loc": {"start": {"line": 43, "column": 24}, "end": {"line": 89, "column": 1}}, "line": 43}, "1": {"name": "LoadingSkeleton", "decl": {"start": {"line": 92, "column": 16}, "end": {"line": 92, "column": 31}}, "loc": {"start": {"line": 99, "column": 25}, "end": {"line": 130, "column": 1}}, "line": 99}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 116, "column": 37}, "end": {"line": 116, "column": 38}}, "loc": {"start": {"line": 117, "column": 8}, "end": {"line": 126, "column": 10}}, "line": 117}, "3": {"name": "LoadingPage", "decl": {"start": {"line": 133, "column": 16}, "end": {"line": 133, "column": 27}}, "loc": {"start": {"line": 139, "column": 21}, "end": {"line": 159, "column": 1}}, "line": 139}, "4": {"name": "LoadingCard", "decl": {"start": {"line": 162, "column": 16}, "end": {"line": 162, "column": 27}}, "loc": {"start": {"line": 167, "column": 21}, "end": {"line": 183, "column": 1}}, "line": 167}, "5": {"name": "LoadingButton", "decl": {"start": {"line": 186, "column": 16}, "end": {"line": 186, "column": 29}}, "loc": {"start": {"line": 200, "column": 3}, "end": {"line": 218, "column": 1}}, "line": 200}, "6": {"name": "LoadingOverlay", "decl": {"start": {"line": 221, "column": 16}, "end": {"line": 221, "column": 30}}, "loc": {"start": {"line": 231, "column": 3}, "end": {"line": 247, "column": 1}}, "line": 231}, "7": {"name": "LoadingList", "decl": {"start": {"line": 250, "column": 16}, "end": {"line": 250, "column": 27}}, "loc": {"start": {"line": 260, "column": 3}, "end": {"line": 279, "column": 1}}, "line": 260}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 263, "column": 37}, "end": {"line": 263, "column": 38}}, "loc": {"start": {"line": 264, "column": 8}, "end": {"line": 275, "column": 14}}, "line": 264}, "9": {"name": "LoadingTable", "decl": {"start": {"line": 282, "column": 16}, "end": {"line": 282, "column": 28}}, "loc": {"start": {"line": 294, "column": 3}, "end": {"line": 326, "column": 1}}, "line": 294}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 301, "column": 47}, "end": {"line": 301, "column": 48}}, "loc": {"start": {"line": 302, "column": 16}, "end": {"line": 304, "column": 21}}, "line": 302}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 310, "column": 40}, "end": {"line": 310, "column": 41}}, "loc": {"start": {"line": 311, "column": 12}, "end": {"line": 320, "column": 17}}, "line": 311}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 312, "column": 47}, "end": {"line": 312, "column": 48}}, "loc": {"start": {"line": 313, "column": 16}, "end": {"line": 318, "column": 21}}, "line": 313}}, "branchMap": {"0": {"loc": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": 13}}, "type": "default-arg", "locations": [{"start": {"line": 38, "column": 9}, "end": {"line": 38, "column": 13}}], "line": 38}, "1": {"loc": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 19}}, "type": "default-arg", "locations": [{"start": {"line": 39, "column": 10}, "end": {"line": 39, "column": 19}}], "line": 39}, "2": {"loc": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 41, "column": 14}, "end": {"line": 41, "column": 16}}], "line": 41}, "3": {"loc": {"start": {"line": 62, "column": 18}, "end": {"line": 62, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 62, "column": 18}, "end": {"line": 62, "column": 22}}, {"start": {"line": 62, "column": 26}, "end": {"line": 62, "column": 35}}], "line": 62}, "4": {"loc": {"start": {"line": 84, "column": 7}, "end": {"line": 86, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 84, "column": 7}, "end": {"line": 84, "column": 11}}, {"start": {"line": 85, "column": 8}, "end": {"line": 85, "column": 67}}], "line": 84}, "5": {"loc": {"start": {"line": 93, "column": 2}, "end": {"line": 93, "column": 11}}, "type": "default-arg", "locations": [{"start": {"line": 93, "column": 10}, "end": {"line": 93, "column": 11}}], "line": 93}, "6": {"loc": {"start": {"line": 94, "column": 2}, "end": {"line": 94, "column": 17}}, "type": "default-arg", "locations": [{"start": {"line": 94, "column": 11}, "end": {"line": 94, "column": 17}}], "line": 94}, "7": {"loc": {"start": {"line": 95, "column": 2}, "end": {"line": 95, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 95, "column": 10}, "end": {"line": 95, "column": 16}}], "line": 95}, "8": {"loc": {"start": {"line": 96, "column": 2}, "end": {"line": 96, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 96, "column": 12}, "end": {"line": 96, "column": 16}}], "line": 96}, "9": {"loc": {"start": {"line": 97, "column": 2}, "end": {"line": 97, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 97, "column": 14}, "end": {"line": 97, "column": 16}}], "line": 97}, "10": {"loc": {"start": {"line": 100, "column": 25}, "end": {"line": 100, "column": 55}}, "type": "cond-expr", "locations": [{"start": {"line": 100, "column": 35}, "end": {"line": 100, "column": 50}}, {"start": {"line": 100, "column": 53}, "end": {"line": 100, "column": 55}}], "line": 100}, "11": {"loc": {"start": {"line": 102, "column": 2}, "end": {"line": 112, "column": 3}}, "type": "if", "locations": [{"start": {"line": 102, "column": 2}, "end": {"line": 112, "column": 3}}, {"start": {}, "end": {}}], "line": 102}, "12": {"loc": {"start": {"line": 122, "column": 19}, "end": {"line": 122, "column": 54}}, "type": "cond-expr", "locations": [{"start": {"line": 122, "column": 41}, "end": {"line": 122, "column": 46}}, {"start": {"line": 122, "column": 49}, "end": {"line": 122, "column": 54}}], "line": 122}, "13": {"loc": {"start": {"line": 134, "column": 2}, "end": {"line": 134, "column": 22}}, "type": "default-arg", "locations": [{"start": {"line": 134, "column": 10}, "end": {"line": 134, "column": 22}}], "line": 134}, "14": {"loc": {"start": {"line": 136, "column": 2}, "end": {"line": 136, "column": 12}}, "type": "default-arg", "locations": [{"start": {"line": 136, "column": 9}, "end": {"line": 136, "column": 12}}], "line": 136}, "15": {"loc": {"start": {"line": 137, "column": 2}, "end": {"line": 137, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 137, "column": 14}, "end": {"line": 137, "column": 16}}], "line": 137}, "16": {"loc": {"start": {"line": 150, "column": 9}, "end": {"line": 152, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 150, "column": 9}, "end": {"line": 150, "column": 17}}, {"start": {"line": 151, "column": 10}, "end": {"line": 151, "column": 63}}], "line": 150}, "17": {"loc": {"start": {"line": 164, "column": 2}, "end": {"line": 164, "column": 11}}, "type": "default-arg", "locations": [{"start": {"line": 164, "column": 10}, "end": {"line": 164, "column": 11}}], "line": 164}, "18": {"loc": {"start": {"line": 165, "column": 2}, "end": {"line": 165, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 165, "column": 14}, "end": {"line": 165, "column": 16}}], "line": 165}, "19": {"loc": {"start": {"line": 173, "column": 7}, "end": {"line": 177, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 173, "column": 7}, "end": {"line": 173, "column": 12}}, {"start": {"line": 174, "column": 8}, "end": {"line": 176, "column": 14}}], "line": 173}, "20": {"loc": {"start": {"line": 188, "column": 2}, "end": {"line": 188, "column": 19}}, "type": "default-arg", "locations": [{"start": {"line": 188, "column": 14}, "end": {"line": 188, "column": 19}}], "line": 188}, "21": {"loc": {"start": {"line": 189, "column": 2}, "end": {"line": 189, "column": 18}}, "type": "default-arg", "locations": [{"start": {"line": 189, "column": 13}, "end": {"line": 189, "column": 18}}], "line": 189}, "22": {"loc": {"start": {"line": 190, "column": 2}, "end": {"line": 190, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 190, "column": 14}, "end": {"line": 190, "column": 16}}], "line": 190}, "23": {"loc": {"start": {"line": 203, "column": 37}, "end": {"line": 203, "column": 85}}, "type": "cond-expr", "locations": [{"start": {"line": 203, "column": 49}, "end": {"line": 203, "column": 80}}, {"start": {"line": 203, "column": 83}, "end": {"line": 203, "column": 85}}], "line": 203}, "24": {"loc": {"start": {"line": 204, "column": 16}, "end": {"line": 204, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 204, "column": 16}, "end": {"line": 204, "column": 24}}, {"start": {"line": 204, "column": 28}, "end": {"line": 204, "column": 37}}], "line": 204}, "25": {"loc": {"start": {"line": 205, "column": 15}, "end": {"line": 205, "column": 46}}, "type": "cond-expr", "locations": [{"start": {"line": 205, "column": 27}, "end": {"line": 205, "column": 36}}, {"start": {"line": 205, "column": 39}, "end": {"line": 205, "column": 46}}], "line": 205}, "26": {"loc": {"start": {"line": 208, "column": 7}, "end": {"line": 215, "column": 7}}, "type": "cond-expr", "locations": [{"start": {"line": 209, "column": 8}, "end": {"line": 212, "column": 14}}, {"start": {"line": 214, "column": 8}, "end": {"line": 214, "column": 16}}], "line": 208}, "27": {"loc": {"start": {"line": 222, "column": 2}, "end": {"line": 222, "column": 19}}, "type": "default-arg", "locations": [{"start": {"line": 222, "column": 14}, "end": {"line": 222, "column": 19}}], "line": 222}, "28": {"loc": {"start": {"line": 223, "column": 2}, "end": {"line": 223, "column": 21}}, "type": "default-arg", "locations": [{"start": {"line": 223, "column": 9}, "end": {"line": 223, "column": 21}}], "line": 223}, "29": {"loc": {"start": {"line": 224, "column": 2}, "end": {"line": 224, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 224, "column": 14}, "end": {"line": 224, "column": 16}}], "line": 224}, "30": {"loc": {"start": {"line": 232, "column": 2}, "end": {"line": 232, "column": 29}}, "type": "if", "locations": [{"start": {"line": 232, "column": 2}, "end": {"line": 232, "column": 29}}, {"start": {}, "end": {}}], "line": 232}, "31": {"loc": {"start": {"line": 251, "column": 2}, "end": {"line": 251, "column": 11}}, "type": "default-arg", "locations": [{"start": {"line": 251, "column": 10}, "end": {"line": 251, "column": 11}}], "line": 251}, "32": {"loc": {"start": {"line": 252, "column": 2}, "end": {"line": 252, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 252, "column": 15}, "end": {"line": 252, "column": 20}}], "line": 252}, "33": {"loc": {"start": {"line": 253, "column": 2}, "end": {"line": 253, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 253, "column": 14}, "end": {"line": 253, "column": 16}}], "line": 253}, "34": {"loc": {"start": {"line": 265, "column": 11}, "end": {"line": 267, "column": 11}}, "type": "binary-expr", "locations": [{"start": {"line": 265, "column": 11}, "end": {"line": 265, "column": 21}}, {"start": {"line": 266, "column": 12}, "end": {"line": 266, "column": 62}}], "line": 265}, "35": {"loc": {"start": {"line": 283, "column": 2}, "end": {"line": 283, "column": 10}}, "type": "default-arg", "locations": [{"start": {"line": 283, "column": 9}, "end": {"line": 283, "column": 10}}], "line": 283}, "36": {"loc": {"start": {"line": 284, "column": 2}, "end": {"line": 284, "column": 13}}, "type": "default-arg", "locations": [{"start": {"line": 284, "column": 12}, "end": {"line": 284, "column": 13}}], "line": 284}, "37": {"loc": {"start": {"line": 285, "column": 2}, "end": {"line": 285, "column": 19}}, "type": "default-arg", "locations": [{"start": {"line": 285, "column": 15}, "end": {"line": 285, "column": 19}}], "line": 285}, "38": {"loc": {"start": {"line": 286, "column": 2}, "end": {"line": 286, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 286, "column": 14}, "end": {"line": 286, "column": 16}}], "line": 286}, "39": {"loc": {"start": {"line": 298, "column": 9}, "end": {"line": 308, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 298, "column": 9}, "end": {"line": 298, "column": 19}}, {"start": {"line": 299, "column": 10}, "end": {"line": 307, "column": 18}}], "line": 298}, "40": {"loc": {"start": {"line": 315, "column": 27}, "end": {"line": 315, "column": 57}}, "type": "cond-expr", "locations": [{"start": {"line": 315, "column": 44}, "end": {"line": 315, "column": 49}}, {"start": {"line": 315, "column": 52}, "end": {"line": 315, "column": 57}}], "line": 315}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0, 0], "4": [0, 0], "5": [0], "6": [0], "7": [0], "8": [0], "9": [0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0], "14": [0], "15": [0], "16": [0, 0], "17": [0], "18": [0], "19": [0, 0], "20": [0], "21": [0], "22": [0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0], "28": [0], "29": [0], "30": [0, 0], "31": [0], "32": [0], "33": [0], "34": [0, 0], "35": [0], "36": [0], "37": [0], "38": [0], "39": [0, 0], "40": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\PerformanceMonitor.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\PerformanceMonitor.tsx", "statementMap": {"0": {"start": {"line": 35, "column": 27}, "end": {"line": 244, "column": 2}}, "1": {"start": {"line": 40, "column": 32}, "end": {"line": 49, "column": 4}}, "2": {"start": {"line": 51, "column": 36}, "end": {"line": 51, "column": 62}}, "3": {"start": {"line": 52, "column": 36}, "end": {"line": 52, "column": 53}}, "4": {"start": {"line": 54, "column": 2}, "end": {"line": 94, "column": 15}}, "5": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 24}}, "6": {"start": {"line": 55, "column": 18}, "end": {"line": 55, "column": 24}}, "7": {"start": {"line": 57, "column": 21}, "end": {"line": 57, "column": 22}}, "8": {"start": {"line": 58, "column": 26}, "end": {"line": 58, "column": 27}}, "9": {"start": {"line": 59, "column": 19}, "end": {"line": 59, "column": 36}}, "10": {"start": {"line": 61, "column": 26}, "end": {"line": 88, "column": 5}}, "11": {"start": {"line": 62, "column": 26}, "end": {"line": 62, "column": 43}}, "12": {"start": {"line": 63, "column": 25}, "end": {"line": 63, "column": 47}}, "13": {"start": {"line": 65, "column": 6}, "end": {"line": 65, "column": 18}}, "14": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 35}}, "15": {"start": {"line": 70, "column": 6}, "end": {"line": 77, "column": 7}}, "16": {"start": {"line": 71, "column": 23}, "end": {"line": 71, "column": 50}}, "17": {"start": {"line": 72, "column": 8}, "end": {"line": 76, "column": 9}}, "18": {"start": {"line": 79, "column": 6}, "end": {"line": 85, "column": 8}}, "19": {"start": {"line": 87, "column": 6}, "end": {"line": 87, "column": 28}}, "20": {"start": {"line": 91, "column": 21}, "end": {"line": 91, "column": 53}}, "21": {"start": {"line": 93, "column": 4}, "end": {"line": 93, "column": 40}}, "22": {"start": {"line": 93, "column": 17}, "end": {"line": 93, "column": 40}}, "23": {"start": {"line": 96, "column": 2}, "end": {"line": 96, "column": 29}}, "24": {"start": {"line": 96, "column": 18}, "end": {"line": 96, "column": 29}}, "25": {"start": {"line": 98, "column": 26}, "end": {"line": 103, "column": 3}}, "26": {"start": {"line": 105, "column": 22}, "end": {"line": 111, "column": 3}}, "27": {"start": {"line": 106, "column": 4}, "end": {"line": 106, "column": 37}}, "28": {"start": {"line": 106, "column": 21}, "end": {"line": 106, "column": 37}}, "29": {"start": {"line": 107, "column": 14}, "end": {"line": 107, "column": 18}}, "30": {"start": {"line": 108, "column": 18}, "end": {"line": 108, "column": 45}}, "31": {"start": {"line": 109, "column": 14}, "end": {"line": 109, "column": 55}}, "32": {"start": {"line": 110, "column": 4}, "end": {"line": 110, "column": 75}}, "33": {"start": {"line": 113, "column": 30}, "end": {"line": 117, "column": 3}}, "34": {"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 57}}, "35": {"start": {"line": 114, "column": 34}, "end": {"line": 114, "column": 57}}, "36": {"start": {"line": 115, "column": 4}, "end": {"line": 115, "column": 61}}, "37": {"start": {"line": 115, "column": 37}, "end": {"line": 115, "column": 61}}, "38": {"start": {"line": 116, "column": 4}, "end": {"line": 116, "column": 25}}, "39": {"start": {"line": 119, "column": 2}, "end": {"line": 243, "column": 3}}, "40": {"start": {"line": 127, "column": 25}, "end": {"line": 127, "column": 44}}, "41": {"start": {"line": 139, "column": 31}, "end": {"line": 139, "column": 49}}, "42": {"start": {"line": 146, "column": 31}, "end": {"line": 146, "column": 50}}, "43": {"start": {"line": 206, "column": 16}, "end": {"line": 220, "column": 22}}}, "fnMap": {"0": {"name": "PerformanceMonitor", "decl": {"start": {"line": 35, "column": 41}, "end": {"line": 35, "column": 59}}, "loc": {"start": {"line": 39, "column": 28}, "end": {"line": 244, "column": 1}}, "line": 39}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 54, "column": 12}, "end": {"line": 54, "column": 13}}, "loc": {"start": {"line": 54, "column": 18}, "end": {"line": 94, "column": 3}}, "line": 54}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 61, "column": 26}, "end": {"line": 61, "column": 27}}, "loc": {"start": {"line": 61, "column": 32}, "end": {"line": 88, "column": 5}}, "line": 61}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 93, "column": 11}, "end": {"line": 93, "column": 12}}, "loc": {"start": {"line": 93, "column": 17}, "end": {"line": 93, "column": 40}}, "line": 93}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 105, "column": 22}, "end": {"line": 105, "column": 23}}, "loc": {"start": {"line": 105, "column": 41}, "end": {"line": 111, "column": 3}}, "line": 105}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 113, "column": 30}, "end": {"line": 113, "column": 31}}, "loc": {"start": {"line": 113, "column": 96}, "end": {"line": 117, "column": 3}}, "line": 113}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 127, "column": 19}, "end": {"line": 127, "column": 20}}, "loc": {"start": {"line": 127, "column": 25}, "end": {"line": 127, "column": 44}}, "line": 127}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 139, "column": 25}, "end": {"line": 139, "column": 26}}, "loc": {"start": {"line": 139, "column": 31}, "end": {"line": 139, "column": 49}}, "line": 139}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 146, "column": 25}, "end": {"line": 146, "column": 26}}, "loc": {"start": {"line": 146, "column": 31}, "end": {"line": 146, "column": 50}}, "line": 146}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 205, "column": 54}, "end": {"line": 205, "column": 55}}, "loc": {"start": {"line": 206, "column": 16}, "end": {"line": 220, "column": 22}}, "line": 206}}, "branchMap": {"0": {"loc": {"start": {"line": 36, "column": 2}, "end": {"line": 36, "column": 50}}, "type": "default-arg", "locations": [{"start": {"line": 36, "column": 12}, "end": {"line": 36, "column": 50}}], "line": 36}, "1": {"loc": {"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": 27}}, "type": "default-arg", "locations": [{"start": {"line": 37, "column": 13}, "end": {"line": 37, "column": 27}}], "line": 37}, "2": {"loc": {"start": {"line": 38, "column": 13}, "end": {"line": 38, "column": 36}}, "type": "default-arg", "locations": [{"start": {"line": 38, "column": 32}, "end": {"line": 38, "column": 36}}], "line": 38}, "3": {"loc": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 24}}, "type": "if", "locations": [{"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 24}}, {"start": {}, "end": {}}], "line": 55}, "4": {"loc": {"start": {"line": 70, "column": 6}, "end": {"line": 77, "column": 7}}, "type": "if", "locations": [{"start": {"line": 70, "column": 6}, "end": {"line": 77, "column": 7}}, {"start": {}, "end": {}}], "line": 70}, "5": {"loc": {"start": {"line": 96, "column": 2}, "end": {"line": 96, "column": 29}}, "type": "if", "locations": [{"start": {"line": 96, "column": 2}, "end": {"line": 96, "column": 29}}, {"start": {}, "end": {}}], "line": 96}, "6": {"loc": {"start": {"line": 106, "column": 4}, "end": {"line": 106, "column": 37}}, "type": "if", "locations": [{"start": {"line": 106, "column": 4}, "end": {"line": 106, "column": 37}}, {"start": {}, "end": {}}], "line": 106}, "7": {"loc": {"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 57}}, "type": "if", "locations": [{"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 57}}, {"start": {}, "end": {}}], "line": 114}, "8": {"loc": {"start": {"line": 115, "column": 4}, "end": {"line": 115, "column": 61}}, "type": "if", "locations": [{"start": {"line": 115, "column": 4}, "end": {"line": 115, "column": 61}}, {"start": {}, "end": {}}], "line": 115}, "9": {"loc": {"start": {"line": 122, "column": 8}, "end": {"line": 122, "column": 65}}, "type": "cond-expr", "locations": [{"start": {"line": 122, "column": 20}, "end": {"line": 122, "column": 31}}, {"start": {"line": 122, "column": 34}, "end": {"line": 122, "column": 65}}], "line": 122}, "10": {"loc": {"start": {"line": 125, "column": 7}, "end": {"line": 241, "column": 7}}, "type": "cond-expr", "locations": [{"start": {"line": 126, "column": 8}, "end": {"line": 132, "column": 17}}, {"start": {"line": 134, "column": 8}, "end": {"line": 240, "column": 14}}], "line": 125}, "11": {"loc": {"start": {"line": 179, "column": 11}, "end": {"line": 199, "column": 11}}, "type": "binary-expr", "locations": [{"start": {"line": 179, "column": 11}, "end": {"line": 179, "column": 30}}, {"start": {"line": 180, "column": 12}, "end": {"line": 198, "column": 18}}], "line": 179}, "12": {"loc": {"start": {"line": 229, "column": 15}, "end": {"line": 231, "column": 15}}, "type": "binary-expr", "locations": [{"start": {"line": 229, "column": 15}, "end": {"line": 229, "column": 45}}, {"start": {"line": 230, "column": 16}, "end": {"line": 230, "column": 86}}], "line": 229}, "13": {"loc": {"start": {"line": 232, "column": 15}, "end": {"line": 234, "column": 15}}, "type": "binary-expr", "locations": [{"start": {"line": 232, "column": 15}, "end": {"line": 232, "column": 34}}, {"start": {"line": 232, "column": 38}, "end": {"line": 232, "column": 73}}, {"start": {"line": 233, "column": 16}, "end": {"line": 233, "column": 80}}], "line": 232}, "14": {"loc": {"start": {"line": 235, "column": 15}, "end": {"line": 237, "column": 15}}, "type": "binary-expr", "locations": [{"start": {"line": 235, "column": 15}, "end": {"line": 235, "column": 51}}, {"start": {"line": 236, "column": 16}, "end": {"line": 236, "column": 79}}], "line": 235}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0, 0], "14": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\DashboardHeader.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\DashboardHeader.tsx", "statementMap": {"0": {"start": {"line": 28, "column": 40}, "end": {"line": 28, "column": 52}}, "1": {"start": {"line": 30, "column": 29}, "end": {"line": 34, "column": 3}}, "2": {"start": {"line": 31, "column": 18}, "end": {"line": 31, "column": 32}}, "3": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 25}}, "4": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 21}}, "5": {"start": {"line": 36, "column": 29}, "end": {"line": 39, "column": 3}}, "6": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 22}}, "7": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 27}}, "8": {"start": {"line": 41, "column": 27}, "end": {"line": 43, "column": 3}}, "9": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 20}}, "10": {"start": {"line": 45, "column": 2}, "end": {"line": 87, "column": 3}}}, "fnMap": {"0": {"name": "DashboardHeader", "decl": {"start": {"line": 20, "column": 24}, "end": {"line": 20, "column": 39}}, "loc": {"start": {"line": 27, "column": 25}, "end": {"line": 88, "column": 1}}, "line": 27}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 30, "column": 29}, "end": {"line": 30, "column": 30}}, "loc": {"start": {"line": 30, "column": 73}, "end": {"line": 34, "column": 3}}, "line": 30}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 36, "column": 29}, "end": {"line": 36, "column": 30}}, "loc": {"start": {"line": 36, "column": 53}, "end": {"line": 39, "column": 3}}, "line": 36}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 41, "column": 27}, "end": {"line": 41, "column": 28}}, "loc": {"start": {"line": 41, "column": 33}, "end": {"line": 43, "column": 3}}, "line": 41}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 31}}, "type": "default-arg", "locations": [{"start": {"line": 21, "column": 15}, "end": {"line": 21, "column": 31}}], "line": 21}, "1": {"loc": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 42}}, "type": "default-arg", "locations": [{"start": {"line": 24, "column": 22}, "end": {"line": 24, "column": 42}}], "line": 24}, "2": {"loc": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 25, "column": 14}, "end": {"line": 25, "column": 16}}], "line": 25}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0], "1": [0], "2": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\DashboardStats.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\DashboardStats.tsx", "statementMap": {"0": {"start": {"line": 27, "column": 17}, "end": {"line": 47, "column": 2}}, "1": {"start": {"line": 28, "column": 2}, "end": {"line": 38, "column": 3}}, "2": {"start": {"line": 29, "column": 4}, "end": {"line": 37, "column": 5}}, "3": {"start": {"line": 40, "column": 2}, "end": {"line": 46, "column": 3}}, "4": {"start": {"line": 49, "column": 23}, "end": {"line": 104, "column": 2}}, "5": {"start": {"line": 56, "column": 2}, "end": {"line": 56, "column": 41}}, "6": {"start": {"line": 59, "column": 22}, "end": {"line": 84, "column": 80}}, "7": {"start": {"line": 59, "column": 42}, "end": {"line": 84, "column": 3}}, "8": {"start": {"line": 86, "column": 2}, "end": {"line": 103, "column": 3}}, "9": {"start": {"line": 94, "column": 8}, "end": {"line": 100, "column": 10}}}, "fnMap": {"0": {"name": "StatCard", "decl": {"start": {"line": 27, "column": 31}, "end": {"line": 27, "column": 39}}, "loc": {"start": {"line": 27, "column": 90}, "end": {"line": 47, "column": 1}}, "line": 27}, "1": {"name": "DashboardStats", "decl": {"start": {"line": 49, "column": 37}, "end": {"line": 49, "column": 51}}, "loc": {"start": {"line": 54, "column": 24}, "end": {"line": 104, "column": 1}}, "line": 54}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 59, "column": 36}, "end": {"line": 59, "column": 37}}, "loc": {"start": {"line": 59, "column": 42}, "end": {"line": 84, "column": 3}}, "line": 59}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 93, "column": 23}, "end": {"line": 93, "column": 24}}, "loc": {"start": {"line": 94, "column": 8}, "end": {"line": 100, "column": 10}}, "line": 94}}, "branchMap": {"0": {"loc": {"start": {"line": 28, "column": 2}, "end": {"line": 38, "column": 3}}, "type": "if", "locations": [{"start": {"line": 28, "column": 2}, "end": {"line": 38, "column": 3}}, {"start": {}, "end": {}}], "line": 28}, "1": {"loc": {"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": 19}}, "type": "default-arg", "locations": [{"start": {"line": 51, "column": 14}, "end": {"line": 51, "column": 19}}], "line": 51}, "2": {"loc": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 52, "column": 14}, "end": {"line": 52, "column": 16}}], "line": 52}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0], "2": [0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\RecentRenewals.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\RecentRenewals.tsx", "statementMap": {"0": {"start": {"line": 25, "column": 22}, "end": {"line": 27, "column": 3}}, "1": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 22}}, "2": {"start": {"line": 29, "column": 24}, "end": {"line": 34, "column": 3}}, "3": {"start": {"line": 30, "column": 4}, "end": {"line": 33, "column": 5}}, "4": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 24}}, "5": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 19}}, "6": {"start": {"line": 36, "column": 2}, "end": {"line": 59, "column": 3}}, "7": {"start": {"line": 63, "column": 2}, "end": {"line": 79, "column": 3}}, "8": {"start": {"line": 66, "column": 8}, "end": {"line": 76, "column": 14}}, "9": {"start": {"line": 83, "column": 2}, "end": {"line": 91, "column": 3}}, "10": {"start": {"line": 102, "column": 26}, "end": {"line": 102, "column": 53}}, "11": {"start": {"line": 104, "column": 2}, "end": {"line": 135, "column": 3}}, "12": {"start": {"line": 123, "column": 14}, "end": {"line": 127, "column": 16}}}, "fnMap": {"0": {"name": "RenewalItem", "decl": {"start": {"line": 24, "column": 9}, "end": {"line": 24, "column": 20}}, "loc": {"start": {"line": 24, "column": 61}, "end": {"line": 60, "column": 1}}, "line": 24}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 25, "column": 22}, "end": {"line": 25, "column": 23}}, "loc": {"start": {"line": 25, "column": 28}, "end": {"line": 27, "column": 3}}, "line": 25}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 29, "column": 24}, "end": {"line": 29, "column": 25}}, "loc": {"start": {"line": 29, "column": 52}, "end": {"line": 34, "column": 3}}, "line": 29}, "3": {"name": "LoadingSkeleton", "decl": {"start": {"line": 62, "column": 9}, "end": {"line": 62, "column": 24}}, "loc": {"start": {"line": 62, "column": 27}, "end": {"line": 80, "column": 1}}, "line": 62}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 65, "column": 25}, "end": {"line": 65, "column": 26}}, "loc": {"start": {"line": 66, "column": 8}, "end": {"line": 76, "column": 14}}, "line": 66}, "5": {"name": "EmptyState", "decl": {"start": {"line": 82, "column": 9}, "end": {"line": 82, "column": 19}}, "loc": {"start": {"line": 82, "column": 22}, "end": {"line": 92, "column": 1}}, "line": 82}, "6": {"name": "Recent<PERSON><PERSON><PERSON><PERSON>", "decl": {"start": {"line": 94, "column": 24}, "end": {"line": 94, "column": 38}}, "loc": {"start": {"line": 101, "column": 24}, "end": {"line": 136, "column": 1}}, "line": 101}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 122, "column": 33}, "end": {"line": 122, "column": 34}}, "loc": {"start": {"line": 123, "column": 14}, "end": {"line": 127, "column": 16}}, "line": 123}}, "branchMap": {"0": {"loc": {"start": {"line": 30, "column": 4}, "end": {"line": 33, "column": 5}}, "type": "if", "locations": [{"start": {"line": 30, "column": 4}, "end": {"line": 33, "column": 5}}, {"start": {}, "end": {}}], "line": 30}, "1": {"loc": {"start": {"line": 30, "column": 8}, "end": {"line": 30, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 30, "column": 8}, "end": {"line": 30, "column": 25}}, {"start": {"line": 30, "column": 29}, "end": {"line": 30, "column": 42}}], "line": 30}, "2": {"loc": {"start": {"line": 51, "column": 20}, "end": {"line": 51, "column": 102}}, "type": "cond-expr", "locations": [{"start": {"line": 51, "column": 41}, "end": {"line": 51, "column": 90}}, {"start": {"line": 51, "column": 93}, "end": {"line": 51, "column": 102}}], "line": 51}, "3": {"loc": {"start": {"line": 96, "column": 2}, "end": {"line": 96, "column": 19}}, "type": "default-arg", "locations": [{"start": {"line": 96, "column": 14}, "end": {"line": 96, "column": 19}}], "line": 96}, "4": {"loc": {"start": {"line": 98, "column": 2}, "end": {"line": 98, "column": 14}}, "type": "default-arg", "locations": [{"start": {"line": 98, "column": 13}, "end": {"line": 98, "column": 14}}], "line": 98}, "5": {"loc": {"start": {"line": 99, "column": 2}, "end": {"line": 99, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 99, "column": 14}, "end": {"line": 99, "column": 16}}], "line": 99}, "6": {"loc": {"start": {"line": 111, "column": 9}, "end": {"line": 115, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 111, "column": 9}, "end": {"line": 111, "column": 35}}, {"start": {"line": 112, "column": 10}, "end": {"line": 114, "column": 17}}], "line": 111}, "7": {"loc": {"start": {"line": 118, "column": 9}, "end": {"line": 132, "column": 9}}, "type": "cond-expr", "locations": [{"start": {"line": 119, "column": 10}, "end": {"line": 119, "column": 29}}, {"start": {"line": 120, "column": 12}, "end": {"line": 132, "column": 9}}], "line": 118}, "8": {"loc": {"start": {"line": 120, "column": 12}, "end": {"line": 132, "column": 9}}, "type": "cond-expr", "locations": [{"start": {"line": 121, "column": 10}, "end": {"line": 129, "column": 16}}, {"start": {"line": 131, "column": 10}, "end": {"line": 131, "column": 24}}], "line": 120}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0], "4": [0], "5": [0], "6": [0, 0], "7": [0, 0], "8": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\ScanResults.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\ScanResults.tsx", "statementMap": {"0": {"start": {"line": 35, "column": 22}, "end": {"line": 37, "column": 3}}, "1": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 21}}, "2": {"start": {"line": 39, "column": 25}, "end": {"line": 52, "column": 3}}, "3": {"start": {"line": 40, "column": 4}, "end": {"line": 51, "column": 5}}, "4": {"start": {"line": 42, "column": 8}, "end": {"line": 42, "column": 43}}, "5": {"start": {"line": 44, "column": 8}, "end": {"line": 44, "column": 39}}, "6": {"start": {"line": 46, "column": 8}, "end": {"line": 46, "column": 39}}, "7": {"start": {"line": 48, "column": 8}, "end": {"line": 48, "column": 45}}, "8": {"start": {"line": 50, "column": 8}, "end": {"line": 50, "column": 41}}, "9": {"start": {"line": 54, "column": 24}, "end": {"line": 67, "column": 3}}, "10": {"start": {"line": 55, "column": 4}, "end": {"line": 66, "column": 5}}, "11": {"start": {"line": 57, "column": 8}, "end": {"line": 57, "column": 18}}, "12": {"start": {"line": 59, "column": 8}, "end": {"line": 59, "column": 18}}, "13": {"start": {"line": 61, "column": 8}, "end": {"line": 61, "column": 18}}, "14": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 19}}, "15": {"start": {"line": 65, "column": 8}, "end": {"line": 65, "column": 18}}, "16": {"start": {"line": 69, "column": 2}, "end": {"line": 98, "column": 3}}, "17": {"start": {"line": 102, "column": 2}, "end": {"line": 121, "column": 3}}, "18": {"start": {"line": 105, "column": 8}, "end": {"line": 118, "column": 14}}, "19": {"start": {"line": 125, "column": 2}, "end": {"line": 138, "column": 3}}, "20": {"start": {"line": 142, "column": 2}, "end": {"line": 160, "column": 3}}, "21": {"start": {"line": 172, "column": 2}, "end": {"line": 198, "column": 3}}, "22": {"start": {"line": 186, "column": 14}, "end": {"line": 190, "column": 16}}}, "fnMap": {"0": {"name": "ScanResultItem", "decl": {"start": {"line": 34, "column": 9}, "end": {"line": 34, "column": 23}}, "loc": {"start": {"line": 34, "column": 66}, "end": {"line": 99, "column": 1}}, "line": 34}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 35, "column": 22}, "end": {"line": 35, "column": 23}}, "loc": {"start": {"line": 35, "column": 28}, "end": {"line": 37, "column": 3}}, "line": 35}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 39, "column": 25}, "end": {"line": 39, "column": 26}}, "loc": {"start": {"line": 39, "column": 59}, "end": {"line": 52, "column": 3}}, "line": 39}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 54, "column": 24}, "end": {"line": 54, "column": 25}}, "loc": {"start": {"line": 54, "column": 58}, "end": {"line": 67, "column": 3}}, "line": 54}, "4": {"name": "LoadingSkeleton", "decl": {"start": {"line": 101, "column": 9}, "end": {"line": 101, "column": 24}}, "loc": {"start": {"line": 101, "column": 27}, "end": {"line": 122, "column": 1}}, "line": 101}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 104, "column": 25}, "end": {"line": 104, "column": 26}}, "loc": {"start": {"line": 105, "column": 8}, "end": {"line": 118, "column": 14}}, "line": 105}, "6": {"name": "EmptyState", "decl": {"start": {"line": 124, "column": 9}, "end": {"line": 124, "column": 19}}, "loc": {"start": {"line": 124, "column": 63}, "end": {"line": 139, "column": 1}}, "line": 124}, "7": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decl": {"start": {"line": 141, "column": 9}, "end": {"line": 141, "column": 19}}, "loc": {"start": {"line": 141, "column": 98}, "end": {"line": 161, "column": 1}}, "line": 141}, "8": {"name": "ScanResults", "decl": {"start": {"line": 163, "column": 24}, "end": {"line": 163, "column": 35}}, "loc": {"start": {"line": 171, "column": 21}, "end": {"line": 199, "column": 1}}, "line": 171}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 185, "column": 25}, "end": {"line": 185, "column": 26}}, "loc": {"start": {"line": 186, "column": 14}, "end": {"line": 190, "column": 16}}, "line": 186}}, "branchMap": {"0": {"loc": {"start": {"line": 40, "column": 4}, "end": {"line": 51, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 41, "column": 6}, "end": {"line": 42, "column": 43}}, {"start": {"line": 43, "column": 6}, "end": {"line": 44, "column": 39}}, {"start": {"line": 45, "column": 6}, "end": {"line": 46, "column": 39}}, {"start": {"line": 47, "column": 6}, "end": {"line": 48, "column": 45}}, {"start": {"line": 49, "column": 6}, "end": {"line": 50, "column": 41}}], "line": 40}, "1": {"loc": {"start": {"line": 55, "column": 4}, "end": {"line": 66, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 56, "column": 6}, "end": {"line": 57, "column": 18}}, {"start": {"line": 58, "column": 6}, "end": {"line": 59, "column": 18}}, {"start": {"line": 60, "column": 6}, "end": {"line": 61, "column": 18}}, {"start": {"line": 62, "column": 6}, "end": {"line": 63, "column": 19}}, {"start": {"line": 64, "column": 6}, "end": {"line": 65, "column": 18}}], "line": 55}, "2": {"loc": {"start": {"line": 84, "column": 11}, "end": {"line": 86, "column": 11}}, "type": "binary-expr", "locations": [{"start": {"line": 84, "column": 11}, "end": {"line": 84, "column": 25}}, {"start": {"line": 85, "column": 12}, "end": {"line": 85, "column": 70}}], "line": 84}, "3": {"loc": {"start": {"line": 146, "column": 9}, "end": {"line": 150, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 146, "column": 9}, "end": {"line": 146, "column": 21}}, {"start": {"line": 147, "column": 10}, "end": {"line": 149, "column": 14}}], "line": 146}, "4": {"loc": {"start": {"line": 165, "column": 2}, "end": {"line": 165, "column": 19}}, "type": "default-arg", "locations": [{"start": {"line": 165, "column": 14}, "end": {"line": 165, "column": 19}}], "line": 165}, "5": {"loc": {"start": {"line": 169, "column": 2}, "end": {"line": 169, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 169, "column": 14}, "end": {"line": 169, "column": 16}}], "line": 169}, "6": {"loc": {"start": {"line": 181, "column": 9}, "end": {"line": 195, "column": 9}}, "type": "cond-expr", "locations": [{"start": {"line": 182, "column": 10}, "end": {"line": 182, "column": 29}}, {"start": {"line": 183, "column": 12}, "end": {"line": 195, "column": 9}}], "line": 181}, "7": {"loc": {"start": {"line": 183, "column": 12}, "end": {"line": 195, "column": 9}}, "type": "cond-expr", "locations": [{"start": {"line": 184, "column": 10}, "end": {"line": 192, "column": 16}}, {"start": {"line": 194, "column": 10}, "end": {"line": 194, "column": 46}}], "line": 183}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0, 0, 0, 0, 0], "1": [0, 0, 0, 0, 0], "2": [0, 0], "3": [0, 0], "4": [0], "5": [0], "6": [0, 0], "7": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\UpcomingRenewals.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\UpcomingRenewals.tsx", "statementMap": {"0": {"start": {"line": 26, "column": 22}, "end": {"line": 28, "column": 3}}, "1": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 22}}, "2": {"start": {"line": 30, "column": 26}, "end": {"line": 35, "column": 3}}, "3": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 50}}, "4": {"start": {"line": 31, "column": 19}, "end": {"line": 31, "column": 50}}, "5": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 57}}, "6": {"start": {"line": 32, "column": 20}, "end": {"line": 32, "column": 57}}, "7": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 57}}, "8": {"start": {"line": 33, "column": 20}, "end": {"line": 33, "column": 57}}, "9": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 37}}, "10": {"start": {"line": 37, "column": 25}, "end": {"line": 42, "column": 3}}, "11": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 34}}, "12": {"start": {"line": 38, "column": 18}, "end": {"line": 38, "column": 34}}, "13": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 38}}, "14": {"start": {"line": 39, "column": 20}, "end": {"line": 39, "column": 38}}, "15": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 41}}, "16": {"start": {"line": 40, "column": 20}, "end": {"line": 40, "column": 41}}, "17": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 32}}, "18": {"start": {"line": 44, "column": 2}, "end": {"line": 77, "column": 3}}, "19": {"start": {"line": 81, "column": 2}, "end": {"line": 100, "column": 3}}, "20": {"start": {"line": 84, "column": 8}, "end": {"line": 97, "column": 14}}, "21": {"start": {"line": 104, "column": 2}, "end": {"line": 112, "column": 3}}, "22": {"start": {"line": 117, "column": 16}, "end": {"line": 117, "column": 26}}, "23": {"start": {"line": 118, "column": 14}, "end": {"line": 118, "column": 31}}, "24": {"start": {"line": 119, "column": 19}, "end": {"line": 119, "column": 50}}, "25": {"start": {"line": 120, "column": 2}, "end": {"line": 120, "column": 52}}, "26": {"start": {"line": 125, "column": 14}, "end": {"line": 125, "column": 24}}, "27": {"start": {"line": 126, "column": 2}, "end": {"line": 135, "column": 4}}, "28": {"start": {"line": 127, "column": 4}, "end": {"line": 127, "column": 39}}, "29": {"start": {"line": 127, "column": 27}, "end": {"line": 127, "column": 39}}, "30": {"start": {"line": 128, "column": 20}, "end": {"line": 128, "column": 46}}, "31": {"start": {"line": 129, "column": 25}, "end": {"line": 129, "column": 49}}, "32": {"start": {"line": 130, "column": 4}, "end": {"line": 130, "column": 62}}, "33": {"start": {"line": 133, "column": 4}, "end": {"line": 133, "column": 44}}, "34": {"start": {"line": 133, "column": 36}, "end": {"line": 133, "column": 44}}, "35": {"start": {"line": 134, "column": 4}, "end": {"line": 134, "column": 74}}, "36": {"start": {"line": 146, "column": 27}, "end": {"line": 146, "column": 71}}, "37": {"start": {"line": 148, "column": 2}, "end": {"line": 181, "column": 3}}, "38": {"start": {"line": 168, "column": 14}, "end": {"line": 173, "column": 16}}}, "fnMap": {"0": {"name": "UpcomingRenewalItem", "decl": {"start": {"line": 25, "column": 9}, "end": {"line": 25, "column": 28}}, "loc": {"start": {"line": 25, "column": 91}, "end": {"line": 78, "column": 1}}, "line": 25}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 26, "column": 22}, "end": {"line": 26, "column": 23}}, "loc": {"start": {"line": 26, "column": 28}, "end": {"line": 28, "column": 3}}, "line": 26}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 30, "column": 26}, "end": {"line": 30, "column": 27}}, "loc": {"start": {"line": 30, "column": 44}, "end": {"line": 35, "column": 3}}, "line": 30}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 37, "column": 25}, "end": {"line": 37, "column": 26}}, "loc": {"start": {"line": 37, "column": 43}, "end": {"line": 42, "column": 3}}, "line": 37}, "4": {"name": "LoadingSkeleton", "decl": {"start": {"line": 80, "column": 9}, "end": {"line": 80, "column": 24}}, "loc": {"start": {"line": 80, "column": 27}, "end": {"line": 101, "column": 1}}, "line": 80}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 83, "column": 25}, "end": {"line": 83, "column": 26}}, "loc": {"start": {"line": 84, "column": 8}, "end": {"line": 97, "column": 14}}, "line": 84}, "6": {"name": "EmptyState", "decl": {"start": {"line": 103, "column": 9}, "end": {"line": 103, "column": 19}}, "loc": {"start": {"line": 103, "column": 22}, "end": {"line": 113, "column": 1}}, "line": 103}, "7": {"name": "getDaysUntilDue", "decl": {"start": {"line": 116, "column": 9}, "end": {"line": 116, "column": 24}}, "loc": {"start": {"line": 116, "column": 48}, "end": {"line": 121, "column": 1}}, "line": 116}, "8": {"name": "getUpcomingRenewals", "decl": {"start": {"line": 124, "column": 9}, "end": {"line": 124, "column": 28}}, "loc": {"start": {"line": 124, "column": 84}, "end": {"line": 136, "column": 1}}, "line": 124}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 126, "column": 25}, "end": {"line": 126, "column": 26}}, "loc": {"start": {"line": 126, "column": 36}, "end": {"line": 131, "column": 3}}, "line": 126}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 131, "column": 10}, "end": {"line": 131, "column": 11}}, "loc": {"start": {"line": 131, "column": 20}, "end": {"line": 135, "column": 3}}, "line": 131}, "11": {"name": "UpcomingRenewals", "decl": {"start": {"line": 138, "column": 24}, "end": {"line": 138, "column": 40}}, "loc": {"start": {"line": 145, "column": 26}, "end": {"line": 182, "column": 1}}, "line": 145}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 167, "column": 34}, "end": {"line": 167, "column": 35}}, "loc": {"start": {"line": 168, "column": 14}, "end": {"line": 173, "column": 16}}, "line": 168}}, "branchMap": {"0": {"loc": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 50}}, "type": "if", "locations": [{"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 50}}, {"start": {}, "end": {}}], "line": 31}, "1": {"loc": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 57}}, "type": "if", "locations": [{"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 57}}, {"start": {}, "end": {}}], "line": 32}, "2": {"loc": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 57}}, "type": "if", "locations": [{"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 57}}, {"start": {}, "end": {}}], "line": 33}, "3": {"loc": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 34}}, "type": "if", "locations": [{"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 34}}, {"start": {}, "end": {}}], "line": 38}, "4": {"loc": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 38}}, "type": "if", "locations": [{"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 38}}, {"start": {}, "end": {}}], "line": 39}, "5": {"loc": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 41}}, "type": "if", "locations": [{"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 41}}, {"start": {}, "end": {}}], "line": 40}, "6": {"loc": {"start": {"line": 59, "column": 11}, "end": {"line": 63, "column": 11}}, "type": "binary-expr", "locations": [{"start": {"line": 59, "column": 11}, "end": {"line": 59, "column": 30}}, {"start": {"line": 60, "column": 12}, "end": {"line": 62, "column": 16}}], "line": 59}, "7": {"loc": {"start": {"line": 70, "column": 9}, "end": {"line": 74, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 70, "column": 9}, "end": {"line": 70, "column": 25}}, {"start": {"line": 71, "column": 10}, "end": {"line": 73, "column": 14}}], "line": 70}, "8": {"loc": {"start": {"line": 127, "column": 4}, "end": {"line": 127, "column": 39}}, "type": "if", "locations": [{"start": {"line": 127, "column": 4}, "end": {"line": 127, "column": 39}}, {"start": {}, "end": {}}], "line": 127}, "9": {"loc": {"start": {"line": 130, "column": 11}, "end": {"line": 130, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 130, "column": 11}, "end": {"line": 130, "column": 29}}, {"start": {"line": 130, "column": 33}, "end": {"line": 130, "column": 62}}], "line": 130}, "10": {"loc": {"start": {"line": 133, "column": 4}, "end": {"line": 133, "column": 44}}, "type": "if", "locations": [{"start": {"line": 133, "column": 4}, "end": {"line": 133, "column": 44}}, {"start": {}, "end": {}}], "line": 133}, "11": {"loc": {"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": 34}}, "type": "binary-expr", "locations": [{"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": 19}}, {"start": {"line": 133, "column": 23}, "end": {"line": 133, "column": 34}}], "line": 133}, "12": {"loc": {"start": {"line": 140, "column": 2}, "end": {"line": 140, "column": 19}}, "type": "default-arg", "locations": [{"start": {"line": 140, "column": 14}, "end": {"line": 140, "column": 19}}], "line": 140}, "13": {"loc": {"start": {"line": 142, "column": 2}, "end": {"line": 142, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 142, "column": 18}, "end": {"line": 142, "column": 20}}], "line": 142}, "14": {"loc": {"start": {"line": 143, "column": 2}, "end": {"line": 143, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 143, "column": 14}, "end": {"line": 143, "column": 16}}], "line": 143}, "15": {"loc": {"start": {"line": 156, "column": 9}, "end": {"line": 160, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 156, "column": 9}, "end": {"line": 156, "column": 36}}, {"start": {"line": 157, "column": 10}, "end": {"line": 159, "column": 17}}], "line": 156}, "16": {"loc": {"start": {"line": 158, "column": 46}, "end": {"line": 158, "column": 86}}, "type": "cond-expr", "locations": [{"start": {"line": 158, "column": 78}, "end": {"line": 158, "column": 81}}, {"start": {"line": 158, "column": 84}, "end": {"line": 158, "column": 86}}], "line": 158}, "17": {"loc": {"start": {"line": 163, "column": 9}, "end": {"line": 178, "column": 9}}, "type": "cond-expr", "locations": [{"start": {"line": 164, "column": 10}, "end": {"line": 164, "column": 29}}, {"start": {"line": 165, "column": 12}, "end": {"line": 178, "column": 9}}], "line": 163}, "18": {"loc": {"start": {"line": 165, "column": 12}, "end": {"line": 178, "column": 9}}, "type": "cond-expr", "locations": [{"start": {"line": 166, "column": 10}, "end": {"line": 175, "column": 16}}, {"start": {"line": 177, "column": 10}, "end": {"line": 177, "column": 24}}], "line": 165}, "19": {"loc": {"start": {"line": 172, "column": 30}, "end": {"line": 172, "column": 96}}, "type": "cond-expr", "locations": [{"start": {"line": 172, "column": 49}, "end": {"line": 172, "column": 92}}, {"start": {"line": 172, "column": 95}, "end": {"line": 172, "column": 96}}], "line": 172}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0], "13": [0], "14": [0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\layout\\MainLayout.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\layout\\MainLayout.tsx", "statementMap": {"0": {"start": {"line": 14, "column": 41}, "end": {"line": 14, "column": 50}}, "1": {"start": {"line": 15, "column": 17}, "end": {"line": 15, "column": 28}}, "2": {"start": {"line": 17, "column": 2}, "end": {"line": 21, "column": 34}}, "3": {"start": {"line": 18, "column": 4}, "end": {"line": 20, "column": 5}}, "4": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": 42}}, "5": {"start": {"line": 24, "column": 2}, "end": {"line": 26, "column": 3}}, "6": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": 86}}, "7": {"start": {"line": 29, "column": 2}, "end": {"line": 31, "column": 3}}, "8": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": 15}}, "9": {"start": {"line": 33, "column": 2}, "end": {"line": 40, "column": 3}}}, "fnMap": {"0": {"name": "MainLayout", "decl": {"start": {"line": 9, "column": 24}, "end": {"line": 9, "column": 34}}, "loc": {"start": {"line": 13, "column": 3}, "end": {"line": 41, "column": 1}}, "line": 13}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 17, "column": 12}, "end": {"line": 17, "column": 13}}, "loc": {"start": {"line": 17, "column": 18}, "end": {"line": 21, "column": 3}}, "line": 17}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 4}, "end": {"line": 20, "column": 5}}, "type": "if", "locations": [{"start": {"line": 18, "column": 4}, "end": {"line": 20, "column": 5}}, {"start": {}, "end": {}}], "line": 18}, "1": {"loc": {"start": {"line": 18, "column": 8}, "end": {"line": 18, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 18, "column": 8}, "end": {"line": 18, "column": 18}}, {"start": {"line": 18, "column": 22}, "end": {"line": 18, "column": 38}}], "line": 18}, "2": {"loc": {"start": {"line": 24, "column": 2}, "end": {"line": 26, "column": 3}}, "type": "if", "locations": [{"start": {"line": 24, "column": 2}, "end": {"line": 26, "column": 3}}, {"start": {}, "end": {}}], "line": 24}, "3": {"loc": {"start": {"line": 29, "column": 2}, "end": {"line": 31, "column": 3}}, "type": "if", "locations": [{"start": {"line": 29, "column": 2}, "end": {"line": 31, "column": 3}}, {"start": {}, "end": {}}], "line": 29}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\layout\\Sidebar.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\layout\\Sidebar.tsx", "statementMap": {"0": {"start": {"line": 6, "column": 19}, "end": {"line": 6, "column": 32}}, "1": {"start": {"line": 7, "column": 28}, "end": {"line": 7, "column": 37}}, "2": {"start": {"line": 10, "column": 17}, "end": {"line": 14, "column": 3}}, "3": {"start": {"line": 11, "column": 4}, "end": {"line": 11, "column": 47}}, "4": {"start": {"line": 11, "column": 35}, "end": {"line": 11, "column": 47}}, "5": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 72}}, "6": {"start": {"line": 17, "column": 22}, "end": {"line": 21, "column": 44}}, "7": {"start": {"line": 24, "column": 23}, "end": {"line": 26, "column": 49}}, "8": {"start": {"line": 28, "column": 2}, "end": {"line": 153, "column": 3}}}, "fnMap": {"0": {"name": "Sidebar", "decl": {"start": {"line": 5, "column": 24}, "end": {"line": 5, "column": 31}}, "loc": {"start": {"line": 5, "column": 34}, "end": {"line": 154, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 10, "column": 17}, "end": {"line": 10, "column": 18}}, "loc": {"start": {"line": 10, "column": 34}, "end": {"line": 14, "column": 3}}, "line": 10}}, "branchMap": {"0": {"loc": {"start": {"line": 11, "column": 4}, "end": {"line": 11, "column": 47}}, "type": "if", "locations": [{"start": {"line": 11, "column": 4}, "end": {"line": 11, "column": 47}}, {"start": {}, "end": {}}], "line": 11}, "1": {"loc": {"start": {"line": 11, "column": 8}, "end": {"line": 11, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 11, "column": 8}, "end": {"line": 11, "column": 12}}, {"start": {"line": 11, "column": 16}, "end": {"line": 11, "column": 33}}], "line": 11}, "2": {"loc": {"start": {"line": 13, "column": 11}, "end": {"line": 13, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 13, "column": 12}, "end": {"line": 13, "column": 29}}, {"start": {"line": 13, "column": 33}, "end": {"line": 13, "column": 48}}, {"start": {"line": 13, "column": 53}, "end": {"line": 13, "column": 71}}], "line": 13}, "3": {"loc": {"start": {"line": 17, "column": 22}, "end": {"line": 21, "column": 44}}, "type": "cond-expr", "locations": [{"start": {"line": 18, "column": 6}, "end": {"line": 18, "column": 46}}, {"start": {"line": 19, "column": 6}, "end": {"line": 21, "column": 44}}], "line": 17}, "4": {"loc": {"start": {"line": 17, "column": 22}, "end": {"line": 17, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 17, "column": 22}, "end": {"line": 17, "column": 38}}, {"start": {"line": 17, "column": 42}, "end": {"line": 17, "column": 59}}], "line": 17}, "5": {"loc": {"start": {"line": 19, "column": 6}, "end": {"line": 21, "column": 44}}, "type": "cond-expr", "locations": [{"start": {"line": 20, "column": 8}, "end": {"line": 20, "column": 17}}, {"start": {"line": 21, "column": 8}, "end": {"line": 21, "column": 44}}], "line": 19}, "6": {"loc": {"start": {"line": 21, "column": 8}, "end": {"line": 21, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 21, "column": 8}, "end": {"line": 21, "column": 34}}, {"start": {"line": 21, "column": 38}, "end": {"line": 21, "column": 44}}], "line": 21}, "7": {"loc": {"start": {"line": 24, "column": 23}, "end": {"line": 26, "column": 49}}, "type": "cond-expr", "locations": [{"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 16}}, {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 49}}], "line": 24}, "8": {"loc": {"start": {"line": 24, "column": 23}, "end": {"line": 24, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 24, "column": 23}, "end": {"line": 24, "column": 34}}, {"start": {"line": 24, "column": 38}, "end": {"line": 24, "column": 57}}], "line": 24}, "9": {"loc": {"start": {"line": 26, "column": 13}, "end": {"line": 26, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 26, "column": 13}, "end": {"line": 26, "column": 38}}, {"start": {"line": 26, "column": 42}, "end": {"line": 26, "column": 44}}], "line": 26}, "10": {"loc": {"start": {"line": 38, "column": 59}, "end": {"line": 38, "column": 100}}, "type": "cond-expr", "locations": [{"start": {"line": 38, "column": 87}, "end": {"line": 38, "column": 95}}, {"start": {"line": 38, "column": 98}, "end": {"line": 38, "column": 100}}], "line": 38}, "11": {"loc": {"start": {"line": 48, "column": 58}, "end": {"line": 48, "column": 98}}, "type": "cond-expr", "locations": [{"start": {"line": 48, "column": 85}, "end": {"line": 48, "column": 93}}, {"start": {"line": 48, "column": 96}, "end": {"line": 48, "column": 98}}], "line": 48}, "12": {"loc": {"start": {"line": 55, "column": 54}, "end": {"line": 55, "column": 90}}, "type": "cond-expr", "locations": [{"start": {"line": 55, "column": 77}, "end": {"line": 55, "column": 85}}, {"start": {"line": 55, "column": 88}, "end": {"line": 55, "column": 90}}], "line": 55}, "13": {"loc": {"start": {"line": 62, "column": 61}, "end": {"line": 62, "column": 104}}, "type": "cond-expr", "locations": [{"start": {"line": 62, "column": 91}, "end": {"line": 62, "column": 99}}, {"start": {"line": 62, "column": 102}, "end": {"line": 62, "column": 104}}], "line": 62}, "14": {"loc": {"start": {"line": 70, "column": 60}, "end": {"line": 70, "column": 102}}, "type": "cond-expr", "locations": [{"start": {"line": 70, "column": 89}, "end": {"line": 70, "column": 97}}, {"start": {"line": 70, "column": 100}, "end": {"line": 70, "column": 102}}], "line": 70}, "15": {"loc": {"start": {"line": 78, "column": 57}, "end": {"line": 78, "column": 96}}, "type": "cond-expr", "locations": [{"start": {"line": 78, "column": 83}, "end": {"line": 78, "column": 91}}, {"start": {"line": 78, "column": 94}, "end": {"line": 78, "column": 96}}], "line": 78}, "16": {"loc": {"start": {"line": 85, "column": 57}, "end": {"line": 85, "column": 96}}, "type": "cond-expr", "locations": [{"start": {"line": 85, "column": 83}, "end": {"line": 85, "column": 91}}, {"start": {"line": 85, "column": 94}, "end": {"line": 85, "column": 96}}], "line": 85}, "17": {"loc": {"start": {"line": 92, "column": 65}, "end": {"line": 92, "column": 112}}, "type": "cond-expr", "locations": [{"start": {"line": 92, "column": 99}, "end": {"line": 92, "column": 107}}, {"start": {"line": 92, "column": 110}, "end": {"line": 92, "column": 112}}], "line": 92}, "18": {"loc": {"start": {"line": 99, "column": 57}, "end": {"line": 99, "column": 96}}, "type": "cond-expr", "locations": [{"start": {"line": 99, "column": 83}, "end": {"line": 99, "column": 91}}, {"start": {"line": 99, "column": 94}, "end": {"line": 99, "column": 96}}], "line": 99}, "19": {"loc": {"start": {"line": 106, "column": 58}, "end": {"line": 106, "column": 98}}, "type": "cond-expr", "locations": [{"start": {"line": 106, "column": 85}, "end": {"line": 106, "column": 93}}, {"start": {"line": 106, "column": 96}, "end": {"line": 106, "column": 98}}], "line": 106}, "20": {"loc": {"start": {"line": 114, "column": 58}, "end": {"line": 114, "column": 98}}, "type": "cond-expr", "locations": [{"start": {"line": 114, "column": 85}, "end": {"line": 114, "column": 93}}, {"start": {"line": 114, "column": 96}, "end": {"line": 114, "column": 98}}], "line": 114}, "21": {"loc": {"start": {"line": 121, "column": 59}, "end": {"line": 121, "column": 100}}, "type": "cond-expr", "locations": [{"start": {"line": 121, "column": 87}, "end": {"line": 121, "column": 95}}, {"start": {"line": 121, "column": 98}, "end": {"line": 121, "column": 100}}], "line": 121}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\AppContext.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\AppContext.tsx", "statementMap": {"0": {"start": {"line": 62, "column": 19}, "end": {"line": 62, "column": 65}}, "1": {"start": {"line": 70, "column": 48}, "end": {"line": 70, "column": 63}}, "2": {"start": {"line": 71, "column": 36}, "end": {"line": 71, "column": 50}}, "3": {"start": {"line": 72, "column": 26}, "end": {"line": 72, "column": 53}}, "4": {"start": {"line": 75, "column": 30}, "end": {"line": 75, "column": 70}}, "5": {"start": {"line": 76, "column": 44}, "end": {"line": 76, "column": 59}}, "6": {"start": {"line": 77, "column": 40}, "end": {"line": 77, "column": 69}}, "7": {"start": {"line": 80, "column": 25}, "end": {"line": 106, "column": 3}}, "8": {"start": {"line": 81, "column": 4}, "end": {"line": 105, "column": 5}}, "9": {"start": {"line": 82, "column": 26}, "end": {"line": 82, "column": 55}}, "10": {"start": {"line": 83, "column": 25}, "end": {"line": 83, "column": 52}}, "11": {"start": {"line": 85, "column": 6}, "end": {"line": 101, "column": 8}}, "12": {"start": {"line": 103, "column": 6}, "end": {"line": 103, "column": 58}}, "13": {"start": {"line": 104, "column": 6}, "end": {"line": 104, "column": 18}}, "14": {"start": {"line": 109, "column": 22}, "end": {"line": 143, "column": 3}}, "15": {"start": {"line": 110, "column": 4}, "end": {"line": 142, "column": 5}}, "16": {"start": {"line": 111, "column": 6}, "end": {"line": 111, "column": 29}}, "17": {"start": {"line": 112, "column": 6}, "end": {"line": 112, "column": 27}}, "18": {"start": {"line": 114, "column": 23}, "end": {"line": 114, "column": 57}}, "19": {"start": {"line": 115, "column": 19}, "end": {"line": 115, "column": 40}}, "20": {"start": {"line": 117, "column": 6}, "end": {"line": 127, "column": 7}}, "21": {"start": {"line": 119, "column": 8}, "end": {"line": 125, "column": 9}}, "22": {"start": {"line": 120, "column": 10}, "end": {"line": 120, "column": 126}}, "23": {"start": {"line": 121, "column": 10}, "end": {"line": 123, "column": 38}}, "24": {"start": {"line": 122, "column": 12}, "end": {"line": 122, "column": 40}}, "25": {"start": {"line": 124, "column": 10}, "end": {"line": 124, "column": 17}}, "26": {"start": {"line": 126, "column": 8}, "end": {"line": 126, "column": 76}}, "27": {"start": {"line": 129, "column": 6}, "end": {"line": 134, "column": 7}}, "28": {"start": {"line": 130, "column": 8}, "end": {"line": 130, "column": 31}}, "29": {"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": 85}}, "30": {"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": 51}}, "31": {"start": {"line": 136, "column": 27}, "end": {"line": 136, "column": 88}}, "32": {"start": {"line": 137, "column": 6}, "end": {"line": 137, "column": 59}}, "33": {"start": {"line": 138, "column": 6}, "end": {"line": 138, "column": 35}}, "34": {"start": {"line": 139, "column": 6}, "end": {"line": 139, "column": 22}}, "35": {"start": {"line": 141, "column": 6}, "end": {"line": 141, "column": 30}}, "36": {"start": {"line": 146, "column": 2}, "end": {"line": 219, "column": 9}}, "37": {"start": {"line": 147, "column": 20}, "end": {"line": 147, "column": 24}}, "38": {"start": {"line": 149, "column": 22}, "end": {"line": 212, "column": 5}}, "39": {"start": {"line": 150, "column": 6}, "end": {"line": 150, "column": 29}}, "40": {"start": {"line": 150, "column": 22}, "end": {"line": 150, "column": 29}}, "41": {"start": {"line": 152, "column": 6}, "end": {"line": 211, "column": 7}}, "42": {"start": {"line": 153, "column": 8}, "end": {"line": 153, "column": 73}}, "43": {"start": {"line": 156, "column": 26}, "end": {"line": 156, "column": 69}}, "44": {"start": {"line": 157, "column": 29}, "end": {"line": 157, "column": 50}}, "45": {"start": {"line": 159, "column": 8}, "end": {"line": 164, "column": 9}}, "46": {"start": {"line": 160, "column": 10}, "end": {"line": 160, "column": 96}}, "47": {"start": {"line": 161, "column": 10}, "end": {"line": 161, "column": 66}}, "48": {"start": {"line": 161, "column": 39}, "end": {"line": 161, "column": 64}}, "49": {"start": {"line": 163, "column": 10}, "end": {"line": 163, "column": 66}}, "50": {"start": {"line": 163, "column": 39}, "end": {"line": 163, "column": 64}}, "51": {"start": {"line": 167, "column": 24}, "end": {"line": 167, "column": 71}}, "52": {"start": {"line": 168, "column": 30}, "end": {"line": 168, "column": 56}}, "53": {"start": {"line": 170, "column": 8}, "end": {"line": 175, "column": 11}}, "54": {"start": {"line": 177, "column": 8}, "end": {"line": 199, "column": 9}}, "55": {"start": {"line": 178, "column": 10}, "end": {"line": 178, "column": 88}}, "56": {"start": {"line": 180, "column": 27}, "end": {"line": 180, "column": 49}}, "57": {"start": {"line": 181, "column": 10}, "end": {"line": 181, "column": 72}}, "58": {"start": {"line": 183, "column": 10}, "end": {"line": 191, "column": 11}}, "59": {"start": {"line": 184, "column": 12}, "end": {"line": 184, "column": 30}}, "60": {"start": {"line": 185, "column": 12}, "end": {"line": 185, "column": 37}}, "61": {"start": {"line": 188, "column": 12}, "end": {"line": 190, "column": 21}}, "62": {"start": {"line": 189, "column": 14}, "end": {"line": 189, "column": 28}}, "63": {"start": {"line": 193, "column": 10}, "end": {"line": 193, "column": 64}}, "64": {"start": {"line": 194, "column": 10}, "end": {"line": 198, "column": 11}}, "65": {"start": {"line": 195, "column": 12}, "end": {"line": 195, "column": 26}}, "66": {"start": {"line": 196, "column": 12}, "end": {"line": 196, "column": 38}}, "67": {"start": {"line": 197, "column": 12}, "end": {"line": 197, "column": 28}}, "68": {"start": {"line": 201, "column": 8}, "end": {"line": 201, "column": 66}}, "69": {"start": {"line": 202, "column": 8}, "end": {"line": 206, "column": 9}}, "70": {"start": {"line": 203, "column": 10}, "end": {"line": 203, "column": 24}}, "71": {"start": {"line": 204, "column": 10}, "end": {"line": 204, "column": 36}}, "72": {"start": {"line": 205, "column": 10}, "end": {"line": 205, "column": 26}}, "73": {"start": {"line": 208, "column": 8}, "end": {"line": 210, "column": 9}}, "74": {"start": {"line": 209, "column": 10}, "end": {"line": 209, "column": 30}}, "75": {"start": {"line": 214, "column": 4}, "end": {"line": 214, "column": 16}}, "76": {"start": {"line": 216, "column": 4}, "end": {"line": 218, "column": 6}}, "77": {"start": {"line": 217, "column": 6}, "end": {"line": 217, "column": 24}}, "78": {"start": {"line": 222, "column": 18}, "end": {"line": 248, "column": 3}}, "79": {"start": {"line": 223, "column": 4}, "end": {"line": 247, "column": 5}}, "80": {"start": {"line": 224, "column": 6}, "end": {"line": 224, "column": 45}}, "81": {"start": {"line": 227, "column": 6}, "end": {"line": 227, "column": 49}}, "82": {"start": {"line": 230, "column": 6}, "end": {"line": 230, "column": 68}}, "83": {"start": {"line": 233, "column": 6}, "end": {"line": 233, "column": 20}}, "84": {"start": {"line": 234, "column": 6}, "end": {"line": 234, "column": 32}}, "85": {"start": {"line": 235, "column": 6}, "end": {"line": 235, "column": 22}}, "86": {"start": {"line": 237, "column": 6}, "end": {"line": 237, "column": 18}}, "87": {"start": {"line": 239, "column": 6}, "end": {"line": 239, "column": 49}}, "88": {"start": {"line": 242, "column": 6}, "end": {"line": 242, "column": 20}}, "89": {"start": {"line": 243, "column": 6}, "end": {"line": 243, "column": 32}}, "90": {"start": {"line": 244, "column": 6}, "end": {"line": 244, "column": 22}}, "91": {"start": {"line": 246, "column": 6}, "end": {"line": 246, "column": 18}}, "92": {"start": {"line": 251, "column": 24}, "end": {"line": 253, "column": 3}}, "93": {"start": {"line": 252, "column": 4}, "end": {"line": 252, "column": 24}}, "94": {"start": {"line": 256, "column": 32}, "end": {"line": 272, "column": 3}}, "95": {"start": {"line": 257, "column": 4}, "end": {"line": 257, "column": 32}}, "96": {"start": {"line": 257, "column": 19}, "end": {"line": 257, "column": 32}}, "97": {"start": {"line": 259, "column": 4}, "end": {"line": 271, "column": 5}}, "98": {"start": {"line": 261, "column": 33}, "end": {"line": 261, "column": 72}}, "99": {"start": {"line": 262, "column": 6}, "end": {"line": 262, "column": 82}}, "100": {"start": {"line": 262, "column": 22}, "end": {"line": 262, "column": 80}}, "101": {"start": {"line": 265, "column": 6}, "end": {"line": 265, "column": 88}}, "102": {"start": {"line": 267, "column": 6}, "end": {"line": 267, "column": 18}}, "103": {"start": {"line": 269, "column": 6}, "end": {"line": 269, "column": 63}}, "104": {"start": {"line": 270, "column": 6}, "end": {"line": 270, "column": 19}}, "105": {"start": {"line": 275, "column": 18}, "end": {"line": 277, "column": 3}}, "106": {"start": {"line": 276, "column": 4}, "end": {"line": 276, "column": 48}}, "107": {"start": {"line": 280, "column": 25}, "end": {"line": 296, "column": 3}}, "108": {"start": {"line": 281, "column": 4}, "end": {"line": 281, "column": 29}}, "109": {"start": {"line": 281, "column": 15}, "end": {"line": 281, "column": 29}}, "110": {"start": {"line": 283, "column": 4}, "end": {"line": 285, "column": 5}}, "111": {"start": {"line": 284, "column": 6}, "end": {"line": 284, "column": 54}}, "112": {"start": {"line": 287, "column": 4}, "end": {"line": 289, "column": 5}}, "113": {"start": {"line": 288, "column": 6}, "end": {"line": 288, "column": 23}}, "114": {"start": {"line": 291, "column": 4}, "end": {"line": 293, "column": 5}}, "115": {"start": {"line": 292, "column": 6}, "end": {"line": 292, "column": 38}}, "116": {"start": {"line": 295, "column": 4}, "end": {"line": 295, "column": 45}}, "117": {"start": {"line": 298, "column": 26}, "end": {"line": 310, "column": 3}}, "118": {"start": {"line": 312, "column": 2}, "end": {"line": 316, "column": 4}}, "119": {"start": {"line": 320, "column": 22}, "end": {"line": 326, "column": 1}}, "120": {"start": {"line": 321, "column": 18}, "end": {"line": 321, "column": 40}}, "121": {"start": {"line": 322, "column": 2}, "end": {"line": 324, "column": 3}}, "122": {"start": {"line": 323, "column": 4}, "end": {"line": 323, "column": 65}}, "123": {"start": {"line": 325, "column": 2}, "end": {"line": 325, "column": 17}}, "124": {"start": {"line": 329, "column": 23}, "end": {"line": 332, "column": 1}}, "125": {"start": {"line": 330, "column": 65}, "end": {"line": 330, "column": 73}}, "126": {"start": {"line": 331, "column": 2}, "end": {"line": 331, "column": 64}}, "127": {"start": {"line": 334, "column": 25}, "end": {"line": 337, "column": 1}}, "128": {"start": {"line": 335, "column": 64}, "end": {"line": 335, "column": 72}}, "129": {"start": {"line": 336, "column": 2}, "end": {"line": 336, "column": 79}}, "130": {"start": {"line": 339, "column": 23}, "end": {"line": 342, "column": 1}}, "131": {"start": {"line": 340, "column": 78}, "end": {"line": 340, "column": 86}}, "132": {"start": {"line": 341, "column": 2}, "end": {"line": 341, "column": 77}}}, "fnMap": {"0": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decl": {"start": {"line": 68, "column": 16}, "end": {"line": 68, "column": 27}}, "loc": {"start": {"line": 68, "column": 60}, "end": {"line": 317, "column": 1}}, "line": 68}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 80, "column": 25}, "end": {"line": 80, "column": 26}}, "loc": {"start": {"line": 80, "column": 59}, "end": {"line": 106, "column": 3}}, "line": 80}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 109, "column": 22}, "end": {"line": 109, "column": 23}}, "loc": {"start": {"line": 109, "column": 63}, "end": {"line": 143, "column": 3}}, "line": 109}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 121, "column": 21}, "end": {"line": 121, "column": 22}}, "loc": {"start": {"line": 121, "column": 27}, "end": {"line": 123, "column": 11}}, "line": 121}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 146, "column": 12}, "end": {"line": 146, "column": 13}}, "loc": {"start": {"line": 146, "column": 18}, "end": {"line": 219, "column": 3}}, "line": 146}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 149, "column": 22}, "end": {"line": 149, "column": 23}}, "loc": {"start": {"line": 149, "column": 34}, "end": {"line": 212, "column": 5}}, "line": 149}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 161, "column": 28}, "end": {"line": 161, "column": 29}}, "loc": {"start": {"line": 161, "column": 39}, "end": {"line": 161, "column": 64}}, "line": 161}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 163, "column": 28}, "end": {"line": 163, "column": 29}}, "loc": {"start": {"line": 163, "column": 39}, "end": {"line": 163, "column": 64}}, "line": 163}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 188, "column": 23}, "end": {"line": 188, "column": 24}}, "loc": {"start": {"line": 188, "column": 29}, "end": {"line": 190, "column": 13}}, "line": 188}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 216, "column": 11}, "end": {"line": 216, "column": 12}}, "loc": {"start": {"line": 216, "column": 17}, "end": {"line": 218, "column": 5}}, "line": 216}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 222, "column": 18}, "end": {"line": 222, "column": 19}}, "loc": {"start": {"line": 222, "column": 48}, "end": {"line": 248, "column": 3}}, "line": 222}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 251, "column": 24}, "end": {"line": 251, "column": 25}}, "loc": {"start": {"line": 251, "column": 51}, "end": {"line": 253, "column": 3}}, "line": 251}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 256, "column": 32}, "end": {"line": 256, "column": 33}}, "loc": {"start": {"line": 256, "column": 99}, "end": {"line": 272, "column": 3}}, "line": 256}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 262, "column": 14}, "end": {"line": 262, "column": 15}}, "loc": {"start": {"line": 262, "column": 22}, "end": {"line": 262, "column": 80}}, "line": 262}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 275, "column": 18}, "end": {"line": 275, "column": 19}}, "loc": {"start": {"line": 275, "column": 45}, "end": {"line": 277, "column": 3}}, "line": 275}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 280, "column": 25}, "end": {"line": 280, "column": 26}}, "loc": {"start": {"line": 280, "column": 39}, "end": {"line": 296, "column": 3}}, "line": 280}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 320, "column": 22}, "end": {"line": 320, "column": 23}}, "loc": {"start": {"line": 320, "column": 28}, "end": {"line": 326, "column": 1}}, "line": 320}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 329, "column": 23}, "end": {"line": 329, "column": 24}}, "loc": {"start": {"line": 329, "column": 29}, "end": {"line": 332, "column": 1}}, "line": 329}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 334, "column": 25}, "end": {"line": 334, "column": 26}}, "loc": {"start": {"line": 334, "column": 31}, "end": {"line": 337, "column": 1}}, "line": 334}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 339, "column": 23}, "end": {"line": 339, "column": 24}}, "loc": {"start": {"line": 339, "column": 29}, "end": {"line": 342, "column": 1}}, "line": 339}}, "branchMap": {"0": {"loc": {"start": {"line": 87, "column": 15}, "end": {"line": 87, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 87, "column": 15}, "end": {"line": 87, "column": 31}}, {"start": {"line": 87, "column": 35}, "end": {"line": 87, "column": 37}}], "line": 87}, "1": {"loc": {"start": {"line": 91, "column": 15}, "end": {"line": 91, "column": 87}}, "type": "cond-expr", "locations": [{"start": {"line": 91, "column": 44}, "end": {"line": 91, "column": 82}}, {"start": {"line": 91, "column": 85}, "end": {"line": 91, "column": 87}}], "line": 91}, "2": {"loc": {"start": {"line": 109, "column": 29}, "end": {"line": 109, "column": 43}}, "type": "default-arg", "locations": [{"start": {"line": 109, "column": 42}, "end": {"line": 109, "column": 43}}], "line": 109}, "3": {"loc": {"start": {"line": 117, "column": 6}, "end": {"line": 127, "column": 7}}, "type": "if", "locations": [{"start": {"line": 117, "column": 6}, "end": {"line": 127, "column": 7}}, {"start": {}, "end": {}}], "line": 117}, "4": {"loc": {"start": {"line": 119, "column": 8}, "end": {"line": 125, "column": 9}}, "type": "if", "locations": [{"start": {"line": 119, "column": 8}, "end": {"line": 125, "column": 9}}, {"start": {}, "end": {}}], "line": 119}, "5": {"loc": {"start": {"line": 119, "column": 12}, "end": {"line": 119, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 119, "column": 12}, "end": {"line": 119, "column": 35}}, {"start": {"line": 119, "column": 39}, "end": {"line": 119, "column": 53}}], "line": 119}, "6": {"loc": {"start": {"line": 126, "column": 24}, "end": {"line": 126, "column": 74}}, "type": "binary-expr", "locations": [{"start": {"line": 126, "column": 24}, "end": {"line": 126, "column": 34}}, {"start": {"line": 126, "column": 38}, "end": {"line": 126, "column": 74}}], "line": 126}, "7": {"loc": {"start": {"line": 129, "column": 6}, "end": {"line": 134, "column": 7}}, "type": "if", "locations": [{"start": {"line": 129, "column": 6}, "end": {"line": 134, "column": 7}}, {"start": {"line": 132, "column": 13}, "end": {"line": 134, "column": 7}}], "line": 129}, "8": {"loc": {"start": {"line": 129, "column": 10}, "end": {"line": 129, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 129, "column": 10}, "end": {"line": 129, "column": 22}}, {"start": {"line": 129, "column": 26}, "end": {"line": 129, "column": 37}}], "line": 129}, "9": {"loc": {"start": {"line": 136, "column": 27}, "end": {"line": 136, "column": 88}}, "type": "cond-expr", "locations": [{"start": {"line": 136, "column": 50}, "end": {"line": 136, "column": 61}}, {"start": {"line": 136, "column": 64}, "end": {"line": 136, "column": 88}}], "line": 136}, "10": {"loc": {"start": {"line": 150, "column": 6}, "end": {"line": 150, "column": 29}}, "type": "if", "locations": [{"start": {"line": 150, "column": 6}, "end": {"line": 150, "column": 29}}, {"start": {}, "end": {}}], "line": 150}, "11": {"loc": {"start": {"line": 159, "column": 8}, "end": {"line": 164, "column": 9}}, "type": "if", "locations": [{"start": {"line": 159, "column": 8}, "end": {"line": 164, "column": 9}}, {"start": {"line": 162, "column": 15}, "end": {"line": 164, "column": 9}}], "line": 159}, "12": {"loc": {"start": {"line": 177, "column": 8}, "end": {"line": 199, "column": 9}}, "type": "if", "locations": [{"start": {"line": 177, "column": 8}, "end": {"line": 199, "column": 9}}, {"start": {"line": 192, "column": 15}, "end": {"line": 199, "column": 9}}], "line": 177}, "13": {"loc": {"start": {"line": 183, "column": 10}, "end": {"line": 191, "column": 11}}, "type": "if", "locations": [{"start": {"line": 183, "column": 10}, "end": {"line": 191, "column": 11}}, {"start": {}, "end": {}}], "line": 183}, "14": {"loc": {"start": {"line": 183, "column": 14}, "end": {"line": 183, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 183, "column": 14}, "end": {"line": 183, "column": 23}}, {"start": {"line": 183, "column": 27}, "end": {"line": 183, "column": 35}}], "line": 183}, "15": {"loc": {"start": {"line": 194, "column": 10}, "end": {"line": 198, "column": 11}}, "type": "if", "locations": [{"start": {"line": 194, "column": 10}, "end": {"line": 198, "column": 11}}, {"start": {}, "end": {}}], "line": 194}, "16": {"loc": {"start": {"line": 202, "column": 8}, "end": {"line": 206, "column": 9}}, "type": "if", "locations": [{"start": {"line": 202, "column": 8}, "end": {"line": 206, "column": 9}}, {"start": {}, "end": {}}], "line": 202}, "17": {"loc": {"start": {"line": 208, "column": 8}, "end": {"line": 210, "column": 9}}, "type": "if", "locations": [{"start": {"line": 208, "column": 8}, "end": {"line": 210, "column": 9}}, {"start": {}, "end": {}}], "line": 208}, "18": {"loc": {"start": {"line": 257, "column": 4}, "end": {"line": 257, "column": 32}}, "type": "if", "locations": [{"start": {"line": 257, "column": 4}, "end": {"line": 257, "column": 32}}, {"start": {}, "end": {}}], "line": 257}, "19": {"loc": {"start": {"line": 262, "column": 22}, "end": {"line": 262, "column": 80}}, "type": "cond-expr", "locations": [{"start": {"line": 262, "column": 29}, "end": {"line": 262, "column": 73}}, {"start": {"line": 262, "column": 76}, "end": {"line": 262, "column": 80}}], "line": 262}, "20": {"loc": {"start": {"line": 276, "column": 11}, "end": {"line": 276, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 276, "column": 11}, "end": {"line": 276, "column": 38}}, {"start": {"line": 276, "column": 42}, "end": {"line": 276, "column": 47}}], "line": 276}, "21": {"loc": {"start": {"line": 281, "column": 4}, "end": {"line": 281, "column": 29}}, "type": "if", "locations": [{"start": {"line": 281, "column": 4}, "end": {"line": 281, "column": 29}}, {"start": {}, "end": {}}], "line": 281}, "22": {"loc": {"start": {"line": 283, "column": 4}, "end": {"line": 285, "column": 5}}, "type": "if", "locations": [{"start": {"line": 283, "column": 4}, "end": {"line": 285, "column": 5}}, {"start": {}, "end": {}}], "line": 283}, "23": {"loc": {"start": {"line": 283, "column": 8}, "end": {"line": 283, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 283, "column": 8}, "end": {"line": 283, "column": 23}}, {"start": {"line": 283, "column": 27}, "end": {"line": 283, "column": 43}}], "line": 283}, "24": {"loc": {"start": {"line": 287, "column": 4}, "end": {"line": 289, "column": 5}}, "type": "if", "locations": [{"start": {"line": 287, "column": 4}, "end": {"line": 289, "column": 5}}, {"start": {}, "end": {}}], "line": 287}, "25": {"loc": {"start": {"line": 291, "column": 4}, "end": {"line": 293, "column": 5}}, "type": "if", "locations": [{"start": {"line": 291, "column": 4}, "end": {"line": 293, "column": 5}}, {"start": {}, "end": {}}], "line": 291}, "26": {"loc": {"start": {"line": 322, "column": 2}, "end": {"line": 324, "column": 3}}, "type": "if", "locations": [{"start": {"line": 322, "column": 2}, "end": {"line": 324, "column": 3}}, {"start": {}, "end": {}}], "line": 322}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\AuthContext.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\AuthContext.tsx", "statementMap": {"0": {"start": {"line": 19, "column": 20}, "end": {"line": 25, "column": 2}}, "1": {"start": {"line": 23, "column": 23}, "end": {"line": 23, "column": 28}}, "2": {"start": {"line": 24, "column": 17}, "end": {"line": 24, "column": 22}}, "3": {"start": {"line": 28, "column": 42}, "end": {"line": 28, "column": 66}}, "4": {"start": {"line": 29, "column": 36}, "end": {"line": 29, "column": 59}}, "5": {"start": {"line": 30, "column": 26}, "end": {"line": 30, "column": 53}}, "6": {"start": {"line": 33, "column": 25}, "end": {"line": 50, "column": 3}}, "7": {"start": {"line": 34, "column": 4}, "end": {"line": 49, "column": 5}}, "8": {"start": {"line": 35, "column": 26}, "end": {"line": 35, "column": 55}}, "9": {"start": {"line": 36, "column": 25}, "end": {"line": 36, "column": 52}}, "10": {"start": {"line": 38, "column": 6}, "end": {"line": 45, "column": 8}}, "11": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 58}}, "12": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 18}}, "13": {"start": {"line": 52, "column": 2}, "end": {"line": 150, "column": 9}}, "14": {"start": {"line": 53, "column": 20}, "end": {"line": 53, "column": 24}}, "15": {"start": {"line": 55, "column": 22}, "end": {"line": 132, "column": 5}}, "16": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 29}}, "17": {"start": {"line": 56, "column": 22}, "end": {"line": 56, "column": 29}}, "18": {"start": {"line": 58, "column": 6}, "end": {"line": 131, "column": 7}}, "19": {"start": {"line": 59, "column": 8}, "end": {"line": 59, "column": 74}}, "20": {"start": {"line": 62, "column": 26}, "end": {"line": 62, "column": 69}}, "21": {"start": {"line": 63, "column": 29}, "end": {"line": 63, "column": 50}}, "22": {"start": {"line": 65, "column": 8}, "end": {"line": 72, "column": 9}}, "23": {"start": {"line": 66, "column": 10}, "end": {"line": 66, "column": 97}}, "24": {"start": {"line": 68, "column": 10}, "end": {"line": 68, "column": 66}}, "25": {"start": {"line": 68, "column": 39}, "end": {"line": 68, "column": 64}}, "26": {"start": {"line": 71, "column": 10}, "end": {"line": 71, "column": 66}}, "27": {"start": {"line": 71, "column": 39}, "end": {"line": 71, "column": 64}}, "28": {"start": {"line": 75, "column": 24}, "end": {"line": 75, "column": 71}}, "29": {"start": {"line": 76, "column": 30}, "end": {"line": 76, "column": 56}}, "30": {"start": {"line": 78, "column": 8}, "end": {"line": 84, "column": 11}}, "31": {"start": {"line": 86, "column": 8}, "end": {"line": 115, "column": 9}}, "32": {"start": {"line": 87, "column": 10}, "end": {"line": 87, "column": 89}}, "33": {"start": {"line": 89, "column": 10}, "end": {"line": 108, "column": 11}}, "34": {"start": {"line": 90, "column": 29}, "end": {"line": 90, "column": 51}}, "35": {"start": {"line": 91, "column": 12}, "end": {"line": 91, "column": 75}}, "36": {"start": {"line": 93, "column": 12}, "end": {"line": 101, "column": 13}}, "37": {"start": {"line": 94, "column": 14}, "end": {"line": 94, "column": 32}}, "38": {"start": {"line": 95, "column": 14}, "end": {"line": 95, "column": 33}}, "39": {"start": {"line": 96, "column": 14}, "end": {"line": 96, "column": 78}}, "40": {"start": {"line": 97, "column": 19}, "end": {"line": 101, "column": 13}}, "41": {"start": {"line": 98, "column": 14}, "end": {"line": 98, "column": 82}}, "42": {"start": {"line": 99, "column": 14}, "end": {"line": 99, "column": 28}}, "43": {"start": {"line": 100, "column": 14}, "end": {"line": 100, "column": 34}}, "44": {"start": {"line": 103, "column": 12}, "end": {"line": 103, "column": 82}}, "45": {"start": {"line": 104, "column": 12}, "end": {"line": 107, "column": 13}}, "46": {"start": {"line": 105, "column": 14}, "end": {"line": 105, "column": 28}}, "47": {"start": {"line": 106, "column": 14}, "end": {"line": 106, "column": 34}}, "48": {"start": {"line": 110, "column": 10}, "end": {"line": 110, "column": 65}}, "49": {"start": {"line": 111, "column": 10}, "end": {"line": 114, "column": 11}}, "50": {"start": {"line": 112, "column": 12}, "end": {"line": 112, "column": 26}}, "51": {"start": {"line": 113, "column": 12}, "end": {"line": 113, "column": 32}}, "52": {"start": {"line": 117, "column": 8}, "end": {"line": 117, "column": 67}}, "53": {"start": {"line": 118, "column": 8}, "end": {"line": 121, "column": 9}}, "54": {"start": {"line": 119, "column": 10}, "end": {"line": 119, "column": 24}}, "55": {"start": {"line": 120, "column": 10}, "end": {"line": 120, "column": 30}}, "56": {"start": {"line": 123, "column": 8}, "end": {"line": 130, "column": 9}}, "57": {"start": {"line": 124, "column": 10}, "end": {"line": 124, "column": 30}}, "58": {"start": {"line": 125, "column": 10}, "end": {"line": 129, "column": 13}}, "59": {"start": {"line": 134, "column": 4}, "end": {"line": 134, "column": 16}}, "60": {"start": {"line": 137, "column": 32}, "end": {"line": 142, "column": 5}}, "61": {"start": {"line": 138, "column": 6}, "end": {"line": 141, "column": 7}}, "62": {"start": {"line": 139, "column": 8}, "end": {"line": 139, "column": 79}}, "63": {"start": {"line": 140, "column": 8}, "end": {"line": 140, "column": 20}}, "64": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 60}}, "65": {"start": {"line": 146, "column": 4}, "end": {"line": 149, "column": 6}}, "66": {"start": {"line": 147, "column": 6}, "end": {"line": 147, "column": 24}}, "67": {"start": {"line": 148, "column": 6}, "end": {"line": 148, "column": 65}}, "68": {"start": {"line": 153, "column": 18}, "end": {"line": 155, "column": 3}}, "69": {"start": {"line": 154, "column": 4}, "end": {"line": 154, "column": 48}}, "70": {"start": {"line": 158, "column": 18}, "end": {"line": 186, "column": 3}}, "71": {"start": {"line": 159, "column": 4}, "end": {"line": 185, "column": 5}}, "72": {"start": {"line": 160, "column": 6}, "end": {"line": 160, "column": 45}}, "73": {"start": {"line": 163, "column": 6}, "end": {"line": 163, "column": 49}}, "74": {"start": {"line": 166, "column": 6}, "end": {"line": 166, "column": 68}}, "75": {"start": {"line": 169, "column": 6}, "end": {"line": 169, "column": 20}}, "76": {"start": {"line": 170, "column": 6}, "end": {"line": 170, "column": 26}}, "77": {"start": {"line": 172, "column": 6}, "end": {"line": 172, "column": 18}}, "78": {"start": {"line": 174, "column": 6}, "end": {"line": 174, "column": 49}}, "79": {"start": {"line": 177, "column": 6}, "end": {"line": 177, "column": 49}}, "80": {"start": {"line": 178, "column": 6}, "end": {"line": 178, "column": 68}}, "81": {"start": {"line": 181, "column": 6}, "end": {"line": 181, "column": 20}}, "82": {"start": {"line": 182, "column": 6}, "end": {"line": 182, "column": 26}}, "83": {"start": {"line": 184, "column": 6}, "end": {"line": 184, "column": 18}}, "84": {"start": {"line": 188, "column": 2}, "end": {"line": 200, "column": 4}}, "85": {"start": {"line": 204, "column": 23}, "end": {"line": 204, "column": 52}}, "86": {"start": {"line": 204, "column": 29}, "end": {"line": 204, "column": 52}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 23, "column": 11}, "end": {"line": 23, "column": 12}}, "loc": {"start": {"line": 23, "column": 23}, "end": {"line": 23, "column": 28}}, "line": 23}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 24, "column": 11}, "end": {"line": 24, "column": 12}}, "loc": {"start": {"line": 24, "column": 17}, "end": {"line": 24, "column": 22}}, "line": 24}, "2": {"name": "<PERSON>th<PERSON><PERSON><PERSON>", "decl": {"start": {"line": 27, "column": 16}, "end": {"line": 27, "column": 28}}, "loc": {"start": {"line": 27, "column": 68}, "end": {"line": 201, "column": 1}}, "line": 27}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 33, "column": 25}, "end": {"line": 33, "column": 26}}, "loc": {"start": {"line": 33, "column": 59}, "end": {"line": 50, "column": 3}}, "line": 33}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 52, "column": 12}, "end": {"line": 52, "column": 13}}, "loc": {"start": {"line": 52, "column": 18}, "end": {"line": 150, "column": 3}}, "line": 52}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 55, "column": 22}, "end": {"line": 55, "column": 23}}, "loc": {"start": {"line": 55, "column": 34}, "end": {"line": 132, "column": 5}}, "line": 55}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 68, "column": 28}, "end": {"line": 68, "column": 29}}, "loc": {"start": {"line": 68, "column": 39}, "end": {"line": 68, "column": 64}}, "line": 68}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 71, "column": 28}, "end": {"line": 71, "column": 29}}, "loc": {"start": {"line": 71, "column": 39}, "end": {"line": 71, "column": 64}}, "line": 71}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 137, "column": 32}, "end": {"line": 137, "column": 33}}, "loc": {"start": {"line": 137, "column": 53}, "end": {"line": 142, "column": 5}}, "line": 137}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 146, "column": 11}, "end": {"line": 146, "column": 12}}, "loc": {"start": {"line": 146, "column": 17}, "end": {"line": 149, "column": 5}}, "line": 146}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 153, "column": 18}, "end": {"line": 153, "column": 19}}, "loc": {"start": {"line": 153, "column": 36}, "end": {"line": 155, "column": 3}}, "line": 153}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 158, "column": 18}, "end": {"line": 158, "column": 19}}, "loc": {"start": {"line": 158, "column": 48}, "end": {"line": 186, "column": 3}}, "line": 158}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 204, "column": 23}, "end": {"line": 204, "column": 24}}, "loc": {"start": {"line": 204, "column": 29}, "end": {"line": 204, "column": 52}}, "line": 204}}, "branchMap": {"0": {"loc": {"start": {"line": 40, "column": 15}, "end": {"line": 40, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 40, "column": 15}, "end": {"line": 40, "column": 31}}, {"start": {"line": 40, "column": 35}, "end": {"line": 40, "column": 37}}], "line": 40}, "1": {"loc": {"start": {"line": 44, "column": 15}, "end": {"line": 44, "column": 87}}, "type": "cond-expr", "locations": [{"start": {"line": 44, "column": 44}, "end": {"line": 44, "column": 82}}, {"start": {"line": 44, "column": 85}, "end": {"line": 44, "column": 87}}], "line": 44}, "2": {"loc": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 29}}, "type": "if", "locations": [{"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 29}}, {"start": {}, "end": {}}], "line": 56}, "3": {"loc": {"start": {"line": 65, "column": 8}, "end": {"line": 72, "column": 9}}, "type": "if", "locations": [{"start": {"line": 65, "column": 8}, "end": {"line": 72, "column": 9}}, {"start": {"line": 69, "column": 15}, "end": {"line": 72, "column": 9}}], "line": 65}, "4": {"loc": {"start": {"line": 86, "column": 8}, "end": {"line": 115, "column": 9}}, "type": "if", "locations": [{"start": {"line": 86, "column": 8}, "end": {"line": 115, "column": 9}}, {"start": {"line": 109, "column": 15}, "end": {"line": 115, "column": 9}}], "line": 86}, "5": {"loc": {"start": {"line": 93, "column": 12}, "end": {"line": 101, "column": 13}}, "type": "if", "locations": [{"start": {"line": 93, "column": 12}, "end": {"line": 101, "column": 13}}, {"start": {"line": 97, "column": 19}, "end": {"line": 101, "column": 13}}], "line": 93}, "6": {"loc": {"start": {"line": 93, "column": 16}, "end": {"line": 93, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 93, "column": 16}, "end": {"line": 93, "column": 25}}, {"start": {"line": 93, "column": 29}, "end": {"line": 93, "column": 37}}], "line": 93}, "7": {"loc": {"start": {"line": 97, "column": 19}, "end": {"line": 101, "column": 13}}, "type": "if", "locations": [{"start": {"line": 97, "column": 19}, "end": {"line": 101, "column": 13}}, {"start": {}, "end": {}}], "line": 97}, "8": {"loc": {"start": {"line": 104, "column": 12}, "end": {"line": 107, "column": 13}}, "type": "if", "locations": [{"start": {"line": 104, "column": 12}, "end": {"line": 107, "column": 13}}, {"start": {}, "end": {}}], "line": 104}, "9": {"loc": {"start": {"line": 111, "column": 10}, "end": {"line": 114, "column": 11}}, "type": "if", "locations": [{"start": {"line": 111, "column": 10}, "end": {"line": 114, "column": 11}}, {"start": {}, "end": {}}], "line": 111}, "10": {"loc": {"start": {"line": 118, "column": 8}, "end": {"line": 121, "column": 9}}, "type": "if", "locations": [{"start": {"line": 118, "column": 8}, "end": {"line": 121, "column": 9}}, {"start": {}, "end": {}}], "line": 118}, "11": {"loc": {"start": {"line": 123, "column": 8}, "end": {"line": 130, "column": 9}}, "type": "if", "locations": [{"start": {"line": 123, "column": 8}, "end": {"line": 130, "column": 9}}, {"start": {}, "end": {}}], "line": 123}, "12": {"loc": {"start": {"line": 138, "column": 6}, "end": {"line": 141, "column": 7}}, "type": "if", "locations": [{"start": {"line": 138, "column": 6}, "end": {"line": 141, "column": 7}}, {"start": {}, "end": {}}], "line": 138}, "13": {"loc": {"start": {"line": 154, "column": 11}, "end": {"line": 154, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 154, "column": 11}, "end": {"line": 154, "column": 38}}, {"start": {"line": 154, "column": 42}, "end": {"line": 154, "column": 47}}], "line": 154}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\ClientContext.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\ClientContext.tsx", "statementMap": {"0": {"start": {"line": 15, "column": 22}, "end": {"line": 22, "column": 2}}, "1": {"start": {"line": 18, "column": 33}, "end": {"line": 18, "column": 37}}, "2": {"start": {"line": 19, "column": 29}, "end": {"line": 19, "column": 33}}, "3": {"start": {"line": 20, "column": 32}, "end": {"line": 20, "column": 36}}, "4": {"start": {"line": 21, "column": 36}, "end": {"line": 21, "column": 41}}, "5": {"start": {"line": 25, "column": 30}, "end": {"line": 25, "column": 59}}, "6": {"start": {"line": 26, "column": 36}, "end": {"line": 26, "column": 51}}, "7": {"start": {"line": 27, "column": 19}, "end": {"line": 27, "column": 28}}, "8": {"start": {"line": 30, "column": 28}, "end": {"line": 53, "column": 8}}, "9": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 29}}, "10": {"start": {"line": 31, "column": 17}, "end": {"line": 31, "column": 29}}, "11": {"start": {"line": 33, "column": 4}, "end": {"line": 52, "column": 5}}, "12": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 25}}, "13": {"start": {"line": 35, "column": 23}, "end": {"line": 35, "column": 94}}, "14": {"start": {"line": 37, "column": 6}, "end": {"line": 42, "column": 7}}, "15": {"start": {"line": 38, "column": 8}, "end": {"line": 40, "column": 9}}, "16": {"start": {"line": 39, "column": 10}, "end": {"line": 39, "column": 73}}, "17": {"start": {"line": 41, "column": 8}, "end": {"line": 41, "column": 20}}, "18": {"start": {"line": 44, "column": 25}, "end": {"line": 44, "column": 46}}, "19": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 28}}, "20": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 24}}, "21": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 57}}, "22": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 18}}, "23": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 26}}, "24": {"start": {"line": 56, "column": 24}, "end": {"line": 79, "column": 8}}, "25": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 25}}, "26": {"start": {"line": 57, "column": 13}, "end": {"line": 57, "column": 25}}, "27": {"start": {"line": 59, "column": 4}, "end": {"line": 78, "column": 5}}, "28": {"start": {"line": 60, "column": 6}, "end": {"line": 60, "column": 25}}, "29": {"start": {"line": 61, "column": 23}, "end": {"line": 61, "column": 76}}, "30": {"start": {"line": 63, "column": 6}, "end": {"line": 68, "column": 7}}, "31": {"start": {"line": 64, "column": 8}, "end": {"line": 66, "column": 9}}, "32": {"start": {"line": 65, "column": 10}, "end": {"line": 65, "column": 73}}, "33": {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 20}}, "34": {"start": {"line": 70, "column": 25}, "end": {"line": 70, "column": 46}}, "35": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 28}}, "36": {"start": {"line": 72, "column": 6}, "end": {"line": 72, "column": 24}}, "37": {"start": {"line": 74, "column": 6}, "end": {"line": 74, "column": 57}}, "38": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 18}}, "39": {"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": 26}}, "40": {"start": {"line": 82, "column": 31}, "end": {"line": 109, "column": 18}}, "41": {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 34}}, "42": {"start": {"line": 83, "column": 21}, "end": {"line": 83, "column": 34}}, "43": {"start": {"line": 85, "column": 4}, "end": {"line": 108, "column": 5}}, "44": {"start": {"line": 86, "column": 6}, "end": {"line": 86, "column": 25}}, "45": {"start": {"line": 87, "column": 23}, "end": {"line": 93, "column": 8}}, "46": {"start": {"line": 95, "column": 6}, "end": {"line": 98, "column": 7}}, "47": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": 80}}, "48": {"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 21}}, "49": {"start": {"line": 100, "column": 28}, "end": {"line": 100, "column": 49}}, "50": {"start": {"line": 101, "column": 6}, "end": {"line": 101, "column": 31}}, "51": {"start": {"line": 102, "column": 6}, "end": {"line": 102, "column": 18}}, "52": {"start": {"line": 104, "column": 6}, "end": {"line": 104, "column": 62}}, "53": {"start": {"line": 105, "column": 6}, "end": {"line": 105, "column": 19}}, "54": {"start": {"line": 107, "column": 6}, "end": {"line": 107, "column": 26}}, "55": {"start": {"line": 112, "column": 27}, "end": {"line": 135, "column": 8}}, "56": {"start": {"line": 113, "column": 4}, "end": {"line": 113, "column": 28}}, "57": {"start": {"line": 113, "column": 16}, "end": {"line": 113, "column": 28}}, "58": {"start": {"line": 115, "column": 4}, "end": {"line": 134, "column": 5}}, "59": {"start": {"line": 116, "column": 6}, "end": {"line": 116, "column": 25}}, "60": {"start": {"line": 117, "column": 23}, "end": {"line": 117, "column": 92}}, "61": {"start": {"line": 119, "column": 6}, "end": {"line": 124, "column": 7}}, "62": {"start": {"line": 120, "column": 8}, "end": {"line": 122, "column": 9}}, "63": {"start": {"line": 121, "column": 10}, "end": {"line": 121, "column": 73}}, "64": {"start": {"line": 123, "column": 8}, "end": {"line": 123, "column": 20}}, "65": {"start": {"line": 126, "column": 25}, "end": {"line": 126, "column": 46}}, "66": {"start": {"line": 127, "column": 6}, "end": {"line": 127, "column": 28}}, "67": {"start": {"line": 128, "column": 6}, "end": {"line": 128, "column": 24}}, "68": {"start": {"line": 130, "column": 6}, "end": {"line": 130, "column": 66}}, "69": {"start": {"line": 131, "column": 6}, "end": {"line": 131, "column": 18}}, "70": {"start": {"line": 133, "column": 6}, "end": {"line": 133, "column": 26}}, "71": {"start": {"line": 137, "column": 2}, "end": {"line": 150, "column": 4}}, "72": {"start": {"line": 154, "column": 25}, "end": {"line": 154, "column": 56}}, "73": {"start": {"line": 154, "column": 31}, "end": {"line": 154, "column": 56}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 18, "column": 21}, "end": {"line": 18, "column": 22}}, "loc": {"start": {"line": 18, "column": 33}, "end": {"line": 18, "column": 37}}, "line": 18}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 19, "column": 17}, "end": {"line": 19, "column": 18}}, "loc": {"start": {"line": 19, "column": 29}, "end": {"line": 19, "column": 33}}, "line": 19}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 20, "column": 20}, "end": {"line": 20, "column": 21}}, "loc": {"start": {"line": 20, "column": 32}, "end": {"line": 20, "column": 36}}, "line": 20}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 21, "column": 24}, "end": {"line": 21, "column": 25}}, "loc": {"start": {"line": 21, "column": 36}, "end": {"line": 21, "column": 41}}, "line": 21}, "4": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decl": {"start": {"line": 24, "column": 16}, "end": {"line": 24, "column": 30}}, "loc": {"start": {"line": 24, "column": 70}, "end": {"line": 151, "column": 1}}, "line": 24}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 30, "column": 40}, "end": {"line": 30, "column": 41}}, "loc": {"start": {"line": 30, "column": 90}, "end": {"line": 53, "column": 3}}, "line": 30}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 56, "column": 36}, "end": {"line": 56, "column": 37}}, "loc": {"start": {"line": 56, "column": 82}, "end": {"line": 79, "column": 3}}, "line": 56}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 82, "column": 43}, "end": {"line": 82, "column": 44}}, "loc": {"start": {"line": 82, "column": 106}, "end": {"line": 109, "column": 3}}, "line": 82}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 112, "column": 39}, "end": {"line": 112, "column": 40}}, "loc": {"start": {"line": 112, "column": 88}, "end": {"line": 135, "column": 3}}, "line": 112}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 154, "column": 25}, "end": {"line": 154, "column": 26}}, "loc": {"start": {"line": 154, "column": 31}, "end": {"line": 154, "column": 56}}, "line": 154}}, "branchMap": {"0": {"loc": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 29}}, "type": "if", "locations": [{"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 29}}, {"start": {}, "end": {}}], "line": 31}, "1": {"loc": {"start": {"line": 37, "column": 6}, "end": {"line": 42, "column": 7}}, "type": "if", "locations": [{"start": {"line": 37, "column": 6}, "end": {"line": 42, "column": 7}}, {"start": {}, "end": {}}], "line": 37}, "2": {"loc": {"start": {"line": 38, "column": 8}, "end": {"line": 40, "column": 9}}, "type": "if", "locations": [{"start": {"line": 38, "column": 8}, "end": {"line": 40, "column": 9}}, {"start": {}, "end": {}}], "line": 38}, "3": {"loc": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 25}}, "type": "if", "locations": [{"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 25}}, {"start": {}, "end": {}}], "line": 57}, "4": {"loc": {"start": {"line": 63, "column": 6}, "end": {"line": 68, "column": 7}}, "type": "if", "locations": [{"start": {"line": 63, "column": 6}, "end": {"line": 68, "column": 7}}, {"start": {}, "end": {}}], "line": 63}, "5": {"loc": {"start": {"line": 64, "column": 8}, "end": {"line": 66, "column": 9}}, "type": "if", "locations": [{"start": {"line": 64, "column": 8}, "end": {"line": 66, "column": 9}}, {"start": {}, "end": {}}], "line": 64}, "6": {"loc": {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 34}}, "type": "if", "locations": [{"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 34}}, {"start": {}, "end": {}}], "line": 83}, "7": {"loc": {"start": {"line": 95, "column": 6}, "end": {"line": 98, "column": 7}}, "type": "if", "locations": [{"start": {"line": 95, "column": 6}, "end": {"line": 98, "column": 7}}, {"start": {}, "end": {}}], "line": 95}, "8": {"loc": {"start": {"line": 113, "column": 4}, "end": {"line": 113, "column": 28}}, "type": "if", "locations": [{"start": {"line": 113, "column": 4}, "end": {"line": 113, "column": 28}}, {"start": {}, "end": {}}], "line": 113}, "9": {"loc": {"start": {"line": 119, "column": 6}, "end": {"line": 124, "column": 7}}, "type": "if", "locations": [{"start": {"line": 119, "column": 6}, "end": {"line": 124, "column": 7}}, {"start": {}, "end": {}}], "line": 119}, "10": {"loc": {"start": {"line": 120, "column": 8}, "end": {"line": 122, "column": 9}}, "type": "if", "locations": [{"start": {"line": 120, "column": 8}, "end": {"line": 122, "column": 9}}, {"start": {}, "end": {}}], "line": 120}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\TenantContext.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\TenantContext.tsx", "statementMap": {"0": {"start": {"line": 13, "column": 22}, "end": {"line": 13, "column": 78}}, "1": {"start": {"line": 20, "column": 30}, "end": {"line": 20, "column": 70}}, "2": {"start": {"line": 21, "column": 32}, "end": {"line": 21, "column": 46}}, "3": {"start": {"line": 22, "column": 28}, "end": {"line": 22, "column": 57}}, "4": {"start": {"line": 24, "column": 22}, "end": {"line": 58, "column": 3}}, "5": {"start": {"line": 25, "column": 4}, "end": {"line": 57, "column": 5}}, "6": {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 23}}, "7": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 21}}, "8": {"start": {"line": 29, "column": 23}, "end": {"line": 29, "column": 57}}, "9": {"start": {"line": 30, "column": 19}, "end": {"line": 30, "column": 40}}, "10": {"start": {"line": 32, "column": 6}, "end": {"line": 42, "column": 7}}, "11": {"start": {"line": 34, "column": 8}, "end": {"line": 40, "column": 9}}, "12": {"start": {"line": 35, "column": 10}, "end": {"line": 35, "column": 126}}, "13": {"start": {"line": 36, "column": 10}, "end": {"line": 38, "column": 38}}, "14": {"start": {"line": 37, "column": 12}, "end": {"line": 37, "column": 40}}, "15": {"start": {"line": 39, "column": 10}, "end": {"line": 39, "column": 17}}, "16": {"start": {"line": 41, "column": 8}, "end": {"line": 41, "column": 76}}, "17": {"start": {"line": 44, "column": 6}, "end": {"line": 49, "column": 7}}, "18": {"start": {"line": 45, "column": 8}, "end": {"line": 45, "column": 31}}, "19": {"start": {"line": 46, "column": 8}, "end": {"line": 46, "column": 85}}, "20": {"start": {"line": 48, "column": 8}, "end": {"line": 48, "column": 51}}, "21": {"start": {"line": 51, "column": 27}, "end": {"line": 51, "column": 88}}, "22": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 59}}, "23": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 29}}, "24": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 22}}, "25": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 24}}, "26": {"start": {"line": 60, "column": 24}, "end": {"line": 62, "column": 3}}, "27": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 24}}, "28": {"start": {"line": 64, "column": 2}, "end": {"line": 71, "column": 9}}, "29": {"start": {"line": 66, "column": 18}, "end": {"line": 68, "column": 12}}, "30": {"start": {"line": 67, "column": 6}, "end": {"line": 67, "column": 20}}, "31": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 37}}, "32": {"start": {"line": 70, "column": 17}, "end": {"line": 70, "column": 36}}, "33": {"start": {"line": 73, "column": 36}, "end": {"line": 78, "column": 3}}, "34": {"start": {"line": 80, "column": 2}, "end": {"line": 84, "column": 4}}, "35": {"start": {"line": 88, "column": 18}, "end": {"line": 88, "column": 43}}, "36": {"start": {"line": 89, "column": 2}, "end": {"line": 91, "column": 3}}, "37": {"start": {"line": 90, "column": 4}, "end": {"line": 90, "column": 70}}, "38": {"start": {"line": 92, "column": 2}, "end": {"line": 92, "column": 17}}, "39": {"start": {"line": 97, "column": 21}, "end": {"line": 97, "column": 32}}, "40": {"start": {"line": 98, "column": 2}, "end": {"line": 98, "column": 38}}, "41": {"start": {"line": 103, "column": 21}, "end": {"line": 103, "column": 32}}, "42": {"start": {"line": 105, "column": 21}, "end": {"line": 107, "column": 3}}, "43": {"start": {"line": 106, "column": 4}, "end": {"line": 106, "column": 62}}, "44": {"start": {"line": 109, "column": 27}, "end": {"line": 111, "column": 3}}, "45": {"start": {"line": 110, "column": 4}, "end": {"line": 110, "column": 53}}, "46": {"start": {"line": 113, "column": 2}, "end": {"line": 117, "column": 4}}}, "fnMap": {"0": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decl": {"start": {"line": 19, "column": 16}, "end": {"line": 19, "column": 30}}, "loc": {"start": {"line": 19, "column": 66}, "end": {"line": 85, "column": 1}}, "line": 19}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 24, "column": 22}, "end": {"line": 24, "column": 23}}, "loc": {"start": {"line": 24, "column": 48}, "end": {"line": 58, "column": 3}}, "line": 24}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 36, "column": 21}, "end": {"line": 36, "column": 22}}, "loc": {"start": {"line": 36, "column": 27}, "end": {"line": 38, "column": 11}}, "line": 36}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 60, "column": 24}, "end": {"line": 60, "column": 25}}, "loc": {"start": {"line": 60, "column": 36}, "end": {"line": 62, "column": 3}}, "line": 60}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 64, "column": 12}, "end": {"line": 64, "column": 13}}, "loc": {"start": {"line": 64, "column": 18}, "end": {"line": 71, "column": 3}}, "line": 64}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 66, "column": 29}, "end": {"line": 66, "column": 30}}, "loc": {"start": {"line": 66, "column": 35}, "end": {"line": 68, "column": 5}}, "line": 66}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 70, "column": 11}, "end": {"line": 70, "column": 12}}, "loc": {"start": {"line": 70, "column": 17}, "end": {"line": 70, "column": 36}}, "line": 70}, "7": {"name": "useTenant", "decl": {"start": {"line": 87, "column": 16}, "end": {"line": 87, "column": 25}}, "loc": {"start": {"line": 87, "column": 28}, "end": {"line": 93, "column": 1}}, "line": 87}, "8": {"name": "useTenantSchema", "decl": {"start": {"line": 96, "column": 16}, "end": {"line": 96, "column": 31}}, "loc": {"start": {"line": 96, "column": 34}, "end": {"line": 99, "column": 1}}, "line": 96}, "9": {"name": "useTenantFeatures", "decl": {"start": {"line": 102, "column": 16}, "end": {"line": 102, "column": 33}}, "loc": {"start": {"line": 102, "column": 36}, "end": {"line": 118, "column": 1}}, "line": 102}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 105, "column": 21}, "end": {"line": 105, "column": 22}}, "loc": {"start": {"line": 105, "column": 55}, "end": {"line": 107, "column": 3}}, "line": 105}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 109, "column": 27}, "end": {"line": 109, "column": 28}}, "loc": {"start": {"line": 109, "column": 57}, "end": {"line": 111, "column": 3}}, "line": 109}}, "branchMap": {"0": {"loc": {"start": {"line": 24, "column": 29}, "end": {"line": 24, "column": 43}}, "type": "default-arg", "locations": [{"start": {"line": 24, "column": 42}, "end": {"line": 24, "column": 43}}], "line": 24}, "1": {"loc": {"start": {"line": 32, "column": 6}, "end": {"line": 42, "column": 7}}, "type": "if", "locations": [{"start": {"line": 32, "column": 6}, "end": {"line": 42, "column": 7}}, {"start": {}, "end": {}}], "line": 32}, "2": {"loc": {"start": {"line": 34, "column": 8}, "end": {"line": 40, "column": 9}}, "type": "if", "locations": [{"start": {"line": 34, "column": 8}, "end": {"line": 40, "column": 9}}, {"start": {}, "end": {}}], "line": 34}, "3": {"loc": {"start": {"line": 34, "column": 12}, "end": {"line": 34, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 34, "column": 12}, "end": {"line": 34, "column": 35}}, {"start": {"line": 34, "column": 39}, "end": {"line": 34, "column": 53}}], "line": 34}, "4": {"loc": {"start": {"line": 41, "column": 24}, "end": {"line": 41, "column": 74}}, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 24}, "end": {"line": 41, "column": 34}}, {"start": {"line": 41, "column": 38}, "end": {"line": 41, "column": 74}}], "line": 41}, "5": {"loc": {"start": {"line": 44, "column": 6}, "end": {"line": 49, "column": 7}}, "type": "if", "locations": [{"start": {"line": 44, "column": 6}, "end": {"line": 49, "column": 7}}, {"start": {"line": 47, "column": 13}, "end": {"line": 49, "column": 7}}], "line": 44}, "6": {"loc": {"start": {"line": 44, "column": 10}, "end": {"line": 44, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 44, "column": 10}, "end": {"line": 44, "column": 22}}, {"start": {"line": 44, "column": 26}, "end": {"line": 44, "column": 37}}], "line": 44}, "7": {"loc": {"start": {"line": 51, "column": 27}, "end": {"line": 51, "column": 88}}, "type": "cond-expr", "locations": [{"start": {"line": 51, "column": 50}, "end": {"line": 51, "column": 61}}, {"start": {"line": 51, "column": 64}, "end": {"line": 51, "column": 88}}], "line": 51}, "8": {"loc": {"start": {"line": 89, "column": 2}, "end": {"line": 91, "column": 3}}, "type": "if", "locations": [{"start": {"line": 89, "column": 2}, "end": {"line": 91, "column": 3}}, {"start": {}, "end": {}}], "line": 89}, "9": {"loc": {"start": {"line": 98, "column": 9}, "end": {"line": 98, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 98, "column": 9}, "end": {"line": 98, "column": 29}}, {"start": {"line": 98, "column": 33}, "end": {"line": 98, "column": 37}}], "line": 98}, "10": {"loc": {"start": {"line": 116, "column": 14}, "end": {"line": 116, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 116, "column": 14}, "end": {"line": 116, "column": 40}}, {"start": {"line": 116, "column": 44}, "end": {"line": 116, "column": 46}}], "line": 116}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\UserContext.tsx": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\UserContext.tsx", "statementMap": {"0": {"start": {"line": 40, "column": 20}, "end": {"line": 47, "column": 2}}, "1": {"start": {"line": 43, "column": 33}, "end": {"line": 43, "column": 38}}, "2": {"start": {"line": 44, "column": 37}, "end": {"line": 44, "column": 42}}, "3": {"start": {"line": 45, "column": 24}, "end": {"line": 45, "column": 30}}, "4": {"start": {"line": 46, "column": 17}, "end": {"line": 46, "column": 22}}, "5": {"start": {"line": 50, "column": 26}, "end": {"line": 50, "column": 53}}, "6": {"start": {"line": 51, "column": 36}, "end": {"line": 51, "column": 51}}, "7": {"start": {"line": 52, "column": 40}, "end": {"line": 52, "column": 49}}, "8": {"start": {"line": 53, "column": 21}, "end": {"line": 53, "column": 32}}, "9": {"start": {"line": 56, "column": 2}, "end": {"line": 75, "column": 41}}, "10": {"start": {"line": 57, "column": 4}, "end": {"line": 74, "column": 5}}, "11": {"start": {"line": 59, "column": 6}, "end": {"line": 71, "column": 8}}, "12": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 19}}, "13": {"start": {"line": 78, "column": 2}, "end": {"line": 110, "column": 16}}, "14": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 25}}, "15": {"start": {"line": 79, "column": 19}, "end": {"line": 79, "column": 25}}, "16": {"start": {"line": 81, "column": 32}, "end": {"line": 107, "column": 5}}, "17": {"start": {"line": 82, "column": 6}, "end": {"line": 106, "column": 7}}, "18": {"start": {"line": 83, "column": 8}, "end": {"line": 83, "column": 26}}, "19": {"start": {"line": 86, "column": 28}, "end": {"line": 86, "column": 73}}, "20": {"start": {"line": 87, "column": 8}, "end": {"line": 91, "column": 9}}, "21": {"start": {"line": 88, "column": 30}, "end": {"line": 88, "column": 53}}, "22": {"start": {"line": 89, "column": 10}, "end": {"line": 89, "column": 78}}, "23": {"start": {"line": 89, "column": 26}, "end": {"line": 89, "column": 77}}, "24": {"start": {"line": 90, "column": 10}, "end": {"line": 90, "column": 16}}, "25": {"start": {"line": 94, "column": 25}, "end": {"line": 94, "column": 73}}, "26": {"start": {"line": 95, "column": 8}, "end": {"line": 101, "column": 9}}, "27": {"start": {"line": 96, "column": 30}, "end": {"line": 96, "column": 51}}, "28": {"start": {"line": 97, "column": 10}, "end": {"line": 97, "column": 65}}, "29": {"start": {"line": 97, "column": 26}, "end": {"line": 97, "column": 64}}, "30": {"start": {"line": 100, "column": 10}, "end": {"line": 100, "column": 84}}, "31": {"start": {"line": 103, "column": 8}, "end": {"line": 103, "column": 63}}, "32": {"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 27}}, "33": {"start": {"line": 109, "column": 4}, "end": {"line": 109, "column": 25}}, "34": {"start": {"line": 113, "column": 28}, "end": {"line": 139, "column": 3}}, "35": {"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 31}}, "36": {"start": {"line": 114, "column": 19}, "end": {"line": 114, "column": 31}}, "37": {"start": {"line": 116, "column": 4}, "end": {"line": 138, "column": 5}}, "38": {"start": {"line": 117, "column": 6}, "end": {"line": 117, "column": 24}}, "39": {"start": {"line": 118, "column": 23}, "end": {"line": 124, "column": 8}}, "40": {"start": {"line": 126, "column": 6}, "end": {"line": 128, "column": 7}}, "41": {"start": {"line": 127, "column": 8}, "end": {"line": 127, "column": 51}}, "42": {"start": {"line": 130, "column": 26}, "end": {"line": 130, "column": 47}}, "43": {"start": {"line": 131, "column": 6}, "end": {"line": 131, "column": 64}}, "44": {"start": {"line": 131, "column": 22}, "end": {"line": 131, "column": 63}}, "45": {"start": {"line": 132, "column": 6}, "end": {"line": 132, "column": 17}}, "46": {"start": {"line": 134, "column": 6}, "end": {"line": 134, "column": 58}}, "47": {"start": {"line": 135, "column": 6}, "end": {"line": 135, "column": 18}}, "48": {"start": {"line": 137, "column": 6}, "end": {"line": 137, "column": 25}}, "49": {"start": {"line": 142, "column": 32}, "end": {"line": 181, "column": 3}}, "50": {"start": {"line": 143, "column": 4}, "end": {"line": 143, "column": 31}}, "51": {"start": {"line": 143, "column": 19}, "end": {"line": 143, "column": 31}}, "52": {"start": {"line": 145, "column": 4}, "end": {"line": 180, "column": 5}}, "53": {"start": {"line": 146, "column": 6}, "end": {"line": 146, "column": 24}}, "54": {"start": {"line": 149, "column": 33}, "end": {"line": 149, "column": 72}}, "55": {"start": {"line": 150, "column": 6}, "end": {"line": 150, "column": 81}}, "56": {"start": {"line": 150, "column": 22}, "end": {"line": 150, "column": 80}}, "57": {"start": {"line": 153, "column": 6}, "end": {"line": 153, "column": 87}}, "58": {"start": {"line": 156, "column": 23}, "end": {"line": 162, "column": 8}}, "59": {"start": {"line": 164, "column": 6}, "end": {"line": 166, "column": 7}}, "60": {"start": {"line": 165, "column": 8}, "end": {"line": 165, "column": 55}}, "61": {"start": {"line": 168, "column": 6}, "end": {"line": 168, "column": 17}}, "62": {"start": {"line": 170, "column": 6}, "end": {"line": 170, "column": 62}}, "63": {"start": {"line": 172, "column": 26}, "end": {"line": 172, "column": 71}}, "64": {"start": {"line": 173, "column": 6}, "end": {"line": 176, "column": 7}}, "65": {"start": {"line": 174, "column": 28}, "end": {"line": 174, "column": 51}}, "66": {"start": {"line": 175, "column": 8}, "end": {"line": 175, "column": 76}}, "67": {"start": {"line": 175, "column": 24}, "end": {"line": 175, "column": 75}}, "68": {"start": {"line": 177, "column": 6}, "end": {"line": 177, "column": 18}}, "69": {"start": {"line": 179, "column": 6}, "end": {"line": 179, "column": 25}}, "70": {"start": {"line": 184, "column": 25}, "end": {"line": 205, "column": 3}}, "71": {"start": {"line": 185, "column": 4}, "end": {"line": 185, "column": 28}}, "72": {"start": {"line": 185, "column": 15}, "end": {"line": 185, "column": 28}}, "73": {"start": {"line": 187, "column": 4}, "end": {"line": 189, "column": 5}}, "74": {"start": {"line": 188, "column": 6}, "end": {"line": 188, "column": 53}}, "75": {"start": {"line": 191, "column": 4}, "end": {"line": 193, "column": 5}}, "76": {"start": {"line": 192, "column": 6}, "end": {"line": 192, "column": 22}}, "77": {"start": {"line": 196, "column": 19}, "end": {"line": 198, "column": 5}}, "78": {"start": {"line": 197, "column": 6}, "end": {"line": 197, "column": 88}}, "79": {"start": {"line": 200, "column": 4}, "end": {"line": 202, "column": 5}}, "80": {"start": {"line": 201, "column": 6}, "end": {"line": 201, "column": 37}}, "81": {"start": {"line": 204, "column": 4}, "end": {"line": 204, "column": 44}}, "82": {"start": {"line": 208, "column": 18}, "end": {"line": 210, "column": 3}}, "83": {"start": {"line": 209, "column": 4}, "end": {"line": 209, "column": 47}}, "84": {"start": {"line": 212, "column": 2}, "end": {"line": 225, "column": 3}}, "85": {"start": {"line": 229, "column": 23}, "end": {"line": 229, "column": 52}}, "86": {"start": {"line": 229, "column": 29}, "end": {"line": 229, "column": 52}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 43, "column": 21}, "end": {"line": 43, "column": 22}}, "loc": {"start": {"line": 43, "column": 33}, "end": {"line": 43, "column": 38}}, "line": 43}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 44, "column": 25}, "end": {"line": 44, "column": 26}}, "loc": {"start": {"line": 44, "column": 37}, "end": {"line": 44, "column": 42}}, "line": 44}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 45, "column": 18}, "end": {"line": 45, "column": 19}}, "loc": {"start": {"line": 45, "column": 24}, "end": {"line": 45, "column": 30}}, "line": 45}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 46, "column": 11}, "end": {"line": 46, "column": 12}}, "loc": {"start": {"line": 46, "column": 17}, "end": {"line": 46, "column": 22}}, "line": 46}, "4": {"name": "UserProvider", "decl": {"start": {"line": 49, "column": 16}, "end": {"line": 49, "column": 28}}, "loc": {"start": {"line": 49, "column": 68}, "end": {"line": 226, "column": 1}}, "line": 49}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 56, "column": 12}, "end": {"line": 56, "column": 13}}, "loc": {"start": {"line": 56, "column": 18}, "end": {"line": 75, "column": 3}}, "line": 56}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 78, "column": 12}, "end": {"line": 78, "column": 13}}, "loc": {"start": {"line": 78, "column": 18}, "end": {"line": 110, "column": 3}}, "line": 78}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 81, "column": 32}, "end": {"line": 81, "column": 33}}, "loc": {"start": {"line": 81, "column": 44}, "end": {"line": 107, "column": 5}}, "line": 81}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 89, "column": 18}, "end": {"line": 89, "column": 19}}, "loc": {"start": {"line": 89, "column": 26}, "end": {"line": 89, "column": 77}}, "line": 89}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 97, "column": 18}, "end": {"line": 97, "column": 19}}, "loc": {"start": {"line": 97, "column": 26}, "end": {"line": 97, "column": 64}}, "line": 97}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 113, "column": 28}, "end": {"line": 113, "column": 29}}, "loc": {"start": {"line": 113, "column": 77}, "end": {"line": 139, "column": 3}}, "line": 113}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 131, "column": 14}, "end": {"line": 131, "column": 15}}, "loc": {"start": {"line": 131, "column": 22}, "end": {"line": 131, "column": 63}}, "line": 131}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 142, "column": 32}, "end": {"line": 142, "column": 33}}, "loc": {"start": {"line": 142, "column": 99}, "end": {"line": 181, "column": 3}}, "line": 142}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 150, "column": 14}, "end": {"line": 150, "column": 15}}, "loc": {"start": {"line": 150, "column": 22}, "end": {"line": 150, "column": 80}}, "line": 150}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 175, "column": 16}, "end": {"line": 175, "column": 17}}, "loc": {"start": {"line": 175, "column": 24}, "end": {"line": 175, "column": 75}}, "line": 175}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 184, "column": 25}, "end": {"line": 184, "column": 26}}, "loc": {"start": {"line": 184, "column": 39}, "end": {"line": 205, "column": 3}}, "line": 184}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 196, "column": 19}, "end": {"line": 196, "column": 20}}, "loc": {"start": {"line": 196, "column": 36}, "end": {"line": 198, "column": 5}}, "line": 196}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 208, "column": 18}, "end": {"line": 208, "column": 19}}, "loc": {"start": {"line": 208, "column": 45}, "end": {"line": 210, "column": 3}}, "line": 208}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 229, "column": 23}, "end": {"line": 229, "column": 24}}, "loc": {"start": {"line": 229, "column": 29}, "end": {"line": 229, "column": 52}}, "line": 229}}, "branchMap": {"0": {"loc": {"start": {"line": 57, "column": 4}, "end": {"line": 74, "column": 5}}, "type": "if", "locations": [{"start": {"line": 57, "column": 4}, "end": {"line": 74, "column": 5}}, {"start": {"line": 72, "column": 11}, "end": {"line": 74, "column": 5}}], "line": 57}, "1": {"loc": {"start": {"line": 57, "column": 8}, "end": {"line": 57, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 57, "column": 8}, "end": {"line": 57, "column": 23}}, {"start": {"line": 57, "column": 27}, "end": {"line": 57, "column": 35}}], "line": 57}, "2": {"loc": {"start": {"line": 61, "column": 18}, "end": {"line": 61, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 61, "column": 18}, "end": {"line": 61, "column": 35}}, {"start": {"line": 61, "column": 39}, "end": {"line": 61, "column": 49}}], "line": 61}, "3": {"loc": {"start": {"line": 62, "column": 21}, "end": {"line": 70, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 62, "column": 21}, "end": {"line": 62, "column": 38}}, {"start": {"line": 62, "column": 42}, "end": {"line": 70, "column": 9}}], "line": 62}, "4": {"loc": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 25}}, "type": "if", "locations": [{"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 25}}, {"start": {}, "end": {}}], "line": 79}, "5": {"loc": {"start": {"line": 87, "column": 8}, "end": {"line": 91, "column": 9}}, "type": "if", "locations": [{"start": {"line": 87, "column": 8}, "end": {"line": 91, "column": 9}}, {"start": {}, "end": {}}], "line": 87}, "6": {"loc": {"start": {"line": 89, "column": 26}, "end": {"line": 89, "column": 77}}, "type": "cond-expr", "locations": [{"start": {"line": 89, "column": 33}, "end": {"line": 89, "column": 70}}, {"start": {"line": 89, "column": 73}, "end": {"line": 89, "column": 77}}], "line": 89}, "7": {"loc": {"start": {"line": 95, "column": 8}, "end": {"line": 101, "column": 9}}, "type": "if", "locations": [{"start": {"line": 95, "column": 8}, "end": {"line": 101, "column": 9}}, {"start": {}, "end": {}}], "line": 95}, "8": {"loc": {"start": {"line": 97, "column": 26}, "end": {"line": 97, "column": 64}}, "type": "cond-expr", "locations": [{"start": {"line": 97, "column": 33}, "end": {"line": 97, "column": 57}}, {"start": {"line": 97, "column": 60}, "end": {"line": 97, "column": 64}}], "line": 97}, "9": {"loc": {"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 31}}, "type": "if", "locations": [{"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 31}}, {"start": {}, "end": {}}], "line": 114}, "10": {"loc": {"start": {"line": 126, "column": 6}, "end": {"line": 128, "column": 7}}, "type": "if", "locations": [{"start": {"line": 126, "column": 6}, "end": {"line": 128, "column": 7}}, {"start": {}, "end": {}}], "line": 126}, "11": {"loc": {"start": {"line": 131, "column": 22}, "end": {"line": 131, "column": 63}}, "type": "cond-expr", "locations": [{"start": {"line": 131, "column": 29}, "end": {"line": 131, "column": 56}}, {"start": {"line": 131, "column": 59}, "end": {"line": 131, "column": 63}}], "line": 131}, "12": {"loc": {"start": {"line": 143, "column": 4}, "end": {"line": 143, "column": 31}}, "type": "if", "locations": [{"start": {"line": 143, "column": 4}, "end": {"line": 143, "column": 31}}, {"start": {}, "end": {}}], "line": 143}, "13": {"loc": {"start": {"line": 150, "column": 22}, "end": {"line": 150, "column": 80}}, "type": "cond-expr", "locations": [{"start": {"line": 150, "column": 29}, "end": {"line": 150, "column": 73}}, {"start": {"line": 150, "column": 76}, "end": {"line": 150, "column": 80}}], "line": 150}, "14": {"loc": {"start": {"line": 164, "column": 6}, "end": {"line": 166, "column": 7}}, "type": "if", "locations": [{"start": {"line": 164, "column": 6}, "end": {"line": 166, "column": 7}}, {"start": {}, "end": {}}], "line": 164}, "15": {"loc": {"start": {"line": 173, "column": 6}, "end": {"line": 176, "column": 7}}, "type": "if", "locations": [{"start": {"line": 173, "column": 6}, "end": {"line": 176, "column": 7}}, {"start": {}, "end": {}}], "line": 173}, "16": {"loc": {"start": {"line": 175, "column": 24}, "end": {"line": 175, "column": 75}}, "type": "cond-expr", "locations": [{"start": {"line": 175, "column": 31}, "end": {"line": 175, "column": 68}}, {"start": {"line": 175, "column": 71}, "end": {"line": 175, "column": 75}}], "line": 175}, "17": {"loc": {"start": {"line": 185, "column": 4}, "end": {"line": 185, "column": 28}}, "type": "if", "locations": [{"start": {"line": 185, "column": 4}, "end": {"line": 185, "column": 28}}, {"start": {}, "end": {}}], "line": 185}, "18": {"loc": {"start": {"line": 187, "column": 4}, "end": {"line": 189, "column": 5}}, "type": "if", "locations": [{"start": {"line": 187, "column": 4}, "end": {"line": 189, "column": 5}}, {"start": {}, "end": {}}], "line": 187}, "19": {"loc": {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 23}}, {"start": {"line": 187, "column": 27}, "end": {"line": 187, "column": 43}}], "line": 187}, "20": {"loc": {"start": {"line": 191, "column": 4}, "end": {"line": 193, "column": 5}}, "type": "if", "locations": [{"start": {"line": 191, "column": 4}, "end": {"line": 193, "column": 5}}, {"start": {}, "end": {}}], "line": 191}, "21": {"loc": {"start": {"line": 200, "column": 4}, "end": {"line": 202, "column": 5}}, "type": "if", "locations": [{"start": {"line": 200, "column": 4}, "end": {"line": 202, "column": 5}}, {"start": {}, "end": {}}], "line": 200}, "22": {"loc": {"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 18}}, {"start": {"line": 200, "column": 22}, "end": {"line": 200, "column": 55}}], "line": 200}, "23": {"loc": {"start": {"line": 209, "column": 11}, "end": {"line": 209, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 209, "column": 11}, "end": {"line": 209, "column": 38}}, {"start": {"line": 209, "column": 42}, "end": {"line": 209, "column": 47}}], "line": 209}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\hooks\\useDashboardData.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\hooks\\useDashboardData.ts", "statementMap": {"0": {"start": {"line": 38, "column": 37}, "end": {"line": 43, "column": 1}}, "1": {"start": {"line": 45, "column": 35}, "end": {"line": 45, "column": 37}}, "2": {"start": {"line": 48, "column": 18}, "end": {"line": 48, "column": 31}}, "3": {"start": {"line": 49, "column": 19}, "end": {"line": 52, "column": 10}}, "4": {"start": {"line": 56, "column": 38}, "end": {"line": 56, "column": 45}}, "5": {"start": {"line": 58, "column": 21}, "end": {"line": 58, "column": 42}}, "6": {"start": {"line": 59, "column": 20}, "end": {"line": 59, "column": 65}}, "7": {"start": {"line": 59, "column": 37}, "end": {"line": 59, "column": 55}}, "8": {"start": {"line": 61, "column": 2}, "end": {"line": 74, "column": 3}}, "9": {"start": {"line": 62, "column": 21}, "end": {"line": 67, "column": 6}}, "10": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 27}}, "11": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 19}}, "12": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 27}}, "13": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 15}}, "14": {"start": {"line": 78, "column": 65}, "end": {"line": 78, "column": 76}}, "15": {"start": {"line": 80, "column": 26}, "end": {"line": 84, "column": 4}}, "16": {"start": {"line": 86, "column": 36}, "end": {"line": 86, "column": 51}}, "17": {"start": {"line": 87, "column": 28}, "end": {"line": 87, "column": 57}}, "18": {"start": {"line": 90, "column": 23}, "end": {"line": 90, "column": 35}}, "19": {"start": {"line": 91, "column": 29}, "end": {"line": 91, "column": 65}}, "20": {"start": {"line": 94, "column": 26}, "end": {"line": 94, "column": 50}}, "21": {"start": {"line": 97, "column": 20}, "end": {"line": 103, "column": 33}}, "22": {"start": {"line": 98, "column": 4}, "end": {"line": 98, "column": 37}}, "23": {"start": {"line": 98, "column": 26}, "end": {"line": 98, "column": 37}}, "24": {"start": {"line": 99, "column": 4}, "end": {"line": 102, "column": 5}}, "25": {"start": {"line": 106, "column": 21}, "end": {"line": 138, "column": 14}}, "26": {"start": {"line": 107, "column": 4}, "end": {"line": 107, "column": 56}}, "27": {"start": {"line": 107, "column": 17}, "end": {"line": 107, "column": 56}}, "28": {"start": {"line": 109, "column": 4}, "end": {"line": 109, "column": 39}}, "29": {"start": {"line": 111, "column": 21}, "end": {"line": 111, "column": 77}}, "30": {"start": {"line": 112, "column": 19}, "end": {"line": 112, "column": 41}}, "31": {"start": {"line": 113, "column": 4}, "end": {"line": 116, "column": 5}}, "32": {"start": {"line": 114, "column": 6}, "end": {"line": 114, "column": 44}}, "33": {"start": {"line": 115, "column": 6}, "end": {"line": 115, "column": 19}}, "34": {"start": {"line": 118, "column": 21}, "end": {"line": 118, "column": 76}}, "35": {"start": {"line": 120, "column": 4}, "end": {"line": 122, "column": 5}}, "36": {"start": {"line": 121, "column": 6}, "end": {"line": 121, "column": 89}}, "37": {"start": {"line": 124, "column": 19}, "end": {"line": 124, "column": 40}}, "38": {"start": {"line": 126, "column": 4}, "end": {"line": 137, "column": 5}}, "39": {"start": {"line": 127, "column": 6}, "end": {"line": 127, "column": 89}}, "40": {"start": {"line": 128, "column": 6}, "end": {"line": 128, "column": 44}}, "41": {"start": {"line": 129, "column": 6}, "end": {"line": 129, "column": 24}}, "42": {"start": {"line": 130, "column": 11}, "end": {"line": 137, "column": 5}}, "43": {"start": {"line": 131, "column": 6}, "end": {"line": 131, "column": 35}}, "44": {"start": {"line": 134, "column": 6}, "end": {"line": 134, "column": 84}}, "45": {"start": {"line": 135, "column": 6}, "end": {"line": 135, "column": 44}}, "46": {"start": {"line": 136, "column": 6}, "end": {"line": 136, "column": 19}}, "47": {"start": {"line": 141, "column": 24}, "end": {"line": 173, "column": 14}}, "48": {"start": {"line": 142, "column": 4}, "end": {"line": 142, "column": 56}}, "49": {"start": {"line": 142, "column": 17}, "end": {"line": 142, "column": 56}}, "50": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 42}}, "51": {"start": {"line": 146, "column": 21}, "end": {"line": 146, "column": 80}}, "52": {"start": {"line": 147, "column": 19}, "end": {"line": 147, "column": 41}}, "53": {"start": {"line": 148, "column": 4}, "end": {"line": 151, "column": 5}}, "54": {"start": {"line": 149, "column": 6}, "end": {"line": 149, "column": 47}}, "55": {"start": {"line": 150, "column": 6}, "end": {"line": 150, "column": 19}}, "56": {"start": {"line": 153, "column": 21}, "end": {"line": 153, "column": 79}}, "57": {"start": {"line": 155, "column": 4}, "end": {"line": 157, "column": 5}}, "58": {"start": {"line": 156, "column": 6}, "end": {"line": 156, "column": 92}}, "59": {"start": {"line": 159, "column": 19}, "end": {"line": 159, "column": 40}}, "60": {"start": {"line": 161, "column": 4}, "end": {"line": 172, "column": 5}}, "61": {"start": {"line": 162, "column": 6}, "end": {"line": 162, "column": 92}}, "62": {"start": {"line": 163, "column": 6}, "end": {"line": 163, "column": 47}}, "63": {"start": {"line": 164, "column": 6}, "end": {"line": 164, "column": 24}}, "64": {"start": {"line": 165, "column": 11}, "end": {"line": 172, "column": 5}}, "65": {"start": {"line": 166, "column": 6}, "end": {"line": 166, "column": 35}}, "66": {"start": {"line": 169, "column": 6}, "end": {"line": 169, "column": 87}}, "67": {"start": {"line": 170, "column": 6}, "end": {"line": 170, "column": 47}}, "68": {"start": {"line": 171, "column": 6}, "end": {"line": 171, "column": 19}}, "69": {"start": {"line": 176, "column": 29}, "end": {"line": 225, "column": 41}}, "70": {"start": {"line": 177, "column": 4}, "end": {"line": 177, "column": 48}}, "71": {"start": {"line": 177, "column": 42}, "end": {"line": 177, "column": 48}}, "72": {"start": {"line": 180, "column": 4}, "end": {"line": 182, "column": 5}}, "73": {"start": {"line": 181, "column": 6}, "end": {"line": 181, "column": 40}}, "74": {"start": {"line": 184, "column": 4}, "end": {"line": 184, "column": 54}}, "75": {"start": {"line": 185, "column": 19}, "end": {"line": 185, "column": 52}}, "76": {"start": {"line": 187, "column": 4}, "end": {"line": 187, "column": 22}}, "77": {"start": {"line": 188, "column": 4}, "end": {"line": 188, "column": 18}}, "78": {"start": {"line": 190, "column": 4}, "end": {"line": 224, "column": 5}}, "79": {"start": {"line": 191, "column": 40}, "end": {"line": 194, "column": 8}}, "80": {"start": {"line": 196, "column": 6}, "end": {"line": 196, "column": 39}}, "81": {"start": {"line": 196, "column": 33}, "end": {"line": 196, "column": 39}}, "82": {"start": {"line": 198, "column": 37}, "end": {"line": 202, "column": 7}}, "83": {"start": {"line": 204, "column": 6}, "end": {"line": 204, "column": 22}}, "84": {"start": {"line": 207, "column": 6}, "end": {"line": 209, "column": 7}}, "85": {"start": {"line": 208, "column": 8}, "end": {"line": 208, "column": 65}}, "86": {"start": {"line": 210, "column": 6}, "end": {"line": 212, "column": 7}}, "87": {"start": {"line": 211, "column": 8}, "end": {"line": 211, "column": 71}}, "88": {"start": {"line": 215, "column": 6}, "end": {"line": 215, "column": 39}}, "89": {"start": {"line": 215, "column": 33}, "end": {"line": 215, "column": 39}}, "90": {"start": {"line": 217, "column": 27}, "end": {"line": 217, "column": 96}}, "91": {"start": {"line": 218, "column": 6}, "end": {"line": 218, "column": 28}}, "92": {"start": {"line": 219, "column": 6}, "end": {"line": 219, "column": 55}}, "93": {"start": {"line": 221, "column": 6}, "end": {"line": 223, "column": 7}}, "94": {"start": {"line": 222, "column": 8}, "end": {"line": 222, "column": 27}}, "95": {"start": {"line": 228, "column": 23}, "end": {"line": 239, "column": 26}}, "96": {"start": {"line": 229, "column": 4}, "end": {"line": 229, "column": 23}}, "97": {"start": {"line": 229, "column": 17}, "end": {"line": 229, "column": 23}}, "98": {"start": {"line": 231, "column": 4}, "end": {"line": 238, "column": 5}}, "99": {"start": {"line": 232, "column": 20}, "end": {"line": 232, "column": 38}}, "100": {"start": {"line": 233, "column": 6}, "end": {"line": 235, "column": 7}}, "101": {"start": {"line": 234, "column": 8}, "end": {"line": 234, "column": 45}}, "102": {"start": {"line": 234, "column": 25}, "end": {"line": 234, "column": 43}}, "103": {"start": {"line": 237, "column": 6}, "end": {"line": 237, "column": 52}}, "104": {"start": {"line": 241, "column": 26}, "end": {"line": 256, "column": 29}}, "105": {"start": {"line": 242, "column": 4}, "end": {"line": 242, "column": 23}}, "106": {"start": {"line": 242, "column": 17}, "end": {"line": 242, "column": 23}}, "107": {"start": {"line": 244, "column": 4}, "end": {"line": 255, "column": 5}}, "108": {"start": {"line": 245, "column": 23}, "end": {"line": 245, "column": 44}}, "109": {"start": {"line": 246, "column": 6}, "end": {"line": 252, "column": 7}}, "110": {"start": {"line": 247, "column": 8}, "end": {"line": 251, "column": 11}}, "111": {"start": {"line": 247, "column": 25}, "end": {"line": 251, "column": 9}}, "112": {"start": {"line": 254, "column": 6}, "end": {"line": 254, "column": 55}}, "113": {"start": {"line": 259, "column": 2}, "end": {"line": 265, "column": 62}}, "114": {"start": {"line": 260, "column": 4}, "end": {"line": 264, "column": 5}}, "115": {"start": {"line": 261, "column": 6}, "end": {"line": 261, "column": 26}}, "116": {"start": {"line": 262, "column": 11}, "end": {"line": 264, "column": 5}}, "117": {"start": {"line": 263, "column": 6}, "end": {"line": 263, "column": 27}}, "118": {"start": {"line": 268, "column": 2}, "end": {"line": 275, "column": 8}}, "119": {"start": {"line": 269, "column": 4}, "end": {"line": 274, "column": 5}}, "120": {"start": {"line": 270, "column": 6}, "end": {"line": 270, "column": 34}}, "121": {"start": {"line": 271, "column": 6}, "end": {"line": 273, "column": 7}}, "122": {"start": {"line": 272, "column": 8}, "end": {"line": 272, "column": 42}}, "123": {"start": {"line": 277, "column": 2}, "end": {"line": 284, "column": 3}}}, "fnMap": {"0": {"name": "fetchWithTimeout", "decl": {"start": {"line": 55, "column": 15}, "end": {"line": 55, "column": 31}}, "loc": {"start": {"line": 55, "column": 92}, "end": {"line": 75, "column": 1}}, "line": 55}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 59, "column": 31}, "end": {"line": 59, "column": 32}}, "loc": {"start": {"line": 59, "column": 37}, "end": {"line": 59, "column": 55}}, "line": 59}, "2": {"name": "useDashboardData", "decl": {"start": {"line": 77, "column": 16}, "end": {"line": 77, "column": 32}}, "loc": {"start": {"line": 77, "column": 59}, "end": {"line": 285, "column": 1}}, "line": 77}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 97, "column": 28}, "end": {"line": 97, "column": 29}}, "loc": {"start": {"line": 97, "column": 34}, "end": {"line": 103, "column": 3}}, "line": 97}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 106, "column": 33}, "end": {"line": 106, "column": 34}}, "loc": {"start": {"line": 106, "column": 96}, "end": {"line": 138, "column": 3}}, "line": 106}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 141, "column": 36}, "end": {"line": 141, "column": 37}}, "loc": {"start": {"line": 141, "column": 94}, "end": {"line": 173, "column": 3}}, "line": 141}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 176, "column": 41}, "end": {"line": 176, "column": 42}}, "loc": {"start": {"line": 176, "column": 68}, "end": {"line": 225, "column": 3}}, "line": 176}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 228, "column": 35}, "end": {"line": 228, "column": 36}}, "loc": {"start": {"line": 228, "column": 62}, "end": {"line": 239, "column": 3}}, "line": 228}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 234, "column": 16}, "end": {"line": 234, "column": 17}}, "loc": {"start": {"line": 234, "column": 25}, "end": {"line": 234, "column": 43}}, "line": 234}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 241, "column": 38}, "end": {"line": 241, "column": 39}}, "loc": {"start": {"line": 241, "column": 65}, "end": {"line": 256, "column": 3}}, "line": 241}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 247, "column": 16}, "end": {"line": 247, "column": 17}}, "loc": {"start": {"line": 247, "column": 25}, "end": {"line": 251, "column": 9}}, "line": 247}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 259, "column": 12}, "end": {"line": 259, "column": 13}}, "loc": {"start": {"line": 259, "column": 18}, "end": {"line": 265, "column": 3}}, "line": 259}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 268, "column": 12}, "end": {"line": 268, "column": 13}}, "loc": {"start": {"line": 268, "column": 18}, "end": {"line": 275, "column": 3}}, "line": 268}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 269, "column": 11}, "end": {"line": 269, "column": 12}}, "loc": {"start": {"line": 269, "column": 17}, "end": {"line": 274, "column": 5}}, "line": 269}}, "branchMap": {"0": {"loc": {"start": {"line": 55, "column": 45}, "end": {"line": 55, "column": 71}}, "type": "default-arg", "locations": [{"start": {"line": 55, "column": 69}, "end": {"line": 55, "column": 71}}], "line": 55}, "1": {"loc": {"start": {"line": 56, "column": 18}, "end": {"line": 56, "column": 33}}, "type": "default-arg", "locations": [{"start": {"line": 56, "column": 28}, "end": {"line": 56, "column": 33}}], "line": 56}, "2": {"loc": {"start": {"line": 63, "column": 14}, "end": {"line": 63, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 63, "column": 14}, "end": {"line": 63, "column": 20}}, {"start": {"line": 63, "column": 24}, "end": {"line": 63, "column": 41}}], "line": 63}, "3": {"loc": {"start": {"line": 98, "column": 4}, "end": {"line": 98, "column": 37}}, "type": "if", "locations": [{"start": {"line": 98, "column": 4}, "end": {"line": 98, "column": 37}}, {"start": {}, "end": {}}], "line": 98}, "4": {"loc": {"start": {"line": 106, "column": 40}, "end": {"line": 106, "column": 66}}, "type": "default-arg", "locations": [{"start": {"line": 106, "column": 64}, "end": {"line": 106, "column": 66}}], "line": 106}, "5": {"loc": {"start": {"line": 107, "column": 4}, "end": {"line": 107, "column": 56}}, "type": "if", "locations": [{"start": {"line": 107, "column": 4}, "end": {"line": 107, "column": 56}}, {"start": {}, "end": {}}], "line": 107}, "6": {"loc": {"start": {"line": 113, "column": 4}, "end": {"line": 116, "column": 5}}, "type": "if", "locations": [{"start": {"line": 113, "column": 4}, "end": {"line": 116, "column": 5}}, {"start": {}, "end": {}}], "line": 113}, "7": {"loc": {"start": {"line": 120, "column": 4}, "end": {"line": 122, "column": 5}}, "type": "if", "locations": [{"start": {"line": 120, "column": 4}, "end": {"line": 122, "column": 5}}, {"start": {}, "end": {}}], "line": 120}, "8": {"loc": {"start": {"line": 126, "column": 4}, "end": {"line": 137, "column": 5}}, "type": "if", "locations": [{"start": {"line": 126, "column": 4}, "end": {"line": 137, "column": 5}}, {"start": {"line": 130, "column": 11}, "end": {"line": 137, "column": 5}}], "line": 126}, "9": {"loc": {"start": {"line": 130, "column": 11}, "end": {"line": 137, "column": 5}}, "type": "if", "locations": [{"start": {"line": 130, "column": 11}, "end": {"line": 137, "column": 5}}, {"start": {"line": 132, "column": 11}, "end": {"line": 137, "column": 5}}], "line": 130}, "10": {"loc": {"start": {"line": 141, "column": 43}, "end": {"line": 141, "column": 69}}, "type": "default-arg", "locations": [{"start": {"line": 141, "column": 67}, "end": {"line": 141, "column": 69}}], "line": 141}, "11": {"loc": {"start": {"line": 142, "column": 4}, "end": {"line": 142, "column": 56}}, "type": "if", "locations": [{"start": {"line": 142, "column": 4}, "end": {"line": 142, "column": 56}}, {"start": {}, "end": {}}], "line": 142}, "12": {"loc": {"start": {"line": 148, "column": 4}, "end": {"line": 151, "column": 5}}, "type": "if", "locations": [{"start": {"line": 148, "column": 4}, "end": {"line": 151, "column": 5}}, {"start": {}, "end": {}}], "line": 148}, "13": {"loc": {"start": {"line": 155, "column": 4}, "end": {"line": 157, "column": 5}}, "type": "if", "locations": [{"start": {"line": 155, "column": 4}, "end": {"line": 157, "column": 5}}, {"start": {}, "end": {}}], "line": 155}, "14": {"loc": {"start": {"line": 161, "column": 4}, "end": {"line": 172, "column": 5}}, "type": "if", "locations": [{"start": {"line": 161, "column": 4}, "end": {"line": 172, "column": 5}}, {"start": {"line": 165, "column": 11}, "end": {"line": 172, "column": 5}}], "line": 161}, "15": {"loc": {"start": {"line": 165, "column": 11}, "end": {"line": 172, "column": 5}}, "type": "if", "locations": [{"start": {"line": 165, "column": 11}, "end": {"line": 172, "column": 5}}, {"start": {"line": 167, "column": 11}, "end": {"line": 172, "column": 5}}], "line": 165}, "16": {"loc": {"start": {"line": 177, "column": 4}, "end": {"line": 177, "column": 48}}, "type": "if", "locations": [{"start": {"line": 177, "column": 4}, "end": {"line": 177, "column": 48}}, {"start": {}, "end": {}}], "line": 177}, "17": {"loc": {"start": {"line": 177, "column": 8}, "end": {"line": 177, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 177, "column": 8}, "end": {"line": 177, "column": 15}}, {"start": {"line": 177, "column": 19}, "end": {"line": 177, "column": 40}}], "line": 177}, "18": {"loc": {"start": {"line": 180, "column": 4}, "end": {"line": 182, "column": 5}}, "type": "if", "locations": [{"start": {"line": 180, "column": 4}, "end": {"line": 182, "column": 5}}, {"start": {}, "end": {}}], "line": 180}, "19": {"loc": {"start": {"line": 196, "column": 6}, "end": {"line": 196, "column": 39}}, "type": "if", "locations": [{"start": {"line": 196, "column": 6}, "end": {"line": 196, "column": 39}}, {"start": {}, "end": {}}], "line": 196}, "20": {"loc": {"start": {"line": 199, "column": 15}, "end": {"line": 199, "column": 80}}, "type": "cond-expr", "locations": [{"start": {"line": 199, "column": 50}, "end": {"line": 199, "column": 65}}, {"start": {"line": 199, "column": 68}, "end": {"line": 199, "column": 80}}], "line": 199}, "21": {"loc": {"start": {"line": 200, "column": 24}, "end": {"line": 200, "column": 98}}, "type": "cond-expr", "locations": [{"start": {"line": 200, "column": 62}, "end": {"line": 200, "column": 80}}, {"start": {"line": 200, "column": 83}, "end": {"line": 200, "column": 98}}], "line": 200}, "22": {"loc": {"start": {"line": 201, "column": 26}, "end": {"line": 201, "column": 100}}, "type": "cond-expr", "locations": [{"start": {"line": 201, "column": 64}, "end": {"line": 201, "column": 82}}, {"start": {"line": 201, "column": 85}, "end": {"line": 201, "column": 100}}], "line": 201}, "23": {"loc": {"start": {"line": 207, "column": 6}, "end": {"line": 209, "column": 7}}, "type": "if", "locations": [{"start": {"line": 207, "column": 6}, "end": {"line": 209, "column": 7}}, {"start": {}, "end": {}}], "line": 207}, "24": {"loc": {"start": {"line": 210, "column": 6}, "end": {"line": 212, "column": 7}}, "type": "if", "locations": [{"start": {"line": 210, "column": 6}, "end": {"line": 212, "column": 7}}, {"start": {}, "end": {}}], "line": 210}, "25": {"loc": {"start": {"line": 215, "column": 6}, "end": {"line": 215, "column": 39}}, "type": "if", "locations": [{"start": {"line": 215, "column": 6}, "end": {"line": 215, "column": 39}}, {"start": {}, "end": {}}], "line": 215}, "26": {"loc": {"start": {"line": 217, "column": 27}, "end": {"line": 217, "column": 96}}, "type": "cond-expr", "locations": [{"start": {"line": 217, "column": 50}, "end": {"line": 217, "column": 61}}, {"start": {"line": 217, "column": 64}, "end": {"line": 217, "column": 96}}], "line": 217}, "27": {"loc": {"start": {"line": 221, "column": 6}, "end": {"line": 223, "column": 7}}, "type": "if", "locations": [{"start": {"line": 221, "column": 6}, "end": {"line": 223, "column": 7}}, {"start": {}, "end": {}}], "line": 221}, "28": {"loc": {"start": {"line": 229, "column": 4}, "end": {"line": 229, "column": 23}}, "type": "if", "locations": [{"start": {"line": 229, "column": 4}, "end": {"line": 229, "column": 23}}, {"start": {}, "end": {}}], "line": 229}, "29": {"loc": {"start": {"line": 233, "column": 6}, "end": {"line": 235, "column": 7}}, "type": "if", "locations": [{"start": {"line": 233, "column": 6}, "end": {"line": 235, "column": 7}}, {"start": {}, "end": {}}], "line": 233}, "30": {"loc": {"start": {"line": 242, "column": 4}, "end": {"line": 242, "column": 23}}, "type": "if", "locations": [{"start": {"line": 242, "column": 4}, "end": {"line": 242, "column": 23}}, {"start": {}, "end": {}}], "line": 242}, "31": {"loc": {"start": {"line": 246, "column": 6}, "end": {"line": 252, "column": 7}}, "type": "if", "locations": [{"start": {"line": 246, "column": 6}, "end": {"line": 252, "column": 7}}, {"start": {}, "end": {}}], "line": 246}, "32": {"loc": {"start": {"line": 260, "column": 4}, "end": {"line": 264, "column": 5}}, "type": "if", "locations": [{"start": {"line": 260, "column": 4}, "end": {"line": 264, "column": 5}}, {"start": {"line": 262, "column": 11}, "end": {"line": 264, "column": 5}}], "line": 260}, "33": {"loc": {"start": {"line": 260, "column": 8}, "end": {"line": 260, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 260, "column": 8}, "end": {"line": 260, "column": 14}}, {"start": {"line": 260, "column": 18}, "end": {"line": 260, "column": 32}}, {"start": {"line": 260, "column": 36}, "end": {"line": 260, "column": 48}}], "line": 260}, "34": {"loc": {"start": {"line": 262, "column": 11}, "end": {"line": 264, "column": 5}}, "type": "if", "locations": [{"start": {"line": 262, "column": 11}, "end": {"line": 264, "column": 5}}, {"start": {}, "end": {}}], "line": 262}, "35": {"loc": {"start": {"line": 271, "column": 6}, "end": {"line": 273, "column": 7}}, "type": "if", "locations": [{"start": {"line": 271, "column": 6}, "end": {"line": 273, "column": 7}}, {"start": {}, "end": {}}], "line": 271}, "36": {"loc": {"start": {"line": 279, "column": 15}, "end": {"line": 279, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 279, "column": 15}, "end": {"line": 279, "column": 24}}, {"start": {"line": 279, "column": 28}, "end": {"line": 279, "column": 41}}], "line": 279}, "37": {"loc": {"start": {"line": 280, "column": 11}, "end": {"line": 280, "column": 31}}, "type": "binary-expr", "locations": [{"start": {"line": 280, "column": 11}, "end": {"line": 280, "column": 16}}, {"start": {"line": 280, "column": 20}, "end": {"line": 280, "column": 31}}], "line": 280}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0], "30": [0, 0], "31": [0, 0], "32": [0, 0], "33": [0, 0, 0], "34": [0, 0], "35": [0, 0], "36": [0, 0], "37": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\actions.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\actions.ts", "statementMap": {"0": {"start": {"line": 9, "column": 24}, "end": {"line": 25, "column": 1}}, "1": {"start": {"line": 10, "column": 22}, "end": {"line": 10, "column": 31}}, "2": {"start": {"line": 11, "column": 22}, "end": {"line": 11, "column": 58}}, "3": {"start": {"line": 12, "column": 20}, "end": {"line": 12, "column": 56}}, "4": {"start": {"line": 14, "column": 2}, "end": {"line": 22, "column": 3}}, "5": {"start": {"line": 16, "column": 4}, "end": {"line": 20, "column": 7}}, "6": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 52}}, "7": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 14}}, "8": {"start": {"line": 29, "column": 16}, "end": {"line": 29, "column": 54}}, "9": {"start": {"line": 32, "column": 2}, "end": {"line": 38, "column": 5}}, "10": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": 15}}, "11": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 33}}, "12": {"start": {"line": 49, "column": 18}, "end": {"line": 49, "column": 39}}, "13": {"start": {"line": 50, "column": 2}, "end": {"line": 52, "column": 3}}, "14": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 35}}, "15": {"start": {"line": 55, "column": 2}, "end": {"line": 57, "column": 3}}, "16": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": 47}}, "17": {"start": {"line": 60, "column": 15}, "end": {"line": 60, "column": 43}}, "18": {"start": {"line": 61, "column": 2}, "end": {"line": 63, "column": 3}}, "19": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": 35}}, "20": {"start": {"line": 66, "column": 2}, "end": {"line": 72, "column": 3}}, "21": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 28}}, "22": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 49}}, "23": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 47}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 24}, "end": {"line": 9, "column": 25}}, "loc": {"start": {"line": 9, "column": 54}, "end": {"line": 25, "column": 1}}, "line": 9}, "1": {"name": "generateCSRFToken", "decl": {"start": {"line": 28, "column": 16}, "end": {"line": 28, "column": 33}}, "loc": {"start": {"line": 28, "column": 36}, "end": {"line": 41, "column": 1}}, "line": 28}, "2": {"name": "updateUserProfile", "decl": {"start": {"line": 44, "column": 22}, "end": {"line": 44, "column": 39}}, "loc": {"start": {"line": 44, "column": 60}, "end": {"line": 73, "column": 1}}, "line": 44}}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 2}, "end": {"line": 22, "column": 3}}, "type": "if", "locations": [{"start": {"line": 14, "column": 2}, "end": {"line": 22, "column": 3}}, {"start": {}, "end": {}}], "line": 14}, "1": {"loc": {"start": {"line": 14, "column": 6}, "end": {"line": 14, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 14, "column": 6}, "end": {"line": 14, "column": 18}}, {"start": {"line": 14, "column": 22}, "end": {"line": 14, "column": 32}}, {"start": {"line": 14, "column": 36}, "end": {"line": 14, "column": 61}}], "line": 14}, "2": {"loc": {"start": {"line": 50, "column": 2}, "end": {"line": 52, "column": 3}}, "type": "if", "locations": [{"start": {"line": 50, "column": 2}, "end": {"line": 52, "column": 3}}, {"start": {}, "end": {}}], "line": 50}, "3": {"loc": {"start": {"line": 55, "column": 2}, "end": {"line": 57, "column": 3}}, "type": "if", "locations": [{"start": {"line": 55, "column": 2}, "end": {"line": 57, "column": 3}}, {"start": {}, "end": {}}], "line": 55}, "4": {"loc": {"start": {"line": 61, "column": 2}, "end": {"line": 63, "column": 3}}, "type": "if", "locations": [{"start": {"line": 61, "column": 2}, "end": {"line": 63, "column": 3}}, {"start": {}, "end": {}}], "line": 61}, "5": {"loc": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 11}}, {"start": {"line": 61, "column": 15}, "end": {"line": 61, "column": 30}}], "line": 61}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\amplify-config.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\amplify-config.ts", "statementMap": {"0": {"start": {"line": 7, "column": 2}, "end": {"line": 10, "column": 3}}, "1": {"start": {"line": 8, "column": 4}, "end": {"line": 8, "column": 59}}, "2": {"start": {"line": 9, "column": 4}, "end": {"line": 9, "column": 11}}, "3": {"start": {"line": 13, "column": 24}, "end": {"line": 13, "column": 36}}, "4": {"start": {"line": 14, "column": 17}, "end": {"line": 21, "column": 3}}, "5": {"start": {"line": 23, "column": 2}, "end": {"line": 30, "column": 5}}, "6": {"start": {"line": 32, "column": 2}, "end": {"line": 56, "column": 3}}, "7": {"start": {"line": 33, "column": 4}, "end": {"line": 51, "column": 7}}, "8": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 92}}, "9": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 55}}, "10": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 16}}, "11": {"start": {"line": 62, "column": 2}, "end": {"line": 64, "column": 3}}}, "fnMap": {"0": {"name": "configureAmplify", "decl": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 32}}, "loc": {"start": {"line": 5, "column": 35}, "end": {"line": 57, "column": 1}}, "line": 5}, "1": {"name": "getAmplifySSR", "decl": {"start": {"line": 60, "column": 16}, "end": {"line": 60, "column": 29}}, "loc": {"start": {"line": 60, "column": 49}, "end": {"line": 65, "column": 1}}, "line": 60}}, "branchMap": {"0": {"loc": {"start": {"line": 7, "column": 2}, "end": {"line": 10, "column": 3}}, "type": "if", "locations": [{"start": {"line": 7, "column": 2}, "end": {"line": 10, "column": 3}}, {"start": {}, "end": {}}], "line": 7}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\api-response.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\api-response.ts", "statementMap": {"0": {"start": {"line": 56, "column": 35}, "end": {"line": 61, "column": 3}}, "1": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 49}}, "2": {"start": {"line": 75, "column": 32}, "end": {"line": 80, "column": 3}}, "3": {"start": {"line": 83, "column": 2}, "end": {"line": 85, "column": 3}}, "4": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 40}}, "5": {"start": {"line": 87, "column": 2}, "end": {"line": 87, "column": 49}}, "6": {"start": {"line": 94, "column": 23}, "end": {"line": 94, "column": 98}}, "7": {"start": {"line": 94, "column": 47}, "end": {"line": 94, "column": 86}}, "8": {"start": {"line": 96, "column": 2}, "end": {"line": 101, "column": 4}}, "9": {"start": {"line": 108, "column": 2}, "end": {"line": 108, "column": 42}}, "10": {"start": {"line": 111, "column": 18}, "end": {"line": 113, "column": 33}}, "11": {"start": {"line": 115, "column": 2}, "end": {"line": 120, "column": 4}}, "12": {"start": {"line": 127, "column": 2}, "end": {"line": 131, "column": 4}}, "13": {"start": {"line": 138, "column": 2}, "end": {"line": 142, "column": 4}}, "14": {"start": {"line": 149, "column": 2}, "end": {"line": 153, "column": 4}}, "15": {"start": {"line": 160, "column": 19}, "end": {"line": 164, "column": 3}}, "16": {"start": {"line": 167, "column": 2}, "end": {"line": 167, "column": 44}}, "17": {"start": {"line": 168, "column": 2}, "end": {"line": 168, "column": 51}}, "18": {"start": {"line": 169, "column": 2}, "end": {"line": 169, "column": 53}}, "19": {"start": {"line": 171, "column": 2}, "end": {"line": 171, "column": 18}}, "20": {"start": {"line": 178, "column": 2}, "end": {"line": 178, "column": 37}}, "21": {"start": {"line": 181, "column": 2}, "end": {"line": 183, "column": 3}}, "22": {"start": {"line": 182, "column": 4}, "end": {"line": 182, "column": 40}}, "23": {"start": {"line": 186, "column": 2}, "end": {"line": 188, "column": 3}}, "24": {"start": {"line": 187, "column": 4}, "end": {"line": 187, "column": 38}}, "25": {"start": {"line": 191, "column": 18}, "end": {"line": 193, "column": 29}}, "26": {"start": {"line": 195, "column": 2}, "end": {"line": 200, "column": 4}}, "27": {"start": {"line": 207, "column": 2}, "end": {"line": 207, "column": 60}}, "28": {"start": {"line": 208, "column": 2}, "end": {"line": 208, "column": 50}}, "29": {"start": {"line": 209, "column": 2}, "end": {"line": 209, "column": 60}}, "30": {"start": {"line": 210, "column": 2}, "end": {"line": 210, "column": 77}}, "31": {"start": {"line": 212, "column": 2}, "end": {"line": 212, "column": 18}}, "32": {"start": {"line": 221, "column": 2}, "end": {"line": 229, "column": 4}}, "33": {"start": {"line": 222, "column": 4}, "end": {"line": 228, "column": 5}}, "34": {"start": {"line": 223, "column": 23}, "end": {"line": 223, "column": 45}}, "35": {"start": {"line": 224, "column": 6}, "end": {"line": 224, "column": 42}}, "36": {"start": {"line": 226, "column": 28}, "end": {"line": 226, "column": 49}}, "37": {"start": {"line": 227, "column": 6}, "end": {"line": 227, "column": 47}}}, "fnMap": {"0": {"name": "createSuccessResponse", "decl": {"start": {"line": 51, "column": 16}, "end": {"line": 51, "column": 37}}, "loc": {"start": {"line": 55, "column": 32}, "end": {"line": 64, "column": 1}}, "line": 55}, "1": {"name": "createErrorResponse", "decl": {"start": {"line": 69, "column": 16}, "end": {"line": 69, "column": 35}}, "loc": {"start": {"line": 74, "column": 29}, "end": {"line": 88, "column": 1}}, "line": 74}, "2": {"name": "handleValidationError", "decl": {"start": {"line": 93, "column": 16}, "end": {"line": 93, "column": 37}}, "loc": {"start": {"line": 93, "column": 82}, "end": {"line": 102, "column": 1}}, "line": 93}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 94, "column": 40}, "end": {"line": 94, "column": 41}}, "loc": {"start": {"line": 94, "column": 47}, "end": {"line": 94, "column": 86}}, "line": 94}, "4": {"name": "handleDatabaseError", "decl": {"start": {"line": 107, "column": 16}, "end": {"line": 107, "column": 35}}, "loc": {"start": {"line": 107, "column": 75}, "end": {"line": 121, "column": 1}}, "line": 107}, "5": {"name": "createUnauthorizedResponse", "decl": {"start": {"line": 126, "column": 16}, "end": {"line": 126, "column": 42}}, "loc": {"start": {"line": 126, "column": 96}, "end": {"line": 132, "column": 1}}, "line": 126}, "6": {"name": "createForbiddenResponse", "decl": {"start": {"line": 137, "column": 16}, "end": {"line": 137, "column": 39}}, "loc": {"start": {"line": 137, "column": 90}, "end": {"line": 143, "column": 1}}, "line": 137}, "7": {"name": "createNotFoundResponse", "decl": {"start": {"line": 148, "column": 16}, "end": {"line": 148, "column": 38}}, "loc": {"start": {"line": 148, "column": 89}, "end": {"line": 154, "column": 1}}, "line": 148}, "8": {"name": "createRateLimitResponse", "decl": {"start": {"line": 159, "column": 16}, "end": {"line": 159, "column": 39}}, "loc": {"start": {"line": 159, "column": 69}, "end": {"line": 172, "column": 1}}, "line": 159}, "9": {"name": "handleApiError", "decl": {"start": {"line": 177, "column": 16}, "end": {"line": 177, "column": 30}}, "loc": {"start": {"line": 177, "column": 70}, "end": {"line": 201, "column": 1}}, "line": 177}, "10": {"name": "addSecurityHeaders", "decl": {"start": {"line": 206, "column": 16}, "end": {"line": 206, "column": 34}}, "loc": {"start": {"line": 206, "column": 73}, "end": {"line": 213, "column": 1}}, "line": 206}, "11": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decl": {"start": {"line": 218, "column": 16}, "end": {"line": 218, "column": 33}}, "loc": {"start": {"line": 220, "column": 2}, "end": {"line": 230, "column": 1}}, "line": 220}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 221, "column": 9}, "end": {"line": 221, "column": 10}}, "loc": {"start": {"line": 221, "column": 70}, "end": {"line": 229, "column": 3}}, "line": 221}}, "branchMap": {"0": {"loc": {"start": {"line": 54, "column": 2}, "end": {"line": 54, "column": 36}}, "type": "default-arg", "locations": [{"start": {"line": 54, "column": 23}, "end": {"line": 54, "column": 36}}], "line": 54}, "1": {"loc": {"start": {"line": 83, "column": 2}, "end": {"line": 85, "column": 3}}, "type": "if", "locations": [{"start": {"line": 83, "column": 2}, "end": {"line": 85, "column": 3}}, {"start": {}, "end": {}}], "line": 83}, "2": {"loc": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 44}}, {"start": {"line": 83, "column": 48}, "end": {"line": 83, "column": 55}}], "line": 83}, "3": {"loc": {"start": {"line": 111, "column": 18}, "end": {"line": 113, "column": 33}}, "type": "cond-expr", "locations": [{"start": {"line": 112, "column": 6}, "end": {"line": 112, "column": 40}}, {"start": {"line": 113, "column": 6}, "end": {"line": 113, "column": 33}}], "line": 111}, "4": {"loc": {"start": {"line": 119, "column": 4}, "end": {"line": 119, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 119, "column": 45}, "end": {"line": 119, "column": 50}}, {"start": {"line": 119, "column": 53}, "end": {"line": 119, "column": 62}}], "line": 119}, "5": {"loc": {"start": {"line": 126, "column": 43}, "end": {"line": 126, "column": 67}}, "type": "default-arg", "locations": [{"start": {"line": 126, "column": 53}, "end": {"line": 126, "column": 67}}], "line": 126}, "6": {"loc": {"start": {"line": 137, "column": 40}, "end": {"line": 137, "column": 61}}, "type": "default-arg", "locations": [{"start": {"line": 137, "column": 50}, "end": {"line": 137, "column": 61}}], "line": 137}, "7": {"loc": {"start": {"line": 148, "column": 39}, "end": {"line": 148, "column": 60}}, "type": "default-arg", "locations": [{"start": {"line": 148, "column": 50}, "end": {"line": 148, "column": 60}}], "line": 148}, "8": {"loc": {"start": {"line": 181, "column": 2}, "end": {"line": 183, "column": 3}}, "type": "if", "locations": [{"start": {"line": 181, "column": 2}, "end": {"line": 183, "column": 3}}, {"start": {}, "end": {}}], "line": 181}, "9": {"loc": {"start": {"line": 186, "column": 2}, "end": {"line": 188, "column": 3}}, "type": "if", "locations": [{"start": {"line": 186, "column": 2}, "end": {"line": 188, "column": 3}}, {"start": {}, "end": {}}], "line": 186}, "10": {"loc": {"start": {"line": 186, "column": 6}, "end": {"line": 186, "column": 80}}, "type": "binary-expr", "locations": [{"start": {"line": 186, "column": 6}, "end": {"line": 186, "column": 16}}, {"start": {"line": 186, "column": 21}, "end": {"line": 186, "column": 48}}, {"start": {"line": 186, "column": 52}, "end": {"line": 186, "column": 79}}], "line": 186}, "11": {"loc": {"start": {"line": 191, "column": 18}, "end": {"line": 193, "column": 29}}, "type": "cond-expr", "locations": [{"start": {"line": 192, "column": 6}, "end": {"line": 192, "column": 46}}, {"start": {"line": 193, "column": 6}, "end": {"line": 193, "column": 29}}], "line": 191}, "12": {"loc": {"start": {"line": 192, "column": 6}, "end": {"line": 192, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 192, "column": 6}, "end": {"line": 192, "column": 19}}, {"start": {"line": 192, "column": 23}, "end": {"line": 192, "column": 46}}], "line": 192}, "13": {"loc": {"start": {"line": 199, "column": 4}, "end": {"line": 199, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 199, "column": 45}, "end": {"line": 199, "column": 50}}, {"start": {"line": 199, "column": 53}, "end": {"line": 199, "column": 62}}], "line": 199}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0], "6": [0], "7": [0], "8": [0, 0], "9": [0, 0], "10": [0, 0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\auth-middleware.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\auth-middleware.ts", "statementMap": {"0": {"start": {"line": 30, "column": 2}, "end": {"line": 50, "column": 3}}, "1": {"start": {"line": 31, "column": 20}, "end": {"line": 31, "column": 41}}, "2": {"start": {"line": 33, "column": 4}, "end": {"line": 38, "column": 5}}, "3": {"start": {"line": 34, "column": 6}, "end": {"line": 37, "column": 8}}, "4": {"start": {"line": 40, "column": 4}, "end": {"line": 43, "column": 6}}, "5": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 64}}, "6": {"start": {"line": 46, "column": 4}, "end": {"line": 49, "column": 6}}, "7": {"start": {"line": 57, "column": 21}, "end": {"line": 57, "column": 40}}, "8": {"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}, "9": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 22}}, "10": {"start": {"line": 63, "column": 16}, "end": {"line": 63, "column": 78}}, "11": {"start": {"line": 64, "column": 20}, "end": {"line": 64, "column": 45}}, "12": {"start": {"line": 66, "column": 26}, "end": {"line": 66, "column": 70}}, "13": {"start": {"line": 66, "column": 45}, "end": {"line": 66, "column": 69}}, "14": {"start": {"line": 68, "column": 2}, "end": {"line": 73, "column": 3}}, "15": {"start": {"line": 69, "column": 4}, "end": {"line": 72, "column": 6}}, "16": {"start": {"line": 75, "column": 2}, "end": {"line": 75, "column": 20}}, "17": {"start": {"line": 82, "column": 2}, "end": {"line": 82, "column": 47}}, "18": {"start": {"line": 89, "column": 2}, "end": {"line": 89, "column": 62}}, "19": {"start": {"line": 89, "column": 36}, "end": {"line": 89, "column": 60}}, "20": {"start": {"line": 96, "column": 2}, "end": {"line": 96, "column": 63}}, "21": {"start": {"line": 96, "column": 37}, "end": {"line": 96, "column": 61}}, "22": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 24}}, "23": {"start": {"line": 110, "column": 2}, "end": {"line": 110, "column": 23}}, "24": {"start": {"line": 117, "column": 2}, "end": {"line": 117, "column": 44}}, "25": {"start": {"line": 126, "column": 2}, "end": {"line": 134, "column": 4}}, "26": {"start": {"line": 127, "column": 23}, "end": {"line": 127, "column": 42}}, "27": {"start": {"line": 129, "column": 4}, "end": {"line": 131, "column": 5}}, "28": {"start": {"line": 130, "column": 6}, "end": {"line": 130, "column": 34}}, "29": {"start": {"line": 133, "column": 4}, "end": {"line": 133, "column": 49}}, "30": {"start": {"line": 144, "column": 2}, "end": {"line": 152, "column": 4}}, "31": {"start": {"line": 145, "column": 23}, "end": {"line": 145, "column": 55}}, "32": {"start": {"line": 147, "column": 4}, "end": {"line": 149, "column": 5}}, "33": {"start": {"line": 148, "column": 6}, "end": {"line": 148, "column": 34}}, "34": {"start": {"line": 151, "column": 4}, "end": {"line": 151, "column": 49}}, "35": {"start": {"line": 161, "column": 2}, "end": {"line": 161, "column": 53}}, "36": {"start": {"line": 167, "column": 21}, "end": {"line": 167, "column": 76}}, "37": {"start": {"line": 174, "column": 14}, "end": {"line": 174, "column": 24}}, "38": {"start": {"line": 175, "column": 22}, "end": {"line": 175, "column": 36}}, "39": {"start": {"line": 178, "column": 2}, "end": {"line": 182, "column": 3}}, "40": {"start": {"line": 179, "column": 4}, "end": {"line": 181, "column": 5}}, "41": {"start": {"line": 180, "column": 6}, "end": {"line": 180, "column": 31}}, "42": {"start": {"line": 184, "column": 18}, "end": {"line": 184, "column": 46}}, "43": {"start": {"line": 186, "column": 2}, "end": {"line": 190, "column": 3}}, "44": {"start": {"line": 188, "column": 4}, "end": {"line": 188, "column": 74}}, "45": {"start": {"line": 189, "column": 4}, "end": {"line": 189, "column": 57}}, "46": {"start": {"line": 192, "column": 2}, "end": {"line": 194, "column": 3}}, "47": {"start": {"line": 193, "column": 4}, "end": {"line": 193, "column": 44}}, "48": {"start": {"line": 196, "column": 2}, "end": {"line": 196, "column": 18}}, "49": {"start": {"line": 197, "column": 2}, "end": {"line": 197, "column": 67}}, "50": {"start": {"line": 205, "column": 2}, "end": {"line": 207, "column": 3}}, "51": {"start": {"line": 206, "column": 4}, "end": {"line": 206, "column": 36}}, "52": {"start": {"line": 210, "column": 20}, "end": {"line": 210, "column": 58}}, "53": {"start": {"line": 211, "column": 17}, "end": {"line": 211, "column": 49}}, "54": {"start": {"line": 212, "column": 13}, "end": {"line": 212, "column": 60}}, "55": {"start": {"line": 214, "column": 2}, "end": {"line": 214, "column": 20}}}, "fnMap": {"0": {"name": "requireAuth", "decl": {"start": {"line": 29, "column": 22}, "end": {"line": 29, "column": 33}}, "loc": {"start": {"line": 29, "column": 57}, "end": {"line": 51, "column": 1}}, "line": 29}, "1": {"name": "requireRole", "decl": {"start": {"line": 56, "column": 22}, "end": {"line": 56, "column": 33}}, "loc": {"start": {"line": 56, "column": 89}, "end": {"line": 76, "column": 1}}, "line": 56}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 66, "column": 37}, "end": {"line": 66, "column": 38}}, "loc": {"start": {"line": 66, "column": 45}, "end": {"line": 66, "column": 69}}, "line": 66}, "3": {"name": "requireAdmin", "decl": {"start": {"line": 81, "column": 22}, "end": {"line": 81, "column": 34}}, "loc": {"start": {"line": 81, "column": 58}, "end": {"line": 83, "column": 1}}, "line": 81}, "4": {"name": "hasAnyRole", "decl": {"start": {"line": 88, "column": 16}, "end": {"line": 88, "column": 26}}, "loc": {"start": {"line": 88, "column": 82}, "end": {"line": 90, "column": 1}}, "line": 88}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 89, "column": 28}, "end": {"line": 89, "column": 29}}, "loc": {"start": {"line": 89, "column": 36}, "end": {"line": 89, "column": 60}}, "line": 89}, "6": {"name": "hasAllRoles", "decl": {"start": {"line": 95, "column": 16}, "end": {"line": 95, "column": 27}}, "loc": {"start": {"line": 95, "column": 83}, "end": {"line": 97, "column": 1}}, "line": 95}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 96, "column": 29}, "end": {"line": 96, "column": 30}}, "loc": {"start": {"line": 96, "column": 37}, "end": {"line": 96, "column": 61}}, "line": 96}, "8": {"name": "getUserId", "decl": {"start": {"line": 102, "column": 16}, "end": {"line": 102, "column": 25}}, "loc": {"start": {"line": 102, "column": 56}, "end": {"line": 104, "column": 1}}, "line": 102}, "9": {"name": "getUserEmail", "decl": {"start": {"line": 109, "column": 16}, "end": {"line": 109, "column": 28}}, "loc": {"start": {"line": 109, "column": 59}, "end": {"line": 111, "column": 1}}, "line": 109}, "10": {"name": "isAuthenticated", "decl": {"start": {"line": 116, "column": 16}, "end": {"line": 116, "column": 31}}, "loc": {"start": {"line": 116, "column": 85}, "end": {"line": 118, "column": 1}}, "line": 116}, "11": {"name": "<PERSON><PERSON><PERSON>", "decl": {"start": {"line": 123, "column": 16}, "end": {"line": 123, "column": 24}}, "loc": {"start": {"line": 125, "column": 2}, "end": {"line": 135, "column": 1}}, "line": 125}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 126, "column": 9}, "end": {"line": 126, "column": 10}}, "loc": {"start": {"line": 126, "column": 71}, "end": {"line": 134, "column": 3}}, "line": 126}, "13": {"name": "with<PERSON><PERSON>", "decl": {"start": {"line": 140, "column": 16}, "end": {"line": 140, "column": 24}}, "loc": {"start": {"line": 143, "column": 2}, "end": {"line": 153, "column": 1}}, "line": 143}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 144, "column": 9}, "end": {"line": 144, "column": 10}}, "loc": {"start": {"line": 144, "column": 71}, "end": {"line": 152, "column": 3}}, "line": 144}, "15": {"name": "with<PERSON><PERSON><PERSON>", "decl": {"start": {"line": 158, "column": 16}, "end": {"line": 158, "column": 25}}, "loc": {"start": {"line": 160, "column": 2}, "end": {"line": 162, "column": 1}}, "line": 160}, "16": {"name": "checkRateLimit", "decl": {"start": {"line": 169, "column": 16}, "end": {"line": 169, "column": 30}}, "loc": {"start": {"line": 173, "column": 43}, "end": {"line": 198, "column": 1}}, "line": 173}, "17": {"name": "getRateLimitIdentifier", "decl": {"start": {"line": 203, "column": 16}, "end": {"line": 203, "column": 38}}, "loc": {"start": {"line": 203, "column": 92}, "end": {"line": 215, "column": 1}}, "line": 203}}, "branchMap": {"0": {"loc": {"start": {"line": 33, "column": 4}, "end": {"line": 38, "column": 5}}, "type": "if", "locations": [{"start": {"line": 33, "column": 4}, "end": {"line": 38, "column": 5}}, {"start": {}, "end": {}}], "line": 33}, "1": {"loc": {"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}, "type": "if", "locations": [{"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}, {"start": {}, "end": {}}], "line": 59}, "2": {"loc": {"start": {"line": 63, "column": 16}, "end": {"line": 63, "column": 78}}, "type": "cond-expr", "locations": [{"start": {"line": 63, "column": 47}, "end": {"line": 63, "column": 60}}, {"start": {"line": 63, "column": 63}, "end": {"line": 63, "column": 78}}], "line": 63}, "3": {"loc": {"start": {"line": 68, "column": 2}, "end": {"line": 73, "column": 3}}, "type": "if", "locations": [{"start": {"line": 68, "column": 2}, "end": {"line": 73, "column": 3}}, {"start": {}, "end": {}}], "line": 68}, "4": {"loc": {"start": {"line": 117, "column": 9}, "end": {"line": 117, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 117, "column": 9}, "end": {"line": 117, "column": 25}}, {"start": {"line": 117, "column": 29}, "end": {"line": 117, "column": 43}}], "line": 117}, "5": {"loc": {"start": {"line": 129, "column": 4}, "end": {"line": 131, "column": 5}}, "type": "if", "locations": [{"start": {"line": 129, "column": 4}, "end": {"line": 131, "column": 5}}, {"start": {}, "end": {}}], "line": 129}, "6": {"loc": {"start": {"line": 147, "column": 4}, "end": {"line": 149, "column": 5}}, "type": "if", "locations": [{"start": {"line": 147, "column": 4}, "end": {"line": 149, "column": 5}}, {"start": {}, "end": {}}], "line": 147}, "7": {"loc": {"start": {"line": 171, "column": 2}, "end": {"line": 171, "column": 27}}, "type": "default-arg", "locations": [{"start": {"line": 171, "column": 24}, "end": {"line": 171, "column": 27}}], "line": 171}, "8": {"loc": {"start": {"line": 172, "column": 2}, "end": {"line": 172, "column": 26}}, "type": "default-arg", "locations": [{"start": {"line": 172, "column": 21}, "end": {"line": 172, "column": 26}}], "line": 172}, "9": {"loc": {"start": {"line": 179, "column": 4}, "end": {"line": 181, "column": 5}}, "type": "if", "locations": [{"start": {"line": 179, "column": 4}, "end": {"line": 181, "column": 5}}, {"start": {}, "end": {}}], "line": 179}, "10": {"loc": {"start": {"line": 186, "column": 2}, "end": {"line": 190, "column": 3}}, "type": "if", "locations": [{"start": {"line": 186, "column": 2}, "end": {"line": 190, "column": 3}}, {"start": {}, "end": {}}], "line": 186}, "11": {"loc": {"start": {"line": 186, "column": 6}, "end": {"line": 186, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 186, "column": 6}, "end": {"line": 186, "column": 14}}, {"start": {"line": 186, "column": 18}, "end": {"line": 186, "column": 49}}], "line": 186}, "12": {"loc": {"start": {"line": 192, "column": 2}, "end": {"line": 194, "column": 3}}, "type": "if", "locations": [{"start": {"line": 192, "column": 2}, "end": {"line": 194, "column": 3}}, {"start": {}, "end": {}}], "line": 192}, "13": {"loc": {"start": {"line": 205, "column": 2}, "end": {"line": 207, "column": 3}}, "type": "if", "locations": [{"start": {"line": 205, "column": 2}, "end": {"line": 207, "column": 3}}, {"start": {}, "end": {}}], "line": 205}, "14": {"loc": {"start": {"line": 212, "column": 13}, "end": {"line": 212, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 212, "column": 13}, "end": {"line": 212, "column": 37}}, {"start": {"line": 212, "column": 41}, "end": {"line": 212, "column": 47}}, {"start": {"line": 212, "column": 51}, "end": {"line": 212, "column": 60}}], "line": 212}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0], "8": [0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\auth.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\auth.ts", "statementMap": {"0": {"start": {"line": 7, "column": 27}, "end": {"line": 12, "column": 1}}, "1": {"start": {"line": 8, "column": 46}, "end": {"line": 8, "column": 62}}, "2": {"start": {"line": 9, "column": 29}, "end": {"line": 9, "column": 46}}, "3": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 199}}, "4": {"start": {"line": 15, "column": 29}, "end": {"line": 18, "column": 1}}, "5": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 86}}, "6": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 46}}, "7": {"start": {"line": 21, "column": 31}, "end": {"line": 32, "column": 1}}, "8": {"start": {"line": 22, "column": 2}, "end": {"line": 31, "column": 3}}, "9": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 23}}, "10": {"start": {"line": 26, "column": 20}, "end": {"line": 26, "column": 44}}, "11": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 38}}, "12": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 46}}, "13": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": 17}}, "14": {"start": {"line": 35, "column": 23}, "end": {"line": 57, "column": 1}}, "15": {"start": {"line": 36, "column": 2}, "end": {"line": 56, "column": 3}}, "16": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 23}}, "17": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 43}}, "18": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 47}}, "19": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 66}}, "20": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 16}}, "21": {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 47}}, "22": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 47}}, "23": {"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 66}}, "24": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 16}}, "25": {"start": {"line": 62, "column": 26}, "end": {"line": 89, "column": 1}}, "26": {"start": {"line": 63, "column": 2}, "end": {"line": 88, "column": 3}}, "27": {"start": {"line": 64, "column": 23}, "end": {"line": 66, "column": 21}}, "28": {"start": {"line": 65, "column": 17}, "end": {"line": 65, "column": 48}}, "29": {"start": {"line": 68, "column": 4}, "end": {"line": 70, "column": 5}}, "30": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": 19}}, "31": {"start": {"line": 73, "column": 20}, "end": {"line": 73, "column": 56}}, "32": {"start": {"line": 74, "column": 4}, "end": {"line": 76, "column": 5}}, "33": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 19}}, "34": {"start": {"line": 78, "column": 4}, "end": {"line": 84, "column": 6}}, "35": {"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": 53}}, "36": {"start": {"line": 87, "column": 4}, "end": {"line": 87, "column": 17}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 28}}, "loc": {"start": {"line": 7, "column": 33}, "end": {"line": 12, "column": 1}}, "line": 7}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 15, "column": 29}, "end": {"line": 15, "column": 30}}, "loc": {"start": {"line": 15, "column": 50}, "end": {"line": 18, "column": 1}}, "line": 15}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 21, "column": 31}, "end": {"line": 21, "column": 32}}, "loc": {"start": {"line": 21, "column": 43}, "end": {"line": 32, "column": 1}}, "line": 21}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 35, "column": 23}, "end": {"line": 35, "column": 24}}, "loc": {"start": {"line": 35, "column": 35}, "end": {"line": 57, "column": 1}}, "line": 35}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 62, "column": 26}, "end": {"line": 62, "column": 27}}, "loc": {"start": {"line": 62, "column": 54}, "end": {"line": 89, "column": 1}}, "line": 62}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 65, "column": 12}, "end": {"line": 65, "column": 13}}, "loc": {"start": {"line": 65, "column": 17}, "end": {"line": 65, "column": 48}}, "line": 65}}, "branchMap": {"0": {"loc": {"start": {"line": 68, "column": 4}, "end": {"line": 70, "column": 5}}, "type": "if", "locations": [{"start": {"line": 68, "column": 4}, "end": {"line": 70, "column": 5}}, {"start": {}, "end": {}}], "line": 68}, "1": {"loc": {"start": {"line": 74, "column": 4}, "end": {"line": 76, "column": 5}}, "type": "if", "locations": [{"start": {"line": 74, "column": 4}, "end": {"line": 76, "column": 5}}, {"start": {}, "end": {}}], "line": 74}, "2": {"loc": {"start": {"line": 82, "column": 12}, "end": {"line": 82, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 82, "column": 12}, "end": {"line": 82, "column": 24}}, {"start": {"line": 82, "column": 28}, "end": {"line": 82, "column": 46}}], "line": 82}, "3": {"loc": {"start": {"line": 83, "column": 13}, "end": {"line": 83, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 83, "column": 13}, "end": {"line": 83, "column": 38}}, {"start": {"line": 83, "column": 42}, "end": {"line": 83, "column": 44}}], "line": 83}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\aws-config.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\aws-config.ts", "statementMap": {"0": {"start": {"line": 6, "column": 2}, "end": {"line": 12, "column": 3}}, "1": {"start": {"line": 7, "column": 4}, "end": {"line": 11, "column": 6}}, "2": {"start": {"line": 14, "column": 20}, "end": {"line": 14, "column": 81}}, "3": {"start": {"line": 16, "column": 18}, "end": {"line": 20, "column": 4}}, "4": {"start": {"line": 22, "column": 19}, "end": {"line": 22, "column": 48}}, "5": {"start": {"line": 24, "column": 2}, "end": {"line": 29, "column": 4}}, "6": {"start": {"line": 34, "column": 22}, "end": {"line": 34, "column": 44}}, "7": {"start": {"line": 36, "column": 20}, "end": {"line": 43, "column": 4}}, "8": {"start": {"line": 45, "column": 18}, "end": {"line": 48, "column": 4}}, "9": {"start": {"line": 50, "column": 19}, "end": {"line": 50, "column": 48}}, "10": {"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": 35}}}, "fnMap": {"0": {"name": "getCredentials", "decl": {"start": {"line": 5, "column": 15}, "end": {"line": 5, "column": 29}}, "loc": {"start": {"line": 5, "column": 32}, "end": {"line": 30, "column": 1}}, "line": 5}, "1": {"name": "getParameter", "decl": {"start": {"line": 33, "column": 22}, "end": {"line": 33, "column": 34}}, "loc": {"start": {"line": 33, "column": 54}, "end": {"line": 52, "column": 1}}, "line": 33}}, "branchMap": {"0": {"loc": {"start": {"line": 6, "column": 2}, "end": {"line": 12, "column": 3}}, "type": "if", "locations": [{"start": {"line": 6, "column": 2}, "end": {"line": 12, "column": 3}}, {"start": {}, "end": {}}], "line": 6}, "1": {"loc": {"start": {"line": 37, "column": 12}, "end": {"line": 37, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 37, "column": 12}, "end": {"line": 37, "column": 46}}, {"start": {"line": 37, "column": 50}, "end": {"line": 37, "column": 64}}], "line": 37}, "2": {"loc": {"start": {"line": 39, "column": 19}, "end": {"line": 39, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 39, "column": 19}, "end": {"line": 39, "column": 42}}, {"start": {"line": 39, "column": 46}, "end": {"line": 39, "column": 48}}], "line": 39}, "3": {"loc": {"start": {"line": 40, "column": 23}, "end": {"line": 40, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 40, "column": 23}, "end": {"line": 40, "column": 50}}, {"start": {"line": 40, "column": 54}, "end": {"line": 40, "column": 56}}], "line": 40}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\cache.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\cache.ts", "statementMap": {"0": {"start": {"line": 39, "column": 18}, "end": {"line": 39, "column": 50}}, "1": {"start": {"line": 41, "column": 34}, "end": {"line": 47, "column": 3}}, "2": {"start": {"line": 55, "column": 4}, "end": {"line": 61, "column": 5}}, "3": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": 44}}, "4": {"start": {"line": 65, "column": 4}, "end": {"line": 65, "column": 23}}, "5": {"start": {"line": 70, "column": 18}, "end": {"line": 70, "column": 37}}, "6": {"start": {"line": 72, "column": 4}, "end": {"line": 75, "column": 5}}, "7": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 32}}, "8": {"start": {"line": 74, "column": 6}, "end": {"line": 74, "column": 17}}, "9": {"start": {"line": 78, "column": 4}, "end": {"line": 82, "column": 5}}, "10": {"start": {"line": 79, "column": 6}, "end": {"line": 79, "column": 28}}, "11": {"start": {"line": 80, "column": 6}, "end": {"line": 80, "column": 32}}, "12": {"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": 17}}, "13": {"start": {"line": 85, "column": 4}, "end": {"line": 85, "column": 23}}, "14": {"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": 35}}, "15": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 29}}, "16": {"start": {"line": 89, "column": 4}, "end": {"line": 89, "column": 21}}, "17": {"start": {"line": 95, "column": 4}, "end": {"line": 97, "column": 5}}, "18": {"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": 18}}, "19": {"start": {"line": 99, "column": 33}, "end": {"line": 106, "column": 5}}, "20": {"start": {"line": 108, "column": 4}, "end": {"line": 108, "column": 30}}, "21": {"start": {"line": 109, "column": 4}, "end": {"line": 109, "column": 29}}, "22": {"start": {"line": 114, "column": 18}, "end": {"line": 114, "column": 37}}, "23": {"start": {"line": 115, "column": 4}, "end": {"line": 115, "column": 28}}, "24": {"start": {"line": 115, "column": 16}, "end": {"line": 115, "column": 28}}, "25": {"start": {"line": 117, "column": 4}, "end": {"line": 120, "column": 5}}, "26": {"start": {"line": 118, "column": 6}, "end": {"line": 118, "column": 28}}, "27": {"start": {"line": 119, "column": 6}, "end": {"line": 119, "column": 18}}, "28": {"start": {"line": 122, "column": 4}, "end": {"line": 122, "column": 15}}, "29": {"start": {"line": 127, "column": 4}, "end": {"line": 127, "column": 33}}, "30": {"start": {"line": 132, "column": 18}, "end": {"line": 132, "column": 19}}, "31": {"start": {"line": 133, "column": 4}, "end": {"line": 138, "column": 5}}, "32": {"start": {"line": 134, "column": 6}, "end": {"line": 137, "column": 7}}, "33": {"start": {"line": 134, "column": 33}, "end": {"line": 134, "column": 51}}, "34": {"start": {"line": 135, "column": 8}, "end": {"line": 135, "column": 30}}, "35": {"start": {"line": 136, "column": 8}, "end": {"line": 136, "column": 17}}, "36": {"start": {"line": 139, "column": 4}, "end": {"line": 139, "column": 18}}, "37": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 22}}, "38": {"start": {"line": 145, "column": 4}, "end": {"line": 145, "column": 23}}, "39": {"start": {"line": 150, "column": 26}, "end": {"line": 150, "column": 65}}, "40": {"start": {"line": 151, "column": 4}, "end": {"line": 155, "column": 5}}, "41": {"start": {"line": 160, "column": 4}, "end": {"line": 160, "column": 40}}, "42": {"start": {"line": 165, "column": 4}, "end": {"line": 165, "column": 26}}, "43": {"start": {"line": 170, "column": 4}, "end": {"line": 170, "column": 51}}, "44": {"start": {"line": 175, "column": 4}, "end": {"line": 175, "column": 37}}, "45": {"start": {"line": 175, "column": 31}, "end": {"line": 175, "column": 37}}, "46": {"start": {"line": 177, "column": 36}, "end": {"line": 177, "column": 40}}, "47": {"start": {"line": 179, "column": 4}, "end": {"line": 192, "column": 5}}, "48": {"start": {"line": 181, "column": 8}, "end": {"line": 181, "column": 38}}, "49": {"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": 13}}, "50": {"start": {"line": 184, "column": 8}, "end": {"line": 184, "column": 38}}, "51": {"start": {"line": 185, "column": 8}, "end": {"line": 185, "column": 13}}, "52": {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 42}}, "53": {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 13}}, "54": {"start": {"line": 190, "column": 8}, "end": {"line": 190, "column": 39}}, "55": {"start": {"line": 191, "column": 8}, "end": {"line": 191, "column": 13}}, "56": {"start": {"line": 194, "column": 4}, "end": {"line": 197, "column": 5}}, "57": {"start": {"line": 195, "column": 6}, "end": {"line": 195, "column": 35}}, "58": {"start": {"line": 196, "column": 6}, "end": {"line": 196, "column": 30}}, "59": {"start": {"line": 202, "column": 35}, "end": {"line": 202, "column": 39}}, "60": {"start": {"line": 203, "column": 21}, "end": {"line": 203, "column": 31}}, "61": {"start": {"line": 205, "column": 4}, "end": {"line": 210, "column": 5}}, "62": {"start": {"line": 206, "column": 6}, "end": {"line": 209, "column": 7}}, "63": {"start": {"line": 207, "column": 8}, "end": {"line": 207, "column": 39}}, "64": {"start": {"line": 208, "column": 8}, "end": {"line": 208, "column": 23}}, "65": {"start": {"line": 212, "column": 4}, "end": {"line": 212, "column": 20}}, "66": {"start": {"line": 217, "column": 38}, "end": {"line": 217, "column": 42}}, "67": {"start": {"line": 218, "column": 21}, "end": {"line": 218, "column": 29}}, "68": {"start": {"line": 220, "column": 4}, "end": {"line": 225, "column": 5}}, "69": {"start": {"line": 221, "column": 6}, "end": {"line": 224, "column": 7}}, "70": {"start": {"line": 222, "column": 8}, "end": {"line": 222, "column": 38}}, "71": {"start": {"line": 223, "column": 8}, "end": {"line": 223, "column": 26}}, "72": {"start": {"line": 227, "column": 4}, "end": {"line": 227, "column": 23}}, "73": {"start": {"line": 232, "column": 4}, "end": {"line": 236, "column": 5}}, "74": {"start": {"line": 233, "column": 6}, "end": {"line": 235, "column": 7}}, "75": {"start": {"line": 234, "column": 8}, "end": {"line": 234, "column": 18}}, "76": {"start": {"line": 237, "column": 4}, "end": {"line": 237, "column": 15}}, "77": {"start": {"line": 242, "column": 35}, "end": {"line": 242, "column": 39}}, "78": {"start": {"line": 243, "column": 21}, "end": {"line": 243, "column": 31}}, "79": {"start": {"line": 245, "column": 4}, "end": {"line": 250, "column": 5}}, "80": {"start": {"line": 246, "column": 6}, "end": {"line": 249, "column": 7}}, "81": {"start": {"line": 247, "column": 8}, "end": {"line": 247, "column": 36}}, "82": {"start": {"line": 248, "column": 8}, "end": {"line": 248, "column": 23}}, "83": {"start": {"line": 252, "column": 4}, "end": {"line": 252, "column": 20}}, "84": {"start": {"line": 257, "column": 4}, "end": {"line": 257, "column": 42}}, "85": {"start": {"line": 257, "column": 36}, "end": {"line": 257, "column": 42}}, "86": {"start": {"line": 259, "column": 4}, "end": {"line": 269, "column": 5}}, "87": {"start": {"line": 261, "column": 8}, "end": {"line": 261, "column": 27}}, "88": {"start": {"line": 262, "column": 8}, "end": {"line": 262, "column": 13}}, "89": {"start": {"line": 264, "column": 8}, "end": {"line": 264, "column": 29}}, "90": {"start": {"line": 265, "column": 8}, "end": {"line": 265, "column": 13}}, "91": {"start": {"line": 268, "column": 8}, "end": {"line": 268, "column": 13}}, "92": {"start": {"line": 274, "column": 4}, "end": {"line": 280, "column": 5}}, "93": {"start": {"line": 285, "column": 4}, "end": {"line": 287, "column": 35}}, "94": {"start": {"line": 286, "column": 6}, "end": {"line": 286, "column": 20}}, "95": {"start": {"line": 292, "column": 16}, "end": {"line": 292, "column": 26}}, "96": {"start": {"line": 293, "column": 35}, "end": {"line": 293, "column": 37}}, "97": {"start": {"line": 295, "column": 4}, "end": {"line": 299, "column": 5}}, "98": {"start": {"line": 296, "column": 6}, "end": {"line": 298, "column": 7}}, "99": {"start": {"line": 297, "column": 8}, "end": {"line": 297, "column": 30}}, "100": {"start": {"line": 301, "column": 4}, "end": {"line": 301, "column": 55}}, "101": {"start": {"line": 301, "column": 32}, "end": {"line": 301, "column": 54}}, "102": {"start": {"line": 306, "column": 4}, "end": {"line": 308, "column": 5}}, "103": {"start": {"line": 307, "column": 6}, "end": {"line": 307, "column": 38}}, "104": {"start": {"line": 309, "column": 4}, "end": {"line": 309, "column": 16}}, "105": {"start": {"line": 314, "column": 24}, "end": {"line": 318, "column": 9}}, "106": {"start": {"line": 320, "column": 30}, "end": {"line": 324, "column": 9}}, "107": {"start": {"line": 326, "column": 29}, "end": {"line": 330, "column": 9}}, "108": {"start": {"line": 333, "column": 26}, "end": {"line": 380, "column": 1}}, "109": {"start": {"line": 336, "column": 4}, "end": {"line": 336, "column": 42}}, "110": {"start": {"line": 341, "column": 4}, "end": {"line": 350, "column": 5}}, "111": {"start": {"line": 342, "column": 6}, "end": {"line": 347, "column": 8}}, "112": {"start": {"line": 343, "column": 8}, "end": {"line": 345, "column": 9}}, "113": {"start": {"line": 344, "column": 10}, "end": {"line": 344, "column": 63}}, "114": {"start": {"line": 346, "column": 8}, "end": {"line": 346, "column": 20}}, "115": {"start": {"line": 349, "column": 6}, "end": {"line": 349, "column": 24}}, "116": {"start": {"line": 355, "column": 4}, "end": {"line": 364, "column": 5}}, "117": {"start": {"line": 356, "column": 6}, "end": {"line": 361, "column": 8}}, "118": {"start": {"line": 357, "column": 8}, "end": {"line": 359, "column": 9}}, "119": {"start": {"line": 358, "column": 10}, "end": {"line": 358, "column": 38}}, "120": {"start": {"line": 360, "column": 8}, "end": {"line": 360, "column": 20}}, "121": {"start": {"line": 363, "column": 6}, "end": {"line": 363, "column": 17}}, "122": {"start": {"line": 368, "column": 25}, "end": {"line": 372, "column": 3}}, "123": {"start": {"line": 376, "column": 4}, "end": {"line": 376, "column": 20}}, "124": {"start": {"line": 377, "column": 4}, "end": {"line": 377, "column": 26}}, "125": {"start": {"line": 378, "column": 4}, "end": {"line": 378, "column": 25}}, "126": {"start": {"line": 386, "column": 2}, "end": {"line": 394, "column": 3}}, "127": {"start": {"line": 387, "column": 26}, "end": {"line": 387, "column": 40}}, "128": {"start": {"line": 389, "column": 6}, "end": {"line": 389, "column": 37}}, "129": {"start": {"line": 390, "column": 26}, "end": {"line": 390, "column": 40}}, "130": {"start": {"line": 391, "column": 29}, "end": {"line": 391, "column": 46}}, "131": {"start": {"line": 392, "column": 17}, "end": {"line": 392, "column": 30}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": 3}}, "loc": {"start": {"line": 54, "column": 4}, "end": {"line": 66, "column": 3}}, "line": 54}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 3}}, "loc": {"start": {"line": 69, "column": 29}, "end": {"line": 90, "column": 3}}, "line": 69}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 93, "column": 2}, "end": {"line": 93, "column": 3}}, "loc": {"start": {"line": 93, "column": 69}, "end": {"line": 110, "column": 3}}, "line": 93}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 3}}, "loc": {"start": {"line": 113, "column": 28}, "end": {"line": 123, "column": 3}}, "line": 113}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 126, "column": 2}, "end": {"line": 126, "column": 3}}, "loc": {"start": {"line": 126, "column": 31}, "end": {"line": 128, "column": 3}}, "line": 126}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 131, "column": 2}, "end": {"line": 131, "column": 3}}, "loc": {"start": {"line": 131, "column": 38}, "end": {"line": 140, "column": 3}}, "line": 131}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 134, "column": 26}, "end": {"line": 134, "column": 27}}, "loc": {"start": {"line": 134, "column": 33}, "end": {"line": 134, "column": 51}}, "line": 134}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 143, "column": 2}, "end": {"line": 143, "column": 3}}, "loc": {"start": {"line": 143, "column": 16}, "end": {"line": 146, "column": 3}}, "line": 143}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 149, "column": 2}, "end": {"line": 149, "column": 3}}, "loc": {"start": {"line": 149, "column": 29}, "end": {"line": 156, "column": 3}}, "line": 149}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 159, "column": 2}, "end": {"line": 159, "column": 3}}, "loc": {"start": {"line": 159, "column": 19}, "end": {"line": 161, "column": 3}}, "line": 159}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 164, "column": 2}, "end": {"line": 164, "column": 3}}, "loc": {"start": {"line": 164, "column": 17}, "end": {"line": 166, "column": 3}}, "line": 164}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 169, "column": 2}, "end": {"line": 169, "column": 3}}, "loc": {"start": {"line": 169, "column": 51}, "end": {"line": 171, "column": 3}}, "line": 169}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 174, "column": 2}, "end": {"line": 174, "column": 3}}, "loc": {"start": {"line": 174, "column": 24}, "end": {"line": 198, "column": 3}}, "line": 174}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 201, "column": 2}, "end": {"line": 201, "column": 3}}, "loc": {"start": {"line": 201, "column": 38}, "end": {"line": 213, "column": 3}}, "line": 201}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 216, "column": 2}, "end": {"line": 216, "column": 3}}, "loc": {"start": {"line": 216, "column": 38}, "end": {"line": 228, "column": 3}}, "line": 216}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 231, "column": 2}, "end": {"line": 231, "column": 3}}, "loc": {"start": {"line": 231, "column": 42}, "end": {"line": 238, "column": 3}}, "line": 231}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 241, "column": 2}, "end": {"line": 241, "column": 3}}, "loc": {"start": {"line": 241, "column": 39}, "end": {"line": 253, "column": 3}}, "line": 241}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 256, "column": 2}, "end": {"line": 256, "column": 3}}, "loc": {"start": {"line": 256, "column": 65}, "end": {"line": 270, "column": 3}}, "line": 256}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 273, "column": 2}, "end": {"line": 273, "column": 3}}, "loc": {"start": {"line": 273, "column": 31}, "end": {"line": 281, "column": 3}}, "line": 273}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 284, "column": 2}, "end": {"line": 284, "column": 3}}, "loc": {"start": {"line": 284, "column": 31}, "end": {"line": 288, "column": 3}}, "line": 284}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 285, "column": 36}, "end": {"line": 285, "column": 37}}, "loc": {"start": {"line": 285, "column": 42}, "end": {"line": 287, "column": 5}}, "line": 285}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 291, "column": 2}, "end": {"line": 291, "column": 3}}, "loc": {"start": {"line": 291, "column": 26}, "end": {"line": 302, "column": 3}}, "line": 291}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 301, "column": 25}, "end": {"line": 301, "column": 26}}, "loc": {"start": {"line": 301, "column": 32}, "end": {"line": 301, "column": 54}}, "line": 301}, "23": {"name": "(anonymous_23)", "decl": {"start": {"line": 305, "column": 2}, "end": {"line": 305, "column": 3}}, "loc": {"start": {"line": 305, "column": 18}, "end": {"line": 310, "column": 3}}, "line": 305}, "24": {"name": "(anonymous_24)", "decl": {"start": {"line": 335, "column": 13}, "end": {"line": 335, "column": 14}}, "loc": {"start": {"line": 335, "column": 83}, "end": {"line": 337, "column": 3}}, "line": 335}, "25": {"name": "(anonymous_25)", "decl": {"start": {"line": 340, "column": 13}, "end": {"line": 340, "column": 14}}, "loc": {"start": {"line": 340, "column": 35}, "end": {"line": 351, "column": 3}}, "line": 340}, "26": {"name": "(anonymous_26)", "decl": {"start": {"line": 342, "column": 33}, "end": {"line": 342, "column": 34}}, "loc": {"start": {"line": 342, "column": 49}, "end": {"line": 347, "column": 7}}, "line": 342}, "27": {"name": "(anonymous_27)", "decl": {"start": {"line": 354, "column": 15}, "end": {"line": 354, "column": 16}}, "loc": {"start": {"line": 354, "column": 45}, "end": {"line": 365, "column": 3}}, "line": 354}, "28": {"name": "(anonymous_28)", "decl": {"start": {"line": 356, "column": 29}, "end": {"line": 356, "column": 30}}, "loc": {"start": {"line": 356, "column": 45}, "end": {"line": 361, "column": 7}}, "line": 356}, "29": {"name": "(anonymous_29)", "decl": {"start": {"line": 368, "column": 18}, "end": {"line": 368, "column": 19}}, "loc": {"start": {"line": 368, "column": 25}, "end": {"line": 372, "column": 3}}, "line": 368}, "30": {"name": "(anonymous_30)", "decl": {"start": {"line": 375, "column": 12}, "end": {"line": 375, "column": 13}}, "loc": {"start": {"line": 375, "column": 18}, "end": {"line": 379, "column": 3}}, "line": 375}, "31": {"name": "useCache", "decl": {"start": {"line": 383, "column": 16}, "end": {"line": 383, "column": 24}}, "loc": {"start": {"line": 385, "column": 2}, "end": {"line": 395, "column": 1}}, "line": 385}, "32": {"name": "(anonymous_32)", "decl": {"start": {"line": 387, "column": 9}, "end": {"line": 387, "column": 10}}, "loc": {"start": {"line": 387, "column": 26}, "end": {"line": 387, "column": 40}}, "line": 387}, "33": {"name": "(anonymous_33)", "decl": {"start": {"line": 388, "column": 9}, "end": {"line": 388, "column": 10}}, "loc": {"start": {"line": 389, "column": 6}, "end": {"line": 389, "column": 37}}, "line": 389}, "34": {"name": "(anonymous_34)", "decl": {"start": {"line": 390, "column": 9}, "end": {"line": 390, "column": 10}}, "loc": {"start": {"line": 390, "column": 26}, "end": {"line": 390, "column": 40}}, "line": 390}, "35": {"name": "(anonymous_35)", "decl": {"start": {"line": 391, "column": 12}, "end": {"line": 391, "column": 13}}, "loc": {"start": {"line": 391, "column": 29}, "end": {"line": 391, "column": 46}}, "line": 391}, "36": {"name": "(anonymous_36)", "decl": {"start": {"line": 392, "column": 11}, "end": {"line": 392, "column": 12}}, "loc": {"start": {"line": 392, "column": 17}, "end": {"line": 392, "column": 30}}, "line": 392}}, "branchMap": {"0": {"loc": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 37}}, "type": "default-arg", "locations": [{"start": {"line": 52, "column": 35}, "end": {"line": 52, "column": 37}}], "line": 52}, "1": {"loc": {"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 46}}, "type": "default-arg", "locations": [{"start": {"line": 53, "column": 41}, "end": {"line": 53, "column": 46}}], "line": 53}, "2": {"loc": {"start": {"line": 72, "column": 4}, "end": {"line": 75, "column": 5}}, "type": "if", "locations": [{"start": {"line": 72, "column": 4}, "end": {"line": 75, "column": 5}}, {"start": {}, "end": {}}], "line": 72}, "3": {"loc": {"start": {"line": 78, "column": 4}, "end": {"line": 82, "column": 5}}, "type": "if", "locations": [{"start": {"line": 78, "column": 4}, "end": {"line": 82, "column": 5}}, {"start": {}, "end": {}}], "line": 78}, "4": {"loc": {"start": {"line": 93, "column": 42}, "end": {"line": 93, "column": 61}}, "type": "default-arg", "locations": [{"start": {"line": 93, "column": 59}, "end": {"line": 93, "column": 61}}], "line": 93}, "5": {"loc": {"start": {"line": 95, "column": 4}, "end": {"line": 97, "column": 5}}, "type": "if", "locations": [{"start": {"line": 95, "column": 4}, "end": {"line": 97, "column": 5}}, {"start": {}, "end": {}}], "line": 95}, "6": {"loc": {"start": {"line": 102, "column": 11}, "end": {"line": 102, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 102, "column": 11}, "end": {"line": 102, "column": 14}}, {"start": {"line": 102, "column": 18}, "end": {"line": 102, "column": 40}}], "line": 102}, "7": {"loc": {"start": {"line": 115, "column": 4}, "end": {"line": 115, "column": 28}}, "type": "if", "locations": [{"start": {"line": 115, "column": 4}, "end": {"line": 115, "column": 28}}, {"start": {}, "end": {}}], "line": 115}, "8": {"loc": {"start": {"line": 117, "column": 4}, "end": {"line": 120, "column": 5}}, "type": "if", "locations": [{"start": {"line": 117, "column": 4}, "end": {"line": 120, "column": 5}}, {"start": {}, "end": {}}], "line": 117}, "9": {"loc": {"start": {"line": 134, "column": 6}, "end": {"line": 137, "column": 7}}, "type": "if", "locations": [{"start": {"line": 134, "column": 6}, "end": {"line": 137, "column": 7}}, {"start": {}, "end": {}}], "line": 134}, "10": {"loc": {"start": {"line": 154, "column": 15}, "end": {"line": 154, "column": 72}}, "type": "cond-expr", "locations": [{"start": {"line": 154, "column": 35}, "end": {"line": 154, "column": 68}}, {"start": {"line": 154, "column": 71}, "end": {"line": 154, "column": 72}}], "line": 154}, "11": {"loc": {"start": {"line": 175, "column": 4}, "end": {"line": 175, "column": 37}}, "type": "if", "locations": [{"start": {"line": 175, "column": 4}, "end": {"line": 175, "column": 37}}, {"start": {}, "end": {}}], "line": 175}, "12": {"loc": {"start": {"line": 179, "column": 4}, "end": {"line": 192, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 180, "column": 6}, "end": {"line": 182, "column": 13}}, {"start": {"line": 183, "column": 6}, "end": {"line": 185, "column": 13}}, {"start": {"line": 186, "column": 6}, "end": {"line": 188, "column": 13}}, {"start": {"line": 189, "column": 6}, "end": {"line": 191, "column": 13}}], "line": 179}, "13": {"loc": {"start": {"line": 194, "column": 4}, "end": {"line": 197, "column": 5}}, "type": "if", "locations": [{"start": {"line": 194, "column": 4}, "end": {"line": 197, "column": 5}}, {"start": {}, "end": {}}], "line": 194}, "14": {"loc": {"start": {"line": 206, "column": 6}, "end": {"line": 209, "column": 7}}, "type": "if", "locations": [{"start": {"line": 206, "column": 6}, "end": {"line": 209, "column": 7}}, {"start": {}, "end": {}}], "line": 206}, "15": {"loc": {"start": {"line": 221, "column": 6}, "end": {"line": 224, "column": 7}}, "type": "if", "locations": [{"start": {"line": 221, "column": 6}, "end": {"line": 224, "column": 7}}, {"start": {}, "end": {}}], "line": 221}, "16": {"loc": {"start": {"line": 233, "column": 6}, "end": {"line": 235, "column": 7}}, "type": "if", "locations": [{"start": {"line": 233, "column": 6}, "end": {"line": 235, "column": 7}}, {"start": {}, "end": {}}], "line": 233}, "17": {"loc": {"start": {"line": 246, "column": 6}, "end": {"line": 249, "column": 7}}, "type": "if", "locations": [{"start": {"line": 246, "column": 6}, "end": {"line": 249, "column": 7}}, {"start": {}, "end": {}}], "line": 246}, "18": {"loc": {"start": {"line": 257, "column": 4}, "end": {"line": 257, "column": 42}}, "type": "if", "locations": [{"start": {"line": 257, "column": 4}, "end": {"line": 257, "column": 42}}, {"start": {}, "end": {}}], "line": 257}, "19": {"loc": {"start": {"line": 259, "column": 4}, "end": {"line": 269, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 260, "column": 6}, "end": {"line": 262, "column": 13}}, {"start": {"line": 263, "column": 6}, "end": {"line": 265, "column": 13}}, {"start": {"line": 266, "column": 6}, "end": {"line": 268, "column": 13}}], "line": 259}, "20": {"loc": {"start": {"line": 296, "column": 6}, "end": {"line": 298, "column": 7}}, "type": "if", "locations": [{"start": {"line": 296, "column": 6}, "end": {"line": 298, "column": 7}}, {"start": {}, "end": {}}], "line": 296}, "21": {"loc": {"start": {"line": 306, "column": 4}, "end": {"line": 308, "column": 5}}, "type": "if", "locations": [{"start": {"line": 306, "column": 4}, "end": {"line": 308, "column": 5}}, {"start": {}, "end": {}}], "line": 306}, "22": {"loc": {"start": {"line": 343, "column": 8}, "end": {"line": 345, "column": 9}}, "type": "if", "locations": [{"start": {"line": 343, "column": 8}, "end": {"line": 345, "column": 9}}, {"start": {}, "end": {}}], "line": 343}, "23": {"loc": {"start": {"line": 357, "column": 8}, "end": {"line": 359, "column": 9}}, "type": "if", "locations": [{"start": {"line": 357, "column": 8}, "end": {"line": 359, "column": 9}}, {"start": {}, "end": {}}], "line": 357}, "24": {"loc": {"start": {"line": 357, "column": 12}, "end": {"line": 357, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 357, "column": 12}, "end": {"line": 357, "column": 17}}, {"start": {"line": 357, "column": 21}, "end": {"line": 357, "column": 44}}], "line": 357}, "25": {"loc": {"start": {"line": 384, "column": 2}, "end": {"line": 384, "column": 56}}, "type": "default-arg", "locations": [{"start": {"line": 384, "column": 28}, "end": {"line": 384, "column": 56}}], "line": 384}}, "s": {"0": 37, "1": 37, "2": 37, "3": 37, "4": 37, "5": 36, "6": 36, "7": 18, "8": 18, "9": 18, "10": 0, "11": 0, "12": 0, "13": 18, "14": 18, "15": 18, "16": 18, "17": 143, "18": 101, "19": 143, "20": 143, "21": 143, "22": 2, "23": 2, "24": 1, "25": 1, "26": 0, "27": 0, "28": 1, "29": 3, "30": 3, "31": 3, "32": 6, "33": 9, "34": 5, "35": 5, "36": 3, "37": 36, "38": 36, "39": 7, "40": 7, "41": 1, "42": 7, "43": 19, "44": 101, "45": 0, "46": 101, "47": 101, "48": 99, "49": 99, "50": 1, "51": 1, "52": 0, "53": 0, "54": 1, "55": 1, "56": 101, "57": 58, "58": 58, "59": 99, "60": 99, "61": 99, "62": 2971, "63": 57, "64": 57, "65": 99, "66": 1, "67": 1, "68": 1, "69": 3, "70": 3, "71": 3, "72": 1, "73": 0, "74": 0, "75": 0, "76": 0, "77": 1, "78": 1, "79": 1, "80": 3, "81": 0, "82": 0, "83": 1, "84": 179, "85": 0, "86": 179, "87": 18, "88": 18, "89": 18, "90": 18, "91": 143, "92": 36, "93": 37, "94": 6, "95": 6, "96": 6, "97": 6, "98": 4, "99": 2, "100": 6, "101": 2, "102": 32, "103": 32, "104": 32, "105": 1, "106": 1, "107": 1, "108": 1, "109": 1, "110": 3, "111": 3, "112": 8, "113": 0, "114": 8, "115": 1, "116": 2, "117": 2, "118": 3, "119": 0, "120": 3, "121": 1, "122": 1, "123": 1, "124": 1, "125": 1, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0}, "f": {"0": 37, "1": 36, "2": 143, "3": 2, "4": 3, "5": 3, "6": 9, "7": 36, "8": 7, "9": 1, "10": 7, "11": 19, "12": 101, "13": 99, "14": 1, "15": 0, "16": 1, "17": 179, "18": 36, "19": 37, "20": 6, "21": 6, "22": 2, "23": 32, "24": 1, "25": 3, "26": 8, "27": 2, "28": 3, "29": 1, "30": 1, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0}, "b": {"0": [0], "1": [0], "2": [18, 18], "3": [0, 18], "4": [137], "5": [101, 42], "6": [143, 137], "7": [1, 1], "8": [0, 1], "9": [5, 1], "10": [2, 5], "11": [0, 101], "12": [99, 1, 0, 1], "13": [58, 43], "14": [57, 2914], "15": [3, 0], "16": [0, 0], "17": [0, 3], "18": [0, 179], "19": [18, 18, 143], "20": [2, 2], "21": [32, 0], "22": [0, 8], "23": [0, 3], "24": [3, 3], "25": [0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "c8fed1e1d306d9954a5aa18355920cf695ef0275"}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\clients.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\clients.ts", "statementMap": {"0": {"start": {"line": 14, "column": 16}, "end": {"line": 20, "column": 3}}, "1": {"start": {"line": 22, "column": 17}, "end": {"line": 22, "column": 91}}, "2": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 45}}, "3": {"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 3}}, "4": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 52}}, "5": {"start": {"line": 32, "column": 16}, "end": {"line": 44, "column": 3}}, "6": {"start": {"line": 46, "column": 17}, "end": {"line": 46, "column": 91}}, "7": {"start": {"line": 48, "column": 2}, "end": {"line": 55, "column": 3}}, "8": {"start": {"line": 49, "column": 4}, "end": {"line": 53, "column": 7}}, "9": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 61}}, "10": {"start": {"line": 57, "column": 2}, "end": {"line": 59, "column": 3}}, "11": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 16}}, "12": {"start": {"line": 62, "column": 17}, "end": {"line": 62, "column": 28}}, "13": {"start": {"line": 63, "column": 2}, "end": {"line": 70, "column": 3}}, "14": {"start": {"line": 64, "column": 4}, "end": {"line": 69, "column": 5}}, "15": {"start": {"line": 65, "column": 6}, "end": {"line": 65, "column": 52}}, "16": {"start": {"line": 67, "column": 6}, "end": {"line": 67, "column": 72}}, "17": {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 27}}, "18": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 16}}, "19": {"start": {"line": 77, "column": 2}, "end": {"line": 83, "column": 3}}, "20": {"start": {"line": 78, "column": 4}, "end": {"line": 82, "column": 6}}, "21": {"start": {"line": 85, "column": 2}, "end": {"line": 170, "column": 3}}, "22": {"start": {"line": 86, "column": 19}, "end": {"line": 86, "column": 38}}, "23": {"start": {"line": 87, "column": 4}, "end": {"line": 93, "column": 5}}, "24": {"start": {"line": 88, "column": 6}, "end": {"line": 92, "column": 8}}, "25": {"start": {"line": 95, "column": 18}, "end": {"line": 107, "column": 5}}, "26": {"start": {"line": 109, "column": 19}, "end": {"line": 109, "column": 78}}, "27": {"start": {"line": 111, "column": 4}, "end": {"line": 117, "column": 5}}, "28": {"start": {"line": 112, "column": 6}, "end": {"line": 116, "column": 8}}, "29": {"start": {"line": 119, "column": 4}, "end": {"line": 125, "column": 5}}, "30": {"start": {"line": 120, "column": 6}, "end": {"line": 124, "column": 8}}, "31": {"start": {"line": 127, "column": 4}, "end": {"line": 134, "column": 5}}, "32": {"start": {"line": 128, "column": 6}, "end": {"line": 128, "column": 96}}, "33": {"start": {"line": 129, "column": 6}, "end": {"line": 133, "column": 8}}, "34": {"start": {"line": 137, "column": 23}, "end": {"line": 137, "column": 37}}, "35": {"start": {"line": 140, "column": 25}, "end": {"line": 140, "column": 81}}, "36": {"start": {"line": 141, "column": 24}, "end": {"line": 141, "column": 56}}, "37": {"start": {"line": 143, "column": 41}, "end": {"line": 153, "column": 5}}, "38": {"start": {"line": 155, "column": 4}, "end": {"line": 158, "column": 6}}, "39": {"start": {"line": 161, "column": 4}, "end": {"line": 164, "column": 7}}, "40": {"start": {"line": 165, "column": 4}, "end": {"line": 169, "column": 6}}, "41": {"start": {"line": 180, "column": 16}, "end": {"line": 196, "column": 3}}, "42": {"start": {"line": 198, "column": 17}, "end": {"line": 203, "column": 3}}, "43": {"start": {"line": 205, "column": 17}, "end": {"line": 205, "column": 89}}, "44": {"start": {"line": 207, "column": 2}, "end": {"line": 210, "column": 3}}, "45": {"start": {"line": 208, "column": 4}, "end": {"line": 208, "column": 58}}, "46": {"start": {"line": 209, "column": 4}, "end": {"line": 209, "column": 47}}, "47": {"start": {"line": 212, "column": 2}, "end": {"line": 212, "column": 21}}, "48": {"start": {"line": 226, "column": 33}, "end": {"line": 226, "column": 35}}, "49": {"start": {"line": 227, "column": 24}, "end": {"line": 227, "column": 26}}, "50": {"start": {"line": 228, "column": 19}, "end": {"line": 228, "column": 20}}, "51": {"start": {"line": 230, "column": 2}, "end": {"line": 233, "column": 3}}, "52": {"start": {"line": 231, "column": 4}, "end": {"line": 231, "column": 49}}, "53": {"start": {"line": 232, "column": 4}, "end": {"line": 232, "column": 30}}, "54": {"start": {"line": 235, "column": 2}, "end": {"line": 238, "column": 3}}, "55": {"start": {"line": 236, "column": 4}, "end": {"line": 236, "column": 51}}, "56": {"start": {"line": 237, "column": 4}, "end": {"line": 237, "column": 32}}, "57": {"start": {"line": 240, "column": 2}, "end": {"line": 243, "column": 3}}, "58": {"start": {"line": 241, "column": 4}, "end": {"line": 241, "column": 51}}, "59": {"start": {"line": 242, "column": 4}, "end": {"line": 242, "column": 32}}, "60": {"start": {"line": 245, "column": 2}, "end": {"line": 248, "column": 3}}, "61": {"start": {"line": 246, "column": 4}, "end": {"line": 246, "column": 53}}, "62": {"start": {"line": 247, "column": 4}, "end": {"line": 247, "column": 50}}, "63": {"start": {"line": 250, "column": 2}, "end": {"line": 252, "column": 3}}, "64": {"start": {"line": 251, "column": 4}, "end": {"line": 251, "column": 16}}, "65": {"start": {"line": 255, "column": 2}, "end": {"line": 255, "column": 24}}, "66": {"start": {"line": 257, "column": 16}, "end": {"line": 269, "column": 3}}, "67": {"start": {"line": 271, "column": 17}, "end": {"line": 271, "column": 89}}, "68": {"start": {"line": 273, "column": 2}, "end": {"line": 276, "column": 3}}, "69": {"start": {"line": 274, "column": 4}, "end": {"line": 274, "column": 58}}, "70": {"start": {"line": 275, "column": 4}, "end": {"line": 275, "column": 47}}, "71": {"start": {"line": 278, "column": 2}, "end": {"line": 278, "column": 21}}}, "fnMap": {"0": {"name": "getTenantByDomain", "decl": {"start": {"line": 13, "column": 22}, "end": {"line": 13, "column": 39}}, "loc": {"start": {"line": 13, "column": 56}, "end": {"line": 24, "column": 1}}, "line": 13}, "1": {"name": "getClientByDomain", "decl": {"start": {"line": 27, "column": 22}, "end": {"line": 27, "column": 39}}, "loc": {"start": {"line": 27, "column": 56}, "end": {"line": 73, "column": 1}}, "line": 27}, "2": {"name": "getClientByEmailDomain", "decl": {"start": {"line": 76, "column": 22}, "end": {"line": 76, "column": 44}}, "loc": {"start": {"line": 76, "column": 89}, "end": {"line": 171, "column": 1}}, "line": 76}, "3": {"name": "createClient", "decl": {"start": {"line": 174, "column": 22}, "end": {"line": 174, "column": 34}}, "loc": {"start": {"line": 179, "column": 3}, "end": {"line": 213, "column": 1}}, "line": 179}, "4": {"name": "updateClient", "decl": {"start": {"line": 216, "column": 22}, "end": {"line": 216, "column": 34}}, "loc": {"start": {"line": 224, "column": 2}, "end": {"line": 279, "column": 1}}, "line": 224}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 9}, "end": {"line": 23, "column": 44}}, "type": "cond-expr", "locations": [{"start": {"line": 23, "column": 26}, "end": {"line": 23, "column": 37}}, {"start": {"line": 23, "column": 40}, "end": {"line": 23, "column": 44}}], "line": 23}, "1": {"loc": {"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 3}}, "type": "if", "locations": [{"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 3}}, {"start": {}, "end": {}}], "line": 28}, "2": {"loc": {"start": {"line": 48, "column": 2}, "end": {"line": 55, "column": 3}}, "type": "if", "locations": [{"start": {"line": 48, "column": 2}, "end": {"line": 55, "column": 3}}, {"start": {}, "end": {}}], "line": 48}, "3": {"loc": {"start": {"line": 57, "column": 2}, "end": {"line": 59, "column": 3}}, "type": "if", "locations": [{"start": {"line": 57, "column": 2}, "end": {"line": 59, "column": 3}}, {"start": {}, "end": {}}], "line": 57}, "4": {"loc": {"start": {"line": 63, "column": 2}, "end": {"line": 70, "column": 3}}, "type": "if", "locations": [{"start": {"line": 63, "column": 2}, "end": {"line": 70, "column": 3}}, {"start": {}, "end": {}}], "line": 63}, "5": {"loc": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 21}}, {"start": {"line": 63, "column": 25}, "end": {"line": 63, "column": 60}}], "line": 63}, "6": {"loc": {"start": {"line": 77, "column": 2}, "end": {"line": 83, "column": 3}}, "type": "if", "locations": [{"start": {"line": 77, "column": 2}, "end": {"line": 83, "column": 3}}, {"start": {}, "end": {}}], "line": 77}, "7": {"loc": {"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": 12}}, {"start": {"line": 77, "column": 16}, "end": {"line": 77, "column": 41}}], "line": 77}, "8": {"loc": {"start": {"line": 87, "column": 4}, "end": {"line": 93, "column": 5}}, "type": "if", "locations": [{"start": {"line": 87, "column": 4}, "end": {"line": 93, "column": 5}}, {"start": {}, "end": {}}], "line": 87}, "9": {"loc": {"start": {"line": 111, "column": 4}, "end": {"line": 117, "column": 5}}, "type": "if", "locations": [{"start": {"line": 111, "column": 4}, "end": {"line": 117, "column": 5}}, {"start": {}, "end": {}}], "line": 111}, "10": {"loc": {"start": {"line": 119, "column": 4}, "end": {"line": 125, "column": 5}}, "type": "if", "locations": [{"start": {"line": 119, "column": 4}, "end": {"line": 125, "column": 5}}, {"start": {}, "end": {}}], "line": 119}, "11": {"loc": {"start": {"line": 119, "column": 8}, "end": {"line": 119, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 119, "column": 8}, "end": {"line": 119, "column": 20}}, {"start": {"line": 119, "column": 24}, "end": {"line": 119, "column": 48}}], "line": 119}, "12": {"loc": {"start": {"line": 127, "column": 4}, "end": {"line": 134, "column": 5}}, "type": "if", "locations": [{"start": {"line": 127, "column": 4}, "end": {"line": 134, "column": 5}}, {"start": {}, "end": {}}], "line": 127}, "13": {"loc": {"start": {"line": 140, "column": 25}, "end": {"line": 140, "column": 81}}, "type": "binary-expr", "locations": [{"start": {"line": 140, "column": 25}, "end": {"line": 140, "column": 45}}, {"start": {"line": 140, "column": 49}, "end": {"line": 140, "column": 81}}], "line": 140}, "14": {"loc": {"start": {"line": 146, "column": 16}, "end": {"line": 146, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 146, "column": 16}, "end": {"line": 146, "column": 36}}, {"start": {"line": 146, "column": 40}, "end": {"line": 146, "column": 72}}], "line": 146}, "15": {"loc": {"start": {"line": 148, "column": 15}, "end": {"line": 148, "column": 80}}, "type": "cond-expr", "locations": [{"start": {"line": 148, "column": 51}, "end": {"line": 148, "column": 69}}, {"start": {"line": 148, "column": 72}, "end": {"line": 148, "column": 80}}], "line": 148}, "16": {"loc": {"start": {"line": 152, "column": 17}, "end": {"line": 152, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 152, "column": 41}, "end": {"line": 152, "column": 72}}, {"start": {"line": 152, "column": 75}, "end": {"line": 152, "column": 79}}], "line": 152}, "17": {"loc": {"start": {"line": 163, "column": 13}, "end": {"line": 163, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 163, "column": 38}, "end": {"line": 163, "column": 51}}, {"start": {"line": 163, "column": 54}, "end": {"line": 163, "column": 67}}], "line": 163}, "18": {"loc": {"start": {"line": 201, "column": 4}, "end": {"line": 201, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 201, "column": 4}, "end": {"line": 201, "column": 21}}, {"start": {"line": 201, "column": 25}, "end": {"line": 201, "column": 33}}], "line": 201}, "19": {"loc": {"start": {"line": 202, "column": 4}, "end": {"line": 202, "column": 68}}, "type": "cond-expr", "locations": [{"start": {"line": 202, "column": 26}, "end": {"line": 202, "column": 61}}, {"start": {"line": 202, "column": 64}, "end": {"line": 202, "column": 68}}], "line": 202}, "20": {"loc": {"start": {"line": 207, "column": 2}, "end": {"line": 210, "column": 3}}, "type": "if", "locations": [{"start": {"line": 207, "column": 2}, "end": {"line": 210, "column": 3}}, {"start": {}, "end": {}}], "line": 207}, "21": {"loc": {"start": {"line": 230, "column": 2}, "end": {"line": 233, "column": 3}}, "type": "if", "locations": [{"start": {"line": 230, "column": 2}, "end": {"line": 233, "column": 3}}, {"start": {}, "end": {}}], "line": 230}, "22": {"loc": {"start": {"line": 235, "column": 2}, "end": {"line": 238, "column": 3}}, "type": "if", "locations": [{"start": {"line": 235, "column": 2}, "end": {"line": 238, "column": 3}}, {"start": {}, "end": {}}], "line": 235}, "23": {"loc": {"start": {"line": 240, "column": 2}, "end": {"line": 243, "column": 3}}, "type": "if", "locations": [{"start": {"line": 240, "column": 2}, "end": {"line": 243, "column": 3}}, {"start": {}, "end": {}}], "line": 240}, "24": {"loc": {"start": {"line": 245, "column": 2}, "end": {"line": 248, "column": 3}}, "type": "if", "locations": [{"start": {"line": 245, "column": 2}, "end": {"line": 248, "column": 3}}, {"start": {}, "end": {}}], "line": 245}, "25": {"loc": {"start": {"line": 250, "column": 2}, "end": {"line": 252, "column": 3}}, "type": "if", "locations": [{"start": {"line": 250, "column": 2}, "end": {"line": 252, "column": 3}}, {"start": {}, "end": {}}], "line": 250}, "26": {"loc": {"start": {"line": 273, "column": 2}, "end": {"line": 276, "column": 3}}, "type": "if", "locations": [{"start": {"line": 273, "column": 2}, "end": {"line": 276, "column": 3}}, {"start": {}, "end": {}}], "line": 273}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\config.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\config.ts", "statementMap": {"0": {"start": {"line": 11, "column": 18}, "end": {"line": 32, "column": 2}}, "1": {"start": {"line": 36, "column": 2}, "end": {"line": 41, "column": 3}}, "2": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 40}}, "3": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 65}}, "4": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 84}}, "5": {"start": {"line": 45, "column": 12}, "end": {"line": 45, "column": 25}}, "6": {"start": {"line": 48, "column": 28}, "end": {"line": 64, "column": 10}}, "7": {"start": {"line": 67, "column": 28}, "end": {"line": 76, "column": 10}}, "8": {"start": {"line": 83, "column": 29}, "end": {"line": 83, "column": 52}}, "9": {"start": {"line": 83, "column": 35}, "end": {"line": 83, "column": 52}}, "10": {"start": {"line": 84, "column": 28}, "end": {"line": 84, "column": 50}}, "11": {"start": {"line": 84, "column": 34}, "end": {"line": 84, "column": 50}}, "12": {"start": {"line": 85, "column": 33}, "end": {"line": 90, "column": 1}}, "13": {"start": {"line": 86, "column": 2}, "end": {"line": 88, "column": 3}}, "14": {"start": {"line": 87, "column": 4}, "end": {"line": 87, "column": 82}}, "15": {"start": {"line": 89, "column": 2}, "end": {"line": 89, "column": 31}}, "16": {"start": {"line": 93, "column": 30}, "end": {"line": 108, "column": 1}}, "17": {"start": {"line": 94, "column": 29}, "end": {"line": 99, "column": 3}}, "18": {"start": {"line": 101, "column": 18}, "end": {"line": 101, "column": 69}}, "19": {"start": {"line": 101, "column": 51}, "end": {"line": 101, "column": 68}}, "20": {"start": {"line": 103, "column": 2}, "end": {"line": 105, "column": 3}}, "21": {"start": {"line": 104, "column": 4}, "end": {"line": 104, "column": 85}}, "22": {"start": {"line": 107, "column": 2}, "end": {"line": 107, "column": 14}}}, "fnMap": {"0": {"name": "validateEnv", "decl": {"start": {"line": 35, "column": 9}, "end": {"line": 35, "column": 20}}, "loc": {"start": {"line": 35, "column": 23}, "end": {"line": 42, "column": 1}}, "line": 35}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 83, "column": 29}, "end": {"line": 83, "column": 30}}, "loc": {"start": {"line": 83, "column": 35}, "end": {"line": 83, "column": 52}}, "line": 83}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 84, "column": 28}, "end": {"line": 84, "column": 29}}, "loc": {"start": {"line": 84, "column": 34}, "end": {"line": 84, "column": 50}}, "line": 84}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 85, "column": 33}, "end": {"line": 85, "column": 34}}, "loc": {"start": {"line": 85, "column": 39}, "end": {"line": 90, "column": 1}}, "line": 85}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 93, "column": 30}, "end": {"line": 93, "column": 31}}, "loc": {"start": {"line": 93, "column": 36}, "end": {"line": 108, "column": 1}}, "line": 93}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 101, "column": 44}, "end": {"line": 101, "column": 45}}, "loc": {"start": {"line": 101, "column": 51}, "end": {"line": 101, "column": 68}}, "line": 101}}, "branchMap": {"0": {"loc": {"start": {"line": 86, "column": 2}, "end": {"line": 88, "column": 3}}, "type": "if", "locations": [{"start": {"line": 86, "column": 2}, "end": {"line": 88, "column": 3}}, {"start": {}, "end": {}}], "line": 86}, "1": {"loc": {"start": {"line": 103, "column": 2}, "end": {"line": 105, "column": 3}}, "type": "if", "locations": [{"start": {"line": 103, "column": 2}, "end": {"line": 105, "column": 3}}, {"start": {}, "end": {}}], "line": 103}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0, 0], "1": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\dal.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\dal.ts", "statementMap": {"0": {"start": {"line": 7, "column": 29}, "end": {"line": 73, "column": 2}}, "1": {"start": {"line": 13, "column": 2}, "end": {"line": 72, "column": 3}}, "2": {"start": {"line": 14, "column": 24}, "end": {"line": 14, "column": 39}}, "3": {"start": {"line": 15, "column": 33}, "end": {"line": 15, "column": 49}}, "4": {"start": {"line": 18, "column": 33}, "end": {"line": 22, "column": 5}}, "5": {"start": {"line": 24, "column": 35}, "end": {"line": 24, "column": 39}}, "6": {"start": {"line": 27, "column": 4}, "end": {"line": 34, "column": 5}}, "7": {"start": {"line": 28, "column": 21}, "end": {"line": 28, "column": 48}}, "8": {"start": {"line": 29, "column": 6}, "end": {"line": 33, "column": 7}}, "9": {"start": {"line": 30, "column": 8}, "end": {"line": 30, "column": 33}}, "10": {"start": {"line": 31, "column": 8}, "end": {"line": 31, "column": 65}}, "11": {"start": {"line": 32, "column": 8}, "end": {"line": 32, "column": 14}}, "12": {"start": {"line": 37, "column": 4}, "end": {"line": 47, "column": 5}}, "13": {"start": {"line": 38, "column": 25}, "end": {"line": 38, "column": 45}}, "14": {"start": {"line": 40, "column": 6}, "end": {"line": 46, "column": 7}}, "15": {"start": {"line": 41, "column": 8}, "end": {"line": 45, "column": 9}}, "16": {"start": {"line": 42, "column": 10}, "end": {"line": 42, "column": 35}}, "17": {"start": {"line": 43, "column": 10}, "end": {"line": 43, "column": 73}}, "18": {"start": {"line": 44, "column": 10}, "end": {"line": 44, "column": 16}}, "19": {"start": {"line": 49, "column": 4}, "end": {"line": 52, "column": 5}}, "20": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 62}}, "21": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 18}}, "22": {"start": {"line": 55, "column": 20}, "end": {"line": 55, "column": 55}}, "23": {"start": {"line": 56, "column": 4}, "end": {"line": 59, "column": 5}}, "24": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 43}}, "25": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 18}}, "26": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 74}}, "27": {"start": {"line": 63, "column": 4}, "end": {"line": 68, "column": 5}}, "28": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 52}}, "29": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 15}}, "30": {"start": {"line": 75, "column": 23}, "end": {"line": 82, "column": 2}}, "31": {"start": {"line": 76, "column": 18}, "end": {"line": 76, "column": 39}}, "32": {"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 27}}, "33": {"start": {"line": 77, "column": 16}, "end": {"line": 77, "column": 27}}, "34": {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 31}}, "35": {"start": {"line": 85, "column": 23}, "end": {"line": 88, "column": 1}}, "36": {"start": {"line": 86, "column": 2}, "end": {"line": 86, "column": 46}}, "37": {"start": {"line": 86, "column": 34}, "end": {"line": 86, "column": 46}}, "38": {"start": {"line": 87, "column": 2}, "end": {"line": 87, "column": 45}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 35}, "end": {"line": 7, "column": 36}}, "loc": {"start": {"line": 12, "column": 13}, "end": {"line": 73, "column": 1}}, "line": 12}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 75, "column": 29}, "end": {"line": 75, "column": 30}}, "loc": {"start": {"line": 75, "column": 41}, "end": {"line": 82, "column": 1}}, "line": 75}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 85, "column": 23}, "end": {"line": 85, "column": 24}}, "loc": {"start": {"line": 85, "column": 63}, "end": {"line": 88, "column": 1}}, "line": 85}}, "branchMap": {"0": {"loc": {"start": {"line": 29, "column": 6}, "end": {"line": 33, "column": 7}}, "type": "if", "locations": [{"start": {"line": 29, "column": 6}, "end": {"line": 33, "column": 7}}, {"start": {}, "end": {}}], "line": 29}, "1": {"loc": {"start": {"line": 37, "column": 4}, "end": {"line": 47, "column": 5}}, "type": "if", "locations": [{"start": {"line": 37, "column": 4}, "end": {"line": 47, "column": 5}}, {"start": {}, "end": {}}], "line": 37}, "2": {"loc": {"start": {"line": 41, "column": 8}, "end": {"line": 45, "column": 9}}, "type": "if", "locations": [{"start": {"line": 41, "column": 8}, "end": {"line": 45, "column": 9}}, {"start": {}, "end": {}}], "line": 41}, "3": {"loc": {"start": {"line": 41, "column": 12}, "end": {"line": 41, "column": 101}}, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 12}, "end": {"line": 41, "column": 66}}, {"start": {"line": 41, "column": 70}, "end": {"line": 41, "column": 101}}], "line": 41}, "4": {"loc": {"start": {"line": 49, "column": 4}, "end": {"line": 52, "column": 5}}, "type": "if", "locations": [{"start": {"line": 49, "column": 4}, "end": {"line": 52, "column": 5}}, {"start": {}, "end": {}}], "line": 49}, "5": {"loc": {"start": {"line": 56, "column": 4}, "end": {"line": 59, "column": 5}}, "type": "if", "locations": [{"start": {"line": 56, "column": 4}, "end": {"line": 59, "column": 5}}, {"start": {}, "end": {}}], "line": 56}, "6": {"loc": {"start": {"line": 67, "column": 13}, "end": {"line": 67, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 13}, "end": {"line": 67, "column": 38}}, {"start": {"line": 67, "column": 42}, "end": {"line": 67, "column": 44}}], "line": 67}, "7": {"loc": {"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 27}}, "type": "if", "locations": [{"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 27}}, {"start": {}, "end": {}}], "line": 77}, "8": {"loc": {"start": {"line": 86, "column": 2}, "end": {"line": 86, "column": 46}}, "type": "if", "locations": [{"start": {"line": 86, "column": 2}, "end": {"line": 86, "column": 46}}, {"start": {}, "end": {}}], "line": 86}, "9": {"loc": {"start": {"line": 86, "column": 6}, "end": {"line": 86, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 86, "column": 6}, "end": {"line": 86, "column": 14}}, {"start": {"line": 86, "column": 18}, "end": {"line": 86, "column": 32}}], "line": 86}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\database.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\database.ts", "statementMap": {"0": {"start": {"line": 43, "column": 19}, "end": {"line": 43, "column": 44}}, "1": {"start": {"line": 53, "column": 34}, "end": {"line": 53, "column": 38}}, "2": {"start": {"line": 55, "column": 2}, "end": {"line": 107, "column": 3}}, "3": {"start": {"line": 56, "column": 17}, "end": {"line": 56, "column": 34}}, "4": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 34}}, "5": {"start": {"line": 60, "column": 4}, "end": {"line": 62, "column": 5}}, "6": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 71}}, "7": {"start": {"line": 65, "column": 4}, "end": {"line": 67, "column": 5}}, "8": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 75}}, "9": {"start": {"line": 69, "column": 22}, "end": {"line": 69, "column": 32}}, "10": {"start": {"line": 70, "column": 32}, "end": {"line": 70, "column": 65}}, "11": {"start": {"line": 71, "column": 21}, "end": {"line": 71, "column": 43}}, "12": {"start": {"line": 74, "column": 4}, "end": {"line": 80, "column": 5}}, "13": {"start": {"line": 75, "column": 6}, "end": {"line": 79, "column": 9}}, "14": {"start": {"line": 82, "column": 4}, "end": {"line": 86, "column": 6}}, "15": {"start": {"line": 89, "column": 20}, "end": {"line": 89, "column": 42}}, "16": {"start": {"line": 90, "column": 4}, "end": {"line": 96, "column": 7}}, "17": {"start": {"line": 98, "column": 4}, "end": {"line": 102, "column": 6}}, "18": {"start": {"line": 104, "column": 4}, "end": {"line": 106, "column": 5}}, "19": {"start": {"line": 105, "column": 6}, "end": {"line": 105, "column": 23}}, "20": {"start": {"line": 118, "column": 17}, "end": {"line": 118, "column": 62}}, "21": {"start": {"line": 120, "column": 2}, "end": {"line": 122, "column": 3}}, "22": {"start": {"line": 121, "column": 4}, "end": {"line": 121, "column": 18}}, "23": {"start": {"line": 124, "column": 2}, "end": {"line": 128, "column": 4}}, "24": {"start": {"line": 138, "column": 34}, "end": {"line": 138, "column": 38}}, "25": {"start": {"line": 140, "column": 2}, "end": {"line": 192, "column": 3}}, "26": {"start": {"line": 141, "column": 17}, "end": {"line": 141, "column": 34}}, "27": {"start": {"line": 142, "column": 4}, "end": {"line": 142, "column": 34}}, "28": {"start": {"line": 145, "column": 4}, "end": {"line": 147, "column": 5}}, "29": {"start": {"line": 146, "column": 6}, "end": {"line": 146, "column": 75}}, "30": {"start": {"line": 149, "column": 4}, "end": {"line": 149, "column": 32}}, "31": {"start": {"line": 151, "column": 27}, "end": {"line": 151, "column": 29}}, "32": {"start": {"line": 153, "column": 4}, "end": {"line": 156, "column": 5}}, "33": {"start": {"line": 154, "column": 21}, "end": {"line": 154, "column": 54}}, "34": {"start": {"line": 155, "column": 6}, "end": {"line": 155, "column": 32}}, "35": {"start": {"line": 158, "column": 4}, "end": {"line": 158, "column": 33}}, "36": {"start": {"line": 160, "column": 4}, "end": {"line": 164, "column": 6}}, "37": {"start": {"line": 163, "column": 46}, "end": {"line": 163, "column": 63}}, "38": {"start": {"line": 167, "column": 4}, "end": {"line": 173, "column": 5}}, "39": {"start": {"line": 168, "column": 6}, "end": {"line": 172, "column": 7}}, "40": {"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 39}}, "41": {"start": {"line": 171, "column": 8}, "end": {"line": 171, "column": 72}}, "42": {"start": {"line": 175, "column": 20}, "end": {"line": 175, "column": 42}}, "43": {"start": {"line": 176, "column": 4}, "end": {"line": 181, "column": 7}}, "44": {"start": {"line": 183, "column": 4}, "end": {"line": 187, "column": 6}}, "45": {"start": {"line": 189, "column": 4}, "end": {"line": 191, "column": 5}}, "46": {"start": {"line": 190, "column": 6}, "end": {"line": 190, "column": 23}}, "47": {"start": {"line": 199, "column": 17}, "end": {"line": 202, "column": 3}}, "48": {"start": {"line": 204, "column": 2}, "end": {"line": 204, "column": 56}}, "49": {"start": {"line": 211, "column": 17}, "end": {"line": 214, "column": 3}}, "50": {"start": {"line": 216, "column": 2}, "end": {"line": 216, "column": 56}}, "51": {"start": {"line": 228, "column": 2}, "end": {"line": 245, "column": 3}}, "52": {"start": {"line": 229, "column": 17}, "end": {"line": 229, "column": 34}}, "53": {"start": {"line": 231, "column": 4}, "end": {"line": 236, "column": 6}}, "54": {"start": {"line": 238, "column": 4}, "end": {"line": 238, "column": 58}}, "55": {"start": {"line": 239, "column": 4}, "end": {"line": 244, "column": 6}}, "56": {"start": {"line": 252, "column": 2}, "end": {"line": 258, "column": 3}}, "57": {"start": {"line": 253, "column": 17}, "end": {"line": 253, "column": 34}}, "58": {"start": {"line": 254, "column": 4}, "end": {"line": 254, "column": 21}}, "59": {"start": {"line": 255, "column": 4}, "end": {"line": 255, "column": 60}}, "60": {"start": {"line": 257, "column": 4}, "end": {"line": 257, "column": 64}}, "61": {"start": {"line": 265, "column": 2}, "end": {"line": 292, "column": 3}}, "62": {"start": {"line": 267, "column": 6}, "end": {"line": 267, "column": 61}}, "63": {"start": {"line": 269, "column": 6}, "end": {"line": 269, "column": 48}}, "64": {"start": {"line": 271, "column": 6}, "end": {"line": 271, "column": 41}}, "65": {"start": {"line": 273, "column": 6}, "end": {"line": 273, "column": 37}}, "66": {"start": {"line": 275, "column": 6}, "end": {"line": 275, "column": 40}}, "67": {"start": {"line": 277, "column": 6}, "end": {"line": 277, "column": 41}}, "68": {"start": {"line": 279, "column": 6}, "end": {"line": 279, "column": 43}}, "69": {"start": {"line": 281, "column": 6}, "end": {"line": 281, "column": 46}}, "70": {"start": {"line": 283, "column": 6}, "end": {"line": 283, "column": 34}}, "71": {"start": {"line": 285, "column": 6}, "end": {"line": 285, "column": 42}}, "72": {"start": {"line": 287, "column": 6}, "end": {"line": 287, "column": 51}}, "73": {"start": {"line": 289, "column": 6}, "end": {"line": 289, "column": 49}}, "74": {"start": {"line": 291, "column": 6}, "end": {"line": 291, "column": 41}}, "75": {"start": {"line": 302, "column": 24}, "end": {"line": 302, "column": 26}}, "76": {"start": {"line": 303, "column": 28}, "end": {"line": 303, "column": 30}}, "77": {"start": {"line": 304, "column": 19}, "end": {"line": 304, "column": 29}}, "78": {"start": {"line": 306, "column": 2}, "end": {"line": 318, "column": 3}}, "79": {"start": {"line": 307, "column": 4}, "end": {"line": 317, "column": 5}}, "80": {"start": {"line": 308, "column": 6}, "end": {"line": 316, "column": 7}}, "81": {"start": {"line": 310, "column": 29}, "end": {"line": 310, "column": 75}}, "82": {"start": {"line": 310, "column": 45}, "end": {"line": 310, "column": 63}}, "83": {"start": {"line": 311, "column": 8}, "end": {"line": 311, "column": 52}}, "84": {"start": {"line": 312, "column": 8}, "end": {"line": 312, "column": 30}}, "85": {"start": {"line": 314, "column": 8}, "end": {"line": 314, "column": 50}}, "86": {"start": {"line": 315, "column": 8}, "end": {"line": 315, "column": 27}}, "87": {"start": {"line": 320, "column": 2}, "end": {"line": 324, "column": 4}}, "88": {"start": {"line": 331, "column": 2}, "end": {"line": 331, "column": 47}}}, "fnMap": {"0": {"name": "execute<PERSON>uery", "decl": {"start": {"line": 48, "column": 22}, "end": {"line": 48, "column": 34}}, "loc": {"start": {"line": 52, "column": 26}, "end": {"line": 108, "column": 1}}, "line": 52}, "1": {"name": "executeQuerySingle", "decl": {"start": {"line": 113, "column": 22}, "end": {"line": 113, "column": 40}}, "loc": {"start": {"line": 117, "column": 24}, "end": {"line": 129, "column": 1}}, "line": 117}, "2": {"name": "executeTransaction", "decl": {"start": {"line": 134, "column": 22}, "end": {"line": 134, "column": 40}}, "loc": {"start": {"line": 137, "column": 26}, "end": {"line": 193, "column": 1}}, "line": 137}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 163, "column": 31}, "end": {"line": 163, "column": 32}}, "loc": {"start": {"line": 163, "column": 46}, "end": {"line": 163, "column": 63}}, "line": 163}, "4": {"name": "schemaExists", "decl": {"start": {"line": 198, "column": 22}, "end": {"line": 198, "column": 34}}, "loc": {"start": {"line": 198, "column": 73}, "end": {"line": 205, "column": 1}}, "line": 198}, "5": {"name": "tableExists", "decl": {"start": {"line": 210, "column": 22}, "end": {"line": 210, "column": 33}}, "loc": {"start": {"line": 210, "column": 94}, "end": {"line": 217, "column": 1}}, "line": 210}, "6": {"name": "getConnectionHealth", "decl": {"start": {"line": 222, "column": 22}, "end": {"line": 222, "column": 41}}, "loc": {"start": {"line": 227, "column": 3}, "end": {"line": 246, "column": 1}}, "line": 227}, "7": {"name": "closeDatabase", "decl": {"start": {"line": 251, "column": 22}, "end": {"line": 251, "column": 35}}, "loc": {"start": {"line": 251, "column": 53}, "end": {"line": 259, "column": 1}}, "line": 251}, "8": {"name": "getDatabaseErrorMessage", "decl": {"start": {"line": 264, "column": 9}, "end": {"line": 264, "column": 32}}, "loc": {"start": {"line": 264, "column": 63}, "end": {"line": 293, "column": 1}}, "line": 264}, "9": {"name": "buildWhereClause", "decl": {"start": {"line": 298, "column": 16}, "end": {"line": 298, "column": 32}}, "loc": {"start": {"line": 301, "column": 56}, "end": {"line": 325, "column": 1}}, "line": 301}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 310, "column": 39}, "end": {"line": 310, "column": 40}}, "loc": {"start": {"line": 310, "column": 45}, "end": {"line": 310, "column": 63}}, "line": 310}, "11": {"name": "escapeIdentifier", "decl": {"start": {"line": 330, "column": 16}, "end": {"line": 330, "column": 32}}, "loc": {"start": {"line": 330, "column": 61}, "end": {"line": 332, "column": 1}}, "line": 330}}, "branchMap": {"0": {"loc": {"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 50, "column": 18}, "end": {"line": 50, "column": 20}}], "line": 50}, "1": {"loc": {"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": 28}}, "type": "default-arg", "locations": [{"start": {"line": 51, "column": 26}, "end": {"line": 51, "column": 28}}], "line": 51}, "2": {"loc": {"start": {"line": 60, "column": 4}, "end": {"line": 62, "column": 5}}, "type": "if", "locations": [{"start": {"line": 60, "column": 4}, "end": {"line": 62, "column": 5}}, {"start": {}, "end": {}}], "line": 60}, "3": {"loc": {"start": {"line": 65, "column": 4}, "end": {"line": 67, "column": 5}}, "type": "if", "locations": [{"start": {"line": 65, "column": 4}, "end": {"line": 67, "column": 5}}, {"start": {}, "end": {}}], "line": 65}, "4": {"loc": {"start": {"line": 74, "column": 4}, "end": {"line": 80, "column": 5}}, "type": "if", "locations": [{"start": {"line": 74, "column": 4}, "end": {"line": 80, "column": 5}}, {"start": {}, "end": {}}], "line": 74}, "5": {"loc": {"start": {"line": 85, "column": 16}, "end": {"line": 85, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 85, "column": 16}, "end": {"line": 85, "column": 31}}, {"start": {"line": 85, "column": 35}, "end": {"line": 85, "column": 36}}], "line": 85}, "6": {"loc": {"start": {"line": 101, "column": 17}, "end": {"line": 101, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 101, "column": 17}, "end": {"line": 101, "column": 29}}, {"start": {"line": 101, "column": 33}, "end": {"line": 101, "column": 48}}], "line": 101}, "7": {"loc": {"start": {"line": 104, "column": 4}, "end": {"line": 106, "column": 5}}, "type": "if", "locations": [{"start": {"line": 104, "column": 4}, "end": {"line": 106, "column": 5}}, {"start": {}, "end": {}}], "line": 104}, "8": {"loc": {"start": {"line": 115, "column": 2}, "end": {"line": 115, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 115, "column": 18}, "end": {"line": 115, "column": 20}}], "line": 115}, "9": {"loc": {"start": {"line": 116, "column": 2}, "end": {"line": 116, "column": 28}}, "type": "default-arg", "locations": [{"start": {"line": 116, "column": 26}, "end": {"line": 116, "column": 28}}], "line": 116}, "10": {"loc": {"start": {"line": 120, "column": 2}, "end": {"line": 122, "column": 3}}, "type": "if", "locations": [{"start": {"line": 120, "column": 2}, "end": {"line": 122, "column": 3}}, {"start": {}, "end": {}}], "line": 120}, "11": {"loc": {"start": {"line": 126, "column": 10}, "end": {"line": 126, "column": 34}}, "type": "binary-expr", "locations": [{"start": {"line": 126, "column": 10}, "end": {"line": 126, "column": 26}}, {"start": {"line": 126, "column": 30}, "end": {"line": 126, "column": 34}}], "line": 126}, "12": {"loc": {"start": {"line": 136, "column": 2}, "end": {"line": 136, "column": 28}}, "type": "default-arg", "locations": [{"start": {"line": 136, "column": 26}, "end": {"line": 136, "column": 28}}], "line": 136}, "13": {"loc": {"start": {"line": 145, "column": 4}, "end": {"line": 147, "column": 5}}, "type": "if", "locations": [{"start": {"line": 145, "column": 4}, "end": {"line": 147, "column": 5}}, {"start": {}, "end": {}}], "line": 145}, "14": {"loc": {"start": {"line": 153, "column": 24}, "end": {"line": 153, "column": 35}}, "type": "default-arg", "locations": [{"start": {"line": 153, "column": 33}, "end": {"line": 153, "column": 35}}], "line": 153}, "15": {"loc": {"start": {"line": 167, "column": 4}, "end": {"line": 173, "column": 5}}, "type": "if", "locations": [{"start": {"line": 167, "column": 4}, "end": {"line": 173, "column": 5}}, {"start": {}, "end": {}}], "line": 167}, "16": {"loc": {"start": {"line": 186, "column": 17}, "end": {"line": 186, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 186, "column": 17}, "end": {"line": 186, "column": 29}}, {"start": {"line": 186, "column": 33}, "end": {"line": 186, "column": 52}}], "line": 186}, "17": {"loc": {"start": {"line": 189, "column": 4}, "end": {"line": 191, "column": 5}}, "type": "if", "locations": [{"start": {"line": 189, "column": 4}, "end": {"line": 191, "column": 5}}, {"start": {}, "end": {}}], "line": 189}, "18": {"loc": {"start": {"line": 204, "column": 9}, "end": {"line": 204, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 204, "column": 9}, "end": {"line": 204, "column": 23}}, {"start": {"line": 204, "column": 27}, "end": {"line": 204, "column": 55}}], "line": 204}, "19": {"loc": {"start": {"line": 210, "column": 53}, "end": {"line": 210, "column": 74}}, "type": "default-arg", "locations": [{"start": {"line": 210, "column": 66}, "end": {"line": 210, "column": 74}}], "line": 210}, "20": {"loc": {"start": {"line": 216, "column": 9}, "end": {"line": 216, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 216, "column": 9}, "end": {"line": 216, "column": 23}}, {"start": {"line": 216, "column": 27}, "end": {"line": 216, "column": 55}}], "line": 216}, "21": {"loc": {"start": {"line": 265, "column": 2}, "end": {"line": 292, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 266, "column": 4}, "end": {"line": 267, "column": 61}}, {"start": {"line": 268, "column": 4}, "end": {"line": 269, "column": 48}}, {"start": {"line": 270, "column": 4}, "end": {"line": 271, "column": 41}}, {"start": {"line": 272, "column": 4}, "end": {"line": 273, "column": 37}}, {"start": {"line": 274, "column": 4}, "end": {"line": 275, "column": 40}}, {"start": {"line": 276, "column": 4}, "end": {"line": 277, "column": 41}}, {"start": {"line": 278, "column": 4}, "end": {"line": 279, "column": 43}}, {"start": {"line": 280, "column": 4}, "end": {"line": 281, "column": 46}}, {"start": {"line": 282, "column": 4}, "end": {"line": 283, "column": 34}}, {"start": {"line": 284, "column": 4}, "end": {"line": 285, "column": 42}}, {"start": {"line": 286, "column": 4}, "end": {"line": 287, "column": 51}}, {"start": {"line": 288, "column": 4}, "end": {"line": 289, "column": 49}}, {"start": {"line": 290, "column": 4}, "end": {"line": 291, "column": 41}}], "line": 265}, "22": {"loc": {"start": {"line": 300, "column": 2}, "end": {"line": 300, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 300, "column": 15}, "end": {"line": 300, "column": 16}}], "line": 300}, "23": {"loc": {"start": {"line": 307, "column": 4}, "end": {"line": 317, "column": 5}}, "type": "if", "locations": [{"start": {"line": 307, "column": 4}, "end": {"line": 317, "column": 5}}, {"start": {}, "end": {}}], "line": 307}, "24": {"loc": {"start": {"line": 307, "column": 8}, "end": {"line": 307, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 307, "column": 8}, "end": {"line": 307, "column": 27}}, {"start": {"line": 307, "column": 31}, "end": {"line": 307, "column": 45}}], "line": 307}, "25": {"loc": {"start": {"line": 308, "column": 6}, "end": {"line": 316, "column": 7}}, "type": "if", "locations": [{"start": {"line": 308, "column": 6}, "end": {"line": 316, "column": 7}}, {"start": {"line": 313, "column": 13}, "end": {"line": 316, "column": 7}}], "line": 308}, "26": {"loc": {"start": {"line": 321, "column": 12}, "end": {"line": 321, "column": 70}}, "type": "cond-expr", "locations": [{"start": {"line": 321, "column": 33}, "end": {"line": 321, "column": 65}}, {"start": {"line": 321, "column": 68}, "end": {"line": 321, "column": 70}}], "line": 321}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0], "9": [0], "10": [0, 0], "11": [0, 0], "12": [0], "13": [0, 0], "14": [0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0], "20": [0, 0], "21": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "22": [0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\db-config.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\db-config.ts", "statementMap": {"0": {"start": {"line": 6, "column": 24}, "end": {"line": 6, "column": 28}}, "1": {"start": {"line": 9, "column": 22}, "end": {"line": 35, "column": 1}}, "2": {"start": {"line": 10, "column": 33}, "end": {"line": 32, "column": 3}}, "3": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": 20}}, "4": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": 24}}, "5": {"start": {"line": 38, "column": 12}, "end": {"line": 38, "column": 24}}, "6": {"start": {"line": 40, "column": 19}, "end": {"line": 40, "column": 38}}, "7": {"start": {"line": 43, "column": 2}, "end": {"line": 68, "column": 3}}, "8": {"start": {"line": 44, "column": 4}, "end": {"line": 49, "column": 7}}, "9": {"start": {"line": 51, "column": 23}, "end": {"line": 51, "column": 46}}, "10": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 32}}, "11": {"start": {"line": 55, "column": 4}, "end": {"line": 57, "column": 7}}, "12": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 60}}, "13": {"start": {"line": 59, "column": 4}, "end": {"line": 61, "column": 7}}, "14": {"start": {"line": 60, "column": 6}, "end": {"line": 60, "column": 54}}, "15": {"start": {"line": 63, "column": 4}, "end": {"line": 65, "column": 7}}, "16": {"start": {"line": 64, "column": 6}, "end": {"line": 64, "column": 46}}, "17": {"start": {"line": 67, "column": 4}, "end": {"line": 67, "column": 16}}, "18": {"start": {"line": 71, "column": 2}, "end": {"line": 105, "column": 3}}, "19": {"start": {"line": 72, "column": 19}, "end": {"line": 72, "column": 130}}, "20": {"start": {"line": 73, "column": 19}, "end": {"line": 73, "column": 67}}, "21": {"start": {"line": 74, "column": 19}, "end": {"line": 74, "column": 48}}, "22": {"start": {"line": 77, "column": 19}, "end": {"line": 82, "column": 6}}, "23": {"start": {"line": 85, "column": 18}, "end": {"line": 85, "column": 45}}, "24": {"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 5}}, "25": {"start": {"line": 88, "column": 6}, "end": {"line": 88, "column": 57}}, "26": {"start": {"line": 92, "column": 4}, "end": {"line": 99, "column": 7}}, "27": {"start": {"line": 101, "column": 4}, "end": {"line": 101, "column": 16}}, "28": {"start": {"line": 103, "column": 4}, "end": {"line": 103, "column": 68}}, "29": {"start": {"line": 104, "column": 4}, "end": {"line": 104, "column": 16}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 23}}, "loc": {"start": {"line": 9, "column": 86}, "end": {"line": 35, "column": 1}}, "line": 9}, "1": {"name": "getDbPool", "decl": {"start": {"line": 37, "column": 22}, "end": {"line": 37, "column": 31}}, "loc": {"start": {"line": 37, "column": 49}, "end": {"line": 106, "column": 1}}, "line": 37}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 55, "column": 21}, "end": {"line": 55, "column": 22}}, "loc": {"start": {"line": 55, "column": 30}, "end": {"line": 57, "column": 5}}, "line": 55}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 59, "column": 23}, "end": {"line": 59, "column": 24}}, "loc": {"start": {"line": 59, "column": 29}, "end": {"line": 61, "column": 5}}, "line": 59}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 63, "column": 22}, "end": {"line": 63, "column": 23}}, "loc": {"start": {"line": 63, "column": 28}, "end": {"line": 65, "column": 5}}, "line": 63}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 10}, "end": {"line": 13, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 13, "column": 10}, "end": {"line": 13, "column": 23}}, {"start": {"line": 13, "column": 27}, "end": {"line": 13, "column": 38}}], "line": 13}, "1": {"loc": {"start": {"line": 16, "column": 9}, "end": {"line": 16, "column": 61}}, "type": "cond-expr", "locations": [{"start": {"line": 16, "column": 24}, "end": {"line": 16, "column": 53}}, {"start": {"line": 16, "column": 56}, "end": {"line": 16, "column": 61}}], "line": 16}, "2": {"loc": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": 24}}, "type": "if", "locations": [{"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": 24}}, {"start": {}, "end": {}}], "line": 38}, "3": {"loc": {"start": {"line": 43, "column": 2}, "end": {"line": 68, "column": 3}}, "type": "if", "locations": [{"start": {"line": 43, "column": 2}, "end": {"line": 68, "column": 3}}, {"start": {}, "end": {}}], "line": 43}, "4": {"loc": {"start": {"line": 46, "column": 12}, "end": {"line": 46, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 46, "column": 12}, "end": {"line": 46, "column": 25}}, {"start": {"line": 46, "column": 29}, "end": {"line": 46, "column": 40}}], "line": 46}, "5": {"loc": {"start": {"line": 72, "column": 19}, "end": {"line": 72, "column": 130}}, "type": "binary-expr", "locations": [{"start": {"line": 72, "column": 19}, "end": {"line": 72, "column": 32}}, {"start": {"line": 72, "column": 36}, "end": {"line": 72, "column": 48}}, {"start": {"line": 72, "column": 52}, "end": {"line": 72, "column": 130}}], "line": 72}, "6": {"loc": {"start": {"line": 73, "column": 19}, "end": {"line": 73, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 73, "column": 19}, "end": {"line": 73, "column": 42}}, {"start": {"line": 73, "column": 46}, "end": {"line": 73, "column": 67}}], "line": 73}, "7": {"loc": {"start": {"line": 74, "column": 19}, "end": {"line": 74, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 74, "column": 19}, "end": {"line": 74, "column": 32}}, {"start": {"line": 74, "column": 36}, "end": {"line": 74, "column": 48}}], "line": 74}, "8": {"loc": {"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 5}}, "type": "if", "locations": [{"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 5}}, {"start": {}, "end": {}}], "line": 87}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\direct-auth-config.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\direct-auth-config.ts", "statementMap": {"0": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": 39}}, "1": {"start": {"line": 5, "column": 32}, "end": {"line": 5, "column": 39}}, "2": {"start": {"line": 8, "column": 17}, "end": {"line": 14, "column": 3}}, "3": {"start": {"line": 16, "column": 2}, "end": {"line": 39, "column": 3}}, "4": {"start": {"line": 17, "column": 4}, "end": {"line": 35, "column": 7}}, "5": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 70}}, "6": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 64}}}, "fnMap": {"0": {"name": "configureAmplifyDirectly", "decl": {"start": {"line": 3, "column": 16}, "end": {"line": 3, "column": 40}}, "loc": {"start": {"line": 3, "column": 43}, "end": {"line": 40, "column": 1}}, "line": 3}}, "branchMap": {"0": {"loc": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": 39}}, "type": "if", "locations": [{"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": 39}}, {"start": {}, "end": {}}], "line": 5}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {"0": 0}, "b": {"0": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\jwt-validator.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\jwt-validator.ts", "statementMap": {"0": {"start": {"line": 30, "column": 62}, "end": {"line": 30, "column": 66}}, "1": {"start": {"line": 36, "column": 33}, "end": {"line": 36, "column": 49}}, "2": {"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": 91}}, "3": {"start": {"line": 44, "column": 2}, "end": {"line": 47, "column": 3}}, "4": {"start": {"line": 45, "column": 20}, "end": {"line": 45, "column": 32}}, "5": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 53}}, "6": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 19}}, "7": {"start": {"line": 55, "column": 2}, "end": {"line": 93, "column": 3}}, "8": {"start": {"line": 56, "column": 17}, "end": {"line": 56, "column": 26}}, "9": {"start": {"line": 57, "column": 53}, "end": {"line": 57, "column": 69}}, "10": {"start": {"line": 60, "column": 27}, "end": {"line": 60, "column": 86}}, "11": {"start": {"line": 63, "column": 24}, "end": {"line": 66, "column": 6}}, "12": {"start": {"line": 69, "column": 27}, "end": {"line": 69, "column": 55}}, "13": {"start": {"line": 72, "column": 4}, "end": {"line": 75, "column": 5}}, "14": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 68}}, "15": {"start": {"line": 74, "column": 6}, "end": {"line": 74, "column": 18}}, "16": {"start": {"line": 78, "column": 4}, "end": {"line": 81, "column": 5}}, "17": {"start": {"line": 79, "column": 6}, "end": {"line": 79, "column": 48}}, "18": {"start": {"line": 80, "column": 6}, "end": {"line": 80, "column": 18}}, "19": {"start": {"line": 84, "column": 4}, "end": {"line": 87, "column": 5}}, "20": {"start": {"line": 85, "column": 6}, "end": {"line": 85, "column": 73}}, "21": {"start": {"line": 89, "column": 4}, "end": {"line": 89, "column": 26}}, "22": {"start": {"line": 91, "column": 4}, "end": {"line": 91, "column": 51}}, "23": {"start": {"line": 92, "column": 4}, "end": {"line": 92, "column": 16}}, "24": {"start": {"line": 100, "column": 2}, "end": {"line": 102, "column": 3}}, "25": {"start": {"line": 101, "column": 4}, "end": {"line": 101, "column": 16}}, "26": {"start": {"line": 105, "column": 16}, "end": {"line": 105, "column": 34}}, "27": {"start": {"line": 108, "column": 2}, "end": {"line": 111, "column": 3}}, "28": {"start": {"line": 109, "column": 4}, "end": {"line": 109, "column": 49}}, "29": {"start": {"line": 110, "column": 4}, "end": {"line": 110, "column": 16}}, "30": {"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 33}}, "31": {"start": {"line": 121, "column": 2}, "end": {"line": 123, "column": 3}}, "32": {"start": {"line": 122, "column": 4}, "end": {"line": 122, "column": 71}}, "33": {"start": {"line": 125, "column": 2}, "end": {"line": 144, "column": 3}}, "34": {"start": {"line": 126, "column": 18}, "end": {"line": 126, "column": 34}}, "35": {"start": {"line": 127, "column": 4}, "end": {"line": 129, "column": 5}}, "36": {"start": {"line": 128, "column": 6}, "end": {"line": 128, "column": 18}}, "37": {"start": {"line": 131, "column": 20}, "end": {"line": 131, "column": 74}}, "38": {"start": {"line": 134, "column": 24}, "end": {"line": 134, "column": 53}}, "39": {"start": {"line": 135, "column": 4}, "end": {"line": 138, "column": 5}}, "40": {"start": {"line": 136, "column": 6}, "end": {"line": 136, "column": 40}}, "41": {"start": {"line": 137, "column": 6}, "end": {"line": 137, "column": 18}}, "42": {"start": {"line": 140, "column": 4}, "end": {"line": 140, "column": 40}}, "43": {"start": {"line": 142, "column": 4}, "end": {"line": 142, "column": 49}}, "44": {"start": {"line": 143, "column": 4}, "end": {"line": 143, "column": 16}}, "45": {"start": {"line": 151, "column": 22}, "end": {"line": 151, "column": 51}}, "46": {"start": {"line": 152, "column": 2}, "end": {"line": 152, "column": 35}}, "47": {"start": {"line": 159, "column": 2}, "end": {"line": 159, "column": 28}}, "48": {"start": {"line": 166, "column": 25}, "end": {"line": 166, "column": 56}}, "49": {"start": {"line": 167, "column": 22}, "end": {"line": 167, "column": 56}}, "50": {"start": {"line": 168, "column": 2}, "end": {"line": 168, "column": 39}}}, "fnMap": {"0": {"name": "getJwksUrl", "decl": {"start": {"line": 35, "column": 9}, "end": {"line": 35, "column": 19}}, "loc": {"start": {"line": 35, "column": 30}, "end": {"line": 38, "column": 1}}, "line": 35}, "1": {"name": "getJwks", "decl": {"start": {"line": 43, "column": 9}, "end": {"line": 43, "column": 16}}, "loc": {"start": {"line": 43, "column": 19}, "end": {"line": 49, "column": 1}}, "line": 43}, "2": {"name": "validateJwtToken", "decl": {"start": {"line": 54, "column": 22}, "end": {"line": 54, "column": 38}}, "loc": {"start": {"line": 54, "column": 89}, "end": {"line": 94, "column": 1}}, "line": 54}, "3": {"name": "validate<PERSON><PERSON><PERSON><PERSON><PERSON>", "decl": {"start": {"line": 99, "column": 22}, "end": {"line": 99, "column": 40}}, "loc": {"start": {"line": 99, "column": 97}, "end": {"line": 114, "column": 1}}, "line": 99}, "4": {"name": "parseJwtUnsafe", "decl": {"start": {"line": 120, "column": 16}, "end": {"line": 120, "column": 30}}, "loc": {"start": {"line": 120, "column": 72}, "end": {"line": 145, "column": 1}}, "line": 120}, "5": {"name": "isTokenExpired", "decl": {"start": {"line": 150, "column": 16}, "end": {"line": 150, "column": 30}}, "loc": {"start": {"line": 150, "column": 68}, "end": {"line": 153, "column": 1}}, "line": 150}, "6": {"name": "getTokenExpirationTime", "decl": {"start": {"line": 158, "column": 16}, "end": {"line": 158, "column": 38}}, "loc": {"start": {"line": 158, "column": 75}, "end": {"line": 160, "column": 1}}, "line": 158}, "7": {"name": "isTokenExpiringWithin", "decl": {"start": {"line": 165, "column": 16}, "end": {"line": 165, "column": 37}}, "loc": {"start": {"line": 165, "column": 92}, "end": {"line": 169, "column": 1}}, "line": 165}}, "branchMap": {"0": {"loc": {"start": {"line": 44, "column": 2}, "end": {"line": 47, "column": 3}}, "type": "if", "locations": [{"start": {"line": 44, "column": 2}, "end": {"line": 47, "column": 3}}, {"start": {}, "end": {}}], "line": 44}, "1": {"loc": {"start": {"line": 72, "column": 4}, "end": {"line": 75, "column": 5}}, "type": "if", "locations": [{"start": {"line": 72, "column": 4}, "end": {"line": 75, "column": 5}}, {"start": {}, "end": {}}], "line": 72}, "2": {"loc": {"start": {"line": 78, "column": 4}, "end": {"line": 81, "column": 5}}, "type": "if", "locations": [{"start": {"line": 78, "column": 4}, "end": {"line": 81, "column": 5}}, {"start": {}, "end": {}}], "line": 78}, "3": {"loc": {"start": {"line": 84, "column": 4}, "end": {"line": 87, "column": 5}}, "type": "if", "locations": [{"start": {"line": 84, "column": 4}, "end": {"line": 87, "column": 5}}, {"start": {}, "end": {}}], "line": 84}, "4": {"loc": {"start": {"line": 100, "column": 2}, "end": {"line": 102, "column": 3}}, "type": "if", "locations": [{"start": {"line": 100, "column": 2}, "end": {"line": 102, "column": 3}}, {"start": {}, "end": {}}], "line": 100}, "5": {"loc": {"start": {"line": 108, "column": 2}, "end": {"line": 111, "column": 3}}, "type": "if", "locations": [{"start": {"line": 108, "column": 2}, "end": {"line": 111, "column": 3}}, {"start": {}, "end": {}}], "line": 108}, "6": {"loc": {"start": {"line": 108, "column": 6}, "end": {"line": 108, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 108, "column": 6}, "end": {"line": 108, "column": 26}}, {"start": {"line": 108, "column": 30}, "end": {"line": 108, "column": 59}}], "line": 108}, "7": {"loc": {"start": {"line": 121, "column": 2}, "end": {"line": 123, "column": 3}}, "type": "if", "locations": [{"start": {"line": 121, "column": 2}, "end": {"line": 123, "column": 3}}, {"start": {}, "end": {}}], "line": 121}, "8": {"loc": {"start": {"line": 127, "column": 4}, "end": {"line": 129, "column": 5}}, "type": "if", "locations": [{"start": {"line": 127, "column": 4}, "end": {"line": 129, "column": 5}}, {"start": {}, "end": {}}], "line": 127}, "9": {"loc": {"start": {"line": 135, "column": 4}, "end": {"line": 138, "column": 5}}, "type": "if", "locations": [{"start": {"line": 135, "column": 4}, "end": {"line": 138, "column": 5}}, {"start": {}, "end": {}}], "line": 135}, "10": {"loc": {"start": {"line": 135, "column": 8}, "end": {"line": 135, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 135, "column": 8}, "end": {"line": 135, "column": 19}}, {"start": {"line": 135, "column": 23}, "end": {"line": 135, "column": 48}}], "line": 135}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\performance.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\performance.ts", "statementMap": {"0": {"start": {"line": 12, "column": 46}, "end": {"line": 12, "column": 64}}, "1": {"start": {"line": 14, "column": 2}, "end": {"line": 22, "column": 20}}, "2": {"start": {"line": 15, "column": 20}, "end": {"line": 17, "column": 13}}, "3": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 30}}, "4": {"start": {"line": 19, "column": 4}, "end": {"line": 21, "column": 5}}, "5": {"start": {"line": 20, "column": 6}, "end": {"line": 20, "column": 27}}, "6": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 23}}, "7": {"start": {"line": 32, "column": 18}, "end": {"line": 32, "column": 36}}, "8": {"start": {"line": 34, "column": 2}, "end": {"line": 42, "column": 3}}, "9": {"start": {"line": 36, "column": 6}, "end": {"line": 39, "column": 7}}, "10": {"start": {"line": 37, "column": 8}, "end": {"line": 37, "column": 25}}, "11": {"start": {"line": 38, "column": 8}, "end": {"line": 38, "column": 36}}, "12": {"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": 36}}, "13": {"start": {"line": 55, "column": 14}, "end": {"line": 55, "column": 64}}, "14": {"start": {"line": 57, "column": 2}, "end": {"line": 59, "column": 3}}, "15": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 44}}, "16": {"start": {"line": 61, "column": 2}, "end": {"line": 61, "column": 26}}, "17": {"start": {"line": 66, "column": 2}, "end": {"line": 66, "column": 41}}, "18": {"start": {"line": 66, "column": 29}, "end": {"line": 66, "column": 41}}, "19": {"start": {"line": 68, "column": 2}, "end": {"line": 72, "column": 3}}, "20": {"start": {"line": 68, "column": 15}, "end": {"line": 68, "column": 16}}, "21": {"start": {"line": 69, "column": 4}, "end": {"line": 71, "column": 5}}, "22": {"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": 18}}, "23": {"start": {"line": 74, "column": 2}, "end": {"line": 74, "column": 13}}, "24": {"start": {"line": 79, "column": 20}, "end": {"line": 79, "column": 36}}, "25": {"start": {"line": 80, "column": 22}, "end": {"line": 80, "column": 31}}, "26": {"start": {"line": 82, "column": 2}, "end": {"line": 99, "column": 4}}, "27": {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 28}}, "28": {"start": {"line": 85, "column": 4}, "end": {"line": 98, "column": 5}}, "29": {"start": {"line": 86, "column": 6}, "end": {"line": 88, "column": 7}}, "30": {"start": {"line": 87, "column": 8}, "end": {"line": 87, "column": 45}}, "31": {"start": {"line": 90, "column": 22}, "end": {"line": 90, "column": 39}}, "32": {"start": {"line": 91, "column": 23}, "end": {"line": 91, "column": 50}}, "33": {"start": {"line": 93, "column": 6}, "end": {"line": 95, "column": 7}}, "34": {"start": {"line": 94, "column": 8}, "end": {"line": 94, "column": 114}}, "35": {"start": {"line": 97, "column": 6}, "end": {"line": 97, "column": 33}}, "36": {"start": {"line": 101, "column": 2}, "end": {"line": 101, "column": 45}}, "37": {"start": {"line": 108, "column": 46}, "end": {"line": 108, "column": 61}}, "38": {"start": {"line": 109, "column": 14}, "end": {"line": 109, "column": 39}}, "39": {"start": {"line": 111, "column": 2}, "end": {"line": 130, "column": 15}}, "40": {"start": {"line": 112, "column": 20}, "end": {"line": 112, "column": 31}}, "41": {"start": {"line": 113, "column": 4}, "end": {"line": 113, "column": 24}}, "42": {"start": {"line": 113, "column": 18}, "end": {"line": 113, "column": 24}}, "43": {"start": {"line": 115, "column": 21}, "end": {"line": 123, "column": 5}}, "44": {"start": {"line": 117, "column": 8}, "end": {"line": 117, "column": 47}}, "45": {"start": {"line": 125, "column": 4}, "end": {"line": 125, "column": 29}}, "46": {"start": {"line": 127, "column": 4}, "end": {"line": 129, "column": 5}}, "47": {"start": {"line": 128, "column": 6}, "end": {"line": 128, "column": 33}}, "48": {"start": {"line": 132, "column": 2}, "end": {"line": 132, "column": 30}}, "49": {"start": {"line": 141, "column": 36}, "end": {"line": 141, "column": 47}}, "50": {"start": {"line": 143, "column": 23}, "end": {"line": 157, "column": 53}}, "51": {"start": {"line": 144, "column": 23}, "end": {"line": 144, "column": 57}}, "52": {"start": {"line": 145, "column": 21}, "end": {"line": 148, "column": 5}}, "53": {"start": {"line": 150, "column": 4}, "end": {"line": 156, "column": 5}}, "54": {"start": {"line": 159, "column": 23}, "end": {"line": 161, "column": 8}}, "55": {"start": {"line": 160, "column": 4}, "end": {"line": 160, "column": 43}}, "56": {"start": {"line": 163, "column": 2}, "end": {"line": 166, "column": 3}}, "57": {"start": {"line": 173, "column": 28}, "end": {"line": 173, "column": 50}}, "58": {"start": {"line": 174, "column": 25}, "end": {"line": 174, "column": 58}}, "59": {"start": {"line": 175, "column": 21}, "end": {"line": 175, "column": 45}}, "60": {"start": {"line": 177, "column": 26}, "end": {"line": 194, "column": 8}}, "61": {"start": {"line": 178, "column": 4}, "end": {"line": 178, "column": 40}}, "62": {"start": {"line": 180, "column": 4}, "end": {"line": 182, "column": 5}}, "63": {"start": {"line": 181, "column": 6}, "end": {"line": 181, "column": 38}}, "64": {"start": {"line": 184, "column": 4}, "end": {"line": 193, "column": 9}}, "65": {"start": {"line": 185, "column": 6}, "end": {"line": 192, "column": 8}}, "66": {"start": {"line": 186, "column": 23}, "end": {"line": 186, "column": 32}}, "67": {"start": {"line": 187, "column": 8}, "end": {"line": 189, "column": 9}}, "68": {"start": {"line": 188, "column": 10}, "end": {"line": 188, "column": 37}}, "69": {"start": {"line": 190, "column": 8}, "end": {"line": 190, "column": 35}}, "70": {"start": {"line": 191, "column": 8}, "end": {"line": 191, "column": 23}}, "71": {"start": {"line": 196, "column": 2}, "end": {"line": 202, "column": 8}}, "72": {"start": {"line": 197, "column": 4}, "end": {"line": 201, "column": 5}}, "73": {"start": {"line": 198, "column": 6}, "end": {"line": 200, "column": 7}}, "74": {"start": {"line": 199, "column": 8}, "end": {"line": 199, "column": 40}}, "75": {"start": {"line": 204, "column": 2}, "end": {"line": 204, "column": 33}}, "76": {"start": {"line": 209, "column": 2}, "end": {"line": 224, "column": 21}}, "77": {"start": {"line": 210, "column": 4}, "end": {"line": 223, "column": 5}}, "78": {"start": {"line": 211, "column": 26}, "end": {"line": 219, "column": 7}}, "79": {"start": {"line": 212, "column": 23}, "end": {"line": 212, "column": 50}}, "80": {"start": {"line": 213, "column": 8}, "end": {"line": 218, "column": 9}}, "81": {"start": {"line": 214, "column": 10}, "end": {"line": 217, "column": 12}}, "82": {"start": {"line": 221, "column": 23}, "end": {"line": 221, "column": 53}}, "83": {"start": {"line": 222, "column": 6}, "end": {"line": 222, "column": 42}}, "84": {"start": {"line": 222, "column": 19}, "end": {"line": 222, "column": 42}}, "85": {"start": {"line": 232, "column": 2}, "end": {"line": 242, "column": 3}}, "86": {"start": {"line": 234, "column": 6}, "end": {"line": 239, "column": 7}}, "87": {"start": {"line": 236, "column": 8}, "end": {"line": 236, "column": 22}}, "88": {"start": {"line": 238, "column": 8}, "end": {"line": 238, "column": 22}}, "89": {"start": {"line": 247, "column": 24}, "end": {"line": 247, "column": 53}}, "90": {"start": {"line": 248, "column": 22}, "end": {"line": 248, "column": 31}}, "91": {"start": {"line": 250, "column": 2}, "end": {"line": 268, "column": 4}}, "92": {"start": {"line": 251, "column": 4}, "end": {"line": 251, "column": 28}}, "93": {"start": {"line": 253, "column": 4}, "end": {"line": 267, "column": 5}}, "94": {"start": {"line": 254, "column": 6}, "end": {"line": 264, "column": 7}}, "95": {"start": {"line": 255, "column": 29}, "end": {"line": 257, "column": 9}}, "96": {"start": {"line": 256, "column": 17}, "end": {"line": 256, "column": 68}}, "97": {"start": {"line": 259, "column": 8}, "end": {"line": 263, "column": 9}}, "98": {"start": {"line": 260, "column": 10}, "end": {"line": 260, "column": 100}}, "99": {"start": {"line": 261, "column": 15}, "end": {"line": 263, "column": 9}}, "100": {"start": {"line": 262, "column": 10}, "end": {"line": 262, "column": 75}}, "101": {"start": {"line": 266, "column": 6}, "end": {"line": 266, "column": 42}}, "102": {"start": {"line": 270, "column": 2}, "end": {"line": 270, "column": 28}}, "103": {"start": {"line": 276, "column": 32}, "end": {"line": 308, "column": 1}}, "104": {"start": {"line": 279, "column": 4}, "end": {"line": 281, "column": 5}}, "105": {"start": {"line": 280, "column": 6}, "end": {"line": 280, "column": 39}}, "106": {"start": {"line": 286, "column": 4}, "end": {"line": 297, "column": 5}}, "107": {"start": {"line": 287, "column": 6}, "end": {"line": 287, "column": 37}}, "108": {"start": {"line": 288, "column": 6}, "end": {"line": 288, "column": 63}}, "109": {"start": {"line": 290, "column": 22}, "end": {"line": 290, "column": 56}}, "110": {"start": {"line": 291, "column": 6}, "end": {"line": 296, "column": 7}}, "111": {"start": {"line": 292, "column": 25}, "end": {"line": 292, "column": 61}}, "112": {"start": {"line": 293, "column": 8}, "end": {"line": 295, "column": 9}}, "113": {"start": {"line": 294, "column": 10}, "end": {"line": 294, "column": 75}}, "114": {"start": {"line": 302, "column": 4}, "end": {"line": 306, "column": 5}}, "115": {"start": {"line": 303, "column": 6}, "end": {"line": 303, "column": 47}}, "116": {"start": {"line": 304, "column": 6}, "end": {"line": 304, "column": 45}}, "117": {"start": {"line": 305, "column": 6}, "end": {"line": 305, "column": 39}}}, "fnMap": {"0": {"name": "useDebounce", "decl": {"start": {"line": 11, "column": 16}, "end": {"line": 11, "column": 27}}, "loc": {"start": {"line": 11, "column": 59}, "end": {"line": 25, "column": 1}}, "line": 11}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": 13}}, "loc": {"start": {"line": 14, "column": 18}, "end": {"line": 22, "column": 3}}, "line": 14}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 15, "column": 31}, "end": {"line": 15, "column": 32}}, "loc": {"start": {"line": 15, "column": 37}, "end": {"line": 17, "column": 5}}, "line": 15}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 19, "column": 11}, "end": {"line": 19, "column": 12}}, "loc": {"start": {"line": 19, "column": 17}, "end": {"line": 21, "column": 5}}, "line": 19}, "4": {"name": "useThrottle", "decl": {"start": {"line": 28, "column": 16}, "end": {"line": 28, "column": 27}}, "loc": {"start": {"line": 31, "column": 5}, "end": {"line": 43, "column": 1}}, "line": 31}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 35, "column": 5}, "end": {"line": 35, "column": 6}}, "loc": {"start": {"line": 35, "column": 18}, "end": {"line": 40, "column": 5}}, "line": 35}, "6": {"name": "useOptimizedCallback", "decl": {"start": {"line": 46, "column": 16}, "end": {"line": 46, "column": 36}}, "loc": {"start": {"line": 49, "column": 5}, "end": {"line": 51, "column": 1}}, "line": 49}, "7": {"name": "useDeepMemo", "decl": {"start": {"line": 54, "column": 16}, "end": {"line": 54, "column": 27}}, "loc": {"start": {"line": 54, "column": 80}, "end": {"line": 62, "column": 1}}, "line": 54}, "8": {"name": "areEqual", "decl": {"start": {"line": 65, "column": 9}, "end": {"line": 65, "column": 17}}, "loc": {"start": {"line": 65, "column": 77}, "end": {"line": 75, "column": 1}}, "line": 65}, "9": {"name": "usePerformanceMonitor", "decl": {"start": {"line": 78, "column": 16}, "end": {"line": 78, "column": 37}}, "loc": {"start": {"line": 78, "column": 52}, "end": {"line": 102, "column": 1}}, "line": 78}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 82, "column": 12}, "end": {"line": 82, "column": 13}}, "loc": {"start": {"line": 82, "column": 18}, "end": {"line": 99, "column": 3}}, "line": 82}, "11": {"name": "useIntersectionObserver", "decl": {"start": {"line": 105, "column": 16}, "end": {"line": 105, "column": 39}}, "loc": {"start": {"line": 107, "column": 43}, "end": {"line": 133, "column": 1}}, "line": 107}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 111, "column": 12}, "end": {"line": 111, "column": 13}}, "loc": {"start": {"line": 111, "column": 18}, "end": {"line": 130, "column": 3}}, "line": 111}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 116, "column": 6}, "end": {"line": 116, "column": 7}}, "loc": {"start": {"line": 116, "column": 19}, "end": {"line": 118, "column": 7}}, "line": 116}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 127, "column": 11}, "end": {"line": 127, "column": 12}}, "loc": {"start": {"line": 127, "column": 17}, "end": {"line": 129, "column": 5}}, "line": 127}, "15": {"name": "useVirtualScrolling", "decl": {"start": {"line": 136, "column": 16}, "end": {"line": 136, "column": 35}}, "loc": {"start": {"line": 140, "column": 2}, "end": {"line": 167, "column": 1}}, "line": 140}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 143, "column": 31}, "end": {"line": 143, "column": 32}}, "loc": {"start": {"line": 143, "column": 37}, "end": {"line": 157, "column": 3}}, "line": 143}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 159, "column": 35}, "end": {"line": 159, "column": 36}}, "loc": {"start": {"line": 159, "column": 70}, "end": {"line": 161, "column": 3}}, "line": 159}, "18": {"name": "useBatchedState", "decl": {"start": {"line": 170, "column": 16}, "end": {"line": 170, "column": 31}}, "loc": {"start": {"line": 172, "column": 42}, "end": {"line": 205, "column": 1}}, "line": 172}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 177, "column": 38}, "end": {"line": 177, "column": 39}}, "loc": {"start": {"line": 177, "column": 67}, "end": {"line": 194, "column": 3}}, "line": 177}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 184, "column": 36}, "end": {"line": 184, "column": 37}}, "loc": {"start": {"line": 184, "column": 42}, "end": {"line": 193, "column": 5}}, "line": 184}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 185, "column": 15}, "end": {"line": 185, "column": 16}}, "loc": {"start": {"line": 185, "column": 30}, "end": {"line": 192, "column": 7}}, "line": 185}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 196, "column": 12}, "end": {"line": 196, "column": 13}}, "loc": {"start": {"line": 196, "column": 18}, "end": {"line": 202, "column": 3}}, "line": 196}, "23": {"name": "(anonymous_23)", "decl": {"start": {"line": 197, "column": 11}, "end": {"line": 197, "column": 12}}, "loc": {"start": {"line": 197, "column": 17}, "end": {"line": 201, "column": 5}}, "line": 197}, "24": {"name": "useMemoryMonitor", "decl": {"start": {"line": 208, "column": 16}, "end": {"line": 208, "column": 32}}, "loc": {"start": {"line": 208, "column": 56}, "end": {"line": 225, "column": 1}}, "line": 208}, "25": {"name": "(anonymous_25)", "decl": {"start": {"line": 209, "column": 12}, "end": {"line": 209, "column": 13}}, "loc": {"start": {"line": 209, "column": 18}, "end": {"line": 224, "column": 3}}, "line": 209}, "26": {"name": "(anonymous_26)", "decl": {"start": {"line": 211, "column": 26}, "end": {"line": 211, "column": 27}}, "loc": {"start": {"line": 211, "column": 32}, "end": {"line": 219, "column": 7}}, "line": 211}, "27": {"name": "(anonymous_27)", "decl": {"start": {"line": 222, "column": 13}, "end": {"line": 222, "column": 14}}, "loc": {"start": {"line": 222, "column": 19}, "end": {"line": 222, "column": 42}}, "line": 222}, "28": {"name": "createOptimizedEventHandler", "decl": {"start": {"line": 228, "column": 16}, "end": {"line": 228, "column": 43}}, "loc": {"start": {"line": 231, "column": 2}, "end": {"line": 243, "column": 1}}, "line": 231}, "29": {"name": "(anonymous_29)", "decl": {"start": {"line": 233, "column": 4}, "end": {"line": 233, "column": 5}}, "loc": {"start": {"line": 233, "column": 18}, "end": {"line": 240, "column": 5}}, "line": 233}, "30": {"name": "useRenderOptimization", "decl": {"start": {"line": 246, "column": 16}, "end": {"line": 246, "column": 37}}, "loc": {"start": {"line": 246, "column": 89}, "end": {"line": 271, "column": 1}}, "line": 246}, "31": {"name": "(anonymous_31)", "decl": {"start": {"line": 250, "column": 12}, "end": {"line": 250, "column": 13}}, "loc": {"start": {"line": 250, "column": 18}, "end": {"line": 268, "column": 3}}, "line": 250}, "32": {"name": "(anonymous_32)", "decl": {"start": {"line": 256, "column": 10}, "end": {"line": 256, "column": 11}}, "loc": {"start": {"line": 256, "column": 17}, "end": {"line": 256, "column": 68}}, "line": 256}, "33": {"name": "(anonymous_33)", "decl": {"start": {"line": 278, "column": 8}, "end": {"line": 278, "column": 9}}, "loc": {"start": {"line": 278, "column": 26}, "end": {"line": 282, "column": 3}}, "line": 278}, "34": {"name": "(anonymous_34)", "decl": {"start": {"line": 285, "column": 11}, "end": {"line": 285, "column": 12}}, "loc": {"start": {"line": 285, "column": 29}, "end": {"line": 298, "column": 3}}, "line": 285}, "35": {"name": "(anonymous_35)", "decl": {"start": {"line": 301, "column": 9}, "end": {"line": 301, "column": 10}}, "loc": {"start": {"line": 301, "column": 27}, "end": {"line": 307, "column": 3}}, "line": 301}}, "branchMap": {"0": {"loc": {"start": {"line": 36, "column": 6}, "end": {"line": 39, "column": 7}}, "type": "if", "locations": [{"start": {"line": 36, "column": 6}, "end": {"line": 39, "column": 7}}, {"start": {}, "end": {}}], "line": 36}, "1": {"loc": {"start": {"line": 57, "column": 2}, "end": {"line": 59, "column": 3}}, "type": "if", "locations": [{"start": {"line": 57, "column": 2}, "end": {"line": 59, "column": 3}}, {"start": {}, "end": {}}], "line": 57}, "2": {"loc": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 18}}, {"start": {"line": 57, "column": 22}, "end": {"line": 57, "column": 55}}], "line": 57}, "3": {"loc": {"start": {"line": 66, "column": 2}, "end": {"line": 66, "column": 41}}, "type": "if", "locations": [{"start": {"line": 66, "column": 2}, "end": {"line": 66, "column": 41}}, {"start": {}, "end": {}}], "line": 66}, "4": {"loc": {"start": {"line": 69, "column": 4}, "end": {"line": 71, "column": 5}}, "type": "if", "locations": [{"start": {"line": 69, "column": 4}, "end": {"line": 71, "column": 5}}, {"start": {}, "end": {}}], "line": 69}, "5": {"loc": {"start": {"line": 85, "column": 4}, "end": {"line": 98, "column": 5}}, "type": "if", "locations": [{"start": {"line": 85, "column": 4}, "end": {"line": 98, "column": 5}}, {"start": {}, "end": {}}], "line": 85}, "6": {"loc": {"start": {"line": 86, "column": 6}, "end": {"line": 88, "column": 7}}, "type": "if", "locations": [{"start": {"line": 86, "column": 6}, "end": {"line": 88, "column": 7}}, {"start": {}, "end": {}}], "line": 86}, "7": {"loc": {"start": {"line": 93, "column": 6}, "end": {"line": 95, "column": 7}}, "type": "if", "locations": [{"start": {"line": 93, "column": 6}, "end": {"line": 95, "column": 7}}, {"start": {}, "end": {}}], "line": 93}, "8": {"loc": {"start": {"line": 106, "column": 2}, "end": {"line": 106, "column": 40}}, "type": "default-arg", "locations": [{"start": {"line": 106, "column": 38}, "end": {"line": 106, "column": 40}}], "line": 106}, "9": {"loc": {"start": {"line": 113, "column": 4}, "end": {"line": 113, "column": 24}}, "type": "if", "locations": [{"start": {"line": 113, "column": 4}, "end": {"line": 113, "column": 24}}, {"start": {}, "end": {}}], "line": 113}, "10": {"loc": {"start": {"line": 180, "column": 4}, "end": {"line": 182, "column": 5}}, "type": "if", "locations": [{"start": {"line": 180, "column": 4}, "end": {"line": 182, "column": 5}}, {"start": {}, "end": {}}], "line": 180}, "11": {"loc": {"start": {"line": 198, "column": 6}, "end": {"line": 200, "column": 7}}, "type": "if", "locations": [{"start": {"line": 198, "column": 6}, "end": {"line": 200, "column": 7}}, {"start": {}, "end": {}}], "line": 198}, "12": {"loc": {"start": {"line": 210, "column": 4}, "end": {"line": 223, "column": 5}}, "type": "if", "locations": [{"start": {"line": 210, "column": 4}, "end": {"line": 223, "column": 5}}, {"start": {}, "end": {}}], "line": 210}, "13": {"loc": {"start": {"line": 210, "column": 8}, "end": {"line": 210, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 210, "column": 8}, "end": {"line": 210, "column": 46}}, {"start": {"line": 210, "column": 50}, "end": {"line": 210, "column": 73}}], "line": 210}, "14": {"loc": {"start": {"line": 213, "column": 8}, "end": {"line": 218, "column": 9}}, "type": "if", "locations": [{"start": {"line": 213, "column": 8}, "end": {"line": 218, "column": 9}}, {"start": {}, "end": {}}], "line": 213}, "15": {"loc": {"start": {"line": 230, "column": 2}, "end": {"line": 230, "column": 56}}, "type": "default-arg", "locations": [{"start": {"line": 230, "column": 54}, "end": {"line": 230, "column": 56}}], "line": 230}, "16": {"loc": {"start": {"line": 234, "column": 6}, "end": {"line": 239, "column": 7}}, "type": "if", "locations": [{"start": {"line": 234, "column": 6}, "end": {"line": 239, "column": 7}}, {"start": {"line": 237, "column": 13}, "end": {"line": 239, "column": 7}}], "line": 234}, "17": {"loc": {"start": {"line": 253, "column": 4}, "end": {"line": 267, "column": 5}}, "type": "if", "locations": [{"start": {"line": 253, "column": 4}, "end": {"line": 267, "column": 5}}, {"start": {}, "end": {}}], "line": 253}, "18": {"loc": {"start": {"line": 254, "column": 6}, "end": {"line": 264, "column": 7}}, "type": "if", "locations": [{"start": {"line": 254, "column": 6}, "end": {"line": 264, "column": 7}}, {"start": {}, "end": {}}], "line": 254}, "19": {"loc": {"start": {"line": 259, "column": 8}, "end": {"line": 263, "column": 9}}, "type": "if", "locations": [{"start": {"line": 259, "column": 8}, "end": {"line": 263, "column": 9}}, {"start": {"line": 261, "column": 15}, "end": {"line": 263, "column": 9}}], "line": 259}, "20": {"loc": {"start": {"line": 261, "column": 15}, "end": {"line": 263, "column": 9}}, "type": "if", "locations": [{"start": {"line": 261, "column": 15}, "end": {"line": 263, "column": 9}}, {"start": {}, "end": {}}], "line": 261}, "21": {"loc": {"start": {"line": 279, "column": 4}, "end": {"line": 281, "column": 5}}, "type": "if", "locations": [{"start": {"line": 279, "column": 4}, "end": {"line": 281, "column": 5}}, {"start": {}, "end": {}}], "line": 279}, "22": {"loc": {"start": {"line": 279, "column": 8}, "end": {"line": 279, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 279, "column": 8}, "end": {"line": 279, "column": 42}}, {"start": {"line": 279, "column": 46}, "end": {"line": 279, "column": 62}}], "line": 279}, "23": {"loc": {"start": {"line": 286, "column": 4}, "end": {"line": 297, "column": 5}}, "type": "if", "locations": [{"start": {"line": 286, "column": 4}, "end": {"line": 297, "column": 5}}, {"start": {}, "end": {}}], "line": 286}, "24": {"loc": {"start": {"line": 286, "column": 8}, "end": {"line": 286, "column": 85}}, "type": "binary-expr", "locations": [{"start": {"line": 286, "column": 8}, "end": {"line": 286, "column": 42}}, {"start": {"line": 286, "column": 46}, "end": {"line": 286, "column": 62}}, {"start": {"line": 286, "column": 66}, "end": {"line": 286, "column": 85}}], "line": 286}, "25": {"loc": {"start": {"line": 291, "column": 6}, "end": {"line": 296, "column": 7}}, "type": "if", "locations": [{"start": {"line": 291, "column": 6}, "end": {"line": 296, "column": 7}}, {"start": {}, "end": {}}], "line": 291}, "26": {"loc": {"start": {"line": 293, "column": 8}, "end": {"line": 295, "column": 9}}, "type": "if", "locations": [{"start": {"line": 293, "column": 8}, "end": {"line": 295, "column": 9}}, {"start": {}, "end": {}}], "line": 293}, "27": {"loc": {"start": {"line": 302, "column": 4}, "end": {"line": 306, "column": 5}}, "type": "if", "locations": [{"start": {"line": 302, "column": 4}, "end": {"line": 306, "column": 5}}, {"start": {}, "end": {}}], "line": 302}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\runtime-env.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\runtime-env.ts", "statementMap": {"0": {"start": {"line": 16, "column": 2}, "end": {"line": 19, "column": 3}}, "1": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 52}}, "2": {"start": {"line": 22, "column": 26}, "end": {"line": 29, "column": 3}}, "3": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": 13}}}, "fnMap": {"0": {"name": "getRuntimeEnv", "decl": {"start": {"line": 14, "column": 16}, "end": {"line": 14, "column": 29}}, "loc": {"start": {"line": 14, "column": 44}, "end": {"line": 32, "column": 1}}, "line": 14}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 2}, "end": {"line": 19, "column": 3}}, "type": "if", "locations": [{"start": {"line": 16, "column": 2}, "end": {"line": 19, "column": 3}}, {"start": {}, "end": {}}], "line": 16}, "1": {"loc": {"start": {"line": 16, "column": 6}, "end": {"line": 17, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 35}}, {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 49}}], "line": 16}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\type-utils.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\type-utils.ts", "statementMap": {"0": {"start": {"line": 27, "column": 26}, "end": {"line": 38, "column": 2}}, "1": {"start": {"line": 40, "column": 28}, "end": {"line": 49, "column": 2}}, "2": {"start": {"line": 51, "column": 29}, "end": {"line": 62, "column": 2}}, "3": {"start": {"line": 66, "column": 2}, "end": {"line": 71, "column": 3}}, "4": {"start": {"line": 67, "column": 4}, "end": {"line": 67, "column": 26}}, "5": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 23}}, "6": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 17}}, "7": {"start": {"line": 75, "column": 2}, "end": {"line": 80, "column": 3}}, "8": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": 28}}, "9": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": 25}}, "10": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 17}}, "11": {"start": {"line": 84, "column": 2}, "end": {"line": 89, "column": 3}}, "12": {"start": {"line": 85, "column": 4}, "end": {"line": 85, "column": 29}}, "13": {"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": 26}}, "14": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 17}}, "15": {"start": {"line": 94, "column": 2}, "end": {"line": 94, "column": 49}}, "16": {"start": {"line": 98, "column": 2}, "end": {"line": 98, "column": 51}}, "17": {"start": {"line": 102, "column": 2}, "end": {"line": 102, "column": 52}}, "18": {"start": {"line": 107, "column": 2}, "end": {"line": 107, "column": 37}}, "19": {"start": {"line": 111, "column": 2}, "end": {"line": 111, "column": 39}}, "20": {"start": {"line": 115, "column": 2}, "end": {"line": 115, "column": 46}}, "21": {"start": {"line": 123, "column": 2}, "end": {"line": 125, "column": 3}}, "22": {"start": {"line": 124, "column": 4}, "end": {"line": 124, "column": 17}}, "23": {"start": {"line": 127, "column": 2}, "end": {"line": 129, "column": 3}}, "24": {"start": {"line": 128, "column": 4}, "end": {"line": 128, "column": 35}}, "25": {"start": {"line": 131, "column": 2}, "end": {"line": 131, "column": 14}}, "26": {"start": {"line": 135, "column": 2}, "end": {"line": 135, "column": 77}}, "27": {"start": {"line": 140, "column": 2}, "end": {"line": 142, "column": 3}}, "28": {"start": {"line": 141, "column": 4}, "end": {"line": 141, "column": 82}}, "29": {"start": {"line": 143, "column": 2}, "end": {"line": 143, "column": 13}}, "30": {"start": {"line": 147, "column": 2}, "end": {"line": 149, "column": 3}}, "31": {"start": {"line": 148, "column": 4}, "end": {"line": 148, "column": 84}}, "32": {"start": {"line": 150, "column": 2}, "end": {"line": 150, "column": 13}}, "33": {"start": {"line": 154, "column": 2}, "end": {"line": 156, "column": 3}}, "34": {"start": {"line": 155, "column": 4}, "end": {"line": 155, "column": 91}}, "35": {"start": {"line": 157, "column": 2}, "end": {"line": 157, "column": 13}}, "36": {"start": {"line": 161, "column": 2}, "end": {"line": 163, "column": 3}}, "37": {"start": {"line": 162, "column": 4}, "end": {"line": 162, "column": 89}}, "38": {"start": {"line": 164, "column": 2}, "end": {"line": 164, "column": 13}}, "39": {"start": {"line": 172, "column": 2}, "end": {"line": 172, "column": 20}}, "40": {"start": {"line": 180, "column": 2}, "end": {"line": 194, "column": 3}}, "41": {"start": {"line": 181, "column": 17}, "end": {"line": 181, "column": 32}}, "42": {"start": {"line": 182, "column": 18}, "end": {"line": 182, "column": 21}}, "43": {"start": {"line": 184, "column": 4}, "end": {"line": 189, "column": 5}}, "44": {"start": {"line": 185, "column": 6}, "end": {"line": 187, "column": 7}}, "45": {"start": {"line": 186, "column": 8}, "end": {"line": 186, "column": 28}}, "46": {"start": {"line": 188, "column": 6}, "end": {"line": 188, "column": 29}}, "47": {"start": {"line": 191, "column": 4}, "end": {"line": 191, "column": 35}}, "48": {"start": {"line": 193, "column": 4}, "end": {"line": 193, "column": 24}}, "49": {"start": {"line": 202, "column": 2}, "end": {"line": 202, "column": 20}}, "50": {"start": {"line": 209, "column": 2}, "end": {"line": 209, "column": 61}}, "51": {"start": {"line": 216, "column": 2}, "end": {"line": 216, "column": 61}}, "52": {"start": {"line": 223, "column": 2}, "end": {"line": 223, "column": 62}}, "53": {"start": {"line": 228, "column": 2}, "end": {"line": 228, "column": 101}}, "54": {"start": {"line": 232, "column": 2}, "end": {"line": 232, "column": 99}}, "55": {"start": {"line": 236, "column": 2}, "end": {"line": 236, "column": 82}}, "56": {"start": {"line": 241, "column": 2}, "end": {"line": 241, "column": 58}}, "57": {"start": {"line": 245, "column": 2}, "end": {"line": 245, "column": 46}}, "58": {"start": {"line": 245, "column": 33}, "end": {"line": 245, "column": 46}}, "59": {"start": {"line": 246, "column": 15}, "end": {"line": 246, "column": 30}}, "60": {"start": {"line": 247, "column": 2}, "end": {"line": 247, "column": 27}}, "61": {"start": {"line": 252, "column": 2}, "end": {"line": 252, "column": 46}}, "62": {"start": {"line": 252, "column": 33}, "end": {"line": 252, "column": 46}}, "63": {"start": {"line": 253, "column": 21}, "end": {"line": 253, "column": 49}}, "64": {"start": {"line": 254, "column": 2}, "end": {"line": 254, "column": 32}}, "65": {"start": {"line": 259, "column": 2}, "end": {"line": 259, "column": 46}}, "66": {"start": {"line": 259, "column": 33}, "end": {"line": 259, "column": 46}}, "67": {"start": {"line": 260, "column": 20}, "end": {"line": 260, "column": 96}}, "68": {"start": {"line": 261, "column": 2}, "end": {"line": 261, "column": 31}}, "69": {"start": {"line": 266, "column": 2}, "end": {"line": 266, "column": 50}}, "70": {"start": {"line": 270, "column": 2}, "end": {"line": 270, "column": 79}}, "71": {"start": {"line": 270, "column": 53}, "end": {"line": 270, "column": 77}}, "72": {"start": {"line": 274, "column": 2}, "end": {"line": 274, "column": 79}}, "73": {"start": {"line": 274, "column": 53}, "end": {"line": 274, "column": 77}}, "74": {"start": {"line": 279, "column": 2}, "end": {"line": 283, "column": 4}}, "75": {"start": {"line": 290, "column": 2}, "end": {"line": 290, "column": 40}}, "76": {"start": {"line": 290, "column": 27}, "end": {"line": 290, "column": 40}}, "77": {"start": {"line": 291, "column": 2}, "end": {"line": 291, "column": 39}}, "78": {"start": {"line": 291, "column": 27}, "end": {"line": 291, "column": 37}}, "79": {"start": {"line": 296, "column": 2}, "end": {"line": 296, "column": 32}}, "80": {"start": {"line": 300, "column": 2}, "end": {"line": 300, "column": 77}}, "81": {"start": {"line": 304, "column": 2}, "end": {"line": 304, "column": 89}}, "82": {"start": {"line": 309, "column": 2}, "end": {"line": 309, "column": 48}}, "83": {"start": {"line": 317, "column": 2}, "end": {"line": 325, "column": 3}}, "84": {"start": {"line": 318, "column": 19}, "end": {"line": 318, "column": 35}}, "85": {"start": {"line": 319, "column": 4}, "end": {"line": 321, "column": 5}}, "86": {"start": {"line": 320, "column": 6}, "end": {"line": 320, "column": 18}}, "87": {"start": {"line": 322, "column": 4}, "end": {"line": 322, "column": 18}}, "88": {"start": {"line": 324, "column": 4}, "end": {"line": 324, "column": 16}}}, "fnMap": {"0": {"name": "validateUser", "decl": {"start": {"line": 65, "column": 16}, "end": {"line": 65, "column": 28}}, "loc": {"start": {"line": 65, "column": 56}, "end": {"line": 72, "column": 1}}, "line": 65}, "1": {"name": "validateClient", "decl": {"start": {"line": 74, "column": 16}, "end": {"line": 74, "column": 30}}, "loc": {"start": {"line": 74, "column": 60}, "end": {"line": 81, "column": 1}}, "line": 74}, "2": {"name": "validate<PERSON><PERSON><PERSON>", "decl": {"start": {"line": 83, "column": 16}, "end": {"line": 83, "column": 31}}, "loc": {"start": {"line": 83, "column": 62}, "end": {"line": 90, "column": 1}}, "line": 83}, "3": {"name": "isUserArray", "decl": {"start": {"line": 93, "column": 16}, "end": {"line": 93, "column": 27}}, "loc": {"start": {"line": 93, "column": 57}, "end": {"line": 95, "column": 1}}, "line": 93}, "4": {"name": "isClientArray", "decl": {"start": {"line": 97, "column": 16}, "end": {"line": 97, "column": 29}}, "loc": {"start": {"line": 97, "column": 61}, "end": {"line": 99, "column": 1}}, "line": 97}, "5": {"name": "isRenewalArray", "decl": {"start": {"line": 101, "column": 16}, "end": {"line": 101, "column": 30}}, "loc": {"start": {"line": 101, "column": 63}, "end": {"line": 103, "column": 1}}, "line": 101}, "6": {"name": "isNullableUser", "decl": {"start": {"line": 106, "column": 16}, "end": {"line": 106, "column": 30}}, "loc": {"start": {"line": 106, "column": 65}, "end": {"line": 108, "column": 1}}, "line": 106}, "7": {"name": "isNullableClient", "decl": {"start": {"line": 110, "column": 16}, "end": {"line": 110, "column": 32}}, "loc": {"start": {"line": 110, "column": 69}, "end": {"line": 112, "column": 1}}, "line": 110}, "8": {"name": "isNullableTenantContext", "decl": {"start": {"line": 114, "column": 16}, "end": {"line": 114, "column": 39}}, "loc": {"start": {"line": 114, "column": 83}, "end": {"line": 116, "column": 1}}, "line": 114}, "9": {"name": "isSuccessResponse", "decl": {"start": {"line": 119, "column": 16}, "end": {"line": 119, "column": 33}}, "loc": {"start": {"line": 122, "column": 54}, "end": {"line": 132, "column": 1}}, "line": 122}, "10": {"name": "isErrorResponse", "decl": {"start": {"line": 134, "column": 16}, "end": {"line": 134, "column": 31}}, "loc": {"start": {"line": 134, "column": 102}, "end": {"line": 136, "column": 1}}, "line": 134}, "11": {"name": "assertUser", "decl": {"start": {"line": 139, "column": 16}, "end": {"line": 139, "column": 26}}, "loc": {"start": {"line": 139, "column": 68}, "end": {"line": 144, "column": 1}}, "line": 139}, "12": {"name": "assertClient", "decl": {"start": {"line": 146, "column": 16}, "end": {"line": 146, "column": 28}}, "loc": {"start": {"line": 146, "column": 72}, "end": {"line": 151, "column": 1}}, "line": 146}, "13": {"name": "assertTenantContext", "decl": {"start": {"line": 153, "column": 16}, "end": {"line": 153, "column": 35}}, "loc": {"start": {"line": 153, "column": 86}, "end": {"line": 158, "column": 1}}, "line": 153}, "14": {"name": "assertAuthSession", "decl": {"start": {"line": 160, "column": 16}, "end": {"line": 160, "column": 33}}, "loc": {"start": {"line": 160, "column": 82}, "end": {"line": 165, "column": 1}}, "line": 160}, "15": {"name": "safeGetProperty", "decl": {"start": {"line": 168, "column": 16}, "end": {"line": 168, "column": 31}}, "loc": {"start": {"line": 171, "column": 20}, "end": {"line": 173, "column": 1}}, "line": 171}, "16": {"name": "safeGetNestedProperty", "decl": {"start": {"line": 175, "column": 16}, "end": {"line": 175, "column": 37}}, "loc": {"start": {"line": 179, "column": 17}, "end": {"line": 195, "column": 1}}, "line": 179}, "17": {"name": "hasProperty", "decl": {"start": {"line": 198, "column": 16}, "end": {"line": 198, "column": 27}}, "loc": {"start": {"line": 201, "column": 33}, "end": {"line": 203, "column": 1}}, "line": 201}, "18": {"name": "hasStringProperty", "decl": {"start": {"line": 205, "column": 16}, "end": {"line": 205, "column": 33}}, "loc": {"start": {"line": 208, "column": 32}, "end": {"line": 210, "column": 1}}, "line": 208}, "19": {"name": "hasNumberProperty", "decl": {"start": {"line": 212, "column": 16}, "end": {"line": 212, "column": 33}}, "loc": {"start": {"line": 215, "column": 32}, "end": {"line": 217, "column": 1}}, "line": 215}, "20": {"name": "hasBooleanProperty", "decl": {"start": {"line": 219, "column": 16}, "end": {"line": 219, "column": 34}}, "loc": {"start": {"line": 222, "column": 33}, "end": {"line": 224, "column": 1}}, "line": 222}, "21": {"name": "isValidStatus", "decl": {"start": {"line": 227, "column": 16}, "end": {"line": 227, "column": 29}}, "loc": {"start": {"line": 227, "column": 104}, "end": {"line": 229, "column": 1}}, "line": 227}, "22": {"name": "isValidRenewalStatus", "decl": {"start": {"line": 231, "column": 16}, "end": {"line": 231, "column": 36}}, "loc": {"start": {"line": 231, "column": 109}, "end": {"line": 233, "column": 1}}, "line": 231}, "23": {"name": "isValidTheme", "decl": {"start": {"line": 235, "column": 16}, "end": {"line": 235, "column": 28}}, "loc": {"start": {"line": 235, "column": 83}, "end": {"line": 237, "column": 1}}, "line": 235}, "24": {"name": "isValidDate", "decl": {"start": {"line": 240, "column": 16}, "end": {"line": 240, "column": 27}}, "loc": {"start": {"line": 240, "column": 59}, "end": {"line": 242, "column": 1}}, "line": 240}, "25": {"name": "isValidDateString", "decl": {"start": {"line": 244, "column": 16}, "end": {"line": 244, "column": 33}}, "loc": {"start": {"line": 244, "column": 67}, "end": {"line": 248, "column": 1}}, "line": 244}, "26": {"name": "isValidEmail", "decl": {"start": {"line": 251, "column": 16}, "end": {"line": 251, "column": 28}}, "loc": {"start": {"line": 251, "column": 62}, "end": {"line": 255, "column": 1}}, "line": 251}, "27": {"name": "isValidUUID", "decl": {"start": {"line": 258, "column": 16}, "end": {"line": 258, "column": 27}}, "loc": {"start": {"line": 258, "column": 61}, "end": {"line": 262, "column": 1}}, "line": 258}, "28": {"name": "isNonEmptyArray", "decl": {"start": {"line": 265, "column": 16}, "end": {"line": 265, "column": 31}}, "loc": {"start": {"line": 265, "column": 73}, "end": {"line": 267, "column": 1}}, "line": 265}, "29": {"name": "isStringArray", "decl": {"start": {"line": 269, "column": 16}, "end": {"line": 269, "column": 29}}, "loc": {"start": {"line": 269, "column": 65}, "end": {"line": 271, "column": 1}}, "line": 269}, "30": {"name": "(anonymous_30)", "decl": {"start": {"line": 270, "column": 45}, "end": {"line": 270, "column": 46}}, "loc": {"start": {"line": 270, "column": 53}, "end": {"line": 270, "column": 77}}, "line": 270}, "31": {"name": "isNumberArray", "decl": {"start": {"line": 273, "column": 16}, "end": {"line": 273, "column": 29}}, "loc": {"start": {"line": 273, "column": 65}, "end": {"line": 275, "column": 1}}, "line": 273}, "32": {"name": "(anonymous_32)", "decl": {"start": {"line": 274, "column": 45}, "end": {"line": 274, "column": 46}}, "loc": {"start": {"line": 274, "column": 53}, "end": {"line": 274, "column": 77}}, "line": 274}, "33": {"name": "isPlainObject", "decl": {"start": {"line": 278, "column": 16}, "end": {"line": 278, "column": 29}}, "loc": {"start": {"line": 278, "column": 80}, "end": {"line": 284, "column": 1}}, "line": 278}, "34": {"name": "hasRequiredKeys", "decl": {"start": {"line": 286, "column": 16}, "end": {"line": 286, "column": 31}}, "loc": {"start": {"line": 289, "column": 12}, "end": {"line": 292, "column": 1}}, "line": 289}, "35": {"name": "(anonymous_35)", "decl": {"start": {"line": 291, "column": 20}, "end": {"line": 291, "column": 21}}, "loc": {"start": {"line": 291, "column": 27}, "end": {"line": 291, "column": 37}}, "line": 291}, "36": {"name": "isError", "decl": {"start": {"line": 295, "column": 16}, "end": {"line": 295, "column": 23}}, "loc": {"start": {"line": 295, "column": 56}, "end": {"line": 297, "column": 1}}, "line": 295}, "37": {"name": "isErrorWithCode", "decl": {"start": {"line": 299, "column": 16}, "end": {"line": 299, "column": 31}}, "loc": {"start": {"line": 299, "column": 83}, "end": {"line": 301, "column": 1}}, "line": 299}, "38": {"name": "isErrorWithStatus", "decl": {"start": {"line": 303, "column": 16}, "end": {"line": 303, "column": 33}}, "loc": {"start": {"line": 303, "column": 91}, "end": {"line": 305, "column": 1}}, "line": 303}, "39": {"name": "assertNever", "decl": {"start": {"line": 308, "column": 16}, "end": {"line": 308, "column": 27}}, "loc": {"start": {"line": 308, "column": 49}, "end": {"line": 310, "column": 1}}, "line": 308}, "40": {"name": "safeJsonParse", "decl": {"start": {"line": 313, "column": 16}, "end": {"line": 313, "column": 29}}, "loc": {"start": {"line": 316, "column": 12}, "end": {"line": 326, "column": 1}}, "line": 316}}, "branchMap": {"0": {"loc": {"start": {"line": 94, "column": 9}, "end": {"line": 94, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 94, "column": 9}, "end": {"line": 94, "column": 27}}, {"start": {"line": 94, "column": 31}, "end": {"line": 94, "column": 48}}], "line": 94}, "1": {"loc": {"start": {"line": 98, "column": 9}, "end": {"line": 98, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 98, "column": 9}, "end": {"line": 98, "column": 27}}, {"start": {"line": 98, "column": 31}, "end": {"line": 98, "column": 50}}], "line": 98}, "2": {"loc": {"start": {"line": 102, "column": 9}, "end": {"line": 102, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 102, "column": 9}, "end": {"line": 102, "column": 27}}, {"start": {"line": 102, "column": 31}, "end": {"line": 102, "column": 51}}], "line": 102}, "3": {"loc": {"start": {"line": 107, "column": 9}, "end": {"line": 107, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 107, "column": 9}, "end": {"line": 107, "column": 21}}, {"start": {"line": 107, "column": 25}, "end": {"line": 107, "column": 36}}], "line": 107}, "4": {"loc": {"start": {"line": 111, "column": 9}, "end": {"line": 111, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 111, "column": 9}, "end": {"line": 111, "column": 21}}, {"start": {"line": 111, "column": 25}, "end": {"line": 111, "column": 38}}], "line": 111}, "5": {"loc": {"start": {"line": 115, "column": 9}, "end": {"line": 115, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 115, "column": 9}, "end": {"line": 115, "column": 21}}, {"start": {"line": 115, "column": 25}, "end": {"line": 115, "column": 45}}], "line": 115}, "6": {"loc": {"start": {"line": 123, "column": 2}, "end": {"line": 125, "column": 3}}, "type": "if", "locations": [{"start": {"line": 123, "column": 2}, "end": {"line": 125, "column": 3}}, {"start": {}, "end": {}}], "line": 123}, "7": {"loc": {"start": {"line": 123, "column": 6}, "end": {"line": 123, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 123, "column": 6}, "end": {"line": 123, "column": 25}}, {"start": {"line": 123, "column": 29}, "end": {"line": 123, "column": 41}}, {"start": {"line": 123, "column": 45}, "end": {"line": 123, "column": 54}}], "line": 123}, "8": {"loc": {"start": {"line": 127, "column": 2}, "end": {"line": 129, "column": 3}}, "type": "if", "locations": [{"start": {"line": 127, "column": 2}, "end": {"line": 129, "column": 3}}, {"start": {}, "end": {}}], "line": 127}, "9": {"loc": {"start": {"line": 135, "column": 9}, "end": {"line": 135, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 135, "column": 9}, "end": {"line": 135, "column": 27}}, {"start": {"line": 135, "column": 31}, "end": {"line": 135, "column": 43}}, {"start": {"line": 135, "column": 47}, "end": {"line": 135, "column": 76}}], "line": 135}, "10": {"loc": {"start": {"line": 139, "column": 41}, "end": {"line": 139, "column": 60}}, "type": "default-arg", "locations": [{"start": {"line": 139, "column": 51}, "end": {"line": 139, "column": 60}}], "line": 139}, "11": {"loc": {"start": {"line": 140, "column": 2}, "end": {"line": 142, "column": 3}}, "type": "if", "locations": [{"start": {"line": 140, "column": 2}, "end": {"line": 142, "column": 3}}, {"start": {}, "end": {}}], "line": 140}, "12": {"loc": {"start": {"line": 146, "column": 43}, "end": {"line": 146, "column": 62}}, "type": "default-arg", "locations": [{"start": {"line": 146, "column": 53}, "end": {"line": 146, "column": 62}}], "line": 146}, "13": {"loc": {"start": {"line": 147, "column": 2}, "end": {"line": 149, "column": 3}}, "type": "if", "locations": [{"start": {"line": 147, "column": 2}, "end": {"line": 149, "column": 3}}, {"start": {}, "end": {}}], "line": 147}, "14": {"loc": {"start": {"line": 153, "column": 50}, "end": {"line": 153, "column": 69}}, "type": "default-arg", "locations": [{"start": {"line": 153, "column": 60}, "end": {"line": 153, "column": 69}}], "line": 153}, "15": {"loc": {"start": {"line": 154, "column": 2}, "end": {"line": 156, "column": 3}}, "type": "if", "locations": [{"start": {"line": 154, "column": 2}, "end": {"line": 156, "column": 3}}, {"start": {}, "end": {}}], "line": 154}, "16": {"loc": {"start": {"line": 160, "column": 48}, "end": {"line": 160, "column": 67}}, "type": "default-arg", "locations": [{"start": {"line": 160, "column": 58}, "end": {"line": 160, "column": 67}}], "line": 160}, "17": {"loc": {"start": {"line": 161, "column": 2}, "end": {"line": 163, "column": 3}}, "type": "if", "locations": [{"start": {"line": 161, "column": 2}, "end": {"line": 163, "column": 3}}, {"start": {}, "end": {}}], "line": 161}, "18": {"loc": {"start": {"line": 185, "column": 6}, "end": {"line": 187, "column": 7}}, "type": "if", "locations": [{"start": {"line": 185, "column": 6}, "end": {"line": 187, "column": 7}}, {"start": {}, "end": {}}], "line": 185}, "19": {"loc": {"start": {"line": 185, "column": 10}, "end": {"line": 185, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 185, "column": 10}, "end": {"line": 185, "column": 25}}, {"start": {"line": 185, "column": 29}, "end": {"line": 185, "column": 56}}], "line": 185}, "20": {"loc": {"start": {"line": 191, "column": 11}, "end": {"line": 191, "column": 34}}, "type": "binary-expr", "locations": [{"start": {"line": 191, "column": 11}, "end": {"line": 191, "column": 18}}, {"start": {"line": 191, "column": 22}, "end": {"line": 191, "column": 34}}], "line": 191}, "21": {"loc": {"start": {"line": 209, "column": 9}, "end": {"line": 209, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 209, "column": 9}, "end": {"line": 209, "column": 19}}, {"start": {"line": 209, "column": 23}, "end": {"line": 209, "column": 60}}], "line": 209}, "22": {"loc": {"start": {"line": 216, "column": 9}, "end": {"line": 216, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 216, "column": 9}, "end": {"line": 216, "column": 19}}, {"start": {"line": 216, "column": 23}, "end": {"line": 216, "column": 60}}], "line": 216}, "23": {"loc": {"start": {"line": 223, "column": 9}, "end": {"line": 223, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 223, "column": 9}, "end": {"line": 223, "column": 19}}, {"start": {"line": 223, "column": 23}, "end": {"line": 223, "column": 61}}], "line": 223}, "24": {"loc": {"start": {"line": 228, "column": 9}, "end": {"line": 228, "column": 100}}, "type": "binary-expr", "locations": [{"start": {"line": 228, "column": 9}, "end": {"line": 228, "column": 34}}, {"start": {"line": 228, "column": 38}, "end": {"line": 228, "column": 100}}], "line": 228}, "25": {"loc": {"start": {"line": 232, "column": 9}, "end": {"line": 232, "column": 98}}, "type": "binary-expr", "locations": [{"start": {"line": 232, "column": 9}, "end": {"line": 232, "column": 34}}, {"start": {"line": 232, "column": 38}, "end": {"line": 232, "column": 98}}], "line": 232}, "26": {"loc": {"start": {"line": 236, "column": 9}, "end": {"line": 236, "column": 81}}, "type": "binary-expr", "locations": [{"start": {"line": 236, "column": 9}, "end": {"line": 236, "column": 34}}, {"start": {"line": 236, "column": 38}, "end": {"line": 236, "column": 81}}], "line": 236}, "27": {"loc": {"start": {"line": 241, "column": 9}, "end": {"line": 241, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 241, "column": 9}, "end": {"line": 241, "column": 30}}, {"start": {"line": 241, "column": 34}, "end": {"line": 241, "column": 57}}], "line": 241}, "28": {"loc": {"start": {"line": 245, "column": 2}, "end": {"line": 245, "column": 46}}, "type": "if", "locations": [{"start": {"line": 245, "column": 2}, "end": {"line": 245, "column": 46}}, {"start": {}, "end": {}}], "line": 245}, "29": {"loc": {"start": {"line": 252, "column": 2}, "end": {"line": 252, "column": 46}}, "type": "if", "locations": [{"start": {"line": 252, "column": 2}, "end": {"line": 252, "column": 46}}, {"start": {}, "end": {}}], "line": 252}, "30": {"loc": {"start": {"line": 259, "column": 2}, "end": {"line": 259, "column": 46}}, "type": "if", "locations": [{"start": {"line": 259, "column": 2}, "end": {"line": 259, "column": 46}}, {"start": {}, "end": {}}], "line": 259}, "31": {"loc": {"start": {"line": 266, "column": 9}, "end": {"line": 266, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 266, "column": 9}, "end": {"line": 266, "column": 29}}, {"start": {"line": 266, "column": 33}, "end": {"line": 266, "column": 49}}], "line": 266}, "32": {"loc": {"start": {"line": 270, "column": 9}, "end": {"line": 270, "column": 78}}, "type": "binary-expr", "locations": [{"start": {"line": 270, "column": 9}, "end": {"line": 270, "column": 29}}, {"start": {"line": 270, "column": 33}, "end": {"line": 270, "column": 78}}], "line": 270}, "33": {"loc": {"start": {"line": 274, "column": 9}, "end": {"line": 274, "column": 78}}, "type": "binary-expr", "locations": [{"start": {"line": 274, "column": 9}, "end": {"line": 274, "column": 29}}, {"start": {"line": 274, "column": 33}, "end": {"line": 274, "column": 78}}], "line": 274}, "34": {"loc": {"start": {"line": 280, "column": 4}, "end": {"line": 282, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 280, "column": 4}, "end": {"line": 280, "column": 18}}, {"start": {"line": 281, "column": 4}, "end": {"line": 281, "column": 29}}, {"start": {"line": 282, "column": 4}, "end": {"line": 282, "column": 63}}], "line": 280}, "35": {"loc": {"start": {"line": 290, "column": 2}, "end": {"line": 290, "column": 40}}, "type": "if", "locations": [{"start": {"line": 290, "column": 2}, "end": {"line": 290, "column": 40}}, {"start": {}, "end": {}}], "line": 290}, "36": {"loc": {"start": {"line": 300, "column": 9}, "end": {"line": 300, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 300, "column": 9}, "end": {"line": 300, "column": 23}}, {"start": {"line": 300, "column": 27}, "end": {"line": 300, "column": 42}}, {"start": {"line": 300, "column": 46}, "end": {"line": 300, "column": 76}}], "line": 300}, "37": {"loc": {"start": {"line": 304, "column": 9}, "end": {"line": 304, "column": 88}}, "type": "binary-expr", "locations": [{"start": {"line": 304, "column": 9}, "end": {"line": 304, "column": 23}}, {"start": {"line": 304, "column": 27}, "end": {"line": 304, "column": 48}}, {"start": {"line": 304, "column": 52}, "end": {"line": 304, "column": 88}}], "line": 304}, "38": {"loc": {"start": {"line": 319, "column": 4}, "end": {"line": 321, "column": 5}}, "type": "if", "locations": [{"start": {"line": 319, "column": 4}, "end": {"line": 321, "column": 5}}, {"start": {}, "end": {}}], "line": 319}, "39": {"loc": {"start": {"line": 319, "column": 8}, "end": {"line": 319, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 319, "column": 8}, "end": {"line": 319, "column": 17}}, {"start": {"line": 319, "column": 21}, "end": {"line": 319, "column": 39}}], "line": 319}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0, 0], "8": [0, 0], "9": [0, 0, 0], "10": [0], "11": [0, 0], "12": [0], "13": [0, 0], "14": [0], "15": [0, 0], "16": [0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0], "30": [0, 0], "31": [0, 0], "32": [0, 0], "33": [0, 0], "34": [0, 0, 0], "35": [0, 0], "36": [0, 0, 0], "37": [0, 0, 0], "38": [0, 0], "39": [0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\types.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\types.ts", "statementMap": {"0": {"start": {"line": 214, "column": 2}, "end": {"line": 221, "column": 4}}, "1": {"start": {"line": 225, "column": 2}, "end": {"line": 232, "column": 4}}, "2": {"start": {"line": 236, "column": 2}, "end": {"line": 245, "column": 4}}, "3": {"start": {"line": 249, "column": 2}, "end": {"line": 256, "column": 4}}, "4": {"start": {"line": 260, "column": 2}, "end": {"line": 265, "column": 4}}, "5": {"start": {"line": 269, "column": 2}, "end": {"line": 276, "column": 4}}, "6": {"start": {"line": 280, "column": 2}, "end": {"line": 287, "column": 4}}, "7": {"start": {"line": 291, "column": 2}, "end": {"line": 299, "column": 4}}}, "fnMap": {"0": {"name": "isUser", "decl": {"start": {"line": 213, "column": 16}, "end": {"line": 213, "column": 22}}, "loc": {"start": {"line": 213, "column": 46}, "end": {"line": 222, "column": 1}}, "line": 213}, "1": {"name": "isClient", "decl": {"start": {"line": 224, "column": 16}, "end": {"line": 224, "column": 24}}, "loc": {"start": {"line": 224, "column": 50}, "end": {"line": 233, "column": 1}}, "line": 224}, "2": {"name": "isTenantContext", "decl": {"start": {"line": 235, "column": 16}, "end": {"line": 235, "column": 31}}, "loc": {"start": {"line": 235, "column": 64}, "end": {"line": 246, "column": 1}}, "line": 235}, "3": {"name": "isRenewal", "decl": {"start": {"line": 248, "column": 16}, "end": {"line": 248, "column": 25}}, "loc": {"start": {"line": 248, "column": 52}, "end": {"line": 257, "column": 1}}, "line": 248}, "4": {"name": "isApiResponse", "decl": {"start": {"line": 259, "column": 16}, "end": {"line": 259, "column": 29}}, "loc": {"start": {"line": 259, "column": 66}, "end": {"line": 266, "column": 1}}, "line": 259}, "5": {"name": "isPaginatedResponse", "decl": {"start": {"line": 268, "column": 16}, "end": {"line": 268, "column": 35}}, "loc": {"start": {"line": 268, "column": 78}, "end": {"line": 277, "column": 1}}, "line": 268}, "6": {"name": "isAuthSession", "decl": {"start": {"line": 279, "column": 16}, "end": {"line": 279, "column": 29}}, "loc": {"start": {"line": 279, "column": 60}, "end": {"line": 288, "column": 1}}, "line": 279}, "7": {"name": "isCognitoJwtPayload", "decl": {"start": {"line": 290, "column": 16}, "end": {"line": 290, "column": 35}}, "loc": {"start": {"line": 290, "column": 72}, "end": {"line": 300, "column": 1}}, "line": 290}}, "branchMap": {"0": {"loc": {"start": {"line": 215, "column": 4}, "end": {"line": 220, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 215, "column": 4}, "end": {"line": 215, "column": 7}}, {"start": {"line": 216, "column": 4}, "end": {"line": 216, "column": 27}}, {"start": {"line": 217, "column": 4}, "end": {"line": 217, "column": 30}}, {"start": {"line": 218, "column": 4}, "end": {"line": 218, "column": 33}}, {"start": {"line": 219, "column": 4}, "end": {"line": 219, "column": 28}}, {"start": {"line": 220, "column": 4}, "end": {"line": 220, "column": 71}}], "line": 215}, "1": {"loc": {"start": {"line": 226, "column": 4}, "end": {"line": 231, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 226, "column": 4}, "end": {"line": 226, "column": 7}}, {"start": {"line": 227, "column": 4}, "end": {"line": 227, "column": 27}}, {"start": {"line": 228, "column": 4}, "end": {"line": 228, "column": 30}}, {"start": {"line": 229, "column": 4}, "end": {"line": 229, "column": 32}}, {"start": {"line": 230, "column": 4}, "end": {"line": 230, "column": 34}}, {"start": {"line": 231, "column": 4}, "end": {"line": 231, "column": 60}}], "line": 226}, "2": {"loc": {"start": {"line": 237, "column": 4}, "end": {"line": 244, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 237, "column": 4}, "end": {"line": 237, "column": 7}}, {"start": {"line": 238, "column": 4}, "end": {"line": 238, "column": 27}}, {"start": {"line": 239, "column": 4}, "end": {"line": 239, "column": 36}}, {"start": {"line": 240, "column": 4}, "end": {"line": 240, "column": 38}}, {"start": {"line": 241, "column": 4}, "end": {"line": 241, "column": 36}}, {"start": {"line": 242, "column": 4}, "end": {"line": 242, "column": 40}}, {"start": {"line": 243, "column": 4}, "end": {"line": 243, "column": 30}}, {"start": {"line": 244, "column": 4}, "end": {"line": 244, "column": 37}}], "line": 237}, "3": {"loc": {"start": {"line": 250, "column": 4}, "end": {"line": 255, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 250, "column": 4}, "end": {"line": 250, "column": 7}}, {"start": {"line": 251, "column": 4}, "end": {"line": 251, "column": 27}}, {"start": {"line": 252, "column": 4}, "end": {"line": 252, "column": 30}}, {"start": {"line": 253, "column": 4}, "end": {"line": 253, "column": 32}}, {"start": {"line": 254, "column": 4}, "end": {"line": 254, "column": 34}}, {"start": {"line": 255, "column": 4}, "end": {"line": 255, "column": 69}}], "line": 250}, "4": {"loc": {"start": {"line": 261, "column": 4}, "end": {"line": 264, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 261, "column": 4}, "end": {"line": 261, "column": 7}}, {"start": {"line": 262, "column": 4}, "end": {"line": 262, "column": 27}}, {"start": {"line": 263, "column": 4}, "end": {"line": 263, "column": 36}}, {"start": {"line": 264, "column": 4}, "end": {"line": 264, "column": 37}}], "line": 261}, "5": {"loc": {"start": {"line": 270, "column": 4}, "end": {"line": 275, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 270, "column": 4}, "end": {"line": 270, "column": 22}}, {"start": {"line": 271, "column": 4}, "end": {"line": 271, "column": 18}}, {"start": {"line": 272, "column": 4}, "end": {"line": 272, "column": 38}}, {"start": {"line": 273, "column": 4}, "end": {"line": 273, "column": 43}}, {"start": {"line": 274, "column": 4}, "end": {"line": 274, "column": 44}}, {"start": {"line": 275, "column": 4}, "end": {"line": 275, "column": 49}}], "line": 270}, "6": {"loc": {"start": {"line": 281, "column": 4}, "end": {"line": 286, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 281, "column": 4}, "end": {"line": 281, "column": 7}}, {"start": {"line": 282, "column": 4}, "end": {"line": 282, "column": 27}}, {"start": {"line": 283, "column": 4}, "end": {"line": 283, "column": 35}}, {"start": {"line": 284, "column": 4}, "end": {"line": 284, "column": 34}}, {"start": {"line": 285, "column": 4}, "end": {"line": 285, "column": 33}}, {"start": {"line": 286, "column": 4}, "end": {"line": 286, "column": 28}}], "line": 281}, "7": {"loc": {"start": {"line": 292, "column": 4}, "end": {"line": 298, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 292, "column": 4}, "end": {"line": 292, "column": 7}}, {"start": {"line": 293, "column": 4}, "end": {"line": 293, "column": 27}}, {"start": {"line": 294, "column": 4}, "end": {"line": 294, "column": 31}}, {"start": {"line": 295, "column": 4}, "end": {"line": 295, "column": 33}}, {"start": {"line": 296, "column": 4}, "end": {"line": 296, "column": 31}}, {"start": {"line": 297, "column": 4}, "end": {"line": 297, "column": 31}}, {"start": {"line": 298, "column": 4}, "end": {"line": 298, "column": 44}}], "line": 292}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "b": {"0": [0, 0, 0, 0, 0, 0], "1": [0, 0, 0, 0, 0, 0], "2": [0, 0, 0, 0, 0, 0, 0, 0], "3": [0, 0, 0, 0, 0, 0], "4": [0, 0, 0, 0], "5": [0, 0, 0, 0, 0, 0], "6": [0, 0, 0, 0, 0, 0], "7": [0, 0, 0, 0, 0, 0, 0]}}, "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\validation.ts": {"path": "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\validation.ts", "statementMap": {"0": {"start": {"line": 13, "column": 20}, "end": {"line": 13, "column": 60}}, "1": {"start": {"line": 14, "column": 19}, "end": {"line": 14, "column": 57}}, "2": {"start": {"line": 15, "column": 26}, "end": {"line": 15, "column": 81}}, "3": {"start": {"line": 16, "column": 29}, "end": {"line": 16, "column": 65}}, "4": {"start": {"line": 19, "column": 32}, "end": {"line": 24, "column": 2}}, "5": {"start": {"line": 27, "column": 34}, "end": {"line": 32, "column": 2}}, "6": {"start": {"line": 34, "column": 34}, "end": {"line": 39, "column": 2}}, "7": {"start": {"line": 42, "column": 32}, "end": {"line": 48, "column": 2}}, "8": {"start": {"line": 50, "column": 32}, "end": {"line": 55, "column": 2}}, "9": {"start": {"line": 58, "column": 37}, "end": {"line": 66, "column": 2}}, "10": {"start": {"line": 69, "column": 35}, "end": {"line": 76, "column": 2}}, "11": {"start": {"line": 78, "column": 35}, "end": {"line": 85, "column": 2}}, "12": {"start": {"line": 88, "column": 41}, "end": {"line": 92, "column": 2}}, "13": {"start": {"line": 94, "column": 35}, "end": {"line": 100, "column": 2}}, "14": {"start": {"line": 103, "column": 29}, "end": {"line": 105, "column": 2}}, "15": {"start": {"line": 107, "column": 35}, "end": {"line": 109, "column": 2}}, "16": {"start": {"line": 116, "column": 2}, "end": {"line": 125, "column": 3}}, "17": {"start": {"line": 117, "column": 17}, "end": {"line": 117, "column": 37}}, "18": {"start": {"line": 118, "column": 26}, "end": {"line": 118, "column": 44}}, "19": {"start": {"line": 119, "column": 4}, "end": {"line": 119, "column": 50}}, "20": {"start": {"line": 121, "column": 4}, "end": {"line": 123, "column": 5}}, "21": {"start": {"line": 122, "column": 6}, "end": {"line": 122, "column": 72}}, "22": {"start": {"line": 124, "column": 4}, "end": {"line": 124, "column": 16}}, "23": {"start": {"line": 132, "column": 2}, "end": {"line": 157, "column": 3}}, "24": {"start": {"line": 134, "column": 40}, "end": {"line": 134, "column": 42}}, "25": {"start": {"line": 136, "column": 4}, "end": {"line": 148, "column": 5}}, "26": {"start": {"line": 138, "column": 6}, "end": {"line": 147, "column": 7}}, "27": {"start": {"line": 139, "column": 8}, "end": {"line": 139, "column": 27}}, "28": {"start": {"line": 140, "column": 13}, "end": {"line": 147, "column": 7}}, "29": {"start": {"line": 141, "column": 8}, "end": {"line": 141, "column": 28}}, "30": {"start": {"line": 142, "column": 13}, "end": {"line": 147, "column": 7}}, "31": {"start": {"line": 144, "column": 8}, "end": {"line": 144, "column": 36}}, "32": {"start": {"line": 146, "column": 8}, "end": {"line": 146, "column": 28}}, "33": {"start": {"line": 150, "column": 26}, "end": {"line": 150, "column": 46}}, "34": {"start": {"line": 151, "column": 4}, "end": {"line": 151, "column": 50}}, "35": {"start": {"line": 153, "column": 4}, "end": {"line": 155, "column": 5}}, "36": {"start": {"line": 154, "column": 6}, "end": {"line": 154, "column": 72}}, "37": {"start": {"line": 156, "column": 4}, "end": {"line": 156, "column": 16}}, "38": {"start": {"line": 164, "column": 2}, "end": {"line": 178, "column": 3}}, "39": {"start": {"line": 166, "column": 53}, "end": {"line": 166, "column": 55}}, "40": {"start": {"line": 167, "column": 4}, "end": {"line": 169, "column": 5}}, "41": {"start": {"line": 168, "column": 6}, "end": {"line": 168, "column": 70}}, "42": {"start": {"line": 171, "column": 26}, "end": {"line": 171, "column": 56}}, "43": {"start": {"line": 172, "column": 4}, "end": {"line": 172, "column": 50}}, "44": {"start": {"line": 174, "column": 4}, "end": {"line": 176, "column": 5}}, "45": {"start": {"line": 175, "column": 6}, "end": {"line": 175, "column": 72}}, "46": {"start": {"line": 177, "column": 4}, "end": {"line": 177, "column": 16}}, "47": {"start": {"line": 183, "column": 2}, "end": {"line": 183, "column": 43}}, "48": {"start": {"line": 187, "column": 41}, "end": {"line": 187, "column": 43}}, "49": {"start": {"line": 189, "column": 2}, "end": {"line": 197, "column": 3}}, "50": {"start": {"line": 190, "column": 4}, "end": {"line": 196, "column": 5}}, "51": {"start": {"line": 191, "column": 6}, "end": {"line": 191, "column": 45}}, "52": {"start": {"line": 192, "column": 11}, "end": {"line": 196, "column": 5}}, "53": {"start": {"line": 193, "column": 6}, "end": {"line": 193, "column": 45}}, "54": {"start": {"line": 195, "column": 6}, "end": {"line": 195, "column": 29}}, "55": {"start": {"line": 199, "column": 2}, "end": {"line": 199, "column": 19}}, "56": {"start": {"line": 203, "column": 31}, "end": {"line": 206, "column": 2}}, "57": {"start": {"line": 209, "column": 32}, "end": {"line": 213, "column": 2}}}, "fnMap": {"0": {"name": "validateRequestBody", "decl": {"start": {"line": 112, "column": 22}, "end": {"line": 112, "column": 41}}, "loc": {"start": {"line": 115, "column": 80}, "end": {"line": 126, "column": 1}}, "line": 115}, "1": {"name": "validateQueryParams", "decl": {"start": {"line": 128, "column": 16}, "end": {"line": 128, "column": 35}}, "loc": {"start": {"line": 131, "column": 71}, "end": {"line": 158, "column": 1}}, "line": 131}, "2": {"name": "validatePathParams", "decl": {"start": {"line": 160, "column": 16}, "end": {"line": 160, "column": 34}}, "loc": {"start": {"line": 163, "column": 71}, "end": {"line": 179, "column": 1}}, "line": 163}, "3": {"name": "sanitizeString", "decl": {"start": {"line": 182, "column": 16}, "end": {"line": 182, "column": 30}}, "loc": {"start": {"line": 182, "column": 54}, "end": {"line": 184, "column": 1}}, "line": 182}, "4": {"name": "sanitizeObject", "decl": {"start": {"line": 186, "column": 16}, "end": {"line": 186, "column": 30}}, "loc": {"start": {"line": 186, "column": 78}, "end": {"line": 200, "column": 1}}, "line": 186}}, "branchMap": {"0": {"loc": {"start": {"line": 121, "column": 4}, "end": {"line": 123, "column": 5}}, "type": "if", "locations": [{"start": {"line": 121, "column": 4}, "end": {"line": 123, "column": 5}}, {"start": {}, "end": {}}], "line": 121}, "1": {"loc": {"start": {"line": 138, "column": 6}, "end": {"line": 147, "column": 7}}, "type": "if", "locations": [{"start": {"line": 138, "column": 6}, "end": {"line": 147, "column": 7}}, {"start": {"line": 140, "column": 13}, "end": {"line": 147, "column": 7}}], "line": 138}, "2": {"loc": {"start": {"line": 140, "column": 13}, "end": {"line": 147, "column": 7}}, "type": "if", "locations": [{"start": {"line": 140, "column": 13}, "end": {"line": 147, "column": 7}}, {"start": {"line": 142, "column": 13}, "end": {"line": 147, "column": 7}}], "line": 140}, "3": {"loc": {"start": {"line": 142, "column": 13}, "end": {"line": 147, "column": 7}}, "type": "if", "locations": [{"start": {"line": 142, "column": 13}, "end": {"line": 147, "column": 7}}, {"start": {"line": 145, "column": 13}, "end": {"line": 147, "column": 7}}], "line": 142}, "4": {"loc": {"start": {"line": 142, "column": 17}, "end": {"line": 142, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 142, "column": 17}, "end": {"line": 142, "column": 38}}, {"start": {"line": 142, "column": 42}, "end": {"line": 142, "column": 54}}], "line": 142}, "5": {"loc": {"start": {"line": 153, "column": 4}, "end": {"line": 155, "column": 5}}, "type": "if", "locations": [{"start": {"line": 153, "column": 4}, "end": {"line": 155, "column": 5}}, {"start": {}, "end": {}}], "line": 153}, "6": {"loc": {"start": {"line": 168, "column": 30}, "end": {"line": 168, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 168, "column": 53}, "end": {"line": 168, "column": 61}}, {"start": {"line": 168, "column": 64}, "end": {"line": 168, "column": 69}}], "line": 168}, "7": {"loc": {"start": {"line": 174, "column": 4}, "end": {"line": 176, "column": 5}}, "type": "if", "locations": [{"start": {"line": 174, "column": 4}, "end": {"line": 176, "column": 5}}, {"start": {}, "end": {}}], "line": 174}, "8": {"loc": {"start": {"line": 190, "column": 4}, "end": {"line": 196, "column": 5}}, "type": "if", "locations": [{"start": {"line": 190, "column": 4}, "end": {"line": 196, "column": 5}}, {"start": {"line": 192, "column": 11}, "end": {"line": 196, "column": 5}}], "line": 190}, "9": {"loc": {"start": {"line": 192, "column": 11}, "end": {"line": 196, "column": 5}}, "type": "if", "locations": [{"start": {"line": 192, "column": 11}, "end": {"line": 196, "column": 5}}, {"start": {"line": 194, "column": 11}, "end": {"line": 196, "column": 5}}], "line": 192}, "10": {"loc": {"start": {"line": 192, "column": 15}, "end": {"line": 192, "column": 83}}, "type": "binary-expr", "locations": [{"start": {"line": 192, "column": 15}, "end": {"line": 192, "column": 40}}, {"start": {"line": 192, "column": 44}, "end": {"line": 192, "column": 58}}, {"start": {"line": 192, "column": 62}, "end": {"line": 192, "column": 83}}], "line": 192}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0, 0]}}}