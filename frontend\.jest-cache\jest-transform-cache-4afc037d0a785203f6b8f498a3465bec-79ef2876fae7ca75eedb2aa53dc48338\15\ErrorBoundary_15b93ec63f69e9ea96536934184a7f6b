d1fb931982845e4140436ce30d017b73
/**
 * Error Boundary Component
 * 
 * Catches JavaScript errors anywhere in the child component tree and displays a fallback UI.
 * Focused responsibility: Error handling and recovery for React components.
 */

'use client';

/* istanbul ignore next */
import _extends from "@babel/runtime/helpers/esm/extends";
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\ErrorBoundary.tsx";
var __jsx = React.createElement;
function cov_14g42dfi2b() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\ErrorBoundary.tsx";
  var hash = "11012c8b5b187eab194edf03954af4025c60a27e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\ErrorBoundary.tsx",
    statementMap: {
      "0": {
        start: {
          line: 33,
          column: 2
        },
        end: {
          line: 69,
          column: 3
        }
      },
      "1": {
        start: {
          line: 51,
          column: 27
        },
        end: {
          line: 51,
          column: 51
        }
      },
      "2": {
        start: {
          line: 73,
          column: 42
        },
        end: {
          line: 73,
          column: 46
        }
      },
      "3": {
        start: {
          line: 76,
          column: 4
        },
        end: {
          line: 76,
          column: 16
        }
      },
      "4": {
        start: {
          line: 77,
          column: 4
        },
        end: {
          line: 77,
          column: 36
        }
      },
      "5": {
        start: {
          line: 82,
          column: 4
        },
        end: {
          line: 82,
          column: 36
        }
      },
      "6": {
        start: {
          line: 87,
          column: 4
        },
        end: {
          line: 89,
          column: 5
        }
      },
      "7": {
        start: {
          line: 88,
          column: 6
        },
        end: {
          line: 88,
          column: 71
        }
      },
      "8": {
        start: {
          line: 92,
          column: 4
        },
        end: {
          line: 92,
          column: 32
        }
      },
      "9": {
        start: {
          line: 95,
          column: 4
        },
        end: {
          line: 95,
          column: 42
        }
      },
      "10": {
        start: {
          line: 98,
          column: 4
        },
        end: {
          line: 105,
          column: 5
        }
      },
      "11": {
        start: {
          line: 100,
          column: 6
        },
        end: {
          line: 104,
          column: 8
        }
      },
      "12": {
        start: {
          line: 109,
          column: 46
        },
        end: {
          line: 109,
          column: 56
        }
      },
      "13": {
        start: {
          line: 110,
          column: 25
        },
        end: {
          line: 110,
          column: 35
        }
      },
      "14": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 120,
          column: 5
        }
      },
      "15": {
        start: {
          line: 114,
          column: 28
        },
        end: {
          line: 114,
          column: 53
        }
      },
      "16": {
        start: {
          line: 115,
          column: 33
        },
        end: {
          line: 115,
          column: 93
        }
      },
      "17": {
        start: {
          line: 115,
          column: 64
        },
        end: {
          line: 115,
          column: 92
        }
      },
      "18": {
        start: {
          line: 117,
          column: 6
        },
        end: {
          line: 119,
          column: 7
        }
      },
      "19": {
        start: {
          line: 118,
          column: 8
        },
        end: {
          line: 118,
          column: 25
        }
      },
      "20": {
        start: {
          line: 124,
          column: 4
        },
        end: {
          line: 126,
          column: 5
        }
      },
      "21": {
        start: {
          line: 125,
          column: 6
        },
        end: {
          line: 125,
          column: 39
        }
      },
      "22": {
        start: {
          line: 129,
          column: 15
        },
        end: {
          line: 131,
          column: 3
        }
      },
      "23": {
        start: {
          line: 130,
          column: 4
        },
        end: {
          line: 130,
          column: 78
        }
      },
      "24": {
        start: {
          line: 134,
          column: 32
        },
        end: {
          line: 134,
          column: 42
        }
      },
      "25": {
        start: {
          line: 135,
          column: 74
        },
        end: {
          line: 135,
          column: 84
        }
      },
      "26": {
        start: {
          line: 137,
          column: 4
        },
        end: {
          line: 154,
          column: 5
        }
      },
      "27": {
        start: {
          line: 139,
          column: 6
        },
        end: {
          line: 145,
          column: 7
        }
      },
      "28": {
        start: {
          line: 140,
          column: 8
        },
        end: {
          line: 144,
          column: 9
        }
      },
      "29": {
        start: {
          line: 147,
          column: 6
        },
        end: {
          line: 153,
          column: 7
        }
      },
      "30": {
        start: {
          line: 156,
          column: 4
        },
        end: {
          line: 156,
          column: 19
        }
      },
      "31": {
        start: {
          line: 162,
          column: 28
        },
        end: {
          line: 162,
          column: 62
        }
      },
      "32": {
        start: {
          line: 164,
          column: 21
        },
        end: {
          line: 166,
          column: 8
        }
      },
      "33": {
        start: {
          line: 165,
          column: 4
        },
        end: {
          line: 165,
          column: 18
        }
      },
      "34": {
        start: {
          line: 168,
          column: 22
        },
        end: {
          line: 170,
          column: 8
        }
      },
      "35": {
        start: {
          line: 169,
          column: 4
        },
        end: {
          line: 169,
          column: 19
        }
      },
      "36": {
        start: {
          line: 173,
          column: 2
        },
        end: {
          line: 175,
          column: 3
        }
      },
      "37": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 174,
          column: 15
        }
      },
      "38": {
        start: {
          line: 177,
          column: 2
        },
        end: {
          line: 177,
          column: 36
        }
      },
      "39": {
        start: {
          line: 185,
          column: 27
        },
        end: {
          line: 189,
          column: 3
        }
      },
      "40": {
        start: {
          line: 186,
          column: 4
        },
        end: {
          line: 188,
          column: 20
        }
      },
      "41": {
        start: {
          line: 191,
          column: 2
        },
        end: {
          line: 191,
          column: 96
        }
      },
      "42": {
        start: {
          line: 193,
          column: 2
        },
        end: {
          line: 193,
          column: 25
        }
      }
    },
    fnMap: {
      "0": {
        name: "DefaultErrorFallback",
        decl: {
          start: {
            line: 32,
            column: 9
          },
          end: {
            line: 32,
            column: 29
          }
        },
        loc: {
          start: {
            line: 32,
            column: 89
          },
          end: {
            line: 70,
            column: 1
          }
        },
        line: 32
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 51,
            column: 21
          },
          end: {
            line: 51,
            column: 22
          }
        },
        loc: {
          start: {
            line: 51,
            column: 27
          },
          end: {
            line: 51,
            column: 51
          }
        },
        line: 51
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 75,
            column: 2
          },
          end: {
            line: 75,
            column: 3
          }
        },
        loc: {
          start: {
            line: 75,
            column: 41
          },
          end: {
            line: 78,
            column: 3
          }
        },
        line: 75
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 80,
            column: 2
          },
          end: {
            line: 80,
            column: 3
          }
        },
        loc: {
          start: {
            line: 80,
            column: 68
          },
          end: {
            line: 83,
            column: 3
          }
        },
        line: 80
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 85,
            column: 2
          },
          end: {
            line: 85,
            column: 3
          }
        },
        loc: {
          start: {
            line: 85,
            column: 56
          },
          end: {
            line: 106,
            column: 3
          }
        },
        line: 85
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 108,
            column: 2
          },
          end: {
            line: 108,
            column: 3
          }
        },
        loc: {
          start: {
            line: 108,
            column: 52
          },
          end: {
            line: 121,
            column: 3
          }
        },
        line: 108
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 115,
            column: 48
          },
          end: {
            line: 115,
            column: 49
          }
        },
        loc: {
          start: {
            line: 115,
            column: 64
          },
          end: {
            line: 115,
            column: 92
          }
        },
        line: 115
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 123,
            column: 2
          },
          end: {
            line: 123,
            column: 3
          }
        },
        loc: {
          start: {
            line: 123,
            column: 25
          },
          end: {
            line: 127,
            column: 3
          }
        },
        line: 123
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 129,
            column: 15
          },
          end: {
            line: 129,
            column: 16
          }
        },
        loc: {
          start: {
            line: 129,
            column: 21
          },
          end: {
            line: 131,
            column: 3
          }
        },
        line: 129
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 133,
            column: 2
          },
          end: {
            line: 133,
            column: 3
          }
        },
        loc: {
          start: {
            line: 133,
            column: 11
          },
          end: {
            line: 157,
            column: 3
          }
        },
        line: 133
      },
      "10": {
        name: "useErrorHandler",
        decl: {
          start: {
            line: 161,
            column: 16
          },
          end: {
            line: 161,
            column: 31
          }
        },
        loc: {
          start: {
            line: 161,
            column: 34
          },
          end: {
            line: 178,
            column: 1
          }
        },
        line: 161
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 164,
            column: 39
          },
          end: {
            line: 164,
            column: 40
          }
        },
        loc: {
          start: {
            line: 164,
            column: 45
          },
          end: {
            line: 166,
            column: 3
          }
        },
        line: 164
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 168,
            column: 40
          },
          end: {
            line: 168,
            column: 41
          }
        },
        loc: {
          start: {
            line: 168,
            column: 58
          },
          end: {
            line: 170,
            column: 3
          }
        },
        line: 168
      },
      "13": {
        name: "withErrorBoundary",
        decl: {
          start: {
            line: 181,
            column: 16
          },
          end: {
            line: 181,
            column: 33
          }
        },
        loc: {
          start: {
            line: 184,
            column: 2
          },
          end: {
            line: 194,
            column: 1
          }
        },
        line: 184
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 185,
            column: 27
          },
          end: {
            line: 185,
            column: 28
          }
        },
        loc: {
          start: {
            line: 186,
            column: 4
          },
          end: {
            line: 188,
            column: 20
          }
        },
        line: 186
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 32,
            column: 51
          },
          end: {
            line: 32,
            column: 65
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 32,
            column: 63
          },
          end: {
            line: 32,
            column: 65
          }
        }],
        line: 32
      },
      "1": {
        loc: {
          start: {
            line: 39,
            column: 11
          },
          end: {
            line: 39,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 11
          },
          end: {
            line: 39,
            column: 25
          }
        }, {
          start: {
            line: 39,
            column: 29
          },
          end: {
            line: 39,
            column: 59
          }
        }],
        line: 39
      },
      "2": {
        loc: {
          start: {
            line: 57,
            column: 9
          },
          end: {
            line: 66,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 57,
            column: 9
          },
          end: {
            line: 57,
            column: 47
          }
        }, {
          start: {
            line: 57,
            column: 51
          },
          end: {
            line: 57,
            column: 56
          }
        }, {
          start: {
            line: 58,
            column: 10
          },
          end: {
            line: 65,
            column: 20
          }
        }],
        line: 57
      },
      "3": {
        loc: {
          start: {
            line: 87,
            column: 4
          },
          end: {
            line: 89,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 87,
            column: 4
          },
          end: {
            line: 89,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 87
      },
      "4": {
        loc: {
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 105,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 105,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 98
      },
      "5": {
        loc: {
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 120,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 120,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 113
      },
      "6": {
        loc: {
          start: {
            line: 113,
            column: 8
          },
          end: {
            line: 113,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 113,
            column: 8
          },
          end: {
            line: 113,
            column: 16
          }
        }, {
          start: {
            line: 113,
            column: 20
          },
          end: {
            line: 113,
            column: 38
          }
        }, {
          start: {
            line: 113,
            column: 42
          },
          end: {
            line: 113,
            column: 51
          }
        }],
        line: 113
      },
      "7": {
        loc: {
          start: {
            line: 114,
            column: 28
          },
          end: {
            line: 114,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 114,
            column: 28
          },
          end: {
            line: 114,
            column: 47
          }
        }, {
          start: {
            line: 114,
            column: 51
          },
          end: {
            line: 114,
            column: 53
          }
        }],
        line: 114
      },
      "8": {
        loc: {
          start: {
            line: 117,
            column: 6
          },
          end: {
            line: 119,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 117,
            column: 6
          },
          end: {
            line: 119,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 117
      },
      "9": {
        loc: {
          start: {
            line: 124,
            column: 4
          },
          end: {
            line: 126,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 124,
            column: 4
          },
          end: {
            line: 126,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 124
      },
      "10": {
        loc: {
          start: {
            line: 135,
            column: 32
          },
          end: {
            line: 135,
            column: 46
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 135,
            column: 44
          },
          end: {
            line: 135,
            column: 46
          }
        }],
        line: 135
      },
      "11": {
        loc: {
          start: {
            line: 137,
            column: 4
          },
          end: {
            line: 154,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 137,
            column: 4
          },
          end: {
            line: 154,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 137
      },
      "12": {
        loc: {
          start: {
            line: 139,
            column: 6
          },
          end: {
            line: 145,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 139,
            column: 6
          },
          end: {
            line: 145,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 139
      },
      "13": {
        loc: {
          start: {
            line: 173,
            column: 2
          },
          end: {
            line: 175,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 173,
            column: 2
          },
          end: {
            line: 175,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 173
      },
      "14": {
        loc: {
          start: {
            line: 191,
            column: 54
          },
          end: {
            line: 191,
            column: 93
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 191,
            column: 54
          },
          end: {
            line: 191,
            column: 75
          }
        }, {
          start: {
            line: 191,
            column: 79
          },
          end: {
            line: 191,
            column: 93
          }
        }],
        line: 191
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "11012c8b5b187eab194edf03954af4025c60a27e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_14g42dfi2b = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_14g42dfi2b();
import React, { Component } from 'react';
function DefaultErrorFallback({
  error,
  resetError,
  className =
  /* istanbul ignore next */
  (cov_14g42dfi2b().b[0][0]++, '')
}) {
  /* istanbul ignore next */
  cov_14g42dfi2b().f[0]++;
  cov_14g42dfi2b().s[0]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: `error-boundary ${className}`,
    role: "alert",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 34,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-center py-8",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 35,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-4xl mb-4",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 36,
      columnNumber: 9
    }
  }, "\u26A0\uFE0F"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h2",
  /* istanbul ignore next */
  {
    className: "text-lg font-medium mb-2",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 37,
      columnNumber: 9
    }
  }, "Something went wrong"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-secondary mb-4",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 38,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  (cov_14g42dfi2b().b[1][0]++, error?.message) ||
  /* istanbul ignore next */
  (cov_14g42dfi2b().b[1][1]++, 'An unexpected error occurred')),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "space-x-2",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 41,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  {
    className: "btn btn-primary",
    onClick: resetError,
    /* istanbul ignore next */
    "aria-label": "Try again",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 42,
      columnNumber: 11
    }
  }, "Try Again"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  {
    className: "btn btn-secondary",
    onClick: () => {
      /* istanbul ignore next */
      cov_14g42dfi2b().f[1]++;
      cov_14g42dfi2b().s[1]++;
      return window.location.reload();
    },
    /* istanbul ignore next */
    "aria-label": "Reload page",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 49,
      columnNumber: 11
    }
  }, "Reload Page")),
  /* istanbul ignore next */
  (cov_14g42dfi2b().b[2][0]++, process.env.NODE_ENV === 'development') &&
  /* istanbul ignore next */
  (cov_14g42dfi2b().b[2][1]++, error) &&
  /* istanbul ignore next */
  (cov_14g42dfi2b().b[2][2]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "details",
  /* istanbul ignore next */
  {
    className: "mt-4 text-left",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 58,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "summary",
  /* istanbul ignore next */
  {
    className: "cursor-pointer text-sm text-secondary",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 59,
      columnNumber: 13
    }
  }, "Error Details (Development)"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "pre",
  /* istanbul ignore next */
  {
    className: "mt-2 p-4 bg-gray-100 rounded text-xs overflow-auto",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 62,
      columnNumber: 13
    }
  }, error.stack)))));
}
export default class ErrorBoundary extends Component {
  resetTimeoutId =
  /* istanbul ignore next */
  (cov_14g42dfi2b().s[2]++, null);
  constructor(props) {
    /* istanbul ignore next */
    cov_14g42dfi2b().f[2]++;
    cov_14g42dfi2b().s[3]++;
    super(props);
    /* istanbul ignore next */
    cov_14g42dfi2b().s[4]++;
    this.state = {
      hasError: false
    };
  }
  static getDerivedStateFromError(error) {
    /* istanbul ignore next */
    cov_14g42dfi2b().f[3]++;
    cov_14g42dfi2b().s[5]++;
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error
    };
  }
  componentDidCatch(error, errorInfo) {
    /* istanbul ignore next */
    cov_14g42dfi2b().f[4]++;
    cov_14g42dfi2b().s[6]++;
    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      /* istanbul ignore next */
      cov_14g42dfi2b().b[3][0]++;
      cov_14g42dfi2b().s[7]++;
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    } else
    /* istanbul ignore next */
    {
      cov_14g42dfi2b().b[3][1]++;
    }

    // Update state with error info
    cov_14g42dfi2b().s[8]++;
    this.setState({
      errorInfo
    });

    // Call custom error handler if provided
    /* istanbul ignore next */
    cov_14g42dfi2b().s[9]++;
    this.props.onError?.(error, errorInfo);

    // Report error to monitoring service in production
    /* istanbul ignore next */
    cov_14g42dfi2b().s[10]++;
    if (process.env.NODE_ENV === 'production') {
      /* istanbul ignore next */
      cov_14g42dfi2b().b[4][0]++;
      cov_14g42dfi2b().s[11]++;
      // TODO: Integrate with error monitoring service (e.g., Sentry)
      console.error('Production error:', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack
      });
    } else
    /* istanbul ignore next */
    {
      cov_14g42dfi2b().b[4][1]++;
    }
  }
  componentDidUpdate(prevProps) {
    /* istanbul ignore next */
    cov_14g42dfi2b().f[5]++;
    const {
      resetOnPropsChange,
      resetKeys
    } =
    /* istanbul ignore next */
    (cov_14g42dfi2b().s[12]++, this.props);
    const {
      hasError
    } =
    /* istanbul ignore next */
    (cov_14g42dfi2b().s[13]++, this.state);

    // Reset error state when resetKeys change
    /* istanbul ignore next */
    cov_14g42dfi2b().s[14]++;
    if (
    /* istanbul ignore next */
    (cov_14g42dfi2b().b[6][0]++, hasError) &&
    /* istanbul ignore next */
    (cov_14g42dfi2b().b[6][1]++, resetOnPropsChange) &&
    /* istanbul ignore next */
    (cov_14g42dfi2b().b[6][2]++, resetKeys)) {
      /* istanbul ignore next */
      cov_14g42dfi2b().b[5][0]++;
      const prevResetKeys =
      /* istanbul ignore next */
      (cov_14g42dfi2b().s[15]++,
      /* istanbul ignore next */
      (cov_14g42dfi2b().b[7][0]++, prevProps.resetKeys) ||
      /* istanbul ignore next */
      (cov_14g42dfi2b().b[7][1]++, []));
      const hasResetKeyChanged =
      /* istanbul ignore next */
      (cov_14g42dfi2b().s[16]++, resetKeys.some((key, index) => {
        /* istanbul ignore next */
        cov_14g42dfi2b().f[6]++;
        cov_14g42dfi2b().s[17]++;
        return key !== prevResetKeys[index];
      }));
      /* istanbul ignore next */
      cov_14g42dfi2b().s[18]++;
      if (hasResetKeyChanged) {
        /* istanbul ignore next */
        cov_14g42dfi2b().b[8][0]++;
        cov_14g42dfi2b().s[19]++;
        this.resetError();
      } else
      /* istanbul ignore next */
      {
        cov_14g42dfi2b().b[8][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_14g42dfi2b().b[5][1]++;
    }
  }
  componentWillUnmount() {
    /* istanbul ignore next */
    cov_14g42dfi2b().f[7]++;
    cov_14g42dfi2b().s[20]++;
    if (this.resetTimeoutId) {
      /* istanbul ignore next */
      cov_14g42dfi2b().b[9][0]++;
      cov_14g42dfi2b().s[21]++;
      clearTimeout(this.resetTimeoutId);
    } else
    /* istanbul ignore next */
    {
      cov_14g42dfi2b().b[9][1]++;
    }
  }
  resetError =
  /* istanbul ignore next */
  (cov_14g42dfi2b().s[22]++, () => {
    /* istanbul ignore next */
    cov_14g42dfi2b().f[8]++;
    cov_14g42dfi2b().s[23]++;
    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined
    });
  });
  render() {
    /* istanbul ignore next */
    cov_14g42dfi2b().f[9]++;
    const {
      hasError,
      error
    } =
    /* istanbul ignore next */
    (cov_14g42dfi2b().s[24]++, this.state);
    const {
      children,
      fallback,
      className =
      /* istanbul ignore next */
      (cov_14g42dfi2b().b[10][0]++, ''),
      'data-testid': testId
    } =
    /* istanbul ignore next */
    (cov_14g42dfi2b().s[25]++, this.props);
    /* istanbul ignore next */
    cov_14g42dfi2b().s[26]++;
    if (hasError) {
      /* istanbul ignore next */
      cov_14g42dfi2b().b[11][0]++;
      cov_14g42dfi2b().s[27]++;
      // Render custom fallback or default error UI
      if (fallback) {
        /* istanbul ignore next */
        cov_14g42dfi2b().b[12][0]++;
        cov_14g42dfi2b().s[28]++;
        return /* istanbul ignore next */__jsx(
        /* istanbul ignore next */
        "div",
        /* istanbul ignore next */
        {
          className: className,
          /* istanbul ignore next */
          "data-testid": testId,
          __self: this,
          __source: {
            fileName: _jsxFileName,
            lineNumber: 141,
            columnNumber: 11
          }
        }, fallback);
      } else
      /* istanbul ignore next */
      {
        cov_14g42dfi2b().b[12][1]++;
      }
      cov_14g42dfi2b().s[29]++;
      return /* istanbul ignore next */__jsx(DefaultErrorFallback,
      /* istanbul ignore next */
      {
        error: error,
        resetError: this.resetError,
        className: className,
        __self: this,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 148,
          columnNumber: 9
        }
      });
    } else
    /* istanbul ignore next */
    {
      cov_14g42dfi2b().b[11][1]++;
    }
    cov_14g42dfi2b().s[30]++;
    return children;
  }
}

// Hook version for functional components
export function useErrorHandler() {
  /* istanbul ignore next */
  cov_14g42dfi2b().f[10]++;
  const [error, setError] =
  /* istanbul ignore next */
  (cov_14g42dfi2b().s[31]++, React.useState(null));
  const resetError =
  /* istanbul ignore next */
  (cov_14g42dfi2b().s[32]++, React.useCallback(() => {
    /* istanbul ignore next */
    cov_14g42dfi2b().f[11]++;
    cov_14g42dfi2b().s[33]++;
    setError(null);
  }, []));
  const handleError =
  /* istanbul ignore next */
  (cov_14g42dfi2b().s[34]++, React.useCallback(error => {
    /* istanbul ignore next */
    cov_14g42dfi2b().f[12]++;
    cov_14g42dfi2b().s[35]++;
    setError(error);
  }, []));

  // Throw error to be caught by ErrorBoundary
  /* istanbul ignore next */
  cov_14g42dfi2b().s[36]++;
  if (error) {
    /* istanbul ignore next */
    cov_14g42dfi2b().b[13][0]++;
    cov_14g42dfi2b().s[37]++;
    throw error;
  } else
  /* istanbul ignore next */
  {
    cov_14g42dfi2b().b[13][1]++;
  }
  cov_14g42dfi2b().s[38]++;
  return {
    handleError,
    resetError
  };
}

// Higher-order component for wrapping components with error boundary
export function withErrorBoundary(Component, errorBoundaryProps) {
  /* istanbul ignore next */
  cov_14g42dfi2b().f[13]++;
  cov_14g42dfi2b().s[39]++;
  const WrappedComponent = props => {
    /* istanbul ignore next */
    cov_14g42dfi2b().f[14]++;
    cov_14g42dfi2b().s[40]++;
    return /* istanbul ignore next */__jsx(ErrorBoundary,
    /* istanbul ignore next */
    _extends({}, errorBoundaryProps, {
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 186,
        columnNumber: 5
      }
    }),
    /* istanbul ignore next */
    __jsx(Component,
    /* istanbul ignore next */
    _extends({}, props, {
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 187,
        columnNumber: 7
      }
    })));
  };
  /* istanbul ignore next */
  cov_14g42dfi2b().s[41]++;
  WrappedComponent.displayName = `withErrorBoundary(${
  /* istanbul ignore next */
  (cov_14g42dfi2b().b[14][0]++, Component.displayName) ||
  /* istanbul ignore next */
  (cov_14g42dfi2b().b[14][1]++, Component.name)})`;
  /* istanbul ignore next */
  cov_14g42dfi2b().s[42]++;
  return WrappedComponent;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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