/**
 * Dashboard Data Hook
 * 
 * Custom hook for managing dashboard data fetching, caching, and state management.
 * Focused responsibility: Data layer for dashboard components.
 */

'use client'

import { useState, useEffect, useCallback, useRef, useMemo } from 'react'
import { useTenant } from '@/contexts/AppContext'
import { DashboardStats, Renewal } from '@/lib/types'
import { isSuccessResponse, isErrorResponse } from '@/lib/type-utils'
import { apiCache, cacheUtils } from '@/lib/cache'
import { useDebounce, performanceUtils } from '@/lib/performance'

interface DashboardData {
  stats: DashboardStats
  recentRenewals: Renewal[]
  upcomingRenewals: Renewal[]
}

interface UseDashboardDataReturn {
  data: DashboardData
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
  refetchStats: () => Promise<void>
  refetchRenewals: () => Promise<void>
}

interface FetchOptions {
  signal?: AbortSignal
  timeout?: number
}

// Default data
const defaultStats: DashboardStats = {
  totalRenewals: 0,
  renewalsDue: 0,
  vendors: 0,
  annualSpend: '$0'
}

const defaultRenewals: Renewal[] = []

// Cache configuration - now using advanced cache
const CACHE_TTL = 5 * 60 * 1000 // 5 minutes
const CACHE_TAGS = {
  STATS: 'dashboard-stats',
  RENEWALS: 'dashboard-renewals',
} as const

// Fetch with timeout and error handling
async function fetchWithTimeout(url: string, options: FetchOptions = {}): Promise<Response> {
  const { signal, timeout = 10000 } = options
  
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), timeout)
  
  try {
    const response = await fetch(url, {
      signal: signal || controller.signal,
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
    clearTimeout(timeoutId)
    return response
  } catch (error) {
    clearTimeout(timeoutId)
    throw error
  }
}

export function useDashboardData(): UseDashboardDataReturn {
  const { tenant, loading: tenantLoading, error: tenantError } = useTenant()

  const [data, setData] = useState<DashboardData>({
    stats: defaultStats,
    recentRenewals: defaultRenewals,
    upcomingRenewals: defaultRenewals
  })

  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Use ref to track if component is mounted
  const isMountedRef = useRef(true)
  const abortControllerRef = useRef<AbortController | null>(null)

  // Debounce tenant changes to prevent excessive API calls
  const debouncedTenant = useDebounce(tenant, 300)

  // Memoize cache keys to prevent recreation
  const cacheKeys = useMemo(() => {
    if (!debouncedTenant) return null
    return {
      stats: cacheUtils.createKey('dashboard-stats', debouncedTenant.tenantId),
      renewals: cacheUtils.createKey('dashboard-renewals', debouncedTenant.tenantId),
    }
  }, [debouncedTenant?.tenantId])

  // Fetch dashboard stats with advanced caching
  const fetchStats = useCallback(async (options: FetchOptions = {}): Promise<DashboardStats> => {
    if (!tenant) throw new Error('Tenant not available')

    performanceUtils.mark('fetchStats')

    const cacheKey = cacheUtils.createKey('dashboard-stats', tenant.tenantId)
    const cached = apiCache.get(cacheKey)
    if (cached) {
      performanceUtils.measure('fetchStats')
      return cached
    }

    const response = await fetchWithTimeout('/api/dashboard/stats', options)

    if (!response.ok) {
      throw new Error(`Failed to fetch stats: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()

    if (isSuccessResponse(result)) {
      apiCache.set(cacheKey, result.data, CACHE_TTL, [CACHE_TAGS.STATS, tenant.tenantId])
      performanceUtils.measure('fetchStats')
      return result.data
    } else if (isErrorResponse(result)) {
      throw new Error(result.error)
    } else {
      // Fallback for legacy API responses
      apiCache.set(cacheKey, result, CACHE_TTL, [CACHE_TAGS.STATS, tenant.tenantId])
      performanceUtils.measure('fetchStats')
      return result
    }
  }, [tenant])

  // Fetch recent renewals with advanced caching
  const fetchRenewals = useCallback(async (options: FetchOptions = {}): Promise<Renewal[]> => {
    if (!tenant) throw new Error('Tenant not available')

    performanceUtils.mark('fetchRenewals')

    const cacheKey = cacheUtils.createKey('dashboard-renewals', tenant.tenantId)
    const cached = apiCache.get(cacheKey)
    if (cached) {
      performanceUtils.measure('fetchRenewals')
      return cached
    }

    const response = await fetchWithTimeout('/api/dashboard/renewals', options)

    if (!response.ok) {
      throw new Error(`Failed to fetch renewals: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()

    if (isSuccessResponse(result)) {
      apiCache.set(cacheKey, result.data, CACHE_TTL, [CACHE_TAGS.RENEWALS, tenant.tenantId])
      performanceUtils.measure('fetchRenewals')
      return result.data
    } else if (isErrorResponse(result)) {
      throw new Error(result.error)
    } else {
      // Fallback for legacy API responses
      apiCache.set(cacheKey, result, CACHE_TTL, [CACHE_TAGS.RENEWALS, tenant.tenantId])
      performanceUtils.measure('fetchRenewals')
      return result
    }
  }, [tenant])

  // Fetch all dashboard data
  const fetchDashboardData = useCallback(async (): Promise<void> => {
    if (!tenant || !isMountedRef.current) return
    
    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    
    abortControllerRef.current = new AbortController()
    const signal = abortControllerRef.current.signal
    
    setIsLoading(true)
    setError(null)
    
    try {
      const [statsData, renewalsData] = await Promise.allSettled([
        fetchStats({ signal }),
        fetchRenewals({ signal })
      ])
      
      if (!isMountedRef.current) return
      
      const newData: DashboardData = {
        stats: statsData.status === 'fulfilled' ? statsData.value : defaultStats,
        recentRenewals: renewalsData.status === 'fulfilled' ? renewalsData.value : defaultRenewals,
        upcomingRenewals: renewalsData.status === 'fulfilled' ? renewalsData.value : defaultRenewals
      }
      
      setData(newData)
      
      // Log any errors but don't fail the entire operation
      if (statsData.status === 'rejected') {
        console.error('Failed to fetch stats:', statsData.reason)
      }
      if (renewalsData.status === 'rejected') {
        console.error('Failed to fetch renewals:', renewalsData.reason)
      }
      
    } catch (err) {
      if (!isMountedRef.current) return
      
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch dashboard data'
      setError(errorMessage)
      console.error('Dashboard data fetch error:', err)
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false)
      }
    }
  }, [tenant, fetchStats, fetchRenewals])

  // Individual refetch functions
  const refetchStats = useCallback(async (): Promise<void> => {
    if (!tenant) return
    
    try {
      const stats = await fetchStats()
      if (isMountedRef.current) {
        setData(prev => ({ ...prev, stats }))
      }
    } catch (err) {
      console.error('Failed to refetch stats:', err)
    }
  }, [tenant, fetchStats])

  const refetchRenewals = useCallback(async (): Promise<void> => {
    if (!tenant) return
    
    try {
      const renewals = await fetchRenewals()
      if (isMountedRef.current) {
        setData(prev => ({ 
          ...prev, 
          recentRenewals: renewals,
          upcomingRenewals: renewals
        }))
      }
    } catch (err) {
      console.error('Failed to refetch renewals:', err)
    }
  }, [tenant, fetchRenewals])

  // Main effect to fetch data when tenant changes
  useEffect(() => {
    if (tenant && !tenantLoading && !tenantError) {
      fetchDashboardData()
    } else if (tenantError) {
      setError(tenantError)
    }
  }, [tenant, tenantLoading, tenantError, fetchDashboardData])

  // Cleanup effect
  useEffect(() => {
    return () => {
      isMountedRef.current = false
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  return {
    data,
    isLoading: isLoading || tenantLoading,
    error: error || tenantError,
    refetch: fetchDashboardData,
    refetchStats,
    refetchRenewals
  }
}
