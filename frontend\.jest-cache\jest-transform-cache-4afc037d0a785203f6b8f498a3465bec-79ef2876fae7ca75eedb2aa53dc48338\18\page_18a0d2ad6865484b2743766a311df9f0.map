{"version": 3, "names": ["_jsxFileName", "React", "__jsx", "createElement", "cov_e9mg3q03f", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "_coverageSchema", "coverage", "actualCoverage", "useState", "useCallback", "useMemo", "useTenant", "useDashboardData", "usePerformanceMonitor", "useDebounce", "DashboardHeader", "DashboardStats", "Recent<PERSON><PERSON><PERSON><PERSON>", "UpcomingRenewals", "ScanResults", "Error<PERSON>ou<PERSON><PERSON>", "LoadingPage", "LazySection", "DashboardPage", "tenant", "loading", "tenantLoading", "error", "tenantError", "data", "isLoading", "refetch", "searchQuery", "setSearch<PERSON>uery", "debounced<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleSearch", "query", "console", "log", "handleAddRenewal", "handleRenewalClick", "renewal", "handleRunScan", "handleScanResultClick", "result", "mockScanResults", "id", "status", "lastSeen", "Date", "details", "title", "subtitle", "icon", "__self", "__source", "fileName", "lineNumber", "columnNumber", "className", "onClick", "onError", "errorInfo", "resetKeys", "clientId", "clientName", "onSearch", "onAddRenewal", "searchPlaceholder", "stats", "placeholder", "renewals", "upcoming<PERSON><PERSON><PERSON><PERSON>", "onRenewalClick", "<PERSON>T<PERSON><PERSON><PERSON>", "recentRenewals", "maxItems", "results", "onRunScan", "onResultClick", "lastScanDate"], "sources": ["page.tsx"], "sourcesContent": ["\r\n/**\r\n * Dashboard Page - Modular and Optimized\r\n *\r\n * This page demonstrates the new modular architecture with:\r\n * - Separated concerns into focused components\r\n * - Custom hooks for data management\r\n * - Error boundaries for resilience\r\n * - Proper loading states\r\n * - Type safety throughout\r\n */\r\n\r\n'use client'\r\n\r\nimport { useState, useCallback, useMemo } from 'react'\r\nimport { useTenant } from '@/contexts/AppContext'\r\nimport { useDashboardData } from '@/hooks/useDashboardData'\r\nimport { usePerformanceMonitor, useDebounce } from '@/lib/performance'\r\n\r\n// Modular dashboard components\r\nimport DashboardHeader from '@/components/dashboard/DashboardHeader'\r\nimport DashboardStats from '@/components/dashboard/DashboardStats'\r\nimport RecentRenewals from '@/components/dashboard/RecentRenewals'\r\nimport UpcomingRenewals from '@/components/dashboard/UpcomingRenewals'\r\nimport ScanResults from '@/components/dashboard/ScanResults'\r\n\r\n// Common components\r\nimport ErrorBoundary from '@/components/common/ErrorBoundary'\r\nimport { LoadingPage } from '@/components/common/LoadingStates'\r\nimport { LazySection } from '@/components/common/LazyLoad'\r\n\r\n// Types\r\nimport { Renewal } from '@/lib/types'\r\n\r\n\r\n\r\nexport default function DashboardPage() {\r\n  const { tenant, loading: tenantLoading, error: tenantError } = useTenant()\r\n  const { data, isLoading, error, refetch } = useDashboardData()\r\n\r\n  // Performance monitoring\r\n  usePerformanceMonitor('DashboardPage')\r\n\r\n  const [searchQuery, setSearchQuery] = useState('')\r\n\r\n  // Debounce search to prevent excessive API calls\r\n  const debouncedSearchQuery = useDebounce(searchQuery, 300)\r\n\r\n  // Memoized event handlers to prevent unnecessary re-renders\r\n  const handleSearch = useCallback((query: string) => {\r\n    setSearchQuery(query)\r\n    // TODO: Implement search functionality\r\n    console.log('Searching for:', query)\r\n  }, [])\r\n\r\n  const handleAddRenewal = useCallback(() => {\r\n    // TODO: Implement add renewal functionality\r\n    console.log('Add renewal clicked')\r\n  }, [])\r\n\r\n  const handleRenewalClick = useCallback((renewal: Renewal) => {\r\n    // TODO: Navigate to renewal details\r\n    console.log('Renewal clicked:', renewal)\r\n  }, [])\r\n\r\n  const handleRunScan = useCallback(() => {\r\n    // TODO: Implement scan functionality\r\n    console.log('Run scan clicked')\r\n  }, [])\r\n\r\n  const handleScanResultClick = useCallback((result: any) => {\r\n    // TODO: Handle scan result click\r\n    console.log('Scan result clicked:', result)\r\n  }, [])\r\n\r\n  // Memoize mock scan results to prevent recreation\r\n  const mockScanResults = useMemo(() => [\r\n    {\r\n      id: '1',\r\n      type: 'software' as const,\r\n      name: 'Microsoft Office 365',\r\n      status: 'found' as const,\r\n      lastSeen: new Date('2025-01-15'),\r\n      details: 'License expires in 30 days'\r\n    },\r\n    {\r\n      id: '2',\r\n      type: 'license' as const,\r\n      name: 'Adobe Creative Suite',\r\n      status: 'warning' as const,\r\n      lastSeen: new Date('2025-01-10'),\r\n      details: 'Usage exceeds license limit'\r\n    },\r\n    {\r\n      id: '3',\r\n      type: 'renewal' as const,\r\n      name: 'Slack Premium',\r\n      status: 'expired' as const,\r\n      lastSeen: new Date('2025-01-01'),\r\n      details: 'Renewal overdue by 15 days'\r\n    }\r\n  ], [])\r\n\r\n  // Show loading state\r\n  if (tenantLoading || isLoading) {\r\n    return (\r\n      <LoadingPage\r\n        title=\"Loading Dashboard...\"\r\n        subtitle=\"Please wait while we set up your dashboard.\"\r\n        icon=\"⏳\"\r\n      />\r\n    )\r\n  }\r\n\r\n  // Show error state\r\n  if (tenantError || error) {\r\n    return (\r\n      <div className=\"dashboard-container\">\r\n        <div className=\"text-center py-8\">\r\n          <div className=\"text-4xl mb-4\">❌</div>\r\n          <h3 className=\"text-lg font-medium mb-2\">Error Loading Dashboard</h3>\r\n          <p className=\"text-secondary mb-4\">{tenantError || error}</p>\r\n          <button\r\n            className=\"btn btn-primary\"\r\n            onClick={() => refetch()}\r\n          >\r\n            Retry\r\n          </button>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <ErrorBoundary\r\n      onError={(error, errorInfo) => {\r\n        console.error('Dashboard error:', error, errorInfo)\r\n      }}\r\n      resetKeys={[tenant?.clientId]}\r\n    >\r\n      <div className=\"dashboard-container\">\r\n        {/* Header Section */}\r\n        <DashboardHeader\r\n          clientName={tenant?.clientName}\r\n          onSearch={handleSearch}\r\n          onAddRenewal={handleAddRenewal}\r\n          searchPlaceholder=\"Search renewals...\"\r\n        />\r\n\r\n        {/* Statistics Section */}\r\n        <DashboardStats\r\n          stats={data.stats}\r\n          isLoading={isLoading}\r\n          className=\"mb-6\"\r\n        />\r\n\r\n        {/* Upcoming Renewals Section - Lazy loaded */}\r\n        <LazySection\r\n          placeholder={<div className=\"h-32 bg-gray-100 animate-pulse rounded-lg mb-6\" />}\r\n          className=\"mb-6\"\r\n        >\r\n          <UpcomingRenewals\r\n            renewals={data.upcomingRenewals}\r\n            isLoading={isLoading}\r\n            onRenewalClick={handleRenewalClick}\r\n            daysThreshold={30}\r\n          />\r\n        </LazySection>\r\n\r\n        {/* Bottom Section with Recent Renewals and Scan Results - Lazy loaded */}\r\n        <LazySection\r\n          placeholder={\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n              <div className=\"h-64 bg-gray-100 animate-pulse rounded-lg\" />\r\n              <div className=\"h-64 bg-gray-100 animate-pulse rounded-lg\" />\r\n            </div>\r\n          }\r\n        >\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n            <RecentRenewals\r\n              renewals={data.recentRenewals}\r\n              isLoading={isLoading}\r\n              onRenewalClick={handleRenewalClick}\r\n              maxItems={5}\r\n            />\r\n\r\n            <ScanResults\r\n              results={mockScanResults}\r\n              isLoading={false}\r\n              onRunScan={handleRunScan}\r\n              onResultClick={handleScanResultClick}\r\n              lastScanDate={new Date()}\r\n            />\r\n          </div>\r\n        </LazySection>\r\n      </div>\r\n    </ErrorBoundary>\r\n  )\r\n}\r\n\r\n"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAAA;AAAA,IAAAA,YAAA;AAAA,OAAAC,KAAA;AAAA,IAAAC,KAAA,GAAAD,KAAA,CAAAE,aAAA;AAAA,SAAAC,cAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,eAAA;IAAArB,IAAA;EAAA;EAAA,IAAAsB,QAAA,GAAArB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAmB,QAAA,CAAAvB,IAAA,KAAAuB,QAAA,CAAAvB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAsB,QAAA,CAAAvB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAmB,cAAA,GAAAD,QAAA,CAAAvB,IAAA;EAAA;IAGA;IAAAD,aAAA,YAAAA,CAAA;MAAA,OAAAyB,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAzB,aAAA;AADZ,SAAS0B,QAAQ,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACtD,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,qBAAqB,EAAEC,WAAW,QAAQ,mBAAmB;;AAEtE;AACA,OAAOC,eAAe,MAAM,wCAAwC;AACpE,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,gBAAgB,MAAM,yCAAyC;AACtE,OAAOC,WAAW,MAAM,oCAAoC;;AAE5D;AACA,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,WAAW,QAAQ,8BAA8B;;AAE1D;;AAKA,eAAe,SAASC,aAAaA,CAAA,EAAG;EAAA;EAAAzC,aAAA,GAAAqB,CAAA;EACtC,MAAM;IAAEqB,MAAM;IAAEC,OAAO,EAAEC,aAAa;IAAEC,KAAK,EAAEC;EAAY,CAAC;EAAA;EAAA,CAAA9C,aAAA,GAAAoB,CAAA,OAAGS,SAAS,CAAC,CAAC;EAC1E,MAAM;IAAEkB,IAAI;IAAEC,SAAS;IAAEH,KAAK;IAAEI;EAAQ,CAAC;EAAA;EAAA,CAAAjD,aAAA,GAAAoB,CAAA,OAAGU,gBAAgB,CAAC,CAAC;;EAE9D;EAAA;EAAA9B,aAAA,GAAAoB,CAAA;EACAW,qBAAqB,CAAC,eAAe,CAAC;EAEtC,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC;EAAA;EAAA,CAAAnD,aAAA,GAAAoB,CAAA,OAAGM,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM0B,oBAAoB;EAAA;EAAA,CAAApD,aAAA,GAAAoB,CAAA,OAAGY,WAAW,CAACkB,WAAW,EAAE,GAAG,CAAC;;EAE1D;EACA,MAAMG,YAAY;EAAA;EAAA,CAAArD,aAAA,GAAAoB,CAAA,OAAGO,WAAW,CAAE2B,KAAa,IAAK;IAAA;IAAAtD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAClD+B,cAAc,CAACG,KAAK,CAAC;IACrB;IAAA;IAAAtD,aAAA,GAAAoB,CAAA;IACAmC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEF,KAAK,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,gBAAgB;EAAA;EAAA,CAAAzD,aAAA,GAAAoB,CAAA,OAAGO,WAAW,CAAC,MAAM;IAAA;IAAA3B,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACzC;IACAmC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,kBAAkB;EAAA;EAAA,CAAA1D,aAAA,GAAAoB,CAAA,QAAGO,WAAW,CAAEgC,OAAgB,IAAK;IAAA;IAAA3D,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC3D;IACAmC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEG,OAAO,CAAC;EAC1C,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,aAAa;EAAA;EAAA,CAAA5D,aAAA,GAAAoB,CAAA,QAAGO,WAAW,CAAC,MAAM;IAAA;IAAA3B,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACtC;IACAmC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;EACjC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,qBAAqB;EAAA;EAAA,CAAA7D,aAAA,GAAAoB,CAAA,QAAGO,WAAW,CAAEmC,MAAW,IAAK;IAAA;IAAA9D,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACzD;IACAmC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEM,MAAM,CAAC;EAC7C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,eAAe;EAAA;EAAA,CAAA/D,aAAA,GAAAoB,CAAA,QAAGQ,OAAO,CAAC,MAAM;IAAA;IAAA5B,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAAA,QACpC;MACE4C,EAAE,EAAE,GAAG;MACP/C,IAAI,EAAE,UAAmB;MACzBJ,IAAI,EAAE,sBAAsB;MAC5BoD,MAAM,EAAE,OAAgB;MACxBC,QAAQ,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MAChCC,OAAO,EAAE;IACX,CAAC,EACD;MACEJ,EAAE,EAAE,GAAG;MACP/C,IAAI,EAAE,SAAkB;MACxBJ,IAAI,EAAE,sBAAsB;MAC5BoD,MAAM,EAAE,SAAkB;MAC1BC,QAAQ,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MAChCC,OAAO,EAAE;IACX,CAAC,EACD;MACEJ,EAAE,EAAE,GAAG;MACP/C,IAAI,EAAE,SAAkB;MACxBJ,IAAI,EAAE,eAAe;MACrBoD,MAAM,EAAE,SAAkB;MAC1BC,QAAQ,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MAChCC,OAAO,EAAE;IACX,CAAC,CACF;EAAD,CAAC,EAAE,EAAE,CAAC;;EAEN;EAAA;EAAApE,aAAA,GAAAoB,CAAA;EACA;EAAI;EAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAsB,aAAa;EAAA;EAAA,CAAA5C,aAAA,GAAAsB,CAAA,UAAI0B,SAAS,GAAE;IAAA;IAAAhD,aAAA,GAAAsB,CAAA;IAAAtB,aAAA,GAAAoB,CAAA;IAC9B,OACE,0BAAAtB,KAAA,CAACyC,WAAW;IAAA;IAAA;MACV8B,KAAK,EAAC,sBAAsB;MAC5BC,QAAQ,EAAC,6CAA6C;MACtDC,IAAI,EAAC,QAAG;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAA9E,YAAA;QAAA+E,UAAA;QAAAC,YAAA;MAAA;IAAA,CACT,CAAC;EAEN,CAAC;EAAA;EAAA;IAAA5E,aAAA,GAAAsB,CAAA;EAAA;;EAED;EAAAtB,aAAA,GAAAoB,CAAA;EACA;EAAI;EAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAwB,WAAW;EAAA;EAAA,CAAA9C,aAAA,GAAAsB,CAAA,UAAIuB,KAAK,GAAE;IAAA;IAAA7C,aAAA,GAAAsB,CAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACxB,OACE,0BAAAtB,KAAA;IAAA;IAAA;IAAA;IAAA;MAAK+E,SAAS,EAAC,qBAAqB;MAAAL,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAA9E,YAAA;QAAA+E,UAAA;QAAAC,YAAA;MAAA;IAAA;IAClC;IAAA9E,KAAA;IAAA;IAAA;IAAA;IAAA;MAAK+E,SAAS,EAAC,kBAAkB;MAAAL,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAA9E,YAAA;QAAA+E,UAAA;QAAAC,YAAA;MAAA;IAAA;IAC/B;IAAA9E,KAAA;IAAA;IAAA;IAAA;IAAA;MAAK+E,SAAS,EAAC,eAAe;MAAAL,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAA9E,YAAA;QAAA+E,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,QAAM,CAAC;IACtC;IAAA9E,KAAA;IAAA;IAAA;IAAA;IAAA;MAAI+E,SAAS,EAAC,0BAA0B;MAAAL,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAA9E,YAAA;QAAA+E,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,yBAA2B,CAAC;IACrE;IAAA9E,KAAA;IAAA;IAAA;IAAA;IAAA;MAAG+E,SAAS,EAAC,qBAAqB;MAAAL,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAA9E,YAAA;QAAA+E,UAAA;QAAAC,YAAA;MAAA;IAAA;IAAE;IAAA,CAAA5E,aAAA,GAAAsB,CAAA,UAAAwB,WAAW;IAAA;IAAA,CAAA9C,aAAA,GAAAsB,CAAA,UAAIuB,KAAK,CAAI,CAAC;IAC7D;IAAA/C,KAAA;IAAA;IAAA;IAAA;IAAA;MACE+E,SAAS,EAAC,iBAAiB;MAC3BC,OAAO,EAAEA,CAAA,KAAM;QAAA;QAAA9E,aAAA,GAAAqB,CAAA;QAAArB,aAAA,GAAAoB,CAAA;QAAA,OAAA6B,OAAO,CAAC,CAAC;MAAD,CAAE;MAAAuB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAA9E,YAAA;QAAA+E,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC1B,OAEO,CACL,CACF,CAAC;EAEV,CAAC;EAAA;EAAA;IAAA5E,aAAA,GAAAsB,CAAA;EAAA;EAAAtB,aAAA,GAAAoB,CAAA;EAED,OACE,0BAAAtB,KAAA,CAACwC,aAAa;EAAA;EAAA;IACZyC,OAAO,EAAEA,CAAClC,KAAK,EAAEmC,SAAS,KAAK;MAAA;MAAAhF,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAC7BmC,OAAO,CAACV,KAAK,CAAC,kBAAkB,EAAEA,KAAK,EAAEmC,SAAS,CAAC;IACrD,CAAE;IACFC,SAAS,EAAE,CAACvC,MAAM,EAAEwC,QAAQ,CAAE;IAAAV,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAA9E,YAAA;MAAA+E,UAAA;MAAAC,YAAA;IAAA;EAAA;EAE9B;EAAA9E,KAAA;EAAA;EAAA;EAAA;EAAA;IAAK+E,SAAS,EAAC,qBAAqB;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAA9E,YAAA;MAAA+E,UAAA;MAAAC,YAAA;IAAA;EAAA;EAElC;EAAA9E,KAAA,CAACmC,eAAe;EAAA;EAAA;IACdkD,UAAU,EAAEzC,MAAM,EAAEyC,UAAW;IAC/BC,QAAQ,EAAE/B,YAAa;IACvBgC,YAAY,EAAE5B,gBAAiB;IAC/B6B,iBAAiB,EAAC,oBAAoB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAA9E,YAAA;MAAA+E,UAAA;MAAAC,YAAA;IAAA;EAAA,CACvC,CAAC;EAGF;EAAA9E,KAAA,CAACoC,cAAc;EAAA;EAAA;IACbqD,KAAK,EAAExC,IAAI,CAACwC,KAAM;IAClBvC,SAAS,EAAEA,SAAU;IACrB6B,SAAS,EAAC,MAAM;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAA9E,YAAA;MAAA+E,UAAA;MAAAC,YAAA;IAAA;EAAA,CACjB,CAAC;EAGF;EAAA9E,KAAA,CAAC0C,WAAW;EAAA;EAAA;IACVgD,WAAW;IAAE;IAAA1F,KAAA;IAAA;IAAA;IAAA;IAAA;MAAK+E,SAAS,EAAC,gDAAgD;MAAAL,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAA9E,YAAA;QAAA+E,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAE;IAChFC,SAAS,EAAC,MAAM;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAA9E,YAAA;MAAA+E,UAAA;MAAAC,YAAA;IAAA;EAAA;EAEhB;EAAA9E,KAAA,CAACsC,gBAAgB;EAAA;EAAA;IACfqD,QAAQ,EAAE1C,IAAI,CAAC2C,gBAAiB;IAChC1C,SAAS,EAAEA,SAAU;IACrB2C,cAAc,EAAEjC,kBAAmB;IACnCkC,aAAa,EAAE,EAAG;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAA9E,YAAA;MAAA+E,UAAA;MAAAC,YAAA;IAAA;EAAA,CACnB,CACU,CAAC;EAGd;EAAA9E,KAAA,CAAC0C,WAAW;EAAA;EAAA;IACVgD,WAAW;IACT;IAAA1F,KAAA;IAAA;IAAA;IAAA;IAAA;MAAK+E,SAAS,EAAC,uCAAuC;MAAAL,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAA9E,YAAA;QAAA+E,UAAA;QAAAC,YAAA;MAAA;IAAA;IACpD;IAAA9E,KAAA;IAAA;IAAA;IAAA;IAAA;MAAK+E,SAAS,EAAC,2CAA2C;MAAAL,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAA9E,YAAA;QAAA+E,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC;IAC7D;IAAA9E,KAAA;IAAA;IAAA;IAAA;IAAA;MAAK+E,SAAS,EAAC,2CAA2C;MAAAL,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAA9E,YAAA;QAAA+E,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CACzD,CACN;IAAAJ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAA9E,YAAA;MAAA+E,UAAA;MAAAC,YAAA;IAAA;EAAA;EAED;EAAA9E,KAAA;EAAA;EAAA;EAAA;EAAA;IAAK+E,SAAS,EAAC,uCAAuC;IAAAL,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAA9E,YAAA;MAAA+E,UAAA;MAAAC,YAAA;IAAA;EAAA;EACpD;EAAA9E,KAAA,CAACqC,cAAc;EAAA;EAAA;IACbsD,QAAQ,EAAE1C,IAAI,CAAC8C,cAAe;IAC9B7C,SAAS,EAAEA,SAAU;IACrB2C,cAAc,EAAEjC,kBAAmB;IACnCoC,QAAQ,EAAE,CAAE;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAA9E,YAAA;MAAA+E,UAAA;MAAAC,YAAA;IAAA;EAAA,CACb,CAAC;EAEF;EAAA9E,KAAA,CAACuC,WAAW;EAAA;EAAA;IACV0D,OAAO,EAAEhC,eAAgB;IACzBf,SAAS,EAAE,KAAM;IACjBgD,SAAS,EAAEpC,aAAc;IACzBqC,aAAa,EAAEpC,qBAAsB;IACrCqC,YAAY,EAAE,IAAI/B,IAAI,CAAC,CAAE;IAAAK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAA9E,YAAA;MAAA+E,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC1B,CACE,CACM,CACV,CACQ,CAAC;AAEpB", "ignoreList": []}