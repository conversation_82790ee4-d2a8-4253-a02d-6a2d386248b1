7275f8a9a2d10f39e19ea6a20b136592
'use client';

/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\dashboard\\layout.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_2ga3zjmfwz() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\dashboard\\layout.tsx";
  var hash = "03acaec71384e40f0563fcb17c9f4dbfef364b62";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\dashboard\\layout.tsx",
    statementMap: {
      "0": {
        start: {
          line: 13,
          column: 41
        },
        end: {
          line: 13,
          column: 50
        }
      },
      "1": {
        start: {
          line: 14,
          column: 17
        },
        end: {
          line: 14,
          column: 28
        }
      },
      "2": {
        start: {
          line: 15,
          column: 44
        },
        end: {
          line: 15,
          column: 59
        }
      },
      "3": {
        start: {
          line: 16,
          column: 46
        },
        end: {
          line: 16,
          column: 61
        }
      },
      "4": {
        start: {
          line: 18,
          column: 2
        },
        end: {
          line: 56,
          column: 73
        }
      },
      "5": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 25,
          column: 7
        }
      },
      "6": {
        start: {
          line: 28,
          column: 22
        },
        end: {
          line: 28,
          column: 65
        }
      },
      "7": {
        start: {
          line: 29,
          column: 25
        },
        end: {
          line: 29,
          column: 46
        }
      },
      "8": {
        start: {
          line: 31,
          column: 4
        },
        end: {
          line: 35,
          column: 5
        }
      },
      "9": {
        start: {
          line: 32,
          column: 6
        },
        end: {
          line: 32,
          column: 101
        }
      },
      "10": {
        start: {
          line: 34,
          column: 6
        },
        end: {
          line: 34,
          column: 12
        }
      },
      "11": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 40,
          column: 5
        }
      },
      "12": {
        start: {
          line: 39,
          column: 6
        },
        end: {
          line: 39,
          column: 29
        }
      },
      "13": {
        start: {
          line: 43,
          column: 4
        },
        end: {
          line: 55,
          column: 5
        }
      },
      "14": {
        start: {
          line: 44,
          column: 6
        },
        end: {
          line: 44,
          column: 28
        }
      },
      "15": {
        start: {
          line: 45,
          column: 6
        },
        end: {
          line: 45,
          column: 86
        }
      },
      "16": {
        start: {
          line: 46,
          column: 6
        },
        end: {
          line: 46,
          column: 27
        }
      },
      "17": {
        start: {
          line: 47,
          column: 11
        },
        end: {
          line: 55,
          column: 5
        }
      },
      "18": {
        start: {
          line: 48,
          column: 6
        },
        end: {
          line: 48,
          column: 82
        }
      },
      "19": {
        start: {
          line: 51,
          column: 6
        },
        end: {
          line: 54,
          column: 7
        }
      },
      "20": {
        start: {
          line: 52,
          column: 25
        },
        end: {
          line: 52,
          column: 49
        }
      },
      "21": {
        start: {
          line: 53,
          column: 8
        },
        end: {
          line: 53,
          column: 65
        }
      },
      "22": {
        start: {
          line: 59,
          column: 2
        },
        end: {
          line: 68,
          column: 3
        }
      },
      "23": {
        start: {
          line: 60,
          column: 4
        },
        end: {
          line: 67,
          column: 5
        }
      },
      "24": {
        start: {
          line: 71,
          column: 2
        },
        end: {
          line: 73,
          column: 3
        }
      },
      "25": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 72,
          column: 15
        }
      },
      "26": {
        start: {
          line: 75,
          column: 2
        },
        end: {
          line: 82,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "DashboardLayout",
        decl: {
          start: {
            line: 8,
            column: 24
          },
          end: {
            line: 8,
            column: 39
          }
        },
        loc: {
          start: {
            line: 12,
            column: 3
          },
          end: {
            line: 83,
            column: 1
          }
        },
        line: 12
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 13
          }
        },
        loc: {
          start: {
            line: 18,
            column: 18
          },
          end: {
            line: 56,
            column: 3
          }
        },
        line: 18
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 31,
            column: 4
          },
          end: {
            line: 35,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 4
          },
          end: {
            line: 35,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "1": {
        loc: {
          start: {
            line: 38,
            column: 4
          },
          end: {
            line: 40,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 4
          },
          end: {
            line: 40,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "2": {
        loc: {
          start: {
            line: 38,
            column: 8
          },
          end: {
            line: 38,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 8
          },
          end: {
            line: 38,
            column: 18
          }
        }, {
          start: {
            line: 38,
            column: 22
          },
          end: {
            line: 38,
            column: 37
          }
        }],
        line: 38
      },
      "3": {
        loc: {
          start: {
            line: 43,
            column: 4
          },
          end: {
            line: 55,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 4
          },
          end: {
            line: 55,
            column: 5
          }
        }, {
          start: {
            line: 47,
            column: 11
          },
          end: {
            line: 55,
            column: 5
          }
        }],
        line: 43
      },
      "4": {
        loc: {
          start: {
            line: 43,
            column: 8
          },
          end: {
            line: 43,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 43,
            column: 8
          },
          end: {
            line: 43,
            column: 18
          }
        }, {
          start: {
            line: 43,
            column: 22
          },
          end: {
            line: 43,
            column: 38
          }
        }, {
          start: {
            line: 43,
            column: 42
          },
          end: {
            line: 43,
            column: 56
          }
        }, {
          start: {
            line: 43,
            column: 60
          },
          end: {
            line: 43,
            column: 74
          }
        }],
        line: 43
      },
      "5": {
        loc: {
          start: {
            line: 47,
            column: 11
          },
          end: {
            line: 55,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 47,
            column: 11
          },
          end: {
            line: 55,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 47
      },
      "6": {
        loc: {
          start: {
            line: 47,
            column: 15
          },
          end: {
            line: 47,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 47,
            column: 15
          },
          end: {
            line: 47,
            column: 25
          }
        }, {
          start: {
            line: 47,
            column: 29
          },
          end: {
            line: 47,
            column: 44
          }
        }, {
          start: {
            line: 47,
            column: 48
          },
          end: {
            line: 47,
            column: 62
          }
        }],
        line: 47
      },
      "7": {
        loc: {
          start: {
            line: 51,
            column: 6
          },
          end: {
            line: 54,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 51,
            column: 6
          },
          end: {
            line: 54,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 51
      },
      "8": {
        loc: {
          start: {
            line: 59,
            column: 2
          },
          end: {
            line: 68,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 59,
            column: 2
          },
          end: {
            line: 68,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 59
      },
      "9": {
        loc: {
          start: {
            line: 59,
            column: 6
          },
          end: {
            line: 59,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 59,
            column: 7
          },
          end: {
            line: 59,
            column: 16
          }
        }, {
          start: {
            line: 59,
            column: 20
          },
          end: {
            line: 59,
            column: 35
          }
        }, {
          start: {
            line: 59,
            column: 40
          },
          end: {
            line: 59,
            column: 53
          }
        }],
        line: 59
      },
      "10": {
        loc: {
          start: {
            line: 64,
            column: 11
          },
          end: {
            line: 64,
            column: 82
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 64,
            column: 27
          },
          end: {
            line: 64,
            column: 52
          }
        }, {
          start: {
            line: 64,
            column: 55
          },
          end: {
            line: 64,
            column: 82
          }
        }],
        line: 64
      },
      "11": {
        loc: {
          start: {
            line: 71,
            column: 2
          },
          end: {
            line: 73,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 71,
            column: 2
          },
          end: {
            line: 73,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 71
      },
      "12": {
        loc: {
          start: {
            line: 71,
            column: 6
          },
          end: {
            line: 71,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 71,
            column: 6
          },
          end: {
            line: 71,
            column: 22
          }
        }, {
          start: {
            line: 71,
            column: 26
          },
          end: {
            line: 71,
            column: 40
          }
        }],
        line: 71
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0, 0, 0],
      "5": [0, 0],
      "6": [0, 0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "03acaec71384e40f0563fcb17c9f4dbfef364b62"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2ga3zjmfwz = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2ga3zjmfwz();
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AppContext';
import Sidebar from '@/components/layout/Sidebar';
export default function DashboardLayout({
  children
}) {
  /* istanbul ignore next */
  cov_2ga3zjmfwz().f[0]++;
  const {
    isAuthenticated,
    isLoading
  } =
  /* istanbul ignore next */
  (cov_2ga3zjmfwz().s[0]++, useAuth());
  const router =
  /* istanbul ignore next */
  (cov_2ga3zjmfwz().s[1]++, useRouter());
  const [isRedirecting, setIsRedirecting] =
  /* istanbul ignore next */
  (cov_2ga3zjmfwz().s[2]++, useState(false));
  const [hasInitialized, setHasInitialized] =
  /* istanbul ignore next */
  (cov_2ga3zjmfwz().s[3]++, useState(false));
  /* istanbul ignore next */
  cov_2ga3zjmfwz().s[4]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_2ga3zjmfwz().f[1]++;
    cov_2ga3zjmfwz().s[5]++;
    console.log('🏠 [DASHBOARD-LAYOUT] Auth state changed:', {
      isLoading,
      isAuthenticated,
      isRedirecting,
      hasInitialized,
      timestamp: new Date().toLocaleTimeString()
    });

    // Check if this is an OAuth callback (has code parameter)
    const urlParams =
    /* istanbul ignore next */
    (cov_2ga3zjmfwz().s[6]++, new URLSearchParams(window.location.search));
    const hasOAuthCode =
    /* istanbul ignore next */
    (cov_2ga3zjmfwz().s[7]++, urlParams.has('code'));
    /* istanbul ignore next */
    cov_2ga3zjmfwz().s[8]++;
    if (hasOAuthCode) {
      /* istanbul ignore next */
      cov_2ga3zjmfwz().b[0][0]++;
      cov_2ga3zjmfwz().s[9]++;
      console.log('🔄 [DASHBOARD-LAYOUT] OAuth callback detected, waiting for Amplify to process...');
      // Give Amplify extra time to process OAuth callback
      /* istanbul ignore next */
      cov_2ga3zjmfwz().s[10]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_2ga3zjmfwz().b[0][1]++;
    }

    // Mark as initialized once we've completed the first auth check
    cov_2ga3zjmfwz().s[11]++;
    if (
    /* istanbul ignore next */
    (cov_2ga3zjmfwz().b[2][0]++, !isLoading) &&
    /* istanbul ignore next */
    (cov_2ga3zjmfwz().b[2][1]++, !hasInitialized)) {
      /* istanbul ignore next */
      cov_2ga3zjmfwz().b[1][0]++;
      cov_2ga3zjmfwz().s[12]++;
      setHasInitialized(true);
    } else
    /* istanbul ignore next */
    {
      cov_2ga3zjmfwz().b[1][1]++;
    }

    // Only redirect if authentication check is complete and user is not authenticated
    cov_2ga3zjmfwz().s[13]++;
    if (
    /* istanbul ignore next */
    (cov_2ga3zjmfwz().b[4][0]++, !isLoading) &&
    /* istanbul ignore next */
    (cov_2ga3zjmfwz().b[4][1]++, !isAuthenticated) &&
    /* istanbul ignore next */
    (cov_2ga3zjmfwz().b[4][2]++, !isRedirecting) &&
    /* istanbul ignore next */
    (cov_2ga3zjmfwz().b[4][3]++, hasInitialized)) {
      /* istanbul ignore next */
      cov_2ga3zjmfwz().b[3][0]++;
      cov_2ga3zjmfwz().s[14]++;
      setIsRedirecting(true);
      /* istanbul ignore next */
      cov_2ga3zjmfwz().s[15]++;
      console.log('❌ [DASHBOARD-LAYOUT] User not authenticated, redirecting to login');
      /* istanbul ignore next */
      cov_2ga3zjmfwz().s[16]++;
      router.push('/login');
    } else {
      /* istanbul ignore next */
      cov_2ga3zjmfwz().b[3][1]++;
      cov_2ga3zjmfwz().s[17]++;
      if (
      /* istanbul ignore next */
      (cov_2ga3zjmfwz().b[6][0]++, !isLoading) &&
      /* istanbul ignore next */
      (cov_2ga3zjmfwz().b[6][1]++, isAuthenticated) &&
      /* istanbul ignore next */
      (cov_2ga3zjmfwz().b[6][2]++, hasInitialized)) {
        /* istanbul ignore next */
        cov_2ga3zjmfwz().b[5][0]++;
        cov_2ga3zjmfwz().s[18]++;
        console.log('✅ [DASHBOARD-LAYOUT] User is authenticated, showing dashboard');

        // Clean up URL if it has OAuth parameters
        /* istanbul ignore next */
        cov_2ga3zjmfwz().s[19]++;
        if (hasOAuthCode) {
          /* istanbul ignore next */
          cov_2ga3zjmfwz().b[7][0]++;
          const cleanUrl =
          /* istanbul ignore next */
          (cov_2ga3zjmfwz().s[20]++, window.location.pathname);
          /* istanbul ignore next */
          cov_2ga3zjmfwz().s[21]++;
          window.history.replaceState({}, document.title, cleanUrl);
        } else
        /* istanbul ignore next */
        {
          cov_2ga3zjmfwz().b[7][1]++;
        }
      } else
      /* istanbul ignore next */
      {
        cov_2ga3zjmfwz().b[5][1]++;
      }
    }
  }, [isLoading, isAuthenticated, router, isRedirecting, hasInitialized]);

  // Show loading state only on initial load or when redirecting
  /* istanbul ignore next */
  cov_2ga3zjmfwz().s[22]++;
  if (
  /* istanbul ignore next */
  (cov_2ga3zjmfwz().b[9][0]++, isLoading) &&
  /* istanbul ignore next */
  (cov_2ga3zjmfwz().b[9][1]++, !hasInitialized) ||
  /* istanbul ignore next */
  (cov_2ga3zjmfwz().b[9][2]++, isRedirecting)) {
    /* istanbul ignore next */
    cov_2ga3zjmfwz().b[8][0]++;
    cov_2ga3zjmfwz().s[23]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "flex items-center justify-center h-screen",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 61,
        columnNumber: 7
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 62,
        columnNumber: 9
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "p",
    /* istanbul ignore next */
    {
      className: "ml-4 text-lg",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 63,
        columnNumber: 9
      }
    }, isRedirecting ?
    /* istanbul ignore next */
    (cov_2ga3zjmfwz().b[10][0]++, 'Redirecting to login...') :
    /* istanbul ignore next */
    (cov_2ga3zjmfwz().b[10][1]++, 'Loading your dashboard...')));
  } else
  /* istanbul ignore next */
  {
    cov_2ga3zjmfwz().b[8][1]++;
  }

  // Don't render dashboard if not authenticated (but don't show loading if already initialized)
  cov_2ga3zjmfwz().s[24]++;
  if (
  /* istanbul ignore next */
  (cov_2ga3zjmfwz().b[12][0]++, !isAuthenticated) &&
  /* istanbul ignore next */
  (cov_2ga3zjmfwz().b[12][1]++, hasInitialized)) {
    /* istanbul ignore next */
    cov_2ga3zjmfwz().b[11][0]++;
    cov_2ga3zjmfwz().s[25]++;
    return null; // Will redirect via useEffect
  } else
  /* istanbul ignore next */
  {
    cov_2ga3zjmfwz().b[11][1]++;
  }
  cov_2ga3zjmfwz().s[26]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex h-screen",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 76,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(Sidebar,
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 77,
      columnNumber: 7
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "main",
  /* istanbul ignore next */
  {
    className: "main-content",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 78,
      columnNumber: 7
    }
  }, children));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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