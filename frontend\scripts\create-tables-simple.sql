-- Simple version to create tables step by step
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum types
DO $$ BEGIN
    CREATE TYPE metadata.sync_status AS ENUM ('pending', 'matched', 'manual_review', 'rejected');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE metadata.entity_status AS ENUM ('active', 'merged', 'deprecated');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE metadata.product_type AS ENUM ('physical', 'digital', 'service');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create global_vendors table
CREATE TABLE IF NOT EXISTS metadata.global_vendors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    canonical_name VARCHAR(255) NOT NULL UNIQUE,
    legal_name <PERSON><PERSON><PERSON><PERSON>(255),
    tax_id VARCHAR(100),
    country_code VARCHAR(2),
    domain VARCHAR(255),
    address_hash VARCHAR(64),
    phone_normalized VARCHAR(50),
    confidence_score DECIMAL(5,4) DEFAULT 0.0000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status metadata.entity_status DEFAULT 'active',
    merged_into_id UUID
);

-- Add foreign key constraint after table creation
ALTER TABLE metadata.global_vendors 
ADD CONSTRAINT fk_global_vendors_merged_into 
FOREIGN KEY (merged_into_id) REFERENCES metadata.global_vendors(id);

-- Create global_products table
CREATE TABLE IF NOT EXISTS metadata.global_products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    canonical_name VARCHAR(255) NOT NULL,
    product_category VARCHAR(100),
    vendor_id UUID NOT NULL,
    gtin VARCHAR(14),
    manufacturer_sku VARCHAR(100),
    product_type metadata.product_type DEFAULT 'physical',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status metadata.entity_status DEFAULT 'active',
    merged_into_id UUID
);

-- Add foreign key constraints
ALTER TABLE metadata.global_products 
ADD CONSTRAINT fk_global_products_vendor 
FOREIGN KEY (vendor_id) REFERENCES metadata.global_vendors(id);

ALTER TABLE metadata.global_products 
ADD CONSTRAINT fk_global_products_merged_into 
FOREIGN KEY (merged_into_id) REFERENCES metadata.global_products(id);

-- Create global_product_versions table
CREATE TABLE IF NOT EXISTS metadata.global_product_versions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL,
    version_number VARCHAR(50) NOT NULL,
    release_date DATE,
    end_of_life_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status metadata.entity_status DEFAULT 'active',
    merged_into_id UUID
);

-- Add foreign key constraints
ALTER TABLE metadata.global_product_versions 
ADD CONSTRAINT fk_global_product_versions_product 
FOREIGN KEY (product_id) REFERENCES metadata.global_products(id);

ALTER TABLE metadata.global_product_versions 
ADD CONSTRAINT fk_global_product_versions_merged_into 
FOREIGN KEY (merged_into_id) REFERENCES metadata.global_product_versions(id);

-- Add unique constraints
ALTER TABLE metadata.global_products 
ADD CONSTRAINT global_products_vendor_name_unique UNIQUE (vendor_id, canonical_name);

ALTER TABLE metadata.global_product_versions 
ADD CONSTRAINT global_product_versions_product_version_unique UNIQUE (product_id, version_number);
