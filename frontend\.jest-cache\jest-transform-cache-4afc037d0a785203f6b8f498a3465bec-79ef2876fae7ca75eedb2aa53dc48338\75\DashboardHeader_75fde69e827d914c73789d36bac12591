2e1801c57a86a92ca717303c34312a47
/**
 * Dashboard Header Component
 * 
 * Contains the dashboard title, subtitle, search functionality, and action buttons.
 * Focused responsibility: Header section with search and actions.
 */

'use client';

/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\DashboardHeader.tsx";
var __jsx = React.createElement;
function cov_22o17h8if() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\DashboardHeader.tsx";
  var hash = "8ce3c2eeb3ba53549438168be1e39c42aaa7ca3b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\DashboardHeader.tsx",
    statementMap: {
      "0": {
        start: {
          line: 20,
          column: 24
        },
        end: {
          line: 88,
          column: 2
        }
      },
      "1": {
        start: {
          line: 28,
          column: 40
        },
        end: {
          line: 28,
          column: 52
        }
      },
      "2": {
        start: {
          line: 30,
          column: 29
        },
        end: {
          line: 34,
          column: 3
        }
      },
      "3": {
        start: {
          line: 31,
          column: 18
        },
        end: {
          line: 31,
          column: 32
        }
      },
      "4": {
        start: {
          line: 32,
          column: 4
        },
        end: {
          line: 32,
          column: 25
        }
      },
      "5": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 33,
          column: 21
        }
      },
      "6": {
        start: {
          line: 36,
          column: 29
        },
        end: {
          line: 39,
          column: 3
        }
      },
      "7": {
        start: {
          line: 37,
          column: 4
        },
        end: {
          line: 37,
          column: 22
        }
      },
      "8": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 38,
          column: 27
        }
      },
      "9": {
        start: {
          line: 41,
          column: 27
        },
        end: {
          line: 43,
          column: 3
        }
      },
      "10": {
        start: {
          line: 42,
          column: 4
        },
        end: {
          line: 42,
          column: 20
        }
      },
      "11": {
        start: {
          line: 45,
          column: 2
        },
        end: {
          line: 87,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "DashboardHeader",
        decl: {
          start: {
            line: 20,
            column: 44
          },
          end: {
            line: 20,
            column: 59
          }
        },
        loc: {
          start: {
            line: 27,
            column: 25
          },
          end: {
            line: 88,
            column: 1
          }
        },
        line: 27
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 30,
            column: 29
          },
          end: {
            line: 30,
            column: 30
          }
        },
        loc: {
          start: {
            line: 30,
            column: 73
          },
          end: {
            line: 34,
            column: 3
          }
        },
        line: 30
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 36,
            column: 29
          },
          end: {
            line: 36,
            column: 30
          }
        },
        loc: {
          start: {
            line: 36,
            column: 53
          },
          end: {
            line: 39,
            column: 3
          }
        },
        line: 36
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 41,
            column: 27
          },
          end: {
            line: 41,
            column: 28
          }
        },
        loc: {
          start: {
            line: 41,
            column: 33
          },
          end: {
            line: 43,
            column: 3
          }
        },
        line: 41
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 21,
            column: 2
          },
          end: {
            line: 21,
            column: 31
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 21,
            column: 15
          },
          end: {
            line: 21,
            column: 31
          }
        }],
        line: 21
      },
      "1": {
        loc: {
          start: {
            line: 24,
            column: 2
          },
          end: {
            line: 24,
            column: 42
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 24,
            column: 22
          },
          end: {
            line: 24,
            column: 42
          }
        }],
        line: 24
      },
      "2": {
        loc: {
          start: {
            line: 25,
            column: 2
          },
          end: {
            line: 25,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 25,
            column: 14
          },
          end: {
            line: 25,
            column: 16
          }
        }],
        line: 25
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "8ce3c2eeb3ba53549438168be1e39c42aaa7ca3b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_22o17h8if = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_22o17h8if();
import React, { useState } from 'react';
const DashboardHeader =
/* istanbul ignore next */
(cov_22o17h8if().s[0]++, /*#__PURE__*/React.memo(function DashboardHeader({
  clientName =
  /* istanbul ignore next */
  (cov_22o17h8if().b[0][0]++, 'Unknown Client'),
  onSearch,
  onAddRenewal,
  searchPlaceholder =
  /* istanbul ignore next */
  (cov_22o17h8if().b[1][0]++, 'Search renewals...'),
  className =
  /* istanbul ignore next */
  (cov_22o17h8if().b[2][0]++, ''),
  'data-testid': testId
}) {
  /* istanbul ignore next */
  cov_22o17h8if().f[0]++;
  const [searchQuery, setSearchQuery] =
  /* istanbul ignore next */
  (cov_22o17h8if().s[1]++, useState(''));
  /* istanbul ignore next */
  cov_22o17h8if().s[2]++;
  const handleSearchChange = e => {
    /* istanbul ignore next */
    cov_22o17h8if().f[1]++;
    const query =
    /* istanbul ignore next */
    (cov_22o17h8if().s[3]++, e.target.value);
    /* istanbul ignore next */
    cov_22o17h8if().s[4]++;
    setSearchQuery(query);
    /* istanbul ignore next */
    cov_22o17h8if().s[5]++;
    onSearch?.(query);
  };
  /* istanbul ignore next */
  cov_22o17h8if().s[6]++;
  const handleSearchSubmit = e => {
    /* istanbul ignore next */
    cov_22o17h8if().f[2]++;
    cov_22o17h8if().s[7]++;
    e.preventDefault();
    /* istanbul ignore next */
    cov_22o17h8if().s[8]++;
    onSearch?.(searchQuery);
  };
  /* istanbul ignore next */
  cov_22o17h8if().s[9]++;
  const handleAddRenewal = () => {
    /* istanbul ignore next */
    cov_22o17h8if().f[3]++;
    cov_22o17h8if().s[10]++;
    onAddRenewal?.();
  };
  /* istanbul ignore next */
  cov_22o17h8if().s[11]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: `dashboard-header ${className}`,
    /* istanbul ignore next */
    "data-testid": testId,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 46,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "dashboard-title-section",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 50,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h1",
  /* istanbul ignore next */
  {
    className: "dashboard-title",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 51,
      columnNumber: 9
    }
  }, "Dashboard - ", clientName),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "dashboard-subtitle",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 54,
      columnNumber: 9
    }
  }, "Manage your subscriptions, maintenance, support and warranties")),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "dashboard-actions",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 59,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "form",
  /* istanbul ignore next */
  {
    onSubmit: handleSearchSubmit,
    className: "search-container",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 60,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "input",
  /* istanbul ignore next */
  {
    type: "text",
    placeholder: searchPlaceholder,
    className: "search-input",
    value: searchQuery,
    onChange: handleSearchChange,
    /* istanbul ignore next */
    "aria-label": "Search renewals",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 61,
      columnNumber: 11
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  {
    type: "submit",
    className: "search-icon",
    /* istanbul ignore next */
    "aria-label": "Submit search",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 69,
      columnNumber: 11
    }
  }, "\uD83D\uDD0D")),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  {
    className: "btn btn-primary",
    onClick: handleAddRenewal,
    /* istanbul ignore next */
    "aria-label": "Add new renewal",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 78,
      columnNumber: 9
    }
  }, "+ Add Renewal")));
}));
export default DashboardHeader;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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