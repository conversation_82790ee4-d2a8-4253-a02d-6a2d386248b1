6619f5fa8cb948de11c48bd7f2260f59
"use strict";

require("@testing-library/jest-dom");
/**
 * Jest Setup File
 * 
 * Global test configuration and setup for all test files.
 * This file is executed before each test file.
 */

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.NEXT_PUBLIC_AWS_REGION = 'ca-central-1';
process.env.NEXT_PUBLIC_USER_POOL_ID = 'ca-central-1_test';
process.env.NEXT_PUBLIC_USER_POOL_CLIENT_ID = 'test-client-id';
process.env.NEXT_PUBLIC_COGNITO_DOMAIN = 'test.auth.renewtrack.com';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_renewtrack';

// Mock console methods to reduce noise in tests
const originalError = console.error;
const originalWarn = console.warn;
beforeAll(() => {
  console.error = (...args) => {
    if (typeof args[0] === 'string' && (args[0].includes('Warning: ReactDOM.render is deprecated') || args[0].includes('Warning: componentWillReceiveProps') || args[0].includes('Warning: componentWillMount'))) {
      return;
    }
    originalError.call(console, ...args);
  };
  console.warn = (...args) => {
    if (typeof args[0] === 'string' && (args[0].includes('Warning: React.createFactory') || args[0].includes('Warning: componentWillReceiveProps'))) {
      return;
    }
    originalWarn.call(console, ...args);
  };
});
afterAll(() => {
  console.error = originalError;
  console.warn = originalWarn;
});

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    // deprecated
    removeListener: jest.fn(),
    // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn()
  }))
});

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: jest.fn()
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
});

// Mock fetch
global.fetch = jest.fn();

// Mock performance API
Object.defineProperty(window, 'performance', {
  writable: true,
  value: {
    now: jest.fn(() => Date.now()),
    mark: jest.fn(),
    measure: jest.fn(),
    getEntriesByName: jest.fn(() => []),
    clearMarks: jest.fn(),
    clearMeasures: jest.fn(),
    memory: {
      usedJSHeapSize: 1000000,
      totalJSHeapSize: 2000000,
      jsHeapSizeLimit: 4000000
    }
  }
});

// Mock crypto API
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: jest.fn(() => 'test-uuid-1234'),
    getRandomValues: jest.fn(arr => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    })
  }
});

// Mock URL constructor
global.URL.createObjectURL = jest.fn(() => 'mocked-url');
global.URL.revokeObjectURL = jest.fn();

// Custom matchers
expect.extend({
  toBeInTheDocument(received) {
    const pass = received !== null && received !== undefined;
    return {
      message: () => `expected element ${pass ? 'not ' : ''}to be in the document`,
      pass
    };
  }
});

// Global test utilities
global.testUtils = {
  // Mock user for authentication tests
  mockUser: {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User',
    tenantId: 'test-tenant-id'
  },
  // Mock tenant data
  mockTenant: {
    tenantId: 'test-tenant-id',
    clientId: 'test-client-id',
    clientName: 'Test Client',
    domain: 'test.example.com'
  },
  // Mock dashboard data
  mockDashboardData: {
    stats: {
      totalRenewals: 25,
      renewalsDue: 5,
      vendors: 12,
      annualSpend: '$125,000'
    },
    recentRenewals: [{
      id: '1',
      name: 'Microsoft Office 365',
      vendor: 'Microsoft',
      renewalDate: new Date('2025-02-15'),
      cost: 1200,
      status: 'active'
    }],
    upcomingRenewals: [{
      id: '2',
      name: 'Adobe Creative Suite',
      vendor: 'Adobe',
      renewalDate: new Date('2025-03-01'),
      cost: 2400,
      status: 'pending'
    }]
  },
  // Helper to create mock API responses
  createMockApiResponse: (data, success = true) => ({
    ok: success,
    status: success ? 200 : 400,
    json: jest.fn().mockResolvedValue(success ? {
      success: true,
      data
    } : {
      success: false,
      error: 'Test error'
    })
  }),
  // Helper to wait for async operations
  waitFor: (callback, timeout = 1000) => {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const check = () => {
        try {
          const result = callback();
          if (result) {
            resolve(result);
          } else if (Date.now() - startTime > timeout) {
            reject(new Error('Timeout waiting for condition'));
          } else {
            setTimeout(check, 10);
          }
        } catch (error) {
          if (Date.now() - startTime > timeout) {
            reject(error);
          } else {
            setTimeout(check, 10);
          }
        }
      };
      check();
    });
  }
};

// Reset all mocks before each test
beforeEach(() => {
  jest.clearAllMocks();

  // Reset localStorage
  localStorageMock.getItem.mockClear();
  localStorageMock.setItem.mockClear();
  localStorageMock.removeItem.mockClear();
  localStorageMock.clear.mockClear();

  // Reset sessionStorage
  sessionStorageMock.getItem.mockClear();
  sessionStorageMock.setItem.mockClear();
  sessionStorageMock.removeItem.mockClear();
  sessionStorageMock.clear.mockClear();

  // Reset fetch
  fetch.mockClear();
});

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJyZXF1aXJlIiwicHJvY2VzcyIsImVudiIsIk5PREVfRU5WIiwiTkVYVF9QVUJMSUNfQVdTX1JFR0lPTiIsIk5FWFRfUFVCTElDX1VTRVJfUE9PTF9JRCIsIk5FWFRfUFVCTElDX1VTRVJfUE9PTF9DTElFTlRfSUQiLCJORVhUX1BVQkxJQ19DT0dOSVRPX0RPTUFJTiIsIkRBVEFCQVNFX1VSTCIsIm9yaWdpbmFsRXJyb3IiLCJjb25zb2xlIiwiZXJyb3IiLCJvcmlnaW5hbFdhcm4iLCJ3YXJuIiwiYmVmb3JlQWxsIiwiYXJncyIsImluY2x1ZGVzIiwiY2FsbCIsImFmdGVyQWxsIiwiZ2xvYmFsIiwiSW50ZXJzZWN0aW9uT2JzZXJ2ZXIiLCJjb25zdHJ1Y3RvciIsImRpc2Nvbm5lY3QiLCJvYnNlcnZlIiwidW5vYnNlcnZlIiwiUmVzaXplT2JzZXJ2ZXIiLCJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsIndpbmRvdyIsIndyaXRhYmxlIiwidmFsdWUiLCJqZXN0IiwiZm4iLCJtb2NrSW1wbGVtZW50YXRpb24iLCJxdWVyeSIsIm1hdGNoZXMiLCJtZWRpYSIsIm9uY2hhbmdlIiwiYWRkTGlzdGVuZXIiLCJyZW1vdmVMaXN0ZW5lciIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiZGlzcGF0Y2hFdmVudCIsImxvY2FsU3RvcmFnZU1vY2siLCJnZXRJdGVtIiwic2V0SXRlbSIsInJlbW92ZUl0ZW0iLCJjbGVhciIsInNlc3Npb25TdG9yYWdlTW9jayIsImZldGNoIiwibm93IiwiRGF0ZSIsIm1hcmsiLCJtZWFzdXJlIiwiZ2V0RW50cmllc0J5TmFtZSIsImNsZWFyTWFya3MiLCJjbGVhck1lYXN1cmVzIiwibWVtb3J5IiwidXNlZEpTSGVhcFNpemUiLCJ0b3RhbEpTSGVhcFNpemUiLCJqc0hlYXBTaXplTGltaXQiLCJyYW5kb21VVUlEIiwiZ2V0UmFuZG9tVmFsdWVzIiwiYXJyIiwiaSIsImxlbmd0aCIsIk1hdGgiLCJmbG9vciIsInJhbmRvbSIsIlVSTCIsImNyZWF0ZU9iamVjdFVSTCIsInJldm9rZU9iamVjdFVSTCIsImV4cGVjdCIsImV4dGVuZCIsInRvQmVJblRoZURvY3VtZW50IiwicmVjZWl2ZWQiLCJwYXNzIiwidW5kZWZpbmVkIiwibWVzc2FnZSIsInRlc3RVdGlscyIsIm1vY2tVc2VyIiwiaWQiLCJlbWFpbCIsIm5hbWUiLCJ0ZW5hbnRJZCIsIm1vY2tUZW5hbnQiLCJjbGllbnRJZCIsImNsaWVudE5hbWUiLCJkb21haW4iLCJtb2NrRGFzaGJvYXJkRGF0YSIsInN0YXRzIiwidG90YWxSZW5ld2FscyIsInJlbmV3YWxzRHVlIiwidmVuZG9ycyIsImFubnVhbFNwZW5kIiwicmVjZW50UmVuZXdhbHMiLCJ2ZW5kb3IiLCJyZW5ld2FsRGF0ZSIsImNvc3QiLCJzdGF0dXMiLCJ1cGNvbWluZ1JlbmV3YWxzIiwiY3JlYXRlTW9ja0FwaVJlc3BvbnNlIiwiZGF0YSIsInN1Y2Nlc3MiLCJvayIsImpzb24iLCJtb2NrUmVzb2x2ZWRWYWx1ZSIsIndhaXRGb3IiLCJjYWxsYmFjayIsInRpbWVvdXQiLCJQcm9taXNlIiwicmVzb2x2ZSIsInJlamVjdCIsInN0YXJ0VGltZSIsImNoZWNrIiwicmVzdWx0IiwiRXJyb3IiLCJzZXRUaW1lb3V0IiwiYmVmb3JlRWFjaCIsImNsZWFyQWxsTW9ja3MiLCJtb2NrQ2xlYXIiLCJvbiIsInJlYXNvbiIsInByb21pc2UiXSwic291cmNlcyI6WyJqZXN0LnNldHVwLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogSmVzdCBTZXR1cCBGaWxlXG4gKiBcbiAqIEdsb2JhbCB0ZXN0IGNvbmZpZ3VyYXRpb24gYW5kIHNldHVwIGZvciBhbGwgdGVzdCBmaWxlcy5cbiAqIFRoaXMgZmlsZSBpcyBleGVjdXRlZCBiZWZvcmUgZWFjaCB0ZXN0IGZpbGUuXG4gKi9cblxuaW1wb3J0ICdAdGVzdGluZy1saWJyYXJ5L2plc3QtZG9tJ1xuXG4vLyBNb2NrIGVudmlyb25tZW50IHZhcmlhYmxlc1xucHJvY2Vzcy5lbnYuTk9ERV9FTlYgPSAndGVzdCdcbnByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FXU19SRUdJT04gPSAnY2EtY2VudHJhbC0xJ1xucHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfVVNFUl9QT09MX0lEID0gJ2NhLWNlbnRyYWwtMV90ZXN0J1xucHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfVVNFUl9QT09MX0NMSUVOVF9JRCA9ICd0ZXN0LWNsaWVudC1pZCdcbnByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NPR05JVE9fRE9NQUlOID0gJ3Rlc3QuYXV0aC5yZW5ld3RyYWNrLmNvbSdcbnByb2Nlc3MuZW52LkRBVEFCQVNFX1VSTCA9ICdwb3N0Z3Jlc3FsOi8vdGVzdDp0ZXN0QGxvY2FsaG9zdDo1NDMyL3Rlc3RfcmVuZXd0cmFjaydcblxuLy8gTW9jayBjb25zb2xlIG1ldGhvZHMgdG8gcmVkdWNlIG5vaXNlIGluIHRlc3RzXG5jb25zdCBvcmlnaW5hbEVycm9yID0gY29uc29sZS5lcnJvclxuY29uc3Qgb3JpZ2luYWxXYXJuID0gY29uc29sZS53YXJuXG5cbmJlZm9yZUFsbCgoKSA9PiB7XG4gIGNvbnNvbGUuZXJyb3IgPSAoLi4uYXJncykgPT4ge1xuICAgIGlmIChcbiAgICAgIHR5cGVvZiBhcmdzWzBdID09PSAnc3RyaW5nJyAmJlxuICAgICAgKGFyZ3NbMF0uaW5jbHVkZXMoJ1dhcm5pbmc6IFJlYWN0RE9NLnJlbmRlciBpcyBkZXByZWNhdGVkJykgfHxcbiAgICAgICBhcmdzWzBdLmluY2x1ZGVzKCdXYXJuaW5nOiBjb21wb25lbnRXaWxsUmVjZWl2ZVByb3BzJykgfHxcbiAgICAgICBhcmdzWzBdLmluY2x1ZGVzKCdXYXJuaW5nOiBjb21wb25lbnRXaWxsTW91bnQnKSlcbiAgICApIHtcbiAgICAgIHJldHVyblxuICAgIH1cbiAgICBvcmlnaW5hbEVycm9yLmNhbGwoY29uc29sZSwgLi4uYXJncylcbiAgfVxuXG4gIGNvbnNvbGUud2FybiA9ICguLi5hcmdzKSA9PiB7XG4gICAgaWYgKFxuICAgICAgdHlwZW9mIGFyZ3NbMF0gPT09ICdzdHJpbmcnICYmXG4gICAgICAoYXJnc1swXS5pbmNsdWRlcygnV2FybmluZzogUmVhY3QuY3JlYXRlRmFjdG9yeScpIHx8XG4gICAgICAgYXJnc1swXS5pbmNsdWRlcygnV2FybmluZzogY29tcG9uZW50V2lsbFJlY2VpdmVQcm9wcycpKVxuICAgICkge1xuICAgICAgcmV0dXJuXG4gICAgfVxuICAgIG9yaWdpbmFsV2Fybi5jYWxsKGNvbnNvbGUsIC4uLmFyZ3MpXG4gIH1cbn0pXG5cbmFmdGVyQWxsKCgpID0+IHtcbiAgY29uc29sZS5lcnJvciA9IG9yaWdpbmFsRXJyb3JcbiAgY29uc29sZS53YXJuID0gb3JpZ2luYWxXYXJuXG59KVxuXG4vLyBNb2NrIEludGVyc2VjdGlvbk9ic2VydmVyXG5nbG9iYWwuSW50ZXJzZWN0aW9uT2JzZXJ2ZXIgPSBjbGFzcyBJbnRlcnNlY3Rpb25PYnNlcnZlciB7XG4gIGNvbnN0cnVjdG9yKCkge31cbiAgZGlzY29ubmVjdCgpIHt9XG4gIG9ic2VydmUoKSB7fVxuICB1bm9ic2VydmUoKSB7fVxufVxuXG4vLyBNb2NrIFJlc2l6ZU9ic2VydmVyXG5nbG9iYWwuUmVzaXplT2JzZXJ2ZXIgPSBjbGFzcyBSZXNpemVPYnNlcnZlciB7XG4gIGNvbnN0cnVjdG9yKCkge31cbiAgZGlzY29ubmVjdCgpIHt9XG4gIG9ic2VydmUoKSB7fVxuICB1bm9ic2VydmUoKSB7fVxufVxuXG4vLyBNb2NrIG1hdGNoTWVkaWFcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eSh3aW5kb3csICdtYXRjaE1lZGlhJywge1xuICB3cml0YWJsZTogdHJ1ZSxcbiAgdmFsdWU6IGplc3QuZm4oKS5tb2NrSW1wbGVtZW50YXRpb24ocXVlcnkgPT4gKHtcbiAgICBtYXRjaGVzOiBmYWxzZSxcbiAgICBtZWRpYTogcXVlcnksXG4gICAgb25jaGFuZ2U6IG51bGwsXG4gICAgYWRkTGlzdGVuZXI6IGplc3QuZm4oKSwgLy8gZGVwcmVjYXRlZFxuICAgIHJlbW92ZUxpc3RlbmVyOiBqZXN0LmZuKCksIC8vIGRlcHJlY2F0ZWRcbiAgICBhZGRFdmVudExpc3RlbmVyOiBqZXN0LmZuKCksXG4gICAgcmVtb3ZlRXZlbnRMaXN0ZW5lcjogamVzdC5mbigpLFxuICAgIGRpc3BhdGNoRXZlbnQ6IGplc3QuZm4oKSxcbiAgfSkpLFxufSlcblxuLy8gTW9jayBzY3JvbGxUb1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KHdpbmRvdywgJ3Njcm9sbFRvJywge1xuICB3cml0YWJsZTogdHJ1ZSxcbiAgdmFsdWU6IGplc3QuZm4oKSxcbn0pXG5cbi8vIE1vY2sgbG9jYWxTdG9yYWdlXG5jb25zdCBsb2NhbFN0b3JhZ2VNb2NrID0ge1xuICBnZXRJdGVtOiBqZXN0LmZuKCksXG4gIHNldEl0ZW06IGplc3QuZm4oKSxcbiAgcmVtb3ZlSXRlbTogamVzdC5mbigpLFxuICBjbGVhcjogamVzdC5mbigpLFxufVxuT2JqZWN0LmRlZmluZVByb3BlcnR5KHdpbmRvdywgJ2xvY2FsU3RvcmFnZScsIHtcbiAgdmFsdWU6IGxvY2FsU3RvcmFnZU1vY2ssXG59KVxuXG4vLyBNb2NrIHNlc3Npb25TdG9yYWdlXG5jb25zdCBzZXNzaW9uU3RvcmFnZU1vY2sgPSB7XG4gIGdldEl0ZW06IGplc3QuZm4oKSxcbiAgc2V0SXRlbTogamVzdC5mbigpLFxuICByZW1vdmVJdGVtOiBqZXN0LmZuKCksXG4gIGNsZWFyOiBqZXN0LmZuKCksXG59XG5PYmplY3QuZGVmaW5lUHJvcGVydHkod2luZG93LCAnc2Vzc2lvblN0b3JhZ2UnLCB7XG4gIHZhbHVlOiBzZXNzaW9uU3RvcmFnZU1vY2ssXG59KVxuXG4vLyBNb2NrIGZldGNoXG5nbG9iYWwuZmV0Y2ggPSBqZXN0LmZuKClcblxuLy8gTW9jayBwZXJmb3JtYW5jZSBBUElcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eSh3aW5kb3csICdwZXJmb3JtYW5jZScsIHtcbiAgd3JpdGFibGU6IHRydWUsXG4gIHZhbHVlOiB7XG4gICAgbm93OiBqZXN0LmZuKCgpID0+IERhdGUubm93KCkpLFxuICAgIG1hcms6IGplc3QuZm4oKSxcbiAgICBtZWFzdXJlOiBqZXN0LmZuKCksXG4gICAgZ2V0RW50cmllc0J5TmFtZTogamVzdC5mbigoKSA9PiBbXSksXG4gICAgY2xlYXJNYXJrczogamVzdC5mbigpLFxuICAgIGNsZWFyTWVhc3VyZXM6IGplc3QuZm4oKSxcbiAgICBtZW1vcnk6IHtcbiAgICAgIHVzZWRKU0hlYXBTaXplOiAxMDAwMDAwLFxuICAgICAgdG90YWxKU0hlYXBTaXplOiAyMDAwMDAwLFxuICAgICAganNIZWFwU2l6ZUxpbWl0OiA0MDAwMDAwLFxuICAgIH0sXG4gIH0sXG59KVxuXG4vLyBNb2NrIGNyeXB0byBBUElcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShnbG9iYWwsICdjcnlwdG8nLCB7XG4gIHZhbHVlOiB7XG4gICAgcmFuZG9tVVVJRDogamVzdC5mbigoKSA9PiAndGVzdC11dWlkLTEyMzQnKSxcbiAgICBnZXRSYW5kb21WYWx1ZXM6IGplc3QuZm4oKGFycikgPT4ge1xuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBhcnIubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgYXJyW2ldID0gTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMjU2KVxuICAgICAgfVxuICAgICAgcmV0dXJuIGFyclxuICAgIH0pLFxuICB9LFxufSlcblxuLy8gTW9jayBVUkwgY29uc3RydWN0b3Jcbmdsb2JhbC5VUkwuY3JlYXRlT2JqZWN0VVJMID0gamVzdC5mbigoKSA9PiAnbW9ja2VkLXVybCcpXG5nbG9iYWwuVVJMLnJldm9rZU9iamVjdFVSTCA9IGplc3QuZm4oKVxuXG4vLyBDdXN0b20gbWF0Y2hlcnNcbmV4cGVjdC5leHRlbmQoe1xuICB0b0JlSW5UaGVEb2N1bWVudChyZWNlaXZlZCkge1xuICAgIGNvbnN0IHBhc3MgPSByZWNlaXZlZCAhPT0gbnVsbCAmJiByZWNlaXZlZCAhPT0gdW5kZWZpbmVkXG4gICAgcmV0dXJuIHtcbiAgICAgIG1lc3NhZ2U6ICgpID0+IGBleHBlY3RlZCBlbGVtZW50ICR7cGFzcyA/ICdub3QgJyA6ICcnfXRvIGJlIGluIHRoZSBkb2N1bWVudGAsXG4gICAgICBwYXNzLFxuICAgIH1cbiAgfSxcbn0pXG5cbi8vIEdsb2JhbCB0ZXN0IHV0aWxpdGllc1xuZ2xvYmFsLnRlc3RVdGlscyA9IHtcbiAgLy8gTW9jayB1c2VyIGZvciBhdXRoZW50aWNhdGlvbiB0ZXN0c1xuICBtb2NrVXNlcjoge1xuICAgIGlkOiAndGVzdC11c2VyLWlkJyxcbiAgICBlbWFpbDogJ3Rlc3RAZXhhbXBsZS5jb20nLFxuICAgIG5hbWU6ICdUZXN0IFVzZXInLFxuICAgIHRlbmFudElkOiAndGVzdC10ZW5hbnQtaWQnLFxuICB9LFxuICBcbiAgLy8gTW9jayB0ZW5hbnQgZGF0YVxuICBtb2NrVGVuYW50OiB7XG4gICAgdGVuYW50SWQ6ICd0ZXN0LXRlbmFudC1pZCcsXG4gICAgY2xpZW50SWQ6ICd0ZXN0LWNsaWVudC1pZCcsXG4gICAgY2xpZW50TmFtZTogJ1Rlc3QgQ2xpZW50JyxcbiAgICBkb21haW46ICd0ZXN0LmV4YW1wbGUuY29tJyxcbiAgfSxcbiAgXG4gIC8vIE1vY2sgZGFzaGJvYXJkIGRhdGFcbiAgbW9ja0Rhc2hib2FyZERhdGE6IHtcbiAgICBzdGF0czoge1xuICAgICAgdG90YWxSZW5ld2FsczogMjUsXG4gICAgICByZW5ld2Fsc0R1ZTogNSxcbiAgICAgIHZlbmRvcnM6IDEyLFxuICAgICAgYW5udWFsU3BlbmQ6ICckMTI1LDAwMCcsXG4gICAgfSxcbiAgICByZWNlbnRSZW5ld2FsczogW1xuICAgICAge1xuICAgICAgICBpZDogJzEnLFxuICAgICAgICBuYW1lOiAnTWljcm9zb2Z0IE9mZmljZSAzNjUnLFxuICAgICAgICB2ZW5kb3I6ICdNaWNyb3NvZnQnLFxuICAgICAgICByZW5ld2FsRGF0ZTogbmV3IERhdGUoJzIwMjUtMDItMTUnKSxcbiAgICAgICAgY29zdDogMTIwMCxcbiAgICAgICAgc3RhdHVzOiAnYWN0aXZlJyxcbiAgICAgIH0sXG4gICAgXSxcbiAgICB1cGNvbWluZ1JlbmV3YWxzOiBbXG4gICAgICB7XG4gICAgICAgIGlkOiAnMicsXG4gICAgICAgIG5hbWU6ICdBZG9iZSBDcmVhdGl2ZSBTdWl0ZScsXG4gICAgICAgIHZlbmRvcjogJ0Fkb2JlJyxcbiAgICAgICAgcmVuZXdhbERhdGU6IG5ldyBEYXRlKCcyMDI1LTAzLTAxJyksXG4gICAgICAgIGNvc3Q6IDI0MDAsXG4gICAgICAgIHN0YXR1czogJ3BlbmRpbmcnLFxuICAgICAgfSxcbiAgICBdLFxuICB9LFxuICBcbiAgLy8gSGVscGVyIHRvIGNyZWF0ZSBtb2NrIEFQSSByZXNwb25zZXNcbiAgY3JlYXRlTW9ja0FwaVJlc3BvbnNlOiAoZGF0YSwgc3VjY2VzcyA9IHRydWUpID0+ICh7XG4gICAgb2s6IHN1Y2Nlc3MsXG4gICAgc3RhdHVzOiBzdWNjZXNzID8gMjAwIDogNDAwLFxuICAgIGpzb246IGplc3QuZm4oKS5tb2NrUmVzb2x2ZWRWYWx1ZShcbiAgICAgIHN1Y2Nlc3MgPyB7IHN1Y2Nlc3M6IHRydWUsIGRhdGEgfSA6IHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnVGVzdCBlcnJvcicgfVxuICAgICksXG4gIH0pLFxuICBcbiAgLy8gSGVscGVyIHRvIHdhaXQgZm9yIGFzeW5jIG9wZXJhdGlvbnNcbiAgd2FpdEZvcjogKGNhbGxiYWNrLCB0aW1lb3V0ID0gMTAwMCkgPT4ge1xuICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICBjb25zdCBzdGFydFRpbWUgPSBEYXRlLm5vdygpXG4gICAgICBjb25zdCBjaGVjayA9ICgpID0+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zdCByZXN1bHQgPSBjYWxsYmFjaygpXG4gICAgICAgICAgaWYgKHJlc3VsdCkge1xuICAgICAgICAgICAgcmVzb2x2ZShyZXN1bHQpXG4gICAgICAgICAgfSBlbHNlIGlmIChEYXRlLm5vdygpIC0gc3RhcnRUaW1lID4gdGltZW91dCkge1xuICAgICAgICAgICAgcmVqZWN0KG5ldyBFcnJvcignVGltZW91dCB3YWl0aW5nIGZvciBjb25kaXRpb24nKSlcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgc2V0VGltZW91dChjaGVjaywgMTApXG4gICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGlmIChEYXRlLm5vdygpIC0gc3RhcnRUaW1lID4gdGltZW91dCkge1xuICAgICAgICAgICAgcmVqZWN0KGVycm9yKVxuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBzZXRUaW1lb3V0KGNoZWNrLCAxMClcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIGNoZWNrKClcbiAgICB9KVxuICB9LFxufVxuXG4vLyBSZXNldCBhbGwgbW9ja3MgYmVmb3JlIGVhY2ggdGVzdFxuYmVmb3JlRWFjaCgoKSA9PiB7XG4gIGplc3QuY2xlYXJBbGxNb2NrcygpXG4gIFxuICAvLyBSZXNldCBsb2NhbFN0b3JhZ2VcbiAgbG9jYWxTdG9yYWdlTW9jay5nZXRJdGVtLm1vY2tDbGVhcigpXG4gIGxvY2FsU3RvcmFnZU1vY2suc2V0SXRlbS5tb2NrQ2xlYXIoKVxuICBsb2NhbFN0b3JhZ2VNb2NrLnJlbW92ZUl0ZW0ubW9ja0NsZWFyKClcbiAgbG9jYWxTdG9yYWdlTW9jay5jbGVhci5tb2NrQ2xlYXIoKVxuICBcbiAgLy8gUmVzZXQgc2Vzc2lvblN0b3JhZ2VcbiAgc2Vzc2lvblN0b3JhZ2VNb2NrLmdldEl0ZW0ubW9ja0NsZWFyKClcbiAgc2Vzc2lvblN0b3JhZ2VNb2NrLnNldEl0ZW0ubW9ja0NsZWFyKClcbiAgc2Vzc2lvblN0b3JhZ2VNb2NrLnJlbW92ZUl0ZW0ubW9ja0NsZWFyKClcbiAgc2Vzc2lvblN0b3JhZ2VNb2NrLmNsZWFyLm1vY2tDbGVhcigpXG4gIFxuICAvLyBSZXNldCBmZXRjaFxuICBmZXRjaC5tb2NrQ2xlYXIoKVxufSlcblxuLy8gR2xvYmFsIGVycm9yIGhhbmRsZXIgZm9yIHVuaGFuZGxlZCBwcm9taXNlIHJlamVjdGlvbnNcbnByb2Nlc3Mub24oJ3VuaGFuZGxlZFJlamVjdGlvbicsIChyZWFzb24sIHByb21pc2UpID0+IHtcbiAgY29uc29sZS5lcnJvcignVW5oYW5kbGVkIFJlamVjdGlvbiBhdDonLCBwcm9taXNlLCAncmVhc29uOicsIHJlYXNvbilcbn0pXG4iXSwibWFwcGluZ3MiOiI7O0FBT0FBLE9BQUE7QUFQQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBSUE7QUFDQUMsT0FBTyxDQUFDQyxHQUFHLENBQUNDLFFBQVEsR0FBRyxNQUFNO0FBQzdCRixPQUFPLENBQUNDLEdBQUcsQ0FBQ0Usc0JBQXNCLEdBQUcsY0FBYztBQUNuREgsT0FBTyxDQUFDQyxHQUFHLENBQUNHLHdCQUF3QixHQUFHLG1CQUFtQjtBQUMxREosT0FBTyxDQUFDQyxHQUFHLENBQUNJLCtCQUErQixHQUFHLGdCQUFnQjtBQUM5REwsT0FBTyxDQUFDQyxHQUFHLENBQUNLLDBCQUEwQixHQUFHLDBCQUEwQjtBQUNuRU4sT0FBTyxDQUFDQyxHQUFHLENBQUNNLFlBQVksR0FBRyx1REFBdUQ7O0FBRWxGO0FBQ0EsTUFBTUMsYUFBYSxHQUFHQyxPQUFPLENBQUNDLEtBQUs7QUFDbkMsTUFBTUMsWUFBWSxHQUFHRixPQUFPLENBQUNHLElBQUk7QUFFakNDLFNBQVMsQ0FBQyxNQUFNO0VBQ2RKLE9BQU8sQ0FBQ0MsS0FBSyxHQUFHLENBQUMsR0FBR0ksSUFBSSxLQUFLO0lBQzNCLElBQ0UsT0FBT0EsSUFBSSxDQUFDLENBQUMsQ0FBQyxLQUFLLFFBQVEsS0FDMUJBLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQ0MsUUFBUSxDQUFDLHdDQUF3QyxDQUFDLElBQzFERCxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUNDLFFBQVEsQ0FBQyxvQ0FBb0MsQ0FBQyxJQUN0REQsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDQyxRQUFRLENBQUMsNkJBQTZCLENBQUMsQ0FBQyxFQUNqRDtNQUNBO0lBQ0Y7SUFDQVAsYUFBYSxDQUFDUSxJQUFJLENBQUNQLE9BQU8sRUFBRSxHQUFHSyxJQUFJLENBQUM7RUFDdEMsQ0FBQztFQUVETCxPQUFPLENBQUNHLElBQUksR0FBRyxDQUFDLEdBQUdFLElBQUksS0FBSztJQUMxQixJQUNFLE9BQU9BLElBQUksQ0FBQyxDQUFDLENBQUMsS0FBSyxRQUFRLEtBQzFCQSxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUNDLFFBQVEsQ0FBQyw4QkFBOEIsQ0FBQyxJQUNoREQsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDQyxRQUFRLENBQUMsb0NBQW9DLENBQUMsQ0FBQyxFQUN4RDtNQUNBO0lBQ0Y7SUFDQUosWUFBWSxDQUFDSyxJQUFJLENBQUNQLE9BQU8sRUFBRSxHQUFHSyxJQUFJLENBQUM7RUFDckMsQ0FBQztBQUNILENBQUMsQ0FBQztBQUVGRyxRQUFRLENBQUMsTUFBTTtFQUNiUixPQUFPLENBQUNDLEtBQUssR0FBR0YsYUFBYTtFQUM3QkMsT0FBTyxDQUFDRyxJQUFJLEdBQUdELFlBQVk7QUFDN0IsQ0FBQyxDQUFDOztBQUVGO0FBQ0FPLE1BQU0sQ0FBQ0Msb0JBQW9CLEdBQUcsTUFBTUEsb0JBQW9CLENBQUM7RUFDdkRDLFdBQVdBLENBQUEsRUFBRyxDQUFDO0VBQ2ZDLFVBQVVBLENBQUEsRUFBRyxDQUFDO0VBQ2RDLE9BQU9BLENBQUEsRUFBRyxDQUFDO0VBQ1hDLFNBQVNBLENBQUEsRUFBRyxDQUFDO0FBQ2YsQ0FBQzs7QUFFRDtBQUNBTCxNQUFNLENBQUNNLGNBQWMsR0FBRyxNQUFNQSxjQUFjLENBQUM7RUFDM0NKLFdBQVdBLENBQUEsRUFBRyxDQUFDO0VBQ2ZDLFVBQVVBLENBQUEsRUFBRyxDQUFDO0VBQ2RDLE9BQU9BLENBQUEsRUFBRyxDQUFDO0VBQ1hDLFNBQVNBLENBQUEsRUFBRyxDQUFDO0FBQ2YsQ0FBQzs7QUFFRDtBQUNBRSxNQUFNLENBQUNDLGNBQWMsQ0FBQ0MsTUFBTSxFQUFFLFlBQVksRUFBRTtFQUMxQ0MsUUFBUSxFQUFFLElBQUk7RUFDZEMsS0FBSyxFQUFFQyxJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDLENBQUNDLGtCQUFrQixDQUFDQyxLQUFLLEtBQUs7SUFDNUNDLE9BQU8sRUFBRSxLQUFLO0lBQ2RDLEtBQUssRUFBRUYsS0FBSztJQUNaRyxRQUFRLEVBQUUsSUFBSTtJQUNkQyxXQUFXLEVBQUVQLElBQUksQ0FBQ0MsRUFBRSxDQUFDLENBQUM7SUFBRTtJQUN4Qk8sY0FBYyxFQUFFUixJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDO0lBQUU7SUFDM0JRLGdCQUFnQixFQUFFVCxJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDO0lBQzNCUyxtQkFBbUIsRUFBRVYsSUFBSSxDQUFDQyxFQUFFLENBQUMsQ0FBQztJQUM5QlUsYUFBYSxFQUFFWCxJQUFJLENBQUNDLEVBQUUsQ0FBQztFQUN6QixDQUFDLENBQUM7QUFDSixDQUFDLENBQUM7O0FBRUY7QUFDQU4sTUFBTSxDQUFDQyxjQUFjLENBQUNDLE1BQU0sRUFBRSxVQUFVLEVBQUU7RUFDeENDLFFBQVEsRUFBRSxJQUFJO0VBQ2RDLEtBQUssRUFBRUMsSUFBSSxDQUFDQyxFQUFFLENBQUM7QUFDakIsQ0FBQyxDQUFDOztBQUVGO0FBQ0EsTUFBTVcsZ0JBQWdCLEdBQUc7RUFDdkJDLE9BQU8sRUFBRWIsSUFBSSxDQUFDQyxFQUFFLENBQUMsQ0FBQztFQUNsQmEsT0FBTyxFQUFFZCxJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDO0VBQ2xCYyxVQUFVLEVBQUVmLElBQUksQ0FBQ0MsRUFBRSxDQUFDLENBQUM7RUFDckJlLEtBQUssRUFBRWhCLElBQUksQ0FBQ0MsRUFBRSxDQUFDO0FBQ2pCLENBQUM7QUFDRE4sTUFBTSxDQUFDQyxjQUFjLENBQUNDLE1BQU0sRUFBRSxjQUFjLEVBQUU7RUFDNUNFLEtBQUssRUFBRWE7QUFDVCxDQUFDLENBQUM7O0FBRUY7QUFDQSxNQUFNSyxrQkFBa0IsR0FBRztFQUN6QkosT0FBTyxFQUFFYixJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDO0VBQ2xCYSxPQUFPLEVBQUVkLElBQUksQ0FBQ0MsRUFBRSxDQUFDLENBQUM7RUFDbEJjLFVBQVUsRUFBRWYsSUFBSSxDQUFDQyxFQUFFLENBQUMsQ0FBQztFQUNyQmUsS0FBSyxFQUFFaEIsSUFBSSxDQUFDQyxFQUFFLENBQUM7QUFDakIsQ0FBQztBQUNETixNQUFNLENBQUNDLGNBQWMsQ0FBQ0MsTUFBTSxFQUFFLGdCQUFnQixFQUFFO0VBQzlDRSxLQUFLLEVBQUVrQjtBQUNULENBQUMsQ0FBQzs7QUFFRjtBQUNBN0IsTUFBTSxDQUFDOEIsS0FBSyxHQUFHbEIsSUFBSSxDQUFDQyxFQUFFLENBQUMsQ0FBQzs7QUFFeEI7QUFDQU4sTUFBTSxDQUFDQyxjQUFjLENBQUNDLE1BQU0sRUFBRSxhQUFhLEVBQUU7RUFDM0NDLFFBQVEsRUFBRSxJQUFJO0VBQ2RDLEtBQUssRUFBRTtJQUNMb0IsR0FBRyxFQUFFbkIsSUFBSSxDQUFDQyxFQUFFLENBQUMsTUFBTW1CLElBQUksQ0FBQ0QsR0FBRyxDQUFDLENBQUMsQ0FBQztJQUM5QkUsSUFBSSxFQUFFckIsSUFBSSxDQUFDQyxFQUFFLENBQUMsQ0FBQztJQUNmcUIsT0FBTyxFQUFFdEIsSUFBSSxDQUFDQyxFQUFFLENBQUMsQ0FBQztJQUNsQnNCLGdCQUFnQixFQUFFdkIsSUFBSSxDQUFDQyxFQUFFLENBQUMsTUFBTSxFQUFFLENBQUM7SUFDbkN1QixVQUFVLEVBQUV4QixJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDO0lBQ3JCd0IsYUFBYSxFQUFFekIsSUFBSSxDQUFDQyxFQUFFLENBQUMsQ0FBQztJQUN4QnlCLE1BQU0sRUFBRTtNQUNOQyxjQUFjLEVBQUUsT0FBTztNQUN2QkMsZUFBZSxFQUFFLE9BQU87TUFDeEJDLGVBQWUsRUFBRTtJQUNuQjtFQUNGO0FBQ0YsQ0FBQyxDQUFDOztBQUVGO0FBQ0FsQyxNQUFNLENBQUNDLGNBQWMsQ0FBQ1IsTUFBTSxFQUFFLFFBQVEsRUFBRTtFQUN0Q1csS0FBSyxFQUFFO0lBQ0wrQixVQUFVLEVBQUU5QixJQUFJLENBQUNDLEVBQUUsQ0FBQyxNQUFNLGdCQUFnQixDQUFDO0lBQzNDOEIsZUFBZSxFQUFFL0IsSUFBSSxDQUFDQyxFQUFFLENBQUUrQixHQUFHLElBQUs7TUFDaEMsS0FBSyxJQUFJQyxDQUFDLEdBQUcsQ0FBQyxFQUFFQSxDQUFDLEdBQUdELEdBQUcsQ0FBQ0UsTUFBTSxFQUFFRCxDQUFDLEVBQUUsRUFBRTtRQUNuQ0QsR0FBRyxDQUFDQyxDQUFDLENBQUMsR0FBR0UsSUFBSSxDQUFDQyxLQUFLLENBQUNELElBQUksQ0FBQ0UsTUFBTSxDQUFDLENBQUMsR0FBRyxHQUFHLENBQUM7TUFDMUM7TUFDQSxPQUFPTCxHQUFHO0lBQ1osQ0FBQztFQUNIO0FBQ0YsQ0FBQyxDQUFDOztBQUVGO0FBQ0E1QyxNQUFNLENBQUNrRCxHQUFHLENBQUNDLGVBQWUsR0FBR3ZDLElBQUksQ0FBQ0MsRUFBRSxDQUFDLE1BQU0sWUFBWSxDQUFDO0FBQ3hEYixNQUFNLENBQUNrRCxHQUFHLENBQUNFLGVBQWUsR0FBR3hDLElBQUksQ0FBQ0MsRUFBRSxDQUFDLENBQUM7O0FBRXRDO0FBQ0F3QyxNQUFNLENBQUNDLE1BQU0sQ0FBQztFQUNaQyxpQkFBaUJBLENBQUNDLFFBQVEsRUFBRTtJQUMxQixNQUFNQyxJQUFJLEdBQUdELFFBQVEsS0FBSyxJQUFJLElBQUlBLFFBQVEsS0FBS0UsU0FBUztJQUN4RCxPQUFPO01BQ0xDLE9BQU8sRUFBRUEsQ0FBQSxLQUFNLG9CQUFvQkYsSUFBSSxHQUFHLE1BQU0sR0FBRyxFQUFFLHVCQUF1QjtNQUM1RUE7SUFDRixDQUFDO0VBQ0g7QUFDRixDQUFDLENBQUM7O0FBRUY7QUFDQXpELE1BQU0sQ0FBQzRELFNBQVMsR0FBRztFQUNqQjtFQUNBQyxRQUFRLEVBQUU7SUFDUkMsRUFBRSxFQUFFLGNBQWM7SUFDbEJDLEtBQUssRUFBRSxrQkFBa0I7SUFDekJDLElBQUksRUFBRSxXQUFXO0lBQ2pCQyxRQUFRLEVBQUU7RUFDWixDQUFDO0VBRUQ7RUFDQUMsVUFBVSxFQUFFO0lBQ1ZELFFBQVEsRUFBRSxnQkFBZ0I7SUFDMUJFLFFBQVEsRUFBRSxnQkFBZ0I7SUFDMUJDLFVBQVUsRUFBRSxhQUFhO0lBQ3pCQyxNQUFNLEVBQUU7RUFDVixDQUFDO0VBRUQ7RUFDQUMsaUJBQWlCLEVBQUU7SUFDakJDLEtBQUssRUFBRTtNQUNMQyxhQUFhLEVBQUUsRUFBRTtNQUNqQkMsV0FBVyxFQUFFLENBQUM7TUFDZEMsT0FBTyxFQUFFLEVBQUU7TUFDWEMsV0FBVyxFQUFFO0lBQ2YsQ0FBQztJQUNEQyxjQUFjLEVBQUUsQ0FDZDtNQUNFZCxFQUFFLEVBQUUsR0FBRztNQUNQRSxJQUFJLEVBQUUsc0JBQXNCO01BQzVCYSxNQUFNLEVBQUUsV0FBVztNQUNuQkMsV0FBVyxFQUFFLElBQUk5QyxJQUFJLENBQUMsWUFBWSxDQUFDO01BQ25DK0MsSUFBSSxFQUFFLElBQUk7TUFDVkMsTUFBTSxFQUFFO0lBQ1YsQ0FBQyxDQUNGO0lBQ0RDLGdCQUFnQixFQUFFLENBQ2hCO01BQ0VuQixFQUFFLEVBQUUsR0FBRztNQUNQRSxJQUFJLEVBQUUsc0JBQXNCO01BQzVCYSxNQUFNLEVBQUUsT0FBTztNQUNmQyxXQUFXLEVBQUUsSUFBSTlDLElBQUksQ0FBQyxZQUFZLENBQUM7TUFDbkMrQyxJQUFJLEVBQUUsSUFBSTtNQUNWQyxNQUFNLEVBQUU7SUFDVixDQUFDO0VBRUwsQ0FBQztFQUVEO0VBQ0FFLHFCQUFxQixFQUFFQSxDQUFDQyxJQUFJLEVBQUVDLE9BQU8sR0FBRyxJQUFJLE1BQU07SUFDaERDLEVBQUUsRUFBRUQsT0FBTztJQUNYSixNQUFNLEVBQUVJLE9BQU8sR0FBRyxHQUFHLEdBQUcsR0FBRztJQUMzQkUsSUFBSSxFQUFFMUUsSUFBSSxDQUFDQyxFQUFFLENBQUMsQ0FBQyxDQUFDMEUsaUJBQWlCLENBQy9CSCxPQUFPLEdBQUc7TUFBRUEsT0FBTyxFQUFFLElBQUk7TUFBRUQ7SUFBSyxDQUFDLEdBQUc7TUFBRUMsT0FBTyxFQUFFLEtBQUs7TUFBRTVGLEtBQUssRUFBRTtJQUFhLENBQzVFO0VBQ0YsQ0FBQyxDQUFDO0VBRUY7RUFDQWdHLE9BQU8sRUFBRUEsQ0FBQ0MsUUFBUSxFQUFFQyxPQUFPLEdBQUcsSUFBSSxLQUFLO0lBQ3JDLE9BQU8sSUFBSUMsT0FBTyxDQUFDLENBQUNDLE9BQU8sRUFBRUMsTUFBTSxLQUFLO01BQ3RDLE1BQU1DLFNBQVMsR0FBRzlELElBQUksQ0FBQ0QsR0FBRyxDQUFDLENBQUM7TUFDNUIsTUFBTWdFLEtBQUssR0FBR0EsQ0FBQSxLQUFNO1FBQ2xCLElBQUk7VUFDRixNQUFNQyxNQUFNLEdBQUdQLFFBQVEsQ0FBQyxDQUFDO1VBQ3pCLElBQUlPLE1BQU0sRUFBRTtZQUNWSixPQUFPLENBQUNJLE1BQU0sQ0FBQztVQUNqQixDQUFDLE1BQU0sSUFBSWhFLElBQUksQ0FBQ0QsR0FBRyxDQUFDLENBQUMsR0FBRytELFNBQVMsR0FBR0osT0FBTyxFQUFFO1lBQzNDRyxNQUFNLENBQUMsSUFBSUksS0FBSyxDQUFDLCtCQUErQixDQUFDLENBQUM7VUFDcEQsQ0FBQyxNQUFNO1lBQ0xDLFVBQVUsQ0FBQ0gsS0FBSyxFQUFFLEVBQUUsQ0FBQztVQUN2QjtRQUNGLENBQUMsQ0FBQyxPQUFPdkcsS0FBSyxFQUFFO1VBQ2QsSUFBSXdDLElBQUksQ0FBQ0QsR0FBRyxDQUFDLENBQUMsR0FBRytELFNBQVMsR0FBR0osT0FBTyxFQUFFO1lBQ3BDRyxNQUFNLENBQUNyRyxLQUFLLENBQUM7VUFDZixDQUFDLE1BQU07WUFDTDBHLFVBQVUsQ0FBQ0gsS0FBSyxFQUFFLEVBQUUsQ0FBQztVQUN2QjtRQUNGO01BQ0YsQ0FBQztNQUNEQSxLQUFLLENBQUMsQ0FBQztJQUNULENBQUMsQ0FBQztFQUNKO0FBQ0YsQ0FBQzs7QUFFRDtBQUNBSSxVQUFVLENBQUMsTUFBTTtFQUNmdkYsSUFBSSxDQUFDd0YsYUFBYSxDQUFDLENBQUM7O0VBRXBCO0VBQ0E1RSxnQkFBZ0IsQ0FBQ0MsT0FBTyxDQUFDNEUsU0FBUyxDQUFDLENBQUM7RUFDcEM3RSxnQkFBZ0IsQ0FBQ0UsT0FBTyxDQUFDMkUsU0FBUyxDQUFDLENBQUM7RUFDcEM3RSxnQkFBZ0IsQ0FBQ0csVUFBVSxDQUFDMEUsU0FBUyxDQUFDLENBQUM7RUFDdkM3RSxnQkFBZ0IsQ0FBQ0ksS0FBSyxDQUFDeUUsU0FBUyxDQUFDLENBQUM7O0VBRWxDO0VBQ0F4RSxrQkFBa0IsQ0FBQ0osT0FBTyxDQUFDNEUsU0FBUyxDQUFDLENBQUM7RUFDdEN4RSxrQkFBa0IsQ0FBQ0gsT0FBTyxDQUFDMkUsU0FBUyxDQUFDLENBQUM7RUFDdEN4RSxrQkFBa0IsQ0FBQ0YsVUFBVSxDQUFDMEUsU0FBUyxDQUFDLENBQUM7RUFDekN4RSxrQkFBa0IsQ0FBQ0QsS0FBSyxDQUFDeUUsU0FBUyxDQUFDLENBQUM7O0VBRXBDO0VBQ0F2RSxLQUFLLENBQUN1RSxTQUFTLENBQUMsQ0FBQztBQUNuQixDQUFDLENBQUM7O0FBRUY7QUFDQXZILE9BQU8sQ0FBQ3dILEVBQUUsQ0FBQyxvQkFBb0IsRUFBRSxDQUFDQyxNQUFNLEVBQUVDLE9BQU8sS0FBSztFQUNwRGpILE9BQU8sQ0FBQ0MsS0FBSyxDQUFDLHlCQUF5QixFQUFFZ0gsT0FBTyxFQUFFLFNBQVMsRUFBRUQsTUFBTSxDQUFDO0FBQ3RFLENBQUMsQ0FBQyIsImlnbm9yZUxpc3QiOltdfQ==