-- Create metadata schema for reference data
CREATE SCHEMA IF NOT EXISTS metadata;

-- Purchase Types table
CREATE TABLE IF NOT EXISTS metadata.PurchaseTypes (
    PurchaseTypeID SERIAL PRIMARY KEY,
    PurchaseTypeName VARCHAR(100) NOT NULL UNIQUE,
    Active BOOLEAN NOT NULL DEFAULT true,
    DisplayOrder INTEGER DEFAULT 0,
    CreatedAt TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Renewal Types table
CREATE TABLE IF NOT EXISTS metadata.RenewalTypes (
    RenewalTypeID SERIAL PRIMARY KEY,
    RenewalTypeName VARCHAR(100) NOT NULL UNIQUE,
    Active BOOLEAN NOT NULL DEFAULT true,
    DisplayOrder INTEGER DEFAULT 0,
    CreatedAt TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Currencies table
CREATE TABLE IF NOT EXISTS metadata.Currencies (
    CurrencyID CHAR(3) PRIMARY KEY,
    CurrencyName VARCHAR(100) NOT NULL,
    CurrencySymbol VARCHAR(10),
    Active BOOLEAN NOT NULL DEFAULT true,
    DisplayOrder INTEGER DEFAULT 0,
    CreatedAt TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_purchase_types_active ON metadata.PurchaseTypes(Active, DisplayOrder);
CREATE INDEX IF NOT EXISTS idx_renewal_types_active ON metadata.RenewalTypes(Active, DisplayOrder);
CREATE INDEX IF NOT EXISTS idx_currencies_active ON metadata.Currencies(Active, DisplayOrder);

-- Insert default Purchase Types
INSERT INTO metadata.PurchaseTypes (PurchaseTypeName, Active, DisplayOrder)
VALUES 
    ('Direct From Vendor', true, 1),
    ('Through Reseller', true, 2),
    ('Marketplace', true, 3),
    ('Internal Development', true, 4)
ON CONFLICT (PurchaseTypeName) DO NOTHING;

-- Insert default Renewal Types
INSERT INTO metadata.RenewalTypes (RenewalTypeName, Active, DisplayOrder)
VALUES 
    ('Subscription', true, 1),
    ('Renewal', true, 2),
    ('Warranty', true, 3),
    ('CapEx', true, 4),
    ('Support Contract', true, 5),
    ('Maintenance', true, 6)
ON CONFLICT (RenewalTypeName) DO NOTHING;

-- Insert default Currencies
INSERT INTO metadata.Currencies (CurrencyID, CurrencyName, CurrencySymbol, Active, DisplayOrder)
VALUES 
    ('USD', 'US Dollar', '$', true, 1),
    ('EUR', 'Euro', '€', true, 2),
    ('GBP', 'British Pound', '£', true, 3),
    ('CAD', 'Canadian Dollar', 'C$', true, 4),
    ('AUD', 'Australian Dollar', 'A$', true, 5),
    ('JPY', 'Japanese Yen', '¥', true, 6),
    ('CHF', 'Swiss Franc', 'CHF', true, 7),
    ('SEK', 'Swedish Krona', 'kr', true, 8),
    ('NOK', 'Norwegian Krone', 'kr', true, 9),
    ('DKK', 'Danish Krone', 'kr', true, 10)
ON CONFLICT (CurrencyID) DO NOTHING;

-- Create function to update UpdatedAt timestamp
CREATE OR REPLACE FUNCTION metadata.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.UpdatedAt = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update UpdatedAt
CREATE TRIGGER update_purchase_types_updated_at 
    BEFORE UPDATE ON metadata.PurchaseTypes 
    FOR EACH ROW EXECUTE FUNCTION metadata.update_updated_at_column();

CREATE TRIGGER update_renewal_types_updated_at 
    BEFORE UPDATE ON metadata.RenewalTypes 
    FOR EACH ROW EXECUTE FUNCTION metadata.update_updated_at_column();

CREATE TRIGGER update_currencies_updated_at 
    BEFORE UPDATE ON metadata.Currencies 
    FOR EACH ROW EXECUTE FUNCTION metadata.update_updated_at_column();

-- Grant permissions (adjust as needed for your security model)
-- GRANT USAGE ON SCHEMA metadata TO your_app_user;
-- GRANT SELECT ON ALL TABLES IN SCHEMA metadata TO your_app_user;
-- GRANT SELECT ON ALL SEQUENCES IN SCHEMA metadata TO your_app_user;

-- Comments for documentation
COMMENT ON SCHEMA metadata IS 'Schema containing reference data and metadata tables';
COMMENT ON TABLE metadata.PurchaseTypes IS 'Reference table for purchase type options';
COMMENT ON TABLE metadata.RenewalTypes IS 'Reference table for renewal type options';
COMMENT ON TABLE metadata.Currencies IS 'Reference table for currency options';

COMMENT ON COLUMN metadata.PurchaseTypes.DisplayOrder IS 'Order for displaying in dropdowns (lower numbers first)';
COMMENT ON COLUMN metadata.RenewalTypes.DisplayOrder IS 'Order for displaying in dropdowns (lower numbers first)';
COMMENT ON COLUMN metadata.Currencies.DisplayOrder IS 'Order for displaying in dropdowns (lower numbers first)';
