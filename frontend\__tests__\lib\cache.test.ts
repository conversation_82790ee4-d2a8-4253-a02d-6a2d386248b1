/**
 * Cache System Tests
 * 
 * Tests for the advanced caching system including different eviction strategies,
 * TTL handling, and performance metrics
 */

import { AdvancedCache, cacheUtils } from '@/lib/cache'
import { jest } from '@jest/globals'

describe('Advanced Cache System', () => {
  let cache: AdvancedCache<string>

  beforeEach(() => {
    jest.clearAllMocks()
    cache = new AdvancedCache({
      maxSize: 3,
      defaultTTL: 1000, // 1 second for testing
      cleanupInterval: 100, // 100ms for testing
      enableMetrics: true,
    }, 'lru')
  })

  afterEach(() => {
    cache.destroy()
  })

  describe('Basic Operations', () => {
    it('should store and retrieve values', () => {
      cache.set('key1', 'value1')
      expect(cache.get('key1')).toBe('value1')
    })

    it('should return null for non-existent keys', () => {
      expect(cache.get('nonexistent')).toBe(null)
    })

    it('should check if key exists', () => {
      cache.set('key1', 'value1')
      expect(cache.has('key1')).toBe(true)
      expect(cache.has('nonexistent')).toBe(false)
    })

    it('should delete keys', () => {
      cache.set('key1', 'value1')
      expect(cache.delete('key1')).toBe(true)
      expect(cache.get('key1')).toBe(null)
      expect(cache.delete('nonexistent')).toBe(false)
    })

    it('should clear all entries', () => {
      cache.set('key1', 'value1')
      cache.set('key2', 'value2')
      cache.clear()
      expect(cache.size()).toBe(0)
      expect(cache.get('key1')).toBe(null)
    })

    it('should return correct size', () => {
      expect(cache.size()).toBe(0)
      cache.set('key1', 'value1')
      expect(cache.size()).toBe(1)
      cache.set('key2', 'value2')
      expect(cache.size()).toBe(2)
    })

    it('should return all keys', () => {
      cache.set('key1', 'value1')
      cache.set('key2', 'value2')
      const keys = cache.keys()
      expect(keys).toContain('key1')
      expect(keys).toContain('key2')
      expect(keys).toHaveLength(2)
    })
  })

  describe('TTL (Time To Live)', () => {
    it('should expire entries after TTL', async () => {
      cache.set('key1', 'value1', 50) // 50ms TTL
      expect(cache.get('key1')).toBe('value1')

      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 100))
      expect(cache.get('key1')).toBe(null)
    })

    it('should use default TTL when not specified', async () => {
      cache.set('key1', 'value1') // Uses default TTL (1000ms)
      expect(cache.get('key1')).toBe('value1')

      // Should still be valid after short time
      await new Promise(resolve => setTimeout(resolve, 100))
      expect(cache.get('key1')).toBe('value1')
    })

    it('should handle custom TTL per entry', () => {
      cache.set('short', 'value1', 50)
      cache.set('long', 'value2', 2000)

      setTimeout(() => {
        expect(cache.get('short')).toBe(null)
        expect(cache.get('long')).toBe('value2')
      }, 100)
    })
  })

  describe('Eviction Strategies', () => {
    describe('LRU (Least Recently Used)', () => {
      beforeEach(() => {
        cache = new AdvancedCache({ maxSize: 3 }, 'lru')
      })

      it('should evict least recently used item', () => {
        cache.set('key1', 'value1')
        cache.set('key2', 'value2')
        cache.set('key3', 'value3')

        // Access key1 to make it recently used
        cache.get('key1')

        // Add new item, should evict key2 (least recently used)
        cache.set('key4', 'value4')

        expect(cache.get('key1')).toBe('value1')
        expect(cache.get('key2')).toBe(null)
        expect(cache.get('key3')).toBe('value3')
        expect(cache.get('key4')).toBe('value4')
      })
    })

    describe('LFU (Least Frequently Used)', () => {
      beforeEach(() => {
        cache = new AdvancedCache({ maxSize: 3 }, 'lfu')
      })

      it('should evict least frequently used item', () => {
        cache.set('key1', 'value1')
        cache.set('key2', 'value2')
        cache.set('key3', 'value3')

        // Access key1 multiple times
        cache.get('key1')
        cache.get('key1')
        cache.get('key2') // key2 accessed once, key3 never accessed

        // Add new item, should evict key3 (least frequently used)
        cache.set('key4', 'value4')

        expect(cache.get('key1')).toBe('value1')
        expect(cache.get('key2')).toBe('value2')
        expect(cache.get('key3')).toBe(null)
        expect(cache.get('key4')).toBe('value4')
      })
    })

    describe('FIFO (First In, First Out)', () => {
      beforeEach(() => {
        cache = new AdvancedCache({ maxSize: 3 }, 'fifo')
      })

      it('should evict first inserted item', () => {
        cache.set('key1', 'value1')
        cache.set('key2', 'value2')
        cache.set('key3', 'value3')

        // Add new item, should evict key1 (first in)
        cache.set('key4', 'value4')

        expect(cache.get('key1')).toBe(null)
        expect(cache.get('key2')).toBe('value2')
        expect(cache.get('key3')).toBe('value3')
        expect(cache.get('key4')).toBe('value4')
      })
    })
  })

  describe('Tag-based Invalidation', () => {
    it('should clear entries by tags', () => {
      cache.set('user1', 'data1', undefined, ['user', 'profile'])
      cache.set('user2', 'data2', undefined, ['user', 'settings'])
      cache.set('admin1', 'data3', undefined, ['admin', 'profile'])

      const cleared = cache.clearByTags(['user'])
      expect(cleared).toBe(2)

      expect(cache.get('user1')).toBe(null)
      expect(cache.get('user2')).toBe(null)
      expect(cache.get('admin1')).toBe('data3')
    })

    it('should clear entries with multiple matching tags', () => {
      cache.set('item1', 'data1', undefined, ['tag1', 'tag2'])
      cache.set('item2', 'data2', undefined, ['tag2', 'tag3'])
      cache.set('item3', 'data3', undefined, ['tag3', 'tag4'])

      const cleared = cache.clearByTags(['tag2', 'tag4'])
      expect(cleared).toBe(3) // All items have at least one matching tag

      expect(cache.get('item1')).toBe(null)
      expect(cache.get('item2')).toBe(null)
      expect(cache.get('item3')).toBe(null)
    })
  })

  describe('Metrics', () => {
    it('should track hit and miss rates', () => {
      cache.set('key1', 'value1')

      // Hit
      cache.get('key1')
      // Miss
      cache.get('nonexistent')

      const metrics = cache.getMetrics()
      expect(metrics.hits).toBe(1)
      expect(metrics.misses).toBe(1)
      expect(metrics.hitRate).toBe(0.5)
    })

    it('should track cache size', () => {
      cache.set('key1', 'value1')
      cache.set('key2', 'value2')

      const metrics = cache.getMetrics()
      expect(metrics.size).toBe(2)
    })

    it('should track evictions', () => {
      // Fill cache to capacity
      cache.set('key1', 'value1')
      cache.set('key2', 'value2')
      cache.set('key3', 'value3')

      // This should trigger eviction
      cache.set('key4', 'value4')

      const metrics = cache.getMetrics()
      expect(metrics.evictions).toBe(1)
    })

    it('should calculate hit rate correctly', () => {
      cache.set('key1', 'value1')

      // 3 hits, 2 misses = 60% hit rate
      cache.get('key1') // hit
      cache.get('key1') // hit
      cache.get('key1') // hit
      cache.get('nonexistent1') // miss
      cache.get('nonexistent2') // miss

      const metrics = cache.getMetrics()
      expect(metrics.hitRate).toBe(0.6)
    })
  })

  describe('Cleanup', () => {
    it('should automatically clean up expired entries', async () => {
      cache.set('key1', 'value1', 50) // 50ms TTL
      cache.set('key2', 'value2', 2000) // 2s TTL

      expect(cache.size()).toBe(2)

      // Wait for cleanup cycle
      await new Promise(resolve => setTimeout(resolve, 200))

      expect(cache.get('key1')).toBe(null)
      expect(cache.get('key2')).toBe('value2')
    })

    it('should destroy cache and stop cleanup', () => {
      const spy = jest.spyOn(global, 'clearInterval')
      cache.destroy()
      expect(spy).toHaveBeenCalled()
      expect(cache.size()).toBe(0)
    })
  })

  describe('Cache Utils', () => {
    it('should create cache keys correctly', () => {
      const key = cacheUtils.createKey('user', 'id123', 'profile')
      expect(key).toBe('user:id123:profile')
    })

    it('should serialize objects correctly', () => {
      const obj = { name: 'test', date: new Date('2025-01-01') }
      const serialized = cacheUtils.serialize(obj)
      expect(typeof serialized).toBe('string')
      expect(serialized).toContain('test')
    })

    it('should deserialize objects correctly', () => {
      const obj = { name: 'test', date: new Date('2025-01-01') }
      const serialized = cacheUtils.serialize(obj)
      const deserialized = cacheUtils.deserialize(serialized)

      expect(deserialized.name).toBe('test')
      expect(deserialized.date).toBeInstanceOf(Date)
    })

    it('should handle serialization errors gracefully', () => {
      const circular: any = {}
      circular.self = circular

      const result = cacheUtils.serialize(circular)
      expect(typeof result).toBe('string')
    })

    it('should handle deserialization errors gracefully', () => {
      const result = cacheUtils.deserialize('invalid json')
      expect(result).toBe(null)
    })

    it('should get global cache statistics', () => {
      const stats = cacheUtils.getGlobalStats()
      expect(stats).toHaveProperty('api')
      expect(stats).toHaveProperty('component')
      expect(stats).toHaveProperty('userData')
    })

    it('should clear all global caches', () => {
      expect(() => cacheUtils.clearAll()).not.toThrow()
    })
  })

  describe('Error Handling', () => {
    it('should handle invalid TTL values', () => {
      expect(() => cache.set('key', 'value', -1)).not.toThrow()
      expect(() => cache.set('key', 'value', 0)).not.toThrow()
    })

    it('should handle empty cache operations', () => {
      expect(cache.get('nonexistent')).toBe(null)
      expect(cache.delete('nonexistent')).toBe(false)
      expect(cache.clearByTags(['nonexistent'])).toBe(0)
    })

    it('should handle large cache operations', () => {
      // Test with many entries
      for (let i = 0; i < 100; i++) {
        cache.set(`key${i}`, `value${i}`)
      }

      expect(cache.size()).toBeLessThanOrEqual(cache['config'].maxSize)
    })
  })
})
