{"version": 3, "names": ["_default", "console", "log", "process", "env", "NODE_ENV", "TZ", "AWS_REGION", "AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY", "DATABASE_URL", "DATABASE_HOST", "DATABASE_PORT", "DATABASE_NAME", "DATABASE_USER", "DATABASE_PASSWORD", "JWT_SECRET", "JWT_EXPIRES_IN", "NEXT_PUBLIC_USER_POOL_ID", "NEXT_PUBLIC_USER_POOL_CLIENT_ID", "NEXT_PUBLIC_COGNITO_DOMAIN", "API_BASE_URL", "NEXT_PUBLIC_API_URL", "originalWarn", "warn", "args", "includes", "call", "exports", "default"], "sources": ["jest.global-setup.js"], "sourcesContent": ["/**\n * Jest Global Setup\n * \n * Runs once before all tests start.\n * Used for global test environment setup.\n */\n\nexport default async () => {\n  console.log('🧪 Setting up test environment...')\n  \n  // Set test environment variables\n  process.env.NODE_ENV = 'test'\n  process.env.TZ = 'UTC'\n  \n  // Mock AWS configuration\n  process.env.AWS_REGION = 'ca-central-1'\n  process.env.AWS_ACCESS_KEY_ID = 'test-access-key'\n  process.env.AWS_SECRET_ACCESS_KEY = 'test-secret-key'\n  \n  // Mock database configuration\n  process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_renewtrack'\n  process.env.DATABASE_HOST = 'localhost'\n  process.env.DATABASE_PORT = '5432'\n  process.env.DATABASE_NAME = 'test_renewtrack'\n  process.env.DATABASE_USER = 'test'\n  process.env.DATABASE_PASSWORD = 'test'\n  \n  // Mock JWT configuration\n  process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only'\n  process.env.JWT_EXPIRES_IN = '1h'\n  \n  // Mock Cognito configuration\n  process.env.NEXT_PUBLIC_USER_POOL_ID = 'ca-central-1_testpool'\n  process.env.NEXT_PUBLIC_USER_POOL_CLIENT_ID = 'test-client-id'\n  process.env.NEXT_PUBLIC_COGNITO_DOMAIN = 'test.auth.renewtrack.com'\n  \n  // Mock API configuration\n  process.env.API_BASE_URL = 'http://localhost:3000/api'\n  process.env.NEXT_PUBLIC_API_URL = 'http://localhost:3000/api'\n  \n  // Disable console warnings for tests\n  const originalWarn = console.warn\n  console.warn = (...args) => {\n    if (\n      typeof args[0] === 'string' &&\n      (args[0].includes('Warning:') || \n       args[0].includes('deprecated') ||\n       args[0].includes('componentWillReceiveProps'))\n    ) {\n      return\n    }\n    originalWarn.call(console, ...args)\n  }\n  \n  console.log('✅ Test environment setup complete')\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AALA,IAAAA,QAAA,GAOe,MAAAA,CAAA,KAAY;EACzBC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;EAEhD;EACAC,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAG,MAAM;EAC7BF,OAAO,CAACC,GAAG,CAACE,EAAE,GAAG,KAAK;;EAEtB;EACAH,OAAO,CAACC,GAAG,CAACG,UAAU,GAAG,cAAc;EACvCJ,OAAO,CAACC,GAAG,CAACI,iBAAiB,GAAG,iBAAiB;EACjDL,OAAO,CAACC,GAAG,CAACK,qBAAqB,GAAG,iBAAiB;;EAErD;EACAN,OAAO,CAACC,GAAG,CAACM,YAAY,GAAG,uDAAuD;EAClFP,OAAO,CAACC,GAAG,CAACO,aAAa,GAAG,WAAW;EACvCR,OAAO,CAACC,GAAG,CAACQ,aAAa,GAAG,MAAM;EAClCT,OAAO,CAACC,GAAG,CAACS,aAAa,GAAG,iBAAiB;EAC7CV,OAAO,CAACC,GAAG,CAACU,aAAa,GAAG,MAAM;EAClCX,OAAO,CAACC,GAAG,CAACW,iBAAiB,GAAG,MAAM;;EAEtC;EACAZ,OAAO,CAACC,GAAG,CAACY,UAAU,GAAG,sCAAsC;EAC/Db,OAAO,CAACC,GAAG,CAACa,cAAc,GAAG,IAAI;;EAEjC;EACAd,OAAO,CAACC,GAAG,CAACc,wBAAwB,GAAG,uBAAuB;EAC9Df,OAAO,CAACC,GAAG,CAACe,+BAA+B,GAAG,gBAAgB;EAC9DhB,OAAO,CAACC,GAAG,CAACgB,0BAA0B,GAAG,0BAA0B;;EAEnE;EACAjB,OAAO,CAACC,GAAG,CAACiB,YAAY,GAAG,2BAA2B;EACtDlB,OAAO,CAACC,GAAG,CAACkB,mBAAmB,GAAG,2BAA2B;;EAE7D;EACA,MAAMC,YAAY,GAAGtB,OAAO,CAACuB,IAAI;EACjCvB,OAAO,CAACuB,IAAI,GAAG,CAAC,GAAGC,IAAI,KAAK;IAC1B,IACE,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,KAC1BA,IAAI,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,UAAU,CAAC,IAC5BD,IAAI,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,YAAY,CAAC,IAC9BD,IAAI,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,2BAA2B,CAAC,CAAC,EAC/C;MACA;IACF;IACAH,YAAY,CAACI,IAAI,CAAC1B,OAAO,EAAE,GAAGwB,IAAI,CAAC;EACrC,CAAC;EAEDxB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;AAClD,CAAC;AAAA0B,OAAA,CAAAC,OAAA,GAAA7B,QAAA", "ignoreList": []}