/**
 * Users API Route
 * 
 * This route demonstrates best practices for API implementation including:
 * - Secure authentication and authorization
 * - Input validation with Zod schemas
 * - Standardized error handling
 * - Proper database operations
 * - Rate limiting
 * - Security headers
 */

import { NextRequest } from 'next/server';
import { requireAuth, requireRole, checkRateLimit, getRateLimitIdentifier } from '@/lib/auth-middleware';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  createRateLimitResponse,
  ApiErrorCode, 
  HttpStatus,
  withErrorHandling 
} from '@/lib/api-response';
import { 
  validateRequestBody, 
  validateQueryParams,
  userCreateSchema,
  paginationSchema,
  PaginationParams,
  UserCreateData 
} from '@/lib/validation';
import { executeQuery, executeQuerySingle } from '@/lib/database';

// User interface
interface User {
  id: string;
  email: string;
  name?: string;
  given_name?: string;
  family_name?: string;
  roles: string[];
  created_at: Date;
  updated_at?: Date;
  last_login?: Date;
}

// GET /api/users - List users with pagination
export const GET = withErrorHandling(async (request: NextRequest) => {
  // Rate limiting
  const authResult = await requireAuth();
  if (authResult.success) {
    const rateLimitId = getRateLimitIdentifier(request, authResult.session);
    const rateLimit = checkRateLimit(rateLimitId, 100, 60000); // 100 requests per minute
    
    if (!rateLimit.allowed) {
      return createRateLimitResponse();
    }
  }

  // Verify authentication and admin role
  const roleResult = await requireRole(['admin', 'user_manager']);
  if (!roleResult.success) {
    return roleResult.response!;
  }

  const session = roleResult.session!;

  // Validate query parameters
  const { searchParams } = new URL(request.url);
  const queryValidation = validateQueryParams(searchParams, paginationSchema);
  
  if (!queryValidation.success) {
    return queryValidation.response;
  }

  const { page, limit, sortBy, sortOrder }: PaginationParams = queryValidation.data;

  // Calculate offset for pagination
  const offset = (page - 1) * limit;

  // Build query with proper sorting
  const allowedSortFields = ['email', 'name', 'created_at', 'last_login'];
  const sortField = allowedSortFields.includes(sortBy || '') ? sortBy : 'created_at';
  
  const query = `
    SELECT 
      id,
      email,
      name,
      given_name,
      family_name,
      roles,
      created_at,
      updated_at,
      last_login,
      COUNT(*) OVER() as total_count
    FROM users
    ORDER BY ${sortField} ${sortOrder.toUpperCase()}
    LIMIT $1 OFFSET $2
  `;

  const result = await executeQuery<User & { total_count: number }>(
    query, 
    [limit, offset],
    { timeout: 10000 }
  );

  if (!result.success) {
    return createErrorResponse(
      'Failed to fetch users',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  const users = result.data || [];
  const totalCount = users.length > 0 ? parseInt(users[0].total_count.toString()) : 0;
  const totalPages = Math.ceil(totalCount / limit);

  return createSuccessResponse({
    users: users.map(({ total_count, ...user }) => user),
    pagination: {
      page,
      limit,
      totalCount,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  }, 'Users retrieved successfully');
});

// POST /api/users - Create a new user
export const POST = withErrorHandling(async (request: NextRequest) => {
  // Verify authentication and admin role
  const roleResult = await requireRole(['admin', 'user_manager']);
  if (!roleResult.success) {
    return roleResult.response!;
  }

  const session = roleResult.session!;

  // Rate limiting for user creation (more restrictive)
  const rateLimitId = getRateLimitIdentifier(request, session);
  const rateLimit = checkRateLimit(rateLimitId, 10, 60000); // 10 user creations per minute
  
  if (!rateLimit.allowed) {
    return createRateLimitResponse();
  }

  // Validate request body
  const bodyValidation = await validateRequestBody(request, userCreateSchema);
  
  if (!bodyValidation.success) {
    return bodyValidation.response;
  }

  const userData: UserCreateData = bodyValidation.data;

  // Check if user already exists
  const existingUserQuery = 'SELECT id FROM users WHERE email = $1';
  const existingUser = await executeQuerySingle(existingUserQuery, [userData.email]);

  if (existingUser.success && existingUser.data) {
    return createErrorResponse(
      'User with this email already exists',
      ApiErrorCode.VALIDATION_ERROR,
      HttpStatus.CONFLICT
    );
  }

  // Create new user
  const insertQuery = `
    INSERT INTO users (
      email, 
      name, 
      given_name, 
      family_name, 
      roles,
      created_at
    ) 
    VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
    RETURNING 
      id,
      email,
      name,
      given_name,
      family_name,
      roles,
      created_at
  `;

  const insertResult = await executeQuerySingle<User>(
    insertQuery,
    [
      userData.email,
      userData.name || null,
      userData.given_name || null,
      userData.family_name || null,
      JSON.stringify(userData.roles)
    ]
  );

  if (!insertResult.success) {
    return createErrorResponse(
      'Failed to create user',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  // Log user creation for audit
  console.log(`User created: ${userData.email} by ${session.email}`);

  return createSuccessResponse(
    insertResult.data,
    'User created successfully',
    HttpStatus.CREATED
  );
});

// OPTIONS /api/users - CORS preflight
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
