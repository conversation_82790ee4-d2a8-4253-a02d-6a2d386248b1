{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "useTenant", "mockUse<PERSON><PERSON>t", "<PERSON><PERSON><PERSON><PERSON>", "mockApiCache", "cacheUtils", "create<PERSON><PERSON>", "jest", "fn", "prefix", "id", "useDebounce", "value", "performanceUtils", "mark", "measure", "_interopRequireDefault", "require", "_defineProperty2", "_react", "_globals", "_useDashboardData", "_testUtils", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "default", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "get", "set", "has", "delete", "clear", "describe", "mockTenant", "testUtils", "generateTestData", "tenant", "mockStats", "dashboardStats", "mockRenewals", "renewal", "beforeEach", "clearAllMocks", "mockReturnValue", "mockImplementation", "loading", "error", "global", "fetch", "mockResolvedValueOnce", "mockApiSuccess", "it", "result", "renderHook", "useDashboardData", "expect", "current", "isLoading", "toBe", "data", "toEqual", "stats", "totalRenewals", "renewalsDue", "vendors", "annualSpend", "recentRenewals", "upcoming<PERSON><PERSON><PERSON><PERSON>", "tenantError", "waitFor", "errorMessage", "mockApiError", "mockRejectedValueOnce", "Error", "toContain", "rerender", "newTenant", "tenantId", "toHaveBeenCalledWith", "any", "mockReturnValueOnce", "not", "toHaveBeenCalled", "Number", "Array", "abortSpy", "AbortController", "abort", "signal", "refetch", "rejects", "toThrow", "toBeTruthy", "unmount", "fetchPromise", "resolves"], "sources": ["useDashboardData.test.ts"], "sourcesContent": ["/**\n * Dashboard Data Hook Tests\n * \n * Tests for the useDashboardData hook including data fetching,\n * caching, error handling, and performance optimizations\n */\n\nimport { renderHook, waitFor } from '@testing-library/react'\nimport { jest } from '@jest/globals'\nimport { useDashboardData } from '@/hooks/useDashboardData'\nimport { testUtils } from '../utils/test-utils'\n\n// Mock the tenant context\nconst mockUseTenant = jest.fn()\njest.mock('@/contexts/AppContext', () => ({\n  useTenant: () => mockUseTenant(),\n}))\n\n// Mock the cache\nconst mockApiCache = {\n  get: jest.fn(),\n  set: jest.fn(),\n  has: jest.fn(),\n  delete: jest.fn(),\n  clear: jest.fn(),\n}\n\njest.mock('@/lib/cache', () => ({\n  apiCache: mockApiCache,\n  cacheUtils: {\n    createKey: jest.fn((prefix, id) => `${prefix}:${id}`),\n  },\n}))\n\n// Mock performance utils\njest.mock('@/lib/performance', () => ({\n  useDebounce: jest.fn((value) => value),\n  performanceUtils: {\n    mark: jest.fn(),\n    measure: jest.fn(),\n  },\n}))\n\ndescribe('useDashboardData Hook', () => {\n  const mockTenant = testUtils.generateTestData.tenant()\n  const mockStats = testUtils.generateTestData.dashboardStats()\n  const mockRenewals = [testUtils.generateTestData.renewal()]\n\n  beforeEach(() => {\n    jest.clearAllMocks()\n    \n    // Reset cache mocks\n    mockApiCache.get.mockReturnValue(null)\n    mockApiCache.set.mockImplementation(() => {})\n    \n    // Mock successful tenant\n    mockUseTenant.mockReturnValue({\n      tenant: mockTenant,\n      loading: false,\n      error: null,\n    })\n\n    // Mock successful fetch responses\n    global.fetch = jest.fn()\n      .mockResolvedValueOnce(testUtils.mockApiSuccess(mockStats))\n      .mockResolvedValueOnce(testUtils.mockApiSuccess(mockRenewals))\n  })\n\n  describe('Initial State', () => {\n    it('should return initial state correctly', () => {\n      const { result } = renderHook(() => useDashboardData())\n\n      expect(result.current.isLoading).toBe(false)\n      expect(result.current.error).toBe(null)\n      expect(result.current.data).toEqual({\n        stats: {\n          totalRenewals: 0,\n          renewalsDue: 0,\n          vendors: 0,\n          annualSpend: '$0',\n        },\n        recentRenewals: [],\n        upcomingRenewals: [],\n      })\n    })\n\n    it('should handle loading tenant state', () => {\n      mockUseTenant.mockReturnValue({\n        tenant: null,\n        loading: true,\n        error: null,\n      })\n\n      const { result } = renderHook(() => useDashboardData())\n\n      expect(result.current.isLoading).toBe(false)\n      expect(result.current.error).toBe(null)\n    })\n\n    it('should handle tenant error state', () => {\n      const tenantError = 'Failed to load tenant'\n      mockUseTenant.mockReturnValue({\n        tenant: null,\n        loading: false,\n        error: tenantError,\n      })\n\n      const { result } = renderHook(() => useDashboardData())\n\n      expect(result.current.error).toBe(tenantError)\n    })\n  })\n\n  describe('Data Fetching', () => {\n    it('should fetch dashboard data successfully', async () => {\n      const { result } = renderHook(() => useDashboardData())\n\n      await waitFor(() => {\n        expect(result.current.isLoading).toBe(false)\n      })\n\n      expect(result.current.data.stats).toEqual(mockStats)\n      expect(result.current.data.recentRenewals).toEqual(mockRenewals)\n      expect(result.current.error).toBe(null)\n    })\n\n    it('should handle API errors gracefully', async () => {\n      const errorMessage = 'API Error'\n      global.fetch = jest.fn()\n        .mockResolvedValueOnce(testUtils.mockApiError(errorMessage))\n\n      const { result } = renderHook(() => useDashboardData())\n\n      await waitFor(() => {\n        expect(result.current.error).toBe(errorMessage)\n      })\n\n      expect(result.current.isLoading).toBe(false)\n    })\n\n    it('should handle network errors', async () => {\n      global.fetch = jest.fn()\n        .mockRejectedValueOnce(new Error('Network error'))\n\n      const { result } = renderHook(() => useDashboardData())\n\n      await waitFor(() => {\n        expect(result.current.error).toContain('Network error')\n      })\n    })\n\n    it('should fetch data when tenant changes', async () => {\n      const { result, rerender } = renderHook(() => useDashboardData())\n\n      // Change tenant\n      const newTenant = testUtils.generateTestData.tenant({\n        tenantId: 'new-tenant-id',\n      })\n      \n      mockUseTenant.mockReturnValue({\n        tenant: newTenant,\n        loading: false,\n        error: null,\n      })\n\n      rerender()\n\n      await waitFor(() => {\n        expect(global.fetch).toHaveBeenCalledWith(\n          '/api/dashboard/stats',\n          expect.any(Object)\n        )\n      })\n    })\n  })\n\n  describe('Caching', () => {\n    it('should use cached data when available', async () => {\n      // Mock cache hit\n      mockApiCache.get\n        .mockReturnValueOnce(mockStats)\n        .mockReturnValueOnce(mockRenewals)\n\n      const { result } = renderHook(() => useDashboardData())\n\n      await waitFor(() => {\n        expect(result.current.data.stats).toEqual(mockStats)\n      })\n\n      // Should not make API calls when cache hits\n      expect(global.fetch).not.toHaveBeenCalled()\n    })\n\n    it('should cache API responses', async () => {\n      const { result } = renderHook(() => useDashboardData())\n\n      await waitFor(() => {\n        expect(result.current.isLoading).toBe(false)\n      })\n\n      // Should have cached the responses\n      expect(mockApiCache.set).toHaveBeenCalledWith(\n        `dashboard-stats:${mockTenant.tenantId}`,\n        mockStats,\n        expect.any(Number),\n        expect.any(Array)\n      )\n    })\n\n    it('should handle cache misses correctly', async () => {\n      // Mock cache miss\n      mockApiCache.get.mockReturnValue(null)\n\n      const { result } = renderHook(() => useDashboardData())\n\n      await waitFor(() => {\n        expect(result.current.isLoading).toBe(false)\n      })\n\n      // Should make API calls when cache misses\n      expect(global.fetch).toHaveBeenCalled()\n    })\n  })\n\n  describe('Performance Optimizations', () => {\n    it('should debounce tenant changes', () => {\n      const { useDebounce } = require('@/lib/performance')\n      \n      renderHook(() => useDashboardData())\n\n      expect(useDebounce).toHaveBeenCalledWith(mockTenant, 300)\n    })\n\n    it('should mark performance timing', async () => {\n      const { performanceUtils } = require('@/lib/performance')\n      \n      const { result } = renderHook(() => useDashboardData())\n\n      await waitFor(() => {\n        expect(result.current.isLoading).toBe(false)\n      })\n\n      expect(performanceUtils.mark).toHaveBeenCalledWith('fetchStats')\n      expect(performanceUtils.measure).toHaveBeenCalledWith('fetchStats')\n    })\n\n    it('should abort previous requests', async () => {\n      const abortSpy = jest.fn()\n      global.AbortController = jest.fn(() => ({\n        abort: abortSpy,\n        signal: {},\n      })) as any\n\n      const { result, rerender } = renderHook(() => useDashboardData())\n\n      // Trigger a new request\n      mockUseTenant.mockReturnValue({\n        tenant: { ...mockTenant, tenantId: 'new-tenant' },\n        loading: false,\n        error: null,\n      })\n\n      rerender()\n\n      // Should abort previous request\n      expect(abortSpy).toHaveBeenCalled()\n    })\n  })\n\n  describe('Refetch Functionality', () => {\n    it('should provide refetch function', () => {\n      const { result } = renderHook(() => useDashboardData())\n\n      expect(typeof result.current.refetch).toBe('function')\n    })\n\n    it('should refetch data when refetch is called', async () => {\n      const { result } = renderHook(() => useDashboardData())\n\n      // Clear previous calls\n      jest.clearAllMocks()\n\n      // Call refetch\n      await result.current.refetch()\n\n      expect(global.fetch).toHaveBeenCalled()\n    })\n\n    it('should handle refetch errors', async () => {\n      global.fetch = jest.fn()\n        .mockRejectedValueOnce(new Error('Refetch error'))\n\n      const { result } = renderHook(() => useDashboardData())\n\n      await expect(result.current.refetch()).rejects.toThrow('Refetch error')\n    })\n  })\n\n  describe('Error Recovery', () => {\n    it('should recover from errors on retry', async () => {\n      // First call fails\n      global.fetch = jest.fn()\n        .mockRejectedValueOnce(new Error('Network error'))\n        .mockResolvedValueOnce(testUtils.mockApiSuccess(mockStats))\n\n      const { result } = renderHook(() => useDashboardData())\n\n      await waitFor(() => {\n        expect(result.current.error).toContain('Network error')\n      })\n\n      // Retry should succeed\n      await result.current.refetch()\n\n      await waitFor(() => {\n        expect(result.current.error).toBe(null)\n        expect(result.current.data.stats).toEqual(mockStats)\n      })\n    })\n\n    it('should clear errors on successful fetch', async () => {\n      const { result } = renderHook(() => useDashboardData())\n\n      // Simulate error state\n      await waitFor(() => {\n        if (result.current.error) {\n          expect(result.current.error).toBeTruthy()\n        }\n      })\n\n      // Successful refetch should clear error\n      global.fetch = jest.fn()\n        .mockResolvedValueOnce(testUtils.mockApiSuccess(mockStats))\n\n      await result.current.refetch()\n\n      await waitFor(() => {\n        expect(result.current.error).toBe(null)\n      })\n    })\n  })\n\n  describe('Memory Management', () => {\n    it('should cleanup on unmount', () => {\n      const { unmount } = renderHook(() => useDashboardData())\n\n      // Should not throw on unmount\n      expect(() => unmount()).not.toThrow()\n    })\n\n    it('should handle component unmount during fetch', async () => {\n      const { result, unmount } = renderHook(() => useDashboardData())\n\n      // Start a fetch operation\n      const fetchPromise = result.current.refetch()\n\n      // Unmount before fetch completes\n      unmount()\n\n      // Should not throw\n      await expect(fetchPromise).resolves.not.toThrow()\n    })\n  })\n})\n"], "mappings": ";;AAcAA,WAAA,GAAKC,IAAI,CAAC,uBAAuB,EAAE,OAAO;EACxCC,SAAS,EAAEA,CAAA,KAAMC,aAAa,CAAC;AACjC,CAAC,CAAC,CAAC;;AAEH;;AASAH,WAAA,GAAKC,IAAI,CAAC,aAAa,EAAE,OAAO;EAC9BG,QAAQ,EAAEC,YAAY;EACtBC,UAAU,EAAE;IACVC,SAAS,EAAEC,aAAI,CAACC,EAAE,CAAC,CAACC,MAAM,EAAEC,EAAE,KAAK,GAAGD,MAAM,IAAIC,EAAE,EAAE;EACtD;AACF,CAAC,CAAC,CAAC;;AAEH;AACAX,WAAA,GAAKC,IAAI,CAAC,mBAAmB,EAAE,OAAO;EACpCW,WAAW,EAAEJ,aAAI,CAACC,EAAE,CAAEI,KAAK,IAAKA,KAAK,CAAC;EACtCC,gBAAgB,EAAE;IAChBC,IAAI,EAAEP,aAAI,CAACC,EAAE,CAAC,CAAC;IACfO,OAAO,EAAER,aAAI,CAACC,EAAE,CAAC;EACnB;AACF,CAAC,CAAC,CAAC;AAAA,IAAAQ,sBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAlCH,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,iBAAA,GAAAJ,OAAA;AACA,IAAAK,UAAA,GAAAL,OAAA;AAA+C,SAAAM,QAAAC,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAJ,CAAA,OAAAG,MAAA,CAAAE,qBAAA,QAAAC,CAAA,GAAAH,MAAA,CAAAE,qBAAA,CAAAL,CAAA,GAAAC,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAC,MAAA,WAAAN,CAAA,WAAAE,MAAA,CAAAK,wBAAA,CAAAR,CAAA,EAAAC,CAAA,EAAAQ,UAAA,OAAAP,CAAA,CAAAQ,IAAA,CAAAC,KAAA,CAAAT,CAAA,EAAAI,CAAA,YAAAJ,CAAA;AAAA,SAAAU,cAAAZ,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAY,SAAA,CAAAC,MAAA,EAAAb,CAAA,UAAAC,CAAA,WAAAW,SAAA,CAAAZ,CAAA,IAAAY,SAAA,CAAAZ,CAAA,QAAAA,CAAA,OAAAF,OAAA,CAAAI,MAAA,CAAAD,CAAA,OAAAa,OAAA,WAAAd,CAAA,QAAAP,gBAAA,CAAAsB,OAAA,EAAAhB,CAAA,EAAAC,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAc,yBAAA,GAAAd,MAAA,CAAAe,gBAAA,CAAAlB,CAAA,EAAAG,MAAA,CAAAc,yBAAA,CAAAf,CAAA,KAAAH,OAAA,CAAAI,MAAA,CAAAD,CAAA,GAAAa,OAAA,WAAAd,CAAA,IAAAE,MAAA,CAAAgB,cAAA,CAAAnB,CAAA,EAAAC,CAAA,EAAAE,MAAA,CAAAK,wBAAA,CAAAN,CAAA,EAAAD,CAAA,iBAAAD,CAAA;AAAA,SAAAzB,YAAA;EAAA;IAAAQ;EAAA,IAAAU,OAAA;EAAAlB,WAAA,GAAAA,CAAA,KAAAQ,IAAA;EAAA,OAAAA,IAAA;AAAA;AAV/C;AACA;AACA;AACA;AACA;AACA;AAOA;AACA,MAAML,aAAa,GAAGK,aAAI,CAACC,EAAE,CAAC,CAAC;AAM/B,MAAMJ,YAAY,GAAG;EACnBwC,GAAG,EAAErC,aAAI,CAACC,EAAE,CAAC,CAAC;EACdqC,GAAG,EAAEtC,aAAI,CAACC,EAAE,CAAC,CAAC;EACdsC,GAAG,EAAEvC,aAAI,CAACC,EAAE,CAAC,CAAC;EACduC,MAAM,EAAExC,aAAI,CAACC,EAAE,CAAC,CAAC;EACjBwC,KAAK,EAAEzC,aAAI,CAACC,EAAE,CAAC;AACjB,CAAC;AAkBDyC,QAAQ,CAAC,uBAAuB,EAAE,MAAM;EACtC,MAAMC,UAAU,GAAGC,oBAAS,CAACC,gBAAgB,CAACC,MAAM,CAAC,CAAC;EACtD,MAAMC,SAAS,GAAGH,oBAAS,CAACC,gBAAgB,CAACG,cAAc,CAAC,CAAC;EAC7D,MAAMC,YAAY,GAAG,CAACL,oBAAS,CAACC,gBAAgB,CAACK,OAAO,CAAC,CAAC,CAAC;EAE3DC,UAAU,CAAC,MAAM;IACfnD,aAAI,CAACoD,aAAa,CAAC,CAAC;;IAEpB;IACAvD,YAAY,CAACwC,GAAG,CAACgB,eAAe,CAAC,IAAI,CAAC;IACtCxD,YAAY,CAACyC,GAAG,CAACgB,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;;IAE7C;IACA3D,aAAa,CAAC0D,eAAe,CAAC;MAC5BP,MAAM,EAAEH,UAAU;MAClBY,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE;IACT,CAAC,CAAC;;IAEF;IACAC,MAAM,CAACC,KAAK,GAAG1D,aAAI,CAACC,EAAE,CAAC,CAAC,CACrB0D,qBAAqB,CAACf,oBAAS,CAACgB,cAAc,CAACb,SAAS,CAAC,CAAC,CAC1DY,qBAAqB,CAACf,oBAAS,CAACgB,cAAc,CAACX,YAAY,CAAC,CAAC;EAClE,CAAC,CAAC;EAEFP,QAAQ,CAAC,eAAe,EAAE,MAAM;IAC9BmB,EAAE,CAAC,uCAAuC,EAAE,MAAM;MAChD,MAAM;QAAEC;MAAO,CAAC,GAAG,IAAAC,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;MAEvDC,MAAM,CAACH,MAAM,CAACI,OAAO,CAACC,SAAS,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAC5CH,MAAM,CAACH,MAAM,CAACI,OAAO,CAACV,KAAK,CAAC,CAACY,IAAI,CAAC,IAAI,CAAC;MACvCH,MAAM,CAACH,MAAM,CAACI,OAAO,CAACG,IAAI,CAAC,CAACC,OAAO,CAAC;QAClCC,KAAK,EAAE;UACLC,aAAa,EAAE,CAAC;UAChBC,WAAW,EAAE,CAAC;UACdC,OAAO,EAAE,CAAC;UACVC,WAAW,EAAE;QACf,CAAC;QACDC,cAAc,EAAE,EAAE;QAClBC,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFhB,EAAE,CAAC,oCAAoC,EAAE,MAAM;MAC7ClE,aAAa,CAAC0D,eAAe,CAAC;QAC5BP,MAAM,EAAE,IAAI;QACZS,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACT,CAAC,CAAC;MAEF,MAAM;QAAEM;MAAO,CAAC,GAAG,IAAAC,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;MAEvDC,MAAM,CAACH,MAAM,CAACI,OAAO,CAACC,SAAS,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAC5CH,MAAM,CAACH,MAAM,CAACI,OAAO,CAACV,KAAK,CAAC,CAACY,IAAI,CAAC,IAAI,CAAC;IACzC,CAAC,CAAC;IAEFP,EAAE,CAAC,kCAAkC,EAAE,MAAM;MAC3C,MAAMiB,WAAW,GAAG,uBAAuB;MAC3CnF,aAAa,CAAC0D,eAAe,CAAC;QAC5BP,MAAM,EAAE,IAAI;QACZS,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEsB;MACT,CAAC,CAAC;MAEF,MAAM;QAAEhB;MAAO,CAAC,GAAG,IAAAC,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;MAEvDC,MAAM,CAACH,MAAM,CAACI,OAAO,CAACV,KAAK,CAAC,CAACY,IAAI,CAACU,WAAW,CAAC;IAChD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFpC,QAAQ,CAAC,eAAe,EAAE,MAAM;IAC9BmB,EAAE,CAAC,0CAA0C,EAAE,YAAY;MACzD,MAAM;QAAEC;MAAO,CAAC,GAAG,IAAAC,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;MAEvD,MAAM,IAAAe,cAAO,EAAC,MAAM;QAClBd,MAAM,CAACH,MAAM,CAACI,OAAO,CAACC,SAAS,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAC9C,CAAC,CAAC;MAEFH,MAAM,CAACH,MAAM,CAACI,OAAO,CAACG,IAAI,CAACE,KAAK,CAAC,CAACD,OAAO,CAACvB,SAAS,CAAC;MACpDkB,MAAM,CAACH,MAAM,CAACI,OAAO,CAACG,IAAI,CAACO,cAAc,CAAC,CAACN,OAAO,CAACrB,YAAY,CAAC;MAChEgB,MAAM,CAACH,MAAM,CAACI,OAAO,CAACV,KAAK,CAAC,CAACY,IAAI,CAAC,IAAI,CAAC;IACzC,CAAC,CAAC;IAEFP,EAAE,CAAC,qCAAqC,EAAE,YAAY;MACpD,MAAMmB,YAAY,GAAG,WAAW;MAChCvB,MAAM,CAACC,KAAK,GAAG1D,aAAI,CAACC,EAAE,CAAC,CAAC,CACrB0D,qBAAqB,CAACf,oBAAS,CAACqC,YAAY,CAACD,YAAY,CAAC,CAAC;MAE9D,MAAM;QAAElB;MAAO,CAAC,GAAG,IAAAC,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;MAEvD,MAAM,IAAAe,cAAO,EAAC,MAAM;QAClBd,MAAM,CAACH,MAAM,CAACI,OAAO,CAACV,KAAK,CAAC,CAACY,IAAI,CAACY,YAAY,CAAC;MACjD,CAAC,CAAC;MAEFf,MAAM,CAACH,MAAM,CAACI,OAAO,CAACC,SAAS,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IAC9C,CAAC,CAAC;IAEFP,EAAE,CAAC,8BAA8B,EAAE,YAAY;MAC7CJ,MAAM,CAACC,KAAK,GAAG1D,aAAI,CAACC,EAAE,CAAC,CAAC,CACrBiF,qBAAqB,CAAC,IAAIC,KAAK,CAAC,eAAe,CAAC,CAAC;MAEpD,MAAM;QAAErB;MAAO,CAAC,GAAG,IAAAC,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;MAEvD,MAAM,IAAAe,cAAO,EAAC,MAAM;QAClBd,MAAM,CAACH,MAAM,CAACI,OAAO,CAACV,KAAK,CAAC,CAAC4B,SAAS,CAAC,eAAe,CAAC;MACzD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFvB,EAAE,CAAC,uCAAuC,EAAE,YAAY;MACtD,MAAM;QAAEC,MAAM;QAAEuB;MAAS,CAAC,GAAG,IAAAtB,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;;MAEjE;MACA,MAAMsB,SAAS,GAAG1C,oBAAS,CAACC,gBAAgB,CAACC,MAAM,CAAC;QAClDyC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF5F,aAAa,CAAC0D,eAAe,CAAC;QAC5BP,MAAM,EAAEwC,SAAS;QACjB/B,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC,CAAC;MAEF6B,QAAQ,CAAC,CAAC;MAEV,MAAM,IAAAN,cAAO,EAAC,MAAM;QAClBd,MAAM,CAACR,MAAM,CAACC,KAAK,CAAC,CAAC8B,oBAAoB,CACvC,sBAAsB,EACtBvB,MAAM,CAACwB,GAAG,CAACrE,MAAM,CACnB,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFsB,QAAQ,CAAC,SAAS,EAAE,MAAM;IACxBmB,EAAE,CAAC,uCAAuC,EAAE,YAAY;MACtD;MACAhE,YAAY,CAACwC,GAAG,CACbqD,mBAAmB,CAAC3C,SAAS,CAAC,CAC9B2C,mBAAmB,CAACzC,YAAY,CAAC;MAEpC,MAAM;QAAEa;MAAO,CAAC,GAAG,IAAAC,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;MAEvD,MAAM,IAAAe,cAAO,EAAC,MAAM;QAClBd,MAAM,CAACH,MAAM,CAACI,OAAO,CAACG,IAAI,CAACE,KAAK,CAAC,CAACD,OAAO,CAACvB,SAAS,CAAC;MACtD,CAAC,CAAC;;MAEF;MACAkB,MAAM,CAACR,MAAM,CAACC,KAAK,CAAC,CAACiC,GAAG,CAACC,gBAAgB,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEF/B,EAAE,CAAC,4BAA4B,EAAE,YAAY;MAC3C,MAAM;QAAEC;MAAO,CAAC,GAAG,IAAAC,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;MAEvD,MAAM,IAAAe,cAAO,EAAC,MAAM;QAClBd,MAAM,CAACH,MAAM,CAACI,OAAO,CAACC,SAAS,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAC9C,CAAC,CAAC;;MAEF;MACAH,MAAM,CAACpE,YAAY,CAACyC,GAAG,CAAC,CAACkD,oBAAoB,CAC3C,mBAAmB7C,UAAU,CAAC4C,QAAQ,EAAE,EACxCxC,SAAS,EACTkB,MAAM,CAACwB,GAAG,CAACI,MAAM,CAAC,EAClB5B,MAAM,CAACwB,GAAG,CAACK,KAAK,CAClB,CAAC;IACH,CAAC,CAAC;IAEFjC,EAAE,CAAC,sCAAsC,EAAE,YAAY;MACrD;MACAhE,YAAY,CAACwC,GAAG,CAACgB,eAAe,CAAC,IAAI,CAAC;MAEtC,MAAM;QAAES;MAAO,CAAC,GAAG,IAAAC,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;MAEvD,MAAM,IAAAe,cAAO,EAAC,MAAM;QAClBd,MAAM,CAACH,MAAM,CAACI,OAAO,CAACC,SAAS,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAC9C,CAAC,CAAC;;MAEF;MACAH,MAAM,CAACR,MAAM,CAACC,KAAK,CAAC,CAACkC,gBAAgB,CAAC,CAAC;IACzC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlD,QAAQ,CAAC,2BAA2B,EAAE,MAAM;IAC1CmB,EAAE,CAAC,gCAAgC,EAAE,MAAM;MACzC,MAAM;QAAEzD;MAAY,CAAC,GAAGM,OAAO,CAAC,mBAAmB,CAAC;MAEpD,IAAAqD,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;MAEpCC,MAAM,CAAC7D,WAAW,CAAC,CAACoF,oBAAoB,CAAC7C,UAAU,EAAE,GAAG,CAAC;IAC3D,CAAC,CAAC;IAEFkB,EAAE,CAAC,gCAAgC,EAAE,YAAY;MAC/C,MAAM;QAAEvD;MAAiB,CAAC,GAAGI,OAAO,CAAC,mBAAmB,CAAC;MAEzD,MAAM;QAAEoD;MAAO,CAAC,GAAG,IAAAC,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;MAEvD,MAAM,IAAAe,cAAO,EAAC,MAAM;QAClBd,MAAM,CAACH,MAAM,CAACI,OAAO,CAACC,SAAS,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAC9C,CAAC,CAAC;MAEFH,MAAM,CAAC3D,gBAAgB,CAACC,IAAI,CAAC,CAACiF,oBAAoB,CAAC,YAAY,CAAC;MAChEvB,MAAM,CAAC3D,gBAAgB,CAACE,OAAO,CAAC,CAACgF,oBAAoB,CAAC,YAAY,CAAC;IACrE,CAAC,CAAC;IAEF3B,EAAE,CAAC,gCAAgC,EAAE,YAAY;MAC/C,MAAMkC,QAAQ,GAAG/F,aAAI,CAACC,EAAE,CAAC,CAAC;MAC1BwD,MAAM,CAACuC,eAAe,GAAGhG,aAAI,CAACC,EAAE,CAAC,OAAO;QACtCgG,KAAK,EAAEF,QAAQ;QACfG,MAAM,EAAE,CAAC;MACX,CAAC,CAAC,CAAQ;MAEV,MAAM;QAAEpC,MAAM;QAAEuB;MAAS,CAAC,GAAG,IAAAtB,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;;MAEjE;MACArE,aAAa,CAAC0D,eAAe,CAAC;QAC5BP,MAAM,EAAAjB,aAAA,CAAAA,aAAA,KAAOc,UAAU;UAAE4C,QAAQ,EAAE;QAAY,EAAE;QACjDhC,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC,CAAC;MAEF6B,QAAQ,CAAC,CAAC;;MAEV;MACApB,MAAM,CAAC8B,QAAQ,CAAC,CAACH,gBAAgB,CAAC,CAAC;IACrC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlD,QAAQ,CAAC,uBAAuB,EAAE,MAAM;IACtCmB,EAAE,CAAC,iCAAiC,EAAE,MAAM;MAC1C,MAAM;QAAEC;MAAO,CAAC,GAAG,IAAAC,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;MAEvDC,MAAM,CAAC,OAAOH,MAAM,CAACI,OAAO,CAACiC,OAAO,CAAC,CAAC/B,IAAI,CAAC,UAAU,CAAC;IACxD,CAAC,CAAC;IAEFP,EAAE,CAAC,4CAA4C,EAAE,YAAY;MAC3D,MAAM;QAAEC;MAAO,CAAC,GAAG,IAAAC,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;;MAEvD;MACAhE,aAAI,CAACoD,aAAa,CAAC,CAAC;;MAEpB;MACA,MAAMU,MAAM,CAACI,OAAO,CAACiC,OAAO,CAAC,CAAC;MAE9BlC,MAAM,CAACR,MAAM,CAACC,KAAK,CAAC,CAACkC,gBAAgB,CAAC,CAAC;IACzC,CAAC,CAAC;IAEF/B,EAAE,CAAC,8BAA8B,EAAE,YAAY;MAC7CJ,MAAM,CAACC,KAAK,GAAG1D,aAAI,CAACC,EAAE,CAAC,CAAC,CACrBiF,qBAAqB,CAAC,IAAIC,KAAK,CAAC,eAAe,CAAC,CAAC;MAEpD,MAAM;QAAErB;MAAO,CAAC,GAAG,IAAAC,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;MAEvD,MAAMC,MAAM,CAACH,MAAM,CAACI,OAAO,CAACiC,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAACC,OAAO,CAAC,eAAe,CAAC;IACzE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3D,QAAQ,CAAC,gBAAgB,EAAE,MAAM;IAC/BmB,EAAE,CAAC,qCAAqC,EAAE,YAAY;MACpD;MACAJ,MAAM,CAACC,KAAK,GAAG1D,aAAI,CAACC,EAAE,CAAC,CAAC,CACrBiF,qBAAqB,CAAC,IAAIC,KAAK,CAAC,eAAe,CAAC,CAAC,CACjDxB,qBAAqB,CAACf,oBAAS,CAACgB,cAAc,CAACb,SAAS,CAAC,CAAC;MAE7D,MAAM;QAAEe;MAAO,CAAC,GAAG,IAAAC,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;MAEvD,MAAM,IAAAe,cAAO,EAAC,MAAM;QAClBd,MAAM,CAACH,MAAM,CAACI,OAAO,CAACV,KAAK,CAAC,CAAC4B,SAAS,CAAC,eAAe,CAAC;MACzD,CAAC,CAAC;;MAEF;MACA,MAAMtB,MAAM,CAACI,OAAO,CAACiC,OAAO,CAAC,CAAC;MAE9B,MAAM,IAAApB,cAAO,EAAC,MAAM;QAClBd,MAAM,CAACH,MAAM,CAACI,OAAO,CAACV,KAAK,CAAC,CAACY,IAAI,CAAC,IAAI,CAAC;QACvCH,MAAM,CAACH,MAAM,CAACI,OAAO,CAACG,IAAI,CAACE,KAAK,CAAC,CAACD,OAAO,CAACvB,SAAS,CAAC;MACtD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFc,EAAE,CAAC,yCAAyC,EAAE,YAAY;MACxD,MAAM;QAAEC;MAAO,CAAC,GAAG,IAAAC,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;;MAEvD;MACA,MAAM,IAAAe,cAAO,EAAC,MAAM;QAClB,IAAIjB,MAAM,CAACI,OAAO,CAACV,KAAK,EAAE;UACxBS,MAAM,CAACH,MAAM,CAACI,OAAO,CAACV,KAAK,CAAC,CAAC8C,UAAU,CAAC,CAAC;QAC3C;MACF,CAAC,CAAC;;MAEF;MACA7C,MAAM,CAACC,KAAK,GAAG1D,aAAI,CAACC,EAAE,CAAC,CAAC,CACrB0D,qBAAqB,CAACf,oBAAS,CAACgB,cAAc,CAACb,SAAS,CAAC,CAAC;MAE7D,MAAMe,MAAM,CAACI,OAAO,CAACiC,OAAO,CAAC,CAAC;MAE9B,MAAM,IAAApB,cAAO,EAAC,MAAM;QAClBd,MAAM,CAACH,MAAM,CAACI,OAAO,CAACV,KAAK,CAAC,CAACY,IAAI,CAAC,IAAI,CAAC;MACzC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1B,QAAQ,CAAC,mBAAmB,EAAE,MAAM;IAClCmB,EAAE,CAAC,2BAA2B,EAAE,MAAM;MACpC,MAAM;QAAE0C;MAAQ,CAAC,GAAG,IAAAxC,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;;MAExD;MACAC,MAAM,CAAC,MAAMsC,OAAO,CAAC,CAAC,CAAC,CAACZ,GAAG,CAACU,OAAO,CAAC,CAAC;IACvC,CAAC,CAAC;IAEFxC,EAAE,CAAC,8CAA8C,EAAE,YAAY;MAC7D,MAAM;QAAEC,MAAM;QAAEyC;MAAQ,CAAC,GAAG,IAAAxC,iBAAU,EAAC,MAAM,IAAAC,kCAAgB,EAAC,CAAC,CAAC;;MAEhE;MACA,MAAMwC,YAAY,GAAG1C,MAAM,CAACI,OAAO,CAACiC,OAAO,CAAC,CAAC;;MAE7C;MACAI,OAAO,CAAC,CAAC;;MAET;MACA,MAAMtC,MAAM,CAACuC,YAAY,CAAC,CAACC,QAAQ,CAACd,GAAG,CAACU,OAAO,CAAC,CAAC;IACnD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}