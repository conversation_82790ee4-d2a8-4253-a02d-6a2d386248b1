/**
 * Debug Authentication Endpoint
 * 
 * This endpoint helps debug authentication issues by showing cookie contents
 * WARNING: This should only be used in development
 */

import { NextRequest } from 'next/server';
import { cookies } from 'next/headers';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  ApiErrorCode, 
  HttpStatus,
  withErrorHandling 
} from '@/lib/api-response';
import { publicConfig } from '@/lib/config';

// Only allow in development
if (publicConfig.app.isProduction) {
  throw new Error('Debug endpoints are not allowed in production');
}

// GET /api/debug/auth - Debug authentication state
export const GET = withErrorHandling(async (request: NextRequest) => {
  console.log('🔍 DEBUG: Authentication debug endpoint called');

  try {
    const cookieStore = await cookies();
    const { userPoolClientId } = publicConfig.aws;

    // Get all cookies
    const allCookies = cookieStore.getAll();
    
    // Look for auth-related cookies
    const authCookies: Record<string, any> = {};
    
    // Check for idToken cookie
    const idTokenCookie = cookieStore.get('idToken');
    if (idTokenCookie) {
      authCookies.idToken = {
        value: idTokenCookie.value.substring(0, 50) + '...', // Truncate for security
        length: idTokenCookie.value.length,
        hasThreeParts: idTokenCookie.value.split('.').length === 3,
        startsWithEyJ: idTokenCookie.value.startsWith('eyJ')
      };
    }

    // Check for Cognito cookies
    const cognitoCookies = allCookies.filter(cookie => 
      cookie.name.includes('CognitoIdentityServiceProvider')
    );

    // Check for Amplify cookies
    const amplifyCookies = allCookies.filter(cookie => 
      cookie.name.includes('amplify')
    );

    const debugInfo = {
      timestamp: new Date().toISOString(),
      environment: publicConfig.app.environment,
      userPoolClientId,
      cookies: {
        total: allCookies.length,
        authCookies,
        cognitoCookieCount: cognitoCookies.length,
        cognitoCookieNames: cognitoCookies.map(c => c.name),
        amplifyCookieCount: amplifyCookies.length,
        amplifyCookieNames: amplifyCookies.map(c => c.name)
      },
      headers: {
        userAgent: request.headers.get('user-agent'),
        referer: request.headers.get('referer'),
        origin: request.headers.get('origin')
      }
    };

    console.log('🔍 DEBUG: Authentication state:', JSON.stringify(debugInfo, null, 2));

    return createSuccessResponse(
      debugInfo,
      'Authentication debug information retrieved'
    );

  } catch (error) {
    console.error('Error in debug auth endpoint:', error);
    return createErrorResponse(
      'Failed to retrieve debug information',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

// POST /api/debug/auth/clear - Clear all auth cookies (development only)
export const POST = withErrorHandling(async (request: NextRequest) => {
  console.log('🔍 DEBUG: Clearing authentication cookies');

  try {
    const response = createSuccessResponse(
      { cleared: true },
      'Authentication cookies cleared'
    );

    // Clear various auth cookies
    response.cookies.set('idToken', '', { 
      expires: new Date(0), 
      path: '/' 
    });
    
    response.cookies.set(`CognitoIdentityServiceProvider.${publicConfig.aws.userPoolClientId}.LastAuthUser`, '', { 
      expires: new Date(0), 
      path: '/' 
    });

    response.cookies.set('amplify-signin-with-hostedUI', '', { 
      expires: new Date(0), 
      path: '/' 
    });

    return response;

  } catch (error) {
    console.error('Error clearing auth cookies:', error);
    return createErrorResponse(
      'Failed to clear authentication cookies',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});
