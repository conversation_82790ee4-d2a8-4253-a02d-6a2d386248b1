84a3266751a226e1b8ba629477228eb7
"use strict";

// Mock jose library
_getJestObj().mock('jose', () => ({
  jwtVerify: _globals.jest.fn(),
  createRemoteJWKSet: _globals.jest.fn()
}));
var _globals = require("@jest/globals");
var _jwtValidator = require("@/lib/jwt-validator");
var _testUtils = require("../utils/test-utils");
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Authentication Library Tests
 * 
 * Tests for the authentication utilities and JWT validation
 */
describe('Authentication Library', () => {
  beforeEach(() => {
    _globals.jest.clearAllMocks();
  });
  describe('JWT Validation', () => {
    const mockJWKS = _globals.jest.fn();
    const mockJwtVerify = _globals.jest.fn();
    beforeEach(() => {
      const jose = require('jose');
      jose.createRemoteJWKSet.mockReturnValue(mockJWKS);
      jose.jwtVerify = mockJwtVerify;
    });
    it('should validate a valid JWT token', async () => {
      const mockPayload = {
        sub: 'test-user-id',
        email: '<EMAIL>',
        aud: 'test-client-id',
        iss: 'https://cognito-idp.ca-central-1.amazonaws.com/ca-central-1_test',
        exp: Math.floor(Date.now() / 1000) + 3600 // 1 hour from now
      };
      mockJwtVerify.mockResolvedValue({
        payload: mockPayload,
        protectedHeader: {
          alg: 'RS256'
        }
      });
      const result = await (0, _jwtValidator.validateJwtToken)('valid-jwt-token', 'ca-central-1_test', 'test-client-id');
      expect(result).toEqual(mockPayload);
      expect(mockJwtVerify).toHaveBeenCalledWith('valid-jwt-token', mockJWKS, expect.objectContaining({
        issuer: 'https://cognito-idp.ca-central-1.amazonaws.com/ca-central-1_test',
        audience: 'test-client-id'
      }));
    });
    it('should reject an expired JWT token', async () => {
      mockJwtVerify.mockRejectedValue(new Error('JWT expired'));
      await expect((0, _jwtValidator.validateJwtToken)('expired-jwt-token', 'ca-central-1_test', 'test-client-id')).rejects.toThrow('JWT expired');
    });
    it('should reject a JWT token with invalid audience', async () => {
      mockJwtVerify.mockRejectedValue(new Error('Invalid audience'));
      await expect((0, _jwtValidator.validateJwtToken)('invalid-audience-token', 'ca-central-1_test', 'wrong-client-id')).rejects.toThrow('Invalid audience');
    });
    it('should reject a JWT token with invalid issuer', async () => {
      mockJwtVerify.mockRejectedValue(new Error('Invalid issuer'));
      await expect((0, _jwtValidator.validateJwtToken)('invalid-issuer-token', 'wrong-pool-id', 'test-client-id')).rejects.toThrow('Invalid issuer');
    });
    it('should handle malformed JWT tokens', async () => {
      mockJwtVerify.mockRejectedValue(new Error('Malformed token'));
      await expect((0, _jwtValidator.validateJwtToken)('malformed.token', 'ca-central-1_test', 'test-client-id')).rejects.toThrow('Malformed token');
    });
    it('should handle network errors when fetching JWKS', async () => {
      mockJwtVerify.mockRejectedValue(new Error('Network error'));
      await expect((0, _jwtValidator.validateJwtToken)('valid-token', 'ca-central-1_test', 'test-client-id')).rejects.toThrow('Network error');
    });
  });
  describe('Authentication Middleware', () => {
    let mockRequest;
    let mockResponse;
    beforeEach(() => {
      mockRequest = {
        headers: {},
        cookies: {}
      };
      mockResponse = {
        status: _globals.jest.fn().mockReturnThis(),
        json: _globals.jest.fn().mockReturnThis()
      };
    });
    it('should extract token from Authorization header', () => {
      mockRequest.headers.authorization = 'Bearer valid-token';

      // Test token extraction logic
      const authHeader = mockRequest.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;
      expect(token).toBe('valid-token');
    });
    it('should extract token from cookies', () => {
      mockRequest.cookies.idToken = 'cookie-token';
      const token = mockRequest.cookies.idToken;
      expect(token).toBe('cookie-token');
    });
    it('should handle missing authentication', () => {
      // No token in headers or cookies
      const authHeader = mockRequest.headers.authorization;
      const cookieToken = mockRequest.cookies.idToken;
      expect(authHeader).toBeUndefined();
      expect(cookieToken).toBeUndefined();
    });
  });
  describe('Session Management', () => {
    it('should create a valid session object', () => {
      const mockUser = _testUtils.testUtils.generateTestData.user();
      const session = {
        user: mockUser,
        isAuthenticated: true,
        expiresAt: new Date(Date.now() + 3600000) // 1 hour from now
      };
      expect(session.user).toEqual(mockUser);
      expect(session.isAuthenticated).toBe(true);
      expect(session.expiresAt).toBeInstanceOf(Date);
    });
    it('should handle session expiration', () => {
      const expiredSession = {
        user: null,
        isAuthenticated: false,
        expiresAt: new Date(Date.now() - 3600000) // 1 hour ago
      };
      const isExpired = expiredSession.expiresAt < new Date();
      expect(isExpired).toBe(true);
      expect(expiredSession.isAuthenticated).toBe(false);
    });
  });
  describe('Error Handling', () => {
    it('should handle authentication errors gracefully', () => {
      const authError = {
        code: 'UNAUTHORIZED',
        message: 'Invalid credentials',
        status: 401
      };
      expect(authError.code).toBe('UNAUTHORIZED');
      expect(authError.status).toBe(401);
    });
    it('should handle token refresh errors', () => {
      const refreshError = {
        code: 'TOKEN_REFRESH_FAILED',
        message: 'Unable to refresh token',
        status: 401
      };
      expect(refreshError.code).toBe('TOKEN_REFRESH_FAILED');
      expect(refreshError.status).toBe(401);
    });
  });
  describe('Security Validations', () => {
    it('should validate token format', () => {
      const validTokenFormat = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/;
      expect('valid.jwt.token').toMatch(validTokenFormat);
      expect('invalid-token').not.toMatch(validTokenFormat);
      expect('').not.toMatch(validTokenFormat);
    });
    it('should validate user permissions', () => {
      const user = {
        id: 'test-user',
        roles: ['user', 'admin'],
        permissions: ['read', 'write', 'delete']
      };
      const hasPermission = permission => user.permissions.includes(permission);
      expect(hasPermission('read')).toBe(true);
      expect(hasPermission('write')).toBe(true);
      expect(hasPermission('admin')).toBe(false);
    });
    it('should validate tenant access', () => {
      const user = {
        id: 'test-user',
        tenantId: 'tenant-123'
      };
      const requestedTenantId = 'tenant-123';
      const hasAccess = user.tenantId === requestedTenantId;
      expect(hasAccess).toBe(true);
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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