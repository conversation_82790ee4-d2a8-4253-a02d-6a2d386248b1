/**
 * Admin API endpoint to create master data management tables
 * This is a one-time setup endpoint for creating the lazy synchronization pattern tables
 */

import { NextRequest } from 'next/server';
import { executeQuery } from '@/lib/database';
import { 
  createSuccessResponse, 
  createErrorR<PERSON>ponse, 
  ApiErrorCode, 
  HttpStatus,
  withErrorHandling 
} from '@/lib/api-response';

export const POST = withErrorHandling(async (request: NextRequest) => {
  try {
    console.log('Starting master data tables creation...');
    
    // Enable UUID extension
    await executeQuery('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');
    console.log('✓ UUID extension enabled');
    
    // Create enum types
    await executeQuery(`
      DO $$ BEGIN
          CREATE TYPE metadata.sync_status AS ENUM ('pending', 'matched', 'manual_review', 'rejected');
      EXCEPTION
          WHEN duplicate_object THEN null;
      END $$;
    `);
    console.log('✓ sync_status enum created');
    
    await executeQuery(`
      DO $$ BEGIN
          CREATE TYPE metadata.entity_status AS ENUM ('active', 'merged', 'deprecated');
      EXCEPTION
          WHEN duplicate_object THEN null;
      END $$;
    `);
    console.log('✓ entity_status enum created');
    
    await executeQuery(`
      DO $$ BEGIN
          CREATE TYPE metadata.product_type AS ENUM ('physical', 'digital', 'service');
      EXCEPTION
          WHEN duplicate_object THEN null;
      END $$;
    `);
    console.log('✓ product_type enum created');
    
    // Create global_vendors table
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS metadata.global_vendors (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          canonical_name VARCHAR(255) NOT NULL UNIQUE,
          legal_name VARCHAR(255),
          tax_id VARCHAR(100),
          country_code VARCHAR(2),
          domain VARCHAR(255),
          address_hash VARCHAR(64),
          phone_normalized VARCHAR(50),
          confidence_score DECIMAL(5,4) DEFAULT 0.0000 CHECK (confidence_score >= 0 AND confidence_score <= 1),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          status metadata.entity_status DEFAULT 'active',
          merged_into_id UUID
      )
    `);
    console.log('✓ global_vendors table created');
    
    // Add self-referencing foreign key for global_vendors
    try {
      await executeQuery(`
        ALTER TABLE metadata.global_vendors 
        ADD CONSTRAINT fk_global_vendors_merged_into 
        FOREIGN KEY (merged_into_id) REFERENCES metadata.global_vendors(id)
      `);
    } catch (error) {
      // Constraint might already exist
      console.log('Note: global_vendors foreign key constraint may already exist');
    }
    
    // Create global_products table
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS metadata.global_products (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          canonical_name VARCHAR(255) NOT NULL,
          product_category VARCHAR(100),
          vendor_id UUID NOT NULL,
          gtin VARCHAR(14),
          manufacturer_sku VARCHAR(100),
          product_type metadata.product_type DEFAULT 'physical',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          status metadata.entity_status DEFAULT 'active',
          merged_into_id UUID
      )
    `);
    console.log('✓ global_products table created');
    
    // Add foreign key constraints for global_products
    try {
      await executeQuery(`
        ALTER TABLE metadata.global_products 
        ADD CONSTRAINT fk_global_products_vendor 
        FOREIGN KEY (vendor_id) REFERENCES metadata.global_vendors(id)
      `);
    } catch (error) {
      console.log('Note: global_products vendor foreign key may already exist');
    }
    
    try {
      await executeQuery(`
        ALTER TABLE metadata.global_products 
        ADD CONSTRAINT fk_global_products_merged_into 
        FOREIGN KEY (merged_into_id) REFERENCES metadata.global_products(id)
      `);
    } catch (error) {
      console.log('Note: global_products merged_into foreign key may already exist');
    }
    
    // Create global_product_versions table
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS metadata.global_product_versions (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          product_id UUID NOT NULL,
          version_number VARCHAR(50) NOT NULL,
          release_date DATE,
          end_of_life_date DATE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          status metadata.entity_status DEFAULT 'active',
          merged_into_id UUID
      )
    `);
    console.log('✓ global_product_versions table created');
    
    // Add foreign key constraints for global_product_versions
    try {
      await executeQuery(`
        ALTER TABLE metadata.global_product_versions 
        ADD CONSTRAINT fk_global_product_versions_product 
        FOREIGN KEY (product_id) REFERENCES metadata.global_products(id)
      `);
    } catch (error) {
      console.log('Note: global_product_versions product foreign key may already exist');
    }
    
    try {
      await executeQuery(`
        ALTER TABLE metadata.global_product_versions 
        ADD CONSTRAINT fk_global_product_versions_merged_into 
        FOREIGN KEY (merged_into_id) REFERENCES metadata.global_product_versions(id)
      `);
    } catch (error) {
      console.log('Note: global_product_versions merged_into foreign key may already exist');
    }
    
    // Add unique constraints
    try {
      await executeQuery(`
        ALTER TABLE metadata.global_products 
        ADD CONSTRAINT global_products_vendor_name_unique UNIQUE (vendor_id, canonical_name)
      `);
    } catch (error) {
      console.log('Note: global_products unique constraint may already exist');
    }
    
    try {
      await executeQuery(`
        ALTER TABLE metadata.global_product_versions 
        ADD CONSTRAINT global_product_versions_product_version_unique UNIQUE (product_id, version_number)
      `);
    } catch (error) {
      console.log('Note: global_product_versions unique constraint may already exist');
    }
    
    // Create indexes
    await executeQuery('CREATE INDEX IF NOT EXISTS idx_global_vendors_canonical_name ON metadata.global_vendors(canonical_name)');
    await executeQuery('CREATE INDEX IF NOT EXISTS idx_global_vendors_domain ON metadata.global_vendors(domain) WHERE domain IS NOT NULL');
    await executeQuery('CREATE INDEX IF NOT EXISTS idx_global_vendors_status ON metadata.global_vendors(status)');
    
    await executeQuery('CREATE INDEX IF NOT EXISTS idx_global_products_vendor_id ON metadata.global_products(vendor_id)');
    await executeQuery('CREATE INDEX IF NOT EXISTS idx_global_products_canonical_name ON metadata.global_products(canonical_name)');
    await executeQuery('CREATE INDEX IF NOT EXISTS idx_global_products_category ON metadata.global_products(product_category)');
    
    await executeQuery('CREATE INDEX IF NOT EXISTS idx_global_product_versions_product_id ON metadata.global_product_versions(product_id)');
    await executeQuery('CREATE INDEX IF NOT EXISTS idx_global_product_versions_version ON metadata.global_product_versions(version_number)');
    console.log('✓ Global table indexes created');
    
    console.log('✅ All global master data tables created successfully!');
    
    return createSuccessResponse(
      { message: 'Master data tables created successfully' },
      'Master data management tables have been created'
    );
    
  } catch (error) {
    console.error('❌ Error creating master data tables:', error);
    return createErrorResponse(
      'Failed to create master data tables',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});
