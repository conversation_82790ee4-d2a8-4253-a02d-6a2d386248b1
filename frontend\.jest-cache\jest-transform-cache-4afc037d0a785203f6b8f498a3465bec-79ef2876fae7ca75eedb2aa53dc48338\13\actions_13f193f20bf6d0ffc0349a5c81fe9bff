d24a579160d31fd5c39434928f7dd216
'use server';

/* istanbul ignore next */
function cov_1re1rugbkv() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\actions.ts";
  var hash = "00549628aea12da53077d3d167ac8dec491253d1";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\actions.ts",
    statementMap: {
      "0": {
        start: {
          line: 9,
          column: 24
        },
        end: {
          line: 25,
          column: 1
        }
      },
      "1": {
        start: {
          line: 10,
          column: 22
        },
        end: {
          line: 10,
          column: 31
        }
      },
      "2": {
        start: {
          line: 11,
          column: 22
        },
        end: {
          line: 11,
          column: 58
        }
      },
      "3": {
        start: {
          line: 12,
          column: 20
        },
        end: {
          line: 12,
          column: 56
        }
      },
      "4": {
        start: {
          line: 14,
          column: 2
        },
        end: {
          line: 22,
          column: 3
        }
      },
      "5": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "6": {
        start: {
          line: 21,
          column: 4
        },
        end: {
          line: 21,
          column: 52
        }
      },
      "7": {
        start: {
          line: 24,
          column: 2
        },
        end: {
          line: 24,
          column: 14
        }
      },
      "8": {
        start: {
          line: 29,
          column: 16
        },
        end: {
          line: 29,
          column: 54
        }
      },
      "9": {
        start: {
          line: 32,
          column: 2
        },
        end: {
          line: 38,
          column: 5
        }
      },
      "10": {
        start: {
          line: 40,
          column: 2
        },
        end: {
          line: 40,
          column: 15
        }
      },
      "11": {
        start: {
          line: 46,
          column: 2
        },
        end: {
          line: 46,
          column: 33
        }
      },
      "12": {
        start: {
          line: 49,
          column: 18
        },
        end: {
          line: 49,
          column: 39
        }
      },
      "13": {
        start: {
          line: 50,
          column: 2
        },
        end: {
          line: 52,
          column: 3
        }
      },
      "14": {
        start: {
          line: 51,
          column: 4
        },
        end: {
          line: 51,
          column: 35
        }
      },
      "15": {
        start: {
          line: 55,
          column: 2
        },
        end: {
          line: 57,
          column: 3
        }
      },
      "16": {
        start: {
          line: 56,
          column: 4
        },
        end: {
          line: 56,
          column: 47
        }
      },
      "17": {
        start: {
          line: 60,
          column: 15
        },
        end: {
          line: 60,
          column: 43
        }
      },
      "18": {
        start: {
          line: 61,
          column: 2
        },
        end: {
          line: 63,
          column: 3
        }
      },
      "19": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 62,
          column: 35
        }
      },
      "20": {
        start: {
          line: 66,
          column: 2
        },
        end: {
          line: 72,
          column: 3
        }
      },
      "21": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 68,
          column: 28
        }
      },
      "22": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 70,
          column: 49
        }
      },
      "23": {
        start: {
          line: 71,
          column: 4
        },
        end: {
          line: 71,
          column: 47
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 9,
            column: 24
          },
          end: {
            line: 9,
            column: 25
          }
        },
        loc: {
          start: {
            line: 9,
            column: 54
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 9
      },
      "1": {
        name: "generateCSRFToken",
        decl: {
          start: {
            line: 28,
            column: 16
          },
          end: {
            line: 28,
            column: 33
          }
        },
        loc: {
          start: {
            line: 28,
            column: 36
          },
          end: {
            line: 41,
            column: 1
          }
        },
        line: 28
      },
      "2": {
        name: "updateUserProfile",
        decl: {
          start: {
            line: 44,
            column: 22
          },
          end: {
            line: 44,
            column: 39
          }
        },
        loc: {
          start: {
            line: 44,
            column: 60
          },
          end: {
            line: 73,
            column: 1
          }
        },
        line: 44
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 14,
            column: 2
          },
          end: {
            line: 22,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 14,
            column: 2
          },
          end: {
            line: 22,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 14
      },
      "1": {
        loc: {
          start: {
            line: 14,
            column: 6
          },
          end: {
            line: 14,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 14,
            column: 6
          },
          end: {
            line: 14,
            column: 18
          }
        }, {
          start: {
            line: 14,
            column: 22
          },
          end: {
            line: 14,
            column: 32
          }
        }, {
          start: {
            line: 14,
            column: 36
          },
          end: {
            line: 14,
            column: 61
          }
        }],
        line: 14
      },
      "2": {
        loc: {
          start: {
            line: 50,
            column: 2
          },
          end: {
            line: 52,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 2
          },
          end: {
            line: 52,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "3": {
        loc: {
          start: {
            line: 55,
            column: 2
          },
          end: {
            line: 57,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 55,
            column: 2
          },
          end: {
            line: 57,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 55
      },
      "4": {
        loc: {
          start: {
            line: 61,
            column: 2
          },
          end: {
            line: 63,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 61,
            column: 2
          },
          end: {
            line: 63,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 61
      },
      "5": {
        loc: {
          start: {
            line: 61,
            column: 6
          },
          end: {
            line: 61,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 61,
            column: 6
          },
          end: {
            line: 61,
            column: 11
          }
        }, {
          start: {
            line: 61,
            column: 15
          },
          end: {
            line: 61,
            column: 30
          }
        }],
        line: 61
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "00549628aea12da53077d3d167ac8dec491253d1"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1re1rugbkv = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1re1rugbkv();
import { verifySession, hasRole } from './dal';
import { cookies } from 'next/headers';

// ISSUE: CSRF token implementation could be strengthened
// RECOMMENDATION: Use double submit cookie pattern with secure, httpOnly flags
/* istanbul ignore next */
cov_1re1rugbkv().s[0]++;
const verifyCSRFToken = async formData => {
  /* istanbul ignore next */
  cov_1re1rugbkv().f[0]++;
  const cookieStore =
  /* istanbul ignore next */
  (cov_1re1rugbkv().s[1]++, cookies());
  const storedToken =
  /* istanbul ignore next */
  (cov_1re1rugbkv().s[2]++, cookieStore.get('csrf-token')?.value);
  const formToken =
  /* istanbul ignore next */
  (cov_1re1rugbkv().s[3]++, formData.get('csrf-token'));
  /* istanbul ignore next */
  cov_1re1rugbkv().s[4]++;
  if (
  /* istanbul ignore next */
  (cov_1re1rugbkv().b[1][0]++, !storedToken) ||
  /* istanbul ignore next */
  (cov_1re1rugbkv().b[1][1]++, !formToken) ||
  /* istanbul ignore next */
  (cov_1re1rugbkv().b[1][2]++, storedToken !== formToken)) {
    /* istanbul ignore next */
    cov_1re1rugbkv().b[0][0]++;
    cov_1re1rugbkv().s[5]++;
    // RECOMMENDATION: Add logging for security events
    console.error('CSRF token validation failed', {
      hasStoredToken: !!storedToken,
      hasFormToken: !!formToken
      // Don't log the actual tokens for security reasons
    });
    /* istanbul ignore next */
    cov_1re1rugbkv().s[6]++;
    throw new Error('CSRF token validation failed');
  } else
  /* istanbul ignore next */
  {
    cov_1re1rugbkv().b[0][1]++;
  }
  cov_1re1rugbkv().s[7]++;
  return true;
};

// RECOMMENDATION: Add a function to generate CSRF tokens
export function generateCSRFToken() {
  /* istanbul ignore next */
  cov_1re1rugbkv().f[1]++;
  const token =
  /* istanbul ignore next */
  (cov_1re1rugbkv().s[8]++, crypto.randomBytes(32).toString('hex'));

  // Set the cookie with proper security flags
  /* istanbul ignore next */
  cov_1re1rugbkv().s[9]++;
  cookies().set('csrf-token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    path: '/',
    maxAge: 3600 // 1 hour
  });
  /* istanbul ignore next */
  cov_1re1rugbkv().s[10]++;
  return token;
}

// Example of a secure server action
export async function updateUserProfile(formData) {
  /* istanbul ignore next */
  cov_1re1rugbkv().f[2]++;
  cov_1re1rugbkv().s[11]++;
  // 1. Verify CSRF token
  await verifyCSRFToken(formData);

  // 2. Verify user session
  const session =
  /* istanbul ignore next */
  (cov_1re1rugbkv().s[12]++, await verifySession());
  /* istanbul ignore next */
  cov_1re1rugbkv().s[13]++;
  if (!session) {
    /* istanbul ignore next */
    cov_1re1rugbkv().b[2][0]++;
    cov_1re1rugbkv().s[14]++;
    throw new Error('Unauthorized');
  } else
  /* istanbul ignore next */
  {
    cov_1re1rugbkv().b[2][1]++;
  }

  // 3. Check permissions for this action
  cov_1re1rugbkv().s[15]++;
  if (!hasRole(session, 'user')) {
    /* istanbul ignore next */
    cov_1re1rugbkv().b[3][0]++;
    cov_1re1rugbkv().s[16]++;
    throw new Error('Insufficient permissions');
  } else
  /* istanbul ignore next */
  {
    cov_1re1rugbkv().b[3][1]++;
  }

  // 4. Sanitize and validate input data
  const name =
  /* istanbul ignore next */
  (cov_1re1rugbkv().s[17]++, String(formData.get('name')));
  /* istanbul ignore next */
  cov_1re1rugbkv().s[18]++;
  if (
  /* istanbul ignore next */
  (cov_1re1rugbkv().b[5][0]++, !name) ||
  /* istanbul ignore next */
  (cov_1re1rugbkv().b[5][1]++, name.length < 2)) {
    /* istanbul ignore next */
    cov_1re1rugbkv().b[4][0]++;
    cov_1re1rugbkv().s[19]++;
    throw new Error('Invalid name');
  } else
  /* istanbul ignore next */
  {
    cov_1re1rugbkv().b[4][1]++;
  }

  // 5. Perform the action
  cov_1re1rugbkv().s[20]++;
  try {
    /* istanbul ignore next */
    cov_1re1rugbkv().s[21]++;
    // Update user profile logic here
    return {
      success: true
    };
  } catch (error) {
    /* istanbul ignore next */
    cov_1re1rugbkv().s[22]++;
    console.error('Profile update error:', error);
    /* istanbul ignore next */
    cov_1re1rugbkv().s[23]++;
    throw new Error('Failed to update profile');
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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