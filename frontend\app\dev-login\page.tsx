'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'

/**
 * Development Login Page
 * 
 * This page provides a simple way to set authentication cookies for development
 * WARNING: This should NEVER be accessible in production
 */

export default function DevLoginPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')

  useEffect(() => {
    // Prevent access in production
    if (process.env.NODE_ENV === 'production') {
      router.push('/')
      return
    }
  }, [router])

  const handleDevLogin = async () => {
    setIsLoading(true)
    setMessage('Setting development authentication...')

    try {
      // Create a simple development token (this is NOT secure and only for development)
      const devPayload = {
        sub: 'dev-user-123',
        email: '<EMAIL>',
        name: 'Development User',
        'cognito:groups': ['admin'],
        token_use: 'id',
        auth_time: Math.floor(Date.now() / 1000),
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
        aud: 'dev-client',
        iss: 'dev-issuer'
      }

      // Create a simple base64 encoded token (NOT a real JWT, just for development)
      const devToken = btoa(JSON.stringify(devPayload))
      
      // Set the cookie
      document.cookie = `idToken=${devToken}; path=/; max-age=${24 * 60 * 60}; SameSite=Lax`
      
      setMessage('Development authentication set! Redirecting to dashboard...')
      
      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push('/dashboard')
      }, 1500)

    } catch (error) {
      console.error('Error setting development auth:', error)
      setMessage('Error setting development authentication')
    } finally {
      setIsLoading(false)
    }
  }

  const handleClearAuth = () => {
    // Clear all auth cookies
    document.cookie = 'idToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
    setMessage('Authentication cleared')
  }

  const handleGoToRealLogin = () => {
    router.push('/login')
  }

  // Don't render anything in production
  if (process.env.NODE_ENV === 'production') {
    return null
  }

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      backgroundColor: '#f5f5f5',
      fontFamily: 'system-ui, sans-serif'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '2rem',
        borderRadius: '8px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        maxWidth: '400px',
        width: '100%'
      }}>
        <h1 style={{ 
          color: '#dc2626', 
          marginBottom: '1rem',
          fontSize: '1.5rem',
          fontWeight: 'bold'
        }}>
          ⚠️ Development Login
        </h1>
        
        <p style={{ 
          color: '#666', 
          marginBottom: '1.5rem',
          fontSize: '0.9rem'
        }}>
          This page is only available in development mode and provides a way to bypass 
          authentication for testing purposes.
        </p>

        {message && (
          <div style={{
            padding: '0.75rem',
            marginBottom: '1rem',
            backgroundColor: '#f0f9ff',
            border: '1px solid #0ea5e9',
            borderRadius: '4px',
            color: '#0369a1',
            fontSize: '0.9rem'
          }}>
            {message}
          </div>
        )}

        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
          <button
            onClick={handleDevLogin}
            disabled={isLoading}
            style={{
              backgroundColor: '#059669',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1rem',
              borderRadius: '4px',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              fontSize: '0.9rem',
              opacity: isLoading ? 0.6 : 1
            }}
          >
            {isLoading ? 'Setting up...' : 'Set Development Auth'}
          </button>

          <button
            onClick={handleClearAuth}
            style={{
              backgroundColor: '#dc2626',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1rem',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '0.9rem'
            }}
          >
            Clear Authentication
          </button>

          <button
            onClick={handleGoToRealLogin}
            style={{
              backgroundColor: '#6b7280',
              color: 'white',
              border: 'none',
              padding: '0.75rem 1rem',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '0.9rem'
            }}
          >
            Go to Real Login
          </button>
        </div>

        <div style={{
          marginTop: '1.5rem',
          padding: '0.75rem',
          backgroundColor: '#fef2f2',
          border: '1px solid #fecaca',
          borderRadius: '4px',
          fontSize: '0.8rem',
          color: '#991b1b'
        }}>
          <strong>Security Warning:</strong> This development login creates an insecure 
          authentication token that should never be used in production.
        </div>
      </div>
    </div>
  )
}
