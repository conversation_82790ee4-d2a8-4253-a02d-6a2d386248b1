65eb8567ee62f5eecb1ca5e351e30081
/**
 * Performance Monitor Component
 * 
 * Provides real-time performance monitoring and optimization suggestions
 * for development and debugging purposes.
 */

'use client';

/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\PerformanceMonitor.tsx";
var __jsx = React.createElement;
function cov_14bz15imf5() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\PerformanceMonitor.tsx";
  var hash = "d89d8e31f2fdc4e2870de0ba5cd2c73384a3c8dc";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\PerformanceMonitor.tsx",
    statementMap: {
      "0": {
        start: {
          line: 35,
          column: 27
        },
        end: {
          line: 244,
          column: 2
        }
      },
      "1": {
        start: {
          line: 40,
          column: 32
        },
        end: {
          line: 49,
          column: 4
        }
      },
      "2": {
        start: {
          line: 51,
          column: 36
        },
        end: {
          line: 51,
          column: 62
        }
      },
      "3": {
        start: {
          line: 52,
          column: 36
        },
        end: {
          line: 52,
          column: 53
        }
      },
      "4": {
        start: {
          line: 54,
          column: 2
        },
        end: {
          line: 94,
          column: 15
        }
      },
      "5": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 55,
          column: 24
        }
      },
      "6": {
        start: {
          line: 55,
          column: 18
        },
        end: {
          line: 55,
          column: 24
        }
      },
      "7": {
        start: {
          line: 57,
          column: 21
        },
        end: {
          line: 57,
          column: 22
        }
      },
      "8": {
        start: {
          line: 58,
          column: 26
        },
        end: {
          line: 58,
          column: 27
        }
      },
      "9": {
        start: {
          line: 59,
          column: 19
        },
        end: {
          line: 59,
          column: 36
        }
      },
      "10": {
        start: {
          line: 61,
          column: 26
        },
        end: {
          line: 88,
          column: 5
        }
      },
      "11": {
        start: {
          line: 62,
          column: 26
        },
        end: {
          line: 62,
          column: 43
        }
      },
      "12": {
        start: {
          line: 63,
          column: 25
        },
        end: {
          line: 63,
          column: 47
        }
      },
      "13": {
        start: {
          line: 65,
          column: 6
        },
        end: {
          line: 65,
          column: 18
        }
      },
      "14": {
        start: {
          line: 66,
          column: 6
        },
        end: {
          line: 66,
          column: 35
        }
      },
      "15": {
        start: {
          line: 70,
          column: 6
        },
        end: {
          line: 77,
          column: 7
        }
      },
      "16": {
        start: {
          line: 71,
          column: 23
        },
        end: {
          line: 71,
          column: 50
        }
      },
      "17": {
        start: {
          line: 72,
          column: 8
        },
        end: {
          line: 76,
          column: 9
        }
      },
      "18": {
        start: {
          line: 79,
          column: 6
        },
        end: {
          line: 85,
          column: 8
        }
      },
      "19": {
        start: {
          line: 87,
          column: 6
        },
        end: {
          line: 87,
          column: 28
        }
      },
      "20": {
        start: {
          line: 91,
          column: 21
        },
        end: {
          line: 91,
          column: 53
        }
      },
      "21": {
        start: {
          line: 93,
          column: 4
        },
        end: {
          line: 93,
          column: 40
        }
      },
      "22": {
        start: {
          line: 93,
          column: 17
        },
        end: {
          line: 93,
          column: 40
        }
      },
      "23": {
        start: {
          line: 96,
          column: 2
        },
        end: {
          line: 96,
          column: 29
        }
      },
      "24": {
        start: {
          line: 96,
          column: 18
        },
        end: {
          line: 96,
          column: 29
        }
      },
      "25": {
        start: {
          line: 98,
          column: 26
        },
        end: {
          line: 103,
          column: 3
        }
      },
      "26": {
        start: {
          line: 105,
          column: 22
        },
        end: {
          line: 111,
          column: 3
        }
      },
      "27": {
        start: {
          line: 106,
          column: 4
        },
        end: {
          line: 106,
          column: 37
        }
      },
      "28": {
        start: {
          line: 106,
          column: 21
        },
        end: {
          line: 106,
          column: 37
        }
      },
      "29": {
        start: {
          line: 107,
          column: 14
        },
        end: {
          line: 107,
          column: 18
        }
      },
      "30": {
        start: {
          line: 108,
          column: 18
        },
        end: {
          line: 108,
          column: 45
        }
      },
      "31": {
        start: {
          line: 109,
          column: 14
        },
        end: {
          line: 109,
          column: 55
        }
      },
      "32": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 110,
          column: 75
        }
      },
      "33": {
        start: {
          line: 113,
          column: 30
        },
        end: {
          line: 117,
          column: 3
        }
      },
      "34": {
        start: {
          line: 114,
          column: 4
        },
        end: {
          line: 114,
          column: 57
        }
      },
      "35": {
        start: {
          line: 114,
          column: 34
        },
        end: {
          line: 114,
          column: 57
        }
      },
      "36": {
        start: {
          line: 115,
          column: 4
        },
        end: {
          line: 115,
          column: 61
        }
      },
      "37": {
        start: {
          line: 115,
          column: 37
        },
        end: {
          line: 115,
          column: 61
        }
      },
      "38": {
        start: {
          line: 116,
          column: 4
        },
        end: {
          line: 116,
          column: 25
        }
      },
      "39": {
        start: {
          line: 119,
          column: 2
        },
        end: {
          line: 243,
          column: 3
        }
      },
      "40": {
        start: {
          line: 127,
          column: 25
        },
        end: {
          line: 127,
          column: 44
        }
      },
      "41": {
        start: {
          line: 139,
          column: 31
        },
        end: {
          line: 139,
          column: 49
        }
      },
      "42": {
        start: {
          line: 146,
          column: 31
        },
        end: {
          line: 146,
          column: 50
        }
      },
      "43": {
        start: {
          line: 206,
          column: 16
        },
        end: {
          line: 220,
          column: 22
        }
      }
    },
    fnMap: {
      "0": {
        name: "PerformanceMonitor",
        decl: {
          start: {
            line: 35,
            column: 41
          },
          end: {
            line: 35,
            column: 59
          }
        },
        loc: {
          start: {
            line: 39,
            column: 28
          },
          end: {
            line: 244,
            column: 1
          }
        },
        line: 39
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 54,
            column: 12
          },
          end: {
            line: 54,
            column: 13
          }
        },
        loc: {
          start: {
            line: 54,
            column: 18
          },
          end: {
            line: 94,
            column: 3
          }
        },
        line: 54
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 61,
            column: 26
          },
          end: {
            line: 61,
            column: 27
          }
        },
        loc: {
          start: {
            line: 61,
            column: 32
          },
          end: {
            line: 88,
            column: 5
          }
        },
        line: 61
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 93,
            column: 11
          },
          end: {
            line: 93,
            column: 12
          }
        },
        loc: {
          start: {
            line: 93,
            column: 17
          },
          end: {
            line: 93,
            column: 40
          }
        },
        line: 93
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 105,
            column: 22
          },
          end: {
            line: 105,
            column: 23
          }
        },
        loc: {
          start: {
            line: 105,
            column: 41
          },
          end: {
            line: 111,
            column: 3
          }
        },
        line: 105
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 113,
            column: 30
          },
          end: {
            line: 113,
            column: 31
          }
        },
        loc: {
          start: {
            line: 113,
            column: 96
          },
          end: {
            line: 117,
            column: 3
          }
        },
        line: 113
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 127,
            column: 19
          },
          end: {
            line: 127,
            column: 20
          }
        },
        loc: {
          start: {
            line: 127,
            column: 25
          },
          end: {
            line: 127,
            column: 44
          }
        },
        line: 127
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 139,
            column: 25
          },
          end: {
            line: 139,
            column: 26
          }
        },
        loc: {
          start: {
            line: 139,
            column: 31
          },
          end: {
            line: 139,
            column: 49
          }
        },
        line: 139
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 146,
            column: 25
          },
          end: {
            line: 146,
            column: 26
          }
        },
        loc: {
          start: {
            line: 146,
            column: 31
          },
          end: {
            line: 146,
            column: 50
          }
        },
        line: 146
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 205,
            column: 54
          },
          end: {
            line: 205,
            column: 55
          }
        },
        loc: {
          start: {
            line: 206,
            column: 16
          },
          end: {
            line: 220,
            column: 22
          }
        },
        line: 206
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 36,
            column: 2
          },
          end: {
            line: 36,
            column: 50
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 36,
            column: 12
          },
          end: {
            line: 36,
            column: 50
          }
        }],
        line: 36
      },
      "1": {
        loc: {
          start: {
            line: 37,
            column: 2
          },
          end: {
            line: 37,
            column: 27
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 37,
            column: 13
          },
          end: {
            line: 37,
            column: 27
          }
        }],
        line: 37
      },
      "2": {
        loc: {
          start: {
            line: 38,
            column: 13
          },
          end: {
            line: 38,
            column: 36
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 38,
            column: 32
          },
          end: {
            line: 38,
            column: 36
          }
        }],
        line: 38
      },
      "3": {
        loc: {
          start: {
            line: 55,
            column: 4
          },
          end: {
            line: 55,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 55,
            column: 4
          },
          end: {
            line: 55,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 55
      },
      "4": {
        loc: {
          start: {
            line: 70,
            column: 6
          },
          end: {
            line: 77,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 70,
            column: 6
          },
          end: {
            line: 77,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 70
      },
      "5": {
        loc: {
          start: {
            line: 96,
            column: 2
          },
          end: {
            line: 96,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 96,
            column: 2
          },
          end: {
            line: 96,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 96
      },
      "6": {
        loc: {
          start: {
            line: 106,
            column: 4
          },
          end: {
            line: 106,
            column: 37
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 106,
            column: 4
          },
          end: {
            line: 106,
            column: 37
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 106
      },
      "7": {
        loc: {
          start: {
            line: 114,
            column: 4
          },
          end: {
            line: 114,
            column: 57
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 114,
            column: 4
          },
          end: {
            line: 114,
            column: 57
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 114
      },
      "8": {
        loc: {
          start: {
            line: 115,
            column: 4
          },
          end: {
            line: 115,
            column: 61
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 115,
            column: 4
          },
          end: {
            line: 115,
            column: 61
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 115
      },
      "9": {
        loc: {
          start: {
            line: 122,
            column: 8
          },
          end: {
            line: 122,
            column: 65
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 122,
            column: 20
          },
          end: {
            line: 122,
            column: 31
          }
        }, {
          start: {
            line: 122,
            column: 34
          },
          end: {
            line: 122,
            column: 65
          }
        }],
        line: 122
      },
      "10": {
        loc: {
          start: {
            line: 125,
            column: 7
          },
          end: {
            line: 241,
            column: 7
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 126,
            column: 8
          },
          end: {
            line: 132,
            column: 17
          }
        }, {
          start: {
            line: 134,
            column: 8
          },
          end: {
            line: 240,
            column: 14
          }
        }],
        line: 125
      },
      "11": {
        loc: {
          start: {
            line: 179,
            column: 11
          },
          end: {
            line: 199,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 179,
            column: 11
          },
          end: {
            line: 179,
            column: 30
          }
        }, {
          start: {
            line: 180,
            column: 12
          },
          end: {
            line: 198,
            column: 18
          }
        }],
        line: 179
      },
      "12": {
        loc: {
          start: {
            line: 229,
            column: 15
          },
          end: {
            line: 231,
            column: 15
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 229,
            column: 15
          },
          end: {
            line: 229,
            column: 45
          }
        }, {
          start: {
            line: 230,
            column: 16
          },
          end: {
            line: 230,
            column: 86
          }
        }],
        line: 229
      },
      "13": {
        loc: {
          start: {
            line: 232,
            column: 15
          },
          end: {
            line: 234,
            column: 15
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 232,
            column: 15
          },
          end: {
            line: 232,
            column: 34
          }
        }, {
          start: {
            line: 232,
            column: 38
          },
          end: {
            line: 232,
            column: 73
          }
        }, {
          start: {
            line: 233,
            column: 16
          },
          end: {
            line: 233,
            column: 80
          }
        }],
        line: 232
      },
      "14": {
        loc: {
          start: {
            line: 235,
            column: 15
          },
          end: {
            line: 237,
            column: 15
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 235,
            column: 15
          },
          end: {
            line: 235,
            column: 51
          }
        }, {
          start: {
            line: 236,
            column: 16
          },
          end: {
            line: 236,
            column: 79
          }
        }],
        line: 235
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d89d8e31f2fdc4e2870de0ba5cd2c73384a3c8dc"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_14bz15imf5 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_14bz15imf5();
import React, { useState, useEffect, memo } from 'react';
import { cacheUtils } from '@/lib/cache';
const PerformanceMonitor =
/* istanbul ignore next */
(cov_14bz15imf5().s[0]++, /*#__PURE__*/memo(function PerformanceMonitor({
  enabled =
  /* istanbul ignore next */
  (cov_14bz15imf5().b[0][0]++, process.env.NODE_ENV === 'development'),
  position =
  /* istanbul ignore next */
  (cov_14bz15imf5().b[1][0]++, 'bottom-right'),
  minimized: initialMinimized =
  /* istanbul ignore next */
  (cov_14bz15imf5().b[2][0]++, true)
}) {
  /* istanbul ignore next */
  cov_14bz15imf5().f[0]++;
  const [metrics, setMetrics] =
  /* istanbul ignore next */
  (cov_14bz15imf5().s[1]++, useState({
    renderCount: 0,
    lastRenderTime: 0,
    averageRenderTime: 0,
    cacheStats: {
      api: {
        hits: 0,
        misses: 0,
        hitRate: 0
      },
      component: {
        hits: 0,
        misses: 0,
        hitRate: 0
      },
      userData: {
        hits: 0,
        misses: 0,
        hitRate: 0
      }
    }
  }));
  const [minimized, setMinimized] =
  /* istanbul ignore next */
  (cov_14bz15imf5().s[2]++, useState(initialMinimized));
  const [isVisible, setIsVisible] =
  /* istanbul ignore next */
  (cov_14bz15imf5().s[3]++, useState(enabled));
  /* istanbul ignore next */
  cov_14bz15imf5().s[4]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_14bz15imf5().f[1]++;
    cov_14bz15imf5().s[5]++;
    if (!enabled) {
      /* istanbul ignore next */
      cov_14bz15imf5().b[3][0]++;
      cov_14bz15imf5().s[6]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_14bz15imf5().b[3][1]++;
    }
    let frameCount =
    /* istanbul ignore next */
    (cov_14bz15imf5().s[7]++, 0);
    let totalRenderTime =
    /* istanbul ignore next */
    (cov_14bz15imf5().s[8]++, 0);
    let lastTime =
    /* istanbul ignore next */
    (cov_14bz15imf5().s[9]++, performance.now());
    /* istanbul ignore next */
    cov_14bz15imf5().s[10]++;
    const updateMetrics = () => {
      /* istanbul ignore next */
      cov_14bz15imf5().f[2]++;
      const currentTime =
      /* istanbul ignore next */
      (cov_14bz15imf5().s[11]++, performance.now());
      const renderTime =
      /* istanbul ignore next */
      (cov_14bz15imf5().s[12]++, currentTime - lastTime);
      /* istanbul ignore next */
      cov_14bz15imf5().s[13]++;
      frameCount++;
      /* istanbul ignore next */
      cov_14bz15imf5().s[14]++;
      totalRenderTime += renderTime;

      // Get memory usage if available
      let memoryUsage;
      /* istanbul ignore next */
      cov_14bz15imf5().s[15]++;
      if ('memory' in performance) {
        /* istanbul ignore next */
        cov_14bz15imf5().b[4][0]++;
        const memory =
        /* istanbul ignore next */
        (cov_14bz15imf5().s[16]++, performance.memory);
        /* istanbul ignore next */
        cov_14bz15imf5().s[17]++;
        memoryUsage = {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          percentage: memory.usedJSHeapSize / memory.totalJSHeapSize * 100
        };
      } else
      /* istanbul ignore next */
      {
        cov_14bz15imf5().b[4][1]++;
      }
      cov_14bz15imf5().s[18]++;
      setMetrics({
        renderCount: frameCount,
        lastRenderTime: renderTime,
        averageRenderTime: totalRenderTime / frameCount,
        memoryUsage,
        cacheStats: cacheUtils.getGlobalStats()
      });
      /* istanbul ignore next */
      cov_14bz15imf5().s[19]++;
      lastTime = currentTime;
    };

    // Update metrics every second
    const interval =
    /* istanbul ignore next */
    (cov_14bz15imf5().s[20]++, setInterval(updateMetrics, 1000));
    /* istanbul ignore next */
    cov_14bz15imf5().s[21]++;
    return () => {
      /* istanbul ignore next */
      cov_14bz15imf5().f[3]++;
      cov_14bz15imf5().s[22]++;
      return clearInterval(interval);
    };
  }, [enabled]);
  /* istanbul ignore next */
  cov_14bz15imf5().s[23]++;
  if (!isVisible) {
    /* istanbul ignore next */
    cov_14bz15imf5().b[5][0]++;
    cov_14bz15imf5().s[24]++;
    return null;
  } else
  /* istanbul ignore next */
  {
    cov_14bz15imf5().b[5][1]++;
  }
  const positionClasses =
  /* istanbul ignore next */
  (cov_14bz15imf5().s[25]++, {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  });
  /* istanbul ignore next */
  cov_14bz15imf5().s[26]++;
  const formatBytes = bytes => {
    /* istanbul ignore next */
    cov_14bz15imf5().f[4]++;
    cov_14bz15imf5().s[27]++;
    if (bytes === 0) {
      /* istanbul ignore next */
      cov_14bz15imf5().b[6][0]++;
      cov_14bz15imf5().s[28]++;
      return '0 Bytes';
    } else
    /* istanbul ignore next */
    {
      cov_14bz15imf5().b[6][1]++;
    }
    const k =
    /* istanbul ignore next */
    (cov_14bz15imf5().s[29]++, 1024);
    const sizes =
    /* istanbul ignore next */
    (cov_14bz15imf5().s[30]++, ['Bytes', 'KB', 'MB', 'GB']);
    const i =
    /* istanbul ignore next */
    (cov_14bz15imf5().s[31]++, Math.floor(Math.log(bytes) / Math.log(k)));
    /* istanbul ignore next */
    cov_14bz15imf5().s[32]++;
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  /* istanbul ignore next */
  cov_14bz15imf5().s[33]++;
  const getPerformanceColor = (value, thresholds) => {
    /* istanbul ignore next */
    cov_14bz15imf5().f[5]++;
    cov_14bz15imf5().s[34]++;
    if (value <= thresholds.good) {
      /* istanbul ignore next */
      cov_14bz15imf5().b[7][0]++;
      cov_14bz15imf5().s[35]++;
      return 'text-green-600';
    } else
    /* istanbul ignore next */
    {
      cov_14bz15imf5().b[7][1]++;
    }
    cov_14bz15imf5().s[36]++;
    if (value <= thresholds.warning) {
      /* istanbul ignore next */
      cov_14bz15imf5().b[8][0]++;
      cov_14bz15imf5().s[37]++;
      return 'text-yellow-600';
    } else
    /* istanbul ignore next */
    {
      cov_14bz15imf5().b[8][1]++;
    }
    cov_14bz15imf5().s[38]++;
    return 'text-red-600';
  };
  /* istanbul ignore next */
  cov_14bz15imf5().s[39]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: `fixed ${positionClasses[position]} z-50 bg-white border border-gray-300 rounded-lg shadow-lg transition-all duration-200 ${minimized ?
    /* istanbul ignore next */
    (cov_14bz15imf5().b[9][0]++, 'w-12 h-12') :
    /* istanbul ignore next */
    (cov_14bz15imf5().b[9][1]++, 'w-80 max-h-96 overflow-y-auto')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 120,
      columnNumber: 5
    }
  }, minimized ?
  /* istanbul ignore next */
  (cov_14bz15imf5().b[10][0]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  {
    onClick: () => {
      /* istanbul ignore next */
      cov_14bz15imf5().f[6]++;
      cov_14bz15imf5().s[40]++;
      return setMinimized(false);
    },
    className: "w-full h-full flex items-center justify-center text-lg hover:bg-gray-50 rounded-lg",
    title: "Performance Monitor",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 126,
      columnNumber: 9
    }
  }, "\uD83D\uDCCA")) :
  /* istanbul ignore next */
  (cov_14bz15imf5().b[10][1]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "p-4",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 134,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex items-center justify-between mb-3",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 135,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h3",
  /* istanbul ignore next */
  {
    className: "text-sm font-semibold text-gray-800",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 136,
      columnNumber: 13
    }
  }, "Performance Monitor"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex space-x-1",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 137,
      columnNumber: 13
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  {
    onClick: () => {
      /* istanbul ignore next */
      cov_14bz15imf5().f[7]++;
      cov_14bz15imf5().s[41]++;
      return setMinimized(true);
    },
    className: "text-gray-500 hover:text-gray-700 text-xs",
    title: "Minimize",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 138,
      columnNumber: 15
    }
  }, "\u2796"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  {
    onClick: () => {
      /* istanbul ignore next */
      cov_14bz15imf5().f[8]++;
      cov_14bz15imf5().s[42]++;
      return setIsVisible(false);
    },
    className: "text-gray-500 hover:text-gray-700 text-xs",
    title: "Close",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 145,
      columnNumber: 15
    }
  }, "\u2715"))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "mb-3",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 156,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h4",
  /* istanbul ignore next */
  {
    className: "text-xs font-medium text-gray-600 mb-1",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 157,
      columnNumber: 13
    }
  }, "Render Performance"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-xs space-y-1",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 158,
      columnNumber: 13
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex justify-between",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 159,
      columnNumber: 15
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 160,
      columnNumber: 17
    }
  }, "Renders:"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 161,
      columnNumber: 17
    }
  }, metrics.renderCount)),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex justify-between",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 163,
      columnNumber: 15
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 164,
      columnNumber: 17
    }
  }, "Last Render:"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    className: getPerformanceColor(metrics.lastRenderTime, {
      good: 16,
      warning: 33
    }),
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 165,
      columnNumber: 17
    }
  }, metrics.lastRenderTime.toFixed(1), "ms")),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex justify-between",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 169,
      columnNumber: 15
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 170,
      columnNumber: 17
    }
  }, "Avg Render:"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    className: getPerformanceColor(metrics.averageRenderTime, {
      good: 16,
      warning: 33
    }),
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 171,
      columnNumber: 17
    }
  }, metrics.averageRenderTime.toFixed(1), "ms")))),
  /* istanbul ignore next */
  (cov_14bz15imf5().b[11][0]++, metrics.memoryUsage) &&
  /* istanbul ignore next */
  (cov_14bz15imf5().b[11][1]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "mb-3",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 180,
      columnNumber: 13
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h4",
  /* istanbul ignore next */
  {
    className: "text-xs font-medium text-gray-600 mb-1",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 181,
      columnNumber: 15
    }
  }, "Memory Usage"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-xs space-y-1",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 182,
      columnNumber: 15
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex justify-between",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 183,
      columnNumber: 17
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 184,
      columnNumber: 19
    }
  }, "Used:"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 185,
      columnNumber: 19
    }
  }, formatBytes(metrics.memoryUsage.used))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex justify-between",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 187,
      columnNumber: 17
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 188,
      columnNumber: 19
    }
  }, "Total:"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 189,
      columnNumber: 19
    }
  }, formatBytes(metrics.memoryUsage.total))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex justify-between",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 191,
      columnNumber: 17
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 192,
      columnNumber: 19
    }
  }, "Usage:"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    className: getPerformanceColor(metrics.memoryUsage.percentage, {
      good: 70,
      warning: 85
    }),
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 193,
      columnNumber: 19
    }
  }, metrics.memoryUsage.percentage.toFixed(1), "%"))))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "mb-3",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 202,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h4",
  /* istanbul ignore next */
  {
    className: "text-xs font-medium text-gray-600 mb-1",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 203,
      columnNumber: 13
    }
  }, "Cache Performance"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-xs space-y-2",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 204,
      columnNumber: 13
    }
  }, Object.entries(metrics.cacheStats).map(([name, stats]) => {
    /* istanbul ignore next */
    cov_14bz15imf5().f[9]++;
    cov_14bz15imf5().s[43]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      key: name,
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 206,
        columnNumber: 17
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "font-medium capitalize",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 207,
        columnNumber: 19
      }
    }, name, ":"),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "ml-2 space-y-1",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 208,
        columnNumber: 19
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "flex justify-between",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 209,
        columnNumber: 21
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "span",
    /* istanbul ignore next */
    {
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 210,
        columnNumber: 23
      }
    }, "Hit Rate:"),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "span",
    /* istanbul ignore next */
    {
      className: getPerformanceColor(100 - stats.hitRate * 100, {
        good: 20,
        warning: 50
      }),
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 211,
        columnNumber: 23
      }
    }, (stats.hitRate * 100).toFixed(1), "%")),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "flex justify-between",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 215,
        columnNumber: 21
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "span",
    /* istanbul ignore next */
    {
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 216,
        columnNumber: 23
      }
    }, "Hits/Misses:"),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "span",
    /* istanbul ignore next */
    {
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 217,
        columnNumber: 23
      }
    }, stats.hits, "/", stats.misses))));
  }))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "border-t pt-2",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 226,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h4",
  /* istanbul ignore next */
  {
    className: "text-xs font-medium text-gray-600 mb-1",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 227,
      columnNumber: 13
    }
  }, "Tips"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-xs text-gray-500 space-y-1",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 228,
      columnNumber: 13
    }
  },
  /* istanbul ignore next */
  (cov_14bz15imf5().b[12][0]++, metrics.averageRenderTime > 16) &&
  /* istanbul ignore next */
  (cov_14bz15imf5().b[12][1]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-yellow-600",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 230,
      columnNumber: 17
    }
  }, "\u2022 Consider memoizing components")),
  /* istanbul ignore next */
  (cov_14bz15imf5().b[13][0]++, metrics.memoryUsage) &&
  /* istanbul ignore next */
  (cov_14bz15imf5().b[13][1]++, metrics.memoryUsage.percentage > 85) &&
  /* istanbul ignore next */
  (cov_14bz15imf5().b[13][2]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-red-600",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 233,
      columnNumber: 17
    }
  }, "\u2022 High memory usage detected")),
  /* istanbul ignore next */
  (cov_14bz15imf5().b[14][0]++, metrics.cacheStats.api.hitRate < 0.7) &&
  /* istanbul ignore next */
  (cov_14bz15imf5().b[14][1]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-yellow-600",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 236,
      columnNumber: 17
    }
  }, "\u2022 Low API cache hit rate")))))));
}));
export default PerformanceMonitor;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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