{"version": 3, "names": ["require", "process", "env", "NODE_ENV", "NEXT_PUBLIC_AWS_REGION", "NEXT_PUBLIC_USER_POOL_ID", "NEXT_PUBLIC_USER_POOL_CLIENT_ID", "NEXT_PUBLIC_COGNITO_DOMAIN", "DATABASE_URL", "originalError", "console", "error", "originalWarn", "warn", "beforeAll", "args", "includes", "call", "afterAll", "global", "IntersectionObserver", "constructor", "disconnect", "observe", "unobserve", "ResizeObserver", "Object", "defineProperty", "window", "writable", "value", "jest", "fn", "mockImplementation", "query", "matches", "media", "onchange", "addListener", "removeListener", "addEventListener", "removeEventListener", "dispatchEvent", "localStorageMock", "getItem", "setItem", "removeItem", "clear", "sessionStorageMock", "fetch", "now", "Date", "mark", "measure", "getEntriesByName", "clearMarks", "clearMeasures", "memory", "usedJSHeapSize", "totalJSHeapSize", "jsHeapSizeLimit", "randomUUID", "getRandomValues", "arr", "i", "length", "Math", "floor", "random", "URL", "createObjectURL", "revokeObjectURL", "expect", "extend", "toBeInTheDocument", "received", "pass", "undefined", "message", "testUtils", "mockUser", "id", "email", "name", "tenantId", "mockTenant", "clientId", "clientName", "domain", "mockDashboardData", "stats", "totalRenewals", "renewalsDue", "vendors", "annualSpend", "recentRenewals", "vendor", "renewalDate", "cost", "status", "upcoming<PERSON><PERSON><PERSON><PERSON>", "createMockApiResponse", "data", "success", "ok", "json", "mockResolvedValue", "waitFor", "callback", "timeout", "Promise", "resolve", "reject", "startTime", "check", "result", "Error", "setTimeout", "beforeEach", "clearAllMocks", "mockClear", "on", "reason", "promise"], "sources": ["jest.setup.js"], "sourcesContent": ["/**\n * Jest Setup File\n * \n * Global test configuration and setup for all test files.\n * This file is executed before each test file.\n */\n\nimport '@testing-library/jest-dom'\n\n// Mock environment variables\nprocess.env.NODE_ENV = 'test'\nprocess.env.NEXT_PUBLIC_AWS_REGION = 'ca-central-1'\nprocess.env.NEXT_PUBLIC_USER_POOL_ID = 'ca-central-1_test'\nprocess.env.NEXT_PUBLIC_USER_POOL_CLIENT_ID = 'test-client-id'\nprocess.env.NEXT_PUBLIC_COGNITO_DOMAIN = 'test.auth.renewtrack.com'\nprocess.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_renewtrack'\n\n// Mock console methods to reduce noise in tests\nconst originalError = console.error\nconst originalWarn = console.warn\n\nbeforeAll(() => {\n  console.error = (...args) => {\n    if (\n      typeof args[0] === 'string' &&\n      (args[0].includes('Warning: ReactDOM.render is deprecated') ||\n       args[0].includes('Warning: componentWillReceiveProps') ||\n       args[0].includes('Warning: componentWillMount'))\n    ) {\n      return\n    }\n    originalError.call(console, ...args)\n  }\n\n  console.warn = (...args) => {\n    if (\n      typeof args[0] === 'string' &&\n      (args[0].includes('Warning: React.createFactory') ||\n       args[0].includes('Warning: componentWillReceiveProps'))\n    ) {\n      return\n    }\n    originalWarn.call(console, ...args)\n  }\n})\n\nafterAll(() => {\n  console.error = originalError\n  console.warn = originalWarn\n})\n\n// Mock IntersectionObserver\nglobal.IntersectionObserver = class IntersectionObserver {\n  constructor() {}\n  disconnect() {}\n  observe() {}\n  unobserve() {}\n}\n\n// Mock ResizeObserver\nglobal.ResizeObserver = class ResizeObserver {\n  constructor() {}\n  disconnect() {}\n  observe() {}\n  unobserve() {}\n}\n\n// Mock matchMedia\nObject.defineProperty(window, 'matchMedia', {\n  writable: true,\n  value: jest.fn().mockImplementation(query => ({\n    matches: false,\n    media: query,\n    onchange: null,\n    addListener: jest.fn(), // deprecated\n    removeListener: jest.fn(), // deprecated\n    addEventListener: jest.fn(),\n    removeEventListener: jest.fn(),\n    dispatchEvent: jest.fn(),\n  })),\n})\n\n// Mock scrollTo\nObject.defineProperty(window, 'scrollTo', {\n  writable: true,\n  value: jest.fn(),\n})\n\n// Mock localStorage\nconst localStorageMock = {\n  getItem: jest.fn(),\n  setItem: jest.fn(),\n  removeItem: jest.fn(),\n  clear: jest.fn(),\n}\nObject.defineProperty(window, 'localStorage', {\n  value: localStorageMock,\n})\n\n// Mock sessionStorage\nconst sessionStorageMock = {\n  getItem: jest.fn(),\n  setItem: jest.fn(),\n  removeItem: jest.fn(),\n  clear: jest.fn(),\n}\nObject.defineProperty(window, 'sessionStorage', {\n  value: sessionStorageMock,\n})\n\n// Mock fetch\nglobal.fetch = jest.fn()\n\n// Mock performance API\nObject.defineProperty(window, 'performance', {\n  writable: true,\n  value: {\n    now: jest.fn(() => Date.now()),\n    mark: jest.fn(),\n    measure: jest.fn(),\n    getEntriesByName: jest.fn(() => []),\n    clearMarks: jest.fn(),\n    clearMeasures: jest.fn(),\n    memory: {\n      usedJSHeapSize: 1000000,\n      totalJSHeapSize: 2000000,\n      jsHeapSizeLimit: 4000000,\n    },\n  },\n})\n\n// Mock crypto API\nObject.defineProperty(global, 'crypto', {\n  value: {\n    randomUUID: jest.fn(() => 'test-uuid-1234'),\n    getRandomValues: jest.fn((arr) => {\n      for (let i = 0; i < arr.length; i++) {\n        arr[i] = Math.floor(Math.random() * 256)\n      }\n      return arr\n    }),\n  },\n})\n\n// Mock URL constructor\nglobal.URL.createObjectURL = jest.fn(() => 'mocked-url')\nglobal.URL.revokeObjectURL = jest.fn()\n\n// Custom matchers\nexpect.extend({\n  toBeInTheDocument(received) {\n    const pass = received !== null && received !== undefined\n    return {\n      message: () => `expected element ${pass ? 'not ' : ''}to be in the document`,\n      pass,\n    }\n  },\n})\n\n// Global test utilities\nglobal.testUtils = {\n  // Mock user for authentication tests\n  mockUser: {\n    id: 'test-user-id',\n    email: '<EMAIL>',\n    name: 'Test User',\n    tenantId: 'test-tenant-id',\n  },\n  \n  // Mock tenant data\n  mockTenant: {\n    tenantId: 'test-tenant-id',\n    clientId: 'test-client-id',\n    clientName: 'Test Client',\n    domain: 'test.example.com',\n  },\n  \n  // Mock dashboard data\n  mockDashboardData: {\n    stats: {\n      totalRenewals: 25,\n      renewalsDue: 5,\n      vendors: 12,\n      annualSpend: '$125,000',\n    },\n    recentRenewals: [\n      {\n        id: '1',\n        name: 'Microsoft Office 365',\n        vendor: 'Microsoft',\n        renewalDate: new Date('2025-02-15'),\n        cost: 1200,\n        status: 'active',\n      },\n    ],\n    upcomingRenewals: [\n      {\n        id: '2',\n        name: 'Adobe Creative Suite',\n        vendor: 'Adobe',\n        renewalDate: new Date('2025-03-01'),\n        cost: 2400,\n        status: 'pending',\n      },\n    ],\n  },\n  \n  // Helper to create mock API responses\n  createMockApiResponse: (data, success = true) => ({\n    ok: success,\n    status: success ? 200 : 400,\n    json: jest.fn().mockResolvedValue(\n      success ? { success: true, data } : { success: false, error: 'Test error' }\n    ),\n  }),\n  \n  // Helper to wait for async operations\n  waitFor: (callback, timeout = 1000) => {\n    return new Promise((resolve, reject) => {\n      const startTime = Date.now()\n      const check = () => {\n        try {\n          const result = callback()\n          if (result) {\n            resolve(result)\n          } else if (Date.now() - startTime > timeout) {\n            reject(new Error('Timeout waiting for condition'))\n          } else {\n            setTimeout(check, 10)\n          }\n        } catch (error) {\n          if (Date.now() - startTime > timeout) {\n            reject(error)\n          } else {\n            setTimeout(check, 10)\n          }\n        }\n      }\n      check()\n    })\n  },\n}\n\n// Reset all mocks before each test\nbeforeEach(() => {\n  jest.clearAllMocks()\n  \n  // Reset localStorage\n  localStorageMock.getItem.mockClear()\n  localStorageMock.setItem.mockClear()\n  localStorageMock.removeItem.mockClear()\n  localStorageMock.clear.mockClear()\n  \n  // Reset sessionStorage\n  sessionStorageMock.getItem.mockClear()\n  sessionStorageMock.setItem.mockClear()\n  sessionStorageMock.removeItem.mockClear()\n  sessionStorageMock.clear.mockClear()\n  \n  // Reset fetch\n  fetch.mockClear()\n})\n\n// Global error handler for unhandled promise rejections\nprocess.on('unhandledRejection', (reason, promise) => {\n  console.error('Unhandled Rejection at:', promise, 'reason:', reason)\n})\n"], "mappings": ";;AAOAA,OAAA;AAPA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACAC,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAG,MAAM;AAC7BF,OAAO,CAACC,GAAG,CAACE,sBAAsB,GAAG,cAAc;AACnDH,OAAO,CAACC,GAAG,CAACG,wBAAwB,GAAG,mBAAmB;AAC1DJ,OAAO,CAACC,GAAG,CAACI,+BAA+B,GAAG,gBAAgB;AAC9DL,OAAO,CAACC,GAAG,CAACK,0BAA0B,GAAG,0BAA0B;AACnEN,OAAO,CAACC,GAAG,CAACM,YAAY,GAAG,uDAAuD;;AAElF;AACA,MAAMC,aAAa,GAAGC,OAAO,CAACC,KAAK;AACnC,MAAMC,YAAY,GAAGF,OAAO,CAACG,IAAI;AAEjCC,SAAS,CAAC,MAAM;EACdJ,OAAO,CAACC,KAAK,GAAG,CAAC,GAAGI,IAAI,KAAK;IAC3B,IACE,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,KAC1BA,IAAI,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,wCAAwC,CAAC,IAC1DD,IAAI,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,oCAAoC,CAAC,IACtDD,IAAI,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,6BAA6B,CAAC,CAAC,EACjD;MACA;IACF;IACAP,aAAa,CAACQ,IAAI,CAACP,OAAO,EAAE,GAAGK,IAAI,CAAC;EACtC,CAAC;EAEDL,OAAO,CAACG,IAAI,GAAG,CAAC,GAAGE,IAAI,KAAK;IAC1B,IACE,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,KAC1BA,IAAI,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,8BAA8B,CAAC,IAChDD,IAAI,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,oCAAoC,CAAC,CAAC,EACxD;MACA;IACF;IACAJ,YAAY,CAACK,IAAI,CAACP,OAAO,EAAE,GAAGK,IAAI,CAAC;EACrC,CAAC;AACH,CAAC,CAAC;AAEFG,QAAQ,CAAC,MAAM;EACbR,OAAO,CAACC,KAAK,GAAGF,aAAa;EAC7BC,OAAO,CAACG,IAAI,GAAGD,YAAY;AAC7B,CAAC,CAAC;;AAEF;AACAO,MAAM,CAACC,oBAAoB,GAAG,MAAMA,oBAAoB,CAAC;EACvDC,WAAWA,CAAA,EAAG,CAAC;EACfC,UAAUA,CAAA,EAAG,CAAC;EACdC,OAAOA,CAAA,EAAG,CAAC;EACXC,SAASA,CAAA,EAAG,CAAC;AACf,CAAC;;AAED;AACAL,MAAM,CAACM,cAAc,GAAG,MAAMA,cAAc,CAAC;EAC3CJ,WAAWA,CAAA,EAAG,CAAC;EACfC,UAAUA,CAAA,EAAG,CAAC;EACdC,OAAOA,CAAA,EAAG,CAAC;EACXC,SAASA,CAAA,EAAG,CAAC;AACf,CAAC;;AAED;AACAE,MAAM,CAACC,cAAc,CAACC,MAAM,EAAE,YAAY,EAAE;EAC1CC,QAAQ,EAAE,IAAI;EACdC,KAAK,EAAEC,IAAI,CAACC,EAAE,CAAC,CAAC,CAACC,kBAAkB,CAACC,KAAK,KAAK;IAC5CC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAEF,KAAK;IACZG,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAEP,IAAI,CAACC,EAAE,CAAC,CAAC;IAAE;IACxBO,cAAc,EAAER,IAAI,CAACC,EAAE,CAAC,CAAC;IAAE;IAC3BQ,gBAAgB,EAAET,IAAI,CAACC,EAAE,CAAC,CAAC;IAC3BS,mBAAmB,EAAEV,IAAI,CAACC,EAAE,CAAC,CAAC;IAC9BU,aAAa,EAAEX,IAAI,CAACC,EAAE,CAAC;EACzB,CAAC,CAAC;AACJ,CAAC,CAAC;;AAEF;AACAN,MAAM,CAACC,cAAc,CAACC,MAAM,EAAE,UAAU,EAAE;EACxCC,QAAQ,EAAE,IAAI;EACdC,KAAK,EAAEC,IAAI,CAACC,EAAE,CAAC;AACjB,CAAC,CAAC;;AAEF;AACA,MAAMW,gBAAgB,GAAG;EACvBC,OAAO,EAAEb,IAAI,CAACC,EAAE,CAAC,CAAC;EAClBa,OAAO,EAAEd,IAAI,CAACC,EAAE,CAAC,CAAC;EAClBc,UAAU,EAAEf,IAAI,CAACC,EAAE,CAAC,CAAC;EACrBe,KAAK,EAAEhB,IAAI,CAACC,EAAE,CAAC;AACjB,CAAC;AACDN,MAAM,CAACC,cAAc,CAACC,MAAM,EAAE,cAAc,EAAE;EAC5CE,KAAK,EAAEa;AACT,CAAC,CAAC;;AAEF;AACA,MAAMK,kBAAkB,GAAG;EACzBJ,OAAO,EAAEb,IAAI,CAACC,EAAE,CAAC,CAAC;EAClBa,OAAO,EAAEd,IAAI,CAACC,EAAE,CAAC,CAAC;EAClBc,UAAU,EAAEf,IAAI,CAACC,EAAE,CAAC,CAAC;EACrBe,KAAK,EAAEhB,IAAI,CAACC,EAAE,CAAC;AACjB,CAAC;AACDN,MAAM,CAACC,cAAc,CAACC,MAAM,EAAE,gBAAgB,EAAE;EAC9CE,KAAK,EAAEkB;AACT,CAAC,CAAC;;AAEF;AACA7B,MAAM,CAAC8B,KAAK,GAAGlB,IAAI,CAACC,EAAE,CAAC,CAAC;;AAExB;AACAN,MAAM,CAACC,cAAc,CAACC,MAAM,EAAE,aAAa,EAAE;EAC3CC,QAAQ,EAAE,IAAI;EACdC,KAAK,EAAE;IACLoB,GAAG,EAAEnB,IAAI,CAACC,EAAE,CAAC,MAAMmB,IAAI,CAACD,GAAG,CAAC,CAAC,CAAC;IAC9BE,IAAI,EAAErB,IAAI,CAACC,EAAE,CAAC,CAAC;IACfqB,OAAO,EAAEtB,IAAI,CAACC,EAAE,CAAC,CAAC;IAClBsB,gBAAgB,EAAEvB,IAAI,CAACC,EAAE,CAAC,MAAM,EAAE,CAAC;IACnCuB,UAAU,EAAExB,IAAI,CAACC,EAAE,CAAC,CAAC;IACrBwB,aAAa,EAAEzB,IAAI,CAACC,EAAE,CAAC,CAAC;IACxByB,MAAM,EAAE;MACNC,cAAc,EAAE,OAAO;MACvBC,eAAe,EAAE,OAAO;MACxBC,eAAe,EAAE;IACnB;EACF;AACF,CAAC,CAAC;;AAEF;AACAlC,MAAM,CAACC,cAAc,CAACR,MAAM,EAAE,QAAQ,EAAE;EACtCW,KAAK,EAAE;IACL+B,UAAU,EAAE9B,IAAI,CAACC,EAAE,CAAC,MAAM,gBAAgB,CAAC;IAC3C8B,eAAe,EAAE/B,IAAI,CAACC,EAAE,CAAE+B,GAAG,IAAK;MAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;QACnCD,GAAG,CAACC,CAAC,CAAC,GAAGE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;MAC1C;MACA,OAAOL,GAAG;IACZ,CAAC;EACH;AACF,CAAC,CAAC;;AAEF;AACA5C,MAAM,CAACkD,GAAG,CAACC,eAAe,GAAGvC,IAAI,CAACC,EAAE,CAAC,MAAM,YAAY,CAAC;AACxDb,MAAM,CAACkD,GAAG,CAACE,eAAe,GAAGxC,IAAI,CAACC,EAAE,CAAC,CAAC;;AAEtC;AACAwC,MAAM,CAACC,MAAM,CAAC;EACZC,iBAAiBA,CAACC,QAAQ,EAAE;IAC1B,MAAMC,IAAI,GAAGD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAKE,SAAS;IACxD,OAAO;MACLC,OAAO,EAAEA,CAAA,KAAM,oBAAoBF,IAAI,GAAG,MAAM,GAAG,EAAE,uBAAuB;MAC5EA;IACF,CAAC;EACH;AACF,CAAC,CAAC;;AAEF;AACAzD,MAAM,CAAC4D,SAAS,GAAG;EACjB;EACAC,QAAQ,EAAE;IACRC,EAAE,EAAE,cAAc;IAClBC,KAAK,EAAE,kBAAkB;IACzBC,IAAI,EAAE,WAAW;IACjBC,QAAQ,EAAE;EACZ,CAAC;EAED;EACAC,UAAU,EAAE;IACVD,QAAQ,EAAE,gBAAgB;IAC1BE,QAAQ,EAAE,gBAAgB;IAC1BC,UAAU,EAAE,aAAa;IACzBC,MAAM,EAAE;EACV,CAAC;EAED;EACAC,iBAAiB,EAAE;IACjBC,KAAK,EAAE;MACLC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,EAAE;MACXC,WAAW,EAAE;IACf,CAAC;IACDC,cAAc,EAAE,CACd;MACEd,EAAE,EAAE,GAAG;MACPE,IAAI,EAAE,sBAAsB;MAC5Ba,MAAM,EAAE,WAAW;MACnBC,WAAW,EAAE,IAAI9C,IAAI,CAAC,YAAY,CAAC;MACnC+C,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE;IACV,CAAC,CACF;IACDC,gBAAgB,EAAE,CAChB;MACEnB,EAAE,EAAE,GAAG;MACPE,IAAI,EAAE,sBAAsB;MAC5Ba,MAAM,EAAE,OAAO;MACfC,WAAW,EAAE,IAAI9C,IAAI,CAAC,YAAY,CAAC;MACnC+C,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE;IACV,CAAC;EAEL,CAAC;EAED;EACAE,qBAAqB,EAAEA,CAACC,IAAI,EAAEC,OAAO,GAAG,IAAI,MAAM;IAChDC,EAAE,EAAED,OAAO;IACXJ,MAAM,EAAEI,OAAO,GAAG,GAAG,GAAG,GAAG;IAC3BE,IAAI,EAAE1E,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC0E,iBAAiB,CAC/BH,OAAO,GAAG;MAAEA,OAAO,EAAE,IAAI;MAAED;IAAK,CAAC,GAAG;MAAEC,OAAO,EAAE,KAAK;MAAE5F,KAAK,EAAE;IAAa,CAC5E;EACF,CAAC,CAAC;EAEF;EACAgG,OAAO,EAAEA,CAACC,QAAQ,EAAEC,OAAO,GAAG,IAAI,KAAK;IACrC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMC,SAAS,GAAG9D,IAAI,CAACD,GAAG,CAAC,CAAC;MAC5B,MAAMgE,KAAK,GAAGA,CAAA,KAAM;QAClB,IAAI;UACF,MAAMC,MAAM,GAAGP,QAAQ,CAAC,CAAC;UACzB,IAAIO,MAAM,EAAE;YACVJ,OAAO,CAACI,MAAM,CAAC;UACjB,CAAC,MAAM,IAAIhE,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG+D,SAAS,GAAGJ,OAAO,EAAE;YAC3CG,MAAM,CAAC,IAAII,KAAK,CAAC,+BAA+B,CAAC,CAAC;UACpD,CAAC,MAAM;YACLC,UAAU,CAACH,KAAK,EAAE,EAAE,CAAC;UACvB;QACF,CAAC,CAAC,OAAOvG,KAAK,EAAE;UACd,IAAIwC,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG+D,SAAS,GAAGJ,OAAO,EAAE;YACpCG,MAAM,CAACrG,KAAK,CAAC;UACf,CAAC,MAAM;YACL0G,UAAU,CAACH,KAAK,EAAE,EAAE,CAAC;UACvB;QACF;MACF,CAAC;MACDA,KAAK,CAAC,CAAC;IACT,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACAI,UAAU,CAAC,MAAM;EACfvF,IAAI,CAACwF,aAAa,CAAC,CAAC;;EAEpB;EACA5E,gBAAgB,CAACC,OAAO,CAAC4E,SAAS,CAAC,CAAC;EACpC7E,gBAAgB,CAACE,OAAO,CAAC2E,SAAS,CAAC,CAAC;EACpC7E,gBAAgB,CAACG,UAAU,CAAC0E,SAAS,CAAC,CAAC;EACvC7E,gBAAgB,CAACI,KAAK,CAACyE,SAAS,CAAC,CAAC;;EAElC;EACAxE,kBAAkB,CAACJ,OAAO,CAAC4E,SAAS,CAAC,CAAC;EACtCxE,kBAAkB,CAACH,OAAO,CAAC2E,SAAS,CAAC,CAAC;EACtCxE,kBAAkB,CAACF,UAAU,CAAC0E,SAAS,CAAC,CAAC;EACzCxE,kBAAkB,CAACD,KAAK,CAACyE,SAAS,CAAC,CAAC;;EAEpC;EACAvE,KAAK,CAACuE,SAAS,CAAC,CAAC;AACnB,CAAC,CAAC;;AAEF;AACAvH,OAAO,CAACwH,EAAE,CAAC,oBAAoB,EAAE,CAACC,MAAM,EAAEC,OAAO,KAAK;EACpDjH,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEgH,OAAO,EAAE,SAAS,EAAED,MAAM,CAAC;AACtE,CAAC,CAAC", "ignoreList": []}