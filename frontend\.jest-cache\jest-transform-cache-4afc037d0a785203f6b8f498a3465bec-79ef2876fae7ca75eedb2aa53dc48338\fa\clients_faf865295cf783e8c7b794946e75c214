336569915cf37a4463dded5eb33a6782
/* istanbul ignore next */
function cov_23mv97lfr6() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\clients.ts";
  var hash = "a131aa6bf1ea0046b55f36b24379f8d13f99a18e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\clients.ts",
    statementMap: {
      "0": {
        start: {
          line: 14,
          column: 16
        },
        end: {
          line: 20,
          column: 3
        }
      },
      "1": {
        start: {
          line: 22,
          column: 17
        },
        end: {
          line: 22,
          column: 91
        }
      },
      "2": {
        start: {
          line: 23,
          column: 2
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "3": {
        start: {
          line: 28,
          column: 2
        },
        end: {
          line: 30,
          column: 3
        }
      },
      "4": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 29,
          column: 52
        }
      },
      "5": {
        start: {
          line: 32,
          column: 16
        },
        end: {
          line: 44,
          column: 3
        }
      },
      "6": {
        start: {
          line: 46,
          column: 17
        },
        end: {
          line: 46,
          column: 91
        }
      },
      "7": {
        start: {
          line: 48,
          column: 2
        },
        end: {
          line: 55,
          column: 3
        }
      },
      "8": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 53,
          column: 7
        }
      },
      "9": {
        start: {
          line: 54,
          column: 4
        },
        end: {
          line: 54,
          column: 61
        }
      },
      "10": {
        start: {
          line: 57,
          column: 2
        },
        end: {
          line: 59,
          column: 3
        }
      },
      "11": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 58,
          column: 16
        }
      },
      "12": {
        start: {
          line: 62,
          column: 17
        },
        end: {
          line: 62,
          column: 28
        }
      },
      "13": {
        start: {
          line: 63,
          column: 2
        },
        end: {
          line: 70,
          column: 3
        }
      },
      "14": {
        start: {
          line: 64,
          column: 4
        },
        end: {
          line: 69,
          column: 5
        }
      },
      "15": {
        start: {
          line: 65,
          column: 6
        },
        end: {
          line: 65,
          column: 52
        }
      },
      "16": {
        start: {
          line: 67,
          column: 6
        },
        end: {
          line: 67,
          column: 72
        }
      },
      "17": {
        start: {
          line: 68,
          column: 6
        },
        end: {
          line: 68,
          column: 27
        }
      },
      "18": {
        start: {
          line: 72,
          column: 2
        },
        end: {
          line: 72,
          column: 16
        }
      },
      "19": {
        start: {
          line: 77,
          column: 2
        },
        end: {
          line: 83,
          column: 3
        }
      },
      "20": {
        start: {
          line: 78,
          column: 4
        },
        end: {
          line: 82,
          column: 6
        }
      },
      "21": {
        start: {
          line: 85,
          column: 2
        },
        end: {
          line: 170,
          column: 3
        }
      },
      "22": {
        start: {
          line: 86,
          column: 19
        },
        end: {
          line: 86,
          column: 38
        }
      },
      "23": {
        start: {
          line: 87,
          column: 4
        },
        end: {
          line: 93,
          column: 5
        }
      },
      "24": {
        start: {
          line: 88,
          column: 6
        },
        end: {
          line: 92,
          column: 8
        }
      },
      "25": {
        start: {
          line: 95,
          column: 18
        },
        end: {
          line: 107,
          column: 5
        }
      },
      "26": {
        start: {
          line: 109,
          column: 19
        },
        end: {
          line: 109,
          column: 78
        }
      },
      "27": {
        start: {
          line: 111,
          column: 4
        },
        end: {
          line: 117,
          column: 5
        }
      },
      "28": {
        start: {
          line: 112,
          column: 6
        },
        end: {
          line: 116,
          column: 8
        }
      },
      "29": {
        start: {
          line: 119,
          column: 4
        },
        end: {
          line: 125,
          column: 5
        }
      },
      "30": {
        start: {
          line: 120,
          column: 6
        },
        end: {
          line: 124,
          column: 8
        }
      },
      "31": {
        start: {
          line: 127,
          column: 4
        },
        end: {
          line: 134,
          column: 5
        }
      },
      "32": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 96
        }
      },
      "33": {
        start: {
          line: 129,
          column: 6
        },
        end: {
          line: 133,
          column: 8
        }
      },
      "34": {
        start: {
          line: 137,
          column: 23
        },
        end: {
          line: 137,
          column: 37
        }
      },
      "35": {
        start: {
          line: 140,
          column: 25
        },
        end: {
          line: 140,
          column: 81
        }
      },
      "36": {
        start: {
          line: 141,
          column: 24
        },
        end: {
          line: 141,
          column: 56
        }
      },
      "37": {
        start: {
          line: 143,
          column: 41
        },
        end: {
          line: 153,
          column: 5
        }
      },
      "38": {
        start: {
          line: 155,
          column: 4
        },
        end: {
          line: 158,
          column: 6
        }
      },
      "39": {
        start: {
          line: 161,
          column: 4
        },
        end: {
          line: 164,
          column: 7
        }
      },
      "40": {
        start: {
          line: 165,
          column: 4
        },
        end: {
          line: 169,
          column: 6
        }
      },
      "41": {
        start: {
          line: 180,
          column: 16
        },
        end: {
          line: 196,
          column: 3
        }
      },
      "42": {
        start: {
          line: 198,
          column: 17
        },
        end: {
          line: 203,
          column: 3
        }
      },
      "43": {
        start: {
          line: 205,
          column: 17
        },
        end: {
          line: 205,
          column: 89
        }
      },
      "44": {
        start: {
          line: 207,
          column: 2
        },
        end: {
          line: 210,
          column: 3
        }
      },
      "45": {
        start: {
          line: 208,
          column: 4
        },
        end: {
          line: 208,
          column: 58
        }
      },
      "46": {
        start: {
          line: 209,
          column: 4
        },
        end: {
          line: 209,
          column: 47
        }
      },
      "47": {
        start: {
          line: 212,
          column: 2
        },
        end: {
          line: 212,
          column: 21
        }
      },
      "48": {
        start: {
          line: 226,
          column: 33
        },
        end: {
          line: 226,
          column: 35
        }
      },
      "49": {
        start: {
          line: 227,
          column: 24
        },
        end: {
          line: 227,
          column: 26
        }
      },
      "50": {
        start: {
          line: 228,
          column: 19
        },
        end: {
          line: 228,
          column: 20
        }
      },
      "51": {
        start: {
          line: 230,
          column: 2
        },
        end: {
          line: 233,
          column: 3
        }
      },
      "52": {
        start: {
          line: 231,
          column: 4
        },
        end: {
          line: 231,
          column: 49
        }
      },
      "53": {
        start: {
          line: 232,
          column: 4
        },
        end: {
          line: 232,
          column: 30
        }
      },
      "54": {
        start: {
          line: 235,
          column: 2
        },
        end: {
          line: 238,
          column: 3
        }
      },
      "55": {
        start: {
          line: 236,
          column: 4
        },
        end: {
          line: 236,
          column: 51
        }
      },
      "56": {
        start: {
          line: 237,
          column: 4
        },
        end: {
          line: 237,
          column: 32
        }
      },
      "57": {
        start: {
          line: 240,
          column: 2
        },
        end: {
          line: 243,
          column: 3
        }
      },
      "58": {
        start: {
          line: 241,
          column: 4
        },
        end: {
          line: 241,
          column: 51
        }
      },
      "59": {
        start: {
          line: 242,
          column: 4
        },
        end: {
          line: 242,
          column: 32
        }
      },
      "60": {
        start: {
          line: 245,
          column: 2
        },
        end: {
          line: 248,
          column: 3
        }
      },
      "61": {
        start: {
          line: 246,
          column: 4
        },
        end: {
          line: 246,
          column: 53
        }
      },
      "62": {
        start: {
          line: 247,
          column: 4
        },
        end: {
          line: 247,
          column: 50
        }
      },
      "63": {
        start: {
          line: 250,
          column: 2
        },
        end: {
          line: 252,
          column: 3
        }
      },
      "64": {
        start: {
          line: 251,
          column: 4
        },
        end: {
          line: 251,
          column: 16
        }
      },
      "65": {
        start: {
          line: 255,
          column: 2
        },
        end: {
          line: 255,
          column: 24
        }
      },
      "66": {
        start: {
          line: 257,
          column: 16
        },
        end: {
          line: 269,
          column: 3
        }
      },
      "67": {
        start: {
          line: 271,
          column: 17
        },
        end: {
          line: 271,
          column: 89
        }
      },
      "68": {
        start: {
          line: 273,
          column: 2
        },
        end: {
          line: 276,
          column: 3
        }
      },
      "69": {
        start: {
          line: 274,
          column: 4
        },
        end: {
          line: 274,
          column: 58
        }
      },
      "70": {
        start: {
          line: 275,
          column: 4
        },
        end: {
          line: 275,
          column: 47
        }
      },
      "71": {
        start: {
          line: 278,
          column: 2
        },
        end: {
          line: 278,
          column: 21
        }
      }
    },
    fnMap: {
      "0": {
        name: "getTenantByDomain",
        decl: {
          start: {
            line: 13,
            column: 22
          },
          end: {
            line: 13,
            column: 39
          }
        },
        loc: {
          start: {
            line: 13,
            column: 56
          },
          end: {
            line: 24,
            column: 1
          }
        },
        line: 13
      },
      "1": {
        name: "getClientByDomain",
        decl: {
          start: {
            line: 27,
            column: 22
          },
          end: {
            line: 27,
            column: 39
          }
        },
        loc: {
          start: {
            line: 27,
            column: 56
          },
          end: {
            line: 73,
            column: 1
          }
        },
        line: 27
      },
      "2": {
        name: "getClientByEmailDomain",
        decl: {
          start: {
            line: 76,
            column: 22
          },
          end: {
            line: 76,
            column: 44
          }
        },
        loc: {
          start: {
            line: 76,
            column: 89
          },
          end: {
            line: 171,
            column: 1
          }
        },
        line: 76
      },
      "3": {
        name: "createClient",
        decl: {
          start: {
            line: 174,
            column: 22
          },
          end: {
            line: 174,
            column: 34
          }
        },
        loc: {
          start: {
            line: 179,
            column: 3
          },
          end: {
            line: 213,
            column: 1
          }
        },
        line: 179
      },
      "4": {
        name: "updateClient",
        decl: {
          start: {
            line: 216,
            column: 22
          },
          end: {
            line: 216,
            column: 34
          }
        },
        loc: {
          start: {
            line: 224,
            column: 2
          },
          end: {
            line: 279,
            column: 1
          }
        },
        line: 224
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 23,
            column: 9
          },
          end: {
            line: 23,
            column: 44
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 26
          },
          end: {
            line: 23,
            column: 37
          }
        }, {
          start: {
            line: 23,
            column: 40
          },
          end: {
            line: 23,
            column: 44
          }
        }],
        line: 23
      },
      "1": {
        loc: {
          start: {
            line: 28,
            column: 2
          },
          end: {
            line: 30,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 2
          },
          end: {
            line: 30,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "2": {
        loc: {
          start: {
            line: 48,
            column: 2
          },
          end: {
            line: 55,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 48,
            column: 2
          },
          end: {
            line: 55,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 48
      },
      "3": {
        loc: {
          start: {
            line: 57,
            column: 2
          },
          end: {
            line: 59,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 57,
            column: 2
          },
          end: {
            line: 59,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 57
      },
      "4": {
        loc: {
          start: {
            line: 63,
            column: 2
          },
          end: {
            line: 70,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 63,
            column: 2
          },
          end: {
            line: 70,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 63
      },
      "5": {
        loc: {
          start: {
            line: 63,
            column: 6
          },
          end: {
            line: 63,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 63,
            column: 6
          },
          end: {
            line: 63,
            column: 21
          }
        }, {
          start: {
            line: 63,
            column: 25
          },
          end: {
            line: 63,
            column: 60
          }
        }],
        line: 63
      },
      "6": {
        loc: {
          start: {
            line: 77,
            column: 2
          },
          end: {
            line: 83,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 2
          },
          end: {
            line: 83,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 77
      },
      "7": {
        loc: {
          start: {
            line: 77,
            column: 6
          },
          end: {
            line: 77,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 77,
            column: 6
          },
          end: {
            line: 77,
            column: 12
          }
        }, {
          start: {
            line: 77,
            column: 16
          },
          end: {
            line: 77,
            column: 41
          }
        }],
        line: 77
      },
      "8": {
        loc: {
          start: {
            line: 87,
            column: 4
          },
          end: {
            line: 93,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 87,
            column: 4
          },
          end: {
            line: 93,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 87
      },
      "9": {
        loc: {
          start: {
            line: 111,
            column: 4
          },
          end: {
            line: 117,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 111,
            column: 4
          },
          end: {
            line: 117,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 111
      },
      "10": {
        loc: {
          start: {
            line: 119,
            column: 4
          },
          end: {
            line: 125,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 119,
            column: 4
          },
          end: {
            line: 125,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 119
      },
      "11": {
        loc: {
          start: {
            line: 119,
            column: 8
          },
          end: {
            line: 119,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 119,
            column: 8
          },
          end: {
            line: 119,
            column: 20
          }
        }, {
          start: {
            line: 119,
            column: 24
          },
          end: {
            line: 119,
            column: 48
          }
        }],
        line: 119
      },
      "12": {
        loc: {
          start: {
            line: 127,
            column: 4
          },
          end: {
            line: 134,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 127,
            column: 4
          },
          end: {
            line: 134,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 127
      },
      "13": {
        loc: {
          start: {
            line: 140,
            column: 25
          },
          end: {
            line: 140,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 140,
            column: 25
          },
          end: {
            line: 140,
            column: 45
          }
        }, {
          start: {
            line: 140,
            column: 49
          },
          end: {
            line: 140,
            column: 81
          }
        }],
        line: 140
      },
      "14": {
        loc: {
          start: {
            line: 146,
            column: 16
          },
          end: {
            line: 146,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 146,
            column: 16
          },
          end: {
            line: 146,
            column: 36
          }
        }, {
          start: {
            line: 146,
            column: 40
          },
          end: {
            line: 146,
            column: 72
          }
        }],
        line: 146
      },
      "15": {
        loc: {
          start: {
            line: 148,
            column: 15
          },
          end: {
            line: 148,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 148,
            column: 51
          },
          end: {
            line: 148,
            column: 69
          }
        }, {
          start: {
            line: 148,
            column: 72
          },
          end: {
            line: 148,
            column: 80
          }
        }],
        line: 148
      },
      "16": {
        loc: {
          start: {
            line: 152,
            column: 17
          },
          end: {
            line: 152,
            column: 79
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 152,
            column: 41
          },
          end: {
            line: 152,
            column: 72
          }
        }, {
          start: {
            line: 152,
            column: 75
          },
          end: {
            line: 152,
            column: 79
          }
        }],
        line: 152
      },
      "17": {
        loc: {
          start: {
            line: 163,
            column: 13
          },
          end: {
            line: 163,
            column: 67
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 163,
            column: 38
          },
          end: {
            line: 163,
            column: 51
          }
        }, {
          start: {
            line: 163,
            column: 54
          },
          end: {
            line: 163,
            column: 67
          }
        }],
        line: 163
      },
      "18": {
        loc: {
          start: {
            line: 201,
            column: 4
          },
          end: {
            line: 201,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 201,
            column: 4
          },
          end: {
            line: 201,
            column: 21
          }
        }, {
          start: {
            line: 201,
            column: 25
          },
          end: {
            line: 201,
            column: 33
          }
        }],
        line: 201
      },
      "19": {
        loc: {
          start: {
            line: 202,
            column: 4
          },
          end: {
            line: 202,
            column: 68
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 202,
            column: 26
          },
          end: {
            line: 202,
            column: 61
          }
        }, {
          start: {
            line: 202,
            column: 64
          },
          end: {
            line: 202,
            column: 68
          }
        }],
        line: 202
      },
      "20": {
        loc: {
          start: {
            line: 207,
            column: 2
          },
          end: {
            line: 210,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 207,
            column: 2
          },
          end: {
            line: 210,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 207
      },
      "21": {
        loc: {
          start: {
            line: 230,
            column: 2
          },
          end: {
            line: 233,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 230,
            column: 2
          },
          end: {
            line: 233,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 230
      },
      "22": {
        loc: {
          start: {
            line: 235,
            column: 2
          },
          end: {
            line: 238,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 235,
            column: 2
          },
          end: {
            line: 238,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 235
      },
      "23": {
        loc: {
          start: {
            line: 240,
            column: 2
          },
          end: {
            line: 243,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 240,
            column: 2
          },
          end: {
            line: 243,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 240
      },
      "24": {
        loc: {
          start: {
            line: 245,
            column: 2
          },
          end: {
            line: 248,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 245,
            column: 2
          },
          end: {
            line: 248,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 245
      },
      "25": {
        loc: {
          start: {
            line: 250,
            column: 2
          },
          end: {
            line: 252,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 250,
            column: 2
          },
          end: {
            line: 252,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 250
      },
      "26": {
        loc: {
          start: {
            line: 273,
            column: 2
          },
          end: {
            line: 276,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 273,
            column: 2
          },
          end: {
            line: 276,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 273
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a131aa6bf1ea0046b55f36b24379f8d13f99a18e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_23mv97lfr6 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_23mv97lfr6();
import { executeQuery, executeQuerySingle, schemaExists } from './database';

// Client lookup result interface

// Get tenant by email domain
export async function getTenantByDomain(domain) {
  /* istanbul ignore next */
  cov_23mv97lfr6().f[0]++;
  const query =
  /* istanbul ignore next */
  (cov_23mv97lfr6().s[0]++, `
    SELECT t.tenant_id, t.tenant_name, t.schema_name, t.subdomain, t.status, t.created_at
    FROM tenant_management.tenants t
    JOIN tenant_management.domains d ON t.tenant_id = d.tenant_id
    WHERE d.domain_name = $1 AND t.status = 'active'
    LIMIT 1
  `);
  const result =
  /* istanbul ignore next */
  (cov_23mv97lfr6().s[1]++, await executeQuerySingle(query, [domain], {
    schema: 'tenant_management'
  }));
  /* istanbul ignore next */
  cov_23mv97lfr6().s[2]++;
  return result.success ?
  /* istanbul ignore next */
  (cov_23mv97lfr6().b[0][0]++, result.data) :
  /* istanbul ignore next */
  (cov_23mv97lfr6().b[0][1]++, null);
}

// Get client by domain (replacing DynamoDB with PostgreSQL)
export async function getClientByDomain(domain) {
  /* istanbul ignore next */
  cov_23mv97lfr6().f[1]++;
  cov_23mv97lfr6().s[3]++;
  if (!domain) {
    /* istanbul ignore next */
    cov_23mv97lfr6().b[1][0]++;
    cov_23mv97lfr6().s[4]++;
    throw new Error('Domain parameter is required');
  } else
  /* istanbul ignore next */
  {
    cov_23mv97lfr6().b[1][1]++;
  }
  const query =
  /* istanbul ignore next */
  (cov_23mv97lfr6().s[5]++, `
    SELECT
      client_id as id,
      name,
      domain,
      status,
      settings,
      created_at,
      updated_at
    FROM tenant_management.clients
    WHERE domain = $1 AND status = 'active'
    LIMIT 1
  `);
  const result =
  /* istanbul ignore next */
  (cov_23mv97lfr6().s[6]++, await executeQuerySingle(query, [domain], {
    schema: 'tenant_management'
  }));
  /* istanbul ignore next */
  cov_23mv97lfr6().s[7]++;
  if (!result.success) {
    /* istanbul ignore next */
    cov_23mv97lfr6().b[2][0]++;
    cov_23mv97lfr6().s[8]++;
    console.error('Error fetching client by domain:', {
      domain,
      error: result.error,
      errorCode: result.errorCode
    });
    /* istanbul ignore next */
    cov_23mv97lfr6().s[9]++;
    throw new Error('Failed to retrieve client information');
  } else
  /* istanbul ignore next */
  {
    cov_23mv97lfr6().b[2][1]++;
  }
  cov_23mv97lfr6().s[10]++;
  if (!result.data) {
    /* istanbul ignore next */
    cov_23mv97lfr6().b[3][0]++;
    cov_23mv97lfr6().s[11]++;
    return null;
  } else
  /* istanbul ignore next */
  {
    cov_23mv97lfr6().b[3][1]++;
  }

  // Parse JSON settings if needed
  const client =
  /* istanbul ignore next */
  (cov_23mv97lfr6().s[12]++, result.data);
  /* istanbul ignore next */
  cov_23mv97lfr6().s[13]++;
  if (
  /* istanbul ignore next */
  (cov_23mv97lfr6().b[5][0]++, client.settings) &&
  /* istanbul ignore next */
  (cov_23mv97lfr6().b[5][1]++, typeof client.settings === 'string')) {
    /* istanbul ignore next */
    cov_23mv97lfr6().b[4][0]++;
    cov_23mv97lfr6().s[14]++;
    try {
      /* istanbul ignore next */
      cov_23mv97lfr6().s[15]++;
      client.settings = JSON.parse(client.settings);
    } catch (parseError) {
      /* istanbul ignore next */
      cov_23mv97lfr6().s[16]++;
      console.warn('Failed to parse client settings JSON:', parseError);
      /* istanbul ignore next */
      cov_23mv97lfr6().s[17]++;
      client.settings = {};
    }
  } else
  /* istanbul ignore next */
  {
    cov_23mv97lfr6().b[4][1]++;
  }
  cov_23mv97lfr6().s[18]++;
  return client;
}

// Get client by email domain with enhanced error handling
export async function getClientByEmailDomain(email) {
  /* istanbul ignore next */
  cov_23mv97lfr6().f[2]++;
  cov_23mv97lfr6().s[19]++;
  if (
  /* istanbul ignore next */
  (cov_23mv97lfr6().b[7][0]++, !email) ||
  /* istanbul ignore next */
  (cov_23mv97lfr6().b[7][1]++, typeof email !== 'string')) {
    /* istanbul ignore next */
    cov_23mv97lfr6().b[6][0]++;
    cov_23mv97lfr6().s[20]++;
    return {
      success: false,
      error: 'Invalid email provided',
      errorCode: 'INVALID_INPUT'
    };
  } else
  /* istanbul ignore next */
  {
    cov_23mv97lfr6().b[6][1]++;
  }
  cov_23mv97lfr6().s[21]++;
  try {
    const domain =
    /* istanbul ignore next */
    (cov_23mv97lfr6().s[22]++, email.split('@')[1]);
    /* istanbul ignore next */
    cov_23mv97lfr6().s[23]++;
    if (!domain) {
      /* istanbul ignore next */
      cov_23mv97lfr6().b[8][0]++;
      cov_23mv97lfr6().s[24]++;
      return {
        success: false,
        error: 'Invalid email format',
        errorCode: 'INVALID_INPUT'
      };
    } else
    /* istanbul ignore next */
    {
      cov_23mv97lfr6().b[8][1]++;
    }
    const query =
    /* istanbul ignore next */
    (cov_23mv97lfr6().s[25]++, `
      SELECT
        "ClientID" as client_id,
        "ClientName" as client_name,
        "ClientDomain" as domains,
        "TenantID" as tenant_id,
        "Active" as is_active,
        "IndustryID" as industry_id,
        "CreatedOn" as created_at,
        "ChangedOn" as updated_at
      FROM metadata."Clients"
      WHERE $1 = ANY("ClientDomain") AND "Active" = true
    `);
    const result =
    /* istanbul ignore next */
    (cov_23mv97lfr6().s[26]++, await executeQuery(query, [domain], {
      schema: 'metadata'
    }));
    /* istanbul ignore next */
    cov_23mv97lfr6().s[27]++;
    if (!result.success) {
      /* istanbul ignore next */
      cov_23mv97lfr6().b[9][0]++;
      cov_23mv97lfr6().s[28]++;
      return {
        success: false,
        error: 'An error occurred, contact support',
        errorCode: 'DATABASE_ERROR'
      };
    } else
    /* istanbul ignore next */
    {
      cov_23mv97lfr6().b[9][1]++;
    }
    cov_23mv97lfr6().s[29]++;
    if (
    /* istanbul ignore next */
    (cov_23mv97lfr6().b[11][0]++, !result.data) ||
    /* istanbul ignore next */
    (cov_23mv97lfr6().b[11][1]++, result.data.length === 0)) {
      /* istanbul ignore next */
      cov_23mv97lfr6().b[10][0]++;
      cov_23mv97lfr6().s[30]++;
      return {
        success: false,
        error: 'A client was not found, contact support',
        errorCode: 'NOT_FOUND'
      };
    } else
    /* istanbul ignore next */
    {
      cov_23mv97lfr6().b[10][1]++;
    }
    cov_23mv97lfr6().s[31]++;
    if (result.data.length > 1) {
      /* istanbul ignore next */
      cov_23mv97lfr6().b[12][0]++;
      cov_23mv97lfr6().s[32]++;
      console.error('Multiple clients found for domain:', domain, 'Count:', result.data.length);
      /* istanbul ignore next */
      cov_23mv97lfr6().s[33]++;
      return {
        success: false,
        error: 'An error occurred, contact support',
        errorCode: 'MULTIPLE_FOUND'
      };
    } else
    /* istanbul ignore next */
    {
      cov_23mv97lfr6().b[12][1]++;
    }

    // Single client found - create tenant context
    const clientData =
    /* istanbul ignore next */
    (cov_23mv97lfr6().s[34]++, result.data[0]);

    // Check if tenant schema exists
    const tenantSchema =
    /* istanbul ignore next */
    (cov_23mv97lfr6().s[35]++,
    /* istanbul ignore next */
    (cov_23mv97lfr6().b[13][0]++, clientData.tenant_id) ||
    /* istanbul ignore next */
    (cov_23mv97lfr6().b[13][1]++, `tenant_${clientData.client_id}`));
    const schemaReady =
    /* istanbul ignore next */
    (cov_23mv97lfr6().s[36]++, await schemaExists(tenantSchema));
    const tenantContext =
    /* istanbul ignore next */
    (cov_23mv97lfr6().s[37]++, {
      clientId: clientData.client_id.toString(),
      clientName: clientData.client_name,
      tenantId:
      /* istanbul ignore next */
      (cov_23mv97lfr6().b[14][0]++, clientData.tenant_id) ||
      /* istanbul ignore next */
      (cov_23mv97lfr6().b[14][1]++, `tenant_${clientData.client_id}`),
      tenantSchema: tenantSchema,
      domains: Array.isArray(clientData.domains) ?
      /* istanbul ignore next */
      (cov_23mv97lfr6().b[15][0]++, clientData.domains) :
      /* istanbul ignore next */
      (cov_23mv97lfr6().b[15][1]++, [domain]),
      isActive: clientData.is_active,
      settings: {
        schemaReady
      },
      // Include schema readiness status
      createdAt: new Date(clientData.created_at),
      updatedAt: clientData.updated_at ?
      /* istanbul ignore next */
      (cov_23mv97lfr6().b[16][0]++, new Date(clientData.updated_at)) :
      /* istanbul ignore next */
      (cov_23mv97lfr6().b[16][1]++, null)
    });
    /* istanbul ignore next */
    cov_23mv97lfr6().s[38]++;
    return {
      success: true,
      client: tenantContext
    };
  } catch (error) {
    /* istanbul ignore next */
    cov_23mv97lfr6().s[39]++;
    console.error('Database error fetching client by email domain:', {
      email,
      error: error instanceof Error ?
      /* istanbul ignore next */
      (cov_23mv97lfr6().b[17][0]++, error.message) :
      /* istanbul ignore next */
      (cov_23mv97lfr6().b[17][1]++, String(error))
    });
    /* istanbul ignore next */
    cov_23mv97lfr6().s[40]++;
    return {
      success: false,
      error: 'An error occurred, contact support',
      errorCode: 'DATABASE_ERROR'
    };
  }
}

// Create a new client
export async function createClient(clientData) {
  /* istanbul ignore next */
  cov_23mv97lfr6().f[3]++;
  const query =
  /* istanbul ignore next */
  (cov_23mv97lfr6().s[41]++, `
    INSERT INTO tenant_management.clients (
      name,
      domain,
      status,
      settings
    )
    VALUES ($1, $2, $3, $4)
    RETURNING
      client_id as id,
      name,
      domain,
      status,
      settings,
      created_at,
      updated_at
  `);
  const values =
  /* istanbul ignore next */
  (cov_23mv97lfr6().s[42]++, [clientData.name, clientData.domain,
  /* istanbul ignore next */
  (cov_23mv97lfr6().b[18][0]++, clientData.status) ||
  /* istanbul ignore next */
  (cov_23mv97lfr6().b[18][1]++, 'active'), clientData.settings ?
  /* istanbul ignore next */
  (cov_23mv97lfr6().b[19][0]++, JSON.stringify(clientData.settings)) :
  /* istanbul ignore next */
  (cov_23mv97lfr6().b[19][1]++, '{}')]);
  const result =
  /* istanbul ignore next */
  (cov_23mv97lfr6().s[43]++, await executeQuerySingle(query, values, {
    schema: 'tenant_management'
  }));
  /* istanbul ignore next */
  cov_23mv97lfr6().s[44]++;
  if (!result.success) {
    /* istanbul ignore next */
    cov_23mv97lfr6().b[20][0]++;
    cov_23mv97lfr6().s[45]++;
    console.error('Error creating client:', result.error);
    /* istanbul ignore next */
    cov_23mv97lfr6().s[46]++;
    throw new Error('Failed to create client');
  } else
  /* istanbul ignore next */
  {
    cov_23mv97lfr6().b[20][1]++;
  }
  cov_23mv97lfr6().s[47]++;
  return result.data;
}

// Update an existing client
export async function updateClient(clientId, updates) {
  /* istanbul ignore next */
  cov_23mv97lfr6().f[4]++;
  // Build dynamic update query
  const updateFields =
  /* istanbul ignore next */
  (cov_23mv97lfr6().s[48]++, []);
  const values =
  /* istanbul ignore next */
  (cov_23mv97lfr6().s[49]++, []);
  let paramIndex =
  /* istanbul ignore next */
  (cov_23mv97lfr6().s[50]++, 1);
  /* istanbul ignore next */
  cov_23mv97lfr6().s[51]++;
  if (updates.name !== undefined) {
    /* istanbul ignore next */
    cov_23mv97lfr6().b[21][0]++;
    cov_23mv97lfr6().s[52]++;
    updateFields.push(`name = $${paramIndex++}`);
    /* istanbul ignore next */
    cov_23mv97lfr6().s[53]++;
    values.push(updates.name);
  } else
  /* istanbul ignore next */
  {
    cov_23mv97lfr6().b[21][1]++;
  }
  cov_23mv97lfr6().s[54]++;
  if (updates.domain !== undefined) {
    /* istanbul ignore next */
    cov_23mv97lfr6().b[22][0]++;
    cov_23mv97lfr6().s[55]++;
    updateFields.push(`domain = $${paramIndex++}`);
    /* istanbul ignore next */
    cov_23mv97lfr6().s[56]++;
    values.push(updates.domain);
  } else
  /* istanbul ignore next */
  {
    cov_23mv97lfr6().b[22][1]++;
  }
  cov_23mv97lfr6().s[57]++;
  if (updates.status !== undefined) {
    /* istanbul ignore next */
    cov_23mv97lfr6().b[23][0]++;
    cov_23mv97lfr6().s[58]++;
    updateFields.push(`status = $${paramIndex++}`);
    /* istanbul ignore next */
    cov_23mv97lfr6().s[59]++;
    values.push(updates.status);
  } else
  /* istanbul ignore next */
  {
    cov_23mv97lfr6().b[23][1]++;
  }
  cov_23mv97lfr6().s[60]++;
  if (updates.settings !== undefined) {
    /* istanbul ignore next */
    cov_23mv97lfr6().b[24][0]++;
    cov_23mv97lfr6().s[61]++;
    updateFields.push(`settings = $${paramIndex++}`);
    /* istanbul ignore next */
    cov_23mv97lfr6().s[62]++;
    values.push(JSON.stringify(updates.settings));
  } else
  /* istanbul ignore next */
  {
    cov_23mv97lfr6().b[24][1]++;
  }
  cov_23mv97lfr6().s[63]++;
  if (updateFields.length === 0) {
    /* istanbul ignore next */
    cov_23mv97lfr6().b[25][0]++;
    cov_23mv97lfr6().s[64]++;
    return null; // Nothing to update
  } else
  /* istanbul ignore next */
  {
    cov_23mv97lfr6().b[25][1]++;
  }

  // Add client_id to values array
  cov_23mv97lfr6().s[65]++;
  values.push(clientId);
  const query =
  /* istanbul ignore next */
  (cov_23mv97lfr6().s[66]++, `
    UPDATE tenant_management.clients
    SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
    WHERE client_id = $${paramIndex}
    RETURNING
      client_id as id,
      name,
      domain,
      status,
      settings,
      created_at,
      updated_at
  `);
  const result =
  /* istanbul ignore next */
  (cov_23mv97lfr6().s[67]++, await executeQuerySingle(query, values, {
    schema: 'tenant_management'
  }));
  /* istanbul ignore next */
  cov_23mv97lfr6().s[68]++;
  if (!result.success) {
    /* istanbul ignore next */
    cov_23mv97lfr6().b[26][0]++;
    cov_23mv97lfr6().s[69]++;
    console.error('Error updating client:', result.error);
    /* istanbul ignore next */
    cov_23mv97lfr6().s[70]++;
    throw new Error('Failed to update client');
  } else
  /* istanbul ignore next */
  {
    cov_23mv97lfr6().b[26][1]++;
  }
  cov_23mv97lfr6().s[71]++;
  return result.data;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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