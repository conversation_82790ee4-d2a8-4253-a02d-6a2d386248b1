import { NextResponse } from 'next/server';
import { verifySession } from '@/lib/dal';
import { getDbPool } from '@/lib/db-config';
import { getClientByEmailDomain } from '@/lib/clients';

export async function GET() {
  // Verify authentication
  const session = await verifySession();
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Get tenant context using the new secure method
    const clientResult = await getClientByEmailDomain(session.email);

    if (!clientResult.success) {
      const statusCode = clientResult.errorCode === 'NOT_FOUND' ? 404 : 500;
      return NextResponse.json(
        { error: clientResult.error },
        { status: statusCode }
      );
    }

    const tenant = clientResult.client!;
    const pool = await getDbPool();

    // Query tenant schema for actual data
    // For now using placeholder queries - replace with actual tenant schema tables
    try {
      // Example queries to tenant schema (replace with actual table names)
      const renewalsQuery = `
        SELECT COUNT(*) as total_renewals
        FROM "${tenant.tenantSchema}"."Renewals"
        WHERE "Active" = true
      `;

      const dueSoonQuery = `
        SELECT COUNT(*) as renewals_due
        FROM "${tenant.tenantSchema}"."Renewals"
        WHERE "Active" = true
        AND "DueDate" <= CURRENT_DATE + INTERVAL '30 days'
      `;

      const vendorsQuery = `
        SELECT COUNT(DISTINCT "VendorID") as vendors
        FROM "${tenant.tenantSchema}"."Renewals"
        WHERE "Active" = true
      `;

      const spendQuery = `
        SELECT COALESCE(SUM("AnnualCost"), 0) as annual_spend
        FROM "${tenant.tenantSchema}"."Renewals"
        WHERE "Active" = true
      `;

      // Execute queries (with fallback to mock data if tables don't exist)
      let stats = {
        totalRenewals: 0,
        renewalsDue: 0,
        vendors: 0,
        annualSpend: '$817,340'
      };

      // Try to get real data, fall back to mock if schema doesn't exist yet
      try {
        const [renewalsResult, dueResult, vendorsResult, spendResult] = await Promise.all([
          pool.query(renewalsQuery),
          pool.query(dueSoonQuery),
          pool.query(vendorsQuery),
          pool.query(spendQuery)
        ]);

        stats = {
          totalRenewals: parseInt(renewalsResult.rows[0]?.total_renewals || '0'),
          renewalsDue: parseInt(dueResult.rows[0]?.renewals_due || '0'),
          vendors: parseInt(vendorsResult.rows[0]?.vendors || '0'),
          annualSpend: `$${parseFloat(spendResult.rows[0]?.annual_spend || '0').toLocaleString()}`
        };
      } catch (schemaError) {
        console.log(`Tenant schema ${tenant.tenantSchema} not ready yet, using mock data`);
      }

      return NextResponse.json(stats);

    } catch (error) {
      console.error('Error querying tenant data:', error);
      // Return mock data as fallback
      return NextResponse.json({
        totalRenewals: 14,
        renewalsDue: 3,
        vendors: 9,
        annualSpend: '$817,340'
      });
    }

  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
