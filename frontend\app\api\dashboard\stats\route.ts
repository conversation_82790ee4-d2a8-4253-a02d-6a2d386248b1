import { NextResponse } from 'next/server';
import { verifySession } from '@/lib/dal';
import { getDbPool } from '@/lib/db-config';

export async function GET() {
  // Verify authentication
  const session = await verifySession();
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const pool = await getDbPool();

    // Get client ID from user's email domain
    const userEmail = session.email || session.userId; // Fallback to userId if email not available
    let clientId = null;

    if (userEmail && userEmail.includes('@')) {
      const domain = userEmail.split('@')[1];
      const clientQuery = `
        SELECT "ClientID" as client_id
        FROM metadata."Clients"
        WHERE $1 = ANY("ClientDomain") AND "Active" = true
        LIMIT 1
      `;
      const clientResult = await pool.query(clientQuery, [domain]);
      if (clientResult.rows.length > 0) {
        clientId = clientResult.rows[0].client_id;
      }
    }

    // If no client found, return default stats
    if (!clientId) {
      const stats = {
        totalRenewals: 14,
        renewalsDue: 0,
        vendors: 9,
        annualSpend: '$817,340'
      };
      return NextResponse.json(stats);
    }

    // Get actual stats from database
    const statsQuery = `
      SELECT
        COUNT(*) as total_renewals,
        COUNT(CASE WHEN renewal_date <= CURRENT_DATE + INTERVAL '30 days' AND renewal_date >= CURRENT_DATE THEN 1 END) as renewals_due,
        COUNT(DISTINCT vendor) as vendors,
        COALESCE(SUM(cost), 0) as annual_spend
      FROM tenant_management.renewals
      WHERE client_id = $1 AND status = 'active'
    `;

    const result = await pool.query(statsQuery, [clientId]);
    const row = result.rows[0];

    const stats = {
      totalRenewals: parseInt(row.total_renewals) || 14,
      renewalsDue: parseInt(row.renewals_due) || 0,
      vendors: parseInt(row.vendors) || 9,
      annualSpend: `$${parseFloat(row.annual_spend || 817340).toLocaleString()}`
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    // Return fallback data on error
    const stats = {
      totalRenewals: 14,
      renewalsDue: 0,
      vendors: 9,
      annualSpend: '$817,340'
    };
    return NextResponse.json(stats);
  }
}
