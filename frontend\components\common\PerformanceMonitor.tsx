/**
 * Performance Monitor Component
 * 
 * Provides real-time performance monitoring and optimization suggestions
 * for development and debugging purposes.
 */

'use client'

import React, { useState, useEffect, memo } from 'react'
import { cacheUtils } from '@/lib/cache'

interface PerformanceMetrics {
  renderCount: number
  lastRenderTime: number
  averageRenderTime: number
  memoryUsage?: {
    used: number
    total: number
    percentage: number
  }
  cacheStats: {
    api: any
    component: any
    userData: any
  }
}

interface PerformanceMonitorProps {
  enabled?: boolean
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  minimized?: boolean
}

const PerformanceMonitor = memo(function PerformanceMonitor({
  enabled = process.env.NODE_ENV === 'development',
  position = 'bottom-right',
  minimized: initialMinimized = true
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderCount: 0,
    lastRenderTime: 0,
    averageRenderTime: 0,
    cacheStats: {
      api: { hits: 0, misses: 0, hitRate: 0 },
      component: { hits: 0, misses: 0, hitRate: 0 },
      userData: { hits: 0, misses: 0, hitRate: 0 }
    }
  })
  
  const [minimized, setMinimized] = useState(initialMinimized)
  const [isVisible, setIsVisible] = useState(enabled)

  useEffect(() => {
    if (!enabled) return

    let frameCount = 0
    let totalRenderTime = 0
    let lastTime = performance.now()

    const updateMetrics = () => {
      const currentTime = performance.now()
      const renderTime = currentTime - lastTime
      
      frameCount++
      totalRenderTime += renderTime
      
      // Get memory usage if available
      let memoryUsage
      if ('memory' in performance) {
        const memory = (performance as any).memory
        memoryUsage = {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
        }
      }

      setMetrics({
        renderCount: frameCount,
        lastRenderTime: renderTime,
        averageRenderTime: totalRenderTime / frameCount,
        memoryUsage,
        cacheStats: cacheUtils.getGlobalStats()
      })

      lastTime = currentTime
    }

    // Update metrics every second
    const interval = setInterval(updateMetrics, 1000)

    return () => clearInterval(interval)
  }, [enabled])

  if (!isVisible) return null

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'text-green-600'
    if (value <= thresholds.warning) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <div 
      className={`fixed ${positionClasses[position]} z-50 bg-white border border-gray-300 rounded-lg shadow-lg transition-all duration-200 ${
        minimized ? 'w-12 h-12' : 'w-80 max-h-96 overflow-y-auto'
      }`}
    >
      {minimized ? (
        <button
          onClick={() => setMinimized(false)}
          className="w-full h-full flex items-center justify-center text-lg hover:bg-gray-50 rounded-lg"
          title="Performance Monitor"
        >
          📊
        </button>
      ) : (
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold text-gray-800">Performance Monitor</h3>
            <div className="flex space-x-1">
              <button
                onClick={() => setMinimized(true)}
                className="text-gray-500 hover:text-gray-700 text-xs"
                title="Minimize"
              >
                ➖
              </button>
              <button
                onClick={() => setIsVisible(false)}
                className="text-gray-500 hover:text-gray-700 text-xs"
                title="Close"
              >
                ✕
              </button>
            </div>
          </div>

          {/* Render Performance */}
          <div className="mb-3">
            <h4 className="text-xs font-medium text-gray-600 mb-1">Render Performance</h4>
            <div className="text-xs space-y-1">
              <div className="flex justify-between">
                <span>Renders:</span>
                <span>{metrics.renderCount}</span>
              </div>
              <div className="flex justify-between">
                <span>Last Render:</span>
                <span className={getPerformanceColor(metrics.lastRenderTime, { good: 16, warning: 33 })}>
                  {metrics.lastRenderTime.toFixed(1)}ms
                </span>
              </div>
              <div className="flex justify-between">
                <span>Avg Render:</span>
                <span className={getPerformanceColor(metrics.averageRenderTime, { good: 16, warning: 33 })}>
                  {metrics.averageRenderTime.toFixed(1)}ms
                </span>
              </div>
            </div>
          </div>

          {/* Memory Usage */}
          {metrics.memoryUsage && (
            <div className="mb-3">
              <h4 className="text-xs font-medium text-gray-600 mb-1">Memory Usage</h4>
              <div className="text-xs space-y-1">
                <div className="flex justify-between">
                  <span>Used:</span>
                  <span>{formatBytes(metrics.memoryUsage.used)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total:</span>
                  <span>{formatBytes(metrics.memoryUsage.total)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Usage:</span>
                  <span className={getPerformanceColor(metrics.memoryUsage.percentage, { good: 70, warning: 85 })}>
                    {metrics.memoryUsage.percentage.toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Cache Performance */}
          <div className="mb-3">
            <h4 className="text-xs font-medium text-gray-600 mb-1">Cache Performance</h4>
            <div className="text-xs space-y-2">
              {Object.entries(metrics.cacheStats).map(([name, stats]) => (
                <div key={name}>
                  <div className="font-medium capitalize">{name}:</div>
                  <div className="ml-2 space-y-1">
                    <div className="flex justify-between">
                      <span>Hit Rate:</span>
                      <span className={getPerformanceColor(100 - (stats.hitRate * 100), { good: 20, warning: 50 })}>
                        {(stats.hitRate * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Hits/Misses:</span>
                      <span>{stats.hits}/{stats.misses}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Performance Tips */}
          <div className="border-t pt-2">
            <h4 className="text-xs font-medium text-gray-600 mb-1">Tips</h4>
            <div className="text-xs text-gray-500 space-y-1">
              {metrics.averageRenderTime > 16 && (
                <div className="text-yellow-600">• Consider memoizing components</div>
              )}
              {metrics.memoryUsage && metrics.memoryUsage.percentage > 85 && (
                <div className="text-red-600">• High memory usage detected</div>
              )}
              {metrics.cacheStats.api.hitRate < 0.7 && (
                <div className="text-yellow-600">• Low API cache hit rate</div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
})

export default PerformanceMonitor
