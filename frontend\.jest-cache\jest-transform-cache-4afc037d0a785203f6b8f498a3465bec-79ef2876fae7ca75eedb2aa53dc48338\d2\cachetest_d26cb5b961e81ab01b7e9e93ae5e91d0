76f1424b02b0cf7a920944f20806c56e
"use strict";

var _cache = require("@/lib/cache");
var _globals = require("@jest/globals");
/**
 * Cache System Tests
 * 
 * Tests for the advanced caching system including different eviction strategies,
 * TTL handling, and performance metrics
 */

describe('Advanced Cache System', () => {
  let cache;
  beforeEach(() => {
    _globals.jest.clearAllMocks();
    cache = new _cache.AdvancedCache({
      maxSize: 3,
      defaultTTL: 1000,
      // 1 second for testing
      cleanupInterval: 100,
      // 100ms for testing
      enableMetrics: true
    }, 'lru');
  });
  afterEach(() => {
    cache.destroy();
  });
  describe('Basic Operations', () => {
    it('should store and retrieve values', () => {
      cache.set('key1', 'value1');
      expect(cache.get('key1')).toBe('value1');
    });
    it('should return null for non-existent keys', () => {
      expect(cache.get('nonexistent')).toBe(null);
    });
    it('should check if key exists', () => {
      cache.set('key1', 'value1');
      expect(cache.has('key1')).toBe(true);
      expect(cache.has('nonexistent')).toBe(false);
    });
    it('should delete keys', () => {
      cache.set('key1', 'value1');
      expect(cache.delete('key1')).toBe(true);
      expect(cache.get('key1')).toBe(null);
      expect(cache.delete('nonexistent')).toBe(false);
    });
    it('should clear all entries', () => {
      cache.set('key1', 'value1');
      cache.set('key2', 'value2');
      cache.clear();
      expect(cache.size()).toBe(0);
      expect(cache.get('key1')).toBe(null);
    });
    it('should return correct size', () => {
      expect(cache.size()).toBe(0);
      cache.set('key1', 'value1');
      expect(cache.size()).toBe(1);
      cache.set('key2', 'value2');
      expect(cache.size()).toBe(2);
    });
    it('should return all keys', () => {
      cache.set('key1', 'value1');
      cache.set('key2', 'value2');
      const keys = cache.keys();
      expect(keys).toContain('key1');
      expect(keys).toContain('key2');
      expect(keys).toHaveLength(2);
    });
  });
  describe('TTL (Time To Live)', () => {
    it('should expire entries after TTL', async () => {
      cache.set('key1', 'value1', 50); // 50ms TTL
      expect(cache.get('key1')).toBe('value1');

      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 100));
      expect(cache.get('key1')).toBe(null);
    });
    it('should use default TTL when not specified', async () => {
      cache.set('key1', 'value1'); // Uses default TTL (1000ms)
      expect(cache.get('key1')).toBe('value1');

      // Should still be valid after short time
      await new Promise(resolve => setTimeout(resolve, 100));
      expect(cache.get('key1')).toBe('value1');
    });
    it('should handle custom TTL per entry', () => {
      cache.set('short', 'value1', 50);
      cache.set('long', 'value2', 2000);
      setTimeout(() => {
        expect(cache.get('short')).toBe(null);
        expect(cache.get('long')).toBe('value2');
      }, 100);
    });
  });
  describe('Eviction Strategies', () => {
    describe('LRU (Least Recently Used)', () => {
      beforeEach(() => {
        cache = new _cache.AdvancedCache({
          maxSize: 3
        }, 'lru');
      });
      it('should evict least recently used item', () => {
        cache.set('key1', 'value1');
        cache.set('key2', 'value2');
        cache.set('key3', 'value3');

        // Access key1 to make it recently used
        cache.get('key1');

        // Add new item, should evict key2 (least recently used)
        cache.set('key4', 'value4');
        expect(cache.get('key1')).toBe('value1');
        expect(cache.get('key2')).toBe(null);
        expect(cache.get('key3')).toBe('value3');
        expect(cache.get('key4')).toBe('value4');
      });
    });
    describe('LFU (Least Frequently Used)', () => {
      beforeEach(() => {
        cache = new _cache.AdvancedCache({
          maxSize: 3
        }, 'lfu');
      });
      it('should evict least frequently used item', () => {
        cache.set('key1', 'value1');
        cache.set('key2', 'value2');
        cache.set('key3', 'value3');

        // Access key1 multiple times
        cache.get('key1');
        cache.get('key1');
        cache.get('key2'); // key2 accessed once, key3 never accessed

        // Add new item, should evict key3 (least frequently used)
        cache.set('key4', 'value4');
        expect(cache.get('key1')).toBe('value1');
        expect(cache.get('key2')).toBe('value2');
        expect(cache.get('key3')).toBe(null);
        expect(cache.get('key4')).toBe('value4');
      });
    });
    describe('FIFO (First In, First Out)', () => {
      beforeEach(() => {
        cache = new _cache.AdvancedCache({
          maxSize: 3
        }, 'fifo');
      });
      it('should evict first inserted item', () => {
        cache.set('key1', 'value1');
        cache.set('key2', 'value2');
        cache.set('key3', 'value3');

        // Add new item, should evict key1 (first in)
        cache.set('key4', 'value4');
        expect(cache.get('key1')).toBe(null);
        expect(cache.get('key2')).toBe('value2');
        expect(cache.get('key3')).toBe('value3');
        expect(cache.get('key4')).toBe('value4');
      });
    });
  });
  describe('Tag-based Invalidation', () => {
    it('should clear entries by tags', () => {
      cache.set('user1', 'data1', undefined, ['user', 'profile']);
      cache.set('user2', 'data2', undefined, ['user', 'settings']);
      cache.set('admin1', 'data3', undefined, ['admin', 'profile']);
      const cleared = cache.clearByTags(['user']);
      expect(cleared).toBe(2);
      expect(cache.get('user1')).toBe(null);
      expect(cache.get('user2')).toBe(null);
      expect(cache.get('admin1')).toBe('data3');
    });
    it('should clear entries with multiple matching tags', () => {
      cache.set('item1', 'data1', undefined, ['tag1', 'tag2']);
      cache.set('item2', 'data2', undefined, ['tag2', 'tag3']);
      cache.set('item3', 'data3', undefined, ['tag3', 'tag4']);
      const cleared = cache.clearByTags(['tag2', 'tag4']);
      expect(cleared).toBe(3); // All items have at least one matching tag

      expect(cache.get('item1')).toBe(null);
      expect(cache.get('item2')).toBe(null);
      expect(cache.get('item3')).toBe(null);
    });
  });
  describe('Metrics', () => {
    it('should track hit and miss rates', () => {
      cache.set('key1', 'value1');

      // Hit
      cache.get('key1');
      // Miss
      cache.get('nonexistent');
      const metrics = cache.getMetrics();
      expect(metrics.hits).toBe(1);
      expect(metrics.misses).toBe(1);
      expect(metrics.hitRate).toBe(0.5);
    });
    it('should track cache size', () => {
      cache.set('key1', 'value1');
      cache.set('key2', 'value2');
      const metrics = cache.getMetrics();
      expect(metrics.size).toBe(2);
    });
    it('should track evictions', () => {
      // Fill cache to capacity
      cache.set('key1', 'value1');
      cache.set('key2', 'value2');
      cache.set('key3', 'value3');

      // This should trigger eviction
      cache.set('key4', 'value4');
      const metrics = cache.getMetrics();
      expect(metrics.evictions).toBe(1);
    });
    it('should calculate hit rate correctly', () => {
      cache.set('key1', 'value1');

      // 3 hits, 2 misses = 60% hit rate
      cache.get('key1'); // hit
      cache.get('key1'); // hit
      cache.get('key1'); // hit
      cache.get('nonexistent1'); // miss
      cache.get('nonexistent2'); // miss

      const metrics = cache.getMetrics();
      expect(metrics.hitRate).toBe(0.6);
    });
  });
  describe('Cleanup', () => {
    it('should automatically clean up expired entries', async () => {
      cache.set('key1', 'value1', 50); // 50ms TTL
      cache.set('key2', 'value2', 2000); // 2s TTL

      expect(cache.size()).toBe(2);

      // Wait for cleanup cycle
      await new Promise(resolve => setTimeout(resolve, 200));
      expect(cache.get('key1')).toBe(null);
      expect(cache.get('key2')).toBe('value2');
    });
    it('should destroy cache and stop cleanup', () => {
      const spy = _globals.jest.spyOn(global, 'clearInterval');
      cache.destroy();
      expect(spy).toHaveBeenCalled();
      expect(cache.size()).toBe(0);
    });
  });
  describe('Cache Utils', () => {
    it('should create cache keys correctly', () => {
      const key = _cache.cacheUtils.createKey('user', 'id123', 'profile');
      expect(key).toBe('user:id123:profile');
    });
    it('should serialize objects correctly', () => {
      const obj = {
        name: 'test',
        date: new Date('2025-01-01')
      };
      const serialized = _cache.cacheUtils.serialize(obj);
      expect(typeof serialized).toBe('string');
      expect(serialized).toContain('test');
    });
    it('should deserialize objects correctly', () => {
      const obj = {
        name: 'test',
        date: new Date('2025-01-01')
      };
      const serialized = _cache.cacheUtils.serialize(obj);
      const deserialized = _cache.cacheUtils.deserialize(serialized);
      expect(deserialized.name).toBe('test');
      expect(deserialized.date).toBeInstanceOf(Date);
    });
    it('should handle serialization errors gracefully', () => {
      const circular = {};
      circular.self = circular;
      const result = _cache.cacheUtils.serialize(circular);
      expect(typeof result).toBe('string');
    });
    it('should handle deserialization errors gracefully', () => {
      const result = _cache.cacheUtils.deserialize('invalid json');
      expect(result).toBe(null);
    });
    it('should get global cache statistics', () => {
      const stats = _cache.cacheUtils.getGlobalStats();
      expect(stats).toHaveProperty('api');
      expect(stats).toHaveProperty('component');
      expect(stats).toHaveProperty('userData');
    });
    it('should clear all global caches', () => {
      expect(() => _cache.cacheUtils.clearAll()).not.toThrow();
    });
  });
  describe('Error Handling', () => {
    it('should handle invalid TTL values', () => {
      expect(() => cache.set('key', 'value', -1)).not.toThrow();
      expect(() => cache.set('key', 'value', 0)).not.toThrow();
    });
    it('should handle empty cache operations', () => {
      expect(cache.get('nonexistent')).toBe(null);
      expect(cache.delete('nonexistent')).toBe(false);
      expect(cache.clearByTags(['nonexistent'])).toBe(0);
    });
    it('should handle large cache operations', () => {
      // Test with many entries
      for (let i = 0; i < 100; i++) {
        cache.set(`key${i}`, `value${i}`);
      }
      expect(cache.size()).toBeLessThanOrEqual(cache['config'].maxSize);
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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