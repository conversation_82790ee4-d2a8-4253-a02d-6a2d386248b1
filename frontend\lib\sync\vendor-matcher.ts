/**
 * Vendor Matching Algorithm
 * 
 * Implements sophisticated vendor matching logic with multiple criteria:
 * 1. Exact Tax ID Match (confidence: 95%)
 * 2. Domain + Name Match (confidence: 90%)
 * 3. Fuzzy Name + Address Match (confidence: 70-85%)
 * 4. Phone Number Match (confidence: 60-75%)
 */

import { executeQuery } from '@/lib/database';

// Interfaces for vendor matching
export interface TenantVendor {
  id: string;
  name: string;
  display_name?: string;
  contact_email?: string;
  phone?: string;
  address_line1?: string;
  address_line2?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  tax_id?: string;
  website?: string;
}

export interface GlobalVendor {
  id: string;
  canonical_name: string;
  legal_name?: string;
  tax_id?: string;
  country_code?: string;
  domain?: string;
  address_hash?: string;
  phone_normalized?: string;
  confidence_score: number;
}

export interface VendorMatch {
  global_vendor_id: string;
  confidence_score: number;
  match_reasons: string[];
  match_details: {
    tax_id_match?: boolean;
    domain_match?: boolean;
    name_similarity?: number;
    address_similarity?: number;
    phone_match?: boolean;
  };
}

export interface VendorMatchResult {
  tenant_vendor_id: string;
  matches: VendorMatch[];
  best_match?: VendorMatch;
  recommendation: 'auto_match' | 'manual_review' | 'create_new';
}

export class VendorMatcher {
  // Confidence thresholds
  private static readonly AUTO_MATCH_THRESHOLD = 0.85;
  private static readonly MANUAL_REVIEW_THRESHOLD = 0.50;
  
  /**
   * Find matches for a tenant vendor against global vendors
   */
  static async findMatches(tenantVendor: TenantVendor): Promise<VendorMatchResult> {
    const matches: VendorMatch[] = [];
    
    // Get all active global vendors
    const globalVendorsResult = await executeQuery(`
      SELECT 
        id, canonical_name, legal_name, tax_id, country_code, 
        domain, address_hash, phone_normalized, confidence_score
      FROM metadata.global_vendors 
      WHERE status = 'active'
      ORDER BY confidence_score DESC
    `);
    
    if (!globalVendorsResult.success || !globalVendorsResult.data) {
      return {
        tenant_vendor_id: tenantVendor.id,
        matches: [],
        recommendation: 'create_new'
      };
    }
    
    const globalVendors: GlobalVendor[] = globalVendorsResult.data;
    
    // Calculate match scores for each global vendor
    for (const globalVendor of globalVendors) {
      const match = this.calculateMatchScore(tenantVendor, globalVendor);
      if (match.confidence_score > 0) {
        matches.push(match);
      }
    }
    
    // Sort matches by confidence score (descending)
    matches.sort((a, b) => b.confidence_score - a.confidence_score);
    
    // Determine recommendation
    const bestMatch = matches.length > 0 ? matches[0] : undefined;
    let recommendation: 'auto_match' | 'manual_review' | 'create_new' = 'create_new';
    
    if (bestMatch) {
      if (bestMatch.confidence_score >= this.AUTO_MATCH_THRESHOLD) {
        recommendation = 'auto_match';
      } else if (bestMatch.confidence_score >= this.MANUAL_REVIEW_THRESHOLD) {
        recommendation = 'manual_review';
      }
    }
    
    return {
      tenant_vendor_id: tenantVendor.id,
      matches: matches.slice(0, 5), // Return top 5 matches
      best_match: bestMatch,
      recommendation
    };
  }
  
  /**
   * Calculate match score between tenant and global vendor
   */
  private static calculateMatchScore(tenantVendor: TenantVendor, globalVendor: GlobalVendor): VendorMatch {
    const matchReasons: string[] = [];
    const matchDetails: VendorMatch['match_details'] = {};
    let totalScore = 0;
    let weightSum = 0;
    
    // 1. Exact Tax ID Match (confidence: 95%, weight: 40)
    if (tenantVendor.tax_id && globalVendor.tax_id) {
      const normalizedTenantTaxId = this.normalizeTaxId(tenantVendor.tax_id);
      const normalizedGlobalTaxId = this.normalizeTaxId(globalVendor.tax_id);
      
      if (normalizedTenantTaxId === normalizedGlobalTaxId) {
        totalScore += 0.95 * 40;
        weightSum += 40;
        matchReasons.push('Exact Tax ID match');
        matchDetails.tax_id_match = true;
      }
    }
    
    // 2. Domain + Name Match (confidence: 90%, weight: 30)
    const domainMatch = this.checkDomainMatch(tenantVendor, globalVendor);
    if (domainMatch.isMatch) {
      const nameScore = this.calculateNameSimilarity(tenantVendor.name, globalVendor.canonical_name);
      if (nameScore > 0.7) {
        const combinedScore = 0.9 * (0.6 + 0.4 * nameScore); // Domain weight 60%, name weight 40%
        totalScore += combinedScore * 30;
        weightSum += 30;
        matchReasons.push(`Domain match (${domainMatch.domain}) with name similarity`);
        matchDetails.domain_match = true;
        matchDetails.name_similarity = nameScore;
      }
    }
    
    // 3. Fuzzy Name + Address Match (confidence: 70-85%, weight: 20)
    const nameScore = this.calculateNameSimilarity(tenantVendor.name, globalVendor.canonical_name);
    if (nameScore > 0.6) {
      const addressScore = this.calculateAddressSimilarity(tenantVendor, globalVendor);
      const combinedScore = Math.min(0.85, 0.7 + 0.15 * Math.min(nameScore, addressScore));
      
      totalScore += combinedScore * 20;
      weightSum += 20;
      matchReasons.push(`Name similarity (${Math.round(nameScore * 100)}%)`);
      matchDetails.name_similarity = nameScore;
      matchDetails.address_similarity = addressScore;
      
      if (addressScore > 0.5) {
        matchReasons.push(`Address similarity (${Math.round(addressScore * 100)}%)`);
      }
    }
    
    // 4. Phone Number Match (confidence: 60-75%, weight: 10)
    if (tenantVendor.phone && globalVendor.phone_normalized) {
      const phoneScore = this.calculatePhoneSimilarity(tenantVendor.phone, globalVendor.phone_normalized);
      if (phoneScore > 0.7) {
        const confidence = 0.6 + 0.15 * phoneScore;
        totalScore += confidence * 10;
        weightSum += 10;
        matchReasons.push('Phone number match');
        matchDetails.phone_match = true;
      }
    }
    
    // Calculate final confidence score
    const confidenceScore = weightSum > 0 ? totalScore / weightSum : 0;
    
    return {
      global_vendor_id: globalVendor.id,
      confidence_score: Math.round(confidenceScore * 10000) / 10000, // Round to 4 decimal places
      match_reasons: matchReasons,
      match_details: matchDetails
    };
  }
  
  /**
   * Normalize tax ID by removing spaces, hyphens, and converting to uppercase
   */
  private static normalizeTaxId(taxId: string): string {
    return taxId.replace(/[\s\-]/g, '').toUpperCase();
  }
  
  /**
   * Check for domain match between tenant and global vendor
   */
  private static checkDomainMatch(tenantVendor: TenantVendor, globalVendor: GlobalVendor): { isMatch: boolean; domain?: string } {
    if (!globalVendor.domain) {
      return { isMatch: false };
    }
    
    // Extract domain from tenant email
    const tenantDomain = this.extractDomainFromEmail(tenantVendor.contact_email);
    if (tenantDomain && tenantDomain.toLowerCase() === globalVendor.domain.toLowerCase()) {
      return { isMatch: true, domain: tenantDomain };
    }
    
    // Extract domain from tenant website
    const websiteDomain = this.extractDomainFromWebsite(tenantVendor.website);
    if (websiteDomain && websiteDomain.toLowerCase() === globalVendor.domain.toLowerCase()) {
      return { isMatch: true, domain: websiteDomain };
    }
    
    return { isMatch: false };
  }
  
  /**
   * Extract domain from email address
   */
  private static extractDomainFromEmail(email?: string): string | null {
    if (!email) return null;
    const match = email.match(/@([^@]+)$/);
    return match ? match[1] : null;
  }
  
  /**
   * Extract domain from website URL
   */
  private static extractDomainFromWebsite(website?: string): string | null {
    if (!website) return null;
    try {
      const url = new URL(website.startsWith('http') ? website : `https://${website}`);
      return url.hostname.replace(/^www\./, '');
    } catch {
      return null;
    }
  }
  
  /**
   * Calculate name similarity using Levenshtein distance
   */
  private static calculateNameSimilarity(name1: string, name2: string): number {
    const normalized1 = this.normalizeName(name1);
    const normalized2 = this.normalizeName(name2);
    
    if (normalized1 === normalized2) return 1.0;
    
    const distance = this.levenshteinDistance(normalized1, normalized2);
    const maxLength = Math.max(normalized1.length, normalized2.length);
    
    return maxLength > 0 ? 1 - (distance / maxLength) : 0;
  }
  
  /**
   * Normalize name for comparison
   */
  private static normalizeName(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^\w\s]/g, '') // Remove punctuation
      .replace(/\b(inc|corp|corporation|ltd|limited|llc|co|company)\b/g, '') // Remove common suffixes
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }
  
  /**
   * Calculate Levenshtein distance between two strings
   */
  private static levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1, // deletion
          matrix[j - 1][i] + 1, // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }
  
  /**
   * Calculate address similarity
   */
  private static calculateAddressSimilarity(tenantVendor: TenantVendor, globalVendor: GlobalVendor): number {
    // For now, return a basic score based on country match
    // In a real implementation, this would normalize and compare full addresses
    if (tenantVendor.country && globalVendor.country_code) {
      const countryMatch = this.normalizeCountry(tenantVendor.country) === globalVendor.country_code.toLowerCase();
      return countryMatch ? 0.7 : 0.3;
    }
    return 0.5; // Default score when address data is incomplete
  }

  /**
   * Normalize country name to ISO code
   */
  private static normalizeCountry(country: string): string {
    const countryMap: { [key: string]: string } = {
      'united states': 'us',
      'usa': 'us',
      'canada': 'ca',
      'united kingdom': 'gb',
      'uk': 'gb'
    };

    return countryMap[country.toLowerCase()] || country.toLowerCase().substring(0, 2);
  }

  /**
   * Calculate phone similarity
   */
  private static calculatePhoneSimilarity(phone1: string, phone2: string): number {
    const normalized1 = this.normalizePhone(phone1);
    const normalized2 = this.normalizePhone(phone2);

    if (normalized1 === normalized2) return 1.0;

    // Check for partial matches (last 7 digits for US numbers)
    if (normalized1.length >= 7 && normalized2.length >= 7) {
      const suffix1 = normalized1.slice(-7);
      const suffix2 = normalized2.slice(-7);
      if (suffix1 === suffix2) return 0.8;
    }

    return 0;
  }

  /**
   * Normalize phone number to digits only
   */
  private static normalizePhone(phone: string): string {
    return phone.replace(/\D/g, '');
  }

  /**
   * Test the vendor matching algorithm with sample data
   */
  static async testMatching(): Promise<any> {
    // Create a test tenant vendor
    const testTenantVendor: TenantVendor = {
      id: 'test-tenant-vendor-1',
      name: 'Microsoft Corp',
      display_name: 'Microsoft Corporation',
      contact_email: '<EMAIL>',
      phone: '******-882-8080',
      address_line1: '1 Microsoft Way',
      city: 'Redmond',
      state: 'WA',
      country: 'United States',
      tax_id: '91-1144442',
      website: 'https://www.microsoft.com'
    };

    return await this.findMatches(testTenantVendor);
  }
}
