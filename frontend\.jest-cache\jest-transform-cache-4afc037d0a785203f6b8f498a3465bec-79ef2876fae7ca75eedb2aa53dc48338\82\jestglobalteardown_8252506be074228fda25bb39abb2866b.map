{"version": 3, "names": ["_default", "console", "log", "process", "env", "NODE_ENV", "TZ", "AWS_REGION", "AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY", "DATABASE_URL", "JWT_SECRET", "exports", "default"], "sources": ["jest.global-teardown.js"], "sourcesContent": ["/**\n * Jest Global Teardown\n * \n * Runs once after all tests complete.\n * Used for global test environment cleanup.\n */\n\nexport default async () => {\n  console.log('🧹 Cleaning up test environment...')\n  \n  // Clean up any global resources\n  // This could include closing database connections,\n  // cleaning up temporary files, etc.\n  \n  // Reset environment variables\n  delete process.env.NODE_ENV\n  delete process.env.TZ\n  delete process.env.AWS_REGION\n  delete process.env.AWS_ACCESS_KEY_ID\n  delete process.env.AWS_SECRET_ACCESS_KEY\n  delete process.env.DATABASE_URL\n  delete process.env.JWT_SECRET\n  \n  console.log('✅ Test environment cleanup complete')\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AALA,IAAAA,QAAA,GAOe,MAAAA,CAAA,KAAY;EACzBC,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;;EAEjD;EACA;EACA;;EAEA;EACA,OAAOC,OAAO,CAACC,GAAG,CAACC,QAAQ;EAC3B,OAAOF,OAAO,CAACC,GAAG,CAACE,EAAE;EACrB,OAAOH,OAAO,CAACC,GAAG,CAACG,UAAU;EAC7B,OAAOJ,OAAO,CAACC,GAAG,CAACI,iBAAiB;EACpC,OAAOL,OAAO,CAACC,GAAG,CAACK,qBAAqB;EACxC,OAAON,OAAO,CAACC,GAAG,CAACM,YAAY;EAC/B,OAAOP,OAAO,CAACC,GAAG,CAACO,UAAU;EAE7BV,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;AACpD,CAAC;AAAAU,OAAA,CAAAC,OAAA,GAAAb,QAAA", "ignoreList": []}