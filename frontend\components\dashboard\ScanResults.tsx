/**
 * Scan Results Component
 * 
 * Displays latest scan results with proper empty and loading states.
 * Focused responsibility: Rendering scan results section only.
 */

'use client'

import { BaseComponentProps } from '@/lib/types'

interface ScanResult {
  id: string
  type: 'software' | 'license' | 'renewal'
  name: string
  status: 'found' | 'missing' | 'expired' | 'warning'
  lastSeen: Date
  details?: string
}

interface ScanResultsProps extends BaseComponentProps {
  results: ScanResult[]
  isLoading?: boolean
  onRunScan?: () => void
  onResultClick?: (result: ScanResult) => void
  lastScanDate?: Date
}

interface ScanResultItemProps {
  result: ScanResult
  onClick?: (result: ScanResult) => void
}

function ScanResultItem({ result, onClick }: ScanResultItemProps) {
  const handleClick = () => {
    onClick?.(result)
  }

  const getStatusColor = (status: ScanResult['status']) => {
    switch (status) {
      case 'found':
        return 'text-green-600 bg-green-50'
      case 'missing':
        return 'text-red-600 bg-red-50'
      case 'expired':
        return 'text-red-600 bg-red-50'
      case 'warning':
        return 'text-yellow-600 bg-yellow-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  const getStatusIcon = (status: ScanResult['status']) => {
    switch (status) {
      case 'found':
        return '✅'
      case 'missing':
        return '❌'
      case 'expired':
        return '⏰'
      case 'warning':
        return '⚠️'
      default:
        return '❓'
    }
  }

  return (
    <div 
      className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
      onClick={handleClick}
      role="button"
      tabIndex={0}
      aria-label={`View details for ${result.name}`}
    >
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
          <span className="text-blue-600 text-sm">{getStatusIcon(result.status)}</span>
        </div>
        <div>
          <p className="font-medium text-sm">{result.name}</p>
          <p className="text-xs text-secondary capitalize">{result.type}</p>
          {result.details && (
            <p className="text-xs text-secondary">{result.details}</p>
          )}
        </div>
      </div>
      <div className="text-right">
        <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getStatusColor(result.status)}`}>
          {result.status}
        </span>
        <p className="text-xs text-secondary mt-1">
          {result.lastSeen.toLocaleDateString()}
        </p>
      </div>
    </div>
  )
}

function LoadingSkeleton() {
  return (
    <div className="space-y-3">
      {[...Array(4)].map((_, index) => (
        <div key={index} className="flex items-center justify-between p-3 border rounded-lg animate-pulse">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gray-200 rounded"></div>
            <div>
              <div className="h-4 bg-gray-200 rounded w-32 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-20 mb-1"></div>
              <div className="h-3 bg-gray-200 rounded w-24"></div>
            </div>
          </div>
          <div className="text-right">
            <div className="h-6 bg-gray-200 rounded-full w-16 mb-1"></div>
            <div className="h-3 bg-gray-200 rounded w-12"></div>
          </div>
        </div>
      ))}
    </div>
  )
}

function EmptyState({ onRunScan }: { onRunScan?: () => void }) {
  return (
    <div className="text-center py-8">
      <div className="text-4xl mb-4">🔍</div>
      <h3 className="text-lg font-medium mb-2">No scan results available</h3>
      <p className="text-secondary mb-4">Run your first scan to discover software and renewals</p>
      <button 
        className="btn btn-primary btn-sm"
        onClick={onRunScan}
        aria-label="Run network scan"
      >
        Run Scan
      </button>
    </div>
  )
}

function ScanHeader({ lastScanDate, onRunScan }: { lastScanDate?: Date; onRunScan?: () => void }) {
  return (
    <div className="flex items-center justify-between mb-4">
      <div>
        <h2 className="text-lg font-semibold">Latest Scan Results</h2>
        {lastScanDate && (
          <p className="text-xs text-secondary">
            Last scan: {lastScanDate.toLocaleDateString()} at {lastScanDate.toLocaleTimeString()}
          </p>
        )}
      </div>
      <button 
        className="btn btn-secondary btn-sm"
        onClick={onRunScan}
        aria-label="Run new scan"
      >
        🔄 Refresh
      </button>
    </div>
  )
}

export default function ScanResults({
  results,
  isLoading = false,
  onRunScan,
  onResultClick,
  lastScanDate,
  className = '',
  'data-testid': testId
}: ScanResultsProps) {
  return (
    <div 
      className={`card ${className}`}
      data-testid={testId}
    >
      <div className="card-header">
        <ScanHeader lastScanDate={lastScanDate} onRunScan={onRunScan} />
      </div>
      <div className="card-content">
        {isLoading ? (
          <LoadingSkeleton />
        ) : results.length > 0 ? (
          <div className="space-y-3">
            {results.map((result) => (
              <ScanResultItem
                key={result.id}
                result={result}
                onClick={onResultClick}
              />
            ))}
          </div>
        ) : (
          <EmptyState onRunScan={onRunScan} />
        )}
      </div>
    </div>
  )
}
