
import { Inter } from 'next/font/google'
import '../styles/globals.css'
import { configureAmplify } from '../lib/amplify-config'
import { Providers } from '@/components/providers'
import PerformanceMonitor from '@/components/common/PerformanceMonitor'

// Configure Amplify at the application root
configureAmplify()

// Optimize font loading with performance settings
const inter = Inter({
  subsets: ['latin'],
  display: 'swap', // Improve font loading performance
  preload: true,
})

export const metadata = {
  title: 'RenewTrack',
  description: 'Track and manage your renewals',
  // Performance and SEO optimizations
  keywords: 'renewals, software, tracking, management',
  authors: [{ name: 'RenewTrack Team' }],
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#000000',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        {/* DNS prefetch for performance */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* Preload critical resources */}
        <link rel="preload" href="/api/clients/domain" as="fetch" crossOrigin="anonymous" />
      </head>
      <body className={inter.className}>
        <Providers>
          {children}
          {/* Performance monitor for development */}
          <PerformanceMonitor />
        </Providers>
      </body>
    </html>
  )
}

