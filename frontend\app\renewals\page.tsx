/**
 * Renewals Inventory Page
 * 
 * Main page for managing renewals inventory with table view, filtering, and actions
 */

'use client'

import { useState, useCallback, useMemo, useEffect } from 'react'
import { useTenant } from '@/contexts/AppContext'
import { usePerformanceMonitor } from '@/lib/performance'

// Components
import RenewalsHeader from '@/components/renewals/RenewalsHeader'
import RenewalsTable from '@/components/renewals/RenewalsTable'
import RenewalsFilters from '@/components/renewals/RenewalsFilters'
import AddRenewalModal, { RenewalFormData, AlertFormData } from '@/components/modals/AddRenewalModal'

// Types
interface Renewal {
  id: string
  name: string
  product: string
  vendor: string
  type: string
  renewalDate: string
  cost: number
  currency: string
  status: string
  alerts: number
}

interface FilterOptions {
  vendors: string[]
  types: string[]
  statuses: string[]
}

interface RenewalsFilters {
  search: string
  vendor: string
  type: string
  status: string
}

export default function RenewalsPage() {
  // Performance monitoring
  usePerformanceMonitor('RenewalsPage')

  // Context
  const { tenant } = useTenant()

  // State
  const [renewals, setRenewals] = useState<Renewal[]>([])
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    vendors: [],
    types: [],
    statuses: []
  })
  const [isLoading, setIsLoading] = useState(true)
  const [filters, setFilters] = useState<RenewalsFilters>({
    search: '',
    vendor: '',
    type: '',
    status: ''
  })
  const [isAddRenewalModalOpen, setIsAddRenewalModalOpen] = useState(false)

  // Fetch renewals data
  const fetchRenewals = useCallback(async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/renewals')

      if (!response.ok) {
        throw new Error('Failed to fetch renewals')
      }

      const result = await response.json()

      if (result.success) {
        // Map API response to component interface
        const mappedRenewals: Renewal[] = result.data.map((renewal: any) => ({
          id: renewal.id,
          name: renewal.name,
          product: renewal.productName,
          vendor: renewal.vendor,
          type: renewal.type,
          renewalDate: renewal.renewalDate,
          cost: renewal.cost,
          currency: renewal.currency,
          status: renewal.status,
          alerts: renewal.alerts
        }))

        setRenewals(mappedRenewals)
      } else {
        console.error('Failed to fetch renewals:', result.message)
        setRenewals([])
      }
    } catch (error) {
      console.error('Error fetching renewals:', error)
      setRenewals([])
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Fetch filter options
  const fetchFilterOptions = useCallback(async () => {
    try {
      const response = await fetch('/api/renewals', { method: 'POST' })

      if (!response.ok) {
        throw new Error('Failed to fetch filter options')
      }

      const result = await response.json()

      if (result.success) {
        setFilterOptions(result.data)
      }
    } catch (error) {
      console.error('Error fetching filter options:', error)
    }
  }, [])

  // Load data on mount
  useEffect(() => {
    fetchRenewals()
    fetchFilterOptions()
  }, [])

  // Filter renewals based on current filters
  const filteredRenewals = useMemo(() => {
    return renewals.filter(renewal => {
      const matchesSearch = !filters.search ||
        renewal.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        renewal.product.toLowerCase().includes(filters.search.toLowerCase()) ||
        renewal.vendor.toLowerCase().includes(filters.search.toLowerCase())

      const matchesVendor = !filters.vendor || filters.vendor === 'All Vendors' || renewal.vendor === filters.vendor
      const matchesType = !filters.type || filters.type === 'All Renewal Types' || renewal.type === filters.type
      const matchesStatus = !filters.status || filters.status === 'All Statuses' || renewal.status === filters.status

      return matchesSearch && matchesVendor && matchesType && matchesStatus
    })
  }, [renewals, filters])

  // Event handlers
  const handleSearch = useCallback((query: string) => {
    setFilters(prev => ({ ...prev, search: query }))
  }, [])

  const handleFilterChange = useCallback((filterType: keyof RenewalsFilters, value: string) => {
    setFilters(prev => ({ ...prev, [filterType]: value }))
  }, [])

  const handleAddRenewal = useCallback(() => {
    setIsAddRenewalModalOpen(true)
  }, [])

  const handleCloseAddRenewalModal = useCallback(() => {
    setIsAddRenewalModalOpen(false)
  }, [])

  const handleSubmitRenewal = useCallback(async (renewalData: RenewalFormData, alertData: AlertFormData[]) => {
    try {
      console.log('Submitting renewal:', renewalData, alertData)
      // TODO: Implement actual API call
      setIsAddRenewalModalOpen(false)
    } catch (error) {
      console.error('Failed to submit renewal:', error)
    }
  }, [])

  const handleRenewalAction = useCallback((renewalId: string, action: string) => {
    console.log('Renewal action:', action, 'for renewal:', renewalId)
    // TODO: Implement renewal actions
  }, [])

  const handleImportCSV = useCallback(() => {
    console.log('Import CSV clicked')
    // TODO: Implement CSV import
  }, [])

  return (
    <div className="renewals-page">
      {/* Header */}
      <RenewalsHeader
        onSearch={handleSearch}
        onAddRenewal={handleAddRenewal}
        onImportCSV={handleImportCSV}
        searchPlaceholder="Search renewals..."
      />

      {/* Filters */}
      <RenewalsFilters
        filters={filters}
        onFilterChange={handleFilterChange}
        renewals={renewals}
        filterOptions={filterOptions}
      />

      {/* Table */}
      <RenewalsTable
        renewals={filteredRenewals}
        isLoading={isLoading}
        onRenewalAction={handleRenewalAction}
      />

      {/* Add Renewal Modal */}
      <AddRenewalModal
        isOpen={isAddRenewalModalOpen}
        onClose={handleCloseAddRenewalModal}
        onSubmit={handleSubmitRenewal}
      />
    </div>
  )
}
