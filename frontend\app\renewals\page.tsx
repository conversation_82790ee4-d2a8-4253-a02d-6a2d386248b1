/**
 * Renewals Inventory Page
 * 
 * Main page for managing renewals inventory with table view, filtering, and actions
 */

'use client'

import { useState, useCallback, useMemo } from 'react'
import { useTenant } from '@/contexts/AppContext'
import { usePerformanceMonitor } from '@/lib/performance'

// Components
import RenewalsHeader from '@/components/renewals/RenewalsHeader'
import RenewalsTable from '@/components/renewals/RenewalsTable'
import RenewalsFilters from '@/components/renewals/RenewalsFilters'
import AddRenewalModal, { RenewalFormData, AlertFormData } from '@/components/modals/AddRenewalModal'

// Types
interface Renewal {
  id: string
  name: string
  product: string
  vendor: string
  type: 'Subscription' | 'Warranty' | 'Support' | 'License'
  renewalDate: string
  cost: number
  currency: string
  status: 'Active' | 'Expired' | 'Expiring'
  alerts: number
}

interface RenewalsFilters {
  search: string
  vendor: string
  type: string
  status: string
}

export default function RenewalsPage() {
  // Performance monitoring
  usePerformanceMonitor('RenewalsPage')

  // Context
  const { tenant } = useTenant()

  // State
  const [renewals, setRenewals] = useState<Renewal[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [filters, setFilters] = useState<RenewalsFilters>({
    search: '',
    vendor: '',
    type: '',
    status: ''
  })
  const [isAddRenewalModalOpen, setIsAddRenewalModalOpen] = useState(false)

  // Mock data for demonstration
  const mockRenewals: Renewal[] = useMemo(() => [
    {
      id: '1',
      name: 'Alert Test',
      product: 'test',
      vendor: 'test',
      type: 'Subscription',
      renewalDate: '2024-05-09',
      cost: 100000,
      currency: 'USD',
      status: 'Active',
      alerts: 2
    },
    {
      id: '2',
      name: 'ASA400',
      product: 'vGat',
      vendor: 'Cisco',
      type: 'Warranty',
      renewalDate: '2024-05-04',
      cost: 50000,
      currency: 'CAD',
      status: 'Active',
      alerts: 0
    },
    {
      id: '3',
      name: 'Case Test',
      product: 'v1.0',
      vendor: 'Case Vendor',
      type: 'Subscription',
      renewalDate: '2025-05-06',
      cost: 0,
      currency: 'USD',
      status: 'Expired',
      alerts: 0
    },
    {
      id: '4',
      name: 'Cloud Suite',
      product: 'vent',
      vendor: 'Adobe',
      type: 'Subscription',
      renewalDate: '2024-05-04',
      cost: 10000,
      currency: 'USD',
      status: 'Active',
      alerts: 1
    },
    {
      id: '5',
      name: 'Diskover',
      product: 'v1',
      vendor: 'Diskover',
      type: 'Subscription',
      renewalDate: '2024-05-01',
      cost: 50000,
      currency: 'USD',
      status: 'Active',
      alerts: 2
    },
    {
      id: '6',
      name: 'DL360',
      product: 'vG10',
      vendor: 'HPE',
      type: 'Warranty',
      renewalDate: '2024-05-05',
      cost: 20000,
      currency: 'CAD',
      status: 'Active',
      alerts: 0
    },
    {
      id: '7',
      name: 'M365',
      product: 'v5',
      vendor: 'Microsoft',
      type: 'Subscription',
      renewalDate: '2025-05-07',
      cost: 200000,
      currency: 'USD',
      status: 'Expired',
      alerts: 1
    },
    {
      id: '8',
      name: 'M365',
      product: 'vF1',
      vendor: 'Microsoft',
      type: 'Subscription',
      renewalDate: '2024-05-04',
      cost: 150000,
      currency: 'CAD',
      status: 'Active',
      alerts: 1
    }
  ], [])

  // Simulate loading
  useState(() => {
    const timer = setTimeout(() => {
      setRenewals(mockRenewals)
      setIsLoading(false)
    }, 500)
    return () => clearTimeout(timer)
  })

  // Filter renewals based on current filters
  const filteredRenewals = useMemo(() => {
    return renewals.filter(renewal => {
      const matchesSearch = !filters.search || 
        renewal.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        renewal.product.toLowerCase().includes(filters.search.toLowerCase()) ||
        renewal.vendor.toLowerCase().includes(filters.search.toLowerCase())
      
      const matchesVendor = !filters.vendor || renewal.vendor === filters.vendor
      const matchesType = !filters.type || renewal.type === filters.type
      const matchesStatus = !filters.status || renewal.status === filters.status

      return matchesSearch && matchesVendor && matchesType && matchesStatus
    })
  }, [renewals, filters])

  // Event handlers
  const handleSearch = useCallback((query: string) => {
    setFilters(prev => ({ ...prev, search: query }))
  }, [])

  const handleFilterChange = useCallback((filterType: keyof RenewalsFilters, value: string) => {
    setFilters(prev => ({ ...prev, [filterType]: value }))
  }, [])

  const handleAddRenewal = useCallback(() => {
    setIsAddRenewalModalOpen(true)
  }, [])

  const handleCloseAddRenewalModal = useCallback(() => {
    setIsAddRenewalModalOpen(false)
  }, [])

  const handleSubmitRenewal = useCallback(async (renewalData: RenewalFormData, alertData: AlertFormData[]) => {
    try {
      console.log('Submitting renewal:', renewalData, alertData)
      // TODO: Implement actual API call
      setIsAddRenewalModalOpen(false)
    } catch (error) {
      console.error('Failed to submit renewal:', error)
    }
  }, [])

  const handleRenewalAction = useCallback((renewalId: string, action: string) => {
    console.log('Renewal action:', action, 'for renewal:', renewalId)
    // TODO: Implement renewal actions
  }, [])

  const handleImportCSV = useCallback(() => {
    console.log('Import CSV clicked')
    // TODO: Implement CSV import
  }, [])

  return (
    <div className="renewals-page">
      {/* Header */}
      <RenewalsHeader
        onSearch={handleSearch}
        onAddRenewal={handleAddRenewal}
        onImportCSV={handleImportCSV}
        searchPlaceholder="Search renewals..."
      />

      {/* Filters */}
      <RenewalsFilters
        filters={filters}
        onFilterChange={handleFilterChange}
        renewals={renewals}
      />

      {/* Table */}
      <RenewalsTable
        renewals={filteredRenewals}
        isLoading={isLoading}
        onRenewalAction={handleRenewalAction}
      />

      {/* Add Renewal Modal */}
      <AddRenewalModal
        isOpen={isAddRenewalModalOpen}
        onClose={handleCloseAddRenewalModal}
        onSubmit={handleSubmitRenewal}
      />
    </div>
  )
}
