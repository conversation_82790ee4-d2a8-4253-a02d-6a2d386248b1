127396f2ba24e8c12aca3a7a906882cd
/* istanbul ignore next */
function cov_17fikhsrsv() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\dashboard\\renewals\\route.ts";
  var hash = "a856f0958daeae59cc7d36ac08baebcf8619cf25";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\dashboard\\renewals\\route.ts",
    statementMap: {
      "0": {
        start: {
          line: 22,
          column: 19
        },
        end: {
          line: 97,
          column: 2
        }
      },
      "1": {
        start: {
          line: 24,
          column: 21
        },
        end: {
          line: 24,
          column: 40
        }
      },
      "2": {
        start: {
          line: 25,
          column: 2
        },
        end: {
          line: 27,
          column: 3
        }
      },
      "3": {
        start: {
          line: 26,
          column: 4
        },
        end: {
          line: 26,
          column: 32
        }
      },
      "4": {
        start: {
          line: 29,
          column: 18
        },
        end: {
          line: 29,
          column: 37
        }
      },
      "5": {
        start: {
          line: 32,
          column: 23
        },
        end: {
          line: 32,
          column: 66
        }
      },
      "6": {
        start: {
          line: 34,
          column: 2
        },
        end: {
          line: 43,
          column: 3
        }
      },
      "7": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 119
        }
      },
      "8": {
        start: {
          line: 36,
          column: 25
        },
        end: {
          line: 36,
          column: 118
        }
      },
      "9": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 42,
          column: 6
        }
      },
      "10": {
        start: {
          line: 45,
          column: 17
        },
        end: {
          line: 45,
          column: 37
        }
      },
      "11": {
        start: {
          line: 48,
          column: 22
        },
        end: {
          line: 48,
          column: 61
        }
      },
      "12": {
        start: {
          line: 50,
          column: 28
        },
        end: {
          line: 50,
          column: 30
        }
      },
      "13": {
        start: {
          line: 52,
          column: 2
        },
        end: {
          line: 83,
          column: 3
        }
      },
      "14": {
        start: {
          line: 54,
          column: 26
        },
        end: {
          line: 66,
          column: 5
        }
      },
      "15": {
        start: {
          line: 68,
          column: 19
        },
        end: {
          line: 68,
          column: 89
        }
      },
      "16": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 80,
          column: 5
        }
      },
      "17": {
        start: {
          line: 72,
          column: 6
        },
        end: {
          line: 79,
          column: 10
        }
      },
      "18": {
        start: {
          line: 72,
          column: 48
        },
        end: {
          line: 79,
          column: 7
        }
      },
      "19": {
        start: {
          line: 82,
          column: 4
        },
        end: {
          line: 82,
          column: 87
        }
      },
      "20": {
        start: {
          line: 86,
          column: 2
        },
        end: {
          line: 94,
          column: 3
        }
      },
      "21": {
        start: {
          line: 87,
          column: 4
        },
        end: {
          line: 93,
          column: 6
        }
      },
      "22": {
        start: {
          line: 96,
          column: 2
        },
        end: {
          line: 96,
          column: 76
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 22,
            column: 37
          },
          end: {
            line: 22,
            column: 38
          }
        },
        loc: {
          start: {
            line: 22,
            column: 49
          },
          end: {
            line: 97,
            column: 1
          }
        },
        line: 22
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 72,
            column: 33
          },
          end: {
            line: 72,
            column: 34
          }
        },
        loc: {
          start: {
            line: 72,
            column: 48
          },
          end: {
            line: 79,
            column: 7
          }
        },
        line: 72
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 25,
            column: 2
          },
          end: {
            line: 27,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 25,
            column: 2
          },
          end: {
            line: 27,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 25
      },
      "1": {
        loc: {
          start: {
            line: 34,
            column: 2
          },
          end: {
            line: 43,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 2
          },
          end: {
            line: 43,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "2": {
        loc: {
          start: {
            line: 35,
            column: 23
          },
          end: {
            line: 35,
            column: 119
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 64
          },
          end: {
            line: 35,
            column: 84
          }
        }, {
          start: {
            line: 35,
            column: 87
          },
          end: {
            line: 35,
            column: 119
          }
        }],
        line: 35
      },
      "3": {
        loc: {
          start: {
            line: 36,
            column: 25
          },
          end: {
            line: 36,
            column: 118
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 36,
            column: 66
          },
          end: {
            line: 36,
            column: 88
          }
        }, {
          start: {
            line: 36,
            column: 91
          },
          end: {
            line: 36,
            column: 118
          }
        }],
        line: 36
      },
      "4": {
        loc: {
          start: {
            line: 39,
            column: 6
          },
          end: {
            line: 39,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 6
          },
          end: {
            line: 39,
            column: 24
          }
        }, {
          start: {
            line: 39,
            column: 28
          },
          end: {
            line: 39,
            column: 64
          }
        }],
        line: 39
      },
      "5": {
        loc: {
          start: {
            line: 52,
            column: 2
          },
          end: {
            line: 83,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 52,
            column: 2
          },
          end: {
            line: 83,
            column: 3
          }
        }, {
          start: {
            line: 81,
            column: 9
          },
          end: {
            line: 83,
            column: 3
          }
        }],
        line: 52
      },
      "6": {
        loc: {
          start: {
            line: 70,
            column: 4
          },
          end: {
            line: 80,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 70,
            column: 4
          },
          end: {
            line: 80,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 70
      },
      "7": {
        loc: {
          start: {
            line: 70,
            column: 8
          },
          end: {
            line: 70,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 70,
            column: 8
          },
          end: {
            line: 70,
            column: 22
          }
        }, {
          start: {
            line: 70,
            column: 26
          },
          end: {
            line: 70,
            column: 37
          }
        }],
        line: 70
      },
      "8": {
        loc: {
          start: {
            line: 77,
            column: 17
          },
          end: {
            line: 77,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 77,
            column: 32
          },
          end: {
            line: 77,
            column: 75
          }
        }, {
          start: {
            line: 77,
            column: 78
          },
          end: {
            line: 77,
            column: 87
          }
        }],
        line: 77
      },
      "9": {
        loc: {
          start: {
            line: 78,
            column: 19
          },
          end: {
            line: 78,
            column: 93
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 78,
            column: 36
          },
          end: {
            line: 78,
            column: 81
          }
        }, {
          start: {
            line: 78,
            column: 84
          },
          end: {
            line: 78,
            column: 93
          }
        }],
        line: 78
      },
      "10": {
        loc: {
          start: {
            line: 86,
            column: 2
          },
          end: {
            line: 94,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 86,
            column: 2
          },
          end: {
            line: 94,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 86
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a856f0958daeae59cc7d36ac08baebcf8619cf25"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_17fikhsrsv = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_17fikhsrsv();
import { getClientByEmailDomain } from '@/lib/clients';
import { executeQuery, schemaExists } from '@/lib/database';
import { requireAuth } from '@/lib/auth-middleware';
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus, withErrorHandling } from '@/lib/api-response';

// Renewal interface

export const GET =
/* istanbul ignore next */
(cov_17fikhsrsv().s[0]++, withErrorHandling(async () => {
  /* istanbul ignore next */
  cov_17fikhsrsv().f[0]++;
  // Verify authentication
  const authResult =
  /* istanbul ignore next */
  (cov_17fikhsrsv().s[1]++, await requireAuth());
  /* istanbul ignore next */
  cov_17fikhsrsv().s[2]++;
  if (!authResult.success) {
    /* istanbul ignore next */
    cov_17fikhsrsv().b[0][0]++;
    cov_17fikhsrsv().s[3]++;
    return authResult.response;
  } else
  /* istanbul ignore next */
  {
    cov_17fikhsrsv().b[0][1]++;
  }
  const session =
  /* istanbul ignore next */
  (cov_17fikhsrsv().s[4]++, authResult.session);

  // Get tenant context using the new secure method
  const clientResult =
  /* istanbul ignore next */
  (cov_17fikhsrsv().s[5]++, await getClientByEmailDomain(session.email));
  /* istanbul ignore next */
  cov_17fikhsrsv().s[6]++;
  if (!clientResult.success) {
    /* istanbul ignore next */
    cov_17fikhsrsv().b[1][0]++;
    const statusCode =
    /* istanbul ignore next */
    (cov_17fikhsrsv().s[7]++, clientResult.errorCode === 'NOT_FOUND' ?
    /* istanbul ignore next */
    (cov_17fikhsrsv().b[2][0]++, HttpStatus.NOT_FOUND) :
    /* istanbul ignore next */
    (cov_17fikhsrsv().b[2][1]++, HttpStatus.INTERNAL_SERVER_ERROR));
    const apiErrorCode =
    /* istanbul ignore next */
    (cov_17fikhsrsv().s[8]++, clientResult.errorCode === 'NOT_FOUND' ?
    /* istanbul ignore next */
    (cov_17fikhsrsv().b[3][0]++, ApiErrorCode.NOT_FOUND) :
    /* istanbul ignore next */
    (cov_17fikhsrsv().b[3][1]++, ApiErrorCode.DATABASE_ERROR));
    /* istanbul ignore next */
    cov_17fikhsrsv().s[9]++;
    return createErrorResponse(
    /* istanbul ignore next */
    (cov_17fikhsrsv().b[4][0]++, clientResult.error) ||
    /* istanbul ignore next */
    (cov_17fikhsrsv().b[4][1]++, 'Failed to fetch tenant information'), apiErrorCode, statusCode);
  } else
  /* istanbul ignore next */
  {
    cov_17fikhsrsv().b[1][1]++;
  }
  const tenant =
  /* istanbul ignore next */
  (cov_17fikhsrsv().s[10]++, clientResult.client);

  // Check if tenant schema exists
  const schemaReady =
  /* istanbul ignore next */
  (cov_17fikhsrsv().s[11]++, await schemaExists(tenant.tenantSchema));
  let renewals =
  /* istanbul ignore next */
  (cov_17fikhsrsv().s[12]++, []);
  /* istanbul ignore next */
  cov_17fikhsrsv().s[13]++;
  if (schemaReady) {
    /* istanbul ignore next */
    cov_17fikhsrsv().b[5][0]++;
    // Query tenant schema for actual renewal data
    const renewalsQuery =
    /* istanbul ignore next */
    (cov_17fikhsrsv().s[14]++, `
      SELECT
        "RenewalID" as id,
        "RenewalName" as name,
        "VendorName" as vendor,
        "Status" as status,
        "DueDate" as due_date,
        "CreatedOn" as created_at
      FROM "${tenant.tenantSchema}"."Renewals"
      WHERE "Active" = true
      ORDER BY "CreatedOn" DESC
      LIMIT 10
    `);
    const result =
    /* istanbul ignore next */
    (cov_17fikhsrsv().s[15]++, await executeQuery(renewalsQuery, [], {
      schema: tenant.tenantSchema
    }));
    /* istanbul ignore next */
    cov_17fikhsrsv().s[16]++;
    if (
    /* istanbul ignore next */
    (cov_17fikhsrsv().b[7][0]++, result.success) &&
    /* istanbul ignore next */
    (cov_17fikhsrsv().b[7][1]++, result.data)) {
      /* istanbul ignore next */
      cov_17fikhsrsv().b[6][0]++;
      cov_17fikhsrsv().s[17]++;
      // Format the results
      renewals = result.data.map(row => {
        /* istanbul ignore next */
        cov_17fikhsrsv().f[1]++;
        cov_17fikhsrsv().s[18]++;
        return {
          id: row.id.toString(),
          name: row.name,
          vendor: row.vendor,
          status: row.status,
          dueDate: row.due_date ?
          /* istanbul ignore next */
          (cov_17fikhsrsv().b[8][0]++, new Date(row.due_date).toLocaleDateString()) :
          /* istanbul ignore next */
          (cov_17fikhsrsv().b[8][1]++, 'Not set'),
          addedDate: row.created_at ?
          /* istanbul ignore next */
          (cov_17fikhsrsv().b[9][0]++, new Date(row.created_at).toLocaleDateString()) :
          /* istanbul ignore next */
          (cov_17fikhsrsv().b[9][1]++, 'Unknown')
        };
      });
    } else
    /* istanbul ignore next */
    {
      cov_17fikhsrsv().b[6][1]++;
    }
  } else {
    /* istanbul ignore next */
    cov_17fikhsrsv().b[5][1]++;
    cov_17fikhsrsv().s[19]++;
    console.log(`Tenant schema ${tenant.tenantSchema} not ready yet, using mock data`);
  }

  // Use mock data if no real data available
  /* istanbul ignore next */
  cov_17fikhsrsv().s[20]++;
  if (renewals.length === 0) {
    /* istanbul ignore next */
    cov_17fikhsrsv().b[10][0]++;
    cov_17fikhsrsv().s[21]++;
    renewals = [{
      id: '1',
      name: 'Unlimited Renewal',
      vendor: 'Vox Audio',
      status: 'Not available',
      dueDate: 'Run Now 2025',
      addedDate: '2025-01-01'
    }, {
      id: '2',
      name: 'Core Pro',
      vendor: 'Vox Audio',
      status: 'Not available',
      dueDate: 'Run Now 2025',
      addedDate: '2025-01-02'
    }, {
      id: '3',
      name: 'Test Software',
      vendor: 'Vox Tools',
      status: 'Not available',
      dueDate: 'Run Now 2025',
      addedDate: '2025-01-03'
    }, {
      id: '4',
      name: 'Mobile app',
      vendor: 'TopCar',
      status: 'Not available',
      dueDate: 'Run Now 2025',
      addedDate: '2025-01-04'
    }, {
      id: '5',
      name: 'ASAUDB',
      vendor: 'Cubix',
      status: 'Not available',
      dueDate: 'Run Now 2025',
      addedDate: '2025-01-05'
    }];
  } else
  /* istanbul ignore next */
  {
    cov_17fikhsrsv().b[10][1]++;
  }
  cov_17fikhsrsv().s[22]++;
  return createSuccessResponse(renewals, 'Renewals retrieved successfully');
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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