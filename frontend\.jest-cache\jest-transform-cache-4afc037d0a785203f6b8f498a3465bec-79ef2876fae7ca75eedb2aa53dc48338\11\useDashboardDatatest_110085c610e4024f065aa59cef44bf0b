7bb83642af589bd58c7a6afe5bf203d0
"use strict";

_getJestObj().mock('@/contexts/AppContext', () => ({
  useTenant: () => mockUseTenant()
}));

// Mock the cache

_getJestObj().mock('@/lib/cache', () => ({
  apiCache: mockApiCache,
  cacheUtils: {
    createKey: _globals.jest.fn((prefix, id) => `${prefix}:${id}`)
  }
}));

// Mock performance utils
_getJestObj().mock('@/lib/performance', () => ({
  useDebounce: _globals.jest.fn(value => value),
  performanceUtils: {
    mark: _globals.jest.fn(),
    measure: _globals.jest.fn()
  }
}));
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _react = require("@testing-library/react");
var _globals = require("@jest/globals");
var _useDashboardData = require("@/hooks/useDashboardData");
var _testUtils = require("../utils/test-utils");
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Dashboard Data Hook Tests
 * 
 * Tests for the useDashboardData hook including data fetching,
 * caching, error handling, and performance optimizations
 */
// Mock the tenant context
const mockUseTenant = _globals.jest.fn();
const mockApiCache = {
  get: _globals.jest.fn(),
  set: _globals.jest.fn(),
  has: _globals.jest.fn(),
  delete: _globals.jest.fn(),
  clear: _globals.jest.fn()
};
describe('useDashboardData Hook', () => {
  const mockTenant = _testUtils.testUtils.generateTestData.tenant();
  const mockStats = _testUtils.testUtils.generateTestData.dashboardStats();
  const mockRenewals = [_testUtils.testUtils.generateTestData.renewal()];
  beforeEach(() => {
    _globals.jest.clearAllMocks();

    // Reset cache mocks
    mockApiCache.get.mockReturnValue(null);
    mockApiCache.set.mockImplementation(() => {});

    // Mock successful tenant
    mockUseTenant.mockReturnValue({
      tenant: mockTenant,
      loading: false,
      error: null
    });

    // Mock successful fetch responses
    global.fetch = _globals.jest.fn().mockResolvedValueOnce(_testUtils.testUtils.mockApiSuccess(mockStats)).mockResolvedValueOnce(_testUtils.testUtils.mockApiSuccess(mockRenewals));
  });
  describe('Initial State', () => {
    it('should return initial state correctly', () => {
      const {
        result
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
      expect(result.current.data).toEqual({
        stats: {
          totalRenewals: 0,
          renewalsDue: 0,
          vendors: 0,
          annualSpend: '$0'
        },
        recentRenewals: [],
        upcomingRenewals: []
      });
    });
    it('should handle loading tenant state', () => {
      mockUseTenant.mockReturnValue({
        tenant: null,
        loading: true,
        error: null
      });
      const {
        result
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });
    it('should handle tenant error state', () => {
      const tenantError = 'Failed to load tenant';
      mockUseTenant.mockReturnValue({
        tenant: null,
        loading: false,
        error: tenantError
      });
      const {
        result
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());
      expect(result.current.error).toBe(tenantError);
    });
  });
  describe('Data Fetching', () => {
    it('should fetch dashboard data successfully', async () => {
      const {
        result
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());
      await (0, _react.waitFor)(() => {
        expect(result.current.isLoading).toBe(false);
      });
      expect(result.current.data.stats).toEqual(mockStats);
      expect(result.current.data.recentRenewals).toEqual(mockRenewals);
      expect(result.current.error).toBe(null);
    });
    it('should handle API errors gracefully', async () => {
      const errorMessage = 'API Error';
      global.fetch = _globals.jest.fn().mockResolvedValueOnce(_testUtils.testUtils.mockApiError(errorMessage));
      const {
        result
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());
      await (0, _react.waitFor)(() => {
        expect(result.current.error).toBe(errorMessage);
      });
      expect(result.current.isLoading).toBe(false);
    });
    it('should handle network errors', async () => {
      global.fetch = _globals.jest.fn().mockRejectedValueOnce(new Error('Network error'));
      const {
        result
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());
      await (0, _react.waitFor)(() => {
        expect(result.current.error).toContain('Network error');
      });
    });
    it('should fetch data when tenant changes', async () => {
      const {
        result,
        rerender
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());

      // Change tenant
      const newTenant = _testUtils.testUtils.generateTestData.tenant({
        tenantId: 'new-tenant-id'
      });
      mockUseTenant.mockReturnValue({
        tenant: newTenant,
        loading: false,
        error: null
      });
      rerender();
      await (0, _react.waitFor)(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/dashboard/stats', expect.any(Object));
      });
    });
  });
  describe('Caching', () => {
    it('should use cached data when available', async () => {
      // Mock cache hit
      mockApiCache.get.mockReturnValueOnce(mockStats).mockReturnValueOnce(mockRenewals);
      const {
        result
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());
      await (0, _react.waitFor)(() => {
        expect(result.current.data.stats).toEqual(mockStats);
      });

      // Should not make API calls when cache hits
      expect(global.fetch).not.toHaveBeenCalled();
    });
    it('should cache API responses', async () => {
      const {
        result
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());
      await (0, _react.waitFor)(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Should have cached the responses
      expect(mockApiCache.set).toHaveBeenCalledWith(`dashboard-stats:${mockTenant.tenantId}`, mockStats, expect.any(Number), expect.any(Array));
    });
    it('should handle cache misses correctly', async () => {
      // Mock cache miss
      mockApiCache.get.mockReturnValue(null);
      const {
        result
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());
      await (0, _react.waitFor)(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Should make API calls when cache misses
      expect(global.fetch).toHaveBeenCalled();
    });
  });
  describe('Performance Optimizations', () => {
    it('should debounce tenant changes', () => {
      const {
        useDebounce
      } = require('@/lib/performance');
      (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());
      expect(useDebounce).toHaveBeenCalledWith(mockTenant, 300);
    });
    it('should mark performance timing', async () => {
      const {
        performanceUtils
      } = require('@/lib/performance');
      const {
        result
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());
      await (0, _react.waitFor)(() => {
        expect(result.current.isLoading).toBe(false);
      });
      expect(performanceUtils.mark).toHaveBeenCalledWith('fetchStats');
      expect(performanceUtils.measure).toHaveBeenCalledWith('fetchStats');
    });
    it('should abort previous requests', async () => {
      const abortSpy = _globals.jest.fn();
      global.AbortController = _globals.jest.fn(() => ({
        abort: abortSpy,
        signal: {}
      }));
      const {
        result,
        rerender
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());

      // Trigger a new request
      mockUseTenant.mockReturnValue({
        tenant: _objectSpread(_objectSpread({}, mockTenant), {}, {
          tenantId: 'new-tenant'
        }),
        loading: false,
        error: null
      });
      rerender();

      // Should abort previous request
      expect(abortSpy).toHaveBeenCalled();
    });
  });
  describe('Refetch Functionality', () => {
    it('should provide refetch function', () => {
      const {
        result
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());
      expect(typeof result.current.refetch).toBe('function');
    });
    it('should refetch data when refetch is called', async () => {
      const {
        result
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());

      // Clear previous calls
      _globals.jest.clearAllMocks();

      // Call refetch
      await result.current.refetch();
      expect(global.fetch).toHaveBeenCalled();
    });
    it('should handle refetch errors', async () => {
      global.fetch = _globals.jest.fn().mockRejectedValueOnce(new Error('Refetch error'));
      const {
        result
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());
      await expect(result.current.refetch()).rejects.toThrow('Refetch error');
    });
  });
  describe('Error Recovery', () => {
    it('should recover from errors on retry', async () => {
      // First call fails
      global.fetch = _globals.jest.fn().mockRejectedValueOnce(new Error('Network error')).mockResolvedValueOnce(_testUtils.testUtils.mockApiSuccess(mockStats));
      const {
        result
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());
      await (0, _react.waitFor)(() => {
        expect(result.current.error).toContain('Network error');
      });

      // Retry should succeed
      await result.current.refetch();
      await (0, _react.waitFor)(() => {
        expect(result.current.error).toBe(null);
        expect(result.current.data.stats).toEqual(mockStats);
      });
    });
    it('should clear errors on successful fetch', async () => {
      const {
        result
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());

      // Simulate error state
      await (0, _react.waitFor)(() => {
        if (result.current.error) {
          expect(result.current.error).toBeTruthy();
        }
      });

      // Successful refetch should clear error
      global.fetch = _globals.jest.fn().mockResolvedValueOnce(_testUtils.testUtils.mockApiSuccess(mockStats));
      await result.current.refetch();
      await (0, _react.waitFor)(() => {
        expect(result.current.error).toBe(null);
      });
    });
  });
  describe('Memory Management', () => {
    it('should cleanup on unmount', () => {
      const {
        unmount
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());

      // Should not throw on unmount
      expect(() => unmount()).not.toThrow();
    });
    it('should handle component unmount during fetch', async () => {
      const {
        result,
        unmount
      } = (0, _react.renderHook)(() => (0, _useDashboardData.useDashboardData)());

      // Start a fetch operation
      const fetchPromise = result.current.refetch();

      // Unmount before fetch completes
      unmount();

      // Should not throw
      await expect(fetchPromise).resolves.not.toThrow();
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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