058922a111e022881f268d97b82e3be6
/* istanbul ignore next */
function cov_uj9oms7rd() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\aws-config.ts";
  var hash = "ee81d87fcafc5d9f1df11af00c4bc699912ad708";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\aws-config.ts",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 2
        },
        end: {
          line: 12,
          column: 3
        }
      },
      "1": {
        start: {
          line: 7,
          column: 4
        },
        end: {
          line: 11,
          column: 6
        }
      },
      "2": {
        start: {
          line: 14,
          column: 20
        },
        end: {
          line: 14,
          column: 81
        }
      },
      "3": {
        start: {
          line: 16,
          column: 18
        },
        end: {
          line: 20,
          column: 4
        }
      },
      "4": {
        start: {
          line: 22,
          column: 19
        },
        end: {
          line: 22,
          column: 48
        }
      },
      "5": {
        start: {
          line: 24,
          column: 2
        },
        end: {
          line: 29,
          column: 4
        }
      },
      "6": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 44
        }
      },
      "7": {
        start: {
          line: 36,
          column: 20
        },
        end: {
          line: 43,
          column: 4
        }
      },
      "8": {
        start: {
          line: 45,
          column: 18
        },
        end: {
          line: 48,
          column: 4
        }
      },
      "9": {
        start: {
          line: 50,
          column: 19
        },
        end: {
          line: 50,
          column: 48
        }
      },
      "10": {
        start: {
          line: 51,
          column: 2
        },
        end: {
          line: 51,
          column: 35
        }
      }
    },
    fnMap: {
      "0": {
        name: "getCredentials",
        decl: {
          start: {
            line: 5,
            column: 15
          },
          end: {
            line: 5,
            column: 29
          }
        },
        loc: {
          start: {
            line: 5,
            column: 32
          },
          end: {
            line: 30,
            column: 1
          }
        },
        line: 5
      },
      "1": {
        name: "getParameter",
        decl: {
          start: {
            line: 33,
            column: 22
          },
          end: {
            line: 33,
            column: 34
          }
        },
        loc: {
          start: {
            line: 33,
            column: 54
          },
          end: {
            line: 52,
            column: 1
          }
        },
        line: 33
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 2
          },
          end: {
            line: 12,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 2
          },
          end: {
            line: 12,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 37,
            column: 12
          },
          end: {
            line: 37,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 12
          },
          end: {
            line: 37,
            column: 46
          }
        }, {
          start: {
            line: 37,
            column: 50
          },
          end: {
            line: 37,
            column: 64
          }
        }],
        line: 37
      },
      "2": {
        loc: {
          start: {
            line: 39,
            column: 19
          },
          end: {
            line: 39,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 19
          },
          end: {
            line: 39,
            column: 42
          }
        }, {
          start: {
            line: 39,
            column: 46
          },
          end: {
            line: 39,
            column: 48
          }
        }],
        line: 39
      },
      "3": {
        loc: {
          start: {
            line: 40,
            column: 23
          },
          end: {
            line: 40,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 23
          },
          end: {
            line: 40,
            column: 50
          }
        }, {
          start: {
            line: 40,
            column: 54
          },
          end: {
            line: 40,
            column: 56
          }
        }],
        line: 40
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ee81d87fcafc5d9f1df11af00c4bc699912ad708"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_uj9oms7rd = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_uj9oms7rd();
import { STSClient, AssumeRoleCommand } from "@aws-sdk/client-sts";
import { SSMClient, GetParameterCommand } from "@aws-sdk/client-ssm";

// Function to get temporary credentials by assuming a role
async function getCredentials() {
  /* istanbul ignore next */
  cov_uj9oms7rd().f[0]++;
  cov_uj9oms7rd().s[0]++;
  if (process.env.NODE_ENV !== 'production') {
    /* istanbul ignore next */
    cov_uj9oms7rd().b[0][0]++;
    cov_uj9oms7rd().s[1]++;
    return {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.NEXT_PUBLIC_AWS_REGION
    };
  } else
  /* istanbul ignore next */
  {
    cov_uj9oms7rd().b[0][1]++;
  }
  const stsClient =
  /* istanbul ignore next */
  (cov_uj9oms7rd().s[2]++, new STSClient({
    region: process.env.NEXT_PUBLIC_AWS_REGION
  }));
  const command =
  /* istanbul ignore next */
  (cov_uj9oms7rd().s[3]++, new AssumeRoleCommand({
    RoleArn: process.env.PARAMETER_STORE_ROLE_ARN,
    RoleSessionName: 'RenewTrackParameterStoreAccess',
    DurationSeconds: 900 // 15 minutes
  }));
  const response =
  /* istanbul ignore next */
  (cov_uj9oms7rd().s[4]++, await stsClient.send(command));
  /* istanbul ignore next */
  cov_uj9oms7rd().s[5]++;
  return {
    accessKeyId: response.Credentials?.AccessKeyId,
    secretAccessKey: response.Credentials?.SecretAccessKey,
    sessionToken: response.Credentials?.SessionToken,
    region: process.env.NEXT_PUBLIC_AWS_REGION
  };
}

// Get parameter from SSM Parameter Store with assumed role
export async function getParameter(paramName) {
  /* istanbul ignore next */
  cov_uj9oms7rd().f[1]++;
  const credentials =
  /* istanbul ignore next */
  (cov_uj9oms7rd().s[6]++, await getCredentials());
  const ssmClient =
  /* istanbul ignore next */
  (cov_uj9oms7rd().s[7]++, new SSMClient({
    region:
    /* istanbul ignore next */
    (cov_uj9oms7rd().b[1][0]++, process.env.NEXT_PUBLIC_AWS_REGION) ||
    /* istanbul ignore next */
    (cov_uj9oms7rd().b[1][1]++, 'ca-central-1'),
    credentials: {
      accessKeyId:
      /* istanbul ignore next */
      (cov_uj9oms7rd().b[2][0]++, credentials.accessKeyId) ||
      /* istanbul ignore next */
      (cov_uj9oms7rd().b[2][1]++, ''),
      secretAccessKey:
      /* istanbul ignore next */
      (cov_uj9oms7rd().b[3][0]++, credentials.secretAccessKey) ||
      /* istanbul ignore next */
      (cov_uj9oms7rd().b[3][1]++, ''),
      sessionToken: credentials.sessionToken
    }
  }));
  const command =
  /* istanbul ignore next */
  (cov_uj9oms7rd().s[8]++, new GetParameterCommand({
    Name: paramName,
    WithDecryption: true
  }));
  const response =
  /* istanbul ignore next */
  (cov_uj9oms7rd().s[9]++, await ssmClient.send(command));
  /* istanbul ignore next */
  cov_uj9oms7rd().s[10]++;
  return response.Parameter?.Value;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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