/**
 * Loading State Components
 * 
 * Reusable loading components with different styles and animations.
 * Focused responsibility: Consistent loading UI across the application.
 */

'use client'

import { BaseComponentProps } from '@/lib/types'

interface LoadingSpinnerProps extends BaseComponentProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'primary' | 'secondary' | 'white'
  text?: string
}

interface LoadingSkeletonProps extends BaseComponentProps {
  lines?: number
  height?: string
  width?: string
  animate?: boolean
}

interface LoadingPageProps extends BaseComponentProps {
  title?: string
  subtitle?: string
  icon?: string
}

interface LoadingCardProps extends BaseComponentProps {
  title?: string
  lines?: number
}

// Loading Spinner Component
export function LoadingSpinner({
  size = 'md',
  color = 'primary',
  text,
  className = '',
  'data-testid': testId
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }

  const colorClasses = {
    primary: 'text-blue-600',
    secondary: 'text-gray-600',
    white: 'text-white'
  }

  return (
    <div 
      className={`flex items-center justify-center ${className}`}
      data-testid={testId}
      role="status"
      aria-label={text || 'Loading'}
    >
      <svg
        className={`animate-spin ${sizeClasses[size]} ${colorClasses[color]}`}
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
      {text && (
        <span className="ml-2 text-sm text-secondary">{text}</span>
      )}
    </div>
  )
}

// Loading Skeleton Component
export function LoadingSkeleton({
  lines = 1,
  height = '1rem',
  width = '100%',
  animate = true,
  className = '',
  'data-testid': testId
}: LoadingSkeletonProps) {
  const animationClass = animate ? 'animate-pulse' : ''

  if (lines === 1) {
    return (
      <div
        className={`bg-gray-200 rounded ${animationClass} ${className}`}
        style={{ height, width }}
        data-testid={testId}
        role="status"
        aria-label="Loading content"
      />
    )
  }

  return (
    <div className={`space-y-2 ${className}`} data-testid={testId}>
      {Array.from({ length: lines }, (_, index) => (
        <div
          key={index}
          className={`bg-gray-200 rounded ${animationClass}`}
          style={{
            height,
            width: index === lines - 1 ? '75%' : width // Last line is shorter
          }}
          role="status"
          aria-label={`Loading content line ${index + 1}`}
        />
      ))}
    </div>
  )
}

// Full Page Loading Component
export function LoadingPage({
  title = 'Loading...',
  subtitle,
  icon = '⏳',
  className = '',
  'data-testid': testId
}: LoadingPageProps) {
  return (
    <div 
      className={`flex items-center justify-center min-h-screen ${className}`}
      data-testid={testId}
    >
      <div className="text-center">
        <div className="text-6xl mb-4" role="img" aria-label="Loading icon">
          {icon}
        </div>
        <h1 className="text-2xl font-semibold mb-2">{title}</h1>
        {subtitle && (
          <p className="text-secondary max-w-md">{subtitle}</p>
        )}
        <div className="mt-6">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    </div>
  )
}

// Loading Card Component
export function LoadingCard({
  title,
  lines = 3,
  className = '',
  'data-testid': testId
}: LoadingCardProps) {
  return (
    <div 
      className={`card ${className}`}
      data-testid={testId}
    >
      {title && (
        <div className="card-header">
          <LoadingSkeleton width="40%" height="1.5rem" />
        </div>
      )}
      <div className="card-content">
        <LoadingSkeleton lines={lines} />
      </div>
    </div>
  )
}

// Loading Button Component
export function LoadingButton({
  children,
  isLoading = false,
  disabled = false,
  className = '',
  onClick,
  ...props
}: {
  children: React.ReactNode
  isLoading?: boolean
  disabled?: boolean
  className?: string
  onClick?: () => void
  [key: string]: any
}) {
  return (
    <button
      className={`btn ${className} ${isLoading ? 'opacity-75 cursor-not-allowed' : ''}`}
      disabled={disabled || isLoading}
      onClick={isLoading ? undefined : onClick}
      {...props}
    >
      {isLoading ? (
        <div className="flex items-center">
          <LoadingSpinner size="sm" color="white" />
          <span className="ml-2">Loading...</span>
        </div>
      ) : (
        children
      )}
    </button>
  )
}

// Loading Overlay Component
export function LoadingOverlay({
  isVisible = false,
  text = 'Loading...',
  className = '',
  'data-testid': testId
}: {
  isVisible?: boolean
  text?: string
  className?: string
  'data-testid'?: string
}) {
  if (!isVisible) return null

  return (
    <div
      className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${className}`}
      data-testid={testId}
      role="dialog"
      aria-modal="true"
      aria-label="Loading overlay"
    >
      <div className="bg-white rounded-lg p-6 shadow-lg">
        <LoadingSpinner size="lg" text={text} />
      </div>
    </div>
  )
}

// Loading List Component
export function LoadingList({
  items = 5,
  showAvatar = false,
  className = '',
  'data-testid': testId
}: {
  items?: number
  showAvatar?: boolean
  className?: string
  'data-testid'?: string
}) {
  return (
    <div className={`space-y-4 ${className}`} data-testid={testId}>
      {Array.from({ length: items }, (_, index) => (
        <div key={index} className="flex items-center space-x-3 p-3 border rounded-lg">
          {showAvatar && (
            <LoadingSkeleton width="2.5rem" height="2.5rem" />
          )}
          <div className="flex-1">
            <LoadingSkeleton width="60%" height="1rem" />
            <div className="mt-2">
              <LoadingSkeleton width="40%" height="0.75rem" />
            </div>
          </div>
          <LoadingSkeleton width="4rem" height="1.5rem" />
        </div>
      ))}
    </div>
  )
}

// Loading Table Component
export function LoadingTable({
  rows = 5,
  columns = 4,
  showHeader = true,
  className = '',
  'data-testid': testId
}: {
  rows?: number
  columns?: number
  showHeader?: boolean
  className?: string
  'data-testid'?: string
}) {
  return (
    <div className={`overflow-hidden border rounded-lg ${className}`} data-testid={testId}>
      <table className="w-full">
        {showHeader && (
          <thead className="bg-gray-50">
            <tr>
              {Array.from({ length: columns }, (_, index) => (
                <th key={index} className="p-3 text-left">
                  <LoadingSkeleton width="80%" height="1rem" />
                </th>
              ))}
            </tr>
          </thead>
        )}
        <tbody>
          {Array.from({ length: rows }, (_, rowIndex) => (
            <tr key={rowIndex} className="border-t">
              {Array.from({ length: columns }, (_, colIndex) => (
                <td key={colIndex} className="p-3">
                  <LoadingSkeleton 
                    width={colIndex === 0 ? '90%' : '70%'} 
                    height="0.875rem" 
                  />
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}
