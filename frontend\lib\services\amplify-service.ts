/**
 * Amplify Service - Singleton Configuration Manager
 * 
 * Eliminates redundant Amplify configurations by providing a centralized,
 * singleton service that configures Amplify once and reuses the configuration.
 */

import { Amplify } from 'aws-amplify'
import { amplifyConfig, appConfig } from '@/lib/config/app-config'

class AmplifyService {
  private static instance: AmplifyService
  private isConfigured = false
  private configurationPromise: Promise<void> | null = null

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): AmplifyService {
    if (!AmplifyService.instance) {
      AmplifyService.instance = new AmplifyService()
    }
    return AmplifyService.instance
  }

  /**
   * Configure Amplify (idempotent - safe to call multiple times)
   */
  public async configure(): Promise<void> {
    // If already configured, return immediately
    if (this.isConfigured) {
      return
    }

    // If configuration is in progress, wait for it
    if (this.configurationPromise) {
      return this.configurationPromise
    }

    // Start configuration
    this.configurationPromise = this.performConfiguration()
    
    try {
      await this.configurationPromise
    } finally {
      this.configurationPromise = null
    }
  }

  /**
   * Perform the actual Amplify configuration
   */
  private async performConfiguration(): Promise<void> {
    try {
      if (appConfig.isDevelopment) {
        console.log('Configuring Amplify with:', {
          region: amplifyConfig.region,
          userPoolId: amplifyConfig.userPoolId,
          userPoolClientId: amplifyConfig.userPoolClientId,
          cognitoDomain: amplifyConfig.cognitoDomain,
          redirectSignIn: amplifyConfig.redirectSignIn,
          redirectSignOut: amplifyConfig.redirectSignOut
        })
      }

      // Configure Amplify
      Amplify.configure({
        Auth: {
          Cognito: {
            userPoolId: amplifyConfig.userPoolId,
            userPoolClientId: amplifyConfig.userPoolClientId,
            loginWith: {
              oauth: {
                domain: amplifyConfig.cognitoDomain,
                scopes: ['openid', 'email', 'profile'],
                redirectSignIn: [amplifyConfig.redirectSignIn],
                redirectSignOut: [amplifyConfig.redirectSignOut],
                responseType: 'code'
              }
            }
          }
        }
      })

      this.isConfigured = true

      if (appConfig.isDevelopment) {
        console.log(`Amplify configured successfully with OAuth domain: ${amplifyConfig.cognitoDomain}`)
      }

    } catch (error) {
      console.error('Failed to configure Amplify:', error)
      throw new Error('Amplify configuration failed')
    }
  }

  /**
   * Check if Amplify is configured
   */
  public isAmplifyConfigured(): boolean {
    return this.isConfigured
  }

  /**
   * Get current configuration (for debugging)
   */
  public getConfiguration() {
    return {
      isConfigured: this.isConfigured,
      config: amplifyConfig
    }
  }

  /**
   * Reset configuration (for testing purposes)
   */
  public reset(): void {
    this.isConfigured = false
    this.configurationPromise = null
  }
}

// Export singleton instance
export const amplifyService = AmplifyService.getInstance()

// Convenience function for easy import
export async function ensureAmplifyConfigured(): Promise<void> {
  await amplifyService.configure()
}

// Export the class for testing
export { AmplifyService }
