{"version": 3, "names": ["cov_17fikhsrsv", "actualCoverage", "getClientByEmailDomain", "execute<PERSON>uery", "schemaExists", "requireAuth", "createSuccessResponse", "createErrorResponse", "ApiErrorCode", "HttpStatus", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GET", "s", "f", "authResult", "success", "b", "response", "session", "clientResult", "email", "statusCode", "errorCode", "NOT_FOUND", "INTERNAL_SERVER_ERROR", "apiErrorCode", "DATABASE_ERROR", "error", "tenant", "client", "schemaReady", "tenantSchema", "renewals", "renewalsQuery", "result", "schema", "data", "map", "row", "id", "toString", "name", "vendor", "status", "dueDate", "due_date", "Date", "toLocaleDateString", "addedDate", "created_at", "console", "log", "length"], "sources": ["route.ts"], "sourcesContent": ["import { getClientByEmailDomain } from '@/lib/clients';\nimport { executeQuery, schemaExists } from '@/lib/database';\nimport { requireAuth } from '@/lib/auth-middleware';\nimport {\n  createSuccessResponse,\n  createErrorResponse,\n  ApiErrorCode,\n  HttpStatus,\n  withErrorHandling\n} from '@/lib/api-response';\n\n// Renewal interface\ninterface Renewal {\n  id: string;\n  name: string;\n  vendor: string;\n  status: string;\n  dueDate: string;\n  addedDate: string;\n}\n\nexport const GET = withErrorHandling(async () => {\n  // Verify authentication\n  const authResult = await requireAuth();\n  if (!authResult.success) {\n    return authResult.response!;\n  }\n\n  const session = authResult.session!;\n\n  // Get tenant context using the new secure method\n  const clientResult = await getClientByEmailDomain(session.email);\n\n  if (!clientResult.success) {\n    const statusCode = clientResult.errorCode === 'NOT_FOUND' ? HttpStatus.NOT_FOUND : HttpStatus.INTERNAL_SERVER_ERROR;\n    const apiErrorCode = clientResult.errorCode === 'NOT_FOUND' ? ApiErrorCode.NOT_FOUND : ApiErrorCode.DATABASE_ERROR;\n\n    return createErrorResponse(\n      clientResult.error || 'Failed to fetch tenant information',\n      apiErrorCode,\n      statusCode\n    );\n  }\n\n  const tenant = clientResult.client!;\n\n  // Check if tenant schema exists\n  const schemaReady = await schemaExists(tenant.tenantSchema);\n\n  let renewals: Renewal[] = [];\n\n  if (schemaReady) {\n    // Query tenant schema for actual renewal data\n    const renewalsQuery = `\n      SELECT\n        \"RenewalID\" as id,\n        \"RenewalName\" as name,\n        \"VendorName\" as vendor,\n        \"Status\" as status,\n        \"DueDate\" as due_date,\n        \"CreatedOn\" as created_at\n      FROM \"${tenant.tenantSchema}\".\"Renewals\"\n      WHERE \"Active\" = true\n      ORDER BY \"CreatedOn\" DESC\n      LIMIT 10\n    `;\n\n    const result = await executeQuery(renewalsQuery, [], { schema: tenant.tenantSchema });\n\n    if (result.success && result.data) {\n      // Format the results\n      renewals = result.data.map((row: any) => ({\n        id: row.id.toString(),\n        name: row.name,\n        vendor: row.vendor,\n        status: row.status,\n        dueDate: row.due_date ? new Date(row.due_date).toLocaleDateString() : 'Not set',\n        addedDate: row.created_at ? new Date(row.created_at).toLocaleDateString() : 'Unknown'\n      }));\n    }\n  } else {\n    console.log(`Tenant schema ${tenant.tenantSchema} not ready yet, using mock data`);\n  }\n\n  // Use mock data if no real data available\n  if (renewals.length === 0) {\n    renewals = [\n      { id: '1', name: 'Unlimited Renewal', vendor: 'Vox Audio', status: 'Not available', dueDate: 'Run Now 2025', addedDate: '2025-01-01' },\n      { id: '2', name: 'Core Pro', vendor: 'Vox Audio', status: 'Not available', dueDate: 'Run Now 2025', addedDate: '2025-01-02' },\n      { id: '3', name: 'Test Software', vendor: 'Vox Tools', status: 'Not available', dueDate: 'Run Now 2025', addedDate: '2025-01-03' },\n      { id: '4', name: 'Mobile app', vendor: 'TopCar', status: 'Not available', dueDate: 'Run Now 2025', addedDate: '2025-01-04' },\n      { id: '5', name: 'ASAUDB', vendor: 'Cubix', status: 'Not available', dueDate: 'Run Now 2025', addedDate: '2025-01-05' }\n    ];\n  }\n\n  return createSuccessResponse(renewals, 'Renewals retrieved successfully');\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ,SAASE,sBAAsB,QAAQ,eAAe;AACtD,SAASC,YAAY,EAAEC,YAAY,QAAQ,gBAAgB;AAC3D,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SACEC,qBAAqB,EACrBC,mBAAmB,EACnBC,YAAY,EACZC,UAAU,EACVC,iBAAiB,QACZ,oBAAoB;;AAE3B;;AAUA,OAAO,MAAMC,GAAG;AAAA;AAAA,CAAAX,cAAA,GAAAY,CAAA,OAAGF,iBAAiB,CAAC,YAAY;EAAA;EAAAV,cAAA,GAAAa,CAAA;EAC/C;EACA,MAAMC,UAAU;EAAA;EAAA,CAAAd,cAAA,GAAAY,CAAA,OAAG,MAAMP,WAAW,CAAC,CAAC;EAAC;EAAAL,cAAA,GAAAY,CAAA;EACvC,IAAI,CAACE,UAAU,CAACC,OAAO,EAAE;IAAA;IAAAf,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAY,CAAA;IACvB,OAAOE,UAAU,CAACG,QAAQ;EAC5B,CAAC;EAAA;EAAA;IAAAjB,cAAA,GAAAgB,CAAA;EAAA;EAED,MAAME,OAAO;EAAA;EAAA,CAAAlB,cAAA,GAAAY,CAAA,OAAGE,UAAU,CAACI,OAAO,CAAC;;EAEnC;EACA,MAAMC,YAAY;EAAA;EAAA,CAAAnB,cAAA,GAAAY,CAAA,OAAG,MAAMV,sBAAsB,CAACgB,OAAO,CAACE,KAAK,CAAC;EAAC;EAAApB,cAAA,GAAAY,CAAA;EAEjE,IAAI,CAACO,YAAY,CAACJ,OAAO,EAAE;IAAA;IAAAf,cAAA,GAAAgB,CAAA;IACzB,MAAMK,UAAU;IAAA;IAAA,CAAArB,cAAA,GAAAY,CAAA,OAAGO,YAAY,CAACG,SAAS,KAAK,WAAW;IAAA;IAAA,CAAAtB,cAAA,GAAAgB,CAAA,UAAGP,UAAU,CAACc,SAAS;IAAA;IAAA,CAAAvB,cAAA,GAAAgB,CAAA,UAAGP,UAAU,CAACe,qBAAqB;IACnH,MAAMC,YAAY;IAAA;IAAA,CAAAzB,cAAA,GAAAY,CAAA,OAAGO,YAAY,CAACG,SAAS,KAAK,WAAW;IAAA;IAAA,CAAAtB,cAAA,GAAAgB,CAAA,UAAGR,YAAY,CAACe,SAAS;IAAA;IAAA,CAAAvB,cAAA,GAAAgB,CAAA,UAAGR,YAAY,CAACkB,cAAc;IAAC;IAAA1B,cAAA,GAAAY,CAAA;IAEnH,OAAOL,mBAAmB;IACxB;IAAA,CAAAP,cAAA,GAAAgB,CAAA,UAAAG,YAAY,CAACQ,KAAK;IAAA;IAAA,CAAA3B,cAAA,GAAAgB,CAAA,UAAI,oCAAoC,GAC1DS,YAAY,EACZJ,UACF,CAAC;EACH,CAAC;EAAA;EAAA;IAAArB,cAAA,GAAAgB,CAAA;EAAA;EAED,MAAMY,MAAM;EAAA;EAAA,CAAA5B,cAAA,GAAAY,CAAA,QAAGO,YAAY,CAACU,MAAM,CAAC;;EAEnC;EACA,MAAMC,WAAW;EAAA;EAAA,CAAA9B,cAAA,GAAAY,CAAA,QAAG,MAAMR,YAAY,CAACwB,MAAM,CAACG,YAAY,CAAC;EAE3D,IAAIC,QAAmB;EAAA;EAAA,CAAAhC,cAAA,GAAAY,CAAA,QAAG,EAAE;EAAC;EAAAZ,cAAA,GAAAY,CAAA;EAE7B,IAAIkB,WAAW,EAAE;IAAA;IAAA9B,cAAA,GAAAgB,CAAA;IACf;IACA,MAAMiB,aAAa;IAAA;IAAA,CAAAjC,cAAA,GAAAY,CAAA,QAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAcgB,MAAM,CAACG,YAAY;AACjC;AACA;AACA;AACA,KAAK;IAED,MAAMG,MAAM;IAAA;IAAA,CAAAlC,cAAA,GAAAY,CAAA,QAAG,MAAMT,YAAY,CAAC8B,aAAa,EAAE,EAAE,EAAE;MAAEE,MAAM,EAAEP,MAAM,CAACG;IAAa,CAAC,CAAC;IAAC;IAAA/B,cAAA,GAAAY,CAAA;IAEtF;IAAI;IAAA,CAAAZ,cAAA,GAAAgB,CAAA,UAAAkB,MAAM,CAACnB,OAAO;IAAA;IAAA,CAAAf,cAAA,GAAAgB,CAAA,UAAIkB,MAAM,CAACE,IAAI,GAAE;MAAA;MAAApC,cAAA,GAAAgB,CAAA;MAAAhB,cAAA,GAAAY,CAAA;MACjC;MACAoB,QAAQ,GAAGE,MAAM,CAACE,IAAI,CAACC,GAAG,CAAEC,GAAQ,IAAM;QAAA;QAAAtC,cAAA,GAAAa,CAAA;QAAAb,cAAA,GAAAY,CAAA;QAAA;UACxC2B,EAAE,EAAED,GAAG,CAACC,EAAE,CAACC,QAAQ,CAAC,CAAC;UACrBC,IAAI,EAAEH,GAAG,CAACG,IAAI;UACdC,MAAM,EAAEJ,GAAG,CAACI,MAAM;UAClBC,MAAM,EAAEL,GAAG,CAACK,MAAM;UAClBC,OAAO,EAAEN,GAAG,CAACO,QAAQ;UAAA;UAAA,CAAA7C,cAAA,GAAAgB,CAAA,UAAG,IAAI8B,IAAI,CAACR,GAAG,CAACO,QAAQ,CAAC,CAACE,kBAAkB,CAAC,CAAC;UAAA;UAAA,CAAA/C,cAAA,GAAAgB,CAAA,UAAG,SAAS;UAC/EgC,SAAS,EAAEV,GAAG,CAACW,UAAU;UAAA;UAAA,CAAAjD,cAAA,GAAAgB,CAAA,UAAG,IAAI8B,IAAI,CAACR,GAAG,CAACW,UAAU,CAAC,CAACF,kBAAkB,CAAC,CAAC;UAAA;UAAA,CAAA/C,cAAA,GAAAgB,CAAA,UAAG,SAAS;QACvF,CAAC;MAAD,CAAE,CAAC;IACL,CAAC;IAAA;IAAA;MAAAhB,cAAA,GAAAgB,CAAA;IAAA;EACH,CAAC,MAAM;IAAA;IAAAhB,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAY,CAAA;IACLsC,OAAO,CAACC,GAAG,CAAC,iBAAiBvB,MAAM,CAACG,YAAY,iCAAiC,CAAC;EACpF;;EAEA;EAAA;EAAA/B,cAAA,GAAAY,CAAA;EACA,IAAIoB,QAAQ,CAACoB,MAAM,KAAK,CAAC,EAAE;IAAA;IAAApD,cAAA,GAAAgB,CAAA;IAAAhB,cAAA,GAAAY,CAAA;IACzBoB,QAAQ,GAAG,CACT;MAAEO,EAAE,EAAE,GAAG;MAAEE,IAAI,EAAE,mBAAmB;MAAEC,MAAM,EAAE,WAAW;MAAEC,MAAM,EAAE,eAAe;MAAEC,OAAO,EAAE,cAAc;MAAEI,SAAS,EAAE;IAAa,CAAC,EACtI;MAAET,EAAE,EAAE,GAAG;MAAEE,IAAI,EAAE,UAAU;MAAEC,MAAM,EAAE,WAAW;MAAEC,MAAM,EAAE,eAAe;MAAEC,OAAO,EAAE,cAAc;MAAEI,SAAS,EAAE;IAAa,CAAC,EAC7H;MAAET,EAAE,EAAE,GAAG;MAAEE,IAAI,EAAE,eAAe;MAAEC,MAAM,EAAE,WAAW;MAAEC,MAAM,EAAE,eAAe;MAAEC,OAAO,EAAE,cAAc;MAAEI,SAAS,EAAE;IAAa,CAAC,EAClI;MAAET,EAAE,EAAE,GAAG;MAAEE,IAAI,EAAE,YAAY;MAAEC,MAAM,EAAE,QAAQ;MAAEC,MAAM,EAAE,eAAe;MAAEC,OAAO,EAAE,cAAc;MAAEI,SAAS,EAAE;IAAa,CAAC,EAC5H;MAAET,EAAE,EAAE,GAAG;MAAEE,IAAI,EAAE,QAAQ;MAAEC,MAAM,EAAE,OAAO;MAAEC,MAAM,EAAE,eAAe;MAAEC,OAAO,EAAE,cAAc;MAAEI,SAAS,EAAE;IAAa,CAAC,CACxH;EACH,CAAC;EAAA;EAAA;IAAAhD,cAAA,GAAAgB,CAAA;EAAA;EAAAhB,cAAA,GAAAY,CAAA;EAED,OAAON,qBAAqB,CAAC0B,QAAQ,EAAE,iCAAiC,CAAC;AAC3E,CAAC,CAAC", "ignoreList": []}