9ac6fe730d19b43b0929c4b8a3a3b84a
/* istanbul ignore next */
"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.validateConfig = exports.serverConfig = exports.publicConfig = exports.getDatabaseConfig = exports.getAwsConfig = exports.getAuthConfig = exports.env = void 0;
var
/* istanbul ignore next */
_zod = require("zod");
/* istanbul ignore next */
function cov_44vatrfhc() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\config.ts";
  var hash = "1f8392462d5b192fa3d4cda736596d304883c24c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\config.ts",
    statementMap: {
      "0": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 32,
          column: 2
        }
      },
      "1": {
        start: {
          line: 36,
          column: 2
        },
        end: {
          line: 41,
          column: 3
        }
      },
      "2": {
        start: {
          line: 37,
          column: 4
        },
        end: {
          line: 37,
          column: 40
        }
      },
      "3": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 65
        }
      },
      "4": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 40,
          column: 84
        }
      },
      "5": {
        start: {
          line: 45,
          column: 12
        },
        end: {
          line: 45,
          column: 25
        }
      },
      "6": {
        start: {
          line: 48,
          column: 28
        },
        end: {
          line: 64,
          column: 10
        }
      },
      "7": {
        start: {
          line: 67,
          column: 28
        },
        end: {
          line: 76,
          column: 10
        }
      },
      "8": {
        start: {
          line: 83,
          column: 29
        },
        end: {
          line: 83,
          column: 52
        }
      },
      "9": {
        start: {
          line: 83,
          column: 35
        },
        end: {
          line: 83,
          column: 52
        }
      },
      "10": {
        start: {
          line: 84,
          column: 28
        },
        end: {
          line: 84,
          column: 50
        }
      },
      "11": {
        start: {
          line: 84,
          column: 34
        },
        end: {
          line: 84,
          column: 50
        }
      },
      "12": {
        start: {
          line: 85,
          column: 33
        },
        end: {
          line: 90,
          column: 1
        }
      },
      "13": {
        start: {
          line: 86,
          column: 2
        },
        end: {
          line: 88,
          column: 3
        }
      },
      "14": {
        start: {
          line: 87,
          column: 4
        },
        end: {
          line: 87,
          column: 82
        }
      },
      "15": {
        start: {
          line: 89,
          column: 2
        },
        end: {
          line: 89,
          column: 31
        }
      },
      "16": {
        start: {
          line: 93,
          column: 30
        },
        end: {
          line: 108,
          column: 1
        }
      },
      "17": {
        start: {
          line: 94,
          column: 29
        },
        end: {
          line: 99,
          column: 3
        }
      },
      "18": {
        start: {
          line: 101,
          column: 18
        },
        end: {
          line: 101,
          column: 69
        }
      },
      "19": {
        start: {
          line: 101,
          column: 51
        },
        end: {
          line: 101,
          column: 68
        }
      },
      "20": {
        start: {
          line: 103,
          column: 2
        },
        end: {
          line: 105,
          column: 3
        }
      },
      "21": {
        start: {
          line: 104,
          column: 4
        },
        end: {
          line: 104,
          column: 85
        }
      },
      "22": {
        start: {
          line: 107,
          column: 2
        },
        end: {
          line: 107,
          column: 14
        }
      }
    },
    fnMap: {
      "0": {
        name: "validateEnv",
        decl: {
          start: {
            line: 35,
            column: 9
          },
          end: {
            line: 35,
            column: 20
          }
        },
        loc: {
          start: {
            line: 35,
            column: 23
          },
          end: {
            line: 42,
            column: 1
          }
        },
        line: 35
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 83,
            column: 29
          },
          end: {
            line: 83,
            column: 30
          }
        },
        loc: {
          start: {
            line: 83,
            column: 35
          },
          end: {
            line: 83,
            column: 52
          }
        },
        line: 83
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 84,
            column: 28
          },
          end: {
            line: 84,
            column: 29
          }
        },
        loc: {
          start: {
            line: 84,
            column: 34
          },
          end: {
            line: 84,
            column: 50
          }
        },
        line: 84
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 85,
            column: 33
          },
          end: {
            line: 85,
            column: 34
          }
        },
        loc: {
          start: {
            line: 85,
            column: 39
          },
          end: {
            line: 90,
            column: 1
          }
        },
        line: 85
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 93,
            column: 30
          },
          end: {
            line: 93,
            column: 31
          }
        },
        loc: {
          start: {
            line: 93,
            column: 36
          },
          end: {
            line: 108,
            column: 1
          }
        },
        line: 93
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 101,
            column: 44
          },
          end: {
            line: 101,
            column: 45
          }
        },
        loc: {
          start: {
            line: 101,
            column: 51
          },
          end: {
            line: 101,
            column: 68
          }
        },
        line: 101
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 86,
            column: 2
          },
          end: {
            line: 88,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 86,
            column: 2
          },
          end: {
            line: 88,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 86
      },
      "1": {
        loc: {
          start: {
            line: 103,
            column: 2
          },
          end: {
            line: 105,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 103,
            column: 2
          },
          end: {
            line: 105,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 103
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1f8392462d5b192fa3d4cda736596d304883c24c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_44vatrfhc = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_44vatrfhc();
/**
 * Centralized Configuration Management
 * 
 * This module provides a secure, centralized way to manage all application configuration.
 * It validates environment variables and provides type-safe access to configuration values.
 */
// Environment validation schema
const envSchema =
/* istanbul ignore next */
(cov_44vatrfhc().s[0]++,
/* istanbul ignore next */
_zod.
/* istanbul ignore next */
z.object({
  // AWS Configuration
  NEXT_PUBLIC_AWS_REGION:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().min(1, 'AWS region is required'),
  NEXT_PUBLIC_AWS_USER_POOLS_ID:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().min(1, 'User pool ID is required'),
  NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().min(1, 'User pool client ID is required'),
  NEXT_PUBLIC_AWS_COGNITO_DOMAIN:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().min(1, 'Cognito domain is required'),
  // Redirect URLs
  NEXT_PUBLIC_REDIRECT_SIGN_IN:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().url('Invalid sign-in redirect URL'),
  NEXT_PUBLIC_REDIRECT_SIGN_OUT:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().url('Invalid sign-out redirect URL'),
  // Database Configuration (server-side only)
  DB_USER:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().optional(),
  DB_PASSWORD:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().optional(),
  DB_HOST:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().optional(),
  DB_NAME:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().optional(),
  DATABASE_URL:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().optional(),
  DATABASE_SSL:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.string().optional(),
  // Environment
  NODE_ENV:
  /* istanbul ignore next */
  _zod.
  /* istanbul ignore next */
  z.enum(['development', 'production', 'test']).default('development')
}));

// Validate environment variables
function validateEnv() {
  /* istanbul ignore next */
  cov_44vatrfhc().f[0]++;
  cov_44vatrfhc().s[1]++;
  try {
    /* istanbul ignore next */
    cov_44vatrfhc().s[2]++;
    return envSchema.parse(process.env);
  } catch (error) {
    /* istanbul ignore next */
    cov_44vatrfhc().s[3]++;
    console.error('❌ Invalid environment configuration:', error);
    /* istanbul ignore next */
    cov_44vatrfhc().s[4]++;
    throw new Error('Environment validation failed. Please check your .env files.');
  }
}

// Get validated environment variables
const env =
/* istanbul ignore next */
exports.env = (cov_44vatrfhc().s[5]++, validateEnv());

// Public configuration (safe to expose to client)
const publicConfig =
/* istanbul ignore next */
exports.publicConfig = (cov_44vatrfhc().s[6]++, {
  aws: {
    region: env.NEXT_PUBLIC_AWS_REGION,
    userPoolId: env.NEXT_PUBLIC_AWS_USER_POOLS_ID,
    userPoolClientId: env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID,
    cognitoDomain: env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN
  },
  auth: {
    redirectSignIn: env.NEXT_PUBLIC_REDIRECT_SIGN_IN,
    redirectSignOut: env.NEXT_PUBLIC_REDIRECT_SIGN_OUT
  },
  app: {
    environment: env.NODE_ENV,
    isDevelopment: env.NODE_ENV === 'development',
    isProduction: env.NODE_ENV === 'production'
  }
});

// Server-side configuration (never expose to client)
const serverConfig =
/* istanbul ignore next */
exports.serverConfig = (cov_44vatrfhc().s[7]++, {
  database: {
    user: env.DB_USER,
    password: env.DB_PASSWORD,
    host: env.DB_HOST,
    name: env.DB_NAME,
    url: env.DATABASE_URL,
    ssl: env.DATABASE_SSL === 'true'
  }
});

// Type exports for better TypeScript support
/* istanbul ignore next */
// Utility functions
cov_44vatrfhc().s[8]++;
const getAuthConfig = () => {
  /* istanbul ignore next */
  cov_44vatrfhc().f[1]++;
  cov_44vatrfhc().s[9]++;
  return publicConfig.auth;
};
/* istanbul ignore next */
exports.getAuthConfig = getAuthConfig;
cov_44vatrfhc().s[10]++;
const getAwsConfig = () => {
  /* istanbul ignore next */
  cov_44vatrfhc().f[2]++;
  cov_44vatrfhc().s[11]++;
  return publicConfig.aws;
};
/* istanbul ignore next */
exports.getAwsConfig = getAwsConfig;
cov_44vatrfhc().s[12]++;
const getDatabaseConfig = () => {
  /* istanbul ignore next */
  cov_44vatrfhc().f[3]++;
  cov_44vatrfhc().s[13]++;
  if (typeof window !== 'undefined') {
    /* istanbul ignore next */
    cov_44vatrfhc().b[0][0]++;
    cov_44vatrfhc().s[14]++;
    throw new Error('Database configuration is not available on the client side');
  } else
  /* istanbul ignore next */
  {
    cov_44vatrfhc().b[0][1]++;
  }
  cov_44vatrfhc().s[15]++;
  return serverConfig.database;
};

// Configuration validation for runtime checks
/* istanbul ignore next */
exports.getDatabaseConfig = getDatabaseConfig;
cov_44vatrfhc().s[16]++;
const validateConfig = () => {
  /* istanbul ignore next */
  cov_44vatrfhc().f[4]++;
  const requiredPublicVars =
  /* istanbul ignore next */
  (cov_44vatrfhc().s[17]++, ['NEXT_PUBLIC_AWS_REGION', 'NEXT_PUBLIC_AWS_USER_POOLS_ID', 'NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID', 'NEXT_PUBLIC_AWS_COGNITO_DOMAIN']);
  const missing =
  /* istanbul ignore next */
  (cov_44vatrfhc().s[18]++, requiredPublicVars.filter(key => {
    /* istanbul ignore next */
    cov_44vatrfhc().f[5]++;
    cov_44vatrfhc().s[19]++;
    return !process.env[key];
  }));
  /* istanbul ignore next */
  cov_44vatrfhc().s[20]++;
  if (missing.length > 0) {
    /* istanbul ignore next */
    cov_44vatrfhc().b[1][0]++;
    cov_44vatrfhc().s[21]++;
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  } else
  /* istanbul ignore next */
  {
    cov_44vatrfhc().b[1][1]++;
  }
  cov_44vatrfhc().s[22]++;
  return true;
};

// Export environment for backward compatibility (to be removed)
/* istanbul ignore next */
exports.validateConfig = validateConfig;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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