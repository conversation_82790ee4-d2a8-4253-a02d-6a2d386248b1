{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "db", "mockDb", "validateJwtToken", "mockValidateJwtToken", "_interopRequireDefault", "require", "_defineProperty2", "_globals", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "default", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_interopRequireWildcard", "WeakMap", "n", "__esModule", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "jest", "query", "fn", "end", "describe", "mockRequest", "mockResponse", "beforeEach", "clearAllMocks", "method", "headers", "authorization", "cookies", "body", "status", "mockReturnThis", "json", "<PERSON><PERSON><PERSON><PERSON>", "mockResolvedValue", "sub", "email", "mockStatsData", "total_renewals", "renewals_due", "vendors", "annual_spend", "rows", "it", "handler", "Promise", "resolve", "then", "expect", "toHaveBeenCalledWith", "any", "String", "stringContaining", "success", "data", "totalRenewals", "renewalsDue", "annualSpend", "error", "mockRejectedValue", "Error", "objectContaining", "mockRenewalsData", "id", "name", "vendor", "renewal_date", "cost", "arrayContaining", "limit", "responseCall", "calls", "renewals", "renewal", "renewalDate", "toMatch", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "token", "startsWith", "substring", "toBe", "idToken", "headerToken", "cookieToken", "mockImplementation", "consoleSpy", "spyOn", "console", "mockRestore"], "sources": ["dashboard.test.ts"], "sourcesContent": ["/**\n * Dashboard API Tests\n * \n * Tests for dashboard API endpoints including authentication,\n * data validation, and error handling\n */\n\nimport { jest } from '@jest/globals'\nimport { testUtils } from '../utils/test-utils'\n\n// Mock the database\nconst mockDb = {\n  query: jest.fn(),\n  end: jest.fn(),\n}\n\njest.mock('@/lib/db', () => ({\n  db: mockDb,\n}))\n\n// Mock JWT validation\nconst mockValidateJwtToken = jest.fn()\njest.mock('@/lib/jwt-validator', () => ({\n  validateJwtToken: mockValidateJwtToken,\n}))\n\ndescribe('Dashboard API Endpoints', () => {\n  let mockRequest: any\n  let mockResponse: any\n\n  beforeEach(() => {\n    jest.clearAllMocks()\n\n    mockRequest = {\n      method: 'GET',\n      headers: {\n        authorization: 'Bearer valid-token',\n      },\n      cookies: {},\n      query: {},\n      body: {},\n    }\n\n    mockResponse = {\n      status: jest.fn().mockReturnThis(),\n      json: jest.fn().mockReturnThis(),\n      setHeader: jest.fn().mockReturnThis(),\n    }\n\n    // Mock successful JWT validation\n    mockValidateJwtToken.mockResolvedValue({\n      sub: 'test-user-id',\n      email: '<EMAIL>',\n      'custom:tenant_id': 'test-tenant-id',\n    })\n  })\n\n  describe('GET /api/dashboard/stats', () => {\n    const mockStatsData = {\n      total_renewals: 25,\n      renewals_due: 5,\n      vendors: 12,\n      annual_spend: 125000,\n    }\n\n    beforeEach(() => {\n      mockDb.query.mockResolvedValue({ rows: [mockStatsData] })\n    })\n\n    it('should return dashboard stats successfully', async () => {\n      // Import the handler dynamically to ensure mocks are applied\n      const { default: handler } = await import('@/app/api/dashboard/stats/route')\n\n      await handler(mockRequest)\n\n      expect(mockValidateJwtToken).toHaveBeenCalledWith(\n        'valid-token',\n        expect.any(String),\n        expect.any(String)\n      )\n\n      expect(mockDb.query).toHaveBeenCalledWith(\n        expect.stringContaining('SELECT'),\n        ['test-tenant-id']\n      )\n\n      expect(mockResponse.status).toHaveBeenCalledWith(200)\n      expect(mockResponse.json).toHaveBeenCalledWith({\n        success: true,\n        data: {\n          totalRenewals: 25,\n          renewalsDue: 5,\n          vendors: 12,\n          annualSpend: '$125,000',\n        },\n      })\n    })\n\n    it('should handle missing authorization header', async () => {\n      delete mockRequest.headers.authorization\n\n      const { default: handler } = await import('@/app/api/dashboard/stats/route')\n\n      await handler(mockRequest)\n\n      expect(mockResponse.status).toHaveBeenCalledWith(401)\n      expect(mockResponse.json).toHaveBeenCalledWith({\n        success: false,\n        error: 'Authorization token required',\n      })\n    })\n\n    it('should handle invalid JWT token', async () => {\n      mockValidateJwtToken.mockRejectedValue(new Error('Invalid token'))\n\n      const { default: handler } = await import('@/app/api/dashboard/stats/route')\n\n      await handler(mockRequest)\n\n      expect(mockResponse.status).toHaveBeenCalledWith(401)\n      expect(mockResponse.json).toHaveBeenCalledWith({\n        success: false,\n        error: 'Invalid or expired token',\n      })\n    })\n\n    it('should handle database errors', async () => {\n      mockDb.query.mockRejectedValue(new Error('Database connection failed'))\n\n      const { default: handler } = await import('@/app/api/dashboard/stats/route')\n\n      await handler(mockRequest)\n\n      expect(mockResponse.status).toHaveBeenCalledWith(500)\n      expect(mockResponse.json).toHaveBeenCalledWith({\n        success: false,\n        error: 'Internal server error',\n      })\n    })\n\n    it('should handle missing tenant ID in token', async () => {\n      mockValidateJwtToken.mockResolvedValue({\n        sub: 'test-user-id',\n        email: '<EMAIL>',\n        // Missing tenant_id\n      })\n\n      const { default: handler } = await import('@/app/api/dashboard/stats/route')\n\n      await handler(mockRequest)\n\n      expect(mockResponse.status).toHaveBeenCalledWith(400)\n      expect(mockResponse.json).toHaveBeenCalledWith({\n        success: false,\n        error: 'Tenant ID not found in token',\n      })\n    })\n\n    it('should handle empty database results', async () => {\n      mockDb.query.mockResolvedValue({ rows: [] })\n\n      const { default: handler } = await import('@/app/api/dashboard/stats/route')\n\n      await handler(mockRequest)\n\n      expect(mockResponse.status).toHaveBeenCalledWith(200)\n      expect(mockResponse.json).toHaveBeenCalledWith({\n        success: true,\n        data: {\n          totalRenewals: 0,\n          renewalsDue: 0,\n          vendors: 0,\n          annualSpend: '$0',\n        },\n      })\n    })\n\n    it('should format currency correctly', async () => {\n      mockDb.query.mockResolvedValue({\n        rows: [{ ...mockStatsData, annual_spend: 1234567.89 }],\n      })\n\n      const { default: handler } = await import('@/app/api/dashboard/stats/route')\n\n      await handler(mockRequest)\n\n      expect(mockResponse.json).toHaveBeenCalledWith({\n        success: true,\n        data: expect.objectContaining({\n          annualSpend: '$1,234,568', // Rounded to nearest dollar\n        }),\n      })\n    })\n\n    it('should handle non-GET methods', async () => {\n      mockRequest.method = 'POST'\n\n      const { default: handler } = await import('@/app/api/dashboard/stats/route')\n\n      await handler(mockRequest)\n\n      expect(mockResponse.status).toHaveBeenCalledWith(405)\n      expect(mockResponse.json).toHaveBeenCalledWith({\n        success: false,\n        error: 'Method not allowed',\n      })\n    })\n  })\n\n  describe('GET /api/dashboard/renewals', () => {\n    const mockRenewalsData = [\n      {\n        id: '1',\n        name: 'Microsoft Office 365',\n        vendor: 'Microsoft',\n        renewal_date: '2025-02-15',\n        cost: 1200,\n        status: 'active',\n      },\n      {\n        id: '2',\n        name: 'Adobe Creative Suite',\n        vendor: 'Adobe',\n        renewal_date: '2025-03-01',\n        cost: 2400,\n        status: 'pending',\n      },\n    ]\n\n    beforeEach(() => {\n      mockDb.query.mockResolvedValue({ rows: mockRenewalsData })\n    })\n\n    it('should return recent renewals successfully', async () => {\n      const { default: handler } = await import('@/app/api/dashboard/renewals/route')\n\n      await handler(mockRequest)\n\n      expect(mockDb.query).toHaveBeenCalledWith(\n        expect.stringContaining('SELECT'),\n        ['test-tenant-id']\n      )\n\n      expect(mockResponse.status).toHaveBeenCalledWith(200)\n      expect(mockResponse.json).toHaveBeenCalledWith({\n        success: true,\n        data: expect.arrayContaining([\n          expect.objectContaining({\n            id: '1',\n            name: 'Microsoft Office 365',\n            vendor: 'Microsoft',\n          }),\n        ]),\n      })\n    })\n\n    it('should handle query parameters for filtering', async () => {\n      mockRequest.query = { limit: '5', status: 'active' }\n\n      const { default: handler } = await import('@/app/api/dashboard/renewals/route')\n\n      await handler(mockRequest)\n\n      expect(mockDb.query).toHaveBeenCalledWith(\n        expect.stringContaining('LIMIT'),\n        expect.arrayContaining(['test-tenant-id', 'active', 5])\n      )\n    })\n\n    it('should validate query parameters', async () => {\n      mockRequest.query = { limit: 'invalid' }\n\n      const { default: handler } = await import('@/app/api/dashboard/renewals/route')\n\n      await handler(mockRequest)\n\n      expect(mockResponse.status).toHaveBeenCalledWith(400)\n      expect(mockResponse.json).toHaveBeenCalledWith({\n        success: false,\n        error: 'Invalid query parameters',\n      })\n    })\n\n    it('should handle date formatting correctly', async () => {\n      const { default: handler } = await import('@/app/api/dashboard/renewals/route')\n\n      await handler(mockRequest)\n\n      const responseCall = mockResponse.json.mock.calls[0][0]\n      const renewals = responseCall.data\n\n      renewals.forEach((renewal: any) => {\n        expect(renewal.renewalDate).toMatch(/^\\d{4}-\\d{2}-\\d{2}T/)\n      })\n    })\n  })\n\n  describe('Authentication Middleware', () => {\n    it('should extract token from Authorization header', () => {\n      const authHeader = 'Bearer test-token-123'\n      const token = authHeader.startsWith('Bearer ') \n        ? authHeader.substring(7) \n        : null\n\n      expect(token).toBe('test-token-123')\n    })\n\n    it('should extract token from cookies', () => {\n      mockRequest.cookies.idToken = 'cookie-token-123'\n      const token = mockRequest.cookies.idToken\n\n      expect(token).toBe('cookie-token-123')\n    })\n\n    it('should prioritize Authorization header over cookies', () => {\n      mockRequest.headers.authorization = 'Bearer header-token'\n      mockRequest.cookies.idToken = 'cookie-token'\n\n      const headerToken = mockRequest.headers.authorization?.substring(7)\n      const cookieToken = mockRequest.cookies.idToken\n      const token = headerToken || cookieToken\n\n      expect(token).toBe('header-token')\n    })\n\n    it('should handle malformed Authorization header', () => {\n      mockRequest.headers.authorization = 'InvalidFormat token'\n      \n      const authHeader = mockRequest.headers.authorization\n      const token = authHeader?.startsWith('Bearer ') \n        ? authHeader.substring(7) \n        : null\n\n      expect(token).toBe(null)\n    })\n  })\n\n  describe('Error Handling', () => {\n    it('should handle unexpected errors gracefully', async () => {\n      mockValidateJwtToken.mockImplementation(() => {\n        throw new Error('Unexpected error')\n      })\n\n      const { default: handler } = await import('@/app/api/dashboard/stats/route')\n\n      await handler(mockRequest)\n\n      expect(mockResponse.status).toHaveBeenCalledWith(500)\n      expect(mockResponse.json).toHaveBeenCalledWith({\n        success: false,\n        error: 'Internal server error',\n      })\n    })\n\n    it('should not expose sensitive error details', async () => {\n      mockDb.query.mockRejectedValue(new Error('Database password is wrong'))\n\n      const { default: handler } = await import('@/app/api/dashboard/stats/route')\n\n      await handler(mockRequest)\n\n      expect(mockResponse.json).toHaveBeenCalledWith({\n        success: false,\n        error: 'Internal server error', // Generic error message\n      })\n    })\n\n    it('should log errors for debugging', async () => {\n      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()\n      mockDb.query.mockRejectedValue(new Error('Database error'))\n\n      const { default: handler } = await import('@/app/api/dashboard/stats/route')\n\n      await handler(mockRequest)\n\n      expect(consoleSpy).toHaveBeenCalledWith(\n        expect.stringContaining('API Error'),\n        expect.any(Error)\n      )\n\n      consoleSpy.mockRestore()\n    })\n  })\n\n  describe('Security', () => {\n    it('should validate tenant isolation', async () => {\n      const { default: handler } = await import('@/app/api/dashboard/stats/route')\n\n      await handler(mockRequest)\n\n      // Ensure query includes tenant_id filter\n      expect(mockDb.query).toHaveBeenCalledWith(\n        expect.stringContaining('tenant_id = $1'),\n        expect.arrayContaining(['test-tenant-id'])\n      )\n    })\n\n    it('should prevent SQL injection', async () => {\n      mockRequest.query = { status: \"'; DROP TABLE renewals; --\" }\n\n      const { default: handler } = await import('@/app/api/dashboard/renewals/route')\n\n      await handler(mockRequest)\n\n      // Should use parameterized queries\n      expect(mockDb.query).toHaveBeenCalledWith(\n        expect.any(String),\n        expect.arrayContaining([expect.any(String)])\n      )\n    })\n\n    it('should set security headers', async () => {\n      const { default: handler } = await import('@/app/api/dashboard/stats/route')\n\n      await handler(mockRequest)\n\n      expect(mockResponse.setHeader).toHaveBeenCalledWith(\n        'Cache-Control',\n        expect.stringContaining('no-cache')\n      )\n    })\n  })\n})\n"], "mappings": ";;AAgBAA,WAAA,GAAKC,IAAI,CAAC,UAAU,EAAE,OAAO;EAC3BC,EAAE,EAAEC;AACN,CAAC,CAAC,CAAC;;AAEH;;AAEAH,WAAA,GAAKC,IAAI,CAAC,qBAAqB,EAAE,OAAO;EACtCG,gBAAgB,EAAEC;AACpB,CAAC,CAAC,CAAC;AAAA,IAAAC,sBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAjBH,IAAAE,QAAA,GAAAF,OAAA;AAAoC,SAAAG,QAAAC,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAJ,CAAA,OAAAG,MAAA,CAAAE,qBAAA,QAAAC,CAAA,GAAAH,MAAA,CAAAE,qBAAA,CAAAL,CAAA,GAAAC,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAC,MAAA,WAAAN,CAAA,WAAAE,MAAA,CAAAK,wBAAA,CAAAR,CAAA,EAAAC,CAAA,EAAAQ,UAAA,OAAAP,CAAA,CAAAQ,IAAA,CAAAC,KAAA,CAAAT,CAAA,EAAAI,CAAA,YAAAJ,CAAA;AAAA,SAAAU,cAAAZ,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAY,SAAA,CAAAC,MAAA,EAAAb,CAAA,UAAAC,CAAA,WAAAW,SAAA,CAAAZ,CAAA,IAAAY,SAAA,CAAAZ,CAAA,QAAAA,CAAA,OAAAF,OAAA,CAAAI,MAAA,CAAAD,CAAA,OAAAa,OAAA,WAAAd,CAAA,QAAAJ,gBAAA,CAAAmB,OAAA,EAAAhB,CAAA,EAAAC,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAc,yBAAA,GAAAd,MAAA,CAAAe,gBAAA,CAAAlB,CAAA,EAAAG,MAAA,CAAAc,yBAAA,CAAAf,CAAA,KAAAH,OAAA,CAAAI,MAAA,CAAAD,CAAA,GAAAa,OAAA,WAAAd,CAAA,IAAAE,MAAA,CAAAgB,cAAA,CAAAnB,CAAA,EAAAC,CAAA,EAAAE,MAAA,CAAAK,wBAAA,CAAAN,CAAA,EAAAD,CAAA,iBAAAD,CAAA;AAAA,SAAAoB,wBAAApB,CAAA,EAAAE,CAAA,6BAAAmB,OAAA,MAAApB,CAAA,OAAAoB,OAAA,IAAAC,CAAA,OAAAD,OAAA,YAAAD,uBAAA,YAAAA,CAAApB,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAuB,UAAA,SAAAvB,CAAA,MAAAM,CAAA,EAAAkB,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAV,OAAA,EAAAhB,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAyB,CAAA,MAAAnB,CAAA,GAAAJ,CAAA,GAAAoB,CAAA,GAAArB,CAAA,QAAAK,CAAA,CAAAqB,GAAA,CAAA3B,CAAA,UAAAM,CAAA,CAAAsB,GAAA,CAAA5B,CAAA,GAAAM,CAAA,CAAAuB,GAAA,CAAA7B,CAAA,EAAAyB,CAAA,gBAAAvB,CAAA,IAAAF,CAAA,gBAAAE,CAAA,OAAA4B,cAAA,CAAAC,IAAA,CAAA/B,CAAA,EAAAE,CAAA,OAAAsB,CAAA,IAAAlB,CAAA,GAAAH,MAAA,CAAAgB,cAAA,KAAAhB,MAAA,CAAAK,wBAAA,CAAAR,CAAA,EAAAE,CAAA,OAAAsB,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAvB,CAAA,CAAAmB,CAAA,EAAAvB,CAAA,EAAAsB,CAAA,IAAAC,CAAA,CAAAvB,CAAA,IAAAF,CAAA,CAAAE,CAAA,WAAAuB,CAAA,KAAAzB,CAAA,EAAAE,CAAA;AAAA,SAAAb,YAAA;EAAA;IAAA2C;EAAA,IAAApC,OAAA;EAAAP,WAAA,GAAAA,CAAA,KAAA2C,IAAA;EAAA,OAAAA,IAAA;AAAA;AAPpC;AACA;AACA;AACA;AACA;AACA;AAKA;AACA,MAAMxC,MAAM,GAAG;EACbyC,KAAK,EAAED,aAAI,CAACE,EAAE,CAAC,CAAC;EAChBC,GAAG,EAAEH,aAAI,CAACE,EAAE,CAAC;AACf,CAAC;AAOD,MAAMxC,oBAAoB,GAAGsC,aAAI,CAACE,EAAE,CAAC,CAAC;AAKtCE,QAAQ,CAAC,yBAAyB,EAAE,MAAM;EACxC,IAAIC,WAAgB;EACpB,IAAIC,YAAiB;EAErBC,UAAU,CAAC,MAAM;IACfP,aAAI,CAACQ,aAAa,CAAC,CAAC;IAEpBH,WAAW,GAAG;MACZI,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;QACPC,aAAa,EAAE;MACjB,CAAC;MACDC,OAAO,EAAE,CAAC,CAAC;MACXX,KAAK,EAAE,CAAC,CAAC;MACTY,IAAI,EAAE,CAAC;IACT,CAAC;IAEDP,YAAY,GAAG;MACbQ,MAAM,EAAEd,aAAI,CAACE,EAAE,CAAC,CAAC,CAACa,cAAc,CAAC,CAAC;MAClCC,IAAI,EAAEhB,aAAI,CAACE,EAAE,CAAC,CAAC,CAACa,cAAc,CAAC,CAAC;MAChCE,SAAS,EAAEjB,aAAI,CAACE,EAAE,CAAC,CAAC,CAACa,cAAc,CAAC;IACtC,CAAC;;IAED;IACArD,oBAAoB,CAACwD,iBAAiB,CAAC;MACrCC,GAAG,EAAE,cAAc;MACnBC,KAAK,EAAE,kBAAkB;MACzB,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFhB,QAAQ,CAAC,0BAA0B,EAAE,MAAM;IACzC,MAAMiB,aAAa,GAAG;MACpBC,cAAc,EAAE,EAAE;MAClBC,YAAY,EAAE,CAAC;MACfC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE;IAChB,CAAC;IAEDlB,UAAU,CAAC,MAAM;MACf/C,MAAM,CAACyC,KAAK,CAACiB,iBAAiB,CAAC;QAAEQ,IAAI,EAAE,CAACL,aAAa;MAAE,CAAC,CAAC;IAC3D,CAAC,CAAC;IAEFM,EAAE,CAAC,4CAA4C,EAAE,YAAY;MAC3D;MACA,MAAM;QAAE3C,OAAO,EAAE4C;MAAQ,CAAC,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAA3C,uBAAA,CAAAxB,OAAA,CAAa,iCAAiC,GAAC;MAE5E,MAAMgE,OAAO,CAACvB,WAAW,CAAC;MAE1B2B,MAAM,CAACtE,oBAAoB,CAAC,CAACuE,oBAAoB,CAC/C,aAAa,EACbD,MAAM,CAACE,GAAG,CAACC,MAAM,CAAC,EAClBH,MAAM,CAACE,GAAG,CAACC,MAAM,CACnB,CAAC;MAEDH,MAAM,CAACxE,MAAM,CAACyC,KAAK,CAAC,CAACgC,oBAAoB,CACvCD,MAAM,CAACI,gBAAgB,CAAC,QAAQ,CAAC,EACjC,CAAC,gBAAgB,CACnB,CAAC;MAEDJ,MAAM,CAAC1B,YAAY,CAACQ,MAAM,CAAC,CAACmB,oBAAoB,CAAC,GAAG,CAAC;MACrDD,MAAM,CAAC1B,YAAY,CAACU,IAAI,CAAC,CAACiB,oBAAoB,CAAC;QAC7CI,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE;UACJC,aAAa,EAAE,EAAE;UACjBC,WAAW,EAAE,CAAC;UACdhB,OAAO,EAAE,EAAE;UACXiB,WAAW,EAAE;QACf;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFd,EAAE,CAAC,4CAA4C,EAAE,YAAY;MAC3D,OAAOtB,WAAW,CAACK,OAAO,CAACC,aAAa;MAExC,MAAM;QAAE3B,OAAO,EAAE4C;MAAQ,CAAC,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAA3C,uBAAA,CAAAxB,OAAA,CAAa,iCAAiC,GAAC;MAE5E,MAAMgE,OAAO,CAACvB,WAAW,CAAC;MAE1B2B,MAAM,CAAC1B,YAAY,CAACQ,MAAM,CAAC,CAACmB,oBAAoB,CAAC,GAAG,CAAC;MACrDD,MAAM,CAAC1B,YAAY,CAACU,IAAI,CAAC,CAACiB,oBAAoB,CAAC;QAC7CI,OAAO,EAAE,KAAK;QACdK,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFf,EAAE,CAAC,iCAAiC,EAAE,YAAY;MAChDjE,oBAAoB,CAACiF,iBAAiB,CAAC,IAAIC,KAAK,CAAC,eAAe,CAAC,CAAC;MAElE,MAAM;QAAE5D,OAAO,EAAE4C;MAAQ,CAAC,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAA3C,uBAAA,CAAAxB,OAAA,CAAa,iCAAiC,GAAC;MAE5E,MAAMgE,OAAO,CAACvB,WAAW,CAAC;MAE1B2B,MAAM,CAAC1B,YAAY,CAACQ,MAAM,CAAC,CAACmB,oBAAoB,CAAC,GAAG,CAAC;MACrDD,MAAM,CAAC1B,YAAY,CAACU,IAAI,CAAC,CAACiB,oBAAoB,CAAC;QAC7CI,OAAO,EAAE,KAAK;QACdK,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFf,EAAE,CAAC,+BAA+B,EAAE,YAAY;MAC9CnE,MAAM,CAACyC,KAAK,CAAC0C,iBAAiB,CAAC,IAAIC,KAAK,CAAC,4BAA4B,CAAC,CAAC;MAEvE,MAAM;QAAE5D,OAAO,EAAE4C;MAAQ,CAAC,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAA3C,uBAAA,CAAAxB,OAAA,CAAa,iCAAiC,GAAC;MAE5E,MAAMgE,OAAO,CAACvB,WAAW,CAAC;MAE1B2B,MAAM,CAAC1B,YAAY,CAACQ,MAAM,CAAC,CAACmB,oBAAoB,CAAC,GAAG,CAAC;MACrDD,MAAM,CAAC1B,YAAY,CAACU,IAAI,CAAC,CAACiB,oBAAoB,CAAC;QAC7CI,OAAO,EAAE,KAAK;QACdK,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFf,EAAE,CAAC,0CAA0C,EAAE,YAAY;MACzDjE,oBAAoB,CAACwD,iBAAiB,CAAC;QACrCC,GAAG,EAAE,cAAc;QACnBC,KAAK,EAAE;QACP;MACF,CAAC,CAAC;MAEF,MAAM;QAAEpC,OAAO,EAAE4C;MAAQ,CAAC,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAA3C,uBAAA,CAAAxB,OAAA,CAAa,iCAAiC,GAAC;MAE5E,MAAMgE,OAAO,CAACvB,WAAW,CAAC;MAE1B2B,MAAM,CAAC1B,YAAY,CAACQ,MAAM,CAAC,CAACmB,oBAAoB,CAAC,GAAG,CAAC;MACrDD,MAAM,CAAC1B,YAAY,CAACU,IAAI,CAAC,CAACiB,oBAAoB,CAAC;QAC7CI,OAAO,EAAE,KAAK;QACdK,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFf,EAAE,CAAC,sCAAsC,EAAE,YAAY;MACrDnE,MAAM,CAACyC,KAAK,CAACiB,iBAAiB,CAAC;QAAEQ,IAAI,EAAE;MAAG,CAAC,CAAC;MAE5C,MAAM;QAAE1C,OAAO,EAAE4C;MAAQ,CAAC,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAA3C,uBAAA,CAAAxB,OAAA,CAAa,iCAAiC,GAAC;MAE5E,MAAMgE,OAAO,CAACvB,WAAW,CAAC;MAE1B2B,MAAM,CAAC1B,YAAY,CAACQ,MAAM,CAAC,CAACmB,oBAAoB,CAAC,GAAG,CAAC;MACrDD,MAAM,CAAC1B,YAAY,CAACU,IAAI,CAAC,CAACiB,oBAAoB,CAAC;QAC7CI,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE;UACJC,aAAa,EAAE,CAAC;UAChBC,WAAW,EAAE,CAAC;UACdhB,OAAO,EAAE,CAAC;UACViB,WAAW,EAAE;QACf;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFd,EAAE,CAAC,kCAAkC,EAAE,YAAY;MACjDnE,MAAM,CAACyC,KAAK,CAACiB,iBAAiB,CAAC;QAC7BQ,IAAI,EAAE,CAAA9C,aAAA,CAAAA,aAAA,KAAMyC,aAAa;UAAEI,YAAY,EAAE;QAAU;MACrD,CAAC,CAAC;MAEF,MAAM;QAAEzC,OAAO,EAAE4C;MAAQ,CAAC,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAA3C,uBAAA,CAAAxB,OAAA,CAAa,iCAAiC,GAAC;MAE5E,MAAMgE,OAAO,CAACvB,WAAW,CAAC;MAE1B2B,MAAM,CAAC1B,YAAY,CAACU,IAAI,CAAC,CAACiB,oBAAoB,CAAC;QAC7CI,OAAO,EAAE,IAAI;QACbC,IAAI,EAAEN,MAAM,CAACa,gBAAgB,CAAC;UAC5BJ,WAAW,EAAE,YAAY,CAAE;QAC7B,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFd,EAAE,CAAC,+BAA+B,EAAE,YAAY;MAC9CtB,WAAW,CAACI,MAAM,GAAG,MAAM;MAE3B,MAAM;QAAEzB,OAAO,EAAE4C;MAAQ,CAAC,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAA3C,uBAAA,CAAAxB,OAAA,CAAa,iCAAiC,GAAC;MAE5E,MAAMgE,OAAO,CAACvB,WAAW,CAAC;MAE1B2B,MAAM,CAAC1B,YAAY,CAACQ,MAAM,CAAC,CAACmB,oBAAoB,CAAC,GAAG,CAAC;MACrDD,MAAM,CAAC1B,YAAY,CAACU,IAAI,CAAC,CAACiB,oBAAoB,CAAC;QAC7CI,OAAO,EAAE,KAAK;QACdK,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFtC,QAAQ,CAAC,6BAA6B,EAAE,MAAM;IAC5C,MAAM0C,gBAAgB,GAAG,CACvB;MACEC,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,MAAM,EAAE,WAAW;MACnBC,YAAY,EAAE,YAAY;MAC1BC,IAAI,EAAE,IAAI;MACVrC,MAAM,EAAE;IACV,CAAC,EACD;MACEiC,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,MAAM,EAAE,OAAO;MACfC,YAAY,EAAE,YAAY;MAC1BC,IAAI,EAAE,IAAI;MACVrC,MAAM,EAAE;IACV,CAAC,CACF;IAEDP,UAAU,CAAC,MAAM;MACf/C,MAAM,CAACyC,KAAK,CAACiB,iBAAiB,CAAC;QAAEQ,IAAI,EAAEoB;MAAiB,CAAC,CAAC;IAC5D,CAAC,CAAC;IAEFnB,EAAE,CAAC,4CAA4C,EAAE,YAAY;MAC3D,MAAM;QAAE3C,OAAO,EAAE4C;MAAQ,CAAC,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAA3C,uBAAA,CAAAxB,OAAA,CAAa,oCAAoC,GAAC;MAE/E,MAAMgE,OAAO,CAACvB,WAAW,CAAC;MAE1B2B,MAAM,CAACxE,MAAM,CAACyC,KAAK,CAAC,CAACgC,oBAAoB,CACvCD,MAAM,CAACI,gBAAgB,CAAC,QAAQ,CAAC,EACjC,CAAC,gBAAgB,CACnB,CAAC;MAEDJ,MAAM,CAAC1B,YAAY,CAACQ,MAAM,CAAC,CAACmB,oBAAoB,CAAC,GAAG,CAAC;MACrDD,MAAM,CAAC1B,YAAY,CAACU,IAAI,CAAC,CAACiB,oBAAoB,CAAC;QAC7CI,OAAO,EAAE,IAAI;QACbC,IAAI,EAAEN,MAAM,CAACoB,eAAe,CAAC,CAC3BpB,MAAM,CAACa,gBAAgB,CAAC;UACtBE,EAAE,EAAE,GAAG;UACPC,IAAI,EAAE,sBAAsB;UAC5BC,MAAM,EAAE;QACV,CAAC,CAAC,CACH;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFtB,EAAE,CAAC,8CAA8C,EAAE,YAAY;MAC7DtB,WAAW,CAACJ,KAAK,GAAG;QAAEoD,KAAK,EAAE,GAAG;QAAEvC,MAAM,EAAE;MAAS,CAAC;MAEpD,MAAM;QAAE9B,OAAO,EAAE4C;MAAQ,CAAC,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAA3C,uBAAA,CAAAxB,OAAA,CAAa,oCAAoC,GAAC;MAE/E,MAAMgE,OAAO,CAACvB,WAAW,CAAC;MAE1B2B,MAAM,CAACxE,MAAM,CAACyC,KAAK,CAAC,CAACgC,oBAAoB,CACvCD,MAAM,CAACI,gBAAgB,CAAC,OAAO,CAAC,EAChCJ,MAAM,CAACoB,eAAe,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC,CAAC,CACxD,CAAC;IACH,CAAC,CAAC;IAEFzB,EAAE,CAAC,kCAAkC,EAAE,YAAY;MACjDtB,WAAW,CAACJ,KAAK,GAAG;QAAEoD,KAAK,EAAE;MAAU,CAAC;MAExC,MAAM;QAAErE,OAAO,EAAE4C;MAAQ,CAAC,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAA3C,uBAAA,CAAAxB,OAAA,CAAa,oCAAoC,GAAC;MAE/E,MAAMgE,OAAO,CAACvB,WAAW,CAAC;MAE1B2B,MAAM,CAAC1B,YAAY,CAACQ,MAAM,CAAC,CAACmB,oBAAoB,CAAC,GAAG,CAAC;MACrDD,MAAM,CAAC1B,YAAY,CAACU,IAAI,CAAC,CAACiB,oBAAoB,CAAC;QAC7CI,OAAO,EAAE,KAAK;QACdK,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFf,EAAE,CAAC,yCAAyC,EAAE,YAAY;MACxD,MAAM;QAAE3C,OAAO,EAAE4C;MAAQ,CAAC,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAA3C,uBAAA,CAAAxB,OAAA,CAAa,oCAAoC,GAAC;MAE/E,MAAMgE,OAAO,CAACvB,WAAW,CAAC;MAE1B,MAAMiD,YAAY,GAAGhD,YAAY,CAACU,IAAI,CAAC1D,IAAI,CAACiG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,MAAMC,QAAQ,GAAGF,YAAY,CAAChB,IAAI;MAElCkB,QAAQ,CAACzE,OAAO,CAAE0E,OAAY,IAAK;QACjCzB,MAAM,CAACyB,OAAO,CAACC,WAAW,CAAC,CAACC,OAAO,CAAC,qBAAqB,CAAC;MAC5D,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFvD,QAAQ,CAAC,2BAA2B,EAAE,MAAM;IAC1CuB,EAAE,CAAC,gDAAgD,EAAE,MAAM;MACzD,MAAMiC,UAAU,GAAG,uBAAuB;MAC1C,MAAMC,KAAK,GAAGD,UAAU,CAACE,UAAU,CAAC,SAAS,CAAC,GAC1CF,UAAU,CAACG,SAAS,CAAC,CAAC,CAAC,GACvB,IAAI;MAER/B,MAAM,CAAC6B,KAAK,CAAC,CAACG,IAAI,CAAC,gBAAgB,CAAC;IACtC,CAAC,CAAC;IAEFrC,EAAE,CAAC,mCAAmC,EAAE,MAAM;MAC5CtB,WAAW,CAACO,OAAO,CAACqD,OAAO,GAAG,kBAAkB;MAChD,MAAMJ,KAAK,GAAGxD,WAAW,CAACO,OAAO,CAACqD,OAAO;MAEzCjC,MAAM,CAAC6B,KAAK,CAAC,CAACG,IAAI,CAAC,kBAAkB,CAAC;IACxC,CAAC,CAAC;IAEFrC,EAAE,CAAC,qDAAqD,EAAE,MAAM;MAC9DtB,WAAW,CAACK,OAAO,CAACC,aAAa,GAAG,qBAAqB;MACzDN,WAAW,CAACO,OAAO,CAACqD,OAAO,GAAG,cAAc;MAE5C,MAAMC,WAAW,GAAG7D,WAAW,CAACK,OAAO,CAACC,aAAa,EAAEoD,SAAS,CAAC,CAAC,CAAC;MACnE,MAAMI,WAAW,GAAG9D,WAAW,CAACO,OAAO,CAACqD,OAAO;MAC/C,MAAMJ,KAAK,GAAGK,WAAW,IAAIC,WAAW;MAExCnC,MAAM,CAAC6B,KAAK,CAAC,CAACG,IAAI,CAAC,cAAc,CAAC;IACpC,CAAC,CAAC;IAEFrC,EAAE,CAAC,8CAA8C,EAAE,MAAM;MACvDtB,WAAW,CAACK,OAAO,CAACC,aAAa,GAAG,qBAAqB;MAEzD,MAAMiD,UAAU,GAAGvD,WAAW,CAACK,OAAO,CAACC,aAAa;MACpD,MAAMkD,KAAK,GAAGD,UAAU,EAAEE,UAAU,CAAC,SAAS,CAAC,GAC3CF,UAAU,CAACG,SAAS,CAAC,CAAC,CAAC,GACvB,IAAI;MAER/B,MAAM,CAAC6B,KAAK,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF5D,QAAQ,CAAC,gBAAgB,EAAE,MAAM;IAC/BuB,EAAE,CAAC,4CAA4C,EAAE,YAAY;MAC3DjE,oBAAoB,CAAC0G,kBAAkB,CAAC,MAAM;QAC5C,MAAM,IAAIxB,KAAK,CAAC,kBAAkB,CAAC;MACrC,CAAC,CAAC;MAEF,MAAM;QAAE5D,OAAO,EAAE4C;MAAQ,CAAC,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAA3C,uBAAA,CAAAxB,OAAA,CAAa,iCAAiC,GAAC;MAE5E,MAAMgE,OAAO,CAACvB,WAAW,CAAC;MAE1B2B,MAAM,CAAC1B,YAAY,CAACQ,MAAM,CAAC,CAACmB,oBAAoB,CAAC,GAAG,CAAC;MACrDD,MAAM,CAAC1B,YAAY,CAACU,IAAI,CAAC,CAACiB,oBAAoB,CAAC;QAC7CI,OAAO,EAAE,KAAK;QACdK,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFf,EAAE,CAAC,2CAA2C,EAAE,YAAY;MAC1DnE,MAAM,CAACyC,KAAK,CAAC0C,iBAAiB,CAAC,IAAIC,KAAK,CAAC,4BAA4B,CAAC,CAAC;MAEvE,MAAM;QAAE5D,OAAO,EAAE4C;MAAQ,CAAC,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAA3C,uBAAA,CAAAxB,OAAA,CAAa,iCAAiC,GAAC;MAE5E,MAAMgE,OAAO,CAACvB,WAAW,CAAC;MAE1B2B,MAAM,CAAC1B,YAAY,CAACU,IAAI,CAAC,CAACiB,oBAAoB,CAAC;QAC7CI,OAAO,EAAE,KAAK;QACdK,KAAK,EAAE,uBAAuB,CAAE;MAClC,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFf,EAAE,CAAC,iCAAiC,EAAE,YAAY;MAChD,MAAM0C,UAAU,GAAGrE,aAAI,CAACsE,KAAK,CAACC,OAAO,EAAE,OAAO,CAAC,CAACH,kBAAkB,CAAC,CAAC;MACpE5G,MAAM,CAACyC,KAAK,CAAC0C,iBAAiB,CAAC,IAAIC,KAAK,CAAC,gBAAgB,CAAC,CAAC;MAE3D,MAAM;QAAE5D,OAAO,EAAE4C;MAAQ,CAAC,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAA3C,uBAAA,CAAAxB,OAAA,CAAa,iCAAiC,GAAC;MAE5E,MAAMgE,OAAO,CAACvB,WAAW,CAAC;MAE1B2B,MAAM,CAACqC,UAAU,CAAC,CAACpC,oBAAoB,CACrCD,MAAM,CAACI,gBAAgB,CAAC,WAAW,CAAC,EACpCJ,MAAM,CAACE,GAAG,CAACU,KAAK,CAClB,CAAC;MAEDyB,UAAU,CAACG,WAAW,CAAC,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFpE,QAAQ,CAAC,UAAU,EAAE,MAAM;IACzBuB,EAAE,CAAC,kCAAkC,EAAE,YAAY;MACjD,MAAM;QAAE3C,OAAO,EAAE4C;MAAQ,CAAC,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAA3C,uBAAA,CAAAxB,OAAA,CAAa,iCAAiC,GAAC;MAE5E,MAAMgE,OAAO,CAACvB,WAAW,CAAC;;MAE1B;MACA2B,MAAM,CAACxE,MAAM,CAACyC,KAAK,CAAC,CAACgC,oBAAoB,CACvCD,MAAM,CAACI,gBAAgB,CAAC,gBAAgB,CAAC,EACzCJ,MAAM,CAACoB,eAAe,CAAC,CAAC,gBAAgB,CAAC,CAC3C,CAAC;IACH,CAAC,CAAC;IAEFzB,EAAE,CAAC,8BAA8B,EAAE,YAAY;MAC7CtB,WAAW,CAACJ,KAAK,GAAG;QAAEa,MAAM,EAAE;MAA6B,CAAC;MAE5D,MAAM;QAAE9B,OAAO,EAAE4C;MAAQ,CAAC,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAA3C,uBAAA,CAAAxB,OAAA,CAAa,oCAAoC,GAAC;MAE/E,MAAMgE,OAAO,CAACvB,WAAW,CAAC;;MAE1B;MACA2B,MAAM,CAACxE,MAAM,CAACyC,KAAK,CAAC,CAACgC,oBAAoB,CACvCD,MAAM,CAACE,GAAG,CAACC,MAAM,CAAC,EAClBH,MAAM,CAACoB,eAAe,CAAC,CAACpB,MAAM,CAACE,GAAG,CAACC,MAAM,CAAC,CAAC,CAC7C,CAAC;IACH,CAAC,CAAC;IAEFR,EAAE,CAAC,6BAA6B,EAAE,YAAY;MAC5C,MAAM;QAAE3C,OAAO,EAAE4C;MAAQ,CAAC,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAA3C,uBAAA,CAAAxB,OAAA,CAAa,iCAAiC,GAAC;MAE5E,MAAMgE,OAAO,CAACvB,WAAW,CAAC;MAE1B2B,MAAM,CAAC1B,YAAY,CAACW,SAAS,CAAC,CAACgB,oBAAoB,CACjD,eAAe,EACfD,MAAM,CAACI,gBAAgB,CAAC,UAAU,CACpC,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}