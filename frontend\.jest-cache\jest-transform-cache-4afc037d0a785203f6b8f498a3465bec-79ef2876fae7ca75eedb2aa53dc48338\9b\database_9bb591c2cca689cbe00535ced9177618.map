{"version": 3, "names": ["cov_2fd3e7m4j", "actualCoverage", "getDbPool", "queryCache", "s", "Map", "execute<PERSON>uery", "query", "params", "b", "options", "f", "client", "pool", "connect", "timeout", "schema", "startTime", "Date", "now", "result", "duration", "console", "warn", "substring", "length", "success", "data", "rows", "rowCount", "error", "db<PERSON><PERSON>r", "message", "code", "detail", "getDatabaseErrorMessage", "errorCode", "release", "executeQuerySingle", "executeTransaction", "queries", "results", "push", "reduce", "sum", "rollbackError", "schemaExists", "schemaName", "exists", "tableExists", "tableName", "getConnectionHealth", "healthy", "totalConnections", "totalCount", "idleConnections", "idleCount", "waitingCount", "closeDatabase", "end", "log", "buildWhereClause", "conditions", "startIndex", "clauses", "paramIndex", "key", "value", "Object", "entries", "undefined", "Array", "isArray", "placeholders", "map", "join", "clause", "nextIndex", "escapeIdentifier", "identifier", "replace"], "sources": ["database.ts"], "sourcesContent": ["/**\n * Database Utilities and Query Optimization\n * \n * This module provides optimized database operations with proper error handling,\n * connection management, and query optimization for the multi-tenant application.\n */\n\nimport { Pool, PoolClient, QueryResult } from 'pg';\nimport { getDbPool } from './db-config';\n\n// Query result interfaces\nexport interface QueryOptions {\n  timeout?: number;\n  retries?: number;\n  schema?: string;\n}\n\nexport interface DatabaseError extends Error {\n  code?: string;\n  detail?: string;\n  hint?: string;\n  position?: string;\n  internalPosition?: string;\n  internalQuery?: string;\n  where?: string;\n  schema?: string;\n  table?: string;\n  column?: string;\n  dataType?: string;\n  constraint?: string;\n}\n\n// Database operation result\nexport interface DbResult<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  errorCode?: string;\n  rowCount?: number;\n}\n\n// Query cache for prepared statements\nconst queryCache = new Map<string, string>();\n\n/**\n * Execute a query with proper error handling and optimization\n */\nexport async function executeQuery<T = any>(\n  query: string,\n  params: any[] = [],\n  options: QueryOptions = {}\n): Promise<DbResult<T[]>> {\n  let client: PoolClient | null = null;\n  \n  try {\n    const pool = await getDbPool();\n    client = await pool.connect();\n    \n    // Set query timeout if specified\n    if (options.timeout) {\n      await client.query(`SET statement_timeout = ${options.timeout}`);\n    }\n    \n    // Set schema if specified (for multi-tenant support)\n    if (options.schema) {\n      await client.query(`SET search_path TO \"${options.schema}\", public`);\n    }\n    \n    const startTime = Date.now();\n    const result: QueryResult = await client.query(query, params);\n    const duration = Date.now() - startTime;\n    \n    // Log slow queries (> 1 second)\n    if (duration > 1000) {\n      console.warn(`Slow query detected (${duration}ms):`, {\n        query: query.substring(0, 100) + '...',\n        params: params.length,\n        schema: options.schema\n      });\n    }\n    \n    return {\n      success: true,\n      data: result.rows,\n      rowCount: result.rowCount || 0\n    };\n    \n  } catch (error) {\n    const dbError = error as DatabaseError;\n    console.error('Database query error:', {\n      error: dbError.message,\n      code: dbError.code,\n      detail: dbError.detail,\n      query: query.substring(0, 100) + '...',\n      schema: options.schema\n    });\n    \n    return {\n      success: false,\n      error: getDatabaseErrorMessage(dbError),\n      errorCode: dbError.code || 'UNKNOWN_ERROR'\n    };\n  } finally {\n    if (client) {\n      client.release();\n    }\n  }\n}\n\n/**\n * Execute a single query and return the first row\n */\nexport async function executeQuerySingle<T = any>(\n  query: string,\n  params: any[] = [],\n  options: QueryOptions = {}\n): Promise<DbResult<T>> {\n  const result = await executeQuery<T>(query, params, options);\n  \n  if (!result.success) {\n    return result;\n  }\n  \n  return {\n    success: true,\n    data: result.data?.[0] || null,\n    rowCount: result.rowCount\n  };\n}\n\n/**\n * Execute a transaction with multiple queries\n */\nexport async function executeTransaction<T = any>(\n  queries: Array<{ query: string; params?: any[] }>,\n  options: QueryOptions = {}\n): Promise<DbResult<T[]>> {\n  let client: PoolClient | null = null;\n  \n  try {\n    const pool = await getDbPool();\n    client = await pool.connect();\n    \n    // Set schema if specified\n    if (options.schema) {\n      await client.query(`SET search_path TO \"${options.schema}\", public`);\n    }\n    \n    await client.query('BEGIN');\n    \n    const results: any[] = [];\n    \n    for (const { query, params = [] } of queries) {\n      const result = await client.query(query, params);\n      results.push(result.rows);\n    }\n    \n    await client.query('COMMIT');\n    \n    return {\n      success: true,\n      data: results,\n      rowCount: results.reduce((sum, rows) => sum + rows.length, 0)\n    };\n    \n  } catch (error) {\n    if (client) {\n      try {\n        await client.query('ROLLBACK');\n      } catch (rollbackError) {\n        console.error('Error rolling back transaction:', rollbackError);\n      }\n    }\n    \n    const dbError = error as DatabaseError;\n    console.error('Transaction error:', {\n      error: dbError.message,\n      code: dbError.code,\n      detail: dbError.detail,\n      schema: options.schema\n    });\n    \n    return {\n      success: false,\n      error: getDatabaseErrorMessage(dbError),\n      errorCode: dbError.code || 'TRANSACTION_ERROR'\n    };\n  } finally {\n    if (client) {\n      client.release();\n    }\n  }\n}\n\n/**\n * Check if a schema exists\n */\nexport async function schemaExists(schemaName: string): Promise<boolean> {\n  const result = await executeQuerySingle<{ exists: boolean }>(\n    'SELECT EXISTS(SELECT 1 FROM information_schema.schemata WHERE schema_name = $1) as exists',\n    [schemaName]\n  );\n  \n  return result.success && result.data?.exists === true;\n}\n\n/**\n * Check if a table exists in a schema\n */\nexport async function tableExists(tableName: string, schemaName = 'public'): Promise<boolean> {\n  const result = await executeQuerySingle<{ exists: boolean }>(\n    'SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_schema = $1 AND table_name = $2) as exists',\n    [schemaName, tableName]\n  );\n  \n  return result.success && result.data?.exists === true;\n}\n\n/**\n * Get database connection health status\n */\nexport async function getConnectionHealth(): Promise<{\n  healthy: boolean;\n  totalConnections: number;\n  idleConnections: number;\n  waitingCount: number;\n}> {\n  try {\n    const pool = await getDbPool();\n    \n    return {\n      healthy: true,\n      totalConnections: pool.totalCount,\n      idleConnections: pool.idleCount,\n      waitingCount: pool.waitingCount\n    };\n  } catch (error) {\n    console.error('Database health check failed:', error);\n    return {\n      healthy: false,\n      totalConnections: 0,\n      idleConnections: 0,\n      waitingCount: 0\n    };\n  }\n}\n\n/**\n * Close database connections (for graceful shutdown)\n */\nexport async function closeDatabase(): Promise<void> {\n  try {\n    const pool = await getDbPool();\n    await pool.end();\n    console.log('Database connections closed successfully');\n  } catch (error) {\n    console.error('Error closing database connections:', error);\n  }\n}\n\n/**\n * Get user-friendly error message from database error\n */\nfunction getDatabaseErrorMessage(error: DatabaseError): string {\n  switch (error.code) {\n    case '23505': // unique_violation\n      return 'A record with this information already exists';\n    case '23503': // foreign_key_violation\n      return 'Referenced record does not exist';\n    case '23502': // not_null_violation\n      return 'Required field is missing';\n    case '23514': // check_violation\n      return 'Invalid data provided';\n    case '42P01': // undefined_table\n      return 'Database table not found';\n    case '42703': // undefined_column\n      return 'Database column not found';\n    case '42883': // undefined_function\n      return 'Database function not found';\n    case '28P01': // invalid_password\n      return 'Database authentication failed';\n    case '3D000': // invalid_catalog_name\n      return 'Database not found';\n    case '08006': // connection_failure\n      return 'Database connection failed';\n    case '57P03': // cannot_connect_now\n      return 'Database is temporarily unavailable';\n    case '53300': // too_many_connections\n      return 'Database connection limit reached';\n    default:\n      return 'A database error occurred';\n  }\n}\n\n/**\n * Build WHERE clause with proper parameter binding\n */\nexport function buildWhereClause(\n  conditions: Record<string, any>,\n  startIndex = 1\n): { clause: string; params: any[]; nextIndex: number } {\n  const params: any[] = [];\n  const clauses: string[] = [];\n  let paramIndex = startIndex;\n  \n  for (const [key, value] of Object.entries(conditions)) {\n    if (value !== undefined && value !== null) {\n      if (Array.isArray(value)) {\n        // Handle IN clause\n        const placeholders = value.map(() => `$${paramIndex++}`).join(', ');\n        clauses.push(`${key} IN (${placeholders})`);\n        params.push(...value);\n      } else {\n        clauses.push(`${key} = $${paramIndex++}`);\n        params.push(value);\n      }\n    }\n  }\n  \n  return {\n    clause: clauses.length > 0 ? `WHERE ${clauses.join(' AND ')}` : '',\n    params,\n    nextIndex: paramIndex\n  };\n}\n\n/**\n * Escape identifier for safe SQL construction\n */\nexport function escapeIdentifier(identifier: string): string {\n  return `\"${identifier.replace(/\"/g, '\"\"')}\"`;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAfZ;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASE,SAAS,QAAQ,aAAa;;AAEvC;;AAsBA;;AASA;AACA,MAAMC,UAAU;AAAA;AAAA,CAAAH,aAAA,GAAAI,CAAA,OAAG,IAAIC,GAAG,CAAiB,CAAC;;AAE5C;AACA;AACA;AACA,OAAO,eAAeC,YAAYA,CAChCC,KAAa,EACbC,MAAa;AAAA;AAAA,CAAAR,aAAA,GAAAS,CAAA,UAAG,EAAE,GAClBC,OAAqB;AAAA;AAAA,CAAAV,aAAA,GAAAS,CAAA,UAAG,CAAC,CAAC,GACF;EAAA;EAAAT,aAAA,GAAAW,CAAA;EACxB,IAAIC,MAAyB;EAAA;EAAA,CAAAZ,aAAA,GAAAI,CAAA,OAAG,IAAI;EAAC;EAAAJ,aAAA,GAAAI,CAAA;EAErC,IAAI;IACF,MAAMS,IAAI;IAAA;IAAA,CAAAb,aAAA,GAAAI,CAAA,OAAG,MAAMF,SAAS,CAAC,CAAC;IAAC;IAAAF,aAAA,GAAAI,CAAA;IAC/BQ,MAAM,GAAG,MAAMC,IAAI,CAACC,OAAO,CAAC,CAAC;;IAE7B;IAAA;IAAAd,aAAA,GAAAI,CAAA;IACA,IAAIM,OAAO,CAACK,OAAO,EAAE;MAAA;MAAAf,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MACnB,MAAMQ,MAAM,CAACL,KAAK,CAAC,2BAA2BG,OAAO,CAACK,OAAO,EAAE,CAAC;IAClE,CAAC;IAAA;IAAA;MAAAf,aAAA,GAAAS,CAAA;IAAA;;IAED;IAAAT,aAAA,GAAAI,CAAA;IACA,IAAIM,OAAO,CAACM,MAAM,EAAE;MAAA;MAAAhB,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MAClB,MAAMQ,MAAM,CAACL,KAAK,CAAC,uBAAuBG,OAAO,CAACM,MAAM,WAAW,CAAC;IACtE,CAAC;IAAA;IAAA;MAAAhB,aAAA,GAAAS,CAAA;IAAA;IAED,MAAMQ,SAAS;IAAA;IAAA,CAAAjB,aAAA,GAAAI,CAAA,OAAGc,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B,MAAMC,MAAmB;IAAA;IAAA,CAAApB,aAAA,GAAAI,CAAA,QAAG,MAAMQ,MAAM,CAACL,KAAK,CAACA,KAAK,EAAEC,MAAM,CAAC;IAC7D,MAAMa,QAAQ;IAAA;IAAA,CAAArB,aAAA,GAAAI,CAAA,QAAGc,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;;IAEvC;IAAA;IAAAjB,aAAA,GAAAI,CAAA;IACA,IAAIiB,QAAQ,GAAG,IAAI,EAAE;MAAA;MAAArB,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MACnBkB,OAAO,CAACC,IAAI,CAAC,wBAAwBF,QAAQ,MAAM,EAAE;QACnDd,KAAK,EAAEA,KAAK,CAACiB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;QACtChB,MAAM,EAAEA,MAAM,CAACiB,MAAM;QACrBT,MAAM,EAAEN,OAAO,CAACM;MAClB,CAAC,CAAC;IACJ,CAAC;IAAA;IAAA;MAAAhB,aAAA,GAAAS,CAAA;IAAA;IAAAT,aAAA,GAAAI,CAAA;IAED,OAAO;MACLsB,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEP,MAAM,CAACQ,IAAI;MACjBC,QAAQ;MAAE;MAAA,CAAA7B,aAAA,GAAAS,CAAA,UAAAW,MAAM,CAACS,QAAQ;MAAA;MAAA,CAAA7B,aAAA,GAAAS,CAAA,UAAI,CAAC;IAChC,CAAC;EAEH,CAAC,CAAC,OAAOqB,KAAK,EAAE;IACd,MAAMC,OAAO;IAAA;IAAA,CAAA/B,aAAA,GAAAI,CAAA,QAAG0B,KAAK,CAAiB;IAAC;IAAA9B,aAAA,GAAAI,CAAA;IACvCkB,OAAO,CAACQ,KAAK,CAAC,uBAAuB,EAAE;MACrCA,KAAK,EAAEC,OAAO,CAACC,OAAO;MACtBC,IAAI,EAAEF,OAAO,CAACE,IAAI;MAClBC,MAAM,EAAEH,OAAO,CAACG,MAAM;MACtB3B,KAAK,EAAEA,KAAK,CAACiB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;MACtCR,MAAM,EAAEN,OAAO,CAACM;IAClB,CAAC,CAAC;IAAC;IAAAhB,aAAA,GAAAI,CAAA;IAEH,OAAO;MACLsB,OAAO,EAAE,KAAK;MACdI,KAAK,EAAEK,uBAAuB,CAACJ,OAAO,CAAC;MACvCK,SAAS;MAAE;MAAA,CAAApC,aAAA,GAAAS,CAAA,UAAAsB,OAAO,CAACE,IAAI;MAAA;MAAA,CAAAjC,aAAA,GAAAS,CAAA,UAAI,eAAe;IAC5C,CAAC;EACH,CAAC,SAAS;IAAA;IAAAT,aAAA,GAAAI,CAAA;IACR,IAAIQ,MAAM,EAAE;MAAA;MAAAZ,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MACVQ,MAAM,CAACyB,OAAO,CAAC,CAAC;IAClB,CAAC;IAAA;IAAA;MAAArC,aAAA,GAAAS,CAAA;IAAA;EACH;AACF;;AAEA;AACA;AACA;AACA,OAAO,eAAe6B,kBAAkBA,CACtC/B,KAAa,EACbC,MAAa;AAAA;AAAA,CAAAR,aAAA,GAAAS,CAAA,UAAG,EAAE,GAClBC,OAAqB;AAAA;AAAA,CAAAV,aAAA,GAAAS,CAAA,UAAG,CAAC,CAAC,GACJ;EAAA;EAAAT,aAAA,GAAAW,CAAA;EACtB,MAAMS,MAAM;EAAA;EAAA,CAAApB,aAAA,GAAAI,CAAA,QAAG,MAAME,YAAY,CAAIC,KAAK,EAAEC,MAAM,EAAEE,OAAO,CAAC;EAAC;EAAAV,aAAA,GAAAI,CAAA;EAE7D,IAAI,CAACgB,MAAM,CAACM,OAAO,EAAE;IAAA;IAAA1B,aAAA,GAAAS,CAAA;IAAAT,aAAA,GAAAI,CAAA;IACnB,OAAOgB,MAAM;EACf,CAAC;EAAA;EAAA;IAAApB,aAAA,GAAAS,CAAA;EAAA;EAAAT,aAAA,GAAAI,CAAA;EAED,OAAO;IACLsB,OAAO,EAAE,IAAI;IACbC,IAAI;IAAE;IAAA,CAAA3B,aAAA,GAAAS,CAAA,WAAAW,MAAM,CAACO,IAAI,GAAG,CAAC,CAAC;IAAA;IAAA,CAAA3B,aAAA,GAAAS,CAAA,WAAI,IAAI;IAC9BoB,QAAQ,EAAET,MAAM,CAACS;EACnB,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,eAAeU,kBAAkBA,CACtCC,OAAiD,EACjD9B,OAAqB;AAAA;AAAA,CAAAV,aAAA,GAAAS,CAAA,WAAG,CAAC,CAAC,GACF;EAAA;EAAAT,aAAA,GAAAW,CAAA;EACxB,IAAIC,MAAyB;EAAA;EAAA,CAAAZ,aAAA,GAAAI,CAAA,QAAG,IAAI;EAAC;EAAAJ,aAAA,GAAAI,CAAA;EAErC,IAAI;IACF,MAAMS,IAAI;IAAA;IAAA,CAAAb,aAAA,GAAAI,CAAA,QAAG,MAAMF,SAAS,CAAC,CAAC;IAAC;IAAAF,aAAA,GAAAI,CAAA;IAC/BQ,MAAM,GAAG,MAAMC,IAAI,CAACC,OAAO,CAAC,CAAC;;IAE7B;IAAA;IAAAd,aAAA,GAAAI,CAAA;IACA,IAAIM,OAAO,CAACM,MAAM,EAAE;MAAA;MAAAhB,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MAClB,MAAMQ,MAAM,CAACL,KAAK,CAAC,uBAAuBG,OAAO,CAACM,MAAM,WAAW,CAAC;IACtE,CAAC;IAAA;IAAA;MAAAhB,aAAA,GAAAS,CAAA;IAAA;IAAAT,aAAA,GAAAI,CAAA;IAED,MAAMQ,MAAM,CAACL,KAAK,CAAC,OAAO,CAAC;IAE3B,MAAMkC,OAAc;IAAA;IAAA,CAAAzC,aAAA,GAAAI,CAAA,QAAG,EAAE;IAAC;IAAAJ,aAAA,GAAAI,CAAA;IAE1B,KAAK,MAAM;MAAEG,KAAK;MAAEC,MAAM;MAAA;MAAA,CAAAR,aAAA,GAAAS,CAAA,WAAG,EAAE;IAAC,CAAC,IAAI+B,OAAO,EAAE;MAC5C,MAAMpB,MAAM;MAAA;MAAA,CAAApB,aAAA,GAAAI,CAAA,QAAG,MAAMQ,MAAM,CAACL,KAAK,CAACA,KAAK,EAAEC,MAAM,CAAC;MAAC;MAAAR,aAAA,GAAAI,CAAA;MACjDqC,OAAO,CAACC,IAAI,CAACtB,MAAM,CAACQ,IAAI,CAAC;IAC3B;IAAC;IAAA5B,aAAA,GAAAI,CAAA;IAED,MAAMQ,MAAM,CAACL,KAAK,CAAC,QAAQ,CAAC;IAAC;IAAAP,aAAA,GAAAI,CAAA;IAE7B,OAAO;MACLsB,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEc,OAAO;MACbZ,QAAQ,EAAEY,OAAO,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEhB,IAAI,KAAK;QAAA;QAAA5B,aAAA,GAAAW,CAAA;QAAAX,aAAA,GAAAI,CAAA;QAAA,OAAAwC,GAAG,GAAGhB,IAAI,CAACH,MAAM;MAAD,CAAC,EAAE,CAAC;IAC9D,CAAC;EAEH,CAAC,CAAC,OAAOK,KAAK,EAAE;IAAA;IAAA9B,aAAA,GAAAI,CAAA;IACd,IAAIQ,MAAM,EAAE;MAAA;MAAAZ,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MACV,IAAI;QAAA;QAAAJ,aAAA,GAAAI,CAAA;QACF,MAAMQ,MAAM,CAACL,KAAK,CAAC,UAAU,CAAC;MAChC,CAAC,CAAC,OAAOsC,aAAa,EAAE;QAAA;QAAA7C,aAAA,GAAAI,CAAA;QACtBkB,OAAO,CAACQ,KAAK,CAAC,iCAAiC,EAAEe,aAAa,CAAC;MACjE;IACF,CAAC;IAAA;IAAA;MAAA7C,aAAA,GAAAS,CAAA;IAAA;IAED,MAAMsB,OAAO;IAAA;IAAA,CAAA/B,aAAA,GAAAI,CAAA,QAAG0B,KAAK,CAAiB;IAAC;IAAA9B,aAAA,GAAAI,CAAA;IACvCkB,OAAO,CAACQ,KAAK,CAAC,oBAAoB,EAAE;MAClCA,KAAK,EAAEC,OAAO,CAACC,OAAO;MACtBC,IAAI,EAAEF,OAAO,CAACE,IAAI;MAClBC,MAAM,EAAEH,OAAO,CAACG,MAAM;MACtBlB,MAAM,EAAEN,OAAO,CAACM;IAClB,CAAC,CAAC;IAAC;IAAAhB,aAAA,GAAAI,CAAA;IAEH,OAAO;MACLsB,OAAO,EAAE,KAAK;MACdI,KAAK,EAAEK,uBAAuB,CAACJ,OAAO,CAAC;MACvCK,SAAS;MAAE;MAAA,CAAApC,aAAA,GAAAS,CAAA,WAAAsB,OAAO,CAACE,IAAI;MAAA;MAAA,CAAAjC,aAAA,GAAAS,CAAA,WAAI,mBAAmB;IAChD,CAAC;EACH,CAAC,SAAS;IAAA;IAAAT,aAAA,GAAAI,CAAA;IACR,IAAIQ,MAAM,EAAE;MAAA;MAAAZ,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MACVQ,MAAM,CAACyB,OAAO,CAAC,CAAC;IAClB,CAAC;IAAA;IAAA;MAAArC,aAAA,GAAAS,CAAA;IAAA;EACH;AACF;;AAEA;AACA;AACA;AACA,OAAO,eAAeqC,YAAYA,CAACC,UAAkB,EAAoB;EAAA;EAAA/C,aAAA,GAAAW,CAAA;EACvE,MAAMS,MAAM;EAAA;EAAA,CAAApB,aAAA,GAAAI,CAAA,QAAG,MAAMkC,kBAAkB,CACrC,2FAA2F,EAC3F,CAACS,UAAU,CACb,CAAC;EAAC;EAAA/C,aAAA,GAAAI,CAAA;EAEF,OAAO,2BAAAJ,aAAA,GAAAS,CAAA,WAAAW,MAAM,CAACM,OAAO;EAAA;EAAA,CAAA1B,aAAA,GAAAS,CAAA,WAAIW,MAAM,CAACO,IAAI,EAAEqB,MAAM,KAAK,IAAI;AACvD;;AAEA;AACA;AACA;AACA,OAAO,eAAeC,WAAWA,CAACC,SAAiB,EAAEH,UAAU;AAAA;AAAA,CAAA/C,aAAA,GAAAS,CAAA,WAAG,QAAQ,GAAoB;EAAA;EAAAT,aAAA,GAAAW,CAAA;EAC5F,MAAMS,MAAM;EAAA;EAAA,CAAApB,aAAA,GAAAI,CAAA,QAAG,MAAMkC,kBAAkB,CACrC,8GAA8G,EAC9G,CAACS,UAAU,EAAEG,SAAS,CACxB,CAAC;EAAC;EAAAlD,aAAA,GAAAI,CAAA;EAEF,OAAO,2BAAAJ,aAAA,GAAAS,CAAA,WAAAW,MAAM,CAACM,OAAO;EAAA;EAAA,CAAA1B,aAAA,GAAAS,CAAA,WAAIW,MAAM,CAACO,IAAI,EAAEqB,MAAM,KAAK,IAAI;AACvD;;AAEA;AACA;AACA;AACA,OAAO,eAAeG,mBAAmBA,CAAA,EAKtC;EAAA;EAAAnD,aAAA,GAAAW,CAAA;EAAAX,aAAA,GAAAI,CAAA;EACD,IAAI;IACF,MAAMS,IAAI;IAAA;IAAA,CAAAb,aAAA,GAAAI,CAAA,QAAG,MAAMF,SAAS,CAAC,CAAC;IAAC;IAAAF,aAAA,GAAAI,CAAA;IAE/B,OAAO;MACLgD,OAAO,EAAE,IAAI;MACbC,gBAAgB,EAAExC,IAAI,CAACyC,UAAU;MACjCC,eAAe,EAAE1C,IAAI,CAAC2C,SAAS;MAC/BC,YAAY,EAAE5C,IAAI,CAAC4C;IACrB,CAAC;EACH,CAAC,CAAC,OAAO3B,KAAK,EAAE;IAAA;IAAA9B,aAAA,GAAAI,CAAA;IACdkB,OAAO,CAACQ,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IAAC;IAAA9B,aAAA,GAAAI,CAAA;IACtD,OAAO;MACLgD,OAAO,EAAE,KAAK;MACdC,gBAAgB,EAAE,CAAC;MACnBE,eAAe,EAAE,CAAC;MAClBE,YAAY,EAAE;IAChB,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA,OAAO,eAAeC,aAAaA,CAAA,EAAkB;EAAA;EAAA1D,aAAA,GAAAW,CAAA;EAAAX,aAAA,GAAAI,CAAA;EACnD,IAAI;IACF,MAAMS,IAAI;IAAA;IAAA,CAAAb,aAAA,GAAAI,CAAA,QAAG,MAAMF,SAAS,CAAC,CAAC;IAAC;IAAAF,aAAA,GAAAI,CAAA;IAC/B,MAAMS,IAAI,CAAC8C,GAAG,CAAC,CAAC;IAAC;IAAA3D,aAAA,GAAAI,CAAA;IACjBkB,OAAO,CAACsC,GAAG,CAAC,0CAA0C,CAAC;EACzD,CAAC,CAAC,OAAO9B,KAAK,EAAE;IAAA;IAAA9B,aAAA,GAAAI,CAAA;IACdkB,OAAO,CAACQ,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;EAC7D;AACF;;AAEA;AACA;AACA;AACA,SAASK,uBAAuBA,CAACL,KAAoB,EAAU;EAAA;EAAA9B,aAAA,GAAAW,CAAA;EAAAX,aAAA,GAAAI,CAAA;EAC7D,QAAQ0B,KAAK,CAACG,IAAI;IAChB,KAAK,OAAO;MAAA;MAAAjC,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MAAE;MACZ,OAAO,+CAA+C;IACxD,KAAK,OAAO;MAAA;MAAAJ,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MAAE;MACZ,OAAO,kCAAkC;IAC3C,KAAK,OAAO;MAAA;MAAAJ,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MAAE;MACZ,OAAO,2BAA2B;IACpC,KAAK,OAAO;MAAA;MAAAJ,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MAAE;MACZ,OAAO,uBAAuB;IAChC,KAAK,OAAO;MAAA;MAAAJ,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MAAE;MACZ,OAAO,0BAA0B;IACnC,KAAK,OAAO;MAAA;MAAAJ,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MAAE;MACZ,OAAO,2BAA2B;IACpC,KAAK,OAAO;MAAA;MAAAJ,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MAAE;MACZ,OAAO,6BAA6B;IACtC,KAAK,OAAO;MAAA;MAAAJ,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MAAE;MACZ,OAAO,gCAAgC;IACzC,KAAK,OAAO;MAAA;MAAAJ,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MAAE;MACZ,OAAO,oBAAoB;IAC7B,KAAK,OAAO;MAAA;MAAAJ,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MAAE;MACZ,OAAO,4BAA4B;IACrC,KAAK,OAAO;MAAA;MAAAJ,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MAAE;MACZ,OAAO,qCAAqC;IAC9C,KAAK,OAAO;MAAA;MAAAJ,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MAAE;MACZ,OAAO,mCAAmC;IAC5C;MAAA;MAAAJ,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MACE,OAAO,2BAA2B;EACtC;AACF;;AAEA;AACA;AACA;AACA,OAAO,SAASyD,gBAAgBA,CAC9BC,UAA+B,EAC/BC,UAAU;AAAA;AAAA,CAAA/D,aAAA,GAAAS,CAAA,WAAG,CAAC,GACwC;EAAA;EAAAT,aAAA,GAAAW,CAAA;EACtD,MAAMH,MAAa;EAAA;EAAA,CAAAR,aAAA,GAAAI,CAAA,QAAG,EAAE;EACxB,MAAM4D,OAAiB;EAAA;EAAA,CAAAhE,aAAA,GAAAI,CAAA,QAAG,EAAE;EAC5B,IAAI6D,UAAU;EAAA;EAAA,CAAAjE,aAAA,GAAAI,CAAA,QAAG2D,UAAU;EAAC;EAAA/D,aAAA,GAAAI,CAAA;EAE5B,KAAK,MAAM,CAAC8D,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACP,UAAU,CAAC,EAAE;IAAA;IAAA9D,aAAA,GAAAI,CAAA;IACrD;IAAI;IAAA,CAAAJ,aAAA,GAAAS,CAAA,WAAA0D,KAAK,KAAKG,SAAS;IAAA;IAAA,CAAAtE,aAAA,GAAAS,CAAA,WAAI0D,KAAK,KAAK,IAAI,GAAE;MAAA;MAAAnE,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAI,CAAA;MACzC,IAAImE,KAAK,CAACC,OAAO,CAACL,KAAK,CAAC,EAAE;QAAA;QAAAnE,aAAA,GAAAS,CAAA;QACxB;QACA,MAAMgE,YAAY;QAAA;QAAA,CAAAzE,aAAA,GAAAI,CAAA,QAAG+D,KAAK,CAACO,GAAG,CAAC,MAAM;UAAA;UAAA1E,aAAA,GAAAW,CAAA;UAAAX,aAAA,GAAAI,CAAA;UAAA,WAAI6D,UAAU,EAAE,EAAE;QAAD,CAAC,CAAC,CAACU,IAAI,CAAC,IAAI,CAAC;QAAC;QAAA3E,aAAA,GAAAI,CAAA;QACpE4D,OAAO,CAACtB,IAAI,CAAC,GAAGwB,GAAG,QAAQO,YAAY,GAAG,CAAC;QAAC;QAAAzE,aAAA,GAAAI,CAAA;QAC5CI,MAAM,CAACkC,IAAI,CAAC,GAAGyB,KAAK,CAAC;MACvB,CAAC,MAAM;QAAA;QAAAnE,aAAA,GAAAS,CAAA;QAAAT,aAAA,GAAAI,CAAA;QACL4D,OAAO,CAACtB,IAAI,CAAC,GAAGwB,GAAG,OAAOD,UAAU,EAAE,EAAE,CAAC;QAAC;QAAAjE,aAAA,GAAAI,CAAA;QAC1CI,MAAM,CAACkC,IAAI,CAACyB,KAAK,CAAC;MACpB;IACF,CAAC;IAAA;IAAA;MAAAnE,aAAA,GAAAS,CAAA;IAAA;EACH;EAAC;EAAAT,aAAA,GAAAI,CAAA;EAED,OAAO;IACLwE,MAAM,EAAEZ,OAAO,CAACvC,MAAM,GAAG,CAAC;IAAA;IAAA,CAAAzB,aAAA,GAAAS,CAAA,WAAG,SAASuD,OAAO,CAACW,IAAI,CAAC,OAAO,CAAC,EAAE;IAAA;IAAA,CAAA3E,aAAA,GAAAS,CAAA,WAAG,EAAE;IAClED,MAAM;IACNqE,SAAS,EAAEZ;EACb,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASa,gBAAgBA,CAACC,UAAkB,EAAU;EAAA;EAAA/E,aAAA,GAAAW,CAAA;EAAAX,aAAA,GAAAI,CAAA;EAC3D,OAAO,IAAI2E,UAAU,CAACC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG;AAC9C", "ignoreList": []}