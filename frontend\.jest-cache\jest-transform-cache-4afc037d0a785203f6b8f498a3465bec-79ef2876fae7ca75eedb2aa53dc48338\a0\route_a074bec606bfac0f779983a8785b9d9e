09e5eff555d415deea1c0ca451a741c8
/* istanbul ignore next */
function cov_1oh1qd3dcz() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\clients\\domain\\route.ts";
  var hash = "86d621843040ececc0d21d1cb0b5c05dc8a92176";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\clients\\domain\\route.ts",
    statementMap: {
      "0": {
        start: {
          line: 11,
          column: 19
        },
        end: {
          line: 47,
          column: 2
        }
      },
      "1": {
        start: {
          line: 13,
          column: 21
        },
        end: {
          line: 13,
          column: 40
        }
      },
      "2": {
        start: {
          line: 14,
          column: 2
        },
        end: {
          line: 16,
          column: 3
        }
      },
      "3": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 32
        }
      },
      "4": {
        start: {
          line: 18,
          column: 18
        },
        end: {
          line: 18,
          column: 37
        }
      },
      "5": {
        start: {
          line: 21,
          column: 16
        },
        end: {
          line: 21,
          column: 29
        }
      },
      "6": {
        start: {
          line: 22,
          column: 2
        },
        end: {
          line: 28,
          column: 3
        }
      },
      "7": {
        start: {
          line: 23,
          column: 4
        },
        end: {
          line: 27,
          column: 6
        }
      },
      "8": {
        start: {
          line: 30,
          column: 17
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "9": {
        start: {
          line: 32,
          column: 2
        },
        end: {
          line: 41,
          column: 3
        }
      },
      "10": {
        start: {
          line: 33,
          column: 23
        },
        end: {
          line: 33,
          column: 113
        }
      },
      "11": {
        start: {
          line: 34,
          column: 25
        },
        end: {
          line: 34,
          column: 112
        }
      },
      "12": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 40,
          column: 6
        }
      },
      "13": {
        start: {
          line: 44,
          column: 2
        },
        end: {
          line: 46,
          column: 50
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 11,
            column: 37
          },
          end: {
            line: 11,
            column: 38
          }
        },
        loc: {
          start: {
            line: 11,
            column: 49
          },
          end: {
            line: 47,
            column: 1
          }
        },
        line: 11
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 14,
            column: 2
          },
          end: {
            line: 16,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 14,
            column: 2
          },
          end: {
            line: 16,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 14
      },
      "1": {
        loc: {
          start: {
            line: 22,
            column: 2
          },
          end: {
            line: 28,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 2
          },
          end: {
            line: 28,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "2": {
        loc: {
          start: {
            line: 32,
            column: 2
          },
          end: {
            line: 41,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 2
          },
          end: {
            line: 41,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      },
      "3": {
        loc: {
          start: {
            line: 33,
            column: 23
          },
          end: {
            line: 33,
            column: 113
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 33,
            column: 58
          },
          end: {
            line: 33,
            column: 78
          }
        }, {
          start: {
            line: 33,
            column: 81
          },
          end: {
            line: 33,
            column: 113
          }
        }],
        line: 33
      },
      "4": {
        loc: {
          start: {
            line: 34,
            column: 25
          },
          end: {
            line: 34,
            column: 112
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 34,
            column: 60
          },
          end: {
            line: 34,
            column: 82
          }
        }, {
          start: {
            line: 34,
            column: 85
          },
          end: {
            line: 34,
            column: 112
          }
        }],
        line: 34
      },
      "5": {
        loc: {
          start: {
            line: 37,
            column: 6
          },
          end: {
            line: 37,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 6
          },
          end: {
            line: 37,
            column: 18
          }
        }, {
          start: {
            line: 37,
            column: 22
          },
          end: {
            line: 37,
            column: 46
          }
        }],
        line: 37
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "86d621843040ececc0d21d1cb0b5c05dc8a92176"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1oh1qd3dcz = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1oh1qd3dcz();
import { getClientByEmailDomain } from '@/lib/clients';
import { requireAuth } from '@/lib/auth-middleware';
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus, withErrorHandling } from '@/lib/api-response';
export const GET =
/* istanbul ignore next */
(cov_1oh1qd3dcz().s[0]++, withErrorHandling(async () => {
  /* istanbul ignore next */
  cov_1oh1qd3dcz().f[0]++;
  // Verify authentication and get session
  const authResult =
  /* istanbul ignore next */
  (cov_1oh1qd3dcz().s[1]++, await requireAuth());
  /* istanbul ignore next */
  cov_1oh1qd3dcz().s[2]++;
  if (!authResult.success) {
    /* istanbul ignore next */
    cov_1oh1qd3dcz().b[0][0]++;
    cov_1oh1qd3dcz().s[3]++;
    return authResult.response;
  } else
  /* istanbul ignore next */
  {
    cov_1oh1qd3dcz().b[0][1]++;
  }
  const session =
  /* istanbul ignore next */
  (cov_1oh1qd3dcz().s[4]++, authResult.session);

  // Get email from session instead of URL parameters for security
  const email =
  /* istanbul ignore next */
  (cov_1oh1qd3dcz().s[5]++, session.email);
  /* istanbul ignore next */
  cov_1oh1qd3dcz().s[6]++;
  if (!email) {
    /* istanbul ignore next */
    cov_1oh1qd3dcz().b[1][0]++;
    cov_1oh1qd3dcz().s[7]++;
    return createErrorResponse('No email found in session', ApiErrorCode.INVALID_INPUT, HttpStatus.BAD_REQUEST);
  } else
  /* istanbul ignore next */
  {
    cov_1oh1qd3dcz().b[1][1]++;
  }
  const result =
  /* istanbul ignore next */
  (cov_1oh1qd3dcz().s[8]++, await getClientByEmailDomain(email));
  /* istanbul ignore next */
  cov_1oh1qd3dcz().s[9]++;
  if (!result.success) {
    /* istanbul ignore next */
    cov_1oh1qd3dcz().b[2][0]++;
    const statusCode =
    /* istanbul ignore next */
    (cov_1oh1qd3dcz().s[10]++, result.errorCode === 'NOT_FOUND' ?
    /* istanbul ignore next */
    (cov_1oh1qd3dcz().b[3][0]++, HttpStatus.NOT_FOUND) :
    /* istanbul ignore next */
    (cov_1oh1qd3dcz().b[3][1]++, HttpStatus.INTERNAL_SERVER_ERROR));
    const apiErrorCode =
    /* istanbul ignore next */
    (cov_1oh1qd3dcz().s[11]++, result.errorCode === 'NOT_FOUND' ?
    /* istanbul ignore next */
    (cov_1oh1qd3dcz().b[4][0]++, ApiErrorCode.NOT_FOUND) :
    /* istanbul ignore next */
    (cov_1oh1qd3dcz().b[4][1]++, ApiErrorCode.DATABASE_ERROR));
    /* istanbul ignore next */
    cov_1oh1qd3dcz().s[12]++;
    return createErrorResponse(
    /* istanbul ignore next */
    (cov_1oh1qd3dcz().b[5][0]++, result.error) ||
    /* istanbul ignore next */
    (cov_1oh1qd3dcz().b[5][1]++, 'Failed to fetch client'), apiErrorCode, statusCode);
  } else
  /* istanbul ignore next */
  {
    cov_1oh1qd3dcz().b[2][1]++;
  }

  // Return tenant context
  cov_1oh1qd3dcz().s[13]++;
  return createSuccessResponse({
    client: result.client
  }, 'Client information retrieved successfully');
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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