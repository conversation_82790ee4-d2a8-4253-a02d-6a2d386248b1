45e00e7e29887aaec16313f4f1faaa64
/**
 * Scan Results Component
 * 
 * Displays latest scan results with proper empty and loading states.
 * Focused responsibility: Rendering scan results section only.
 */

'use client';

/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\ScanResults.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_2548iywawl() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\ScanResults.tsx";
  var hash = "8c66f691a4763fa4866d85042d850de0bbafc8d8";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\ScanResults.tsx",
    statementMap: {
      "0": {
        start: {
          line: 35,
          column: 22
        },
        end: {
          line: 37,
          column: 3
        }
      },
      "1": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 36,
          column: 21
        }
      },
      "2": {
        start: {
          line: 39,
          column: 25
        },
        end: {
          line: 52,
          column: 3
        }
      },
      "3": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 51,
          column: 5
        }
      },
      "4": {
        start: {
          line: 42,
          column: 8
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "5": {
        start: {
          line: 44,
          column: 8
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "6": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 39
        }
      },
      "7": {
        start: {
          line: 48,
          column: 8
        },
        end: {
          line: 48,
          column: 45
        }
      },
      "8": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 50,
          column: 41
        }
      },
      "9": {
        start: {
          line: 54,
          column: 24
        },
        end: {
          line: 67,
          column: 3
        }
      },
      "10": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 66,
          column: 5
        }
      },
      "11": {
        start: {
          line: 57,
          column: 8
        },
        end: {
          line: 57,
          column: 18
        }
      },
      "12": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 59,
          column: 18
        }
      },
      "13": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 61,
          column: 18
        }
      },
      "14": {
        start: {
          line: 63,
          column: 8
        },
        end: {
          line: 63,
          column: 19
        }
      },
      "15": {
        start: {
          line: 65,
          column: 8
        },
        end: {
          line: 65,
          column: 18
        }
      },
      "16": {
        start: {
          line: 69,
          column: 2
        },
        end: {
          line: 98,
          column: 3
        }
      },
      "17": {
        start: {
          line: 102,
          column: 2
        },
        end: {
          line: 121,
          column: 3
        }
      },
      "18": {
        start: {
          line: 105,
          column: 8
        },
        end: {
          line: 118,
          column: 14
        }
      },
      "19": {
        start: {
          line: 125,
          column: 2
        },
        end: {
          line: 138,
          column: 3
        }
      },
      "20": {
        start: {
          line: 142,
          column: 2
        },
        end: {
          line: 160,
          column: 3
        }
      },
      "21": {
        start: {
          line: 172,
          column: 2
        },
        end: {
          line: 198,
          column: 3
        }
      },
      "22": {
        start: {
          line: 186,
          column: 14
        },
        end: {
          line: 190,
          column: 16
        }
      }
    },
    fnMap: {
      "0": {
        name: "ScanResultItem",
        decl: {
          start: {
            line: 34,
            column: 9
          },
          end: {
            line: 34,
            column: 23
          }
        },
        loc: {
          start: {
            line: 34,
            column: 66
          },
          end: {
            line: 99,
            column: 1
          }
        },
        line: 34
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 35,
            column: 22
          },
          end: {
            line: 35,
            column: 23
          }
        },
        loc: {
          start: {
            line: 35,
            column: 28
          },
          end: {
            line: 37,
            column: 3
          }
        },
        line: 35
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 39,
            column: 25
          },
          end: {
            line: 39,
            column: 26
          }
        },
        loc: {
          start: {
            line: 39,
            column: 59
          },
          end: {
            line: 52,
            column: 3
          }
        },
        line: 39
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 54,
            column: 24
          },
          end: {
            line: 54,
            column: 25
          }
        },
        loc: {
          start: {
            line: 54,
            column: 58
          },
          end: {
            line: 67,
            column: 3
          }
        },
        line: 54
      },
      "4": {
        name: "LoadingSkeleton",
        decl: {
          start: {
            line: 101,
            column: 9
          },
          end: {
            line: 101,
            column: 24
          }
        },
        loc: {
          start: {
            line: 101,
            column: 27
          },
          end: {
            line: 122,
            column: 1
          }
        },
        line: 101
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 104,
            column: 25
          },
          end: {
            line: 104,
            column: 26
          }
        },
        loc: {
          start: {
            line: 105,
            column: 8
          },
          end: {
            line: 118,
            column: 14
          }
        },
        line: 105
      },
      "6": {
        name: "EmptyState",
        decl: {
          start: {
            line: 124,
            column: 9
          },
          end: {
            line: 124,
            column: 19
          }
        },
        loc: {
          start: {
            line: 124,
            column: 63
          },
          end: {
            line: 139,
            column: 1
          }
        },
        line: 124
      },
      "7": {
        name: "ScanHeader",
        decl: {
          start: {
            line: 141,
            column: 9
          },
          end: {
            line: 141,
            column: 19
          }
        },
        loc: {
          start: {
            line: 141,
            column: 98
          },
          end: {
            line: 161,
            column: 1
          }
        },
        line: 141
      },
      "8": {
        name: "ScanResults",
        decl: {
          start: {
            line: 163,
            column: 24
          },
          end: {
            line: 163,
            column: 35
          }
        },
        loc: {
          start: {
            line: 171,
            column: 21
          },
          end: {
            line: 199,
            column: 1
          }
        },
        line: 171
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 185,
            column: 25
          },
          end: {
            line: 185,
            column: 26
          }
        },
        loc: {
          start: {
            line: 186,
            column: 14
          },
          end: {
            line: 190,
            column: 16
          }
        },
        line: 186
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 40,
            column: 4
          },
          end: {
            line: 51,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 41,
            column: 6
          },
          end: {
            line: 42,
            column: 43
          }
        }, {
          start: {
            line: 43,
            column: 6
          },
          end: {
            line: 44,
            column: 39
          }
        }, {
          start: {
            line: 45,
            column: 6
          },
          end: {
            line: 46,
            column: 39
          }
        }, {
          start: {
            line: 47,
            column: 6
          },
          end: {
            line: 48,
            column: 45
          }
        }, {
          start: {
            line: 49,
            column: 6
          },
          end: {
            line: 50,
            column: 41
          }
        }],
        line: 40
      },
      "1": {
        loc: {
          start: {
            line: 55,
            column: 4
          },
          end: {
            line: 66,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 56,
            column: 6
          },
          end: {
            line: 57,
            column: 18
          }
        }, {
          start: {
            line: 58,
            column: 6
          },
          end: {
            line: 59,
            column: 18
          }
        }, {
          start: {
            line: 60,
            column: 6
          },
          end: {
            line: 61,
            column: 18
          }
        }, {
          start: {
            line: 62,
            column: 6
          },
          end: {
            line: 63,
            column: 19
          }
        }, {
          start: {
            line: 64,
            column: 6
          },
          end: {
            line: 65,
            column: 18
          }
        }],
        line: 55
      },
      "2": {
        loc: {
          start: {
            line: 84,
            column: 11
          },
          end: {
            line: 86,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 84,
            column: 11
          },
          end: {
            line: 84,
            column: 25
          }
        }, {
          start: {
            line: 85,
            column: 12
          },
          end: {
            line: 85,
            column: 70
          }
        }],
        line: 84
      },
      "3": {
        loc: {
          start: {
            line: 146,
            column: 9
          },
          end: {
            line: 150,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 146,
            column: 9
          },
          end: {
            line: 146,
            column: 21
          }
        }, {
          start: {
            line: 147,
            column: 10
          },
          end: {
            line: 149,
            column: 14
          }
        }],
        line: 146
      },
      "4": {
        loc: {
          start: {
            line: 165,
            column: 2
          },
          end: {
            line: 165,
            column: 19
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 165,
            column: 14
          },
          end: {
            line: 165,
            column: 19
          }
        }],
        line: 165
      },
      "5": {
        loc: {
          start: {
            line: 169,
            column: 2
          },
          end: {
            line: 169,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 169,
            column: 14
          },
          end: {
            line: 169,
            column: 16
          }
        }],
        line: 169
      },
      "6": {
        loc: {
          start: {
            line: 181,
            column: 9
          },
          end: {
            line: 195,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 182,
            column: 10
          },
          end: {
            line: 182,
            column: 29
          }
        }, {
          start: {
            line: 183,
            column: 12
          },
          end: {
            line: 195,
            column: 9
          }
        }],
        line: 181
      },
      "7": {
        loc: {
          start: {
            line: 183,
            column: 12
          },
          end: {
            line: 195,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 184,
            column: 10
          },
          end: {
            line: 192,
            column: 16
          }
        }, {
          start: {
            line: 194,
            column: 10
          },
          end: {
            line: 194,
            column: 46
          }
        }],
        line: 183
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    b: {
      "0": [0, 0, 0, 0, 0],
      "1": [0, 0, 0, 0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0],
      "5": [0],
      "6": [0, 0],
      "7": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "8c66f691a4763fa4866d85042d850de0bbafc8d8"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2548iywawl = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2548iywawl();
function ScanResultItem({
  result,
  onClick
}) {
  /* istanbul ignore next */
  cov_2548iywawl().f[0]++;
  cov_2548iywawl().s[0]++;
  const handleClick = () => {
    /* istanbul ignore next */
    cov_2548iywawl().f[1]++;
    cov_2548iywawl().s[1]++;
    onClick?.(result);
  };
  /* istanbul ignore next */
  cov_2548iywawl().s[2]++;
  const getStatusColor = status => {
    /* istanbul ignore next */
    cov_2548iywawl().f[2]++;
    cov_2548iywawl().s[3]++;
    switch (status) {
      case 'found':
        /* istanbul ignore next */
        cov_2548iywawl().b[0][0]++;
        cov_2548iywawl().s[4]++;
        return 'text-green-600 bg-green-50';
      case 'missing':
        /* istanbul ignore next */
        cov_2548iywawl().b[0][1]++;
        cov_2548iywawl().s[5]++;
        return 'text-red-600 bg-red-50';
      case 'expired':
        /* istanbul ignore next */
        cov_2548iywawl().b[0][2]++;
        cov_2548iywawl().s[6]++;
        return 'text-red-600 bg-red-50';
      case 'warning':
        /* istanbul ignore next */
        cov_2548iywawl().b[0][3]++;
        cov_2548iywawl().s[7]++;
        return 'text-yellow-600 bg-yellow-50';
      default:
        /* istanbul ignore next */
        cov_2548iywawl().b[0][4]++;
        cov_2548iywawl().s[8]++;
        return 'text-gray-600 bg-gray-50';
    }
  };
  /* istanbul ignore next */
  cov_2548iywawl().s[9]++;
  const getStatusIcon = status => {
    /* istanbul ignore next */
    cov_2548iywawl().f[3]++;
    cov_2548iywawl().s[10]++;
    switch (status) {
      case 'found':
        /* istanbul ignore next */
        cov_2548iywawl().b[1][0]++;
        cov_2548iywawl().s[11]++;
        return '✅';
      case 'missing':
        /* istanbul ignore next */
        cov_2548iywawl().b[1][1]++;
        cov_2548iywawl().s[12]++;
        return '❌';
      case 'expired':
        /* istanbul ignore next */
        cov_2548iywawl().b[1][2]++;
        cov_2548iywawl().s[13]++;
        return '⏰';
      case 'warning':
        /* istanbul ignore next */
        cov_2548iywawl().b[1][3]++;
        cov_2548iywawl().s[14]++;
        return '⚠️';
      default:
        /* istanbul ignore next */
        cov_2548iywawl().b[1][4]++;
        cov_2548iywawl().s[15]++;
        return '❓';
    }
  };
  /* istanbul ignore next */
  cov_2548iywawl().s[16]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",
    onClick: handleClick,
    role: "button",
    tabIndex: 0,
    /* istanbul ignore next */
    "aria-label": `View details for ${result.name}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 70,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex items-center space-x-3",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 77,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "w-8 h-8 bg-blue-100 rounded flex items-center justify-center",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 78,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    className: "text-blue-600 text-sm",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 79,
      columnNumber: 11
    }
  }, getStatusIcon(result.status))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 81,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "font-medium text-sm",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 82,
      columnNumber: 11
    }
  }, result.name),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-xs text-secondary capitalize",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 83,
      columnNumber: 11
    }
  }, result.type),
  /* istanbul ignore next */
  (cov_2548iywawl().b[2][0]++, result.details) &&
  /* istanbul ignore next */
  (cov_2548iywawl().b[2][1]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-xs text-secondary",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 85,
      columnNumber: 13
    }
  }, result.details)))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-right",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 89,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    className: `px-2 py-1 rounded-full text-xs font-medium capitalize ${getStatusColor(result.status)}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 90,
      columnNumber: 9
    }
  }, result.status),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-xs text-secondary mt-1",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 93,
      columnNumber: 9
    }
  }, result.lastSeen.toLocaleDateString())));
}
function LoadingSkeleton() {
  /* istanbul ignore next */
  cov_2548iywawl().f[4]++;
  cov_2548iywawl().s[17]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "space-y-3",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 103,
      columnNumber: 5
    }
  }, [...Array(4)].map((_, index) => {
    /* istanbul ignore next */
    cov_2548iywawl().f[5]++;
    cov_2548iywawl().s[18]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      key: index,
      className: "flex items-center justify-between p-3 border rounded-lg animate-pulse",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 105,
        columnNumber: 9
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "flex items-center space-x-3",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 106,
        columnNumber: 11
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "w-8 h-8 bg-gray-200 rounded",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 107,
        columnNumber: 13
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 108,
        columnNumber: 13
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-4 bg-gray-200 rounded w-32 mb-2",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 109,
        columnNumber: 15
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-3 bg-gray-200 rounded w-20 mb-1",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 110,
        columnNumber: 15
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-3 bg-gray-200 rounded w-24",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 111,
        columnNumber: 15
      }
    }))),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "text-right",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 114,
        columnNumber: 11
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-6 bg-gray-200 rounded-full w-16 mb-1",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 115,
        columnNumber: 13
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-3 bg-gray-200 rounded w-12",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 116,
        columnNumber: 13
      }
    })));
  }));
}
function EmptyState({
  onRunScan
}) {
  /* istanbul ignore next */
  cov_2548iywawl().f[6]++;
  cov_2548iywawl().s[19]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-center py-8",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 126,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-4xl mb-4",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 127,
      columnNumber: 7
    }
  }, "\uD83D\uDD0D"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h3",
  /* istanbul ignore next */
  {
    className: "text-lg font-medium mb-2",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 128,
      columnNumber: 7
    }
  }, "No scan results available"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-secondary mb-4",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 129,
      columnNumber: 7
    }
  }, "Run your first scan to discover software and renewals"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  {
    className: "btn btn-primary btn-sm",
    onClick: onRunScan,
    /* istanbul ignore next */
    "aria-label": "Run network scan",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 130,
      columnNumber: 7
    }
  }, "Run Scan"));
}
function ScanHeader({
  lastScanDate,
  onRunScan
}) {
  /* istanbul ignore next */
  cov_2548iywawl().f[7]++;
  cov_2548iywawl().s[20]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex items-center justify-between mb-4",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 143,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 144,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h2",
  /* istanbul ignore next */
  {
    className: "text-lg font-semibold",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 145,
      columnNumber: 9
    }
  }, "Latest Scan Results"),
  /* istanbul ignore next */
  (cov_2548iywawl().b[3][0]++, lastScanDate) &&
  /* istanbul ignore next */
  (cov_2548iywawl().b[3][1]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-xs text-secondary",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 147,
      columnNumber: 11
    }
  }, "Last scan: ", lastScanDate.toLocaleDateString(), " at ", lastScanDate.toLocaleTimeString()))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  {
    className: "btn btn-secondary btn-sm",
    onClick: onRunScan,
    /* istanbul ignore next */
    "aria-label": "Run new scan",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 152,
      columnNumber: 7
    }
  }, "\uD83D\uDD04 Refresh"));
}
export default function ScanResults({
  results,
  isLoading =
  /* istanbul ignore next */
  (cov_2548iywawl().b[4][0]++, false),
  onRunScan,
  onResultClick,
  lastScanDate,
  className =
  /* istanbul ignore next */
  (cov_2548iywawl().b[5][0]++, ''),
  'data-testid': testId
}) {
  /* istanbul ignore next */
  cov_2548iywawl().f[8]++;
  cov_2548iywawl().s[21]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: `card ${className}`,
    /* istanbul ignore next */
    "data-testid": testId,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 173,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "card-header",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 177,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(ScanHeader,
  /* istanbul ignore next */
  {
    lastScanDate: lastScanDate,
    onRunScan: onRunScan,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 178,
      columnNumber: 9
    }
  })),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "card-content",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 180,
      columnNumber: 7
    }
  }, isLoading ?
  /* istanbul ignore next */
  (cov_2548iywawl().b[6][0]++,
  /* istanbul ignore next */
  __jsx(LoadingSkeleton,
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 182,
      columnNumber: 11
    }
  })) :
  /* istanbul ignore next */
  (cov_2548iywawl().b[6][1]++, results.length > 0 ?
  /* istanbul ignore next */
  (cov_2548iywawl().b[7][0]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "space-y-3",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 184,
      columnNumber: 11
    }
  }, results.map(result => {
    /* istanbul ignore next */
    cov_2548iywawl().f[9]++;
    cov_2548iywawl().s[22]++;
    return /* istanbul ignore next */__jsx(ScanResultItem,
    /* istanbul ignore next */
    {
      key: result.id,
      result: result,
      onClick: onResultClick,
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 186,
        columnNumber: 15
      }
    });
  }))) :
  /* istanbul ignore next */
  (cov_2548iywawl().b[7][1]++,
  /* istanbul ignore next */
  __jsx(EmptyState,
  /* istanbul ignore next */
  {
    onRunScan: onRunScan,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 194,
      columnNumber: 11
    }
  })))));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfanN4RmlsZU5hbWUiLCJSZWFjdCIsIl9fanN4IiwiY3JlYXRlRWxlbWVudCIsImNvdl8yNTQ4aXl3YXdsIiwicGF0aCIsImhhc2giLCJnbG9iYWwiLCJGdW5jdGlvbiIsImdjdiIsImNvdmVyYWdlRGF0YSIsInN0YXRlbWVudE1hcCIsInN0YXJ0IiwibGluZSIsImNvbHVtbiIsImVuZCIsImZuTWFwIiwibmFtZSIsImRlY2wiLCJsb2MiLCJicmFuY2hNYXAiLCJ0eXBlIiwibG9jYXRpb25zIiwicyIsImYiLCJiIiwiX2NvdmVyYWdlU2NoZW1hIiwiY292ZXJhZ2UiLCJhY3R1YWxDb3ZlcmFnZSIsIlNjYW5SZXN1bHRJdGVtIiwicmVzdWx0Iiwib25DbGljayIsImhhbmRsZUNsaWNrIiwiZ2V0U3RhdHVzQ29sb3IiLCJzdGF0dXMiLCJnZXRTdGF0dXNJY29uIiwiY2xhc3NOYW1lIiwicm9sZSIsInRhYkluZGV4IiwiX19zZWxmIiwiX19zb3VyY2UiLCJmaWxlTmFtZSIsImxpbmVOdW1iZXIiLCJjb2x1bW5OdW1iZXIiLCJkZXRhaWxzIiwibGFzdFNlZW4iLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJMb2FkaW5nU2tlbGV0b24iLCJBcnJheSIsIm1hcCIsIl8iLCJpbmRleCIsImtleSIsIkVtcHR5U3RhdGUiLCJvblJ1blNjYW4iLCJTY2FuSGVhZGVyIiwibGFzdFNjYW5EYXRlIiwidG9Mb2NhbGVUaW1lU3RyaW5nIiwiU2NhblJlc3VsdHMiLCJyZXN1bHRzIiwiaXNMb2FkaW5nIiwib25SZXN1bHRDbGljayIsInRlc3RJZCIsImxlbmd0aCIsImlkIl0sInNvdXJjZXMiOlsiU2NhblJlc3VsdHMudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogU2NhbiBSZXN1bHRzIENvbXBvbmVudFxuICogXG4gKiBEaXNwbGF5cyBsYXRlc3Qgc2NhbiByZXN1bHRzIHdpdGggcHJvcGVyIGVtcHR5IGFuZCBsb2FkaW5nIHN0YXRlcy5cbiAqIEZvY3VzZWQgcmVzcG9uc2liaWxpdHk6IFJlbmRlcmluZyBzY2FuIHJlc3VsdHMgc2VjdGlvbiBvbmx5LlxuICovXG5cbid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBCYXNlQ29tcG9uZW50UHJvcHMgfSBmcm9tICdAL2xpYi90eXBlcydcblxuaW50ZXJmYWNlIFNjYW5SZXN1bHQge1xuICBpZDogc3RyaW5nXG4gIHR5cGU6ICdzb2Z0d2FyZScgfCAnbGljZW5zZScgfCAncmVuZXdhbCdcbiAgbmFtZTogc3RyaW5nXG4gIHN0YXR1czogJ2ZvdW5kJyB8ICdtaXNzaW5nJyB8ICdleHBpcmVkJyB8ICd3YXJuaW5nJ1xuICBsYXN0U2VlbjogRGF0ZVxuICBkZXRhaWxzPzogc3RyaW5nXG59XG5cbmludGVyZmFjZSBTY2FuUmVzdWx0c1Byb3BzIGV4dGVuZHMgQmFzZUNvbXBvbmVudFByb3BzIHtcbiAgcmVzdWx0czogU2NhblJlc3VsdFtdXG4gIGlzTG9hZGluZz86IGJvb2xlYW5cbiAgb25SdW5TY2FuPzogKCkgPT4gdm9pZFxuICBvblJlc3VsdENsaWNrPzogKHJlc3VsdDogU2NhblJlc3VsdCkgPT4gdm9pZFxuICBsYXN0U2NhbkRhdGU/OiBEYXRlXG59XG5cbmludGVyZmFjZSBTY2FuUmVzdWx0SXRlbVByb3BzIHtcbiAgcmVzdWx0OiBTY2FuUmVzdWx0XG4gIG9uQ2xpY2s/OiAocmVzdWx0OiBTY2FuUmVzdWx0KSA9PiB2b2lkXG59XG5cbmZ1bmN0aW9uIFNjYW5SZXN1bHRJdGVtKHsgcmVzdWx0LCBvbkNsaWNrIH06IFNjYW5SZXN1bHRJdGVtUHJvcHMpIHtcbiAgY29uc3QgaGFuZGxlQ2xpY2sgPSAoKSA9PiB7XG4gICAgb25DbGljaz8uKHJlc3VsdClcbiAgfVxuXG4gIGNvbnN0IGdldFN0YXR1c0NvbG9yID0gKHN0YXR1czogU2NhblJlc3VsdFsnc3RhdHVzJ10pID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnZm91bmQnOlxuICAgICAgICByZXR1cm4gJ3RleHQtZ3JlZW4tNjAwIGJnLWdyZWVuLTUwJ1xuICAgICAgY2FzZSAnbWlzc2luZyc6XG4gICAgICAgIHJldHVybiAndGV4dC1yZWQtNjAwIGJnLXJlZC01MCdcbiAgICAgIGNhc2UgJ2V4cGlyZWQnOlxuICAgICAgICByZXR1cm4gJ3RleHQtcmVkLTYwMCBiZy1yZWQtNTAnXG4gICAgICBjYXNlICd3YXJuaW5nJzpcbiAgICAgICAgcmV0dXJuICd0ZXh0LXllbGxvdy02MDAgYmcteWVsbG93LTUwJ1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuICd0ZXh0LWdyYXktNjAwIGJnLWdyYXktNTAnXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0U3RhdHVzSWNvbiA9IChzdGF0dXM6IFNjYW5SZXN1bHRbJ3N0YXR1cyddKSA9PiB7XG4gICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgIGNhc2UgJ2ZvdW5kJzpcbiAgICAgICAgcmV0dXJuICfinIUnXG4gICAgICBjYXNlICdtaXNzaW5nJzpcbiAgICAgICAgcmV0dXJuICfinYwnXG4gICAgICBjYXNlICdleHBpcmVkJzpcbiAgICAgICAgcmV0dXJuICfij7AnXG4gICAgICBjYXNlICd3YXJuaW5nJzpcbiAgICAgICAgcmV0dXJuICfimqDvuI8nXG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gJ+KdkydcbiAgICB9XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgXG4gICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC0zIGJvcmRlciByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktNTAgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgb25DbGljaz17aGFuZGxlQ2xpY2t9XG4gICAgICByb2xlPVwiYnV0dG9uXCJcbiAgICAgIHRhYkluZGV4PXswfVxuICAgICAgYXJpYS1sYWJlbD17YFZpZXcgZGV0YWlscyBmb3IgJHtyZXN1bHQubmFtZX1gfVxuICAgID5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ibHVlLTEwMCByb3VuZGVkIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCB0ZXh0LXNtXCI+e2dldFN0YXR1c0ljb24ocmVzdWx0LnN0YXR1cyl9PC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtXCI+e3Jlc3VsdC5uYW1lfTwvcD5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2Vjb25kYXJ5IGNhcGl0YWxpemVcIj57cmVzdWx0LnR5cGV9PC9wPlxuICAgICAgICAgIHtyZXN1bHQuZGV0YWlscyAmJiAoXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2Vjb25kYXJ5XCI+e3Jlc3VsdC5kZXRhaWxzfTwvcD5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSBjYXBpdGFsaXplICR7Z2V0U3RhdHVzQ29sb3IocmVzdWx0LnN0YXR1cyl9YH0+XG4gICAgICAgICAge3Jlc3VsdC5zdGF0dXN9XG4gICAgICAgIDwvc3Bhbj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNlY29uZGFyeSBtdC0xXCI+XG4gICAgICAgICAge3Jlc3VsdC5sYXN0U2Vlbi50b0xvY2FsZURhdGVTdHJpbmcoKX1cbiAgICAgICAgPC9wPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZnVuY3Rpb24gTG9hZGluZ1NrZWxldG9uKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICB7Wy4uLkFycmF5KDQpXS5tYXAoKF8sIGluZGV4KSA9PiAoXG4gICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtMyBib3JkZXIgcm91bmRlZC1sZyBhbmltYXRlLXB1bHNlXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ncmF5LTIwMCByb3VuZGVkXCI+PC9kaXY+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNCBiZy1ncmF5LTIwMCByb3VuZGVkIHctMzIgbWItMlwiPjwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMyBiZy1ncmF5LTIwMCByb3VuZGVkIHctMjAgbWItMVwiPjwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMyBiZy1ncmF5LTIwMCByb3VuZGVkIHctMjRcIj48L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTYgYmctZ3JheS0yMDAgcm91bmRlZC1mdWxsIHctMTYgbWItMVwiPjwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTMgYmctZ3JheS0yMDAgcm91bmRlZCB3LTEyXCI+PC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKSl9XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZnVuY3Rpb24gRW1wdHlTdGF0ZSh7IG9uUnVuU2NhbiB9OiB7IG9uUnVuU2Nhbj86ICgpID0+IHZvaWQgfSkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBtYi00XCI+8J+UjTwvZGl2PlxuICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gbWItMlwiPk5vIHNjYW4gcmVzdWx0cyBhdmFpbGFibGU8L2gzPlxuICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zZWNvbmRhcnkgbWItNFwiPlJ1biB5b3VyIGZpcnN0IHNjYW4gdG8gZGlzY292ZXIgc29mdHdhcmUgYW5kIHJlbmV3YWxzPC9wPlxuICAgICAgPGJ1dHRvbiBcbiAgICAgICAgY2xhc3NOYW1lPVwiYnRuIGJ0bi1wcmltYXJ5IGJ0bi1zbVwiXG4gICAgICAgIG9uQ2xpY2s9e29uUnVuU2Nhbn1cbiAgICAgICAgYXJpYS1sYWJlbD1cIlJ1biBuZXR3b3JrIHNjYW5cIlxuICAgICAgPlxuICAgICAgICBSdW4gU2NhblxuICAgICAgPC9idXR0b24+XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZnVuY3Rpb24gU2NhbkhlYWRlcih7IGxhc3RTY2FuRGF0ZSwgb25SdW5TY2FuIH06IHsgbGFzdFNjYW5EYXRlPzogRGF0ZTsgb25SdW5TY2FuPzogKCkgPT4gdm9pZCB9KSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgPGRpdj5cbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZFwiPkxhdGVzdCBTY2FuIFJlc3VsdHM8L2gyPlxuICAgICAgICB7bGFzdFNjYW5EYXRlICYmIChcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2Vjb25kYXJ5XCI+XG4gICAgICAgICAgICBMYXN0IHNjYW46IHtsYXN0U2NhbkRhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCl9IGF0IHtsYXN0U2NhbkRhdGUudG9Mb2NhbGVUaW1lU3RyaW5nKCl9XG4gICAgICAgICAgPC9wPlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgICA8YnV0dG9uIFxuICAgICAgICBjbGFzc05hbWU9XCJidG4gYnRuLXNlY29uZGFyeSBidG4tc21cIlxuICAgICAgICBvbkNsaWNrPXtvblJ1blNjYW59XG4gICAgICAgIGFyaWEtbGFiZWw9XCJSdW4gbmV3IHNjYW5cIlxuICAgICAgPlxuICAgICAgICDwn5SEIFJlZnJlc2hcbiAgICAgIDwvYnV0dG9uPlxuICAgIDwvZGl2PlxuICApXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNjYW5SZXN1bHRzKHtcbiAgcmVzdWx0cyxcbiAgaXNMb2FkaW5nID0gZmFsc2UsXG4gIG9uUnVuU2NhbixcbiAgb25SZXN1bHRDbGljayxcbiAgbGFzdFNjYW5EYXRlLFxuICBjbGFzc05hbWUgPSAnJyxcbiAgJ2RhdGEtdGVzdGlkJzogdGVzdElkXG59OiBTY2FuUmVzdWx0c1Byb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBcbiAgICAgIGNsYXNzTmFtZT17YGNhcmQgJHtjbGFzc05hbWV9YH1cbiAgICAgIGRhdGEtdGVzdGlkPXt0ZXN0SWR9XG4gICAgPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkLWhlYWRlclwiPlxuICAgICAgICA8U2NhbkhlYWRlciBsYXN0U2NhbkRhdGU9e2xhc3RTY2FuRGF0ZX0gb25SdW5TY2FuPXtvblJ1blNjYW59IC8+XG4gICAgICA8L2Rpdj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZC1jb250ZW50XCI+XG4gICAgICAgIHtpc0xvYWRpbmcgPyAoXG4gICAgICAgICAgPExvYWRpbmdTa2VsZXRvbiAvPlxuICAgICAgICApIDogcmVzdWx0cy5sZW5ndGggPiAwID8gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICB7cmVzdWx0cy5tYXAoKHJlc3VsdCkgPT4gKFxuICAgICAgICAgICAgICA8U2NhblJlc3VsdEl0ZW1cbiAgICAgICAgICAgICAgICBrZXk9e3Jlc3VsdC5pZH1cbiAgICAgICAgICAgICAgICByZXN1bHQ9e3Jlc3VsdH1cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtvblJlc3VsdENsaWNrfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgPEVtcHR5U3RhdGUgb25SdW5TY2FuPXtvblJ1blNjYW59IC8+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsWUFBWTs7QUFBQTtBQUFBLElBQUFBLFlBQUE7QUFBQSxPQUFBQyxLQUFBO0FBQUEsSUFBQUMsS0FBQSxHQUFBRCxLQUFBLENBQUFFLGFBQUE7QUFBQSxTQUFBQyxlQUFBO0VBQUEsSUFBQUMsSUFBQTtFQUFBLElBQUFDLElBQUE7RUFBQSxJQUFBQyxNQUFBLE9BQUFDLFFBQUE7RUFBQSxJQUFBQyxHQUFBO0VBQUEsSUFBQUMsWUFBQTtJQUFBTCxJQUFBO0lBQUFNLFlBQUE7TUFBQTtRQUFBQyxLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO0lBQUE7SUFBQUUsS0FBQTtNQUFBO1FBQUFDLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtJQUFBO0lBQUFPLFNBQUE7TUFBQTtRQUFBRCxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7SUFBQTtJQUFBVSxDQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtJQUFBO0lBQUFDLENBQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtJQUFBO0lBQUFDLENBQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO0lBQUE7SUFBQUMsZUFBQTtJQUFBcEIsSUFBQTtFQUFBO0VBQUEsSUFBQXFCLFFBQUEsR0FBQXBCLE1BQUEsQ0FBQUUsR0FBQSxNQUFBRixNQUFBLENBQUFFLEdBQUE7RUFBQSxLQUFBa0IsUUFBQSxDQUFBdEIsSUFBQSxLQUFBc0IsUUFBQSxDQUFBdEIsSUFBQSxFQUFBQyxJQUFBLEtBQUFBLElBQUE7SUFBQXFCLFFBQUEsQ0FBQXRCLElBQUEsSUFBQUssWUFBQTtFQUFBO0VBQUEsSUFBQWtCLGNBQUEsR0FBQUQsUUFBQSxDQUFBdEIsSUFBQTtFQUFBO0lBUUE7SUFBQUQsY0FBQSxZQUFBQSxDQUFBO01BQUEsT0FBQXdCLGNBQUE7SUFBQTtFQUFBO0VBQUEsT0FBQUEsY0FBQTtBQUFBO0FBQUF4QixjQUFBO0FBa0JaLFNBQVN5QixjQUFjQSxDQUFDO0VBQUVDLE1BQU07RUFBRUM7QUFBNkIsQ0FBQyxFQUFFO0VBQUE7RUFBQTNCLGNBQUEsR0FBQW9CLENBQUE7RUFBQXBCLGNBQUEsR0FBQW1CLENBQUE7RUFDaEUsTUFBTVMsV0FBVyxHQUFHQSxDQUFBLEtBQU07SUFBQTtJQUFBNUIsY0FBQSxHQUFBb0IsQ0FBQTtJQUFBcEIsY0FBQSxHQUFBbUIsQ0FBQTtJQUN4QlEsT0FBTyxHQUFHRCxNQUFNLENBQUM7RUFDbkIsQ0FBQztFQUFBO0VBQUExQixjQUFBLEdBQUFtQixDQUFBO0VBRUQsTUFBTVUsY0FBYyxHQUFJQyxNQUE0QixJQUFLO0lBQUE7SUFBQTlCLGNBQUEsR0FBQW9CLENBQUE7SUFBQXBCLGNBQUEsR0FBQW1CLENBQUE7SUFDdkQsUUFBUVcsTUFBTTtNQUNaLEtBQUssT0FBTztRQUFBO1FBQUE5QixjQUFBLEdBQUFxQixDQUFBO1FBQUFyQixjQUFBLEdBQUFtQixDQUFBO1FBQ1YsT0FBTyw0QkFBNEI7TUFDckMsS0FBSyxTQUFTO1FBQUE7UUFBQW5CLGNBQUEsR0FBQXFCLENBQUE7UUFBQXJCLGNBQUEsR0FBQW1CLENBQUE7UUFDWixPQUFPLHdCQUF3QjtNQUNqQyxLQUFLLFNBQVM7UUFBQTtRQUFBbkIsY0FBQSxHQUFBcUIsQ0FBQTtRQUFBckIsY0FBQSxHQUFBbUIsQ0FBQTtRQUNaLE9BQU8sd0JBQXdCO01BQ2pDLEtBQUssU0FBUztRQUFBO1FBQUFuQixjQUFBLEdBQUFxQixDQUFBO1FBQUFyQixjQUFBLEdBQUFtQixDQUFBO1FBQ1osT0FBTyw4QkFBOEI7TUFDdkM7UUFBQTtRQUFBbkIsY0FBQSxHQUFBcUIsQ0FBQTtRQUFBckIsY0FBQSxHQUFBbUIsQ0FBQTtRQUNFLE9BQU8sMEJBQTBCO0lBQ3JDO0VBQ0YsQ0FBQztFQUFBO0VBQUFuQixjQUFBLEdBQUFtQixDQUFBO0VBRUQsTUFBTVksYUFBYSxHQUFJRCxNQUE0QixJQUFLO0lBQUE7SUFBQTlCLGNBQUEsR0FBQW9CLENBQUE7SUFBQXBCLGNBQUEsR0FBQW1CLENBQUE7SUFDdEQsUUFBUVcsTUFBTTtNQUNaLEtBQUssT0FBTztRQUFBO1FBQUE5QixjQUFBLEdBQUFxQixDQUFBO1FBQUFyQixjQUFBLEdBQUFtQixDQUFBO1FBQ1YsT0FBTyxHQUFHO01BQ1osS0FBSyxTQUFTO1FBQUE7UUFBQW5CLGNBQUEsR0FBQXFCLENBQUE7UUFBQXJCLGNBQUEsR0FBQW1CLENBQUE7UUFDWixPQUFPLEdBQUc7TUFDWixLQUFLLFNBQVM7UUFBQTtRQUFBbkIsY0FBQSxHQUFBcUIsQ0FBQTtRQUFBckIsY0FBQSxHQUFBbUIsQ0FBQTtRQUNaLE9BQU8sR0FBRztNQUNaLEtBQUssU0FBUztRQUFBO1FBQUFuQixjQUFBLEdBQUFxQixDQUFBO1FBQUFyQixjQUFBLEdBQUFtQixDQUFBO1FBQ1osT0FBTyxJQUFJO01BQ2I7UUFBQTtRQUFBbkIsY0FBQSxHQUFBcUIsQ0FBQTtRQUFBckIsY0FBQSxHQUFBbUIsQ0FBQTtRQUNFLE9BQU8sR0FBRztJQUNkO0VBQ0YsQ0FBQztFQUFBO0VBQUFuQixjQUFBLEdBQUFtQixDQUFBO0VBRUQsT0FDRSwwQkFBQXJCLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUNFa0MsU0FBUyxFQUFDLDJHQUEyRztJQUNySEwsT0FBTyxFQUFFQyxXQUFZO0lBQ3JCSyxJQUFJLEVBQUMsUUFBUTtJQUNiQyxRQUFRLEVBQUUsQ0FBRTtJQUNaO0lBQUEsY0FBWSxvQkFBb0JSLE1BQU0sQ0FBQ2IsSUFBSSxFQUFHO0lBQUFzQixNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBekMsWUFBQTtNQUFBMEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQTtFQUU5QztFQUFBekMsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQUtrQyxTQUFTLEVBQUMsNkJBQTZCO0lBQUFHLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUF6QyxZQUFBO01BQUEwQyxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBO0VBQzFDO0VBQUF6QyxLQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFBS2tDLFNBQVMsRUFBQyw4REFBOEQ7SUFBQUcsTUFBQTtJQUFBQyxRQUFBO01BQUFDLFFBQUEsRUFBQXpDLFlBQUE7TUFBQTBDLFVBQUE7TUFBQUMsWUFBQTtJQUFBO0VBQUE7RUFDM0U7RUFBQXpDLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUFNa0MsU0FBUyxFQUFDLHVCQUF1QjtJQUFBRyxNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBekMsWUFBQTtNQUFBMEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQSxHQUFFUixhQUFhLENBQUNMLE1BQU0sQ0FBQ0ksTUFBTSxDQUFRLENBQ3pFLENBQUM7RUFDTjtFQUFBaEMsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQUFxQyxNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBekMsWUFBQTtNQUFBMEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQTtFQUNFO0VBQUF6QyxLQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFBR2tDLFNBQVMsRUFBQyxxQkFBcUI7SUFBQUcsTUFBQTtJQUFBQyxRQUFBO01BQUFDLFFBQUEsRUFBQXpDLFlBQUE7TUFBQTBDLFVBQUE7TUFBQUMsWUFBQTtJQUFBO0VBQUEsR0FBRWIsTUFBTSxDQUFDYixJQUFRLENBQUM7RUFDcEQ7RUFBQWYsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQUdrQyxTQUFTLEVBQUMsbUNBQW1DO0lBQUFHLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUF6QyxZQUFBO01BQUEwQyxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBLEdBQUViLE1BQU0sQ0FBQ1QsSUFBUSxDQUFDO0VBQ2pFO0VBQUEsQ0FBQWpCLGNBQUEsR0FBQXFCLENBQUEsVUFBQUssTUFBTSxDQUFDYyxPQUFPO0VBQUE7RUFBQSxDQUFBeEMsY0FBQSxHQUFBcUIsQ0FBQTtFQUNiO0VBQUF2QixLQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFBR2tDLFNBQVMsRUFBQyx3QkFBd0I7SUFBQUcsTUFBQTtJQUFBQyxRQUFBO01BQUFDLFFBQUEsRUFBQXpDLFlBQUE7TUFBQTBDLFVBQUE7TUFBQUMsWUFBQTtJQUFBO0VBQUEsR0FBRWIsTUFBTSxDQUFDYyxPQUFXLENBQUMsQ0FFekQsQ0FDRixDQUFDO0VBQ047RUFBQTFDLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUFLa0MsU0FBUyxFQUFDLFlBQVk7SUFBQUcsTUFBQTtJQUFBQyxRQUFBO01BQUFDLFFBQUEsRUFBQXpDLFlBQUE7TUFBQTBDLFVBQUE7TUFBQUMsWUFBQTtJQUFBO0VBQUE7RUFDekI7RUFBQXpDLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUFNa0MsU0FBUyxFQUFFLHlEQUF5REgsY0FBYyxDQUFDSCxNQUFNLENBQUNJLE1BQU0sQ0FBQyxFQUFHO0lBQUFLLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUF6QyxZQUFBO01BQUEwQyxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBLEdBQ3ZHYixNQUFNLENBQUNJLE1BQ0osQ0FBQztFQUNQO0VBQUFoQyxLQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFBR2tDLFNBQVMsRUFBQyw2QkFBNkI7SUFBQUcsTUFBQTtJQUFBQyxRQUFBO01BQUFDLFFBQUEsRUFBQXpDLFlBQUE7TUFBQTBDLFVBQUE7TUFBQUMsWUFBQTtJQUFBO0VBQUEsR0FDdkNiLE1BQU0sQ0FBQ2UsUUFBUSxDQUFDQyxrQkFBa0IsQ0FBQyxDQUNuQyxDQUNBLENBQ0YsQ0FBQztBQUVWO0FBRUEsU0FBU0MsZUFBZUEsQ0FBQSxFQUFHO0VBQUE7RUFBQTNDLGNBQUEsR0FBQW9CLENBQUE7RUFBQXBCLGNBQUEsR0FBQW1CLENBQUE7RUFDekIsT0FDRSwwQkFBQXJCLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUFLa0MsU0FBUyxFQUFDLFdBQVc7SUFBQUcsTUFBQTtJQUFBQyxRQUFBO01BQUFDLFFBQUEsRUFBQXpDLFlBQUE7TUFBQTBDLFVBQUE7TUFBQUMsWUFBQTtJQUFBO0VBQUEsR0FDdkIsQ0FBQyxHQUFHSyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQ0MsR0FBRyxDQUFDLENBQUNDLENBQUMsRUFBRUMsS0FBSyxLQUMxQjtJQUFBO0lBQUEvQyxjQUFBLEdBQUFvQixDQUFBO0lBQUFwQixjQUFBLEdBQUFtQixDQUFBO0lBQUEsaUNBQUFyQixLQUFBO0lBQUE7SUFBQTtJQUFBO0lBQUE7TUFBS2tELEdBQUcsRUFBRUQsS0FBTTtNQUFDZixTQUFTLEVBQUMsdUVBQXVFO01BQUFHLE1BQUE7TUFBQUMsUUFBQTtRQUFBQyxRQUFBLEVBQUF6QyxZQUFBO1FBQUEwQyxVQUFBO1FBQUFDLFlBQUE7TUFBQTtJQUFBO0lBQ2hHO0lBQUF6QyxLQUFBO0lBQUE7SUFBQTtJQUFBO0lBQUE7TUFBS2tDLFNBQVMsRUFBQyw2QkFBNkI7TUFBQUcsTUFBQTtNQUFBQyxRQUFBO1FBQUFDLFFBQUEsRUFBQXpDLFlBQUE7UUFBQTBDLFVBQUE7UUFBQUMsWUFBQTtNQUFBO0lBQUE7SUFDMUM7SUFBQXpDLEtBQUE7SUFBQTtJQUFBO0lBQUE7SUFBQTtNQUFLa0MsU0FBUyxFQUFDLDZCQUE2QjtNQUFBRyxNQUFBO01BQUFDLFFBQUE7UUFBQUMsUUFBQSxFQUFBekMsWUFBQTtRQUFBMEMsVUFBQTtRQUFBQyxZQUFBO01BQUE7SUFBQSxDQUFNLENBQUM7SUFDbkQ7SUFBQXpDLEtBQUE7SUFBQTtJQUFBO0lBQUE7SUFBQTtNQUFBcUMsTUFBQTtNQUFBQyxRQUFBO1FBQUFDLFFBQUEsRUFBQXpDLFlBQUE7UUFBQTBDLFVBQUE7UUFBQUMsWUFBQTtNQUFBO0lBQUE7SUFDRTtJQUFBekMsS0FBQTtJQUFBO0lBQUE7SUFBQTtJQUFBO01BQUtrQyxTQUFTLEVBQUMsbUNBQW1DO01BQUFHLE1BQUE7TUFBQUMsUUFBQTtRQUFBQyxRQUFBLEVBQUF6QyxZQUFBO1FBQUEwQyxVQUFBO1FBQUFDLFlBQUE7TUFBQTtJQUFBLENBQU0sQ0FBQztJQUN6RDtJQUFBekMsS0FBQTtJQUFBO0lBQUE7SUFBQTtJQUFBO01BQUtrQyxTQUFTLEVBQUMsbUNBQW1DO01BQUFHLE1BQUE7TUFBQUMsUUFBQTtRQUFBQyxRQUFBLEVBQUF6QyxZQUFBO1FBQUEwQyxVQUFBO1FBQUFDLFlBQUE7TUFBQTtJQUFBLENBQU0sQ0FBQztJQUN6RDtJQUFBekMsS0FBQTtJQUFBO0lBQUE7SUFBQTtJQUFBO01BQUtrQyxTQUFTLEVBQUMsOEJBQThCO01BQUFHLE1BQUE7TUFBQUMsUUFBQTtRQUFBQyxRQUFBLEVBQUF6QyxZQUFBO1FBQUEwQyxVQUFBO1FBQUFDLFlBQUE7TUFBQTtJQUFBLENBQU0sQ0FDaEQsQ0FDRixDQUFDO0lBQ047SUFBQXpDLEtBQUE7SUFBQTtJQUFBO0lBQUE7SUFBQTtNQUFLa0MsU0FBUyxFQUFDLFlBQVk7TUFBQUcsTUFBQTtNQUFBQyxRQUFBO1FBQUFDLFFBQUEsRUFBQXpDLFlBQUE7UUFBQTBDLFVBQUE7UUFBQUMsWUFBQTtNQUFBO0lBQUE7SUFDekI7SUFBQXpDLEtBQUE7SUFBQTtJQUFBO0lBQUE7SUFBQTtNQUFLa0MsU0FBUyxFQUFDLHdDQUF3QztNQUFBRyxNQUFBO01BQUFDLFFBQUE7UUFBQUMsUUFBQSxFQUFBekMsWUFBQTtRQUFBMEMsVUFBQTtRQUFBQyxZQUFBO01BQUE7SUFBQSxDQUFNLENBQUM7SUFDOUQ7SUFBQXpDLEtBQUE7SUFBQTtJQUFBO0lBQUE7SUFBQTtNQUFLa0MsU0FBUyxFQUFDLDhCQUE4QjtNQUFBRyxNQUFBO01BQUFDLFFBQUE7UUFBQUMsUUFBQSxFQUFBekMsWUFBQTtRQUFBMEMsVUFBQTtRQUFBQyxZQUFBO01BQUE7SUFBQSxDQUFNLENBQ2hELENBQ0YsQ0FBQztFQUFELENBQ04sQ0FDRSxDQUFDO0FBRVY7QUFFQSxTQUFTVSxVQUFVQSxDQUFDO0VBQUVDO0FBQXNDLENBQUMsRUFBRTtFQUFBO0VBQUFsRCxjQUFBLEdBQUFvQixDQUFBO0VBQUFwQixjQUFBLEdBQUFtQixDQUFBO0VBQzdELE9BQ0UsMEJBQUFyQixLQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFBS2tDLFNBQVMsRUFBQyxrQkFBa0I7SUFBQUcsTUFBQTtJQUFBQyxRQUFBO01BQUFDLFFBQUEsRUFBQXpDLFlBQUE7TUFBQTBDLFVBQUE7TUFBQUMsWUFBQTtJQUFBO0VBQUE7RUFDL0I7RUFBQXpDLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUFLa0MsU0FBUyxFQUFDLGVBQWU7SUFBQUcsTUFBQTtJQUFBQyxRQUFBO01BQUFDLFFBQUEsRUFBQXpDLFlBQUE7TUFBQTBDLFVBQUE7TUFBQUMsWUFBQTtJQUFBO0VBQUEsR0FBQyxjQUFPLENBQUM7RUFDdkM7RUFBQXpDLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUFJa0MsU0FBUyxFQUFDLDBCQUEwQjtJQUFBRyxNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBekMsWUFBQTtNQUFBMEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQSxHQUFDLDJCQUE2QixDQUFDO0VBQ3ZFO0VBQUF6QyxLQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFBR2tDLFNBQVMsRUFBQyxxQkFBcUI7SUFBQUcsTUFBQTtJQUFBQyxRQUFBO01BQUFDLFFBQUEsRUFBQXpDLFlBQUE7TUFBQTBDLFVBQUE7TUFBQUMsWUFBQTtJQUFBO0VBQUEsR0FBQyx1REFBd0QsQ0FBQztFQUM1RjtFQUFBekMsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQ0VrQyxTQUFTLEVBQUMsd0JBQXdCO0lBQ2xDTCxPQUFPLEVBQUV1QixTQUFVO0lBQ25CO0lBQUEsY0FBVyxrQkFBa0I7SUFBQWYsTUFBQTtJQUFBQyxRQUFBO01BQUFDLFFBQUEsRUFBQXpDLFlBQUE7TUFBQTBDLFVBQUE7TUFBQUMsWUFBQTtJQUFBO0VBQUEsR0FDOUIsVUFFTyxDQUNMLENBQUM7QUFFVjtBQUVBLFNBQVNZLFVBQVVBLENBQUM7RUFBRUMsWUFBWTtFQUFFRjtBQUEyRCxDQUFDLEVBQUU7RUFBQTtFQUFBbEQsY0FBQSxHQUFBb0IsQ0FBQTtFQUFBcEIsY0FBQSxHQUFBbUIsQ0FBQTtFQUNoRyxPQUNFLDBCQUFBckIsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQUtrQyxTQUFTLEVBQUMsd0NBQXdDO0lBQUFHLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUF6QyxZQUFBO01BQUEwQyxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBO0VBQ3JEO0VBQUF6QyxLQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFBQXFDLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUF6QyxZQUFBO01BQUEwQyxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBO0VBQ0U7RUFBQXpDLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUFJa0MsU0FBUyxFQUFDLHVCQUF1QjtJQUFBRyxNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBekMsWUFBQTtNQUFBMEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQSxHQUFDLHFCQUF1QixDQUFDO0VBQzdEO0VBQUEsQ0FBQXZDLGNBQUEsR0FBQXFCLENBQUEsVUFBQStCLFlBQVk7RUFBQTtFQUFBLENBQUFwRCxjQUFBLEdBQUFxQixDQUFBO0VBQ1g7RUFBQXZCLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUFHa0MsU0FBUyxFQUFDLHdCQUF3QjtJQUFBRyxNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBekMsWUFBQTtNQUFBMEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQSxHQUFDLGFBQ3pCLEVBQUNhLFlBQVksQ0FBQ1Ysa0JBQWtCLENBQUMsQ0FBQyxFQUFDLE1BQUksRUFBQ1UsWUFBWSxDQUFDQyxrQkFBa0IsQ0FBQyxDQUNsRixDQUFDLENBRUgsQ0FBQztFQUNOO0VBQUF2RCxLQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFDRWtDLFNBQVMsRUFBQywwQkFBMEI7SUFDcENMLE9BQU8sRUFBRXVCLFNBQVU7SUFDbkI7SUFBQSxjQUFXLGNBQWM7SUFBQWYsTUFBQTtJQUFBQyxRQUFBO01BQUFDLFFBQUEsRUFBQXpDLFlBQUE7TUFBQTBDLFVBQUE7TUFBQUMsWUFBQTtJQUFBO0VBQUEsR0FDMUIsc0JBRU8sQ0FDTCxDQUFDO0FBRVY7QUFFQSxlQUFlLFNBQVNlLFdBQVdBLENBQUM7RUFDbENDLE9BQU87RUFDUEMsU0FBUztFQUFBO0VBQUEsQ0FBQXhELGNBQUEsR0FBQXFCLENBQUEsVUFBRyxLQUFLO0VBQ2pCNkIsU0FBUztFQUNUTyxhQUFhO0VBQ2JMLFlBQVk7RUFDWnBCLFNBQVM7RUFBQTtFQUFBLENBQUFoQyxjQUFBLEdBQUFxQixDQUFBLFVBQUcsRUFBRTtFQUNkLGFBQWEsRUFBRXFDO0FBQ0MsQ0FBQyxFQUFFO0VBQUE7RUFBQTFELGNBQUEsR0FBQW9CLENBQUE7RUFBQXBCLGNBQUEsR0FBQW1CLENBQUE7RUFDbkIsT0FDRSwwQkFBQXJCLEtBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtJQUNFa0MsU0FBUyxFQUFFLFFBQVFBLFNBQVMsRUFBRztJQUMvQjtJQUFBLGVBQWEwQixNQUFPO0lBQUF2QixNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBekMsWUFBQTtNQUFBMEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQTtFQUVwQjtFQUFBekMsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQUtrQyxTQUFTLEVBQUMsYUFBYTtJQUFBRyxNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBekMsWUFBQTtNQUFBMEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQTtFQUMxQjtFQUFBekMsS0FBQSxDQUFDcUQsVUFBVTtFQUFBO0VBQUE7SUFBQ0MsWUFBWSxFQUFFQSxZQUFhO0lBQUNGLFNBQVMsRUFBRUEsU0FBVTtJQUFBZixNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBekMsWUFBQTtNQUFBMEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQSxDQUFFLENBQzVELENBQUM7RUFDTjtFQUFBekMsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQUtrQyxTQUFTLEVBQUMsY0FBYztJQUFBRyxNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBekMsWUFBQTtNQUFBMEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQSxHQUMxQmlCLFNBQVM7RUFBQTtFQUFBLENBQUF4RCxjQUFBLEdBQUFxQixDQUFBO0VBQ1I7RUFBQXZCLEtBQUEsQ0FBQzZDLGVBQWU7RUFBQTtFQUFBO0lBQUFSLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUF6QyxZQUFBO01BQUEwQyxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBLENBQUUsQ0FBQztFQUFBO0VBQUEsQ0FBQXZDLGNBQUEsR0FBQXFCLENBQUEsVUFDakJrQyxPQUFPLENBQUNJLE1BQU0sR0FBRyxDQUFDO0VBQUE7RUFBQSxDQUFBM0QsY0FBQSxHQUFBcUIsQ0FBQTtFQUNwQjtFQUFBdkIsS0FBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQUtrQyxTQUFTLEVBQUMsV0FBVztJQUFBRyxNQUFBO0lBQUFDLFFBQUE7TUFBQUMsUUFBQSxFQUFBekMsWUFBQTtNQUFBMEMsVUFBQTtNQUFBQyxZQUFBO0lBQUE7RUFBQSxHQUN2QmdCLE9BQU8sQ0FBQ1YsR0FBRyxDQUFFbkIsTUFBTSxJQUNsQjtJQUFBO0lBQUExQixjQUFBLEdBQUFvQixDQUFBO0lBQUFwQixjQUFBLEdBQUFtQixDQUFBO0lBQUEsaUNBQUFyQixLQUFBLENBQUMyQixjQUFjO0lBQUE7SUFBQTtNQUNidUIsR0FBRyxFQUFFdEIsTUFBTSxDQUFDa0MsRUFBRztNQUNmbEMsTUFBTSxFQUFFQSxNQUFPO01BQ2ZDLE9BQU8sRUFBRThCLGFBQWM7TUFBQXRCLE1BQUE7TUFBQUMsUUFBQTtRQUFBQyxRQUFBLEVBQUF6QyxZQUFBO1FBQUEwQyxVQUFBO1FBQUFDLFlBQUE7TUFBQTtJQUFBLENBQ3hCLENBQUM7RUFBRCxDQUNGLENBQ0UsQ0FBQztFQUFBO0VBQUEsQ0FBQXZDLGNBQUEsR0FBQXFCLENBQUE7RUFFTjtFQUFBdkIsS0FBQSxDQUFDbUQsVUFBVTtFQUFBO0VBQUE7SUFBQ0MsU0FBUyxFQUFFQSxTQUFVO0lBQUFmLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUF6QyxZQUFBO01BQUEwQyxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBLENBQUUsQ0FBQyxDQUNyQyxDQUNFLENBQ0YsQ0FBQztBQUVWIiwiaWdub3JlTGlzdCI6W119