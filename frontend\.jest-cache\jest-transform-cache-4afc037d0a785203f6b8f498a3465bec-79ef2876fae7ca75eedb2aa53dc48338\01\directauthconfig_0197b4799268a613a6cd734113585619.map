{"version": 3, "names": ["cov_1jc8q2ba66", "actualCoverage", "Amplify", "configureAmplifyDirectly", "f", "s", "getConfig", "<PERSON><PERSON>", "b", "config", "userPoolId", "userPoolClientId", "domain", "redirectSignIn", "redirectSignOut", "configure", "Cognito", "loginWith", "o<PERSON>h", "scopes", "responseType", "ssr", "console", "log", "error"], "sources": ["direct-auth-config.ts"], "sourcesContent": ["import { Amplify } from 'aws-amplify'\n\nexport function configureAmplifyDirectly() {\n  // If already configured, don't configure again\n  if (Amplify.getConfig().Auth) return;\n  \n  // Hard-coded values for testing - replace with your actual values\n  const config = {\n    userPoolId: 'ca-central-1_uwPuGUhLc',\n    userPoolClientId: '6fc4ks4poom3mqk5icavr7np1k',\n    domain: 'auth.renewtrack.com',\n    redirectSignIn: 'http://localhost:3000/callback',\n    redirectSignOut: 'http://localhost:3000/signout'\n  };\n  \n  try {\n    Amplify.configure({\n      Auth: {\n        Cognito: {\n          userPoolId: config.userPoolId,\n          userPoolClientId: config.userPoolClientId,\n          loginWith: {\n            oauth: {\n              domain: config.domain,\n              scopes: [\"email\", \"profile\", \"openid\", \"aws.cognito.signin.user.admin\"],\n              redirectSignIn: [config.redirectSignIn],\n              redirectSignOut: [config.redirectSignOut],\n              responseType: \"code\"\n            }\n          }\n        }\n      }\n    }, {\n      ssr: true\n    });\n    console.log('Amplify configured directly with hard-coded values');\n  } catch (error) {\n    console.error('Error configuring Amplify directly:', error);\n  }\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ,SAASE,OAAO,QAAQ,aAAa;AAErC,OAAO,SAASC,wBAAwBA,CAAA,EAAG;EAAA;EAAAH,cAAA,GAAAI,CAAA;EAAAJ,cAAA,GAAAK,CAAA;EACzC;EACA,IAAIH,OAAO,CAACI,SAAS,CAAC,CAAC,CAACC,IAAI,EAAE;IAAA;IAAAP,cAAA,GAAAQ,CAAA;IAAAR,cAAA,GAAAK,CAAA;IAAA;EAAM,CAAC;EAAA;EAAA;IAAAL,cAAA,GAAAQ,CAAA;EAAA;;EAErC;EACA,MAAMC,MAAM;EAAA;EAAA,CAAAT,cAAA,GAAAK,CAAA,OAAG;IACbK,UAAU,EAAE,wBAAwB;IACpCC,gBAAgB,EAAE,4BAA4B;IAC9CC,MAAM,EAAE,qBAAqB;IAC7BC,cAAc,EAAE,gCAAgC;IAChDC,eAAe,EAAE;EACnB,CAAC;EAAC;EAAAd,cAAA,GAAAK,CAAA;EAEF,IAAI;IAAA;IAAAL,cAAA,GAAAK,CAAA;IACFH,OAAO,CAACa,SAAS,CAAC;MAChBR,IAAI,EAAE;QACJS,OAAO,EAAE;UACPN,UAAU,EAAED,MAAM,CAACC,UAAU;UAC7BC,gBAAgB,EAAEF,MAAM,CAACE,gBAAgB;UACzCM,SAAS,EAAE;YACTC,KAAK,EAAE;cACLN,MAAM,EAAEH,MAAM,CAACG,MAAM;cACrBO,MAAM,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,+BAA+B,CAAC;cACvEN,cAAc,EAAE,CAACJ,MAAM,CAACI,cAAc,CAAC;cACvCC,eAAe,EAAE,CAACL,MAAM,CAACK,eAAe,CAAC;cACzCM,YAAY,EAAE;YAChB;UACF;QACF;MACF;IACF,CAAC,EAAE;MACDC,GAAG,EAAE;IACP,CAAC,CAAC;IAAC;IAAArB,cAAA,GAAAK,CAAA;IACHiB,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;EACnE,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA;IAAAxB,cAAA,GAAAK,CAAA;IACdiB,OAAO,CAACE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;EAC7D;AACF", "ignoreList": []}