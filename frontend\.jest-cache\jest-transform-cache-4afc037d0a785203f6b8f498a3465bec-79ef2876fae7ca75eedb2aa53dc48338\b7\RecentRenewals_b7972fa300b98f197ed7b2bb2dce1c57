fc89baa4d7e7c47617e83cc7f9b9bb18
/**
 * Recent Renewals Component
 * 
 * Displays a list of recently added renewals with proper loading and empty states.
 * Focused responsibility: Rendering recent renewals list only.
 */

'use client';

/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\RecentRenewals.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_1sef079p6j() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\RecentRenewals.tsx";
  var hash = "0d9c8bec4c8a1f9fdef2035c7bdc91e3a5182604";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\dashboard\\RecentRenewals.tsx",
    statementMap: {
      "0": {
        start: {
          line: 25,
          column: 22
        },
        end: {
          line: 27,
          column: 3
        }
      },
      "1": {
        start: {
          line: 26,
          column: 4
        },
        end: {
          line: 26,
          column: 22
        }
      },
      "2": {
        start: {
          line: 29,
          column: 24
        },
        end: {
          line: 34,
          column: 3
        }
      },
      "3": {
        start: {
          line: 30,
          column: 4
        },
        end: {
          line: 33,
          column: 5
        }
      },
      "4": {
        start: {
          line: 31,
          column: 6
        },
        end: {
          line: 31,
          column: 24
        }
      },
      "5": {
        start: {
          line: 32,
          column: 6
        },
        end: {
          line: 32,
          column: 19
        }
      },
      "6": {
        start: {
          line: 36,
          column: 2
        },
        end: {
          line: 59,
          column: 3
        }
      },
      "7": {
        start: {
          line: 63,
          column: 2
        },
        end: {
          line: 79,
          column: 3
        }
      },
      "8": {
        start: {
          line: 66,
          column: 8
        },
        end: {
          line: 76,
          column: 14
        }
      },
      "9": {
        start: {
          line: 83,
          column: 2
        },
        end: {
          line: 91,
          column: 3
        }
      },
      "10": {
        start: {
          line: 102,
          column: 26
        },
        end: {
          line: 102,
          column: 53
        }
      },
      "11": {
        start: {
          line: 104,
          column: 2
        },
        end: {
          line: 135,
          column: 3
        }
      },
      "12": {
        start: {
          line: 123,
          column: 14
        },
        end: {
          line: 127,
          column: 16
        }
      }
    },
    fnMap: {
      "0": {
        name: "RenewalItem",
        decl: {
          start: {
            line: 24,
            column: 9
          },
          end: {
            line: 24,
            column: 20
          }
        },
        loc: {
          start: {
            line: 24,
            column: 61
          },
          end: {
            line: 60,
            column: 1
          }
        },
        line: 24
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 25,
            column: 22
          },
          end: {
            line: 25,
            column: 23
          }
        },
        loc: {
          start: {
            line: 25,
            column: 28
          },
          end: {
            line: 27,
            column: 3
          }
        },
        line: 25
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        },
        loc: {
          start: {
            line: 29,
            column: 52
          },
          end: {
            line: 34,
            column: 3
          }
        },
        line: 29
      },
      "3": {
        name: "LoadingSkeleton",
        decl: {
          start: {
            line: 62,
            column: 9
          },
          end: {
            line: 62,
            column: 24
          }
        },
        loc: {
          start: {
            line: 62,
            column: 27
          },
          end: {
            line: 80,
            column: 1
          }
        },
        line: 62
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 65,
            column: 25
          },
          end: {
            line: 65,
            column: 26
          }
        },
        loc: {
          start: {
            line: 66,
            column: 8
          },
          end: {
            line: 76,
            column: 14
          }
        },
        line: 66
      },
      "5": {
        name: "EmptyState",
        decl: {
          start: {
            line: 82,
            column: 9
          },
          end: {
            line: 82,
            column: 19
          }
        },
        loc: {
          start: {
            line: 82,
            column: 22
          },
          end: {
            line: 92,
            column: 1
          }
        },
        line: 82
      },
      "6": {
        name: "RecentRenewals",
        decl: {
          start: {
            line: 94,
            column: 24
          },
          end: {
            line: 94,
            column: 38
          }
        },
        loc: {
          start: {
            line: 101,
            column: 24
          },
          end: {
            line: 136,
            column: 1
          }
        },
        line: 101
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 122,
            column: 33
          },
          end: {
            line: 122,
            column: 34
          }
        },
        loc: {
          start: {
            line: 123,
            column: 14
          },
          end: {
            line: 127,
            column: 16
          }
        },
        line: 123
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 30,
            column: 4
          },
          end: {
            line: 33,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 4
          },
          end: {
            line: 33,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "1": {
        loc: {
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 30,
            column: 25
          }
        }, {
          start: {
            line: 30,
            column: 29
          },
          end: {
            line: 30,
            column: 42
          }
        }],
        line: 30
      },
      "2": {
        loc: {
          start: {
            line: 51,
            column: 20
          },
          end: {
            line: 51,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 51,
            column: 41
          },
          end: {
            line: 51,
            column: 90
          }
        }, {
          start: {
            line: 51,
            column: 93
          },
          end: {
            line: 51,
            column: 102
          }
        }],
        line: 51
      },
      "3": {
        loc: {
          start: {
            line: 96,
            column: 2
          },
          end: {
            line: 96,
            column: 19
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 96,
            column: 14
          },
          end: {
            line: 96,
            column: 19
          }
        }],
        line: 96
      },
      "4": {
        loc: {
          start: {
            line: 98,
            column: 2
          },
          end: {
            line: 98,
            column: 14
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 98,
            column: 13
          },
          end: {
            line: 98,
            column: 14
          }
        }],
        line: 98
      },
      "5": {
        loc: {
          start: {
            line: 99,
            column: 2
          },
          end: {
            line: 99,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 99,
            column: 14
          },
          end: {
            line: 99,
            column: 16
          }
        }],
        line: 99
      },
      "6": {
        loc: {
          start: {
            line: 111,
            column: 9
          },
          end: {
            line: 115,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 111,
            column: 9
          },
          end: {
            line: 111,
            column: 35
          }
        }, {
          start: {
            line: 112,
            column: 10
          },
          end: {
            line: 114,
            column: 17
          }
        }],
        line: 111
      },
      "7": {
        loc: {
          start: {
            line: 118,
            column: 9
          },
          end: {
            line: 132,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 119,
            column: 10
          },
          end: {
            line: 119,
            column: 29
          }
        }, {
          start: {
            line: 120,
            column: 12
          },
          end: {
            line: 132,
            column: 9
          }
        }],
        line: 118
      },
      "8": {
        loc: {
          start: {
            line: 120,
            column: 12
          },
          end: {
            line: 132,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 121,
            column: 10
          },
          end: {
            line: 129,
            column: 16
          }
        }, {
          start: {
            line: 131,
            column: 10
          },
          end: {
            line: 131,
            column: 24
          }
        }],
        line: 120
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0],
      "4": [0],
      "5": [0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0d9c8bec4c8a1f9fdef2035c7bdc91e3a5182604"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1sef079p6j = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1sef079p6j();
function RenewalItem({
  renewal,
  onClick
}) {
  /* istanbul ignore next */
  cov_1sef079p6j().f[0]++;
  cov_1sef079p6j().s[0]++;
  const handleClick = () => {
    /* istanbul ignore next */
    cov_1sef079p6j().f[1]++;
    cov_1sef079p6j().s[1]++;
    onClick?.(renewal);
  };
  /* istanbul ignore next */
  cov_1sef079p6j().s[2]++;
  const handleKeyDown = e => {
    /* istanbul ignore next */
    cov_1sef079p6j().f[2]++;
    cov_1sef079p6j().s[3]++;
    if (
    /* istanbul ignore next */
    (cov_1sef079p6j().b[1][0]++, e.key === 'Enter') ||
    /* istanbul ignore next */
    (cov_1sef079p6j().b[1][1]++, e.key === ' ')) {
      /* istanbul ignore next */
      cov_1sef079p6j().b[0][0]++;
      cov_1sef079p6j().s[4]++;
      e.preventDefault();
      /* istanbul ignore next */
      cov_1sef079p6j().s[5]++;
      handleClick();
    } else
    /* istanbul ignore next */
    {
      cov_1sef079p6j().b[0][1]++;
    }
  };
  /* istanbul ignore next */
  cov_1sef079p6j().s[6]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",
    onClick: handleClick,
    onKeyDown: handleKeyDown,
    tabIndex: 0,
    role: "button",
    /* istanbul ignore next */
    "aria-label": `View details for ${renewal.name}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 37,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "w-8 h-8 bg-blue-100 rounded flex items-center justify-center",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 45,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    className: "text-blue-600 text-sm",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 46,
      columnNumber: 9
    }
  }, "\uD83D\uDCC4")),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex-1",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 48,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "font-medium text-sm",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 49,
      columnNumber: 9
    }
  }, renewal.name),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-xs text-secondary",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 50,
      columnNumber: 9
    }
  }, "Added on ", renewal.created_at ?
  /* istanbul ignore next */
  (cov_1sef079p6j().b[2][0]++, new Date(renewal.created_at).toLocaleDateString()) :
  /* istanbul ignore next */
  (cov_1sef079p6j().b[2][1]++, 'Unknown'))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-right",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 54,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-xs text-secondary",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 55,
      columnNumber: 9
    }
  }, renewal.vendor),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-xs text-blue-600",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 56,
      columnNumber: 9
    }
  }, "View Details")));
}
function LoadingSkeleton() {
  /* istanbul ignore next */
  cov_1sef079p6j().f[3]++;
  cov_1sef079p6j().s[7]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "space-y-3",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 64,
      columnNumber: 5
    }
  }, [...Array(5)].map((_, index) => {
    /* istanbul ignore next */
    cov_1sef079p6j().f[4]++;
    cov_1sef079p6j().s[8]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      key: index,
      className: "flex items-center space-x-3 p-3 border rounded-lg animate-pulse",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 66,
        columnNumber: 9
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "w-8 h-8 bg-gray-200 rounded",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 67,
        columnNumber: 11
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "flex-1",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 68,
        columnNumber: 11
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-4 bg-gray-200 rounded w-3/4 mb-2",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 69,
        columnNumber: 13
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-3 bg-gray-200 rounded w-1/2",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 70,
        columnNumber: 13
      }
    })),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "text-right",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 72,
        columnNumber: 11
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-3 bg-gray-200 rounded w-16 mb-1",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 73,
        columnNumber: 13
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-3 bg-gray-200 rounded w-20",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 74,
        columnNumber: 13
      }
    })));
  }));
}
function EmptyState() {
  /* istanbul ignore next */
  cov_1sef079p6j().f[5]++;
  cov_1sef079p6j().s[9]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-center py-8",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 84,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-4xl mb-4",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 85,
      columnNumber: 7
    }
  }, "\uD83D\uDCC4"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h3",
  /* istanbul ignore next */
  {
    className: "text-lg font-medium mb-2",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 86,
      columnNumber: 7
    }
  }, "No recent renewals"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-secondary",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 87,
      columnNumber: 7
    }
  }, "Recent renewals will appear here once you start adding them."));
}
export default function RecentRenewals({
  renewals,
  isLoading =
  /* istanbul ignore next */
  (cov_1sef079p6j().b[3][0]++, false),
  onRenewalClick,
  maxItems =
  /* istanbul ignore next */
  (cov_1sef079p6j().b[4][0]++, 5),
  className =
  /* istanbul ignore next */
  (cov_1sef079p6j().b[5][0]++, ''),
  'data-testid': testId
}) {
  /* istanbul ignore next */
  cov_1sef079p6j().f[6]++;
  const displayRenewals =
  /* istanbul ignore next */
  (cov_1sef079p6j().s[10]++, renewals.slice(0, maxItems));
  /* istanbul ignore next */
  cov_1sef079p6j().s[11]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: `card ${className}`,
    /* istanbul ignore next */
    "data-testid": testId,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 105,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "card-header",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 109,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h2",
  /* istanbul ignore next */
  {
    className: "text-lg font-semibold",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 110,
      columnNumber: 9
    }
  }, "Recently Added Renewals"),
  /* istanbul ignore next */
  (cov_1sef079p6j().b[6][0]++, renewals.length > maxItems) &&
  /* istanbul ignore next */
  (cov_1sef079p6j().b[6][1]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    className: "text-sm text-secondary",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 112,
      columnNumber: 11
    }
  }, "Showing ", maxItems, " of ", renewals.length))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "card-content",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 117,
      columnNumber: 7
    }
  }, isLoading ?
  /* istanbul ignore next */
  (cov_1sef079p6j().b[7][0]++,
  /* istanbul ignore next */
  __jsx(LoadingSkeleton,
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 119,
      columnNumber: 11
    }
  })) :
  /* istanbul ignore next */
  (cov_1sef079p6j().b[7][1]++, displayRenewals.length > 0 ?
  /* istanbul ignore next */
  (cov_1sef079p6j().b[8][0]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "space-y-3",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 121,
      columnNumber: 11
    }
  }, displayRenewals.map(renewal => {
    /* istanbul ignore next */
    cov_1sef079p6j().f[7]++;
    cov_1sef079p6j().s[12]++;
    return /* istanbul ignore next */__jsx(RenewalItem,
    /* istanbul ignore next */
    {
      key: renewal.id,
      renewal: renewal,
      onClick: onRenewalClick,
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 123,
        columnNumber: 15
      }
    });
  }))) :
  /* istanbul ignore next */
  (cov_1sef079p6j().b[8][1]++,
  /* istanbul ignore next */
  __jsx(EmptyState,
  /* istanbul ignore next */
  {
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 131,
      columnNumber: 11
    }
  })))));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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