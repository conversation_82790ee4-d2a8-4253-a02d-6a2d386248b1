4b4ab3bde4<PERSON>23a1ab8c63c4aba0dfec
/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\layout\\Sidebar.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_2lfieank0g() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\layout\\Sidebar.tsx";
  var hash = "a5883d49600f226bd450199263ea6b9e7a55e239";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\layout\\Sidebar.tsx",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 19
        },
        end: {
          line: 6,
          column: 32
        }
      },
      "1": {
        start: {
          line: 7,
          column: 28
        },
        end: {
          line: 7,
          column: 37
        }
      },
      "2": {
        start: {
          line: 10,
          column: 17
        },
        end: {
          line: 14,
          column: 3
        }
      },
      "3": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 47
        }
      },
      "4": {
        start: {
          line: 11,
          column: 35
        },
        end: {
          line: 11,
          column: 47
        }
      },
      "5": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 72
        }
      },
      "6": {
        start: {
          line: 17,
          column: 22
        },
        end: {
          line: 21,
          column: 44
        }
      },
      "7": {
        start: {
          line: 24,
          column: 23
        },
        end: {
          line: 26,
          column: 49
        }
      },
      "8": {
        start: {
          line: 28,
          column: 2
        },
        end: {
          line: 153,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "Sidebar",
        decl: {
          start: {
            line: 5,
            column: 24
          },
          end: {
            line: 5,
            column: 31
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 154,
            column: 1
          }
        },
        line: 5
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 10,
            column: 17
          },
          end: {
            line: 10,
            column: 18
          }
        },
        loc: {
          start: {
            line: 10,
            column: 34
          },
          end: {
            line: 14,
            column: 3
          }
        },
        line: 10
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 11,
            column: 4
          },
          end: {
            line: 11,
            column: 47
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 11,
            column: 4
          },
          end: {
            line: 11,
            column: 47
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 11
      },
      "1": {
        loc: {
          start: {
            line: 11,
            column: 8
          },
          end: {
            line: 11,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 8
          },
          end: {
            line: 11,
            column: 12
          }
        }, {
          start: {
            line: 11,
            column: 16
          },
          end: {
            line: 11,
            column: 33
          }
        }],
        line: 11
      },
      "2": {
        loc: {
          start: {
            line: 13,
            column: 11
          },
          end: {
            line: 13,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 12
          },
          end: {
            line: 13,
            column: 29
          }
        }, {
          start: {
            line: 13,
            column: 33
          },
          end: {
            line: 13,
            column: 48
          }
        }, {
          start: {
            line: 13,
            column: 53
          },
          end: {
            line: 13,
            column: 71
          }
        }],
        line: 13
      },
      "3": {
        loc: {
          start: {
            line: 17,
            column: 22
          },
          end: {
            line: 21,
            column: 44
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 6
          },
          end: {
            line: 18,
            column: 46
          }
        }, {
          start: {
            line: 19,
            column: 6
          },
          end: {
            line: 21,
            column: 44
          }
        }],
        line: 17
      },
      "4": {
        loc: {
          start: {
            line: 17,
            column: 22
          },
          end: {
            line: 17,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 22
          },
          end: {
            line: 17,
            column: 38
          }
        }, {
          start: {
            line: 17,
            column: 42
          },
          end: {
            line: 17,
            column: 59
          }
        }],
        line: 17
      },
      "5": {
        loc: {
          start: {
            line: 19,
            column: 6
          },
          end: {
            line: 21,
            column: 44
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 20,
            column: 17
          }
        }, {
          start: {
            line: 21,
            column: 8
          },
          end: {
            line: 21,
            column: 44
          }
        }],
        line: 19
      },
      "6": {
        loc: {
          start: {
            line: 21,
            column: 8
          },
          end: {
            line: 21,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 8
          },
          end: {
            line: 21,
            column: 34
          }
        }, {
          start: {
            line: 21,
            column: 38
          },
          end: {
            line: 21,
            column: 44
          }
        }],
        line: 21
      },
      "7": {
        loc: {
          start: {
            line: 24,
            column: 23
          },
          end: {
            line: 26,
            column: 49
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 25,
            column: 6
          },
          end: {
            line: 25,
            column: 16
          }
        }, {
          start: {
            line: 26,
            column: 6
          },
          end: {
            line: 26,
            column: 49
          }
        }],
        line: 24
      },
      "8": {
        loc: {
          start: {
            line: 24,
            column: 23
          },
          end: {
            line: 24,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 23
          },
          end: {
            line: 24,
            column: 34
          }
        }, {
          start: {
            line: 24,
            column: 38
          },
          end: {
            line: 24,
            column: 57
          }
        }],
        line: 24
      },
      "9": {
        loc: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 38
          }
        }, {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 44
          }
        }],
        line: 26
      },
      "10": {
        loc: {
          start: {
            line: 38,
            column: 59
          },
          end: {
            line: 38,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 38,
            column: 87
          },
          end: {
            line: 38,
            column: 95
          }
        }, {
          start: {
            line: 38,
            column: 98
          },
          end: {
            line: 38,
            column: 100
          }
        }],
        line: 38
      },
      "11": {
        loc: {
          start: {
            line: 48,
            column: 58
          },
          end: {
            line: 48,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 48,
            column: 85
          },
          end: {
            line: 48,
            column: 93
          }
        }, {
          start: {
            line: 48,
            column: 96
          },
          end: {
            line: 48,
            column: 98
          }
        }],
        line: 48
      },
      "12": {
        loc: {
          start: {
            line: 55,
            column: 54
          },
          end: {
            line: 55,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 55,
            column: 77
          },
          end: {
            line: 55,
            column: 85
          }
        }, {
          start: {
            line: 55,
            column: 88
          },
          end: {
            line: 55,
            column: 90
          }
        }],
        line: 55
      },
      "13": {
        loc: {
          start: {
            line: 62,
            column: 61
          },
          end: {
            line: 62,
            column: 104
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 62,
            column: 91
          },
          end: {
            line: 62,
            column: 99
          }
        }, {
          start: {
            line: 62,
            column: 102
          },
          end: {
            line: 62,
            column: 104
          }
        }],
        line: 62
      },
      "14": {
        loc: {
          start: {
            line: 70,
            column: 60
          },
          end: {
            line: 70,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 70,
            column: 89
          },
          end: {
            line: 70,
            column: 97
          }
        }, {
          start: {
            line: 70,
            column: 100
          },
          end: {
            line: 70,
            column: 102
          }
        }],
        line: 70
      },
      "15": {
        loc: {
          start: {
            line: 78,
            column: 57
          },
          end: {
            line: 78,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 78,
            column: 83
          },
          end: {
            line: 78,
            column: 91
          }
        }, {
          start: {
            line: 78,
            column: 94
          },
          end: {
            line: 78,
            column: 96
          }
        }],
        line: 78
      },
      "16": {
        loc: {
          start: {
            line: 85,
            column: 57
          },
          end: {
            line: 85,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 85,
            column: 83
          },
          end: {
            line: 85,
            column: 91
          }
        }, {
          start: {
            line: 85,
            column: 94
          },
          end: {
            line: 85,
            column: 96
          }
        }],
        line: 85
      },
      "17": {
        loc: {
          start: {
            line: 92,
            column: 65
          },
          end: {
            line: 92,
            column: 112
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 92,
            column: 99
          },
          end: {
            line: 92,
            column: 107
          }
        }, {
          start: {
            line: 92,
            column: 110
          },
          end: {
            line: 92,
            column: 112
          }
        }],
        line: 92
      },
      "18": {
        loc: {
          start: {
            line: 99,
            column: 57
          },
          end: {
            line: 99,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 99,
            column: 83
          },
          end: {
            line: 99,
            column: 91
          }
        }, {
          start: {
            line: 99,
            column: 94
          },
          end: {
            line: 99,
            column: 96
          }
        }],
        line: 99
      },
      "19": {
        loc: {
          start: {
            line: 106,
            column: 58
          },
          end: {
            line: 106,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 106,
            column: 85
          },
          end: {
            line: 106,
            column: 93
          }
        }, {
          start: {
            line: 106,
            column: 96
          },
          end: {
            line: 106,
            column: 98
          }
        }],
        line: 106
      },
      "20": {
        loc: {
          start: {
            line: 114,
            column: 58
          },
          end: {
            line: 114,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 114,
            column: 85
          },
          end: {
            line: 114,
            column: 93
          }
        }, {
          start: {
            line: 114,
            column: 96
          },
          end: {
            line: 114,
            column: 98
          }
        }],
        line: 114
      },
      "21": {
        loc: {
          start: {
            line: 121,
            column: 59
          },
          end: {
            line: 121,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 121,
            column: 87
          },
          end: {
            line: 121,
            column: 95
          }
        }, {
          start: {
            line: 121,
            column: 98
          },
          end: {
            line: 121,
            column: 100
          }
        }],
        line: 121
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a5883d49600f226bd450199263ea6b9e7a55e239"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2lfieank0g = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2lfieank0g();
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';
export default function Sidebar() {
  /* istanbul ignore next */
  cov_2lfieank0g().f[0]++;
  const pathname =
  /* istanbul ignore next */
  (cov_2lfieank0g().s[0]++, usePathname());
  const {
    user,
    signOut
  } =
  /* istanbul ignore next */
  (cov_2lfieank0g().s[1]++, useAuth());

  // Check if a string looks like a UUID or is empty/invalid
  /* istanbul ignore next */
  cov_2lfieank0g().s[2]++;
  const isUUID = str => {
    /* istanbul ignore next */
    cov_2lfieank0g().f[1]++;
    cov_2lfieank0g().s[3]++;
    if (
    /* istanbul ignore next */
    (cov_2lfieank0g().b[1][0]++, !str) ||
    /* istanbul ignore next */
    (cov_2lfieank0g().b[1][1]++, str.trim() === '')) {
      /* istanbul ignore next */
      cov_2lfieank0g().b[0][0]++;
      cov_2lfieank0g().s[4]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_2lfieank0g().b[0][1]++;
    }
    // Check for UUID pattern or if it doesn't contain @ (not an email)
    cov_2lfieank0g().s[5]++;
    return /* istanbul ignore next */(cov_2lfieank0g().b[2][0]++, str.includes('-')) &&
    /* istanbul ignore next */
    (cov_2lfieank0g().b[2][1]++, str.length > 30) ||
    /* istanbul ignore next */
    (cov_2lfieank0g().b[2][2]++, !str.includes('@'));
  };

  // Format display name - safely access user properties
  const displayName =
  /* istanbul ignore next */
  (cov_2lfieank0g().s[6]++,
  /* istanbul ignore next */
  (cov_2lfieank0g().b[4][0]++, user?.given_name) &&
  /* istanbul ignore next */
  (cov_2lfieank0g().b[4][1]++, user?.family_name) ?
  /* istanbul ignore next */
  (cov_2lfieank0g().b[3][0]++, `${user.given_name} ${user.family_name}`) :
  /* istanbul ignore next */
  (cov_2lfieank0g().b[3][1]++, user?.name ?
  /* istanbul ignore next */
  (cov_2lfieank0g().b[5][0]++, user.name) :
  /* istanbul ignore next */
  (cov_2lfieank0g().b[5][1]++,
  /* istanbul ignore next */
  (cov_2lfieank0g().b[6][0]++, user?.email?.split('@')[0]) ||
  /* istanbul ignore next */
  (cov_2lfieank0g().b[6][1]++, 'User'))));

  // Format email display - only show email if it's a valid email
  const displayEmail =
  /* istanbul ignore next */
  (cov_2lfieank0g().s[7]++,
  /* istanbul ignore next */
  (cov_2lfieank0g().b[8][0]++, user?.email) &&
  /* istanbul ignore next */
  (cov_2lfieank0g().b[8][1]++, !isUUID(user.email)) ?
  /* istanbul ignore next */
  (cov_2lfieank0g().b[7][0]++, user.email) :
  /* istanbul ignore next */
  (cov_2lfieank0g().b[7][1]++, `ID: ${
  /* istanbul ignore next */
  (cov_2lfieank0g().b[9][0]++, user?.id?.substring(0, 8)) ||
  /* istanbul ignore next */
  (cov_2lfieank0g().b[9][1]++, '')}...`));
  /* istanbul ignore next */
  cov_2lfieank0g().s[8]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "aside",
  /* istanbul ignore next */
  {
    className: "sidebar",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 29,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "sidebar-logo",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 31,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h1",
  /* istanbul ignore next */
  {
    className: "text-2xl font-bold",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 32,
      columnNumber: 9
    }
  }, "R2T2"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-xs text-secondary",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 33,
      columnNumber: 9
    }
  }, "RENEWALS TRACKER")),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "nav",
  /* istanbul ignore next */
  {
    className: "sidebar-nav",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 37,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/dashboard",
    className: `sidebar-link ${pathname === '/dashboard' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[10][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[10][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 38,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 39,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M4 5a1 1 0 011-1h4a1 1 0 011 1v5a1 1 0 01-1 1H5a1 1 0 01-1-1V5z",
    fill: "currentColor",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 40,
      columnNumber: 13
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M14 5a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V5z",
    fill: "currentColor",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 41,
      columnNumber: 13
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M4 14a1 1 0 011-1h4a1 1 0 011 1v5a1 1 0 01-1 1H5a1 1 0 01-1-1v-5z",
    fill: "currentColor",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 42,
      columnNumber: 13
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M14 12a1 1 0 011-1h4a1 1 0 011 1v7a1 1 0 01-1 1h-4a1 1 0 01-1-1v-7z",
    fill: "currentColor",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 43,
      columnNumber: 13
    }
  })), "Dashboard"),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/renewals",
    className: `sidebar-link ${pathname === '/renewals' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[11][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[11][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 48,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 49,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 50,
      columnNumber: 13
    }
  })), "Renewals Inventory"),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/scan",
    className: `sidebar-link ${pathname === '/scan' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[12][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[12][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 55,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 56,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 57,
      columnNumber: 13
    }
  })), "Scan Network"),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/departments",
    className: `sidebar-link ${pathname === '/departments' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[13][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[13][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 62,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 63,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 64,
      columnNumber: 13
    }
  })), "Departments",
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    className: "badge badge-primary ml-auto",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 67,
      columnNumber: 11
    }
  }, "BETA")),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/financials",
    className: `sidebar-link ${pathname === '/financials' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[14][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[14][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 70,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 71,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 72,
      columnNumber: 13
    }
  })), "Financials",
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "span",
  /* istanbul ignore next */
  {
    className: "badge badge-primary ml-auto",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 75,
      columnNumber: 11
    }
  }, "BETA")),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/reports",
    className: `sidebar-link ${pathname === '/reports' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[15][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[15][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 78,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 79,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 80,
      columnNumber: 13
    }
  })), "Reports"),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/vendors",
    className: `sidebar-link ${pathname === '/vendors' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[16][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[16][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 85,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 86,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 87,
      columnNumber: 13
    }
  })), "Vendors"),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/user-management",
    className: `sidebar-link ${pathname === '/user-management' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[17][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[17][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 92,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 93,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 94,
      columnNumber: 13
    }
  })), "User Management"),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/license",
    className: `sidebar-link ${pathname === '/license' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[18][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[18][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 99,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 100,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 101,
      columnNumber: 13
    }
  })), "License"),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/settings",
    className: `sidebar-link ${pathname === '/settings' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[19][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[19][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 106,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 107,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 108,
      columnNumber: 13
    }
  }),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M15 12a3 3 0 11-6 0 3 3 0 016 0z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 109,
      columnNumber: 13
    }
  })), "Settings"),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/timeline",
    className: `sidebar-link ${pathname === '/timeline' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[20][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[20][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 114,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 115,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 116,
      columnNumber: 13
    }
  })), "Event Timeline"),
  /* istanbul ignore next */
  __jsx(Link,
  /* istanbul ignore next */
  {
    href: "/documents",
    className: `sidebar-link ${pathname === '/documents' ?
    /* istanbul ignore next */
    (cov_2lfieank0g().b[21][0]++, 'active') :
    /* istanbul ignore next */
    (cov_2lfieank0g().b[21][1]++, '')}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 121,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    className: "sidebar-icon",
    width: "20",
    height: "20",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 122,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 123,
      columnNumber: 13
    }
  })), "Documents")),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "sidebar-user",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 130,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "user-avatar",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 131,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    width: "16",
    height: "16",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 132,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",
    fill: "currentColor",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 133,
      columnNumber: 13
    }
  }))),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex-1",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 136,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "user-email",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 137,
      columnNumber: 11
    }
  }, displayEmail),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "user-role",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 138,
      columnNumber: 11
    }
  }, displayName)),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "button",
  /* istanbul ignore next */
  {
    onClick: signOut,
    className: "sidebar-signout",
    title: "Sign out",
    /* istanbul ignore next */
    "aria-label": "Sign out",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 140,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "svg",
  /* istanbul ignore next */
  {
    width: "18",
    height: "18",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 146,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "path",
  /* istanbul ignore next */
  {
    d: "M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 147,
      columnNumber: 13
    }
  })))));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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