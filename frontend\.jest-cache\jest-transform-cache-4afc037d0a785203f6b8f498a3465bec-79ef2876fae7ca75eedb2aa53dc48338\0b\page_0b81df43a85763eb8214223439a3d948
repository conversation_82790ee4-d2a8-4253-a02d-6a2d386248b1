3ed177ca180192e1f8fc5bee849a0dd0
'use client';

/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\callback\\page.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_1m3coxoxpf() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\callback\\page.tsx";
  var hash = "3d7f17a5fa3f270dea8a9266598d6ff7f2fbbd86";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\callback\\page.tsx",
    statementMap: {
      "0": {
        start: {
          line: 10,
          column: 17
        },
        end: {
          line: 10,
          column: 28
        }
      },
      "1": {
        start: {
          line: 11,
          column: 23
        },
        end: {
          line: 11,
          column: 40
        }
      },
      "2": {
        start: {
          line: 12,
          column: 28
        },
        end: {
          line: 12,
          column: 57
        }
      },
      "3": {
        start: {
          line: 13,
          column: 30
        },
        end: {
          line: 13,
          column: 63
        }
      },
      "4": {
        start: {
          line: 15,
          column: 2
        },
        end: {
          line: 97,
          column: 28
        }
      },
      "5": {
        start: {
          line: 16,
          column: 20
        },
        end: {
          line: 16,
          column: 24
        }
      },
      "6": {
        start: {
          line: 19,
          column: 6
        },
        end: {
          line: 89,
          column: 7
        }
      },
      "7": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 20,
          column: 71
        }
      },
      "8": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 26
        }
      },
      "9": {
        start: {
          line: 26,
          column: 21
        },
        end: {
          line: 26,
          column: 45
        }
      },
      "10": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 29,
          column: 9
        }
      },
      "11": {
        start: {
          line: 28,
          column: 10
        },
        end: {
          line: 28,
          column: 63
        }
      },
      "12": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 79
        }
      },
      "13": {
        start: {
          line: 33,
          column: 8
        },
        end: {
          line: 80,
          column: 9
        }
      },
      "14": {
        start: {
          line: 35,
          column: 34
        },
        end: {
          line: 35,
          column: 58
        }
      },
      "15": {
        start: {
          line: 36,
          column: 10
        },
        end: {
          line: 42,
          column: 11
        }
      },
      "16": {
        start: {
          line: 37,
          column: 12
        },
        end: {
          line: 37,
          column: 77
        }
      },
      "17": {
        start: {
          line: 38,
          column: 12
        },
        end: {
          line: 38,
          column: 69
        }
      },
      "18": {
        start: {
          line: 39,
          column: 12
        },
        end: {
          line: 39,
          column: 60
        }
      },
      "19": {
        start: {
          line: 40,
          column: 12
        },
        end: {
          line: 40,
          column: 38
        }
      },
      "20": {
        start: {
          line: 41,
          column: 12
        },
        end: {
          line: 41,
          column: 19
        }
      },
      "21": {
        start: {
          line: 45,
          column: 10
        },
        end: {
          line: 45,
          column: 37
        }
      },
      "22": {
        start: {
          line: 46,
          column: 10
        },
        end: {
          line: 46,
          column: 55
        }
      },
      "23": {
        start: {
          line: 49,
          column: 26
        },
        end: {
          line: 49,
          column: 72
        }
      },
      "24": {
        start: {
          line: 50,
          column: 10
        },
        end: {
          line: 50,
          column: 98
        }
      },
      "25": {
        start: {
          line: 52,
          column: 10
        },
        end: {
          line: 64,
          column: 11
        }
      },
      "26": {
        start: {
          line: 53,
          column: 12
        },
        end: {
          line: 53,
          column: 53
        }
      },
      "27": {
        start: {
          line: 55,
          column: 12
        },
        end: {
          line: 55,
          column: 61
        }
      },
      "28": {
        start: {
          line: 58,
          column: 12
        },
        end: {
          line: 58,
          column: 60
        }
      },
      "29": {
        start: {
          line: 61,
          column: 12
        },
        end: {
          line: 61,
          column: 38
        }
      },
      "30": {
        start: {
          line: 63,
          column: 12
        },
        end: {
          line: 63,
          column: 84
        }
      },
      "31": {
        start: {
          line: 66,
          column: 10
        },
        end: {
          line: 66,
          column: 59
        }
      },
      "32": {
        start: {
          line: 68,
          column: 10
        },
        end: {
          line: 78,
          column: 11
        }
      },
      "33": {
        start: {
          line: 69,
          column: 12
        },
        end: {
          line: 69,
          column: 79
        }
      },
      "34": {
        start: {
          line: 71,
          column: 28
        },
        end: {
          line: 71,
          column: 74
        }
      },
      "35": {
        start: {
          line: 72,
          column: 12
        },
        end: {
          line: 75,
          column: 13
        }
      },
      "36": {
        start: {
          line: 73,
          column: 14
        },
        end: {
          line: 73,
          column: 63
        }
      },
      "37": {
        start: {
          line: 74,
          column: 14
        },
        end: {
          line: 74,
          column: 62
        }
      },
      "38": {
        start: {
          line: 76,
          column: 12
        },
        end: {
          line: 76,
          column: 38
        }
      },
      "39": {
        start: {
          line: 77,
          column: 12
        },
        end: {
          line: 77,
          column: 19
        }
      },
      "40": {
        start: {
          line: 79,
          column: 10
        },
        end: {
          line: 79,
          column: 22
        }
      },
      "41": {
        start: {
          line: 82,
          column: 8
        },
        end: {
          line: 82,
          column: 53
        }
      },
      "42": {
        start: {
          line: 83,
          column: 8
        },
        end: {
          line: 88,
          column: 9
        }
      },
      "43": {
        start: {
          line: 84,
          column: 10
        },
        end: {
          line: 84,
          column: 61
        }
      },
      "44": {
        start: {
          line: 85,
          column: 10
        },
        end: {
          line: 87,
          column: 18
        }
      },
      "45": {
        start: {
          line: 86,
          column: 12
        },
        end: {
          line: 86,
          column: 33
        }
      },
      "46": {
        start: {
          line: 92,
          column: 4
        },
        end: {
          line: 92,
          column: 20
        }
      },
      "47": {
        start: {
          line: 94,
          column: 4
        },
        end: {
          line: 96,
          column: 5
        }
      },
      "48": {
        start: {
          line: 95,
          column: 6
        },
        end: {
          line: 95,
          column: 23
        }
      },
      "49": {
        start: {
          line: 99,
          column: 2
        },
        end: {
          line: 114,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "CallbackPage",
        decl: {
          start: {
            line: 9,
            column: 24
          },
          end: {
            line: 9,
            column: 36
          }
        },
        loc: {
          start: {
            line: 9,
            column: 39
          },
          end: {
            line: 115,
            column: 1
          }
        },
        line: 9
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 15,
            column: 12
          },
          end: {
            line: 15,
            column: 13
          }
        },
        loc: {
          start: {
            line: 15,
            column: 18
          },
          end: {
            line: 97,
            column: 3
          }
        },
        line: 15
      },
      "2": {
        name: "handleCallback",
        decl: {
          start: {
            line: 18,
            column: 19
          },
          end: {
            line: 18,
            column: 33
          }
        },
        loc: {
          start: {
            line: 18,
            column: 36
          },
          end: {
            line: 90,
            column: 5
          }
        },
        line: 18
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 85,
            column: 21
          },
          end: {
            line: 85,
            column: 22
          }
        },
        loc: {
          start: {
            line: 85,
            column: 27
          },
          end: {
            line: 87,
            column: 11
          }
        },
        line: 85
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 94,
            column: 11
          },
          end: {
            line: 94,
            column: 12
          }
        },
        loc: {
          start: {
            line: 94,
            column: 17
          },
          end: {
            line: 96,
            column: 5
          }
        },
        line: 94
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 29,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 29,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "1": {
        loc: {
          start: {
            line: 36,
            column: 10
          },
          end: {
            line: 42,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 36,
            column: 10
          },
          end: {
            line: 42,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 36
      },
      "2": {
        loc: {
          start: {
            line: 50,
            column: 66
          },
          end: {
            line: 50,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 50,
            column: 76
          },
          end: {
            line: 50,
            column: 85
          }
        }, {
          start: {
            line: 50,
            column: 88
          },
          end: {
            line: 50,
            column: 96
          }
        }],
        line: 50
      },
      "3": {
        loc: {
          start: {
            line: 52,
            column: 10
          },
          end: {
            line: 64,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 52,
            column: 10
          },
          end: {
            line: 64,
            column: 11
          }
        }, {
          start: {
            line: 62,
            column: 17
          },
          end: {
            line: 64,
            column: 11
          }
        }],
        line: 52
      },
      "4": {
        loc: {
          start: {
            line: 68,
            column: 10
          },
          end: {
            line: 78,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 68,
            column: 10
          },
          end: {
            line: 78,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 68
      },
      "5": {
        loc: {
          start: {
            line: 68,
            column: 14
          },
          end: {
            line: 68,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 68,
            column: 14
          },
          end: {
            line: 68,
            column: 27
          }
        }, {
          start: {
            line: 68,
            column: 31
          },
          end: {
            line: 68,
            column: 74
          }
        }],
        line: 68
      },
      "6": {
        loc: {
          start: {
            line: 72,
            column: 12
          },
          end: {
            line: 75,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 12
          },
          end: {
            line: 75,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "7": {
        loc: {
          start: {
            line: 83,
            column: 8
          },
          end: {
            line: 88,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 83,
            column: 8
          },
          end: {
            line: 88,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 83
      },
      "8": {
        loc: {
          start: {
            line: 101,
            column: 7
          },
          end: {
            line: 112,
            column: 7
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 102,
            column: 8
          },
          end: {
            line: 105,
            column: 14
          }
        }, {
          start: {
            line: 107,
            column: 8
          },
          end: {
            line: 111,
            column: 14
          }
        }],
        line: 101
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "3d7f17a5fa3f270dea8a9266598d6ff7f2fbbd86"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1m3coxoxpf = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1m3coxoxpf();
import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { fetchAuthSession, signInWithRedirect } from 'aws-amplify/auth';
import { setAuthCookie } from '@/lib/auth';
import { configureAmplify } from '@/lib/amplify-config';
export default function CallbackPage() {
  /* istanbul ignore next */
  cov_1m3coxoxpf().f[0]++;
  const router =
  /* istanbul ignore next */
  (cov_1m3coxoxpf().s[0]++, useRouter());
  const searchParams =
  /* istanbul ignore next */
  (cov_1m3coxoxpf().s[1]++, useSearchParams());
  const [error, setError] =
  /* istanbul ignore next */
  (cov_1m3coxoxpf().s[2]++, useState(null));
  const [status, setStatus] =
  /* istanbul ignore next */
  (cov_1m3coxoxpf().s[3]++, useState('Completing sign-in...'));
  /* istanbul ignore next */
  cov_1m3coxoxpf().s[4]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_1m3coxoxpf().f[1]++;
    let isMounted =
    /* istanbul ignore next */
    (cov_1m3coxoxpf().s[5]++, true);
    async function handleCallback() {
      /* istanbul ignore next */
      cov_1m3coxoxpf().f[2]++;
      cov_1m3coxoxpf().s[6]++;
      try {
        /* istanbul ignore next */
        cov_1m3coxoxpf().s[7]++;
        console.log('Callback page loaded, handling OAuth callback...');

        // Ensure Amplify is configured
        /* istanbul ignore next */
        cov_1m3coxoxpf().s[8]++;
        configureAmplify();

        // Get the code from URL
        const code =
        /* istanbul ignore next */
        (cov_1m3coxoxpf().s[9]++, searchParams.get('code'));
        /* istanbul ignore next */
        cov_1m3coxoxpf().s[10]++;
        if (!code) {
          /* istanbul ignore next */
          cov_1m3coxoxpf().b[0][0]++;
          cov_1m3coxoxpf().s[11]++;
          throw new Error('No authorization code found in URL');
        } else
        /* istanbul ignore next */
        {
          cov_1m3coxoxpf().b[0][1]++;
        }
        cov_1m3coxoxpf().s[12]++;
        console.log('Authorization code found:', code.substring(0, 10) + '...');
        /* istanbul ignore next */
        cov_1m3coxoxpf().s[13]++;
        try {
          // Try to fetch the session first to see if we're already authenticated
          const existingSession =
          /* istanbul ignore next */
          (cov_1m3coxoxpf().s[14]++, await fetchAuthSession());
          /* istanbul ignore next */
          cov_1m3coxoxpf().s[15]++;
          if (existingSession?.tokens?.idToken) {
            /* istanbul ignore next */
            cov_1m3coxoxpf().b[1][0]++;
            cov_1m3coxoxpf().s[16]++;
            console.log('User is already signed in, using existing session');
            /* istanbul ignore next */
            cov_1m3coxoxpf().s[17]++;
            setAuthCookie(existingSession.tokens.idToken.toString());
            /* istanbul ignore next */
            cov_1m3coxoxpf().s[18]++;
            localStorage.setItem('isAuthenticated', 'true');
            /* istanbul ignore next */
            cov_1m3coxoxpf().s[19]++;
            router.push('/dashboard');
            /* istanbul ignore next */
            cov_1m3coxoxpf().s[20]++;
            return;
          } else
          /* istanbul ignore next */
          {
            cov_1m3coxoxpf().b[1][1]++;
          }

          // If not already authenticated, complete the sign-in
          cov_1m3coxoxpf().s[21]++;
          await signInWithRedirect();
          /* istanbul ignore next */
          cov_1m3coxoxpf().s[22]++;
          console.log('Redirect handled successfully');

          // Then fetch the session
          const session =
          /* istanbul ignore next */
          (cov_1m3coxoxpf().s[23]++, await fetchAuthSession({
            forceRefresh: true
          }));
          /* istanbul ignore next */
          cov_1m3coxoxpf().s[24]++;
          console.log('Session fetched after handling redirect:', session ?
          /* istanbul ignore next */
          (cov_1m3coxoxpf().b[2][0]++, 'success') :
          /* istanbul ignore next */
          (cov_1m3coxoxpf().b[2][1]++, 'failed'));
          /* istanbul ignore next */
          cov_1m3coxoxpf().s[25]++;
          if (session?.tokens?.idToken) {
            /* istanbul ignore next */
            cov_1m3coxoxpf().b[3][0]++;
            cov_1m3coxoxpf().s[26]++;
            console.log('ID token found in session');
            // Set the cookie with the token
            /* istanbul ignore next */
            cov_1m3coxoxpf().s[27]++;
            setAuthCookie(session.tokens.idToken.toString());

            // Set flag in localStorage for cross-tab auth state
            /* istanbul ignore next */
            cov_1m3coxoxpf().s[28]++;
            localStorage.setItem('isAuthenticated', 'true');

            // Redirect to dashboard
            /* istanbul ignore next */
            cov_1m3coxoxpf().s[29]++;
            router.push('/dashboard');
          } else {
            /* istanbul ignore next */
            cov_1m3coxoxpf().b[3][1]++;
            cov_1m3coxoxpf().s[30]++;
            throw new Error('No ID token found in session after handling redirect');
          }
        } catch (error) {
          /* istanbul ignore next */
          cov_1m3coxoxpf().s[31]++;
          console.error('Error handling redirect:', error);
          // If the error is because user is already signed in, redirect to dashboard
          /* istanbul ignore next */
          cov_1m3coxoxpf().s[32]++;
          if (
          /* istanbul ignore next */
          (cov_1m3coxoxpf().b[5][0]++, error.message) &&
          /* istanbul ignore next */
          (cov_1m3coxoxpf().b[5][1]++, error.message.includes('already signed in'))) {
            /* istanbul ignore next */
            cov_1m3coxoxpf().b[4][0]++;
            cov_1m3coxoxpf().s[33]++;
            console.log('User is already signed in, redirecting to dashboard');
            // Try to get the session directly
            const session =
            /* istanbul ignore next */
            (cov_1m3coxoxpf().s[34]++, await fetchAuthSession({
              forceRefresh: true
            }));
            /* istanbul ignore next */
            cov_1m3coxoxpf().s[35]++;
            if (session?.tokens?.idToken) {
              /* istanbul ignore next */
              cov_1m3coxoxpf().b[6][0]++;
              cov_1m3coxoxpf().s[36]++;
              setAuthCookie(session.tokens.idToken.toString());
              /* istanbul ignore next */
              cov_1m3coxoxpf().s[37]++;
              localStorage.setItem('isAuthenticated', 'true');
            } else
            /* istanbul ignore next */
            {
              cov_1m3coxoxpf().b[6][1]++;
            }
            cov_1m3coxoxpf().s[38]++;
            router.push('/dashboard');
            /* istanbul ignore next */
            cov_1m3coxoxpf().s[39]++;
            return;
          } else
          /* istanbul ignore next */
          {
            cov_1m3coxoxpf().b[4][1]++;
          }
          cov_1m3coxoxpf().s[40]++;
          throw error;
        }
      } catch (error) {
        /* istanbul ignore next */
        cov_1m3coxoxpf().s[41]++;
        console.error('Authentication error:', error);
        /* istanbul ignore next */
        cov_1m3coxoxpf().s[42]++;
        if (isMounted) {
          /* istanbul ignore next */
          cov_1m3coxoxpf().b[7][0]++;
          cov_1m3coxoxpf().s[43]++;
          setError(`Authentication failed: ${error.message}`);
          /* istanbul ignore next */
          cov_1m3coxoxpf().s[44]++;
          setTimeout(() => {
            /* istanbul ignore next */
            cov_1m3coxoxpf().f[3]++;
            cov_1m3coxoxpf().s[45]++;
            router.push('/login');
          }, 2000);
        } else
        /* istanbul ignore next */
        {
          cov_1m3coxoxpf().b[7][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_1m3coxoxpf().s[46]++;
    handleCallback();
    /* istanbul ignore next */
    cov_1m3coxoxpf().s[47]++;
    return () => {
      /* istanbul ignore next */
      cov_1m3coxoxpf().f[4]++;
      cov_1m3coxoxpf().s[48]++;
      isMounted = false;
    };
  }, [router, searchParams]);
  /* istanbul ignore next */
  cov_1m3coxoxpf().s[49]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex flex-col items-center justify-center h-screen p-4",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 100,
      columnNumber: 5
    }
  }, error ?
  /* istanbul ignore next */
  (cov_1m3coxoxpf().b[8][0]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "p-4 bg-red-100 border border-red-400 rounded max-w-md w-full",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 102,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-red-700",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 103,
      columnNumber: 11
    }
  }, error),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "text-sm text-gray-600 mt-2",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 104,
      columnNumber: 11
    }
  }, "Redirecting to login page..."))) :
  /* istanbul ignore next */
  (cov_1m3coxoxpf().b[8][1]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "p-4 text-center",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 107,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h1",
  /* istanbul ignore next */
  {
    className: "text-2xl font-bold mb-4",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 108,
      columnNumber: 11
    }
  }, "Welcome Back!"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "mb-4",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 109,
      columnNumber: 11
    }
  }, status),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin mx-auto",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 110,
      columnNumber: 11
    }
  }))));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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