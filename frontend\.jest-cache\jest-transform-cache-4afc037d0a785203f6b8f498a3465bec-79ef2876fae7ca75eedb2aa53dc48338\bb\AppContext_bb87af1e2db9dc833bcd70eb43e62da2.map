{"version": 3, "names": ["_defineProperty", "_jsxFileName", "__jsx", "React", "createElement", "cov_21c2hnsjtl", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "_coverageSchema", "coverage", "actualCoverage", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "createContext", "useContext", "useState", "useEffect", "fetchAuthSession", "signOut", "amplifySignOut", "getCurrentUser", "getAmplifyCurrentUser", "fetchUserAttributes", "AppContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "isAuthenticated", "setIsAuthenticated", "isLoading", "setIsLoading", "user", "setUser", "tenant", "<PERSON><PERSON><PERSON><PERSON>", "tenantLoading", "setTenantLoading", "tenantError", "setTenantError", "currentUser", "attributes", "id", "userId", "email", "given_name", "family_name", "roles", "JSON", "parse", "preferences", "theme", "notifications", "sms", "displayDensity", "error", "console", "fetch<PERSON><PERSON>t", "retryCount", "response", "fetch", "data", "json", "ok", "status", "log", "setTimeout", "Error", "success", "client", "clientName", "err", "errorMessage", "message", "isMounted", "checkAuth", "urlParams", "URLSearchParams", "window", "location", "search", "hasOAuthCode", "has", "Promise", "resolve", "session", "forceRefresh", "authenticated", "tokens", "idToken", "hasSession", "hasTokens", "hasIdToken", "userInfo", "localStorage", "removeItem", "document", "cookie", "refreshTenant", "updateUserPreferences", "updatedPreferences", "prev", "setItem", "stringify", "hasRole", "role", "includes", "getDisplayName", "split", "substring", "value", "Provider", "__self", "__source", "fileName", "lineNumber", "columnNumber", "useApp", "context", "useAuth", "useTenant", "loading", "useUser"], "sources": ["AppContext.tsx"], "sourcesContent": ["/**\n * Consolidated Application Context\n * \n * This module consolidates authentication, user, client, and tenant contexts\n * into a single, efficient context provider to eliminate redundancy and\n * improve performance.\n */\n\n'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport {\n  fetchAuthSession,\n  signOut as amplifySignOut,\n  getCurrentUser as getAmplifyCurrentUser,\n  fetchUserAttributes\n} from 'aws-amplify/auth';\nimport { TenantContext as TenantContextType } from '@/lib/clients';\n\n// Consolidated interfaces\nexport interface User {\n  id: string;\n  email: string;\n  name?: string;\n  given_name?: string;\n  family_name?: string;\n  roles: string[];\n  preferences?: UserPreferences;\n  lastLogin?: Date;\n}\n\nexport interface UserPreferences {\n  theme?: 'light' | 'dark' | 'system';\n  notifications?: {\n    email?: boolean;\n    push?: boolean;\n    sms?: boolean;\n  };\n  displayDensity?: 'comfortable' | 'compact';\n  [key: string]: any;\n}\n\nexport interface AppState {\n  // Authentication state\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  user: User | null;\n  \n  // Tenant/Client state\n  tenant: TenantContextType | null;\n  tenantLoading: boolean;\n  tenantError: string | null;\n  \n  // Actions\n  signOut: () => Promise<boolean>;\n  refreshTenant: () => Promise<void>;\n  updateUserPreferences: (preferences: Partial<UserPreferences>) => Promise<boolean>;\n  hasRole: (role: string) => boolean;\n  getDisplayName: () => string;\n}\n\nconst AppContext = createContext<AppState | undefined>(undefined);\n\ninterface AppProviderProps {\n  children: ReactNode;\n}\n\nexport function AppProvider({ children }: AppProviderProps) {\n  // Authentication state\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [user, setUser] = useState<User | null>(null);\n  \n  // Tenant state\n  const [tenant, setTenant] = useState<TenantContextType | null>(null);\n  const [tenantLoading, setTenantLoading] = useState(false);\n  const [tenantError, setTenantError] = useState<string | null>(null);\n\n  // Get current user from Amplify\n  const getCurrentUser = async (): Promise<User | null> => {\n    try {\n      const currentUser = await getAmplifyCurrentUser();\n      const attributes = await fetchUserAttributes();\n      \n      return {\n        id: currentUser.userId,\n        email: attributes.email || '',\n        name: attributes.name,\n        given_name: attributes.given_name,\n        family_name: attributes.family_name,\n        roles: attributes['custom:roles'] ? JSON.parse(attributes['custom:roles']) : [],\n        preferences: {\n          theme: 'system',\n          notifications: {\n            email: true,\n            push: true,\n            sms: false\n          },\n          displayDensity: 'comfortable'\n        }\n      };\n    } catch (error) {\n      console.error('Error getting current user:', error);\n      return null;\n    }\n  };\n\n  // Fetch tenant information\n  const fetchTenant = async (retryCount = 0): Promise<void> => {\n    try {\n      setTenantLoading(true);\n      setTenantError(null);\n      \n      const response = await fetch('/api/clients/domain');\n      const data = await response.json();\n      \n      if (!response.ok) {\n        // If unauthorized and we haven't retried much, wait and retry\n        if (response.status === 401 && retryCount < 3) {\n          console.log(`Tenant fetch failed (401), retrying in ${(retryCount + 1) * 1000}ms... (attempt ${retryCount + 1}/3)`);\n          setTimeout(() => {\n            fetchTenant(retryCount + 1);\n          }, (retryCount + 1) * 1000);\n          return;\n        }\n        throw new Error(data.error || 'Failed to fetch tenant information');\n      }\n      \n      if (data.success && data.client) {\n        setTenant(data.client);\n        console.log('✅ Tenant context loaded successfully:', data.client.clientName);\n      } else {\n        throw new Error('Invalid response format');\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';\n      console.error('❌ Tenant fetch error:', errorMessage);\n      setTenantError(errorMessage);\n      setTenant(null);\n    } finally {\n      setTenantLoading(false);\n    }\n  };\n\n  // Authentication check\n  useEffect(() => {\n    let isMounted = true;\n\n    const checkAuth = async () => {\n      if (!isMounted) return;\n\n      try {\n        console.log('🔍 [APP-CONTEXT] Starting authentication check...');\n\n        // Check if this is an OAuth callback\n        const urlParams = new URLSearchParams(window.location.search);\n        const hasOAuthCode = urlParams.has('code');\n\n        if (hasOAuthCode) {\n          console.log('🔄 [APP-CONTEXT] OAuth callback detected, giving Amplify extra time...');\n          await new Promise(resolve => setTimeout(resolve, 5000));\n        } else {\n          await new Promise(resolve => setTimeout(resolve, 1000));\n        }\n\n        // Check if authenticated with Amplify\n        const session = await fetchAuthSession({ forceRefresh: false });\n        const authenticated = !!session?.tokens?.idToken;\n\n        console.log('🔍 [APP-CONTEXT] Session details:', {\n          hasSession: !!session,\n          hasTokens: !!session?.tokens,\n          hasIdToken: !!session?.tokens?.idToken,\n          authenticated\n        });\n\n        if (authenticated) {\n          console.log('✅ [APP-CONTEXT] Session is authenticated, getting user info...');\n\n          const userInfo = await getCurrentUser();\n          console.log('✅ [APP-CONTEXT] User info retrieved:', userInfo);\n\n          if (isMounted && userInfo) {\n            setUser(userInfo);\n            setIsAuthenticated(true);\n            \n            // Fetch tenant information after authentication\n            setTimeout(() => {\n              fetchTenant();\n            }, 1000);\n          }\n        } else {\n          console.log('❌ [APP-CONTEXT] No valid session found');\n          if (isMounted) {\n            setUser(null);\n            setIsAuthenticated(false);\n            setTenant(null);\n          }\n        }\n      } catch (error) {\n        console.error('❌ [APP-CONTEXT] Auth check error:', error);\n        if (isMounted) {\n          setUser(null);\n          setIsAuthenticated(false);\n          setTenant(null);\n        }\n      } finally {\n        if (isMounted) {\n          setIsLoading(false);\n        }\n      }\n    };\n\n    checkAuth();\n    \n    return () => {\n      isMounted = false;\n    };\n  }, []);\n\n  // Sign out function\n  const signOut = async (): Promise<boolean> => {\n    try {\n      await amplifySignOut({ global: true });\n      \n      // Clear localStorage\n      localStorage.removeItem('isAuthenticated');\n      \n      // Clear the auth cookie\n      document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';\n      \n      // Update state\n      setUser(null);\n      setIsAuthenticated(false);\n      setTenant(null);\n      \n      return true;\n    } catch (error) {\n      console.error('Error signing out:', error);\n      \n      // Clear state anyway\n      setUser(null);\n      setIsAuthenticated(false);\n      setTenant(null);\n      \n      return true;\n    }\n  };\n\n  // Refresh tenant information\n  const refreshTenant = async (): Promise<void> => {\n    await fetchTenant();\n  };\n\n  // Update user preferences\n  const updateUserPreferences = async (preferences: Partial<UserPreferences>): Promise<boolean> => {\n    if (!user?.id) return false;\n    \n    try {\n      // Update in state first for immediate feedback\n      const updatedPreferences = { ...user.preferences, ...preferences };\n      setUser(prev => prev ? { ...prev, preferences: updatedPreferences } : null);\n      \n      // Cache in localStorage\n      localStorage.setItem(`user_prefs_${user.id}`, JSON.stringify(updatedPreferences));\n      \n      return true;\n    } catch (error) {\n      console.error('Error updating user preferences:', error);\n      return false;\n    }\n  };\n\n  // Role checking utility\n  const hasRole = (role: string): boolean => {\n    return user?.roles?.includes(role) || false;\n  };\n\n  // Get formatted display name\n  const getDisplayName = (): string => {\n    if (!user) return 'User';\n    \n    if (user.given_name && user.family_name) {\n      return `${user.given_name} ${user.family_name}`;\n    }\n    \n    if (user.name) {\n      return user.name;\n    }\n    \n    if (user.email) {\n      return user.email.split('@')[0];\n    }\n    \n    return `User ${user.id.substring(0, 8)}`;\n  };\n\n  const value: AppState = {\n    isAuthenticated,\n    isLoading,\n    user,\n    tenant,\n    tenantLoading,\n    tenantError,\n    signOut,\n    refreshTenant,\n    updateUserPreferences,\n    hasRole,\n    getDisplayName,\n  };\n\n  return (\n    <AppContext.Provider value={value}>\n      {children}\n    </AppContext.Provider>\n  );\n}\n\n// Custom hook for using app context\nexport const useApp = () => {\n  const context = useContext(AppContext);\n  if (context === undefined) {\n    throw new Error('useApp must be used within an AppProvider');\n  }\n  return context;\n};\n\n// Backward compatibility hooks\nexport const useAuth = () => {\n  const { isAuthenticated, isLoading, user, signOut, hasRole } = useApp();\n  return { isAuthenticated, isLoading, user, signOut, hasRole };\n};\n\nexport const useTenant = () => {\n  const { tenant, tenantLoading, tenantError, refreshTenant } = useApp();\n  return { tenant, loading: tenantLoading, error: tenantError, refreshTenant };\n};\n\nexport const useUser = () => {\n  const { user, isLoading, updateUserPreferences, getDisplayName, hasRole } = useApp();\n  return { user, isLoading, updateUserPreferences, getDisplayName, hasRole };\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAAC;AAAA,OAAAA,eAAA;AAAA,IAAAC,YAAA;AAAA,IAAAC,KAAA,GAAAC,KAAA,CAAAC,aAAA;AAAA,SAAAC,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,eAAA;IAAArB,IAAA;EAAA;EAAA,IAAAsB,QAAA,GAAArB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAmB,QAAA,CAAAvB,IAAA,KAAAuB,QAAA,CAAAvB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAsB,QAAA,CAAAvB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAmB,cAAA,GAAAD,QAAA,CAAAvB,IAAA;EAAA;IAOD;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAyB,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAzB,cAAA;AAAA,SAAA0B,QAAAC,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAJ,CAAA,OAAAG,MAAA,CAAAE,qBAAA,QAAAC,CAAA,GAAAH,MAAA,CAAAE,qBAAA,CAAAL,CAAA,GAAAC,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAC,MAAA,WAAAN,CAAA,WAAAE,MAAA,CAAAK,wBAAA,CAAAR,CAAA,EAAAC,CAAA,EAAAQ,UAAA,OAAAP,CAAA,CAAAQ,IAAA,CAAAC,KAAA,CAAAT,CAAA,EAAAI,CAAA,YAAAJ,CAAA;AAAA,SAAAU,cAAAZ,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAY,SAAA,CAAAC,MAAA,EAAAb,CAAA,UAAAC,CAAA,WAAAW,SAAA,CAAAZ,CAAA,IAAAY,SAAA,CAAAZ,CAAA,QAAAA,CAAA,OAAAF,OAAA,CAAAI,MAAA,CAAAD,CAAA,OAAAa,OAAA,WAAAd,CAAA,IAAAjC,eAAA,CAAAgC,CAAA,EAAAC,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAa,yBAAA,GAAAb,MAAA,CAAAc,gBAAA,CAAAjB,CAAA,EAAAG,MAAA,CAAAa,yBAAA,CAAAd,CAAA,KAAAH,OAAA,CAAAI,MAAA,CAAAD,CAAA,GAAAa,OAAA,WAAAd,CAAA,IAAAE,MAAA,CAAAe,cAAA,CAAAlB,CAAA,EAAAC,CAAA,EAAAE,MAAA,CAAAK,wBAAA,CAAAN,CAAA,EAAAD,CAAA,iBAAAD,CAAA;AALZ,OAAO7B,KAAK,IAAIgD,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;AACxF,SACEC,gBAAgB,EAChBC,OAAO,IAAIC,cAAc,EACzBC,cAAc,IAAIC,qBAAqB,EACvCC,mBAAmB,QACd,kBAAkB;;AAGzB;;AA0CA,MAAMC,UAAU;AAAA;AAAA,CAAAxD,cAAA,GAAAoB,CAAA,oBAAG0B,aAAa,CAAuB3B,SAAS,CAAC;AAMjE,OAAO,SAASsC,WAAWA,CAAC;EAAEC;AAA2B,CAAC,EAAE;EAAA;EAAA1D,cAAA,GAAAqB,CAAA;EAC1D;EACA,MAAM,CAACsC,eAAe,EAAEC,kBAAkB,CAAC;EAAA;EAAA,CAAA5D,cAAA,GAAAoB,CAAA,OAAG4B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC;EAAA;EAAA,CAAA9D,cAAA,GAAAoB,CAAA,OAAG4B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACe,IAAI,EAAEC,OAAO,CAAC;EAAA;EAAA,CAAAhE,cAAA,GAAAoB,CAAA,OAAG4B,QAAQ,CAAc,IAAI,CAAC;;EAEnD;EACA,MAAM,CAACiB,MAAM,EAAEC,SAAS,CAAC;EAAA;EAAA,CAAAlE,cAAA,GAAAoB,CAAA,OAAG4B,QAAQ,CAA2B,IAAI,CAAC;EACpE,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC;EAAA;EAAA,CAAApE,cAAA,GAAAoB,CAAA,OAAG4B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC;EAAA;EAAA,CAAAtE,cAAA,GAAAoB,CAAA,OAAG4B,QAAQ,CAAgB,IAAI,CAAC;;EAEnE;EAAA;EAAAhD,cAAA,GAAAoB,CAAA;EACA,MAAMiC,cAAc,GAAG,MAAAA,CAAA,KAAkC;IAAA;IAAArD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACvD,IAAI;MACF,MAAMmD,WAAW;MAAA;MAAA,CAAAvE,cAAA,GAAAoB,CAAA,OAAG,MAAMkC,qBAAqB,CAAC,CAAC;MACjD,MAAMkB,UAAU;MAAA;MAAA,CAAAxE,cAAA,GAAAoB,CAAA,QAAG,MAAMmC,mBAAmB,CAAC,CAAC;MAAC;MAAAvD,cAAA,GAAAoB,CAAA;MAE/C,OAAO;QACLqD,EAAE,EAAEF,WAAW,CAACG,MAAM;QACtBC,KAAK;QAAE;QAAA,CAAA3E,cAAA,GAAAsB,CAAA,UAAAkD,UAAU,CAACG,KAAK;QAAA;QAAA,CAAA3E,cAAA,GAAAsB,CAAA,UAAI,EAAE;QAC7BT,IAAI,EAAE2D,UAAU,CAAC3D,IAAI;QACrB+D,UAAU,EAAEJ,UAAU,CAACI,UAAU;QACjCC,WAAW,EAAEL,UAAU,CAACK,WAAW;QACnCC,KAAK,EAAEN,UAAU,CAAC,cAAc,CAAC;QAAA;QAAA,CAAAxE,cAAA,GAAAsB,CAAA,UAAGyD,IAAI,CAACC,KAAK,CAACR,UAAU,CAAC,cAAc,CAAC,CAAC;QAAA;QAAA,CAAAxE,cAAA,GAAAsB,CAAA,UAAG,EAAE;QAC/E2D,WAAW,EAAE;UACXC,KAAK,EAAE,QAAQ;UACfC,aAAa,EAAE;YACbR,KAAK,EAAE,IAAI;YACXtC,IAAI,EAAE,IAAI;YACV+C,GAAG,EAAE;UACP,CAAC;UACDC,cAAc,EAAE;QAClB;MACF,CAAC;IACH,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA;MAAAtF,cAAA,GAAAoB,CAAA;MACdmE,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MAAC;MAAAtF,cAAA,GAAAoB,CAAA;MACpD,OAAO,IAAI;IACb;EACF,CAAC;;EAED;EAAA;EAAApB,cAAA,GAAAoB,CAAA;EACA,MAAMoE,WAAW,GAAG,MAAAA,CAAOC,UAAU;EAAA;EAAA,CAAAzF,cAAA,GAAAsB,CAAA,UAAG,CAAC,MAAoB;IAAA;IAAAtB,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC3D,IAAI;MAAA;MAAApB,cAAA,GAAAoB,CAAA;MACFgD,gBAAgB,CAAC,IAAI,CAAC;MAAC;MAAApE,cAAA,GAAAoB,CAAA;MACvBkD,cAAc,CAAC,IAAI,CAAC;MAEpB,MAAMoB,QAAQ;MAAA;MAAA,CAAA1F,cAAA,GAAAoB,CAAA,QAAG,MAAMuE,KAAK,CAAC,qBAAqB,CAAC;MACnD,MAAMC,IAAI;MAAA;MAAA,CAAA5F,cAAA,GAAAoB,CAAA,QAAG,MAAMsE,QAAQ,CAACG,IAAI,CAAC,CAAC;MAAC;MAAA7F,cAAA,GAAAoB,CAAA;MAEnC,IAAI,CAACsE,QAAQ,CAACI,EAAE,EAAE;QAAA;QAAA9F,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAChB;QACA;QAAI;QAAA,CAAApB,cAAA,GAAAsB,CAAA,UAAAoE,QAAQ,CAACK,MAAM,KAAK,GAAG;QAAA;QAAA,CAAA/F,cAAA,GAAAsB,CAAA,UAAImE,UAAU,GAAG,CAAC,GAAE;UAAA;UAAAzF,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAC7CmE,OAAO,CAACS,GAAG,CAAC,0CAA0C,CAACP,UAAU,GAAG,CAAC,IAAI,IAAI,kBAAkBA,UAAU,GAAG,CAAC,KAAK,CAAC;UAAC;UAAAzF,cAAA,GAAAoB,CAAA;UACpH6E,UAAU,CAAC,MAAM;YAAA;YAAAjG,cAAA,GAAAqB,CAAA;YAAArB,cAAA,GAAAoB,CAAA;YACfoE,WAAW,CAACC,UAAU,GAAG,CAAC,CAAC;UAC7B,CAAC,EAAE,CAACA,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC;UAAC;UAAAzF,cAAA,GAAAoB,CAAA;UAC5B;QACF,CAAC;QAAA;QAAA;UAAApB,cAAA,GAAAsB,CAAA;QAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACD,MAAM,IAAI8E,KAAK;QAAC;QAAA,CAAAlG,cAAA,GAAAsB,CAAA,UAAAsE,IAAI,CAACN,KAAK;QAAA;QAAA,CAAAtF,cAAA,GAAAsB,CAAA,UAAI,oCAAoC,EAAC;MACrE,CAAC;MAAA;MAAA;QAAAtB,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,UAAAsE,IAAI,CAACO,OAAO;MAAA;MAAA,CAAAnG,cAAA,GAAAsB,CAAA,UAAIsE,IAAI,CAACQ,MAAM,GAAE;QAAA;QAAApG,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC/B8C,SAAS,CAAC0B,IAAI,CAACQ,MAAM,CAAC;QAAC;QAAApG,cAAA,GAAAoB,CAAA;QACvBmE,OAAO,CAACS,GAAG,CAAC,uCAAuC,EAAEJ,IAAI,CAACQ,MAAM,CAACC,UAAU,CAAC;MAC9E,CAAC,MAAM;QAAA;QAAArG,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACL,MAAM,IAAI8E,KAAK,CAAC,yBAAyB,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZ,MAAMC,YAAY;MAAA;MAAA,CAAAvG,cAAA,GAAAoB,CAAA,QAAGkF,GAAG,YAAYJ,KAAK;MAAA;MAAA,CAAAlG,cAAA,GAAAsB,CAAA,UAAGgF,GAAG,CAACE,OAAO;MAAA;MAAA,CAAAxG,cAAA,GAAAsB,CAAA,UAAG,wBAAwB;MAAC;MAAAtB,cAAA,GAAAoB,CAAA;MACnFmE,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEiB,YAAY,CAAC;MAAC;MAAAvG,cAAA,GAAAoB,CAAA;MACrDkD,cAAc,CAACiC,YAAY,CAAC;MAAC;MAAAvG,cAAA,GAAAoB,CAAA;MAC7B8C,SAAS,CAAC,IAAI,CAAC;IACjB,CAAC,SAAS;MAAA;MAAAlE,cAAA,GAAAoB,CAAA;MACRgD,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EAAA;EAAApE,cAAA,GAAAoB,CAAA;EACA6B,SAAS,CAAC,MAAM;IAAA;IAAAjD,cAAA,GAAAqB,CAAA;IACd,IAAIoF,SAAS;IAAA;IAAA,CAAAzG,cAAA,GAAAoB,CAAA,QAAG,IAAI;IAAC;IAAApB,cAAA,GAAAoB,CAAA;IAErB,MAAMsF,SAAS,GAAG,MAAAA,CAAA,KAAY;MAAA;MAAA1G,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAC5B,IAAI,CAACqF,SAAS,EAAE;QAAA;QAAAzG,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAAA;MAAM,CAAC;MAAA;MAAA;QAAApB,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAEvB,IAAI;QAAA;QAAApB,cAAA,GAAAoB,CAAA;QACFmE,OAAO,CAACS,GAAG,CAAC,mDAAmD,CAAC;;QAEhE;QACA,MAAMW,SAAS;QAAA;QAAA,CAAA3G,cAAA,GAAAoB,CAAA,QAAG,IAAIwF,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;QAC7D,MAAMC,YAAY;QAAA;QAAA,CAAAhH,cAAA,GAAAoB,CAAA,QAAGuF,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC;QAAC;QAAAjH,cAAA,GAAAoB,CAAA;QAE3C,IAAI4F,YAAY,EAAE;UAAA;UAAAhH,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAChBmE,OAAO,CAACS,GAAG,CAAC,wEAAwE,CAAC;UAAC;UAAAhG,cAAA,GAAAoB,CAAA;UACtF,MAAM,IAAI8F,OAAO,CAACC,OAAO,IAAI;YAAA;YAAAnH,cAAA,GAAAqB,CAAA;YAAArB,cAAA,GAAAoB,CAAA;YAAA,OAAA6E,UAAU,CAACkB,OAAO,EAAE,IAAI,CAAC;UAAD,CAAC,CAAC;QACzD,CAAC,MAAM;UAAA;UAAAnH,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACL,MAAM,IAAI8F,OAAO,CAACC,OAAO,IAAI;YAAA;YAAAnH,cAAA,GAAAqB,CAAA;YAAArB,cAAA,GAAAoB,CAAA;YAAA,OAAA6E,UAAU,CAACkB,OAAO,EAAE,IAAI,CAAC;UAAD,CAAC,CAAC;QACzD;;QAEA;QACA,MAAMC,OAAO;QAAA;QAAA,CAAApH,cAAA,GAAAoB,CAAA,QAAG,MAAM8B,gBAAgB,CAAC;UAAEmE,YAAY,EAAE;QAAM,CAAC,CAAC;QAC/D,MAAMC,aAAa;QAAA;QAAA,CAAAtH,cAAA,GAAAoB,CAAA,QAAG,CAAC,CAACgG,OAAO,EAAEG,MAAM,EAAEC,OAAO;QAAC;QAAAxH,cAAA,GAAAoB,CAAA;QAEjDmE,OAAO,CAACS,GAAG,CAAC,mCAAmC,EAAE;UAC/CyB,UAAU,EAAE,CAAC,CAACL,OAAO;UACrBM,SAAS,EAAE,CAAC,CAACN,OAAO,EAAEG,MAAM;UAC5BI,UAAU,EAAE,CAAC,CAACP,OAAO,EAAEG,MAAM,EAAEC,OAAO;UACtCF;QACF,CAAC,CAAC;QAAC;QAAAtH,cAAA,GAAAoB,CAAA;QAEH,IAAIkG,aAAa,EAAE;UAAA;UAAAtH,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACjBmE,OAAO,CAACS,GAAG,CAAC,gEAAgE,CAAC;UAE7E,MAAM4B,QAAQ;UAAA;UAAA,CAAA5H,cAAA,GAAAoB,CAAA,QAAG,MAAMiC,cAAc,CAAC,CAAC;UAAC;UAAArD,cAAA,GAAAoB,CAAA;UACxCmE,OAAO,CAACS,GAAG,CAAC,sCAAsC,EAAE4B,QAAQ,CAAC;UAAC;UAAA5H,cAAA,GAAAoB,CAAA;UAE9D;UAAI;UAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAmF,SAAS;UAAA;UAAA,CAAAzG,cAAA,GAAAsB,CAAA,WAAIsG,QAAQ,GAAE;YAAA;YAAA5H,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACzB4C,OAAO,CAAC4D,QAAQ,CAAC;YAAC;YAAA5H,cAAA,GAAAoB,CAAA;YAClBwC,kBAAkB,CAAC,IAAI,CAAC;;YAExB;YAAA;YAAA5D,cAAA,GAAAoB,CAAA;YACA6E,UAAU,CAAC,MAAM;cAAA;cAAAjG,cAAA,GAAAqB,CAAA;cAAArB,cAAA,GAAAoB,CAAA;cACfoE,WAAW,CAAC,CAAC;YACf,CAAC,EAAE,IAAI,CAAC;UACV,CAAC;UAAA;UAAA;YAAAxF,cAAA,GAAAsB,CAAA;UAAA;QACH,CAAC,MAAM;UAAA;UAAAtB,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACLmE,OAAO,CAACS,GAAG,CAAC,wCAAwC,CAAC;UAAC;UAAAhG,cAAA,GAAAoB,CAAA;UACtD,IAAIqF,SAAS,EAAE;YAAA;YAAAzG,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACb4C,OAAO,CAAC,IAAI,CAAC;YAAC;YAAAhE,cAAA,GAAAoB,CAAA;YACdwC,kBAAkB,CAAC,KAAK,CAAC;YAAC;YAAA5D,cAAA,GAAAoB,CAAA;YAC1B8C,SAAS,CAAC,IAAI,CAAC;UACjB,CAAC;UAAA;UAAA;YAAAlE,cAAA,GAAAsB,CAAA;UAAA;QACH;MACF,CAAC,CAAC,OAAOgE,KAAK,EAAE;QAAA;QAAAtF,cAAA,GAAAoB,CAAA;QACdmE,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QAAC;QAAAtF,cAAA,GAAAoB,CAAA;QAC1D,IAAIqF,SAAS,EAAE;UAAA;UAAAzG,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACb4C,OAAO,CAAC,IAAI,CAAC;UAAC;UAAAhE,cAAA,GAAAoB,CAAA;UACdwC,kBAAkB,CAAC,KAAK,CAAC;UAAC;UAAA5D,cAAA,GAAAoB,CAAA;UAC1B8C,SAAS,CAAC,IAAI,CAAC;QACjB,CAAC;QAAA;QAAA;UAAAlE,cAAA,GAAAsB,CAAA;QAAA;MACH,CAAC,SAAS;QAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACR,IAAIqF,SAAS,EAAE;UAAA;UAAAzG,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACb0C,YAAY,CAAC,KAAK,CAAC;QACrB,CAAC;QAAA;QAAA;UAAA9D,cAAA,GAAAsB,CAAA;QAAA;MACH;IACF,CAAC;IAAC;IAAAtB,cAAA,GAAAoB,CAAA;IAEFsF,SAAS,CAAC,CAAC;IAAC;IAAA1G,cAAA,GAAAoB,CAAA;IAEZ,OAAO,MAAM;MAAA;MAAApB,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MACXqF,SAAS,GAAG,KAAK;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EAAA;EAAAzG,cAAA,GAAAoB,CAAA;EACA,MAAM+B,OAAO,GAAG,MAAAA,CAAA,KAA8B;IAAA;IAAAnD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC5C,IAAI;MAAA;MAAApB,cAAA,GAAAoB,CAAA;MACF,MAAMgC,cAAc,CAAC;QAAEjD,MAAM,EAAE;MAAK,CAAC,CAAC;;MAEtC;MAAA;MAAAH,cAAA,GAAAoB,CAAA;MACAyG,YAAY,CAACC,UAAU,CAAC,iBAAiB,CAAC;;MAE1C;MAAA;MAAA9H,cAAA,GAAAoB,CAAA;MACA2G,QAAQ,CAACC,MAAM,GAAG,2CAA2C;;MAE7D;MAAA;MAAAhI,cAAA,GAAAoB,CAAA;MACA4C,OAAO,CAAC,IAAI,CAAC;MAAC;MAAAhE,cAAA,GAAAoB,CAAA;MACdwC,kBAAkB,CAAC,KAAK,CAAC;MAAC;MAAA5D,cAAA,GAAAoB,CAAA;MAC1B8C,SAAS,CAAC,IAAI,CAAC;MAAC;MAAAlE,cAAA,GAAAoB,CAAA;MAEhB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOkE,KAAK,EAAE;MAAA;MAAAtF,cAAA,GAAAoB,CAAA;MACdmE,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;;MAE1C;MAAA;MAAAtF,cAAA,GAAAoB,CAAA;MACA4C,OAAO,CAAC,IAAI,CAAC;MAAC;MAAAhE,cAAA,GAAAoB,CAAA;MACdwC,kBAAkB,CAAC,KAAK,CAAC;MAAC;MAAA5D,cAAA,GAAAoB,CAAA;MAC1B8C,SAAS,CAAC,IAAI,CAAC;MAAC;MAAAlE,cAAA,GAAAoB,CAAA;MAEhB,OAAO,IAAI;IACb;EACF,CAAC;;EAED;EAAA;EAAApB,cAAA,GAAAoB,CAAA;EACA,MAAM6G,aAAa,GAAG,MAAAA,CAAA,KAA2B;IAAA;IAAAjI,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC/C,MAAMoE,WAAW,CAAC,CAAC;EACrB,CAAC;;EAED;EAAA;EAAAxF,cAAA,GAAAoB,CAAA;EACA,MAAM8G,qBAAqB,GAAG,MAAOjD,WAAqC,IAAuB;IAAA;IAAAjF,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IAC/F,IAAI,CAAC2C,IAAI,EAAEU,EAAE,EAAE;MAAA;MAAAzE,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,KAAK;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAE5B,IAAI;MACF;MACA,MAAM+G,kBAAkB;MAAA;MAAA,CAAAnI,cAAA,GAAAoB,CAAA,QAAAmB,aAAA,CAAAA,aAAA,KAAQwB,IAAI,CAACkB,WAAW,GAAKA,WAAW,EAAE;MAAC;MAAAjF,cAAA,GAAAoB,CAAA;MACnE4C,OAAO,CAACoE,IAAI,IAAI;QAAA;QAAApI,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAAgH,IAAI;QAAA;QAAA,CAAApI,cAAA,GAAAsB,CAAA,WAAAiB,aAAA,CAAAA,aAAA,KAAQ6F,IAAI;UAAEnD,WAAW,EAAEkD;QAAkB;QAAA;QAAA,CAAAnI,cAAA,GAAAsB,CAAA,WAAK,IAAI;MAAD,CAAC,CAAC;;MAE3E;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACAyG,YAAY,CAACQ,OAAO,CAAC,cAActE,IAAI,CAACU,EAAE,EAAE,EAAEM,IAAI,CAACuD,SAAS,CAACH,kBAAkB,CAAC,CAAC;MAAC;MAAAnI,cAAA,GAAAoB,CAAA;MAElF,OAAO,IAAI;IACb,CAAC,CAAC,OAAOkE,KAAK,EAAE;MAAA;MAAAtF,cAAA,GAAAoB,CAAA;MACdmE,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAAC;MAAAtF,cAAA,GAAAoB,CAAA;MACzD,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EAAA;EAAApB,cAAA,GAAAoB,CAAA;EACA,MAAMmH,OAAO,GAAIC,IAAY,IAAc;IAAA;IAAAxI,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACzC,OAAO,2BAAApB,cAAA,GAAAsB,CAAA,WAAAyC,IAAI,EAAEe,KAAK,EAAE2D,QAAQ,CAACD,IAAI,CAAC;IAAA;IAAA,CAAAxI,cAAA,GAAAsB,CAAA,WAAI,KAAK;EAC7C,CAAC;;EAED;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EACA,MAAMsH,cAAc,GAAGA,CAAA,KAAc;IAAA;IAAA1I,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACnC,IAAI,CAAC2C,IAAI,EAAE;MAAA;MAAA/D,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA,OAAO,MAAM;IAAA,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAEzB;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAAAyC,IAAI,CAACa,UAAU;IAAA;IAAA,CAAA5E,cAAA,GAAAsB,CAAA,WAAIyC,IAAI,CAACc,WAAW,GAAE;MAAA;MAAA7E,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACvC,OAAO,GAAG2C,IAAI,CAACa,UAAU,IAAIb,IAAI,CAACc,WAAW,EAAE;IACjD,CAAC;IAAA;IAAA;MAAA7E,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAI2C,IAAI,CAAClD,IAAI,EAAE;MAAA;MAAAb,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACb,OAAO2C,IAAI,CAAClD,IAAI;IAClB,CAAC;IAAA;IAAA;MAAAb,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,IAAI2C,IAAI,CAACY,KAAK,EAAE;MAAA;MAAA3E,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACd,OAAO2C,IAAI,CAACY,KAAK,CAACgE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IAAA;IAAA;MAAA3I,cAAA,GAAAsB,CAAA;IAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAED,OAAO,QAAQ2C,IAAI,CAACU,EAAE,CAACmE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EAC1C,CAAC;EAED,MAAMC,KAAe;EAAA;EAAA,CAAA7I,cAAA,GAAAoB,CAAA,SAAG;IACtBuC,eAAe;IACfE,SAAS;IACTE,IAAI;IACJE,MAAM;IACNE,aAAa;IACbE,WAAW;IACXlB,OAAO;IACP8E,aAAa;IACbC,qBAAqB;IACrBK,OAAO;IACPG;EACF,CAAC;EAAC;EAAA1I,cAAA,GAAAoB,CAAA;EAEF,OACE,0BAAAvB,KAAA;EAAA;EAAC2D,UAAU,CAACsF,QAAQ;EAAA;EAAA;IAACD,KAAK,EAAEA,KAAM;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAArJ,YAAA;MAAAsJ,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/BzF,QACkB,CAAC;AAE1B;;AAEA;AAAA;AAAA1D,cAAA,GAAAoB,CAAA;AACA,OAAO,MAAMgI,MAAM,GAAGA,CAAA,KAAM;EAAA;EAAApJ,cAAA,GAAAqB,CAAA;EAC1B,MAAMgI,OAAO;EAAA;EAAA,CAAArJ,cAAA,GAAAoB,CAAA,SAAG2B,UAAU,CAACS,UAAU,CAAC;EAAC;EAAAxD,cAAA,GAAAoB,CAAA;EACvC,IAAIiI,OAAO,KAAKlI,SAAS,EAAE;IAAA;IAAAnB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACzB,MAAM,IAAI8E,KAAK,CAAC,2CAA2C,CAAC;EAC9D,CAAC;EAAA;EAAA;IAAAlG,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EACD,OAAOiI,OAAO;AAChB,CAAC;;AAED;AAAA;AAAArJ,cAAA,GAAAoB,CAAA;AACA,OAAO,MAAMkI,OAAO,GAAGA,CAAA,KAAM;EAAA;EAAAtJ,cAAA,GAAAqB,CAAA;EAC3B,MAAM;IAAEsC,eAAe;IAAEE,SAAS;IAAEE,IAAI;IAAEZ,OAAO;IAAEoF;EAAQ,CAAC;EAAA;EAAA,CAAAvI,cAAA,GAAAoB,CAAA,SAAGgI,MAAM,CAAC,CAAC;EAAC;EAAApJ,cAAA,GAAAoB,CAAA;EACxE,OAAO;IAAEuC,eAAe;IAAEE,SAAS;IAAEE,IAAI;IAAEZ,OAAO;IAAEoF;EAAQ,CAAC;AAC/D,CAAC;AAAC;AAAAvI,cAAA,GAAAoB,CAAA;AAEF,OAAO,MAAMmI,SAAS,GAAGA,CAAA,KAAM;EAAA;EAAAvJ,cAAA,GAAAqB,CAAA;EAC7B,MAAM;IAAE4C,MAAM;IAAEE,aAAa;IAAEE,WAAW;IAAE4D;EAAc,CAAC;EAAA;EAAA,CAAAjI,cAAA,GAAAoB,CAAA,SAAGgI,MAAM,CAAC,CAAC;EAAC;EAAApJ,cAAA,GAAAoB,CAAA;EACvE,OAAO;IAAE6C,MAAM;IAAEuF,OAAO,EAAErF,aAAa;IAAEmB,KAAK,EAAEjB,WAAW;IAAE4D;EAAc,CAAC;AAC9E,CAAC;AAAC;AAAAjI,cAAA,GAAAoB,CAAA;AAEF,OAAO,MAAMqI,OAAO,GAAGA,CAAA,KAAM;EAAA;EAAAzJ,cAAA,GAAAqB,CAAA;EAC3B,MAAM;IAAE0C,IAAI;IAAEF,SAAS;IAAEqE,qBAAqB;IAAEQ,cAAc;IAAEH;EAAQ,CAAC;EAAA;EAAA,CAAAvI,cAAA,GAAAoB,CAAA,SAAGgI,MAAM,CAAC,CAAC;EAAC;EAAApJ,cAAA,GAAAoB,CAAA;EACrF,OAAO;IAAE2C,IAAI;IAAEF,SAAS;IAAEqE,qBAAqB;IAAEQ,cAAc;IAAEH;EAAQ,CAAC;AAC5E,CAAC", "ignoreList": []}