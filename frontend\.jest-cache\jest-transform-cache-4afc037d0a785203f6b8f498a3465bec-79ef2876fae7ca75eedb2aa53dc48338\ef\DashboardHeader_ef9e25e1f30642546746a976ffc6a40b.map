{"version": 3, "names": ["_jsxFileName", "React", "__jsx", "createElement", "cov_22o17h8if", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "s", "f", "b", "_coverageSchema", "coverage", "actualCoverage", "useState", "DashboardHeader", "clientName", "onSearch", "onAddRenewal", "searchPlaceholder", "className", "testId", "searchQuery", "setSearch<PERSON>uery", "handleSearchChange", "e", "query", "target", "value", "handleSearchSubmit", "preventDefault", "handleAddRenewal", "__self", "__source", "fileName", "lineNumber", "columnNumber", "onSubmit", "placeholder", "onChange", "onClick"], "sources": ["DashboardHeader.tsx"], "sourcesContent": ["/**\n * Dashboard Header Component\n * \n * Contains the dashboard title, subtitle, search functionality, and action buttons.\n * Focused responsibility: Header section with search and actions.\n */\n\n'use client'\n\nimport { useState } from 'react'\nimport { BaseComponentProps } from '@/lib/types'\n\ninterface DashboardHeaderProps extends BaseComponentProps {\n  clientName?: string\n  onSearch?: (query: string) => void\n  onAddRenewal?: () => void\n  searchPlaceholder?: string\n}\n\nexport default function DashboardHeader({\n  clientName = 'Unknown Client',\n  onSearch,\n  onAddRenewal,\n  searchPlaceholder = 'Search renewals...',\n  className = '',\n  'data-testid': testId\n}: DashboardHeaderProps) {\n  const [searchQuery, setSearchQuery] = useState('')\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const query = e.target.value\n    setSearchQuery(query)\n    onSearch?.(query)\n  }\n\n  const handleSearchSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    onSearch?.(searchQuery)\n  }\n\n  const handleAddRenewal = () => {\n    onAddRenewal?.()\n  }\n\n  return (\n    <div \n      className={`dashboard-header ${className}`}\n      data-testid={testId}\n    >\n      <div className=\"dashboard-title-section\">\n        <h1 className=\"dashboard-title\">\n          Dashboard - {clientName}\n        </h1>\n        <p className=\"dashboard-subtitle\">\n          Manage your subscriptions, maintenance, support and warranties\n        </p>\n      </div>\n      \n      <div className=\"dashboard-actions\">\n        <form onSubmit={handleSearchSubmit} className=\"search-container\">\n          <input\n            type=\"text\"\n            placeholder={searchPlaceholder}\n            className=\"search-input\"\n            value={searchQuery}\n            onChange={handleSearchChange}\n            aria-label=\"Search renewals\"\n          />\n          <button \n            type=\"submit\"\n            className=\"search-icon\"\n            aria-label=\"Submit search\"\n          >\n            🔍\n          </button>\n        </form>\n        \n        <button \n          className=\"btn btn-primary\"\n          onClick={handleAddRenewal}\n          aria-label=\"Add new renewal\"\n        >\n          + Add Renewal\n        </button>\n      </div>\n    </div>\n  )\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAAA;AAAA,IAAAA,YAAA;AAAA,OAAAC,KAAA;AAAA,IAAAC,KAAA,GAAAD,KAAA,CAAAE,aAAA;AAAA,SAAAC,cAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAU,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,eAAA;IAAApB,IAAA;EAAA;EAAA,IAAAqB,QAAA,GAAApB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAkB,QAAA,CAAAtB,IAAA,KAAAsB,QAAA,CAAAtB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAqB,QAAA,CAAAtB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAkB,cAAA,GAAAD,QAAA,CAAAtB,IAAA;EAAA;IAQA;IAAAD,aAAA,YAAAA,CAAA;MAAA,OAAAwB,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAxB,aAAA;AANZ,SAASyB,QAAQ,QAAQ,OAAO;AAUhC,eAAe,SAASC,eAAeA,CAAC;EACtCC,UAAU;EAAA;EAAA,CAAA3B,aAAA,GAAAqB,CAAA,UAAG,gBAAgB;EAC7BO,QAAQ;EACRC,YAAY;EACZC,iBAAiB;EAAA;EAAA,CAAA9B,aAAA,GAAAqB,CAAA,UAAG,oBAAoB;EACxCU,SAAS;EAAA;EAAA,CAAA/B,aAAA,GAAAqB,CAAA,UAAG,EAAE;EACd,aAAa,EAAEW;AACK,CAAC,EAAE;EAAA;EAAAhC,aAAA,GAAAoB,CAAA;EACvB,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC;EAAA;EAAA,CAAAlC,aAAA,GAAAmB,CAAA,OAAGM,QAAQ,CAAC,EAAE,CAAC;EAAA;EAAAzB,aAAA,GAAAmB,CAAA;EAElD,MAAMgB,kBAAkB,GAAIC,CAAsC,IAAK;IAAA;IAAApC,aAAA,GAAAoB,CAAA;IACrE,MAAMiB,KAAK;IAAA;IAAA,CAAArC,aAAA,GAAAmB,CAAA,OAAGiB,CAAC,CAACE,MAAM,CAACC,KAAK;IAAA;IAAAvC,aAAA,GAAAmB,CAAA;IAC5Be,cAAc,CAACG,KAAK,CAAC;IAAA;IAAArC,aAAA,GAAAmB,CAAA;IACrBS,QAAQ,GAAGS,KAAK,CAAC;EACnB,CAAC;EAAA;EAAArC,aAAA,GAAAmB,CAAA;EAED,MAAMqB,kBAAkB,GAAIJ,CAAkB,IAAK;IAAA;IAAApC,aAAA,GAAAoB,CAAA;IAAApB,aAAA,GAAAmB,CAAA;IACjDiB,CAAC,CAACK,cAAc,CAAC,CAAC;IAAA;IAAAzC,aAAA,GAAAmB,CAAA;IAClBS,QAAQ,GAAGK,WAAW,CAAC;EACzB,CAAC;EAAA;EAAAjC,aAAA,GAAAmB,CAAA;EAED,MAAMuB,gBAAgB,GAAGA,CAAA,KAAM;IAAA;IAAA1C,aAAA,GAAAoB,CAAA;IAAApB,aAAA,GAAAmB,CAAA;IAC7BU,YAAY,GAAG,CAAC;EAClB,CAAC;EAAA;EAAA7B,aAAA,GAAAmB,CAAA;EAED,OACE,0BAAArB,KAAA;EAAA;EAAA;EAAA;EAAA;IACEiC,SAAS,EAAE,oBAAoBA,SAAS,EAAG;IAC3C;IAAA,eAAaC,MAAO;IAAAW,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAjD,YAAA;MAAAkD,UAAA;MAAAC,YAAA;IAAA;EAAA;EAEpB;EAAAjD,KAAA;EAAA;EAAA;EAAA;EAAA;IAAKiC,SAAS,EAAC,yBAAyB;IAAAY,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAjD,YAAA;MAAAkD,UAAA;MAAAC,YAAA;IAAA;EAAA;EACtC;EAAAjD,KAAA;EAAA;EAAA;EAAA;EAAA;IAAIiC,SAAS,EAAC,iBAAiB;IAAAY,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAjD,YAAA;MAAAkD,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAClB,EAACpB,UACX,CAAC;EACL;EAAA7B,KAAA;EAAA;EAAA;EAAA;EAAA;IAAGiC,SAAS,EAAC,oBAAoB;IAAAY,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAjD,YAAA;MAAAkD,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gEAE/B,CACA,CAAC;EAEN;EAAAjD,KAAA;EAAA;EAAA;EAAA;EAAA;IAAKiC,SAAS,EAAC,mBAAmB;IAAAY,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAjD,YAAA;MAAAkD,UAAA;MAAAC,YAAA;IAAA;EAAA;EAChC;EAAAjD,KAAA;EAAA;EAAA;EAAA;EAAA;IAAMkD,QAAQ,EAAER,kBAAmB;IAACT,SAAS,EAAC,kBAAkB;IAAAY,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAjD,YAAA;MAAAkD,UAAA;MAAAC,YAAA;IAAA;EAAA;EAC9D;EAAAjD,KAAA;EAAA;EAAA;EAAA;EAAA;IACEmB,IAAI,EAAC,MAAM;IACXgC,WAAW,EAAEnB,iBAAkB;IAC/BC,SAAS,EAAC,cAAc;IACxBQ,KAAK,EAAEN,WAAY;IACnBiB,QAAQ,EAAEf,kBAAmB;IAC7B;IAAA,cAAW,iBAAiB;IAAAQ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAjD,YAAA;MAAAkD,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC7B,CAAC;EACF;EAAAjD,KAAA;EAAA;EAAA;EAAA;EAAA;IACEmB,IAAI,EAAC,QAAQ;IACbc,SAAS,EAAC,aAAa;IACvB;IAAA,cAAW,eAAe;IAAAY,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAjD,YAAA;MAAAkD,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3B,cAEO,CACJ,CAAC;EAEP;EAAAjD,KAAA;EAAA;EAAA;EAAA;EAAA;IACEiC,SAAS,EAAC,iBAAiB;IAC3BoB,OAAO,EAAET,gBAAiB;IAC1B;IAAA,cAAW,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAjD,YAAA;MAAAkD,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7B,eAEO,CACL,CACF,CAAC;AAEV", "ignoreList": []}