{"version": 3, "names": ["_jsxFileName", "__jsx", "React", "createElement", "cov_93c1ej79g", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "_coverageSchema", "coverage", "actualCoverage", "createContext", "useContext", "useState", "useEffect", "TenantContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "tenant", "<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "error", "setError", "fetch<PERSON><PERSON>t", "retryCount", "response", "fetch", "data", "json", "ok", "status", "console", "log", "setTimeout", "Error", "success", "client", "clientName", "err", "errorMessage", "message", "refreshTenant", "timer", "clearTimeout", "value", "Provider", "__self", "__source", "fileName", "lineNumber", "columnNumber", "useTenant", "context", "useTenantSchema", "tenantSchema", "useTenantFeatures", "hasFeature", "featureName", "settings", "features", "getFeatureConfig"], "sources": ["TenantContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { TenantContext as TenantContextType } from '@/lib/clients';\n\ninterface TenantContextState {\n  tenant: TenantContextType | null;\n  loading: boolean;\n  error: string | null;\n  refreshTenant: () => Promise<void>;\n}\n\nconst TenantContext = createContext<TenantContextState | undefined>(undefined);\n\ninterface TenantProviderProps {\n  children: ReactNode;\n}\n\nexport function TenantProvider({ children }: TenantProviderProps) {\n  const [tenant, setTenant] = useState<TenantContextType | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchTenant = async (retryCount = 0) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch('/api/clients/domain');\n      const data = await response.json();\n\n      if (!response.ok) {\n        // If unauthorized and we haven't retried much, wait and retry\n        if (response.status === 401 && retryCount < 3) {\n          console.log(`Tenant fetch failed (401), retrying in ${(retryCount + 1) * 1000}ms... (attempt ${retryCount + 1}/3)`);\n          setTimeout(() => {\n            fetchTenant(retryCount + 1);\n          }, (retryCount + 1) * 1000);\n          return;\n        }\n        throw new Error(data.error || 'Failed to fetch tenant information');\n      }\n\n      if (data.success && data.client) {\n        setTenant(data.client);\n        console.log('✅ Tenant context loaded successfully:', data.client.clientName);\n      } else {\n        throw new Error('Invalid response format');\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';\n      console.error('❌ Tenant fetch error:', errorMessage);\n      setError(errorMessage);\n      setTenant(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const refreshTenant = async () => {\n    await fetchTenant();\n  };\n\n  useEffect(() => {\n    // Give the authentication system a moment to initialize\n    const timer = setTimeout(() => {\n      fetchTenant();\n    }, 1000);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  const value: TenantContextState = {\n    tenant,\n    loading,\n    error,\n    refreshTenant\n  };\n\n  return (\n    <TenantContext.Provider value={value}>\n      {children}\n    </TenantContext.Provider>\n  );\n}\n\nexport function useTenant() {\n  const context = useContext(TenantContext);\n  if (context === undefined) {\n    throw new Error('useTenant must be used within a TenantProvider');\n  }\n  return context;\n}\n\n// Hook for getting tenant schema name for database queries\nexport function useTenantSchema() {\n  const { tenant } = useTenant();\n  return tenant?.tenantSchema || null;\n}\n\n// Hook for checking if tenant has specific features\nexport function useTenantFeatures() {\n  const { tenant } = useTenant();\n  \n  const hasFeature = (featureName: string): boolean => {\n    return tenant?.settings?.features?.[featureName] === true;\n  };\n  \n  const getFeatureConfig = (featureName: string): any => {\n    return tenant?.settings?.features?.[featureName];\n  };\n  \n  return {\n    hasFeature,\n    getFeatureConfig,\n    features: tenant?.settings?.features || {}\n  };\n}\n"], "mappings": "AAAA,YAAY;;AAAC;AAAA,IAAAA,YAAA;AAAA,IAAAC,KAAA,GAAAC,KAAA,CAAAC,aAAA;AAAA,SAAAC,cAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,eAAA;IAAArB,IAAA;EAAA;EAAA,IAAAsB,QAAA,GAAArB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAmB,QAAA,CAAAvB,IAAA,KAAAuB,QAAA,CAAAvB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAsB,QAAA,CAAAvB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAmB,cAAA,GAAAD,QAAA,CAAAvB,IAAA;EAAA;IAeD;IAAAD,aAAA,YAAAA,CAAA;MAAA,OAAAyB,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAzB,aAAA;AAbZ,OAAOF,KAAK,IAAI4B,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;AAUxF,MAAMC,aAAa;AAAA;AAAA,CAAA9B,aAAA,GAAAoB,CAAA,oBAAGM,aAAa,CAAiCP,SAAS,CAAC;AAM9E,OAAO,SAASY,cAAcA,CAAC;EAAEC;AAA8B,CAAC,EAAE;EAAA;EAAAhC,aAAA,GAAAqB,CAAA;EAChE,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC;EAAA;EAAA,CAAAlC,aAAA,GAAAoB,CAAA,OAAGQ,QAAQ,CAA2B,IAAI,CAAC;EACpE,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC;EAAA;EAAA,CAAApC,aAAA,GAAAoB,CAAA,OAAGQ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC;EAAA;EAAA,CAAAtC,aAAA,GAAAoB,CAAA,OAAGQ,QAAQ,CAAgB,IAAI,CAAC;EAAC;EAAA5B,aAAA,GAAAoB,CAAA;EAExD,MAAMmB,WAAW,GAAG,MAAAA,CAAOC,UAAU;EAAA;EAAA,CAAAxC,aAAA,GAAAsB,CAAA,UAAG,CAAC,MAAK;IAAA;IAAAtB,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAC5C,IAAI;MAAA;MAAApB,aAAA,GAAAoB,CAAA;MACFgB,UAAU,CAAC,IAAI,CAAC;MAAC;MAAApC,aAAA,GAAAoB,CAAA;MACjBkB,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMG,QAAQ;MAAA;MAAA,CAAAzC,aAAA,GAAAoB,CAAA,OAAG,MAAMsB,KAAK,CAAC,qBAAqB,CAAC;MACnD,MAAMC,IAAI;MAAA;MAAA,CAAA3C,aAAA,GAAAoB,CAAA,OAAG,MAAMqB,QAAQ,CAACG,IAAI,CAAC,CAAC;MAAC;MAAA5C,aAAA,GAAAoB,CAAA;MAEnC,IAAI,CAACqB,QAAQ,CAACI,EAAE,EAAE;QAAA;QAAA7C,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAChB;QACA;QAAI;QAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAmB,QAAQ,CAACK,MAAM,KAAK,GAAG;QAAA;QAAA,CAAA9C,aAAA,GAAAsB,CAAA,UAAIkB,UAAU,GAAG,CAAC,GAAE;UAAA;UAAAxC,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAoB,CAAA;UAC7C2B,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAACR,UAAU,GAAG,CAAC,IAAI,IAAI,kBAAkBA,UAAU,GAAG,CAAC,KAAK,CAAC;UAAC;UAAAxC,aAAA,GAAAoB,CAAA;UACpH6B,UAAU,CAAC,MAAM;YAAA;YAAAjD,aAAA,GAAAqB,CAAA;YAAArB,aAAA,GAAAoB,CAAA;YACfmB,WAAW,CAACC,UAAU,GAAG,CAAC,CAAC;UAC7B,CAAC,EAAE,CAACA,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC;UAAC;UAAAxC,aAAA,GAAAoB,CAAA;UAC5B;QACF,CAAC;QAAA;QAAA;UAAApB,aAAA,GAAAsB,CAAA;QAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACD,MAAM,IAAI8B,KAAK;QAAC;QAAA,CAAAlD,aAAA,GAAAsB,CAAA,UAAAqB,IAAI,CAACN,KAAK;QAAA;QAAA,CAAArC,aAAA,GAAAsB,CAAA,UAAI,oCAAoC,EAAC;MACrE,CAAC;MAAA;MAAA;QAAAtB,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAoB,CAAA;MAED;MAAI;MAAA,CAAApB,aAAA,GAAAsB,CAAA,UAAAqB,IAAI,CAACQ,OAAO;MAAA;MAAA,CAAAnD,aAAA,GAAAsB,CAAA,UAAIqB,IAAI,CAACS,MAAM,GAAE;QAAA;QAAApD,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QAC/Bc,SAAS,CAACS,IAAI,CAACS,MAAM,CAAC;QAAC;QAAApD,aAAA,GAAAoB,CAAA;QACvB2B,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEL,IAAI,CAACS,MAAM,CAACC,UAAU,CAAC;MAC9E,CAAC,MAAM;QAAA;QAAArD,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAoB,CAAA;QACL,MAAM,IAAI8B,KAAK,CAAC,yBAAyB,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZ,MAAMC,YAAY;MAAA;MAAA,CAAAvD,aAAA,GAAAoB,CAAA,QAAGkC,GAAG,YAAYJ,KAAK;MAAA;MAAA,CAAAlD,aAAA,GAAAsB,CAAA,UAAGgC,GAAG,CAACE,OAAO;MAAA;MAAA,CAAAxD,aAAA,GAAAsB,CAAA,UAAG,wBAAwB;MAAC;MAAAtB,aAAA,GAAAoB,CAAA;MACnF2B,OAAO,CAACV,KAAK,CAAC,uBAAuB,EAAEkB,YAAY,CAAC;MAAC;MAAAvD,aAAA,GAAAoB,CAAA;MACrDkB,QAAQ,CAACiB,YAAY,CAAC;MAAC;MAAAvD,aAAA,GAAAoB,CAAA;MACvBc,SAAS,CAAC,IAAI,CAAC;IACjB,CAAC,SAAS;MAAA;MAAAlC,aAAA,GAAAoB,CAAA;MACRgB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAAC;EAAApC,aAAA,GAAAoB,CAAA;EAEF,MAAMqC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAAA;IAAAzD,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IAChC,MAAMmB,WAAW,CAAC,CAAC;EACrB,CAAC;EAAC;EAAAvC,aAAA,GAAAoB,CAAA;EAEFS,SAAS,CAAC,MAAM;IAAA;IAAA7B,aAAA,GAAAqB,CAAA;IACd;IACA,MAAMqC,KAAK;IAAA;IAAA,CAAA1D,aAAA,GAAAoB,CAAA,QAAG6B,UAAU,CAAC,MAAM;MAAA;MAAAjD,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAC7BmB,WAAW,CAAC,CAAC;IACf,CAAC,EAAE,IAAI,CAAC;IAAC;IAAAvC,aAAA,GAAAoB,CAAA;IAET,OAAO,MAAM;MAAA;MAAApB,aAAA,GAAAqB,CAAA;MAAArB,aAAA,GAAAoB,CAAA;MAAA,OAAAuC,YAAY,CAACD,KAAK,CAAC;IAAD,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,KAAyB;EAAA;EAAA,CAAA5D,aAAA,GAAAoB,CAAA,QAAG;IAChCa,MAAM;IACNE,OAAO;IACPE,KAAK;IACLoB;EACF,CAAC;EAAC;EAAAzD,aAAA,GAAAoB,CAAA;EAEF,OACE,0BAAAvB,KAAA;EAAA;EAACiC,aAAa,CAAC+B,QAAQ;EAAA;EAAA;IAACD,KAAK,EAAEA,KAAM;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAApE,YAAA;MAAAqE,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClClC,QACqB,CAAC;AAE7B;AAEA,OAAO,SAASmC,SAASA,CAAA,EAAG;EAAA;EAAAnE,aAAA,GAAAqB,CAAA;EAC1B,MAAM+C,OAAO;EAAA;EAAA,CAAApE,aAAA,GAAAoB,CAAA,QAAGO,UAAU,CAACG,aAAa,CAAC;EAAC;EAAA9B,aAAA,GAAAoB,CAAA;EAC1C,IAAIgD,OAAO,KAAKjD,SAAS,EAAE;IAAA;IAAAnB,aAAA,GAAAsB,CAAA;IAAAtB,aAAA,GAAAoB,CAAA;IACzB,MAAM,IAAI8B,KAAK,CAAC,gDAAgD,CAAC;EACnE,CAAC;EAAA;EAAA;IAAAlD,aAAA,GAAAsB,CAAA;EAAA;EAAAtB,aAAA,GAAAoB,CAAA;EACD,OAAOgD,OAAO;AAChB;;AAEA;AACA,OAAO,SAASC,eAAeA,CAAA,EAAG;EAAA;EAAArE,aAAA,GAAAqB,CAAA;EAChC,MAAM;IAAEY;EAAO,CAAC;EAAA;EAAA,CAAAjC,aAAA,GAAAoB,CAAA,QAAG+C,SAAS,CAAC,CAAC;EAAC;EAAAnE,aAAA,GAAAoB,CAAA;EAC/B,OAAO,2BAAApB,aAAA,GAAAsB,CAAA,UAAAW,MAAM,EAAEqC,YAAY;EAAA;EAAA,CAAAtE,aAAA,GAAAsB,CAAA,UAAI,IAAI;AACrC;;AAEA;AACA,OAAO,SAASiD,iBAAiBA,CAAA,EAAG;EAAA;EAAAvE,aAAA,GAAAqB,CAAA;EAClC,MAAM;IAAEY;EAAO,CAAC;EAAA;EAAA,CAAAjC,aAAA,GAAAoB,CAAA,QAAG+C,SAAS,CAAC,CAAC;EAAC;EAAAnE,aAAA,GAAAoB,CAAA;EAE/B,MAAMoD,UAAU,GAAIC,WAAmB,IAAc;IAAA;IAAAzE,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACnD,OAAOa,MAAM,EAAEyC,QAAQ,EAAEC,QAAQ,GAAGF,WAAW,CAAC,KAAK,IAAI;EAC3D,CAAC;EAAC;EAAAzE,aAAA,GAAAoB,CAAA;EAEF,MAAMwD,gBAAgB,GAAIH,WAAmB,IAAU;IAAA;IAAAzE,aAAA,GAAAqB,CAAA;IAAArB,aAAA,GAAAoB,CAAA;IACrD,OAAOa,MAAM,EAAEyC,QAAQ,EAAEC,QAAQ,GAAGF,WAAW,CAAC;EAClD,CAAC;EAAC;EAAAzE,aAAA,GAAAoB,CAAA;EAEF,OAAO;IACLoD,UAAU;IACVI,gBAAgB;IAChBD,QAAQ;IAAE;IAAA,CAAA3E,aAAA,GAAAsB,CAAA,WAAAW,MAAM,EAAEyC,QAAQ,EAAEC,QAAQ;IAAA;IAAA,CAAA3E,aAAA,GAAAsB,CAAA,WAAI,CAAC,CAAC;EAC5C,CAAC;AACH", "ignoreList": []}