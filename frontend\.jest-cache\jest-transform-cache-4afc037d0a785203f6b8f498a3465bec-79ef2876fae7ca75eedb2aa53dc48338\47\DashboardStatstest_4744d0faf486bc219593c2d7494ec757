95b7803c17db8b231e3bf8ff1cca83fc
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _react = _interopRequireDefault(require("react"));
var _testUtils = require("../utils/test-utils");
var _DashboardStats = _interopRequireDefault(require("@/components/dashboard/DashboardStats"));
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\__tests__\\components\\dashboard\\DashboardStats.test.tsx";
/**
 * Dashboard Stats Component Tests
 * 
 * Tests for the DashboardStats component including loading states,
 * data display, and performance optimizations
 */
var __jsx = _react.default.createElement;
describe('DashboardStats Component', () => {
  const mockStats = {
    totalRenewals: 25,
    renewalsDue: 5,
    vendors: 12,
    annualSpend: '$125,000'
  };
  beforeEach(() => {
    jest.clearAllMocks();
  });
  describe('Rendering', () => {
    it('should render all stat cards with correct data', () => {
      (0, _testUtils.render)(__jsx(_DashboardStats.default, {
        stats: mockStats,
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 27,
          columnNumber: 14
        }
      }));

      // Check if all stat cards are rendered
      expect(_testUtils.screen.getByText('Total Renewals')).toBeInTheDocument();
      expect(_testUtils.screen.getByText('Renewals Due')).toBeInTheDocument();
      expect(_testUtils.screen.getByText('Vendors')).toBeInTheDocument();
      expect(_testUtils.screen.getByText('Annual Spend')).toBeInTheDocument();

      // Check if values are displayed correctly
      expect(_testUtils.screen.getByText('25')).toBeInTheDocument();
      expect(_testUtils.screen.getByText('5')).toBeInTheDocument();
      expect(_testUtils.screen.getByText('12')).toBeInTheDocument();
      expect(_testUtils.screen.getByText('$125,000')).toBeInTheDocument();
    });
    it('should render with custom className', () => {
      const {
        container
      } = (0, _testUtils.render)(__jsx(_DashboardStats.default, {
        stats: mockStats,
        className: "custom-class",
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 44,
          columnNumber: 9
        }
      }));
      const statsGrid = container.querySelector('.stats-grid');
      expect(statsGrid).toHaveClass('custom-class');
    });
    it('should render with data-testid', () => {
      (0, _testUtils.render)(__jsx(_DashboardStats.default, {
        stats: mockStats,
        "data-testid": "dashboard-stats-test",
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 53,
          columnNumber: 9
        }
      }));
      expect(_testUtils.screen.getByTestId('dashboard-stats-test')).toBeInTheDocument();
    });
    it('should have proper accessibility attributes', () => {
      (0, _testUtils.render)(__jsx(_DashboardStats.default, {
        stats: mockStats,
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 63,
          columnNumber: 14
        }
      }));
      const statsContainer = _testUtils.screen.getByRole('region');
      expect(statsContainer).toHaveAttribute('aria-label', 'Dashboard Statistics');
    });
  });
  describe('Loading States', () => {
    it('should show loading skeletons when isLoading is true', () => {
      (0, _testUtils.render)(__jsx(_DashboardStats.default, {
        stats: mockStats,
        isLoading: true,
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 72,
          columnNumber: 14
        }
      }));

      // Check for loading indicators
      const loadingElements = _testUtils.screen.getAllByText('⏳');
      expect(loadingElements).toHaveLength(4); // One for each stat card

      // Check for skeleton loading elements
      const skeletons = document.querySelectorAll('.animate-pulse');
      expect(skeletons.length).toBeGreaterThan(0);
    });
    it('should hide loading skeletons when isLoading is false', () => {
      (0, _testUtils.render)(__jsx(_DashboardStats.default, {
        stats: mockStats,
        isLoading: false,
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 84,
          columnNumber: 14
        }
      }));

      // Should not show loading indicators
      expect(_testUtils.screen.queryByText('⏳')).not.toBeInTheDocument();

      // Should show actual data
      expect(_testUtils.screen.getByText('25')).toBeInTheDocument();
    });
    it('should handle transition from loading to loaded state', () => {
      const {
        rerender
      } = (0, _testUtils.render)(__jsx(_DashboardStats.default, {
        stats: mockStats,
        isLoading: true,
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 95,
          columnNumber: 9
        }
      }));

      // Initially loading
      expect(_testUtils.screen.getAllByText('⏳')).toHaveLength(4);

      // Rerender with loaded state
      rerender(__jsx(_DashboardStats.default, {
        stats: mockStats,
        isLoading: false,
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 102,
          columnNumber: 16
        }
      }));

      // Should show data now
      expect(_testUtils.screen.queryByText('⏳')).not.toBeInTheDocument();
      expect(_testUtils.screen.getByText('25')).toBeInTheDocument();
    });
  });
  describe('Data Handling', () => {
    it('should handle zero values correctly', () => {
      const zeroStats = {
        totalRenewals: 0,
        renewalsDue: 0,
        vendors: 0,
        annualSpend: '$0'
      };
      (0, _testUtils.render)(__jsx(_DashboardStats.default, {
        stats: zeroStats,
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 119,
          columnNumber: 14
        }
      }));
      expect(_testUtils.screen.getAllByText('0')).toHaveLength(3);
      expect(_testUtils.screen.getByText('$0')).toBeInTheDocument();
    });
    it('should handle large numbers correctly', () => {
      const largeStats = {
        totalRenewals: 1000,
        renewalsDue: 250,
        vendors: 500,
        annualSpend: '$1,000,000'
      };
      (0, _testUtils.render)(__jsx(_DashboardStats.default, {
        stats: largeStats,
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 133,
          columnNumber: 14
        }
      }));
      expect(_testUtils.screen.getByText('1000')).toBeInTheDocument();
      expect(_testUtils.screen.getByText('250')).toBeInTheDocument();
      expect(_testUtils.screen.getByText('500')).toBeInTheDocument();
      expect(_testUtils.screen.getByText('$1,000,000')).toBeInTheDocument();
    });
    it('should handle string values for annual spend', () => {
      const stringStats = {
        totalRenewals: 25,
        renewalsDue: 5,
        vendors: 12,
        annualSpend: 'N/A'
      };
      (0, _testUtils.render)(__jsx(_DashboardStats.default, {
        stats: stringStats,
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 149,
          columnNumber: 14
        }
      }));
      expect(_testUtils.screen.getByText('N/A')).toBeInTheDocument();
    });
  });
  describe('Performance', () => {
    it('should memoize stats configuration', () => {
      const {
        rerender
      } = (0, _testUtils.render)(__jsx(_DashboardStats.default, {
        stats: mockStats,
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 157,
          columnNumber: 35
        }
      }));

      // Rerender with same stats - should not cause unnecessary recalculation
      rerender(__jsx(_DashboardStats.default, {
        stats: mockStats,
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 160,
          columnNumber: 16
        }
      }));

      // Component should still render correctly
      expect(_testUtils.screen.getByText('Total Renewals')).toBeInTheDocument();
    });
    it('should only re-render when stats change', () => {
      const {
        rerender
      } = (0, _testUtils.render)(__jsx(_DashboardStats.default, {
        stats: mockStats,
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 167,
          columnNumber: 35
        }
      }));

      // Rerender with different props but same stats
      rerender(__jsx(_DashboardStats.default, {
        stats: mockStats,
        className: "new-class",
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 170,
          columnNumber: 16
        }
      }));

      // Should still show same data
      expect(_testUtils.screen.getByText('25')).toBeInTheDocument();
    });
    it('should update when stats change', () => {
      const {
        rerender
      } = (0, _testUtils.render)(__jsx(_DashboardStats.default, {
        stats: mockStats,
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 177,
          columnNumber: 35
        }
      }));
      const newStats = {
        totalRenewals: 30,
        renewalsDue: 8,
        vendors: 15,
        annualSpend: '$150,000'
      };
      rerender(__jsx(_DashboardStats.default, {
        stats: newStats,
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 186,
          columnNumber: 16
        }
      }));

      // Should show updated data
      expect(_testUtils.screen.getByText('30')).toBeInTheDocument();
      expect(_testUtils.screen.getByText('8')).toBeInTheDocument();
      expect(_testUtils.screen.getByText('15')).toBeInTheDocument();
      expect(_testUtils.screen.getByText('$150,000')).toBeInTheDocument();
    });
  });
  describe('Icons and Visual Elements', () => {
    it('should display correct icons for each stat', () => {
      (0, _testUtils.render)(__jsx(_DashboardStats.default, {
        stats: mockStats,
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 198,
          columnNumber: 14
        }
      }));

      // Check for emoji icons (these might need adjustment based on actual implementation)
      expect(_testUtils.screen.getByText('📊')).toBeInTheDocument(); // Total Renewals
      expect(_testUtils.screen.getByText('⚠️')).toBeInTheDocument(); // Renewals Due
      expect(_testUtils.screen.getByText('🏢')).toBeInTheDocument(); // Vendors
      expect(_testUtils.screen.getByText('💰')).toBeInTheDocument(); // Annual Spend
    });
    it('should maintain consistent layout structure', () => {
      const {
        container
      } = (0, _testUtils.render)(__jsx(_DashboardStats.default, {
        stats: mockStats,
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 208,
          columnNumber: 36
        }
      }));
      const statsGrid = container.querySelector('.stats-grid');
      expect(statsGrid).toBeInTheDocument();
      const statCards = container.querySelectorAll('.stat-card');
      expect(statCards).toHaveLength(4);
    });
  });
  describe('Error Handling', () => {
    it('should handle undefined stats gracefully', () => {
      // This test ensures the component doesn't crash with undefined stats
      const undefinedStats = {
        totalRenewals: undefined,
        renewalsDue: undefined,
        vendors: undefined,
        annualSpend: undefined
      };
      expect(() => {
        (0, _testUtils.render)(__jsx(_DashboardStats.default, {
          stats: undefinedStats,
          __self: void 0,
          __source: {
            fileName: _jsxFileName,
            lineNumber: 229,
            columnNumber: 16
          }
        }));
      }).not.toThrow();
    });
    it('should handle null stats gracefully', () => {
      const nullStats = {
        totalRenewals: null,
        renewalsDue: null,
        vendors: null,
        annualSpend: null
      };
      expect(() => {
        (0, _testUtils.render)(__jsx(_DashboardStats.default, {
          stats: nullStats,
          __self: void 0,
          __source: {
            fileName: _jsxFileName,
            lineNumber: 242,
            columnNumber: 16
          }
        }));
      }).not.toThrow();
    });
  });
  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      (0, _testUtils.render)(__jsx(_DashboardStats.default, {
        stats: mockStats,
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 249,
          columnNumber: 14
        }
      }));
      const region = _testUtils.screen.getByRole('region');
      expect(region).toHaveAttribute('aria-label', 'Dashboard Statistics');
    });
    it('should be keyboard navigable', () => {
      (0, _testUtils.render)(__jsx(_DashboardStats.default, {
        stats: mockStats,
        __self: void 0,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 256,
          columnNumber: 14
        }
      }));

      // The component should not interfere with keyboard navigation
      // This is more of a structural test
      const statsContainer = _testUtils.screen.getByRole('region');
      expect(statsContainer).toBeInTheDocument();
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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