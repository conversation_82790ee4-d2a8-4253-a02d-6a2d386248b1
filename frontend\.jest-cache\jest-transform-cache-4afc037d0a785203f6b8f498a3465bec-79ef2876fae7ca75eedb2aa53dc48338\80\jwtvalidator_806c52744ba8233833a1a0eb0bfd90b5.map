{"version": 3, "names": ["cov_2k8i4ud2ua", "actualCoverage", "jwtVerify", "createRemoteJWKSet", "publicConfig", "jwksCache", "s", "getJwksUrl", "f", "region", "userPoolId", "aws", "getJwks", "b", "jwksUrl", "URL", "validateJwtToken", "token", "jwks", "userPoolClientId", "<PERSON><PERSON><PERSON><PERSON>", "payload", "issuer", "audience", "cognitoPayload", "token_use", "console", "warn", "email", "email_verified", "error", "validate<PERSON><PERSON><PERSON><PERSON><PERSON>", "cookieValue", "trim", "includes", "split", "length", "parseJwtUnsafe", "app", "isProduction", "Error", "parts", "JSON", "parse", "<PERSON><PERSON><PERSON>", "from", "toString", "currentTime", "Math", "floor", "Date", "now", "exp", "isTokenExpired", "getTokenExpirationTime", "isTokenExpiringWithin", "minutes", "expirationTime", "warningTime"], "sources": ["jwt-validator.ts"], "sourcesContent": ["/**\n * Secure JWT Token Validation\n * \n * This module provides secure JWT token validation with proper signature verification\n * using AWS Cognito's JWKS (JSON Web Key Set).\n */\n\nimport { jwtVerify, createRemoteJWKSet, JWTPayload } from 'jose';\nimport { publicConfig } from './config';\n\n// Cognito JWT payload interface\nexport interface CognitoJwtPayload extends JWTPayload {\n  sub: string;\n  email: string;\n  email_verified?: boolean;\n  name?: string;\n  given_name?: string;\n  family_name?: string;\n  'cognito:groups'?: string[];\n  'cognito:username'?: string;\n  token_use: 'id' | 'access';\n  auth_time: number;\n  iat: number;\n  exp: number;\n  aud: string;\n  iss: string;\n}\n\n// Cache for JWKS\nlet jwksCache: ReturnType<typeof createRemoteJWKSet> | null = null;\n\n/**\n * Get JWKS URL for the Cognito User Pool\n */\nfunction getJwksUrl(): string {\n  const { region, userPoolId } = publicConfig.aws;\n  return `https://cognito-idp.${region}.amazonaws.com/${userPoolId}/.well-known/jwks.json`;\n}\n\n/**\n * Get or create JWKS instance\n */\nfunction getJwks() {\n  if (!jwksCache) {\n    const jwksUrl = getJwksUrl();\n    jwksCache = createRemoteJWKSet(new URL(jwksUrl));\n  }\n  return jwksCache;\n}\n\n/**\n * Validate JWT token with proper signature verification\n */\nexport async function validateJwtToken(token: string): Promise<CognitoJwtPayload | null> {\n  try {\n    const jwks = getJwks();\n    const { region, userPoolId, userPoolClientId } = publicConfig.aws;\n    \n    // Expected issuer\n    const expectedIssuer = `https://cognito-idp.${region}.amazonaws.com/${userPoolId}`;\n    \n    // Verify the JWT\n    const { payload } = await jwtVerify(token, jwks, {\n      issuer: expectedIssuer,\n      audience: userPoolClientId,\n    });\n\n    // Additional validation\n    const cognitoPayload = payload as CognitoJwtPayload;\n    \n    // Validate token type (should be 'id' for ID tokens)\n    if (cognitoPayload.token_use !== 'id') {\n      console.warn('Invalid token type:', cognitoPayload.token_use);\n      return null;\n    }\n\n    // Validate email is present and verified (if available)\n    if (!cognitoPayload.email) {\n      console.warn('Token missing email claim');\n      return null;\n    }\n\n    // Check if email is verified (optional but recommended)\n    if (cognitoPayload.email_verified === false) {\n      console.warn('Email not verified for user:', cognitoPayload.email);\n      // You might want to reject unverified emails in production\n    }\n\n    return cognitoPayload;\n  } catch (error) {\n    console.error('JWT validation failed:', error);\n    return null;\n  }\n}\n\n/**\n * Extract and validate JWT from cookie string\n */\nexport async function validateAuthCookie(cookieValue: string): Promise<CognitoJwtPayload | null> {\n  if (!cookieValue) {\n    return null;\n  }\n\n  // Remove any potential cookie prefix/suffix\n  const token = cookieValue.trim();\n  \n  // Basic JWT format check\n  if (!token.includes('.') || token.split('.').length !== 3) {\n    console.warn('Invalid JWT format in cookie');\n    return null;\n  }\n\n  return validateJwtToken(token);\n}\n\n/**\n * Fallback JWT parsing (INSECURE - only for development/debugging)\n * This should NOT be used in production\n */\nexport function parseJwtUnsafe(token: string): CognitoJwtPayload | null {\n  if (publicConfig.app.isProduction) {\n    throw new Error('Unsafe JWT parsing is not allowed in production');\n  }\n\n  try {\n    const parts = token.split('.');\n    if (parts.length !== 3) {\n      return null;\n    }\n\n    const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());\n    \n    // Check expiration\n    const currentTime = Math.floor(Date.now() / 1000);\n    if (payload.exp && payload.exp < currentTime) {\n      console.warn('Token has expired');\n      return null;\n    }\n\n    return payload as CognitoJwtPayload;\n  } catch (error) {\n    console.error('Failed to parse JWT:', error);\n    return null;\n  }\n}\n\n/**\n * Validate token expiration\n */\nexport function isTokenExpired(payload: CognitoJwtPayload): boolean {\n  const currentTime = Math.floor(Date.now() / 1000);\n  return payload.exp < currentTime;\n}\n\n/**\n * Get token expiration time in milliseconds\n */\nexport function getTokenExpirationTime(payload: CognitoJwtPayload): number {\n  return payload.exp * 1000;\n}\n\n/**\n * Check if token expires within the specified minutes\n */\nexport function isTokenExpiringWithin(payload: CognitoJwtPayload, minutes: number): boolean {\n  const expirationTime = getTokenExpirationTime(payload);\n  const warningTime = Date.now() + (minutes * 60 * 1000);\n  return expirationTime <= warningTime;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASE,SAAS,EAAEC,kBAAkB,QAAoB,MAAM;AAChE,SAASC,YAAY,QAAQ,UAAU;;AAEvC;;AAkBA;AACA,IAAIC,SAAuD;AAAA;AAAA,CAAAL,cAAA,GAAAM,CAAA,OAAG,IAAI;;AAElE;AACA;AACA;AACA,SAASC,UAAUA,CAAA,EAAW;EAAA;EAAAP,cAAA,GAAAQ,CAAA;EAC5B,MAAM;IAAEC,MAAM;IAAEC;EAAW,CAAC;EAAA;EAAA,CAAAV,cAAA,GAAAM,CAAA,OAAGF,YAAY,CAACO,GAAG;EAAC;EAAAX,cAAA,GAAAM,CAAA;EAChD,OAAO,uBAAuBG,MAAM,kBAAkBC,UAAU,wBAAwB;AAC1F;;AAEA;AACA;AACA;AACA,SAASE,OAAOA,CAAA,EAAG;EAAA;EAAAZ,cAAA,GAAAQ,CAAA;EAAAR,cAAA,GAAAM,CAAA;EACjB,IAAI,CAACD,SAAS,EAAE;IAAA;IAAAL,cAAA,GAAAa,CAAA;IACd,MAAMC,OAAO;IAAA;IAAA,CAAAd,cAAA,GAAAM,CAAA,OAAGC,UAAU,CAAC,CAAC;IAAC;IAAAP,cAAA,GAAAM,CAAA;IAC7BD,SAAS,GAAGF,kBAAkB,CAAC,IAAIY,GAAG,CAACD,OAAO,CAAC,CAAC;EAClD,CAAC;EAAA;EAAA;IAAAd,cAAA,GAAAa,CAAA;EAAA;EAAAb,cAAA,GAAAM,CAAA;EACD,OAAOD,SAAS;AAClB;;AAEA;AACA;AACA;AACA,OAAO,eAAeW,gBAAgBA,CAACC,KAAa,EAAqC;EAAA;EAAAjB,cAAA,GAAAQ,CAAA;EAAAR,cAAA,GAAAM,CAAA;EACvF,IAAI;IACF,MAAMY,IAAI;IAAA;IAAA,CAAAlB,cAAA,GAAAM,CAAA,OAAGM,OAAO,CAAC,CAAC;IACtB,MAAM;MAAEH,MAAM;MAAEC,UAAU;MAAES;IAAiB,CAAC;IAAA;IAAA,CAAAnB,cAAA,GAAAM,CAAA,OAAGF,YAAY,CAACO,GAAG;;IAEjE;IACA,MAAMS,cAAc;IAAA;IAAA,CAAApB,cAAA,GAAAM,CAAA,QAAG,uBAAuBG,MAAM,kBAAkBC,UAAU,EAAE;;IAElF;IACA,MAAM;MAAEW;IAAQ,CAAC;IAAA;IAAA,CAAArB,cAAA,GAAAM,CAAA,QAAG,MAAMJ,SAAS,CAACe,KAAK,EAAEC,IAAI,EAAE;MAC/CI,MAAM,EAAEF,cAAc;MACtBG,QAAQ,EAAEJ;IACZ,CAAC,CAAC;;IAEF;IACA,MAAMK,cAAc;IAAA;IAAA,CAAAxB,cAAA,GAAAM,CAAA,QAAGe,OAAO,CAAqB;;IAEnD;IAAA;IAAArB,cAAA,GAAAM,CAAA;IACA,IAAIkB,cAAc,CAACC,SAAS,KAAK,IAAI,EAAE;MAAA;MAAAzB,cAAA,GAAAa,CAAA;MAAAb,cAAA,GAAAM,CAAA;MACrCoB,OAAO,CAACC,IAAI,CAAC,qBAAqB,EAAEH,cAAc,CAACC,SAAS,CAAC;MAAC;MAAAzB,cAAA,GAAAM,CAAA;MAC9D,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAAN,cAAA,GAAAa,CAAA;IAAA;;IAED;IAAAb,cAAA,GAAAM,CAAA;IACA,IAAI,CAACkB,cAAc,CAACI,KAAK,EAAE;MAAA;MAAA5B,cAAA,GAAAa,CAAA;MAAAb,cAAA,GAAAM,CAAA;MACzBoB,OAAO,CAACC,IAAI,CAAC,2BAA2B,CAAC;MAAC;MAAA3B,cAAA,GAAAM,CAAA;MAC1C,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAAN,cAAA,GAAAa,CAAA;IAAA;;IAED;IAAAb,cAAA,GAAAM,CAAA;IACA,IAAIkB,cAAc,CAACK,cAAc,KAAK,KAAK,EAAE;MAAA;MAAA7B,cAAA,GAAAa,CAAA;MAAAb,cAAA,GAAAM,CAAA;MAC3CoB,OAAO,CAACC,IAAI,CAAC,8BAA8B,EAAEH,cAAc,CAACI,KAAK,CAAC;MAClE;IACF,CAAC;IAAA;IAAA;MAAA5B,cAAA,GAAAa,CAAA;IAAA;IAAAb,cAAA,GAAAM,CAAA;IAED,OAAOkB,cAAc;EACvB,CAAC,CAAC,OAAOM,KAAK,EAAE;IAAA;IAAA9B,cAAA,GAAAM,CAAA;IACdoB,OAAO,CAACI,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAAC;IAAA9B,cAAA,GAAAM,CAAA;IAC/C,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA,OAAO,eAAeyB,kBAAkBA,CAACC,WAAmB,EAAqC;EAAA;EAAAhC,cAAA,GAAAQ,CAAA;EAAAR,cAAA,GAAAM,CAAA;EAC/F,IAAI,CAAC0B,WAAW,EAAE;IAAA;IAAAhC,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAM,CAAA;IAChB,OAAO,IAAI;EACb,CAAC;EAAA;EAAA;IAAAN,cAAA,GAAAa,CAAA;EAAA;;EAED;EACA,MAAMI,KAAK;EAAA;EAAA,CAAAjB,cAAA,GAAAM,CAAA,QAAG0B,WAAW,CAACC,IAAI,CAAC,CAAC;;EAEhC;EAAA;EAAAjC,cAAA,GAAAM,CAAA;EACA;EAAI;EAAA,CAAAN,cAAA,GAAAa,CAAA,WAACI,KAAK,CAACiB,QAAQ,CAAC,GAAG,CAAC;EAAA;EAAA,CAAAlC,cAAA,GAAAa,CAAA,UAAII,KAAK,CAACkB,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,KAAK,CAAC,GAAE;IAAA;IAAApC,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAM,CAAA;IACzDoB,OAAO,CAACC,IAAI,CAAC,8BAA8B,CAAC;IAAC;IAAA3B,cAAA,GAAAM,CAAA;IAC7C,OAAO,IAAI;EACb,CAAC;EAAA;EAAA;IAAAN,cAAA,GAAAa,CAAA;EAAA;EAAAb,cAAA,GAAAM,CAAA;EAED,OAAOU,gBAAgB,CAACC,KAAK,CAAC;AAChC;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASoB,cAAcA,CAACpB,KAAa,EAA4B;EAAA;EAAAjB,cAAA,GAAAQ,CAAA;EAAAR,cAAA,GAAAM,CAAA;EACtE,IAAIF,YAAY,CAACkC,GAAG,CAACC,YAAY,EAAE;IAAA;IAAAvC,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAM,CAAA;IACjC,MAAM,IAAIkC,KAAK,CAAC,iDAAiD,CAAC;EACpE,CAAC;EAAA;EAAA;IAAAxC,cAAA,GAAAa,CAAA;EAAA;EAAAb,cAAA,GAAAM,CAAA;EAED,IAAI;IACF,MAAMmC,KAAK;IAAA;IAAA,CAAAzC,cAAA,GAAAM,CAAA,QAAGW,KAAK,CAACkB,KAAK,CAAC,GAAG,CAAC;IAAC;IAAAnC,cAAA,GAAAM,CAAA;IAC/B,IAAImC,KAAK,CAACL,MAAM,KAAK,CAAC,EAAE;MAAA;MAAApC,cAAA,GAAAa,CAAA;MAAAb,cAAA,GAAAM,CAAA;MACtB,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAAN,cAAA,GAAAa,CAAA;IAAA;IAED,MAAMQ,OAAO;IAAA;IAAA,CAAArB,cAAA,GAAAM,CAAA,QAAGoC,IAAI,CAACC,KAAK,CAACC,MAAM,CAACC,IAAI,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAC;;IAEtE;IACA,MAAMC,WAAW;IAAA;IAAA,CAAA/C,cAAA,GAAAM,CAAA,QAAG0C,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;IAAC;IAAAnD,cAAA,GAAAM,CAAA;IAClD;IAAI;IAAA,CAAAN,cAAA,GAAAa,CAAA,WAAAQ,OAAO,CAAC+B,GAAG;IAAA;IAAA,CAAApD,cAAA,GAAAa,CAAA,WAAIQ,OAAO,CAAC+B,GAAG,GAAGL,WAAW,GAAE;MAAA;MAAA/C,cAAA,GAAAa,CAAA;MAAAb,cAAA,GAAAM,CAAA;MAC5CoB,OAAO,CAACC,IAAI,CAAC,mBAAmB,CAAC;MAAC;MAAA3B,cAAA,GAAAM,CAAA;MAClC,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAAN,cAAA,GAAAa,CAAA;IAAA;IAAAb,cAAA,GAAAM,CAAA;IAED,OAAOe,OAAO;EAChB,CAAC,CAAC,OAAOS,KAAK,EAAE;IAAA;IAAA9B,cAAA,GAAAM,CAAA;IACdoB,OAAO,CAACI,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAAC;IAAA9B,cAAA,GAAAM,CAAA;IAC7C,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA,OAAO,SAAS+C,cAAcA,CAAChC,OAA0B,EAAW;EAAA;EAAArB,cAAA,GAAAQ,CAAA;EAClE,MAAMuC,WAAW;EAAA;EAAA,CAAA/C,cAAA,GAAAM,CAAA,QAAG0C,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;EAAC;EAAAnD,cAAA,GAAAM,CAAA;EAClD,OAAOe,OAAO,CAAC+B,GAAG,GAAGL,WAAW;AAClC;;AAEA;AACA;AACA;AACA,OAAO,SAASO,sBAAsBA,CAACjC,OAA0B,EAAU;EAAA;EAAArB,cAAA,GAAAQ,CAAA;EAAAR,cAAA,GAAAM,CAAA;EACzE,OAAOe,OAAO,CAAC+B,GAAG,GAAG,IAAI;AAC3B;;AAEA;AACA;AACA;AACA,OAAO,SAASG,qBAAqBA,CAAClC,OAA0B,EAAEmC,OAAe,EAAW;EAAA;EAAAxD,cAAA,GAAAQ,CAAA;EAC1F,MAAMiD,cAAc;EAAA;EAAA,CAAAzD,cAAA,GAAAM,CAAA,QAAGgD,sBAAsB,CAACjC,OAAO,CAAC;EACtD,MAAMqC,WAAW;EAAA;EAAA,CAAA1D,cAAA,GAAAM,CAAA,QAAG4C,IAAI,CAACC,GAAG,CAAC,CAAC,GAAIK,OAAO,GAAG,EAAE,GAAG,IAAK;EAAC;EAAAxD,cAAA,GAAAM,CAAA;EACvD,OAAOmD,cAAc,IAAIC,WAAW;AACtC", "ignoreList": []}