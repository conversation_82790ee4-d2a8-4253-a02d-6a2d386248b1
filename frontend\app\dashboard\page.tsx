
/**
 * Dashboard Page - Modular and Optimized
 *
 * This page demonstrates the new modular architecture with:
 * - Separated concerns into focused components
 * - Custom hooks for data management
 * - Error boundaries for resilience
 * - Proper loading states
 * - Type safety throughout
 */

'use client'

import { useState, useCallback, useEffect } from 'react'
import { useTenant } from '@/contexts/AppContext'
import { useDashboardData } from '@/hooks/useDashboardData'
// import { useScanResults } from '@/hooks/useScanResults' // FUTURE DEVELOPMENT - Commented out for initial release
import { usePerformanceMonitor, useDebounce } from '@/lib/performance'

// Modular dashboard components
import DashboardHeader from '@/components/dashboard/DashboardHeader'
import DashboardStats from '@/components/dashboard/DashboardStats'
import RecentRenewals from '@/components/dashboard/RecentRenewals'
import UpcomingRenewals from '@/components/dashboard/UpcomingRenewals'
// import ScanResults from '@/components/dashboard/ScanResults' // FUTURE DEVELOPMENT - Commented out for initial release

// Modal components
import AddRenewalModal, { RenewalFormData, AlertFormData } from '@/components/modals/AddRenewalModal'

// Services
import { saveRenewal } from '@/lib/services/renewalService'

// Common components
import ErrorBoundary from '@/components/common/ErrorBoundary'
import { LoadingPage } from '@/components/common/LoadingStates'
import { LazySection } from '@/components/common/LazyLoad'

// Types
import { Renewal } from '@/lib/types'



export default function DashboardPage() {
  const { tenant, loading: tenantLoading, error: tenantError } = useTenant()
  const { data, isLoading, error, refetch } = useDashboardData()

  // FUTURE DEVELOPMENT - Scan Results - Commented out for initial release
  /*
  const {
    results: scanResults,
    isLoading: scanLoading,
    lastScanDate,
    runScan
  } = useScanResults()
  */

  // Performance monitoring
  usePerformanceMonitor('DashboardPage')

  const [searchQuery, setSearchQuery] = useState('')
  const [isAddRenewalModalOpen, setIsAddRenewalModalOpen] = useState(false)

  // Force refresh when component mounts to ensure consistent rendering
  useEffect(() => {
    if (tenant && !tenantLoading && !tenantError) {
      refetch()
    }
  }, []) // Only run on mount

  // Debounce search to prevent excessive API calls
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Memoized event handlers to prevent unnecessary re-renders
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query)
    // TODO: Implement search functionality with debouncedSearchQuery
    console.log('Searching for:', query, 'Debounced:', debouncedSearchQuery)
  }, [debouncedSearchQuery])

  const handleAddRenewal = useCallback(() => {
    setIsAddRenewalModalOpen(true)
  }, [])

  const handleCloseAddRenewalModal = useCallback(() => {
    setIsAddRenewalModalOpen(false)
  }, [])

  const handleSubmitRenewal = useCallback(async (renewalData: RenewalFormData, alertsData: AlertFormData[]) => {
    try {
      if (!tenant?.clientId) {
        throw new Error('Tenant information not available')
      }

      // Save renewal and alerts using the service
      const result = await saveRenewal(tenant.clientId, renewalData, alertsData)

      if (!result.success) {
        throw new Error(result.message || 'Failed to save renewal')
      }

      // Refresh dashboard data after successful save
      await refetch()

      // Show success message (TODO: implement toast notifications)
      console.log('Renewal and alerts saved successfully!', result)
    } catch (error) {
      console.error('Error saving renewal:', error)
      // TODO: Show error message to user
      throw error // Re-throw to let modal handle the error state
    }
  }, [tenant?.clientId, refetch])

  const handleRenewalClick = useCallback((renewal: Renewal) => {
    // TODO: Navigate to renewal details
    console.log('Renewal clicked:', renewal)
  }, [])

  // FUTURE DEVELOPMENT - Scan handlers - Commented out for initial release
  /*
  const handleRunScan = useCallback(async () => {
    try {
      await runScan()
    } catch (error) {
      console.error('Failed to run scan:', error)
    }
  }, [runScan])

  const handleScanResultClick = useCallback((result: any) => {
    // TODO: Handle scan result click
    console.log('Scan result clicked:', result)
  }, [])
  */

  // Show loading state only for initial tenant loading
  if (tenantLoading) {
    return (
      <LoadingPage
        title="Loading Dashboard..."
        subtitle="Please wait while we set up your dashboard."
        icon="⏳"
      />
    )
  }

  // Show error state
  if (tenantError || error) {
    return (
      <div className="dashboard-container">
        <div className="text-center py-8">
          <div className="text-4xl mb-4">❌</div>
          <h3 className="text-lg font-medium mb-2">Error Loading Dashboard</h3>
          <p className="text-secondary mb-4">{tenantError || error}</p>
          <button
            className="btn btn-primary"
            onClick={() => refetch()}
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('Dashboard error:', error, errorInfo)
      }}
      resetKeys={[tenant?.clientId]}
    >
      <div className="dashboard-container" key={`dashboard-${tenant?.clientId}`}>
        {/* Header Section */}
        <DashboardHeader
          key={`header-${tenant?.clientId}`}
          clientName={tenant?.clientName}
          onSearch={handleSearch}
          onAddRenewal={handleAddRenewal}
          searchPlaceholder="Search renewals..."
        />

        {/* Statistics Section */}
        <DashboardStats
          key={`stats-${tenant?.clientId}`}
          stats={data.stats}
          isLoading={isLoading}
          className="mb-6"
        />

        {/* Upcoming Renewals Section - Lazy loaded */}
        <LazySection
          key={`upcoming-${tenant?.clientId}`}
          placeholder={<div className="h-32 bg-gray-100 animate-pulse rounded-lg mb-6" />}
          className="mb-6"
        >
          <UpcomingRenewals
            renewals={data.upcomingRenewals}
            isLoading={isLoading}
            onRenewalClick={handleRenewalClick}
            daysThreshold={30}
          />
        </LazySection>

        {/* Recent Renewals Section - Lazy loaded */}
        <LazySection
          key={`recent-${tenant?.clientId}`}
          placeholder={<div className="h-64 bg-gray-100 animate-pulse rounded-lg" />}
        >
          <RecentRenewals
            renewals={data.recentRenewals}
            isLoading={isLoading}
            onRenewalClick={handleRenewalClick}
            maxItems={5}
          />
        </LazySection>

        {/* FUTURE DEVELOPMENT - Scan Results Section - Commented out for initial release */}
        {/*
        <LazySection
          placeholder={<div className="h-64 bg-gray-100 animate-pulse rounded-lg" />}
        >
          <ScanResults
            results={scanResults}
            isLoading={scanLoading}
            onRunScan={handleRunScan}
            onResultClick={handleScanResultClick}
            lastScanDate={lastScanDate || undefined}
          />
        </LazySection>
        */}

        {/* Add Renewal Modal */}
        <AddRenewalModal
          isOpen={isAddRenewalModalOpen}
          onClose={handleCloseAddRenewalModal}
          onSubmit={handleSubmitRenewal}
        />
      </div>
    </ErrorBoundary>
  )
}

