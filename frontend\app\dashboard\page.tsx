
'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useTenant } from '@/contexts/TenantContext'
import { useEffect, useState } from 'react'

interface DashboardStats {
  totalRenewals: number
  renewalsDue: number
  vendors: number
  annualSpend: string
}

interface Renewal {
  id: string
  name: string
  vendor: string
  status: string
  dueDate: string
}

export default function DashboardPage() {
  const { user } = useAuth()
  const { tenant, loading: tenantLoading, error: tenantError } = useTenant()
  const [stats, setStats] = useState<DashboardStats>({
    totalRenewals: 14,
    renewalsDue: 0,
    vendors: 9,
    annualSpend: '$817,340'
  })
  const [recentRenewals, setRecentRenewals] = useState<Renewal[]>([
    { id: '1', name: 'Unlimited Renewal', vendor: 'Vox Audio', status: 'Not available', dueDate: 'Run Now 2025' },
    { id: '2', name: 'Core Pro', vendor: 'Vox Audio', status: 'Not available', dueDate: 'Run Now 2025' },
    { id: '3', name: 'Test Software', vendor: 'Vox Tools', status: 'Not available', dueDate: 'Run Now 2025' },
    { id: '4', name: 'Mobile app', vendor: 'TopCar', status: 'Not available', dueDate: 'Run Now 2025' },
    { id: '5', name: 'ASAUDB', vendor: 'Cubix', status: 'Not available', dueDate: 'Run Now 2025' }
  ])
  const [isLoading, setIsLoading] = useState(false)

  // Fetch dashboard data
  useEffect(() => {
    let isMounted = true

    const fetchDashboardData = async () => {
      if (tenant && isMounted) {
        console.log('✅ [DASHBOARD] Tenant context loaded:', {
          clientId: tenant.clientId,
          clientName: tenant.clientName,
          tenantSchema: tenant.tenantSchema,
          domains: tenant.domains,
          isActive: tenant.isActive
        })
        setIsLoading(true)

        try {

          // Fetch dashboard stats
          try {
            const statsResponse = await fetch('/api/dashboard/stats')
            if (statsResponse.ok && isMounted) {
              const statsData = await statsResponse.json()
              setStats(statsData)
              console.log('📊 [DASHBOARD] Stats updated:', statsData)
            }
          } catch (error) {
            if (isMounted) {
              console.error('💥 [DASHBOARD] Error fetching stats:', error)
            }
          }

          // Fetch recent renewals
          try {
            const renewalsResponse = await fetch('/api/dashboard/renewals')
            if (renewalsResponse.ok && isMounted) {
              const renewalsData = await renewalsResponse.json()
              setRecentRenewals(renewalsData)
              console.log('📋 [DASHBOARD] Renewals updated:', renewalsData)
            }
          } catch (error) {
            if (isMounted) {
              console.error('💥 [DASHBOARD] Error fetching renewals:', error)
            }
          }
        } catch (error) {
          if (isMounted) {
            console.error('💥 [DASHBOARD] Error fetching client details:', error)
          }
        } finally {
          if (isMounted) {
            setIsLoading(false)
          }
        }
      }
    }

    fetchDashboardData()

    return () => {
      isMounted = false
    }
  }, [tenant])

  // Show loading state
  if (tenantLoading) {
    return (
      <div className="dashboard-container">
        <div className="text-center py-8">
          <div className="text-4xl mb-4">⏳</div>
          <h3 className="text-lg font-medium mb-2">Loading tenant information...</h3>
          <p className="text-secondary">Please wait while we set up your dashboard.</p>
        </div>
      </div>
    )
  }

  // Show error state
  if (tenantError) {
    return (
      <div className="dashboard-container">
        <div className="text-center py-8">
          <div className="text-4xl mb-4">❌</div>
          <h3 className="text-lg font-medium mb-2">Error Loading Dashboard</h3>
          <p className="text-secondary mb-4">{tenantError}</p>
          <button
            className="btn btn-primary"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <div className="dashboard-title-section">
          <h1 className="dashboard-title">Dashboard - {tenant?.clientName || 'Unknown Client'}</h1>
          <p className="dashboard-subtitle">Manage your subscriptions, maintenance, support and warranties</p>
        </div>
        <div className="dashboard-actions">
          <div className="search-container">
            <input
              type="text"
              placeholder="Search renewals..."
              className="search-input"
            />
            <span className="search-icon">🔍</span>
          </div>
          <button className="btn btn-primary">
            + Add Renewal
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">📊</div>
          <h3>Total Renewals</h3>
          <p className="stat-value">{stats.totalRenewals}</p>
        </div>

        <div className="stat-card">
          <div className="stat-icon">⚠️</div>
          <h3>Renewals Due</h3>
          <p className="stat-value">{stats.renewalsDue}</p>
        </div>

        <div className="stat-card">
          <div className="stat-icon">🏢</div>
          <h3>Vendors</h3>
          <p className="stat-value">{stats.vendors}</p>
        </div>

        <div className="stat-card">
          <div className="stat-icon">💰</div>
          <h3>Annual Spend</h3>
          <p className="stat-value">{stats.annualSpend}</p>
        </div>
      </div>

      {/* Upcoming Renewals Section */}
      <div className="card" style={{ marginBottom: '24px' }}>
        <div className="card-header">
          <h2 className="text-lg font-semibold">Upcoming Renewals</h2>
        </div>
        <div className="card-content">
          <div className="text-center py-8">
            <div className="text-6xl mb-4">📄</div>
            <h3 className="text-lg font-medium mb-2">No upcoming renewals</h3>
            <p className="text-secondary">There are no software renewals due in the next 30 days.</p>
          </div>
        </div>
      </div>

      {/* Bottom Section with Recent Renewals and Scan Results */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recently Added Renewals */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold">Recently Added Renewals</h2>
          </div>
          <div className="card-content">
            <div className="space-y-3">
              {recentRenewals.map((renewal) => (
                <div key={renewal.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                  <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                    <span className="text-blue-600 text-sm">📄</span>
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-sm">{renewal.name}</p>
                    <p className="text-xs text-secondary">Added on {(renewal as any).addedDate || renewal.dueDate}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-secondary">{renewal.vendor}</p>
                    <p className="text-xs text-blue-600">View Details</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Latest Scan Results */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold">Latest Scan Results</h2>
          </div>
          <div className="card-content">
            <div className="text-center py-8">
              <div className="text-4xl mb-4">🔍</div>
              <h3 className="text-lg font-medium mb-2">No scan results available.</h3>
              <p className="text-secondary mb-4">Run your first scan</p>
              <button className="btn btn-primary btn-sm">Run Scan</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

