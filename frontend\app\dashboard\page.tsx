
/**
 * Dashboard Page - Modular and Optimized
 *
 * This page demonstrates the new modular architecture with:
 * - Separated concerns into focused components
 * - Custom hooks for data management
 * - Error boundaries for resilience
 * - Proper loading states
 * - Type safety throughout
 */

'use client'

import { useState, useCallback, useMemo } from 'react'
import { useTenant } from '@/contexts/AppContext'
import { useDashboardData } from '@/hooks/useDashboardData'
import { usePerformanceMonitor, useDebounce } from '@/lib/performance'

// Modular dashboard components
import DashboardHeader from '@/components/dashboard/DashboardHeader'
import DashboardStats from '@/components/dashboard/DashboardStats'
import RecentRenewals from '@/components/dashboard/RecentRenewals'
import UpcomingRenewals from '@/components/dashboard/UpcomingRenewals'
import ScanResults from '@/components/dashboard/ScanResults'

// Common components
import ErrorBoundary from '@/components/common/ErrorBoundary'
import { LoadingPage } from '@/components/common/LoadingStates'
import { LazySection } from '@/components/common/LazyLoad'

// Types
import { Renewal } from '@/lib/types'



export default function DashboardPage() {
  const { tenant, loading: tenantLoading, error: tenantError } = useTenant()
  const { data, isLoading, error, refetch } = useDashboardData()

  // Performance monitoring
  usePerformanceMonitor('DashboardPage')

  const [searchQuery, setSearchQuery] = useState('')

  // Debounce search to prevent excessive API calls
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Memoized event handlers to prevent unnecessary re-renders
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query)
    // TODO: Implement search functionality
    console.log('Searching for:', query)
  }, [])

  const handleAddRenewal = useCallback(() => {
    // TODO: Implement add renewal functionality
    console.log('Add renewal clicked')
  }, [])

  const handleRenewalClick = useCallback((renewal: Renewal) => {
    // TODO: Navigate to renewal details
    console.log('Renewal clicked:', renewal)
  }, [])

  const handleRunScan = useCallback(() => {
    // TODO: Implement scan functionality
    console.log('Run scan clicked')
  }, [])

  const handleScanResultClick = useCallback((result: any) => {
    // TODO: Handle scan result click
    console.log('Scan result clicked:', result)
  }, [])

  // Memoize mock scan results to prevent recreation
  const mockScanResults = useMemo(() => [
    {
      id: '1',
      type: 'software' as const,
      name: 'Microsoft Office 365',
      status: 'found' as const,
      lastSeen: new Date('2025-01-15'),
      details: 'License expires in 30 days'
    },
    {
      id: '2',
      type: 'license' as const,
      name: 'Adobe Creative Suite',
      status: 'warning' as const,
      lastSeen: new Date('2025-01-10'),
      details: 'Usage exceeds license limit'
    },
    {
      id: '3',
      type: 'renewal' as const,
      name: 'Slack Premium',
      status: 'expired' as const,
      lastSeen: new Date('2025-01-01'),
      details: 'Renewal overdue by 15 days'
    }
  ], [])

  // Show loading state
  if (tenantLoading || isLoading) {
    return (
      <LoadingPage
        title="Loading Dashboard..."
        subtitle="Please wait while we set up your dashboard."
        icon="⏳"
      />
    )
  }

  // Show error state
  if (tenantError || error) {
    return (
      <div className="dashboard-container">
        <div className="text-center py-8">
          <div className="text-4xl mb-4">❌</div>
          <h3 className="text-lg font-medium mb-2">Error Loading Dashboard</h3>
          <p className="text-secondary mb-4">{tenantError || error}</p>
          <button
            className="btn btn-primary"
            onClick={() => refetch()}
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('Dashboard error:', error, errorInfo)
      }}
      resetKeys={[tenant?.clientId]}
    >
      <div className="dashboard-container">
        {/* Header Section */}
        <DashboardHeader
          clientName={tenant?.clientName}
          onSearch={handleSearch}
          onAddRenewal={handleAddRenewal}
          searchPlaceholder="Search renewals..."
        />

        {/* Statistics Section */}
        <DashboardStats
          stats={data.stats}
          isLoading={isLoading}
          className="mb-6"
        />

        {/* Upcoming Renewals Section - Lazy loaded */}
        <LazySection
          placeholder={<div className="h-32 bg-gray-100 animate-pulse rounded-lg mb-6" />}
          className="mb-6"
        >
          <UpcomingRenewals
            renewals={data.upcomingRenewals}
            isLoading={isLoading}
            onRenewalClick={handleRenewalClick}
            daysThreshold={30}
          />
        </LazySection>

        {/* Bottom Section with Recent Renewals and Scan Results - Lazy loaded */}
        <LazySection
          placeholder={
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="h-64 bg-gray-100 animate-pulse rounded-lg" />
              <div className="h-64 bg-gray-100 animate-pulse rounded-lg" />
            </div>
          }
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <RecentRenewals
              renewals={data.recentRenewals}
              isLoading={isLoading}
              onRenewalClick={handleRenewalClick}
              maxItems={5}
            />

            <ScanResults
              results={mockScanResults}
              isLoading={false}
              onRunScan={handleRunScan}
              onResultClick={handleScanResultClick}
              lastScanDate={new Date()}
            />
          </div>
        </LazySection>
      </div>
    </ErrorBoundary>
  )
}

