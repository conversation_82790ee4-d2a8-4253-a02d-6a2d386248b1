e4a05f59836661d556dcb86eed33f51b
/**
 * Dashboard Page - Modular and Optimized
 *
 * This page demonstrates the new modular architecture with:
 * - Separated concerns into focused components
 * - Custom hooks for data management
 * - Error boundaries for resilience
 * - Proper loading states
 * - Type safety throughout
 */

'use client';

/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\dashboard\\page.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_e9mg3q03f() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\dashboard\\page.tsx";
  var hash = "4407ae9f504a07b9f6c5dc611d42a49b829df59d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\dashboard\\page.tsx",
    statementMap: {
      "0": {
        start: {
          line: 38,
          column: 65
        },
        end: {
          line: 38,
          column: 76
        }
      },
      "1": {
        start: {
          line: 39,
          column: 46
        },
        end: {
          line: 39,
          column: 64
        }
      },
      "2": {
        start: {
          line: 42,
          column: 2
        },
        end: {
          line: 42,
          column: 40
        }
      },
      "3": {
        start: {
          line: 44,
          column: 40
        },
        end: {
          line: 44,
          column: 52
        }
      },
      "4": {
        start: {
          line: 47,
          column: 31
        },
        end: {
          line: 47,
          column: 60
        }
      },
      "5": {
        start: {
          line: 50,
          column: 23
        },
        end: {
          line: 54,
          column: 8
        }
      },
      "6": {
        start: {
          line: 51,
          column: 4
        },
        end: {
          line: 51,
          column: 25
        }
      },
      "7": {
        start: {
          line: 53,
          column: 4
        },
        end: {
          line: 53,
          column: 40
        }
      },
      "8": {
        start: {
          line: 56,
          column: 27
        },
        end: {
          line: 59,
          column: 8
        }
      },
      "9": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 58,
          column: 38
        }
      },
      "10": {
        start: {
          line: 61,
          column: 29
        },
        end: {
          line: 64,
          column: 8
        }
      },
      "11": {
        start: {
          line: 63,
          column: 4
        },
        end: {
          line: 63,
          column: 44
        }
      },
      "12": {
        start: {
          line: 66,
          column: 24
        },
        end: {
          line: 69,
          column: 8
        }
      },
      "13": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 68,
          column: 35
        }
      },
      "14": {
        start: {
          line: 71,
          column: 32
        },
        end: {
          line: 74,
          column: 8
        }
      },
      "15": {
        start: {
          line: 73,
          column: 4
        },
        end: {
          line: 73,
          column: 47
        }
      },
      "16": {
        start: {
          line: 77,
          column: 26
        },
        end: {
          line: 102,
          column: 8
        }
      },
      "17": {
        start: {
          line: 77,
          column: 40
        },
        end: {
          line: 102,
          column: 3
        }
      },
      "18": {
        start: {
          line: 105,
          column: 2
        },
        end: {
          line: 113,
          column: 3
        }
      },
      "19": {
        start: {
          line: 106,
          column: 4
        },
        end: {
          line: 112,
          column: 5
        }
      },
      "20": {
        start: {
          line: 116,
          column: 2
        },
        end: {
          line: 132,
          column: 3
        }
      },
      "21": {
        start: {
          line: 117,
          column: 4
        },
        end: {
          line: 131,
          column: 5
        }
      },
      "22": {
        start: {
          line: 125,
          column: 27
        },
        end: {
          line: 125,
          column: 36
        }
      },
      "23": {
        start: {
          line: 134,
          column: 2
        },
        end: {
          line: 198,
          column: 3
        }
      },
      "24": {
        start: {
          line: 137,
          column: 8
        },
        end: {
          line: 137,
          column: 59
        }
      }
    },
    fnMap: {
      "0": {
        name: "DashboardPage",
        decl: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 37
          }
        },
        loc: {
          start: {
            line: 37,
            column: 40
          },
          end: {
            line: 199,
            column: 1
          }
        },
        line: 37
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 50,
            column: 35
          },
          end: {
            line: 50,
            column: 36
          }
        },
        loc: {
          start: {
            line: 50,
            column: 54
          },
          end: {
            line: 54,
            column: 3
          }
        },
        line: 50
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 56,
            column: 39
          },
          end: {
            line: 56,
            column: 40
          }
        },
        loc: {
          start: {
            line: 56,
            column: 45
          },
          end: {
            line: 59,
            column: 3
          }
        },
        line: 56
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 61,
            column: 41
          },
          end: {
            line: 61,
            column: 42
          }
        },
        loc: {
          start: {
            line: 61,
            column: 63
          },
          end: {
            line: 64,
            column: 3
          }
        },
        line: 61
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 66,
            column: 36
          },
          end: {
            line: 66,
            column: 37
          }
        },
        loc: {
          start: {
            line: 66,
            column: 42
          },
          end: {
            line: 69,
            column: 3
          }
        },
        line: 66
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 71,
            column: 44
          },
          end: {
            line: 71,
            column: 45
          }
        },
        loc: {
          start: {
            line: 71,
            column: 61
          },
          end: {
            line: 74,
            column: 3
          }
        },
        line: 71
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 77,
            column: 34
          },
          end: {
            line: 77,
            column: 35
          }
        },
        loc: {
          start: {
            line: 77,
            column: 40
          },
          end: {
            line: 102,
            column: 3
          }
        },
        line: 77
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 125,
            column: 21
          },
          end: {
            line: 125,
            column: 22
          }
        },
        loc: {
          start: {
            line: 125,
            column: 27
          },
          end: {
            line: 125,
            column: 36
          }
        },
        line: 125
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 136,
            column: 15
          },
          end: {
            line: 136,
            column: 16
          }
        },
        loc: {
          start: {
            line: 136,
            column: 37
          },
          end: {
            line: 138,
            column: 7
          }
        },
        line: 136
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 105,
            column: 2
          },
          end: {
            line: 113,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 105,
            column: 2
          },
          end: {
            line: 113,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 105
      },
      "1": {
        loc: {
          start: {
            line: 105,
            column: 6
          },
          end: {
            line: 105,
            column: 32
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 105,
            column: 6
          },
          end: {
            line: 105,
            column: 19
          }
        }, {
          start: {
            line: 105,
            column: 23
          },
          end: {
            line: 105,
            column: 32
          }
        }],
        line: 105
      },
      "2": {
        loc: {
          start: {
            line: 116,
            column: 2
          },
          end: {
            line: 132,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 116,
            column: 2
          },
          end: {
            line: 132,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 116
      },
      "3": {
        loc: {
          start: {
            line: 116,
            column: 6
          },
          end: {
            line: 116,
            column: 26
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 116,
            column: 6
          },
          end: {
            line: 116,
            column: 17
          }
        }, {
          start: {
            line: 116,
            column: 21
          },
          end: {
            line: 116,
            column: 26
          }
        }],
        line: 116
      },
      "4": {
        loc: {
          start: {
            line: 122,
            column: 46
          },
          end: {
            line: 122,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 122,
            column: 46
          },
          end: {
            line: 122,
            column: 57
          }
        }, {
          start: {
            line: 122,
            column: 61
          },
          end: {
            line: 122,
            column: 66
          }
        }],
        line: 122
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "4407ae9f504a07b9f6c5dc611d42a49b829df59d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_e9mg3q03f = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_e9mg3q03f();
import { useState, useCallback, useMemo } from 'react';
import { useTenant } from '@/contexts/AppContext';
import { useDashboardData } from '@/hooks/useDashboardData';
import { usePerformanceMonitor, useDebounce } from '@/lib/performance';

// Modular dashboard components
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import DashboardStats from '@/components/dashboard/DashboardStats';
import RecentRenewals from '@/components/dashboard/RecentRenewals';
import UpcomingRenewals from '@/components/dashboard/UpcomingRenewals';
import ScanResults from '@/components/dashboard/ScanResults';

// Common components
import ErrorBoundary from '@/components/common/ErrorBoundary';
import { LoadingPage } from '@/components/common/LoadingStates';
import { LazySection } from '@/components/common/LazyLoad';

// Types

export default function DashboardPage() {
  /* istanbul ignore next */
  cov_e9mg3q03f().f[0]++;
  const {
    tenant,
    loading: tenantLoading,
    error: tenantError
  } =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[0]++, useTenant());
  const {
    data,
    isLoading,
    error,
    refetch
  } =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[1]++, useDashboardData());

  // Performance monitoring
  /* istanbul ignore next */
  cov_e9mg3q03f().s[2]++;
  usePerformanceMonitor('DashboardPage');
  const [searchQuery, setSearchQuery] =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[3]++, useState(''));

  // Debounce search to prevent excessive API calls
  const debouncedSearchQuery =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[4]++, useDebounce(searchQuery, 300));

  // Memoized event handlers to prevent unnecessary re-renders
  const handleSearch =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[5]++, useCallback(query => {
    /* istanbul ignore next */
    cov_e9mg3q03f().f[1]++;
    cov_e9mg3q03f().s[6]++;
    setSearchQuery(query);
    // TODO: Implement search functionality
    /* istanbul ignore next */
    cov_e9mg3q03f().s[7]++;
    console.log('Searching for:', query);
  }, []));
  const handleAddRenewal =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[8]++, useCallback(() => {
    /* istanbul ignore next */
    cov_e9mg3q03f().f[2]++;
    cov_e9mg3q03f().s[9]++;
    // TODO: Implement add renewal functionality
    console.log('Add renewal clicked');
  }, []));
  const handleRenewalClick =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[10]++, useCallback(renewal => {
    /* istanbul ignore next */
    cov_e9mg3q03f().f[3]++;
    cov_e9mg3q03f().s[11]++;
    // TODO: Navigate to renewal details
    console.log('Renewal clicked:', renewal);
  }, []));
  const handleRunScan =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[12]++, useCallback(() => {
    /* istanbul ignore next */
    cov_e9mg3q03f().f[4]++;
    cov_e9mg3q03f().s[13]++;
    // TODO: Implement scan functionality
    console.log('Run scan clicked');
  }, []));
  const handleScanResultClick =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[14]++, useCallback(result => {
    /* istanbul ignore next */
    cov_e9mg3q03f().f[5]++;
    cov_e9mg3q03f().s[15]++;
    // TODO: Handle scan result click
    console.log('Scan result clicked:', result);
  }, []));

  // Memoize mock scan results to prevent recreation
  const mockScanResults =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[16]++, useMemo(() => {
    /* istanbul ignore next */
    cov_e9mg3q03f().f[6]++;
    cov_e9mg3q03f().s[17]++;
    return [{
      id: '1',
      type: 'software',
      name: 'Microsoft Office 365',
      status: 'found',
      lastSeen: new Date('2025-01-15'),
      details: 'License expires in 30 days'
    }, {
      id: '2',
      type: 'license',
      name: 'Adobe Creative Suite',
      status: 'warning',
      lastSeen: new Date('2025-01-10'),
      details: 'Usage exceeds license limit'
    }, {
      id: '3',
      type: 'renewal',
      name: 'Slack Premium',
      status: 'expired',
      lastSeen: new Date('2025-01-01'),
      details: 'Renewal overdue by 15 days'
    }];
  }, []));

  // Show loading state
  /* istanbul ignore next */
  cov_e9mg3q03f().s[18]++;
  if (
  /* istanbul ignore next */
  (cov_e9mg3q03f().b[1][0]++, tenantLoading) ||
  /* istanbul ignore next */
  (cov_e9mg3q03f().b[1][1]++, isLoading)) {
    /* istanbul ignore next */
    cov_e9mg3q03f().b[0][0]++;
    cov_e9mg3q03f().s[19]++;
    return /* istanbul ignore next */__jsx(LoadingPage,
    /* istanbul ignore next */
    {
      title: "Loading Dashboard...",
      subtitle: "Please wait while we set up your dashboard.",
      icon: "\u23F3",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 107,
        columnNumber: 7
      }
    });
  } else
  /* istanbul ignore next */
  {
    cov_e9mg3q03f().b[0][1]++;
  }

  // Show error state
  cov_e9mg3q03f().s[20]++;
  if (
  /* istanbul ignore next */
  (cov_e9mg3q03f().b[3][0]++, tenantError) ||
  /* istanbul ignore next */
  (cov_e9mg3q03f().b[3][1]++, error)) {
    /* istanbul ignore next */
    cov_e9mg3q03f().b[2][0]++;
    cov_e9mg3q03f().s[21]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "dashboard-container",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 118,
        columnNumber: 7
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "text-center py-8",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 119,
        columnNumber: 9
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "text-4xl mb-4",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 120,
        columnNumber: 11
      }
    }, "\u274C"),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "h3",
    /* istanbul ignore next */
    {
      className: "text-lg font-medium mb-2",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 121,
        columnNumber: 11
      }
    }, "Error Loading Dashboard"),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "p",
    /* istanbul ignore next */
    {
      className: "text-secondary mb-4",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 122,
        columnNumber: 11
      }
    },
    /* istanbul ignore next */
    (cov_e9mg3q03f().b[4][0]++, tenantError) ||
    /* istanbul ignore next */
    (cov_e9mg3q03f().b[4][1]++, error)),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "button",
    /* istanbul ignore next */
    {
      className: "btn btn-primary",
      onClick: () => {
        /* istanbul ignore next */
        cov_e9mg3q03f().f[7]++;
        cov_e9mg3q03f().s[22]++;
        return refetch();
      },
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 123,
        columnNumber: 11
      }
    }, "Retry")));
  } else
  /* istanbul ignore next */
  {
    cov_e9mg3q03f().b[2][1]++;
  }
  cov_e9mg3q03f().s[23]++;
  return /* istanbul ignore next */__jsx(ErrorBoundary,
  /* istanbul ignore next */
  {
    onError: (error, errorInfo) => {
      /* istanbul ignore next */
      cov_e9mg3q03f().f[8]++;
      cov_e9mg3q03f().s[24]++;
      console.error('Dashboard error:', error, errorInfo);
    },
    resetKeys: [tenant?.clientId],
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 135,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "dashboard-container",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 141,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(DashboardHeader,
  /* istanbul ignore next */
  {
    clientName: tenant?.clientName,
    onSearch: handleSearch,
    onAddRenewal: handleAddRenewal,
    searchPlaceholder: "Search renewals...",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 143,
      columnNumber: 9
    }
  }),
  /* istanbul ignore next */
  __jsx(DashboardStats,
  /* istanbul ignore next */
  {
    stats: data.stats,
    isLoading: isLoading,
    className: "mb-6",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 151,
      columnNumber: 9
    }
  }),
  /* istanbul ignore next */
  __jsx(LazySection,
  /* istanbul ignore next */
  {
    placeholder:
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-32 bg-gray-100 animate-pulse rounded-lg mb-6",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 159,
        columnNumber: 24
      }
    }),
    className: "mb-6",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 158,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(UpcomingRenewals,
  /* istanbul ignore next */
  {
    renewals: data.upcomingRenewals,
    isLoading: isLoading,
    onRenewalClick: handleRenewalClick,
    daysThreshold: 30,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 162,
      columnNumber: 11
    }
  })),
  /* istanbul ignore next */
  __jsx(LazySection,
  /* istanbul ignore next */
  {
    placeholder:
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "grid grid-cols-1 lg:grid-cols-2 gap-6",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 173,
        columnNumber: 13
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-64 bg-gray-100 animate-pulse rounded-lg",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 174,
        columnNumber: 15
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-64 bg-gray-100 animate-pulse rounded-lg",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 175,
        columnNumber: 15
      }
    })),
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 171,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "grid grid-cols-1 lg:grid-cols-2 gap-6",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 179,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(RecentRenewals,
  /* istanbul ignore next */
  {
    renewals: data.recentRenewals,
    isLoading: isLoading,
    onRenewalClick: handleRenewalClick,
    maxItems: 5,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 180,
      columnNumber: 13
    }
  }),
  /* istanbul ignore next */
  __jsx(ScanResults,
  /* istanbul ignore next */
  {
    results: mockScanResults,
    isLoading: false,
    onRunScan: handleRunScan,
    onResultClick: handleScanResultClick,
    lastScanDate: new Date(),
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 187,
      columnNumber: 13
    }
  })))));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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