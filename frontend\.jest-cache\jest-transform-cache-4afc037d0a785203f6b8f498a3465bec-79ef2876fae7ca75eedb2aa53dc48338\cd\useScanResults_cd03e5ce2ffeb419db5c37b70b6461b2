aefb4e6e31dc260cb07d03d3112131c1
/**
 * Scan Results Hook
 * 
 * Custom hook for managing scan results data fetching and state management.
 * Focused responsibility: Data layer for scan results components.
 */

'use client';

/* istanbul ignore next */
function cov_1bwiy5smq8() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\hooks\\useScanResults.ts";
  var hash = "500ba874613144642bddc2925ee30a9c644f16d6";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\hooks\\useScanResults.ts",
    statementMap: {
      "0": {
        start: {
          line: 32,
          column: 65
        },
        end: {
          line: 32,
          column: 76
        }
      },
      "1": {
        start: {
          line: 34,
          column: 32
        },
        end: {
          line: 34,
          column: 58
        }
      },
      "2": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 51
        }
      },
      "3": {
        start: {
          line: 36,
          column: 28
        },
        end: {
          line: 36,
          column: 57
        }
      },
      "4": {
        start: {
          line: 37,
          column: 42
        },
        end: {
          line: 37,
          column: 69
        }
      },
      "5": {
        start: {
          line: 40,
          column: 23
        },
        end: {
          line: 40,
          column: 35
        }
      },
      "6": {
        start: {
          line: 41,
          column: 29
        },
        end: {
          line: 41,
          column: 65
        }
      },
      "7": {
        start: {
          line: 44,
          column: 27
        },
        end: {
          line: 107,
          column: 14
        }
      },
      "8": {
        start: {
          line: 45,
          column: 4
        },
        end: {
          line: 45,
          column: 48
        }
      },
      "9": {
        start: {
          line: 45,
          column: 42
        },
        end: {
          line: 45,
          column: 48
        }
      },
      "10": {
        start: {
          line: 48,
          column: 4
        },
        end: {
          line: 50,
          column: 5
        }
      },
      "11": {
        start: {
          line: 49,
          column: 6
        },
        end: {
          line: 49,
          column: 40
        }
      },
      "12": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 52,
          column: 54
        }
      },
      "13": {
        start: {
          line: 53,
          column: 19
        },
        end: {
          line: 53,
          column: 52
        }
      },
      "14": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 55,
          column: 22
        }
      },
      "15": {
        start: {
          line: 56,
          column: 4
        },
        end: {
          line: 56,
          column: 18
        }
      },
      "16": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 106,
          column: 5
        }
      },
      "17": {
        start: {
          line: 59,
          column: 23
        },
        end: {
          line: 64,
          column: 8
        }
      },
      "18": {
        start: {
          line: 66,
          column: 6
        },
        end: {
          line: 68,
          column: 7
        }
      },
      "19": {
        start: {
          line: 67,
          column: 8
        },
        end: {
          line: 67,
          column: 98
        }
      },
      "20": {
        start: {
          line: 70,
          column: 19
        },
        end: {
          line: 70,
          column: 40
        }
      },
      "21": {
        start: {
          line: 72,
          column: 6
        },
        end: {
          line: 72,
          column: 39
        }
      },
      "22": {
        start: {
          line: 72,
          column: 33
        },
        end: {
          line: 72,
          column: 39
        }
      },
      "23": {
        start: {
          line: 75,
          column: 6
        },
        end: {
          line: 85,
          column: 7
        }
      },
      "24": {
        start: {
          line: 76,
          column: 8
        },
        end: {
          line: 76,
          column: 43
        }
      },
      "25": {
        start: {
          line: 77,
          column: 8
        },
        end: {
          line: 77,
          column: 89
        }
      },
      "26": {
        start: {
          line: 78,
          column: 13
        },
        end: {
          line: 85,
          column: 7
        }
      },
      "27": {
        start: {
          line: 80,
          column: 8
        },
        end: {
          line: 80,
          column: 24
        }
      },
      "28": {
        start: {
          line: 81,
          column: 8
        },
        end: {
          line: 81,
          column: 35
        }
      },
      "29": {
        start: {
          line: 83,
          column: 8
        },
        end: {
          line: 83,
          column: 22
        }
      },
      "30": {
        start: {
          line: 84,
          column: 8
        },
        end: {
          line: 84,
          column: 29
        }
      },
      "31": {
        start: {
          line: 88,
          column: 6
        },
        end: {
          line: 88,
          column: 39
        }
      },
      "32": {
        start: {
          line: 88,
          column: 33
        },
        end: {
          line: 88,
          column: 39
        }
      },
      "33": {
        start: {
          line: 91,
          column: 6
        },
        end: {
          line: 93,
          column: 7
        }
      },
      "34": {
        start: {
          line: 92,
          column: 8
        },
        end: {
          line: 92,
          column: 14
        }
      },
      "35": {
        start: {
          line: 95,
          column: 27
        },
        end: {
          line: 95,
          column: 94
        }
      },
      "36": {
        start: {
          line: 96,
          column: 6
        },
        end: {
          line: 96,
          column: 28
        }
      },
      "37": {
        start: {
          line: 97,
          column: 6
        },
        end: {
          line: 97,
          column: 53
        }
      },
      "38": {
        start: {
          line: 100,
          column: 6
        },
        end: {
          line: 100,
          column: 20
        }
      },
      "39": {
        start: {
          line: 101,
          column: 6
        },
        end: {
          line: 101,
          column: 27
        }
      },
      "40": {
        start: {
          line: 103,
          column: 6
        },
        end: {
          line: 105,
          column: 7
        }
      },
      "41": {
        start: {
          line: 104,
          column: 8
        },
        end: {
          line: 104,
          column: 27
        }
      },
      "42": {
        start: {
          line: 110,
          column: 18
        },
        end: {
          line: 138,
          column: 32
        }
      },
      "43": {
        start: {
          line: 111,
          column: 4
        },
        end: {
          line: 111,
          column: 48
        }
      },
      "44": {
        start: {
          line: 111,
          column: 42
        },
        end: {
          line: 111,
          column: 48
        }
      },
      "45": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 113,
          column: 22
        }
      },
      "46": {
        start: {
          line: 114,
          column: 4
        },
        end: {
          line: 114,
          column: 18
        }
      },
      "47": {
        start: {
          line: 116,
          column: 4
        },
        end: {
          line: 137,
          column: 5
        }
      },
      "48": {
        start: {
          line: 117,
          column: 23
        },
        end: {
          line: 122,
          column: 8
        }
      },
      "49": {
        start: {
          line: 124,
          column: 6
        },
        end: {
          line: 126,
          column: 7
        }
      },
      "50": {
        start: {
          line: 125,
          column: 8
        },
        end: {
          line: 125,
          column: 88
        }
      },
      "51": {
        start: {
          line: 129,
          column: 6
        },
        end: {
          line: 129,
          column: 30
        }
      },
      "52": {
        start: {
          line: 132,
          column: 6
        },
        end: {
          line: 132,
          column: 39
        }
      },
      "53": {
        start: {
          line: 132,
          column: 33
        },
        end: {
          line: 132,
          column: 39
        }
      },
      "54": {
        start: {
          line: 134,
          column: 27
        },
        end: {
          line: 134,
          column: 84
        }
      },
      "55": {
        start: {
          line: 135,
          column: 6
        },
        end: {
          line: 135,
          column: 28
        }
      },
      "56": {
        start: {
          line: 136,
          column: 6
        },
        end: {
          line: 136,
          column: 43
        }
      },
      "57": {
        start: {
          line: 141,
          column: 2
        },
        end: {
          line: 147,
          column: 60
        }
      },
      "58": {
        start: {
          line: 142,
          column: 4
        },
        end: {
          line: 146,
          column: 5
        }
      },
      "59": {
        start: {
          line: 143,
          column: 6
        },
        end: {
          line: 143,
          column: 24
        }
      },
      "60": {
        start: {
          line: 144,
          column: 11
        },
        end: {
          line: 146,
          column: 5
        }
      },
      "61": {
        start: {
          line: 145,
          column: 6
        },
        end: {
          line: 145,
          column: 27
        }
      },
      "62": {
        start: {
          line: 150,
          column: 2
        },
        end: {
          line: 157,
          column: 8
        }
      },
      "63": {
        start: {
          line: 151,
          column: 4
        },
        end: {
          line: 156,
          column: 5
        }
      },
      "64": {
        start: {
          line: 152,
          column: 6
        },
        end: {
          line: 152,
          column: 34
        }
      },
      "65": {
        start: {
          line: 153,
          column: 6
        },
        end: {
          line: 155,
          column: 7
        }
      },
      "66": {
        start: {
          line: 154,
          column: 8
        },
        end: {
          line: 154,
          column: 42
        }
      },
      "67": {
        start: {
          line: 159,
          column: 2
        },
        end: {
          line: 166,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "useScanResults",
        decl: {
          start: {
            line: 31,
            column: 16
          },
          end: {
            line: 31,
            column: 30
          }
        },
        loc: {
          start: {
            line: 31,
            column: 55
          },
          end: {
            line: 167,
            column: 1
          }
        },
        line: 31
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 44,
            column: 39
          },
          end: {
            line: 44,
            column: 40
          }
        },
        loc: {
          start: {
            line: 44,
            column: 66
          },
          end: {
            line: 107,
            column: 3
          }
        },
        line: 44
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 110,
            column: 30
          },
          end: {
            line: 110,
            column: 31
          }
        },
        loc: {
          start: {
            line: 110,
            column: 57
          },
          end: {
            line: 138,
            column: 3
          }
        },
        line: 110
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 141,
            column: 12
          },
          end: {
            line: 141,
            column: 13
          }
        },
        loc: {
          start: {
            line: 141,
            column: 18
          },
          end: {
            line: 147,
            column: 3
          }
        },
        line: 141
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 150,
            column: 12
          },
          end: {
            line: 150,
            column: 13
          }
        },
        loc: {
          start: {
            line: 150,
            column: 18
          },
          end: {
            line: 157,
            column: 3
          }
        },
        line: 150
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 151,
            column: 11
          },
          end: {
            line: 151,
            column: 12
          }
        },
        loc: {
          start: {
            line: 151,
            column: 17
          },
          end: {
            line: 156,
            column: 5
          }
        },
        line: 151
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 45,
            column: 4
          },
          end: {
            line: 45,
            column: 48
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 45,
            column: 4
          },
          end: {
            line: 45,
            column: 48
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 45
      },
      "1": {
        loc: {
          start: {
            line: 45,
            column: 8
          },
          end: {
            line: 45,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 45,
            column: 8
          },
          end: {
            line: 45,
            column: 15
          }
        }, {
          start: {
            line: 45,
            column: 19
          },
          end: {
            line: 45,
            column: 40
          }
        }],
        line: 45
      },
      "2": {
        loc: {
          start: {
            line: 48,
            column: 4
          },
          end: {
            line: 50,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 48,
            column: 4
          },
          end: {
            line: 50,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 48
      },
      "3": {
        loc: {
          start: {
            line: 66,
            column: 6
          },
          end: {
            line: 68,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 66,
            column: 6
          },
          end: {
            line: 68,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 66
      },
      "4": {
        loc: {
          start: {
            line: 72,
            column: 6
          },
          end: {
            line: 72,
            column: 39
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 6
          },
          end: {
            line: 72,
            column: 39
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "5": {
        loc: {
          start: {
            line: 75,
            column: 6
          },
          end: {
            line: 85,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 75,
            column: 6
          },
          end: {
            line: 85,
            column: 7
          }
        }, {
          start: {
            line: 78,
            column: 13
          },
          end: {
            line: 85,
            column: 7
          }
        }],
        line: 75
      },
      "6": {
        loc: {
          start: {
            line: 75,
            column: 10
          },
          end: {
            line: 75,
            column: 35
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 75,
            column: 10
          },
          end: {
            line: 75,
            column: 22
          }
        }, {
          start: {
            line: 75,
            column: 26
          },
          end: {
            line: 75,
            column: 35
          }
        }],
        line: 75
      },
      "7": {
        loc: {
          start: {
            line: 76,
            column: 19
          },
          end: {
            line: 76,
            column: 42
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 76,
            column: 19
          },
          end: {
            line: 76,
            column: 36
          }
        }, {
          start: {
            line: 76,
            column: 40
          },
          end: {
            line: 76,
            column: 42
          }
        }],
        line: 76
      },
      "8": {
        loc: {
          start: {
            line: 77,
            column: 24
          },
          end: {
            line: 77,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 77,
            column: 49
          },
          end: {
            line: 77,
            column: 81
          }
        }, {
          start: {
            line: 77,
            column: 84
          },
          end: {
            line: 77,
            column: 88
          }
        }],
        line: 77
      },
      "9": {
        loc: {
          start: {
            line: 78,
            column: 13
          },
          end: {
            line: 85,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 78,
            column: 13
          },
          end: {
            line: 85,
            column: 7
          }
        }, {
          start: {
            line: 82,
            column: 13
          },
          end: {
            line: 85,
            column: 7
          }
        }],
        line: 78
      },
      "10": {
        loc: {
          start: {
            line: 88,
            column: 6
          },
          end: {
            line: 88,
            column: 39
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 88,
            column: 6
          },
          end: {
            line: 88,
            column: 39
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 88
      },
      "11": {
        loc: {
          start: {
            line: 91,
            column: 6
          },
          end: {
            line: 93,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 91,
            column: 6
          },
          end: {
            line: 93,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 91
      },
      "12": {
        loc: {
          start: {
            line: 91,
            column: 10
          },
          end: {
            line: 91,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 91,
            column: 10
          },
          end: {
            line: 91,
            column: 30
          }
        }, {
          start: {
            line: 91,
            column: 34
          },
          end: {
            line: 91,
            column: 59
          }
        }],
        line: 91
      },
      "13": {
        loc: {
          start: {
            line: 95,
            column: 27
          },
          end: {
            line: 95,
            column: 94
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 95,
            column: 50
          },
          end: {
            line: 95,
            column: 61
          }
        }, {
          start: {
            line: 95,
            column: 64
          },
          end: {
            line: 95,
            column: 94
          }
        }],
        line: 95
      },
      "14": {
        loc: {
          start: {
            line: 103,
            column: 6
          },
          end: {
            line: 105,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 103,
            column: 6
          },
          end: {
            line: 105,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 103
      },
      "15": {
        loc: {
          start: {
            line: 111,
            column: 4
          },
          end: {
            line: 111,
            column: 48
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 111,
            column: 4
          },
          end: {
            line: 111,
            column: 48
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 111
      },
      "16": {
        loc: {
          start: {
            line: 111,
            column: 8
          },
          end: {
            line: 111,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 111,
            column: 8
          },
          end: {
            line: 111,
            column: 15
          }
        }, {
          start: {
            line: 111,
            column: 19
          },
          end: {
            line: 111,
            column: 40
          }
        }],
        line: 111
      },
      "17": {
        loc: {
          start: {
            line: 124,
            column: 6
          },
          end: {
            line: 126,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 124,
            column: 6
          },
          end: {
            line: 126,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 124
      },
      "18": {
        loc: {
          start: {
            line: 132,
            column: 6
          },
          end: {
            line: 132,
            column: 39
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 132,
            column: 6
          },
          end: {
            line: 132,
            column: 39
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 132
      },
      "19": {
        loc: {
          start: {
            line: 134,
            column: 27
          },
          end: {
            line: 134,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 134,
            column: 50
          },
          end: {
            line: 134,
            column: 61
          }
        }, {
          start: {
            line: 134,
            column: 64
          },
          end: {
            line: 134,
            column: 84
          }
        }],
        line: 134
      },
      "20": {
        loc: {
          start: {
            line: 142,
            column: 4
          },
          end: {
            line: 146,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 142,
            column: 4
          },
          end: {
            line: 146,
            column: 5
          }
        }, {
          start: {
            line: 144,
            column: 11
          },
          end: {
            line: 146,
            column: 5
          }
        }],
        line: 142
      },
      "21": {
        loc: {
          start: {
            line: 142,
            column: 8
          },
          end: {
            line: 142,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 142,
            column: 8
          },
          end: {
            line: 142,
            column: 14
          }
        }, {
          start: {
            line: 142,
            column: 18
          },
          end: {
            line: 142,
            column: 32
          }
        }, {
          start: {
            line: 142,
            column: 36
          },
          end: {
            line: 142,
            column: 48
          }
        }],
        line: 142
      },
      "22": {
        loc: {
          start: {
            line: 144,
            column: 11
          },
          end: {
            line: 146,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 144,
            column: 11
          },
          end: {
            line: 146,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 144
      },
      "23": {
        loc: {
          start: {
            line: 153,
            column: 6
          },
          end: {
            line: 155,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 153,
            column: 6
          },
          end: {
            line: 155,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 153
      },
      "24": {
        loc: {
          start: {
            line: 161,
            column: 15
          },
          end: {
            line: 161,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 161,
            column: 15
          },
          end: {
            line: 161,
            column: 24
          }
        }, {
          start: {
            line: 161,
            column: 28
          },
          end: {
            line: 161,
            column: 41
          }
        }],
        line: 161
      },
      "25": {
        loc: {
          start: {
            line: 162,
            column: 11
          },
          end: {
            line: 162,
            column: 31
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 162,
            column: 11
          },
          end: {
            line: 162,
            column: 16
          }
        }, {
          start: {
            line: 162,
            column: 20
          },
          end: {
            line: 162,
            column: 31
          }
        }],
        line: 162
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "500ba874613144642bddc2925ee30a9c644f16d6"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1bwiy5smq8 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1bwiy5smq8();
import { useState, useEffect, useCallback, useRef } from 'react';
import { useTenant } from '@/contexts/AppContext';
export function useScanResults() {
  /* istanbul ignore next */
  cov_1bwiy5smq8().f[0]++;
  const {
    tenant,
    loading: tenantLoading,
    error: tenantError
  } =
  /* istanbul ignore next */
  (cov_1bwiy5smq8().s[0]++, useTenant());
  const [results, setResults] =
  /* istanbul ignore next */
  (cov_1bwiy5smq8().s[1]++, useState([]));
  const [isLoading, setIsLoading] =
  /* istanbul ignore next */
  (cov_1bwiy5smq8().s[2]++, useState(false));
  const [error, setError] =
  /* istanbul ignore next */
  (cov_1bwiy5smq8().s[3]++, useState(null));
  const [lastScanDate, setLastScanDate] =
  /* istanbul ignore next */
  (cov_1bwiy5smq8().s[4]++, useState(null));

  // Use ref to track if component is mounted
  const isMountedRef =
  /* istanbul ignore next */
  (cov_1bwiy5smq8().s[5]++, useRef(true));
  const abortControllerRef =
  /* istanbul ignore next */
  (cov_1bwiy5smq8().s[6]++, useRef(null));

  // Fetch scan results
  const fetchScanResults =
  /* istanbul ignore next */
  (cov_1bwiy5smq8().s[7]++, useCallback(async () => {
    /* istanbul ignore next */
    cov_1bwiy5smq8().f[1]++;
    cov_1bwiy5smq8().s[8]++;
    if (
    /* istanbul ignore next */
    (cov_1bwiy5smq8().b[1][0]++, !tenant) ||
    /* istanbul ignore next */
    (cov_1bwiy5smq8().b[1][1]++, !isMountedRef.current)) {
      /* istanbul ignore next */
      cov_1bwiy5smq8().b[0][0]++;
      cov_1bwiy5smq8().s[9]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1bwiy5smq8().b[0][1]++;
    }

    // Cancel any existing request
    cov_1bwiy5smq8().s[10]++;
    if (abortControllerRef.current) {
      /* istanbul ignore next */
      cov_1bwiy5smq8().b[2][0]++;
      cov_1bwiy5smq8().s[11]++;
      abortControllerRef.current.abort();
    } else
    /* istanbul ignore next */
    {
      cov_1bwiy5smq8().b[2][1]++;
    }
    cov_1bwiy5smq8().s[12]++;
    abortControllerRef.current = new AbortController();
    const signal =
    /* istanbul ignore next */
    (cov_1bwiy5smq8().s[13]++, abortControllerRef.current.signal);
    /* istanbul ignore next */
    cov_1bwiy5smq8().s[14]++;
    setIsLoading(true);
    /* istanbul ignore next */
    cov_1bwiy5smq8().s[15]++;
    setError(null);
    /* istanbul ignore next */
    cov_1bwiy5smq8().s[16]++;
    try {
      const response =
      /* istanbul ignore next */
      (cov_1bwiy5smq8().s[17]++, await fetch('/api/dashboard/scan-results', {
        signal,
        headers: {
          'Content-Type': 'application/json'
        }
      }));
      /* istanbul ignore next */
      cov_1bwiy5smq8().s[18]++;
      if (!response.ok) {
        /* istanbul ignore next */
        cov_1bwiy5smq8().b[3][0]++;
        cov_1bwiy5smq8().s[19]++;
        throw new Error(`Failed to fetch scan results: ${response.status} ${response.statusText}`);
      } else
      /* istanbul ignore next */
      {
        cov_1bwiy5smq8().b[3][1]++;
      }
      const data =
      /* istanbul ignore next */
      (cov_1bwiy5smq8().s[20]++, await response.json());
      /* istanbul ignore next */
      cov_1bwiy5smq8().s[21]++;
      if (!isMountedRef.current) {
        /* istanbul ignore next */
        cov_1bwiy5smq8().b[4][0]++;
        cov_1bwiy5smq8().s[22]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_1bwiy5smq8().b[4][1]++;
      }

      // Handle both new API format and legacy format
      cov_1bwiy5smq8().s[23]++;
      if (
      /* istanbul ignore next */
      (cov_1bwiy5smq8().b[6][0]++, data.success) &&
      /* istanbul ignore next */
      (cov_1bwiy5smq8().b[6][1]++, data.data)) {
        /* istanbul ignore next */
        cov_1bwiy5smq8().b[5][0]++;
        cov_1bwiy5smq8().s[24]++;
        setResults(
        /* istanbul ignore next */
        (cov_1bwiy5smq8().b[7][0]++, data.data.results) ||
        /* istanbul ignore next */
        (cov_1bwiy5smq8().b[7][1]++, []));
        /* istanbul ignore next */
        cov_1bwiy5smq8().s[25]++;
        setLastScanDate(data.data.lastScanDate ?
        /* istanbul ignore next */
        (cov_1bwiy5smq8().b[8][0]++, new Date(data.data.lastScanDate)) :
        /* istanbul ignore next */
        (cov_1bwiy5smq8().b[8][1]++, null));
      } else {
        /* istanbul ignore next */
        cov_1bwiy5smq8().b[5][1]++;
        cov_1bwiy5smq8().s[26]++;
        if (Array.isArray(data)) {
          /* istanbul ignore next */
          cov_1bwiy5smq8().b[9][0]++;
          cov_1bwiy5smq8().s[27]++;
          // Legacy format
          setResults(data);
          /* istanbul ignore next */
          cov_1bwiy5smq8().s[28]++;
          setLastScanDate(new Date()); // Fallback to current date
        } else {
          /* istanbul ignore next */
          cov_1bwiy5smq8().b[9][1]++;
          cov_1bwiy5smq8().s[29]++;
          setResults([]);
          /* istanbul ignore next */
          cov_1bwiy5smq8().s[30]++;
          setLastScanDate(null);
        }
      }
    } catch (err) {
      /* istanbul ignore next */
      cov_1bwiy5smq8().s[31]++;
      if (!isMountedRef.current) {
        /* istanbul ignore next */
        cov_1bwiy5smq8().b[10][0]++;
        cov_1bwiy5smq8().s[32]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_1bwiy5smq8().b[10][1]++;
      }

      // Don't show error for aborted requests
      cov_1bwiy5smq8().s[33]++;
      if (
      /* istanbul ignore next */
      (cov_1bwiy5smq8().b[12][0]++, err instanceof Error) &&
      /* istanbul ignore next */
      (cov_1bwiy5smq8().b[12][1]++, err.name === 'AbortError')) {
        /* istanbul ignore next */
        cov_1bwiy5smq8().b[11][0]++;
        cov_1bwiy5smq8().s[34]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_1bwiy5smq8().b[11][1]++;
      }
      const errorMessage =
      /* istanbul ignore next */
      (cov_1bwiy5smq8().s[35]++, err instanceof Error ?
      /* istanbul ignore next */
      (cov_1bwiy5smq8().b[13][0]++, err.message) :
      /* istanbul ignore next */
      (cov_1bwiy5smq8().b[13][1]++, 'Failed to fetch scan results'));
      /* istanbul ignore next */
      cov_1bwiy5smq8().s[36]++;
      setError(errorMessage);
      /* istanbul ignore next */
      cov_1bwiy5smq8().s[37]++;
      console.error('Scan results fetch error:', err);

      // Set empty results on error
      /* istanbul ignore next */
      cov_1bwiy5smq8().s[38]++;
      setResults([]);
      /* istanbul ignore next */
      cov_1bwiy5smq8().s[39]++;
      setLastScanDate(null);
    } finally {
      /* istanbul ignore next */
      cov_1bwiy5smq8().s[40]++;
      if (isMountedRef.current) {
        /* istanbul ignore next */
        cov_1bwiy5smq8().b[14][0]++;
        cov_1bwiy5smq8().s[41]++;
        setIsLoading(false);
      } else
      /* istanbul ignore next */
      {
        cov_1bwiy5smq8().b[14][1]++;
      }
    }
  }, [tenant]));

  // Run a new scan
  const runScan =
  /* istanbul ignore next */
  (cov_1bwiy5smq8().s[42]++, useCallback(async () => {
    /* istanbul ignore next */
    cov_1bwiy5smq8().f[2]++;
    cov_1bwiy5smq8().s[43]++;
    if (
    /* istanbul ignore next */
    (cov_1bwiy5smq8().b[16][0]++, !tenant) ||
    /* istanbul ignore next */
    (cov_1bwiy5smq8().b[16][1]++, !isMountedRef.current)) {
      /* istanbul ignore next */
      cov_1bwiy5smq8().b[15][0]++;
      cov_1bwiy5smq8().s[44]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1bwiy5smq8().b[15][1]++;
    }
    cov_1bwiy5smq8().s[45]++;
    setIsLoading(true);
    /* istanbul ignore next */
    cov_1bwiy5smq8().s[46]++;
    setError(null);
    /* istanbul ignore next */
    cov_1bwiy5smq8().s[47]++;
    try {
      const response =
      /* istanbul ignore next */
      (cov_1bwiy5smq8().s[48]++, await fetch('/api/dashboard/run-scan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      }));
      /* istanbul ignore next */
      cov_1bwiy5smq8().s[49]++;
      if (!response.ok) {
        /* istanbul ignore next */
        cov_1bwiy5smq8().b[17][0]++;
        cov_1bwiy5smq8().s[50]++;
        throw new Error(`Failed to run scan: ${response.status} ${response.statusText}`);
      } else
      /* istanbul ignore next */
      {
        cov_1bwiy5smq8().b[17][1]++;
      }

      // After running scan, fetch the latest results
      cov_1bwiy5smq8().s[51]++;
      await fetchScanResults();
    } catch (err) {
      /* istanbul ignore next */
      cov_1bwiy5smq8().s[52]++;
      if (!isMountedRef.current) {
        /* istanbul ignore next */
        cov_1bwiy5smq8().b[18][0]++;
        cov_1bwiy5smq8().s[53]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_1bwiy5smq8().b[18][1]++;
      }
      const errorMessage =
      /* istanbul ignore next */
      (cov_1bwiy5smq8().s[54]++, err instanceof Error ?
      /* istanbul ignore next */
      (cov_1bwiy5smq8().b[19][0]++, err.message) :
      /* istanbul ignore next */
      (cov_1bwiy5smq8().b[19][1]++, 'Failed to run scan'));
      /* istanbul ignore next */
      cov_1bwiy5smq8().s[55]++;
      setError(errorMessage);
      /* istanbul ignore next */
      cov_1bwiy5smq8().s[56]++;
      console.error('Run scan error:', err);
    }
  }, [tenant, fetchScanResults]));

  // Main effect to fetch data when tenant changes
  /* istanbul ignore next */
  cov_1bwiy5smq8().s[57]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_1bwiy5smq8().f[3]++;
    cov_1bwiy5smq8().s[58]++;
    if (
    /* istanbul ignore next */
    (cov_1bwiy5smq8().b[21][0]++, tenant) &&
    /* istanbul ignore next */
    (cov_1bwiy5smq8().b[21][1]++, !tenantLoading) &&
    /* istanbul ignore next */
    (cov_1bwiy5smq8().b[21][2]++, !tenantError)) {
      /* istanbul ignore next */
      cov_1bwiy5smq8().b[20][0]++;
      cov_1bwiy5smq8().s[59]++;
      fetchScanResults();
    } else {
      /* istanbul ignore next */
      cov_1bwiy5smq8().b[20][1]++;
      cov_1bwiy5smq8().s[60]++;
      if (tenantError) {
        /* istanbul ignore next */
        cov_1bwiy5smq8().b[22][0]++;
        cov_1bwiy5smq8().s[61]++;
        setError(tenantError);
      } else
      /* istanbul ignore next */
      {
        cov_1bwiy5smq8().b[22][1]++;
      }
    }
  }, [tenant, tenantLoading, tenantError, fetchScanResults]);

  // Cleanup effect
  /* istanbul ignore next */
  cov_1bwiy5smq8().s[62]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_1bwiy5smq8().f[4]++;
    cov_1bwiy5smq8().s[63]++;
    return () => {
      /* istanbul ignore next */
      cov_1bwiy5smq8().f[5]++;
      cov_1bwiy5smq8().s[64]++;
      isMountedRef.current = false;
      /* istanbul ignore next */
      cov_1bwiy5smq8().s[65]++;
      if (abortControllerRef.current) {
        /* istanbul ignore next */
        cov_1bwiy5smq8().b[23][0]++;
        cov_1bwiy5smq8().s[66]++;
        abortControllerRef.current.abort();
      } else
      /* istanbul ignore next */
      {
        cov_1bwiy5smq8().b[23][1]++;
      }
    };
  }, []);
  /* istanbul ignore next */
  cov_1bwiy5smq8().s[67]++;
  return {
    results,
    isLoading:
    /* istanbul ignore next */
    (cov_1bwiy5smq8().b[24][0]++, isLoading) ||
    /* istanbul ignore next */
    (cov_1bwiy5smq8().b[24][1]++, tenantLoading),
    error:
    /* istanbul ignore next */
    (cov_1bwiy5smq8().b[25][0]++, error) ||
    /* istanbul ignore next */
    (cov_1bwiy5smq8().b[25][1]++, tenantError),
    lastScanDate,
    refetch: fetchScanResults,
    runScan
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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