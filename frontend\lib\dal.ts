import 'server-only'
import { cache } from 'react'
import { cookies } from 'next/headers'
import { validateAuthCookie, CognitoJwtPayload } from './jwt-validator'
import { publicConfig } from './config'

export const verifySession = cache(async (): Promise<{
  isAuth: boolean;
  userId: string;
  email: string;
  roles: string[];
} | null> => {
  try {
    const cookieStore = await cookies()
    const { userPoolClientId } = publicConfig.aws;

    // Try to get authentication tokens from cookies
    const possibleTokenCookies = [
      'idToken',
      `CognitoIdentityServiceProvider.${userPoolClientId}.LastAuthUser`,
      'amplify-signin-with-hostedUI'
    ];

    let authToken: string | null = null;

    // Check for various Amplify cookie patterns
    for (const cookieName of possibleTokenCookies) {
      const cookie = cookieStore.get(cookieName);
      if (cookie?.value) {
        authToken = cookie.value;
        console.log(`Found auth token in cookie: ${cookieName}`);
        break;
      }
    }

    // Also check for Cognito-specific cookies
    if (!authToken) {
      const allCookies = cookieStore.getAll();

      for (const cookie of allCookies) {
        if (cookie.name.includes('CognitoIdentityServiceProvider') && cookie.name.includes('idToken')) {
          authToken = cookie.value;
          console.log(`Found Cognito idToken in cookie: ${cookie.name}`);
          break;
        }
      }
    }

    if (!authToken) {
      console.log('No authentication token found in cookies');
      return null;
    }

    // Use secure JWT validation
    const payload = await validateAuthCookie(authToken);
    if (!payload) {
      console.log('JWT validation failed - token may be expired or invalid');

      // Clear invalid cookies to force re-authentication
      if (typeof window !== 'undefined') {
        // Clear all auth-related cookies
        document.cookie = 'idToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
        document.cookie = `CognitoIdentityServiceProvider.${publicConfig.aws.userPoolClientId}.LastAuthUser=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;

        // Redirect to login
        window.location.href = '/login';
      }

      return null;
    }

    console.log('Successfully verified session for user:', payload.email);

    return {
      isAuth: true,
      userId: payload.sub,
      email: payload.email,
      roles: payload['cognito:groups'] || []
    }
  } catch (error) {
    console.error('Auth verification error:', error)
    return null
  }
})

export const getUser = cache(async () => {
  const session = await verifySession()
  if (!session) return null
  
  // Get user data from session
  // Add your user fetching logic here
  return { id: session.userId }
})

// Role-based authorization helper
export const hasRole = (session: any, requiredRole: string) => {
  if (!session || !session.roles) return false
  return session.roles.includes(requiredRole)
}




