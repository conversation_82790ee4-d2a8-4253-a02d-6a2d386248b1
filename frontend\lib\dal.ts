import 'server-only'
import { cache } from 'react'
import { cookies } from 'next/headers'
import { getAmplifySSR } from './amplify-config'

export const verifySession = cache(async () => {
  try {
    const cookieStore = await cookies()

    // Try to get Amplify's authentication tokens from cookies
    // Amplify v6 stores tokens in different cookie names
    const possibleTokenCookies = [
      'idToken',
      'CognitoIdentityServiceProvider.6fc4ks4poom3mqk5icavr7np1k.LastAuthUser',
      'amplify-signin-with-hostedUI'
    ];

    let authToken = null;

    // Check for various Amplify cookie patterns
    for (const cookieName of possibleTokenCookies) {
      const cookie = cookieStore.get(cookieName);
      if (cookie?.value) {
        authToken = cookie.value;
        console.log(`Found auth token in cookie: ${cookieName}`);
        break;
      }
    }

    // Also check for Cognito-specific cookies
    const allCookies = cookieStore.getAll();
    console.log('All available cookies:', allCookies.map(c => c.name));

    for (const cookie of allCookies) {
      if (cookie.name.includes('CognitoIdentityServiceProvider') && cookie.name.includes('idToken')) {
        authToken = cookie.value;
        console.log(`Found Cognito idToken in cookie: ${cookie.name}`);
        break;
      }
    }

    if (!authToken) {
      console.log('No authentication token found in cookies. Available cookies:', allCookies.map(c => c.name));
      return null;
    }

    // Try to decode JWT token
    const tokenParts = authToken.split('.');
    if (tokenParts.length !== 3) {
      console.log('Invalid JWT token format');
      return null;
    }

    // Decode the payload part of the JWT
    const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());

    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (payload.exp && payload.exp < currentTime) {
      console.log('Token has expired');
      return null;
    }

    console.log('Successfully verified session for user:', payload.email);

    return {
      isAuth: true,
      userId: payload.sub,
      email: payload.email,
      roles: payload['cognito:groups'] || []
    }
  } catch (error) {
    console.error('Auth verification error:', error)
    return null
  }
})

export const getUser = cache(async () => {
  const session = await verifySession()
  if (!session) return null
  
  // Get user data from session
  // Add your user fetching logic here
  return { id: session.userId }
})

// Role-based authorization helper
export const hasRole = (session: any, requiredRole: string) => {
  if (!session || !session.roles) return false
  return session.roles.includes(requiredRole)
}




