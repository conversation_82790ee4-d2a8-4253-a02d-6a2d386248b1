/**
 * Scan Results Hook
 * 
 * Custom hook for managing scan results data fetching and state management.
 * Focused responsibility: Data layer for scan results components.
 */

'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { useTenant } from '@/contexts/AppContext'

interface ScanResult {
  id: string
  type: 'software' | 'license' | 'renewal'
  name: string
  status: 'found' | 'missing' | 'expired' | 'warning'
  lastSeen: Date
  details?: string
}

interface UseScanResultsReturn {
  results: ScanResult[]
  isLoading: boolean
  error: string | null
  lastScanDate: Date | null
  refetch: () => Promise<void>
  runScan: () => Promise<void>
}

export function useScanResults(): UseScanResultsReturn {
  const { tenant, loading: tenantLoading, error: tenantError } = useTenant()

  const [results, setResults] = useState<ScanResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastScanDate, setLastScanDate] = useState<Date | null>(null)

  // Use ref to track if component is mounted
  const isMountedRef = useRef(true)
  const abortControllerRef = useRef<AbortController | null>(null)

  // Fetch scan results
  const fetchScanResults = useCallback(async (): Promise<void> => {
    if (!tenant || !isMountedRef.current) return
    
    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    
    abortControllerRef.current = new AbortController()
    const signal = abortControllerRef.current.signal
    
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/dashboard/scan-results', {
        signal,
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        throw new Error(`Failed to fetch scan results: ${response.status} ${response.statusText}`)
      }
      
      const data = await response.json()
      
      if (!isMountedRef.current) return
      
      // Handle both new API format and legacy format
      if (data.success && data.data) {
        setResults(data.data.results || [])
        setLastScanDate(data.data.lastScanDate ? new Date(data.data.lastScanDate) : null)
      } else if (Array.isArray(data)) {
        // Legacy format
        setResults(data)
        setLastScanDate(new Date()) // Fallback to current date
      } else {
        setResults([])
        setLastScanDate(null)
      }
      
    } catch (err) {
      if (!isMountedRef.current) return
      
      // Don't show error for aborted requests
      if (err instanceof Error && err.name === 'AbortError') {
        return
      }
      
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch scan results'
      setError(errorMessage)
      console.error('Scan results fetch error:', err)
      
      // Set empty results on error
      setResults([])
      setLastScanDate(null)
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false)
      }
    }
  }, [tenant])

  // Run a new scan
  const runScan = useCallback(async (): Promise<void> => {
    if (!tenant || !isMountedRef.current) return
    
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/dashboard/run-scan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        throw new Error(`Failed to run scan: ${response.status} ${response.statusText}`)
      }
      
      // After running scan, fetch the latest results
      await fetchScanResults()
      
    } catch (err) {
      if (!isMountedRef.current) return
      
      const errorMessage = err instanceof Error ? err.message : 'Failed to run scan'
      setError(errorMessage)
      console.error('Run scan error:', err)
    }
  }, [tenant, fetchScanResults])

  // Main effect to fetch data when tenant changes
  useEffect(() => {
    if (tenant && !tenantLoading && !tenantError) {
      fetchScanResults()
    } else if (tenantError) {
      setError(tenantError)
    }
  }, [tenant, tenantLoading, tenantError, fetchScanResults])

  // Cleanup effect
  useEffect(() => {
    return () => {
      isMountedRef.current = false
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  return {
    results,
    isLoading: isLoading || tenantLoading,
    error: error || tenantError,
    lastScanDate,
    refetch: fetchScanResults,
    runScan
  }
}
