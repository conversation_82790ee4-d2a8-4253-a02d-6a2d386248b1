-- Migration: Create analytics views for cross-tenant analysis
-- These views enable analysis on clean, synchronized data

-- Cross-Tenant Vendor Analysis View
CREATE OR REPLACE VIEW metadata.vendor_analytics AS
WITH vendor_stats AS (
    SELECT 
        gv.id as global_vendor_id,
        gv.name as vendor_name,
        gv.country,
        gv.domain,
        COUNT(DISTINCT CASE 
            WHEN tvs.sync_status = 'synced' THEN 
                SUBSTRING(tvs.tenant_vendor_id FROM '^tenant_(\d+)\.') 
        END) as tenant_count,
        AVG(gv.confidence) as avg_confidence,
        MAX(gv.last_updated) as last_updated,
        ARRAY_AGG(DISTINCT gv.country) FILTER (WHERE gv.country IS NOT NULL) as geographic_presence
    FROM metadata.global_vendors gv
    LEFT JOIN (
        -- Union all tenant vendor sync tables (would need dynamic generation in practice)
        SELECT 'tenant_0000000000000001' as tenant_schema, tenant_vendor_id, global_vendor_id, sync_status
        FROM tenant_0000000000000001.tenant_vendor_sync
        WHERE sync_status = 'synced'
    ) tvs ON gv.id = tvs.global_vendor_id
    GROUP BY gv.id, gv.name, gv.country, gv.domain
),
spend_stats AS (
    SELECT 
        gv.id as global_vendor_id,
        SUM(r.cost) as total_spend,
        AVG(r.cost) as average_spend,
        COUNT(r.id) as renewal_count
    FROM metadata.global_vendors gv
    JOIN tenant_0000000000000001.tenant_vendor_sync tvs ON gv.id = tvs.global_vendor_id
    JOIN tenant_0000000000000001.renewals r ON r.vendor_id = tvs.tenant_vendor_id
    WHERE tvs.sync_status = 'synced'
    GROUP BY gv.id
)
SELECT 
    vs.global_vendor_id,
    vs.vendor_name,
    vs.country,
    vs.domain,
    vs.tenant_count,
    vs.avg_confidence,
    COALESCE(ss.total_spend, 0) as total_spend,
    COALESCE(ss.average_spend, 0) as average_spend,
    COALESCE(ss.renewal_count, 0) as renewal_count,
    vs.geographic_presence,
    CASE 
        WHEN vs.tenant_count >= 10 THEN 'low'
        WHEN vs.tenant_count >= 5 THEN 'medium'
        WHEN vs.tenant_count >= 2 THEN 'high'
        ELSE 'critical'
    END as risk_level,
    vs.last_updated
FROM vendor_stats vs
LEFT JOIN spend_stats ss ON vs.global_vendor_id = ss.global_vendor_id;

-- Product Portfolio Analysis View
CREATE OR REPLACE VIEW metadata.product_portfolio_analytics AS
WITH product_stats AS (
    SELECT 
        gp.id as global_product_id,
        gp.name as product_name,
        gv.name as vendor_name,
        gv.id as global_vendor_id,
        COUNT(DISTINCT tps.tenant_product_id) as tenant_count,
        AVG(gp.confidence) as avg_confidence,
        MAX(gp.last_updated) as last_updated
    FROM metadata.global_products gp
    JOIN metadata.global_vendors gv ON gp.global_vendor_id = gv.id
    LEFT JOIN tenant_0000000000000001.tenant_product_sync tps ON gp.id = tps.global_product_id
    WHERE tps.sync_status = 'synced' OR tps.sync_status IS NULL
    GROUP BY gp.id, gp.name, gv.name, gv.id
),
usage_stats AS (
    SELECT 
        gp.id as global_product_id,
        COUNT(r.id) as total_licenses,
        SUM(r.cost) as total_cost,
        AVG(r.cost) as average_cost
    FROM metadata.global_products gp
    JOIN tenant_0000000000000001.tenant_product_sync tps ON gp.id = tps.global_product_id
    JOIN tenant_0000000000000001.renewals r ON r.product_id = tps.tenant_product_id
    WHERE tps.sync_status = 'synced'
    GROUP BY gp.id
),
version_stats AS (
    SELECT 
        gp.id as global_product_id,
        COUNT(DISTINCT gpv.version) as version_count,
        MAX(gpv.release_date) as latest_version_date
    FROM metadata.global_products gp
    LEFT JOIN metadata.global_product_versions gpv ON gpv.global_product_id = gp.id
    GROUP BY gp.id
),
total_tenants AS (
    SELECT COUNT(DISTINCT tenant_id) as total FROM metadata.sync_batches
)
SELECT 
    ps.global_product_id,
    ps.product_name,
    ps.vendor_name,
    ps.global_vendor_id,
    ps.tenant_count,
    ROUND((ps.tenant_count::DECIMAL / tt.total) * 100, 2) as adoption_rate,
    ps.avg_confidence,
    COALESCE(us.total_licenses, 0) as total_licenses,
    COALESCE(us.total_cost, 0) as total_cost,
    COALESCE(us.average_cost, 0) as average_cost,
    COALESCE(vs.version_count, 0) as version_count,
    vs.latest_version_date,
    CASE 
        WHEN ps.tenant_count > tt.total * 0.7 THEN 'growing'
        WHEN ps.tenant_count > tt.total * 0.3 THEN 'stable'
        ELSE 'declining'
    END as adoption_trend,
    ps.last_updated
FROM product_stats ps
CROSS JOIN total_tenants tt
LEFT JOIN usage_stats us ON ps.global_product_id = us.global_product_id
LEFT JOIN version_stats vs ON ps.global_product_id = vs.global_product_id;

-- Geographic Distribution View
CREATE OR REPLACE VIEW metadata.geographic_distribution AS
WITH geographic_stats AS (
    SELECT 
        COALESCE(gv.country, 'Unknown') as country,
        COALESCE(gv.state, 'Unknown') as region,
        COUNT(DISTINCT gv.id) as vendor_count,
        COUNT(DISTINCT gp.id) as product_count,
        COUNT(DISTINCT tvs.tenant_vendor_id) as tenant_vendor_count
    FROM metadata.global_vendors gv
    LEFT JOIN metadata.global_products gp ON gp.global_vendor_id = gv.id
    LEFT JOIN tenant_0000000000000001.tenant_vendor_sync tvs ON gv.id = tvs.global_vendor_id
    WHERE tvs.sync_status = 'synced' OR tvs.sync_status IS NULL
    GROUP BY gv.country, gv.state
),
spend_stats AS (
    SELECT 
        COALESCE(gv.country, 'Unknown') as country,
        COALESCE(gv.state, 'Unknown') as region,
        SUM(r.cost) as total_spend,
        AVG(r.cost) as average_spend
    FROM metadata.global_vendors gv
    JOIN tenant_0000000000000001.tenant_vendor_sync tvs ON gv.id = tvs.global_vendor_id
    JOIN tenant_0000000000000001.renewals r ON r.vendor_id = tvs.tenant_vendor_id
    WHERE tvs.sync_status = 'synced'
    GROUP BY gv.country, gv.state
),
total_spend AS (
    SELECT SUM(total_spend) as total FROM spend_stats
)
SELECT 
    gs.country,
    gs.region,
    gs.vendor_count,
    gs.product_count,
    gs.tenant_vendor_count,
    COALESCE(ss.total_spend, 0) as total_spend,
    COALESCE(ss.average_spend, 0) as average_spend,
    ROUND((COALESCE(ss.total_spend, 0) / NULLIF(ts.total, 0)) * 100, 2) as market_share_percent
FROM geographic_stats gs
LEFT JOIN spend_stats ss ON gs.country = ss.country AND gs.region = ss.region
CROSS JOIN total_spend ts
ORDER BY ss.total_spend DESC NULLS LAST;

-- Category Trends View
CREATE OR REPLACE VIEW metadata.category_trends AS
WITH category_stats AS (
    SELECT 
        COALESCE(tp.category, 'Uncategorized') as category,
        COUNT(DISTINCT gp.id) as product_count,
        COUNT(DISTINCT gv.id) as vendor_count,
        COUNT(DISTINCT tps.tenant_product_id) as adoption_count,
        AVG(gp.confidence) as avg_confidence
    FROM tenant_0000000000000001.tenant_products tp
    LEFT JOIN tenant_0000000000000001.tenant_product_sync tps ON tp.id = tps.tenant_product_id
    LEFT JOIN metadata.global_products gp ON gp.id = tps.global_product_id
    LEFT JOIN metadata.global_vendors gv ON gv.id = gp.global_vendor_id
    WHERE tps.sync_status = 'synced' OR tps.sync_status IS NULL
    GROUP BY tp.category
),
spend_stats AS (
    SELECT 
        COALESCE(tp.category, 'Uncategorized') as category,
        SUM(r.cost) as total_spend,
        AVG(r.cost) as average_spend,
        COUNT(r.id) as renewal_count
    FROM tenant_0000000000000001.tenant_products tp
    JOIN tenant_0000000000000001.renewals r ON r.product_id = tp.id
    GROUP BY tp.category
),
growth_calculation AS (
    SELECT 
        category,
        total_spend,
        LAG(total_spend) OVER (PARTITION BY category ORDER BY NOW()) as previous_spend
    FROM spend_stats
)
SELECT 
    cs.category,
    cs.product_count,
    cs.vendor_count,
    cs.adoption_count,
    cs.avg_confidence,
    COALESCE(ss.total_spend, 0) as total_spend,
    COALESCE(ss.average_spend, 0) as average_spend,
    COALESCE(ss.renewal_count, 0) as renewal_count,
    CASE 
        WHEN ss.total_spend > 100000 THEN 'increasing'
        WHEN ss.total_spend > 50000 THEN 'stable'
        ELSE 'decreasing'
    END as spend_trend,
    CASE 
        WHEN cs.adoption_count > 10 THEN 'high'
        WHEN cs.adoption_count > 5 THEN 'medium'
        ELSE 'low'
    END as adoption_level
FROM category_stats cs
LEFT JOIN spend_stats ss ON cs.category = ss.category
ORDER BY ss.total_spend DESC NULLS LAST;

-- Sync Health Summary View
CREATE OR REPLACE VIEW metadata.sync_health_summary AS
WITH batch_stats AS (
    SELECT 
        entity_type,
        COUNT(*) as total_batches,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_batches,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_batches,
        COUNT(CASE WHEN status IN ('pending', 'processing') THEN 1 END) as active_batches,
        AVG(EXTRACT(EPOCH FROM (completed_at - created_at))) as avg_processing_seconds,
        SUM(total_records) as total_records,
        SUM(matched_records) as matched_records,
        SUM(conflict_records) as conflict_records
    FROM metadata.sync_batches
    WHERE created_at >= NOW() - INTERVAL '24 hours'
    GROUP BY entity_type
),
conflict_stats AS (
    SELECT 
        entity_type,
        COUNT(*) as pending_conflicts
    FROM metadata.sync_conflicts
    WHERE status = 'pending'
    GROUP BY entity_type
),
job_stats AS (
    SELECT 
        job_type,
        COUNT(*) as total_jobs,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_jobs,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_jobs,
        COUNT(CASE WHEN status IN ('pending', 'processing', 'retrying') THEN 1 END) as active_jobs
    FROM metadata.sync_jobs
    WHERE created_at >= NOW() - INTERVAL '24 hours'
    GROUP BY job_type
)
SELECT 
    bs.entity_type,
    bs.total_batches,
    bs.completed_batches,
    bs.failed_batches,
    bs.active_batches,
    ROUND((bs.completed_batches::DECIMAL / NULLIF(bs.total_batches, 0)) * 100, 2) as success_rate_percent,
    bs.avg_processing_seconds,
    bs.total_records,
    bs.matched_records,
    bs.conflict_records,
    ROUND((bs.matched_records::DECIMAL / NULLIF(bs.total_records, 0)) * 100, 2) as match_rate_percent,
    ROUND((bs.conflict_records::DECIMAL / NULLIF(bs.total_records, 0)) * 100, 2) as conflict_rate_percent,
    COALESCE(cs.pending_conflicts, 0) as pending_conflicts,
    COALESCE(js.active_jobs, 0) as active_jobs,
    CASE 
        WHEN bs.failed_batches > bs.completed_batches * 0.1 THEN 'critical'
        WHEN bs.failed_batches > bs.completed_batches * 0.05 THEN 'warning'
        ELSE 'healthy'
    END as health_status
FROM batch_stats bs
LEFT JOIN conflict_stats cs ON bs.entity_type = cs.entity_type
LEFT JOIN job_stats js ON bs.entity_type = js.job_type;

-- Add comments for documentation
COMMENT ON VIEW metadata.vendor_analytics IS 'Cross-tenant vendor analysis with spend and risk metrics';
COMMENT ON VIEW metadata.product_portfolio_analytics IS 'Product adoption and usage analysis across tenants';
COMMENT ON VIEW metadata.geographic_distribution IS 'Geographic distribution of vendors and spend';
COMMENT ON VIEW metadata.category_trends IS 'Product category trends and adoption patterns';
COMMENT ON VIEW metadata.sync_health_summary IS 'Sync system health and performance metrics';

-- Grant permissions
GRANT SELECT ON metadata.vendor_analytics TO renewtrack_app;
GRANT SELECT ON metadata.product_portfolio_analytics TO renewtrack_app;
GRANT SELECT ON metadata.geographic_distribution TO renewtrack_app;
GRANT SELECT ON metadata.category_trends TO renewtrack_app;
GRANT SELECT ON metadata.sync_health_summary TO renewtrack_app;
