/**
 * Renewal Types API Endpoint
 * 
 * Provides access to renewal type metadata
 * GET /api/metadata/renewal-types - Returns active renewal types
 */

import { NextRequest } from 'next/server';
import { requireAuth } from '@/lib/auth-middleware';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  ApiErrorCode, 
  HttpStatus,
  withErrorHandling 
} from '@/lib/api-response';
import { executeQuery } from '@/lib/database';
import { RenewalType } from '../route';

// GET /api/metadata/renewal-types - Get active renewal types
export const GET = withErrorHandling(async (request: NextRequest) => {
  // Verify authentication
  const authResult = await requireAuth();
  if (!authResult.success) {
    return authResult.response!;
  }

  try {
    // Query renewal types from metadata schema
    const result = await executeQuery<RenewalType>(
      `SELECT 
        RenewalTypeID,
        RenewalTypeName,
        Active,
        DisplayOrder
      FROM metadata.RenewalTypes 
      WHERE Active = true 
      ORDER BY DisplayOrder ASC, RenewalTypeName ASC`,
      []
    );

    if (!result.success) {
      console.error('Failed to fetch renewal types:', result.error);
      return createErrorResponse(
        'Failed to fetch renewal types',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    // Log successful fetch
    console.log(`Renewal types fetched successfully: ${result.data?.length || 0} records`);

    return createSuccessResponse(
      result.data || [],
      'Renewal types retrieved successfully'
    );

  } catch (error) {
    console.error('Error fetching renewal types:', error);
    return createErrorResponse(
      'Failed to fetch renewal types',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});
