{"version": 3, "names": ["cov_1cp5k7oyks", "actualCoverage", "Inter", "configureAmplify", "Providers", "PerformanceMonitor", "s", "inter", "subsets", "display", "preload", "metadata", "title", "description", "keywords", "authors", "name", "viewport", "themeColor", "RootLayout", "children", "f", "__jsx", "lang", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "rel", "href", "crossOrigin", "as", "className"], "sources": ["layout.tsx"], "sourcesContent": ["\r\nimport { Inter } from 'next/font/google'\r\nimport '../styles/globals.css'\r\nimport { configureAmplify } from '../lib/amplify-config'\r\nimport { Providers } from '@/components/providers'\r\nimport PerformanceMonitor from '@/components/common/PerformanceMonitor'\r\n\r\n// Configure Amplify at the application root\r\nconfigureAmplify()\r\n\r\n// Optimize font loading with performance settings\r\nconst inter = Inter({\r\n  subsets: ['latin'],\r\n  display: 'swap', // Improve font loading performance\r\n  preload: true,\r\n})\r\n\r\nexport const metadata = {\r\n  title: 'RenewTrack',\r\n  description: 'Track and manage your renewals',\r\n  // Performance and SEO optimizations\r\n  keywords: 'renewals, software, tracking, management',\r\n  authors: [{ name: 'RenewTrack Team' }],\r\n  viewport: 'width=device-width, initial-scale=1',\r\n  themeColor: '#000000',\r\n}\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode\r\n}) {\r\n  return (\r\n    <html lang=\"en\">\r\n      <head>\r\n        {/* DNS prefetch for performance */}\r\n        <link rel=\"dns-prefetch\" href=\"//fonts.googleapis.com\" />\r\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"anonymous\" />\r\n\r\n        {/* Preload critical resources */}\r\n        <link rel=\"preload\" href=\"/api/auth/session\" as=\"fetch\" crossOrigin=\"anonymous\" />\r\n      </head>\r\n      <body className={inter.className}>\r\n        <Providers>\r\n          {children}\r\n          {/* Performance monitor for development */}\r\n          <PerformanceMonitor />\r\n        </Providers>\r\n      </body>\r\n    </html>\r\n  )\r\n}\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAdZ,SAASE,KAAK,QAAQ,kBAAkB;AACxC,OAAO,uBAAuB;AAC9B,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,SAAS,QAAQ,wBAAwB;AAClD,OAAOC,kBAAkB,MAAM,wCAAwC;;AAEvE;AAAA;AAAAL,cAAA,GAAAM,CAAA;AACAH,gBAAgB,CAAC,CAAC;;AAElB;AACA,MAAMI,KAAK;AAAA;AAAA,CAAAP,cAAA,GAAAM,CAAA,OAAGJ,KAAK,CAAC;EAClBM,OAAO,EAAE,CAAC,OAAO,CAAC;EAClBC,OAAO,EAAE,MAAM;EAAE;EACjBC,OAAO,EAAE;AACX,CAAC,CAAC;AAEF,OAAO,MAAMC,QAAQ;AAAA;AAAA,CAAAX,cAAA,GAAAM,CAAA,OAAG;EACtBM,KAAK,EAAE,YAAY;EACnBC,WAAW,EAAE,gCAAgC;EAC7C;EACAC,QAAQ,EAAE,0CAA0C;EACpDC,OAAO,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAkB,CAAC,CAAC;EACtCC,QAAQ,EAAE,qCAAqC;EAC/CC,UAAU,EAAE;AACd,CAAC;AAED,eAAe,SAASC,UAAUA,CAAC;EACjCC;AAGF,CAAC,EAAE;EAAA;EAAApB,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAM,CAAA;EACD,OACE,0BAAAgB,KAAA;EAAA;EAAA;EAAA;EAAA;IAAMC,IAAI,EAAC,IAAI;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA;EACb;EAAAP,KAAA;EAAA;EAAA;EAAA;EAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA;EAEE;EAAAP,KAAA;EAAA;EAAA;EAAA;EAAA;IAAMQ,GAAG,EAAC,cAAc;IAACC,IAAI,EAAC,wBAAwB;IAAAP,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC;EACzD;EAAAP,KAAA;EAAA;EAAA;EAAA;EAAA;IAAMQ,GAAG,EAAC,YAAY;IAACC,IAAI,EAAC,2BAA2B;IAACC,WAAW,EAAC,WAAW;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC;EAGlF;EAAAP,KAAA;EAAA;EAAA;EAAA;EAAA;IAAMQ,GAAG,EAAC,SAAS;IAACC,IAAI,EAAC,mBAAmB;IAACE,EAAE,EAAC,OAAO;IAACD,WAAW,EAAC,WAAW;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAC7E,CAAC;EACP;EAAAP,KAAA;EAAA;EAAA;EAAA;EAAA;IAAMY,SAAS,EAAE3B,KAAK,CAAC2B,SAAU;IAAAV,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA;EAC/B;EAAAP,KAAA,CAAClB,SAAS;EAAA;EAAA;IAAAoB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACPT,QAAQ;EAET;EAAAE,KAAA,CAACjB,kBAAkB;EAAA;EAAA;IAAAmB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACZ,CACP,CACF,CAAC;AAEX", "ignoreList": []}