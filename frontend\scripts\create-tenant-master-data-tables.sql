-- Create Tenant-Specific Master Data Tables
-- This implements tenant-specific tables that can sync with global master data

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum types for tenant schema (reuse metadata types if they exist)
DO $$ BEGIN
    CREATE TYPE "tenant_0000000000000001".sync_status AS ENUM ('pending', 'matched', 'manual_review', 'rejected');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create tenant_vendors table
CREATE TABLE IF NOT EXISTS "tenant_0000000000000001".tenant_vendors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VA<PERSON>HA<PERSON>(255) NOT NULL, -- As entered by user
    display_name VA<PERSON>HA<PERSON>(255), -- User-friendly name
    contact_email VARCHAR(255),
    phone VARCHAR(50),
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100),
    tax_id VARCHAR(100), -- As provided by user
    website VARCHAR(255),
    notes TEXT, -- Tenant-specific notes
    custom_fields JSONB DEFAULT '{}', -- Tenant-specific data
    
    -- Synchronization fields
    global_vendor_id UUID, -- Foreign key to metadata.global_vendors(id)
    sync_status "tenant_0000000000000001".sync_status DEFAULT 'pending',
    sync_confidence DECIMAL(5,4) DEFAULT 0.0000 CHECK (sync_confidence >= 0 AND sync_confidence <= 1),
    last_sync_attempt TIMESTAMP,
    
    -- Audit fields
    created_by UUID, -- User reference (would link to users table)
    updated_by UUID, -- User reference (would link to users table)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT tenant_vendors_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT tenant_vendors_email_format CHECK (
        contact_email IS NULL OR contact_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'
    )
);

-- Create tenant_products table
CREATE TABLE IF NOT EXISTS "tenant_0000000000000001".tenant_products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    vendor_id UUID NOT NULL REFERENCES "tenant_0000000000000001".tenant_vendors(id),
    name VARCHAR(255) NOT NULL, -- As entered by user
    description TEXT,
    category VARCHAR(100), -- Tenant-specific category
    sku VARCHAR(100), -- Tenant's internal SKU
    barcode VARCHAR(50), -- If applicable
    unit_of_measure VARCHAR(50),
    custom_fields JSONB DEFAULT '{}',
    
    -- Synchronization fields
    global_product_id UUID, -- Foreign key to metadata.global_products(id)
    sync_status "tenant_0000000000000001".sync_status DEFAULT 'pending',
    sync_confidence DECIMAL(5,4) DEFAULT 0.0000 CHECK (sync_confidence >= 0 AND sync_confidence <= 1),
    last_sync_attempt TIMESTAMP,
    
    -- Audit fields
    created_by UUID, -- User reference
    updated_by UUID, -- User reference
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT tenant_products_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT tenant_products_sku_unique UNIQUE (sku) WHERE sku IS NOT NULL
);

-- Create tenant_product_versions table
CREATE TABLE IF NOT EXISTS "tenant_0000000000000001".tenant_product_versions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES "tenant_0000000000000001".tenant_products(id),
    version VARCHAR(50) NOT NULL, -- As entered by user
    release_date DATE,
    notes TEXT,
    is_current BOOLEAN DEFAULT false, -- Tenant's current version flag
    custom_fields JSONB DEFAULT '{}',
    
    -- Synchronization fields
    global_product_version_id UUID, -- Foreign key to metadata.global_product_versions(id)
    sync_status "tenant_0000000000000001".sync_status DEFAULT 'pending',
    sync_confidence DECIMAL(5,4) DEFAULT 0.0000 CHECK (sync_confidence >= 0 AND sync_confidence <= 1),
    last_sync_attempt TIMESTAMP,
    
    -- Audit fields
    created_by UUID, -- User reference
    updated_by UUID, -- User reference
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT tenant_product_versions_version_not_empty CHECK (LENGTH(TRIM(version)) > 0),
    CONSTRAINT tenant_product_versions_product_version_unique UNIQUE (product_id, version)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_tenant_vendors_name ON "tenant_0000000000000001".tenant_vendors(name);
CREATE INDEX IF NOT EXISTS idx_tenant_vendors_display_name ON "tenant_0000000000000001".tenant_vendors(display_name);
CREATE INDEX IF NOT EXISTS idx_tenant_vendors_global_id ON "tenant_0000000000000001".tenant_vendors(global_vendor_id);
CREATE INDEX IF NOT EXISTS idx_tenant_vendors_sync_status ON "tenant_0000000000000001".tenant_vendors(sync_status);
CREATE INDEX IF NOT EXISTS idx_tenant_vendors_sync_confidence ON "tenant_0000000000000001".tenant_vendors(sync_confidence DESC);
CREATE INDEX IF NOT EXISTS idx_tenant_vendors_created_at ON "tenant_0000000000000001".tenant_vendors(created_at);

CREATE INDEX IF NOT EXISTS idx_tenant_products_vendor_id ON "tenant_0000000000000001".tenant_products(vendor_id);
CREATE INDEX IF NOT EXISTS idx_tenant_products_name ON "tenant_0000000000000001".tenant_products(name);
CREATE INDEX IF NOT EXISTS idx_tenant_products_category ON "tenant_0000000000000001".tenant_products(category);
CREATE INDEX IF NOT EXISTS idx_tenant_products_sku ON "tenant_0000000000000001".tenant_products(sku);
CREATE INDEX IF NOT EXISTS idx_tenant_products_global_id ON "tenant_0000000000000001".tenant_products(global_product_id);
CREATE INDEX IF NOT EXISTS idx_tenant_products_sync_status ON "tenant_0000000000000001".tenant_products(sync_status);

CREATE INDEX IF NOT EXISTS idx_tenant_product_versions_product_id ON "tenant_0000000000000001".tenant_product_versions(product_id);
CREATE INDEX IF NOT EXISTS idx_tenant_product_versions_version ON "tenant_0000000000000001".tenant_product_versions(version);
CREATE INDEX IF NOT EXISTS idx_tenant_product_versions_is_current ON "tenant_0000000000000001".tenant_product_versions(is_current);
CREATE INDEX IF NOT EXISTS idx_tenant_product_versions_global_id ON "tenant_0000000000000001".tenant_product_versions(global_product_version_id);
CREATE INDEX IF NOT EXISTS idx_tenant_product_versions_sync_status ON "tenant_0000000000000001".tenant_product_versions(sync_status);

-- Create update triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION "tenant_0000000000000001".update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_tenant_vendors_updated_at
    BEFORE UPDATE ON "tenant_0000000000000001".tenant_vendors
    FOR EACH ROW
    EXECUTE FUNCTION "tenant_0000000000000001".update_updated_at_column();

CREATE TRIGGER update_tenant_products_updated_at
    BEFORE UPDATE ON "tenant_0000000000000001".tenant_products
    FOR EACH ROW
    EXECUTE FUNCTION "tenant_0000000000000001".update_updated_at_column();

CREATE TRIGGER update_tenant_product_versions_updated_at
    BEFORE UPDATE ON "tenant_0000000000000001".tenant_product_versions
    FOR EACH ROW
    EXECUTE FUNCTION "tenant_0000000000000001".update_updated_at_column();
