/**
 * Dashboard Data Hook Tests
 * 
 * Tests for the useDashboardData hook including data fetching,
 * caching, error handling, and performance optimizations
 */

import { renderHook, waitFor } from '@testing-library/react'
import { jest } from '@jest/globals'
import { useDashboardData } from '@/hooks/useDashboardData'
import { testUtils } from '../utils/test-utils'

// Mock the tenant context
const mockUseTenant = jest.fn()
jest.mock('@/contexts/AppContext', () => ({
  useTenant: () => mockUseTenant(),
}))

// Mock the cache
const mockApiCache = {
  get: jest.fn(),
  set: jest.fn(),
  has: jest.fn(),
  delete: jest.fn(),
  clear: jest.fn(),
}

jest.mock('@/lib/cache', () => ({
  apiCache: mockApiCache,
  cacheUtils: {
    createKey: jest.fn((prefix, id) => `${prefix}:${id}`),
  },
}))

// Mock performance utils
jest.mock('@/lib/performance', () => ({
  useDebounce: jest.fn((value) => value),
  performanceUtils: {
    mark: jest.fn(),
    measure: jest.fn(),
  },
}))

describe('useDashboardData Hook', () => {
  const mockTenant = testUtils.generateTestData.tenant()
  const mockStats = testUtils.generateTestData.dashboardStats()
  const mockRenewals = [testUtils.generateTestData.renewal()]

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Reset cache mocks
    mockApiCache.get.mockReturnValue(null)
    mockApiCache.set.mockImplementation(() => {})
    
    // Mock successful tenant
    mockUseTenant.mockReturnValue({
      tenant: mockTenant,
      loading: false,
      error: null,
    })

    // Mock successful fetch responses
    global.fetch = jest.fn()
      .mockResolvedValueOnce(testUtils.mockApiSuccess(mockStats))
      .mockResolvedValueOnce(testUtils.mockApiSuccess(mockRenewals))
  })

  describe('Initial State', () => {
    it('should return initial state correctly', () => {
      const { result } = renderHook(() => useDashboardData())

      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe(null)
      expect(result.current.data).toEqual({
        stats: {
          totalRenewals: 0,
          renewalsDue: 0,
          vendors: 0,
          annualSpend: '$0',
        },
        recentRenewals: [],
        upcomingRenewals: [],
      })
    })

    it('should handle loading tenant state', () => {
      mockUseTenant.mockReturnValue({
        tenant: null,
        loading: true,
        error: null,
      })

      const { result } = renderHook(() => useDashboardData())

      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe(null)
    })

    it('should handle tenant error state', () => {
      const tenantError = 'Failed to load tenant'
      mockUseTenant.mockReturnValue({
        tenant: null,
        loading: false,
        error: tenantError,
      })

      const { result } = renderHook(() => useDashboardData())

      expect(result.current.error).toBe(tenantError)
    })
  })

  describe('Data Fetching', () => {
    it('should fetch dashboard data successfully', async () => {
      const { result } = renderHook(() => useDashboardData())

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.data.stats).toEqual(mockStats)
      expect(result.current.data.recentRenewals).toEqual(mockRenewals)
      expect(result.current.error).toBe(null)
    })

    it('should handle API errors gracefully', async () => {
      const errorMessage = 'API Error'
      global.fetch = jest.fn()
        .mockResolvedValueOnce(testUtils.mockApiError(errorMessage))

      const { result } = renderHook(() => useDashboardData())

      await waitFor(() => {
        expect(result.current.error).toBe(errorMessage)
      })

      expect(result.current.isLoading).toBe(false)
    })

    it('should handle network errors', async () => {
      global.fetch = jest.fn()
        .mockRejectedValueOnce(new Error('Network error'))

      const { result } = renderHook(() => useDashboardData())

      await waitFor(() => {
        expect(result.current.error).toContain('Network error')
      })
    })

    it('should fetch data when tenant changes', async () => {
      const { result, rerender } = renderHook(() => useDashboardData())

      // Change tenant
      const newTenant = testUtils.generateTestData.tenant({
        tenantId: 'new-tenant-id',
      })
      
      mockUseTenant.mockReturnValue({
        tenant: newTenant,
        loading: false,
        error: null,
      })

      rerender()

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          '/api/dashboard/stats',
          expect.any(Object)
        )
      })
    })
  })

  describe('Caching', () => {
    it('should use cached data when available', async () => {
      // Mock cache hit
      mockApiCache.get
        .mockReturnValueOnce(mockStats)
        .mockReturnValueOnce(mockRenewals)

      const { result } = renderHook(() => useDashboardData())

      await waitFor(() => {
        expect(result.current.data.stats).toEqual(mockStats)
      })

      // Should not make API calls when cache hits
      expect(global.fetch).not.toHaveBeenCalled()
    })

    it('should cache API responses', async () => {
      const { result } = renderHook(() => useDashboardData())

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      // Should have cached the responses
      expect(mockApiCache.set).toHaveBeenCalledWith(
        `dashboard-stats:${mockTenant.tenantId}`,
        mockStats,
        expect.any(Number),
        expect.any(Array)
      )
    })

    it('should handle cache misses correctly', async () => {
      // Mock cache miss
      mockApiCache.get.mockReturnValue(null)

      const { result } = renderHook(() => useDashboardData())

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      // Should make API calls when cache misses
      expect(global.fetch).toHaveBeenCalled()
    })
  })

  describe('Performance Optimizations', () => {
    it('should debounce tenant changes', () => {
      const { useDebounce } = require('@/lib/performance')
      
      renderHook(() => useDashboardData())

      expect(useDebounce).toHaveBeenCalledWith(mockTenant, 300)
    })

    it('should mark performance timing', async () => {
      const { performanceUtils } = require('@/lib/performance')
      
      const { result } = renderHook(() => useDashboardData())

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(performanceUtils.mark).toHaveBeenCalledWith('fetchStats')
      expect(performanceUtils.measure).toHaveBeenCalledWith('fetchStats')
    })

    it('should abort previous requests', async () => {
      const abortSpy = jest.fn()
      global.AbortController = jest.fn(() => ({
        abort: abortSpy,
        signal: {},
      })) as any

      const { result, rerender } = renderHook(() => useDashboardData())

      // Trigger a new request
      mockUseTenant.mockReturnValue({
        tenant: { ...mockTenant, tenantId: 'new-tenant' },
        loading: false,
        error: null,
      })

      rerender()

      // Should abort previous request
      expect(abortSpy).toHaveBeenCalled()
    })
  })

  describe('Refetch Functionality', () => {
    it('should provide refetch function', () => {
      const { result } = renderHook(() => useDashboardData())

      expect(typeof result.current.refetch).toBe('function')
    })

    it('should refetch data when refetch is called', async () => {
      const { result } = renderHook(() => useDashboardData())

      // Clear previous calls
      jest.clearAllMocks()

      // Call refetch
      await result.current.refetch()

      expect(global.fetch).toHaveBeenCalled()
    })

    it('should handle refetch errors', async () => {
      global.fetch = jest.fn()
        .mockRejectedValueOnce(new Error('Refetch error'))

      const { result } = renderHook(() => useDashboardData())

      await expect(result.current.refetch()).rejects.toThrow('Refetch error')
    })
  })

  describe('Error Recovery', () => {
    it('should recover from errors on retry', async () => {
      // First call fails
      global.fetch = jest.fn()
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce(testUtils.mockApiSuccess(mockStats))

      const { result } = renderHook(() => useDashboardData())

      await waitFor(() => {
        expect(result.current.error).toContain('Network error')
      })

      // Retry should succeed
      await result.current.refetch()

      await waitFor(() => {
        expect(result.current.error).toBe(null)
        expect(result.current.data.stats).toEqual(mockStats)
      })
    })

    it('should clear errors on successful fetch', async () => {
      const { result } = renderHook(() => useDashboardData())

      // Simulate error state
      await waitFor(() => {
        if (result.current.error) {
          expect(result.current.error).toBeTruthy()
        }
      })

      // Successful refetch should clear error
      global.fetch = jest.fn()
        .mockResolvedValueOnce(testUtils.mockApiSuccess(mockStats))

      await result.current.refetch()

      await waitFor(() => {
        expect(result.current.error).toBe(null)
      })
    })
  })

  describe('Memory Management', () => {
    it('should cleanup on unmount', () => {
      const { unmount } = renderHook(() => useDashboardData())

      // Should not throw on unmount
      expect(() => unmount()).not.toThrow()
    })

    it('should handle component unmount during fetch', async () => {
      const { result, unmount } = renderHook(() => useDashboardData())

      // Start a fetch operation
      const fetchPromise = result.current.refetch()

      // Unmount before fetch completes
      unmount()

      // Should not throw
      await expect(fetchPromise).resolves.not.toThrow()
    })
  })
})
