/**
 * Renewals Layout
 *
 * Layout wrapper for renewals pages with sidebar navigation
 */

'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AppContext'
import Sidebar from '@/components/layout/Sidebar'

interface RenewalsLayoutProps {
  children: React.ReactNode
}

export default function RenewalsLayout({ children }: RenewalsLayoutProps) {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [isRedirecting, setIsRedirecting] = useState(false)
  const [hasInitialized, setHasInitialized] = useState(false)

  // Track initialization
  useEffect(() => {
    if (!isLoading) {
      setHasInitialized(true)
    }
  }, [isLoading])

  // Handle authentication redirects
  useEffect(() => {
    // Only redirect if authentication check is complete and user is not authenticated
    if (!isLoading && !isAuthenticated && !isRedirecting && hasInitialized) {
      setIsRedirecting(true)
      console.log('❌ [RENEWALS-LAYOUT] User not authenticated, redirecting to login')
      router.push('/login')
    } else if (!isLoading && isAuthenticated && hasInitialized) {
      console.log('✅ [RENEWALS-LAYOUT] User is authenticated, showing renewals')
    }
  }, [isLoading, isAuthenticated, router, isRedirecting, hasInitialized])

  // Show loading state only on initial load or when redirecting
  if ((isLoading && !hasInitialized) || isRedirecting) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin"></div>
        <p className="ml-4 text-lg">
          {isRedirecting ? 'Redirecting to login...' : 'Loading renewals...'}
        </p>
      </div>
    )
  }

  // Don't render renewals if not authenticated (but don't show loading if already initialized)
  if (!isAuthenticated && hasInitialized) {
    return null // Will redirect via useEffect
  }

  return (
    <div className="flex h-screen">
      <Sidebar />
      <main className="main-content">
        {children}
      </main>
    </div>
  )
}
