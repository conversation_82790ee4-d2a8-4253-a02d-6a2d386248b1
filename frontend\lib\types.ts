/**
 * Comprehensive Type Definitions and Type Guards
 * 
 * This module provides centralized type definitions and type guard functions
 * to ensure type safety throughout the application.
 */

// Base entity interface
export interface BaseEntity {
  id: string;
  created_at: Date;
  updated_at?: Date;
  version?: number; // For optimistic concurrency control
}

// User types
export interface User extends BaseEntity {
  email: string;
  name?: string;
  given_name?: string;
  family_name?: string;
  roles: string[];
  status: 'active' | 'inactive' | 'suspended' | 'deleted';
  last_login?: Date;
  preferences?: UserPreferences;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  displayDensity: 'comfortable' | 'compact';
  language?: string;
  timezone?: string;
  [key: string]: any;
}

// Client/Tenant types
export interface Client extends BaseEntity {
  name: string;
  domain: string;
  domains?: string[];
  status: 'active' | 'inactive' | 'suspended';
  settings: ClientSettings;
  industry_id?: string;
  tenant_id?: string;
  tenant_schema?: string;
}

export interface ClientSettings {
  schemaReady?: boolean;
  features?: {
    renewals?: boolean;
    vendors?: boolean;
    reports?: boolean;
    notifications?: boolean;
  };
  branding?: {
    logo?: string;
    primaryColor?: string;
    secondaryColor?: string;
  };
  [key: string]: any;
}

export interface TenantContext {
  clientId: string;
  clientName: string;
  tenantId: string;
  tenantSchema: string;
  domains: string[];
  isActive: boolean;
  settings: ClientSettings;
  createdAt: Date;
  updatedAt?: Date | null;
}

// Renewal types
export interface Renewal extends BaseEntity {
  name: string;
  vendor: string;
  vendor_id?: string;
  status: 'active' | 'inactive' | 'pending' | 'expired';
  due_date?: Date;
  annual_cost?: number;
  description?: string;
  category?: string;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  auto_renew?: boolean;
  notification_days?: number[];
}

// Vendor types
export interface Vendor extends BaseEntity {
  name: string;
  contact_email?: string;
  contact_phone?: string;
  website?: string;
  status: 'active' | 'inactive';
  address?: Address;
  notes?: string;
}

export interface Address {
  street?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errorCode?: string;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Authentication types
export interface AuthSession {
  isAuth: boolean;
  userId: string;
  email: string;
  roles: string[];
}

export interface CognitoJwtPayload {
  sub: string;
  email: string;
  email_verified?: boolean;
  name?: string;
  given_name?: string;
  family_name?: string;
  'cognito:groups'?: string[];
  'cognito:username'?: string;
  token_use: 'id' | 'access';
  auth_time: number;
  iat: number;
  exp: number;
  aud: string;
  iss: string;
}

// Database types
export interface DbResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errorCode?: string;
  rowCount?: number;
}

export interface QueryOptions {
  timeout?: number;
  retries?: number;
  schema?: string;
}

// Form types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'select' | 'textarea' | 'checkbox';
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    custom?: (value: any) => string | null;
  };
}

export interface FormState<T = any> {
  values: T;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isSubmitting: boolean;
  isValid: boolean;
}

// Component prop types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  'data-testid'?: string;
}

export interface LoadingState {
  isLoading: boolean;
  error?: string | null;
}

// Type Guards
export function isUser(obj: any): obj is User {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.email === 'string' &&
    Array.isArray(obj.roles) &&
    ['active', 'inactive', 'suspended', 'deleted'].includes(obj.status)
  );
}

export function isClient(obj: any): obj is Client {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.name === 'string' &&
    typeof obj.domain === 'string' &&
    ['active', 'inactive', 'suspended'].includes(obj.status)
  );
}

export function isTenantContext(obj: any): obj is TenantContext {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.clientId === 'string' &&
    typeof obj.clientName === 'string' &&
    typeof obj.tenantId === 'string' &&
    typeof obj.tenantSchema === 'string' &&
    Array.isArray(obj.domains) &&
    typeof obj.isActive === 'boolean'
  );
}

export function isRenewal(obj: any): obj is Renewal {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.name === 'string' &&
    typeof obj.vendor === 'string' &&
    ['active', 'inactive', 'pending', 'expired'].includes(obj.status)
  );
}

export function isApiResponse<T>(obj: any): obj is ApiResponse<T> {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.success === 'boolean' &&
    typeof obj.timestamp === 'string'
  );
}

export function isPaginatedResponse<T>(obj: any): obj is PaginatedResponse<T> {
  return (
    isApiResponse(obj) &&
    obj.pagination &&
    typeof obj.pagination === 'object' &&
    typeof obj.pagination.page === 'number' &&
    typeof obj.pagination.limit === 'number' &&
    typeof obj.pagination.totalCount === 'number'
  );
}

export function isAuthSession(obj: any): obj is AuthSession {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.isAuth === 'boolean' &&
    typeof obj.userId === 'string' &&
    typeof obj.email === 'string' &&
    Array.isArray(obj.roles)
  );
}

export function isCognitoJwtPayload(obj: any): obj is CognitoJwtPayload {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.sub === 'string' &&
    typeof obj.email === 'string' &&
    typeof obj.exp === 'number' &&
    typeof obj.iat === 'number' &&
    ['id', 'access'].includes(obj.token_use)
  );
}

// Utility types
export type Nullable<T> = T | null;
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// Event types
export interface AppEvent<T = any> {
  type: string;
  payload: T;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
}

// Error types
export interface AppError extends Error {
  code?: string;
  statusCode?: number;
  details?: any;
  timestamp: Date;
}

// Configuration types
export interface AppConfig {
  aws: {
    region: string;
    userPoolId: string;
    userPoolClientId: string;
    cognitoDomain: string;
  };
  auth: {
    redirectSignIn: string;
    redirectSignOut: string;
  };
  app: {
    environment: 'development' | 'production' | 'test';
    isDevelopment: boolean;
    isProduction: boolean;
  };
}

// Dashboard specific types
export interface DashboardStats {
  totalRenewals: number;
  renewalsDue: number;
  vendors: number;
  annualSpend: string;
}

// Export all types for easy importing
export type {
  BaseEntity,
  User,
  UserPreferences,
  Client,
  ClientSettings,
  TenantContext,
  Renewal,
  Vendor,
  Address,
  ApiResponse,
  PaginatedResponse,
  AuthSession,
  CognitoJwtPayload,
  DbResult,
  QueryOptions,
  FormField,
  FormState,
  BaseComponentProps,
  LoadingState,
  AppEvent,
  AppError,
  AppConfig,
  DashboardStats,
};
