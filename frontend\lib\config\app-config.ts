/**
 * Centralized Application Configuration
 * 
 * Single source of truth for all application configuration to eliminate redundancy
 * and improve maintainability. This includes AWS Amplify, database, and other settings.
 */

// Environment validation with fallbacks for development
const requiredEnvVars = [
  'NEXT_PUBLIC_AWS_REGION',
  'NEXT_PUBLIC_COGNITO_USER_POOL_ID',
  'NEXT_PUBLIC_COGNITO_CLIENT_ID',
  'NEXT_PUBLIC_COGNITO_DOMAIN'
] as const

// Validate required environment variables with fallbacks
function validateEnvironment() {
  const missing = requiredEnvVars.filter(envVar => !process.env[envVar])

  if (missing.length > 0 && process.env.NODE_ENV === 'production') {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
  }

  // In development, warn but don't fail
  if (missing.length > 0 && process.env.NODE_ENV !== 'production') {
    console.warn(`Warning: Missing environment variables (using fallbacks): ${missing.join(', ')}`)
  }
}

// Validate environment on module load
validateEnvironment()

// AWS Amplify Configuration with fallbacks
export const amplifyConfig = {
  region: process.env.NEXT_PUBLIC_AWS_REGION || 'ca-central-1',
  userPoolId: process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID || 'ca-central-1_uwPuGUhLc',
  userPoolClientId: process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID || '6fc4ks4poom3mqk5icavr7np1k',
  cognitoDomain: process.env.NEXT_PUBLIC_COGNITO_DOMAIN || 'auth.renewtrack.com',
  redirectSignIn: process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN || 'http://localhost:3000/callback',
  redirectSignOut: process.env.NEXT_PUBLIC_REDIRECT_SIGN_OUT || 'http://localhost:3000/signout'
} as const

// Database Configuration
export const databaseConfig = {
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || '127.0.0.1',
  database: process.env.DB_NAME || 'Renewtrack',
  password: process.env.DB_PASSWORD,
  port: parseInt(process.env.DB_PORT || '5432'),
  ssl: process.env.DB_SSL === 'true',
  // Connection pool settings
  max: parseInt(process.env.DB_POOL_MAX || '20'),
  min: parseInt(process.env.DB_POOL_MIN || '2'),
  idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'),
  connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000')
} as const

// Application Configuration
export const appConfig = {
  // Environment
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  
  // API Configuration
  api: {
    timeout: parseInt(process.env.API_TIMEOUT || '30000'),
    retries: parseInt(process.env.API_RETRIES || '3'),
    baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || ''
  },
  
  // Authentication
  auth: {
    tokenRefreshThreshold: parseInt(process.env.AUTH_TOKEN_REFRESH_THRESHOLD || '300'), // 5 minutes
    sessionTimeout: parseInt(process.env.AUTH_SESSION_TIMEOUT || '3600'), // 1 hour
    maxLoginAttempts: parseInt(process.env.AUTH_MAX_LOGIN_ATTEMPTS || '5')
  },
  
  // Features
  features: {
    enableNetworkScan: process.env.FEATURE_NETWORK_SCAN === 'true',
    enableDepartments: process.env.FEATURE_DEPARTMENTS === 'true',
    enableFinancials: process.env.FEATURE_FINANCIALS === 'true',
    enableAdvancedAnalytics: process.env.FEATURE_ADVANCED_ANALYTICS === 'true'
  },
  
  // UI Configuration
  ui: {
    defaultPageSize: parseInt(process.env.UI_DEFAULT_PAGE_SIZE || '10'),
    maxPageSize: parseInt(process.env.UI_MAX_PAGE_SIZE || '100'),
    debounceDelay: parseInt(process.env.UI_DEBOUNCE_DELAY || '300'),
    toastDuration: parseInt(process.env.UI_TOAST_DURATION || '5000')
  },
  
  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    enableConsole: process.env.LOG_ENABLE_CONSOLE !== 'false',
    enableFile: process.env.LOG_ENABLE_FILE === 'true'
  }
} as const

// Type exports for better TypeScript support
export type AmplifyConfig = typeof amplifyConfig
export type DatabaseConfig = typeof databaseConfig
export type AppConfig = typeof appConfig

// Configuration validation function
export function validateConfig(): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  // Validate AWS region format
  if (!/^[a-z]{2}-[a-z]+-\d+$/.test(amplifyConfig.region)) {
    errors.push('Invalid AWS region format')
  }
  
  // Validate User Pool ID format
  if (!/^[a-z]{2}-[a-z]+-\d+_[A-Za-z0-9]+$/.test(amplifyConfig.userPoolId)) {
    errors.push('Invalid Cognito User Pool ID format')
  }
  
  // Validate database port
  if (databaseConfig.port < 1 || databaseConfig.port > 65535) {
    errors.push('Invalid database port number')
  }
  
  // Validate URLs
  try {
    new URL(amplifyConfig.redirectSignIn)
    new URL(amplifyConfig.redirectSignOut)
  } catch {
    errors.push('Invalid redirect URLs')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Helper function to get configuration summary for logging
export function getConfigSummary() {
  return {
    environment: process.env.NODE_ENV,
    region: amplifyConfig.region,
    database: {
      host: databaseConfig.host,
      database: databaseConfig.database,
      ssl: databaseConfig.ssl
    },
    features: appConfig.features
  }
}

// Export default configuration object
export default {
  amplify: amplifyConfig,
  database: databaseConfig,
  app: appConfig,
  validate: validateConfig,
  summary: getConfigSummary
} as const
