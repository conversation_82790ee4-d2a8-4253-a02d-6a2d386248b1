e1f3feecaa6a69b1ff338c642e5a5e78
'use client';

/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\EnvDebugger.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_1qw5a7plbo() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\EnvDebugger.tsx";
  var hash = "6f59be1c5c501f8bf8b6783ebebe1757edd3fd0e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\EnvDebugger.tsx",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 44
        },
        end: {
          line: 6,
          column: 92
        }
      },
      "1": {
        start: {
          line: 9,
          column: 24
        },
        end: {
          line: 14,
          column: 3
        }
      },
      "2": {
        start: {
          line: 16,
          column: 2
        },
        end: {
          line: 25,
          column: 8
        }
      },
      "3": {
        start: {
          line: 18,
          column: 53
        },
        end: {
          line: 18,
          column: 55
        }
      },
      "4": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 23,
          column: 6
        }
      },
      "5": {
        start: {
          line: 20,
          column: 6
        },
        end: {
          line: 22,
          column: 7
        }
      },
      "6": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 21,
          column: 36
        }
      },
      "7": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 26
        }
      },
      "8": {
        start: {
          line: 27,
          column: 2
        },
        end: {
          line: 41,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "EnvDebugger",
        decl: {
          start: {
            line: 5,
            column: 24
          },
          end: {
            line: 5,
            column: 35
          }
        },
        loc: {
          start: {
            line: 5,
            column: 38
          },
          end: {
            line: 42,
            column: 1
          }
        },
        line: 5
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 16,
            column: 12
          },
          end: {
            line: 16,
            column: 13
          }
        },
        loc: {
          start: {
            line: 16,
            column: 18
          },
          end: {
            line: 25,
            column: 3
          }
        },
        line: 16
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 37
          },
          end: {
            line: 19,
            column: 38
          }
        },
        loc: {
          start: {
            line: 19,
            column: 44
          },
          end: {
            line: 23,
            column: 5
          }
        },
        line: 19
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 20,
            column: 6
          },
          end: {
            line: 22,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 20,
            column: 6
          },
          end: {
            line: 22,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 20
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    b: {
      "0": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "6f59be1c5c501f8bf8b6783ebebe1757edd3fd0e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1qw5a7plbo = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1qw5a7plbo();
import { useEffect, useState } from 'react';
export default function EnvDebugger() {
  /* istanbul ignore next */
  cov_1qw5a7plbo().f[0]++;
  const [clientEnvVars, setClientEnvVars] =
  /* istanbul ignore next */
  (cov_1qw5a7plbo().s[0]++, useState({}));

  // These are server-side environment variables (available during build/SSR)
  const serverEnvVars =
  /* istanbul ignore next */
  (cov_1qw5a7plbo().s[1]++, {
    NEXT_PUBLIC_AWS_REGION: process.env.NEXT_PUBLIC_AWS_REGION,
    NEXT_PUBLIC_AWS_USER_POOLS_ID: process.env.NEXT_PUBLIC_AWS_USER_POOLS_ID,
    NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID: process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID,
    NEXT_PUBLIC_AWS_COGNITO_DOMAIN: process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN
  });
  /* istanbul ignore next */
  cov_1qw5a7plbo().s[2]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_1qw5a7plbo().f[1]++;
    // These are client-side environment variables (available in browser)
    const vars =
    /* istanbul ignore next */
    (cov_1qw5a7plbo().s[3]++, {});
    /* istanbul ignore next */
    cov_1qw5a7plbo().s[4]++;
    Object.keys(process.env).forEach(key => {
      /* istanbul ignore next */
      cov_1qw5a7plbo().f[2]++;
      cov_1qw5a7plbo().s[5]++;
      if (key.startsWith('NEXT_PUBLIC_')) {
        /* istanbul ignore next */
        cov_1qw5a7plbo().b[0][0]++;
        cov_1qw5a7plbo().s[6]++;
        vars[key] = process.env[key];
      } else
      /* istanbul ignore next */
      {
        cov_1qw5a7plbo().b[0][1]++;
      }
    });
    /* istanbul ignore next */
    cov_1qw5a7plbo().s[7]++;
    setClientEnvVars(vars);
  }, []);
  /* istanbul ignore next */
  cov_1qw5a7plbo().s[8]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "p-4 bg-gray-100 rounded-lg my-4 max-w-2xl mx-auto text-sm",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 28,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h3",
  /* istanbul ignore next */
  {
    className: "font-bold mb-2",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 29,
      columnNumber: 7
    }
  }, "Environment Variables Debug"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h4",
  /* istanbul ignore next */
  {
    className: "font-semibold mt-2",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 31,
      columnNumber: 7
    }
  }, "Server-side Environment Variables:"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "pre",
  /* istanbul ignore next */
  {
    className: "bg-white p-3 rounded overflow-auto max-h-60",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 32,
      columnNumber: 7
    }
  }, JSON.stringify(serverEnvVars, null, 2)),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h4",
  /* istanbul ignore next */
  {
    className: "font-semibold mt-4",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 36,
      columnNumber: 7
    }
  }, "Client-side Environment Variables:"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "pre",
  /* istanbul ignore next */
  {
    className: "bg-white p-3 rounded overflow-auto max-h-60",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 37,
      columnNumber: 7
    }
  }, JSON.stringify(clientEnvVars, null, 2)));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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