/**
 * Test Utilities
 * 
 * Comprehensive testing utilities for React components and hooks
 */

import React, { ReactElement, ReactNode } from 'react'
import { render, RenderOptions, RenderResult } from '@testing-library/react'
import { jest } from '@jest/globals'

// Mock providers for testing
const MockAuthProvider = ({ children }: { children: ReactNode }) => {
  const mockAuthContext = {
    user: global.testUtils.mockUser,
    isAuthenticated: true,
    isLoading: false,
    error: null,
    signIn: jest.fn(),
    signOut: jest.fn(),
    signUp: jest.fn(),
  }

  return (
    <div data-testid="mock-auth-provider">
      {children}
    </div>
  )
}

const MockTenantProvider = ({ children }: { children: ReactNode }) => {
  const mockTenantContext = {
    tenant: global.testUtils.mockTenant,
    loading: false,
    error: null,
    refetch: jest.fn(),
  }

  return (
    <div data-testid="mock-tenant-provider">
      {children}
    </div>
  )
}

const MockAppProvider = ({ children }: { children: ReactNode }) => {
  return (
    <MockAuthProvider>
      <MockTenantProvider>
        {children}
      </MockTenantProvider>
    </MockAuthProvider>
  )
}

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  withProviders?: boolean
  initialProps?: Record<string, any>
}

export const renderWithProviders = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
): RenderResult => {
  const { withProviders = true, ...renderOptions } = options

  const Wrapper = ({ children }: { children: ReactNode }) => {
    if (withProviders) {
      return <MockAppProvider>{children}</MockAppProvider>
    }
    return <>{children}</>
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Re-export everything from testing library
export * from '@testing-library/react'
export { renderWithProviders as render }

// Custom matchers and utilities
export const testUtils = {
  // Mock API responses
  mockApiSuccess: (data: any) => ({
    ok: true,
    status: 200,
    json: jest.fn().mockResolvedValue({ success: true, data }),
  }),

  mockApiError: (error: string, status = 400) => ({
    ok: false,
    status,
    json: jest.fn().mockResolvedValue({ success: false, error }),
  }),

  // Mock fetch responses
  mockFetch: (response: any) => {
    global.fetch = jest.fn().mockResolvedValue(response)
  },

  // Mock localStorage
  mockLocalStorage: () => {
    const store: Record<string, string> = {}
    return {
      getItem: jest.fn((key: string) => store[key] || null),
      setItem: jest.fn((key: string, value: string) => {
        store[key] = value
      }),
      removeItem: jest.fn((key: string) => {
        delete store[key]
      }),
      clear: jest.fn(() => {
        Object.keys(store).forEach(key => delete store[key])
      }),
    }
  },

  // Mock console methods
  mockConsole: () => {
    const originalConsole = { ...console }
    return {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      info: jest.fn(),
      restore: () => {
        Object.assign(console, originalConsole)
      },
    }
  },

  // Wait for async operations
  waitFor: async (callback: () => void | Promise<void>, timeout = 1000) => {
    const startTime = Date.now()
    while (Date.now() - startTime < timeout) {
      try {
        await callback()
        return
      } catch (error) {
        await new Promise(resolve => setTimeout(resolve, 10))
      }
    }
    throw new Error(`Timeout after ${timeout}ms`)
  },

  // Create mock component
  createMockComponent: (name: string, props?: Record<string, any>) => {
    const MockComponent = (componentProps: any) => (
      <div data-testid={`mock-${name.toLowerCase()}`} {...props} {...componentProps}>
        {name} Mock
      </div>
    )
    MockComponent.displayName = `Mock${name}`
    return MockComponent
  },

  // Mock hook return values
  mockHook: (hookName: string, returnValue: any) => {
    const mockModule = jest.fn().mockReturnValue(returnValue)
    jest.doMock(hookName, () => mockModule)
    return mockModule
  },

  // Generate test data
  generateTestData: {
    user: (overrides = {}) => ({
      id: 'test-user-id',
      email: '<EMAIL>',
      name: 'Test User',
      tenantId: 'test-tenant-id',
      ...overrides,
    }),

    tenant: (overrides = {}) => ({
      tenantId: 'test-tenant-id',
      clientId: 'test-client-id',
      clientName: 'Test Client',
      domain: 'test.example.com',
      ...overrides,
    }),

    renewal: (overrides = {}) => ({
      id: 'test-renewal-id',
      name: 'Test Software',
      vendor: 'Test Vendor',
      renewalDate: new Date('2025-03-01'),
      cost: 1000,
      status: 'active',
      ...overrides,
    }),

    dashboardStats: (overrides = {}) => ({
      totalRenewals: 25,
      renewalsDue: 5,
      vendors: 12,
      annualSpend: '$125,000',
      ...overrides,
    }),
  },

  // Mock timers
  mockTimers: () => {
    jest.useFakeTimers()
    return {
      advanceTimersByTime: jest.advanceTimersByTime,
      runAllTimers: jest.runAllTimers,
      runOnlyPendingTimers: jest.runOnlyPendingTimers,
      restore: () => jest.useRealTimers(),
    }
  },

  // Mock dates
  mockDate: (date: string | Date) => {
    const mockDate = new Date(date)
    const originalDate = Date
    global.Date = jest.fn(() => mockDate) as any
    global.Date.now = jest.fn(() => mockDate.getTime())
    return {
      restore: () => {
        global.Date = originalDate
      },
    }
  },

  // Mock window methods
  mockWindow: (overrides: Partial<Window> = {}) => {
    const originalWindow = { ...window }
    Object.assign(window, overrides)
    return {
      restore: () => {
        Object.assign(window, originalWindow)
      },
    }
  },

  // Mock environment variables
  mockEnv: (env: Record<string, string>) => {
    const originalEnv = { ...process.env }
    Object.assign(process.env, env)
    return {
      restore: () => {
        process.env = originalEnv
      },
    }
  },
}

// Export default render function
export default renderWithProviders
