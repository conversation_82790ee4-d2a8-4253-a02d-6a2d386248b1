d686ca73539032e3505316a187179c17
/**
 * Dashboard Page - Modular and Optimized
 *
 * This page demonstrates the new modular architecture with:
 * - Separated concerns into focused components
 * - Custom hooks for data management
 * - Error boundaries for resilience
 * - Proper loading states
 * - Type safety throughout
 */

'use client';

/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\dashboard\\page.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_e9mg3q03f() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\dashboard\\page.tsx";
  var hash = "0d9491331babf5d6b8817139ba349f547e4cd653";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\dashboard\\page.tsx",
    statementMap: {
      "0": {
        start: {
          line: 45,
          column: 65
        },
        end: {
          line: 45,
          column: 76
        }
      },
      "1": {
        start: {
          line: 46,
          column: 46
        },
        end: {
          line: 46,
          column: 64
        }
      },
      "2": {
        start: {
          line: 52,
          column: 6
        },
        end: {
          line: 52,
          column: 22
        }
      },
      "3": {
        start: {
          line: 55,
          column: 2
        },
        end: {
          line: 55,
          column: 40
        }
      },
      "4": {
        start: {
          line: 57,
          column: 40
        },
        end: {
          line: 57,
          column: 52
        }
      },
      "5": {
        start: {
          line: 58,
          column: 60
        },
        end: {
          line: 58,
          column: 75
        }
      },
      "6": {
        start: {
          line: 61,
          column: 31
        },
        end: {
          line: 61,
          column: 60
        }
      },
      "7": {
        start: {
          line: 64,
          column: 23
        },
        end: {
          line: 68,
          column: 8
        }
      },
      "8": {
        start: {
          line: 65,
          column: 4
        },
        end: {
          line: 65,
          column: 25
        }
      },
      "9": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 67,
          column: 40
        }
      },
      "10": {
        start: {
          line: 70,
          column: 27
        },
        end: {
          line: 72,
          column: 8
        }
      },
      "11": {
        start: {
          line: 71,
          column: 4
        },
        end: {
          line: 71,
          column: 34
        }
      },
      "12": {
        start: {
          line: 74,
          column: 37
        },
        end: {
          line: 76,
          column: 8
        }
      },
      "13": {
        start: {
          line: 75,
          column: 4
        },
        end: {
          line: 75,
          column: 35
        }
      },
      "14": {
        start: {
          line: 78,
          column: 30
        },
        end: {
          line: 101,
          column: 33
        }
      },
      "15": {
        start: {
          line: 79,
          column: 4
        },
        end: {
          line: 100,
          column: 5
        }
      },
      "16": {
        start: {
          line: 80,
          column: 6
        },
        end: {
          line: 82,
          column: 7
        }
      },
      "17": {
        start: {
          line: 81,
          column: 8
        },
        end: {
          line: 81,
          column: 59
        }
      },
      "18": {
        start: {
          line: 85,
          column: 21
        },
        end: {
          line: 85,
          column: 80
        }
      },
      "19": {
        start: {
          line: 87,
          column: 6
        },
        end: {
          line: 89,
          column: 7
        }
      },
      "20": {
        start: {
          line: 88,
          column: 8
        },
        end: {
          line: 88,
          column: 67
        }
      },
      "21": {
        start: {
          line: 92,
          column: 6
        },
        end: {
          line: 92,
          column: 21
        }
      },
      "22": {
        start: {
          line: 95,
          column: 6
        },
        end: {
          line: 95,
          column: 67
        }
      },
      "23": {
        start: {
          line: 97,
          column: 6
        },
        end: {
          line: 97,
          column: 51
        }
      },
      "24": {
        start: {
          line: 99,
          column: 6
        },
        end: {
          line: 99,
          column: 17
        }
      },
      "25": {
        start: {
          line: 103,
          column: 29
        },
        end: {
          line: 106,
          column: 8
        }
      },
      "26": {
        start: {
          line: 105,
          column: 4
        },
        end: {
          line: 105,
          column: 44
        }
      },
      "27": {
        start: {
          line: 108,
          column: 24
        },
        end: {
          line: 114,
          column: 15
        }
      },
      "28": {
        start: {
          line: 109,
          column: 4
        },
        end: {
          line: 113,
          column: 5
        }
      },
      "29": {
        start: {
          line: 110,
          column: 6
        },
        end: {
          line: 110,
          column: 21
        }
      },
      "30": {
        start: {
          line: 112,
          column: 6
        },
        end: {
          line: 112,
          column: 49
        }
      },
      "31": {
        start: {
          line: 116,
          column: 32
        },
        end: {
          line: 119,
          column: 8
        }
      },
      "32": {
        start: {
          line: 118,
          column: 4
        },
        end: {
          line: 118,
          column: 47
        }
      },
      "33": {
        start: {
          line: 122,
          column: 2
        },
        end: {
          line: 130,
          column: 3
        }
      },
      "34": {
        start: {
          line: 123,
          column: 4
        },
        end: {
          line: 129,
          column: 5
        }
      },
      "35": {
        start: {
          line: 133,
          column: 2
        },
        end: {
          line: 149,
          column: 3
        }
      },
      "36": {
        start: {
          line: 134,
          column: 4
        },
        end: {
          line: 148,
          column: 5
        }
      },
      "37": {
        start: {
          line: 142,
          column: 27
        },
        end: {
          line: 142,
          column: 36
        }
      },
      "38": {
        start: {
          line: 151,
          column: 2
        },
        end: {
          line: 222,
          column: 3
        }
      },
      "39": {
        start: {
          line: 154,
          column: 8
        },
        end: {
          line: 154,
          column: 59
        }
      }
    },
    fnMap: {
      "0": {
        name: "DashboardPage",
        decl: {
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 37
          }
        },
        loc: {
          start: {
            line: 44,
            column: 40
          },
          end: {
            line: 223,
            column: 1
          }
        },
        line: 44
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 64,
            column: 35
          },
          end: {
            line: 64,
            column: 36
          }
        },
        loc: {
          start: {
            line: 64,
            column: 54
          },
          end: {
            line: 68,
            column: 3
          }
        },
        line: 64
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 70,
            column: 39
          },
          end: {
            line: 70,
            column: 40
          }
        },
        loc: {
          start: {
            line: 70,
            column: 45
          },
          end: {
            line: 72,
            column: 3
          }
        },
        line: 70
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 74,
            column: 49
          },
          end: {
            line: 74,
            column: 50
          }
        },
        loc: {
          start: {
            line: 74,
            column: 55
          },
          end: {
            line: 76,
            column: 3
          }
        },
        line: 74
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 78,
            column: 42
          },
          end: {
            line: 78,
            column: 43
          }
        },
        loc: {
          start: {
            line: 78,
            column: 111
          },
          end: {
            line: 101,
            column: 3
          }
        },
        line: 78
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 103,
            column: 41
          },
          end: {
            line: 103,
            column: 42
          }
        },
        loc: {
          start: {
            line: 103,
            column: 63
          },
          end: {
            line: 106,
            column: 3
          }
        },
        line: 103
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 108,
            column: 36
          },
          end: {
            line: 108,
            column: 37
          }
        },
        loc: {
          start: {
            line: 108,
            column: 48
          },
          end: {
            line: 114,
            column: 3
          }
        },
        line: 108
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 116,
            column: 44
          },
          end: {
            line: 116,
            column: 45
          }
        },
        loc: {
          start: {
            line: 116,
            column: 61
          },
          end: {
            line: 119,
            column: 3
          }
        },
        line: 116
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 142,
            column: 21
          },
          end: {
            line: 142,
            column: 22
          }
        },
        loc: {
          start: {
            line: 142,
            column: 27
          },
          end: {
            line: 142,
            column: 36
          }
        },
        line: 142
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 153,
            column: 15
          },
          end: {
            line: 153,
            column: 16
          }
        },
        loc: {
          start: {
            line: 153,
            column: 37
          },
          end: {
            line: 155,
            column: 7
          }
        },
        line: 153
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 80,
            column: 6
          },
          end: {
            line: 82,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 80,
            column: 6
          },
          end: {
            line: 82,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 80
      },
      "1": {
        loc: {
          start: {
            line: 87,
            column: 6
          },
          end: {
            line: 89,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 87,
            column: 6
          },
          end: {
            line: 89,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 87
      },
      "2": {
        loc: {
          start: {
            line: 88,
            column: 24
          },
          end: {
            line: 88,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 88,
            column: 24
          },
          end: {
            line: 88,
            column: 38
          }
        }, {
          start: {
            line: 88,
            column: 42
          },
          end: {
            line: 88,
            column: 66
          }
        }],
        line: 88
      },
      "3": {
        loc: {
          start: {
            line: 122,
            column: 2
          },
          end: {
            line: 130,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 122,
            column: 2
          },
          end: {
            line: 130,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 122
      },
      "4": {
        loc: {
          start: {
            line: 133,
            column: 2
          },
          end: {
            line: 149,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 133,
            column: 2
          },
          end: {
            line: 149,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 133
      },
      "5": {
        loc: {
          start: {
            line: 133,
            column: 6
          },
          end: {
            line: 133,
            column: 26
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 133,
            column: 6
          },
          end: {
            line: 133,
            column: 17
          }
        }, {
          start: {
            line: 133,
            column: 21
          },
          end: {
            line: 133,
            column: 26
          }
        }],
        line: 133
      },
      "6": {
        loc: {
          start: {
            line: 139,
            column: 46
          },
          end: {
            line: 139,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 139,
            column: 46
          },
          end: {
            line: 139,
            column: 57
          }
        }, {
          start: {
            line: 139,
            column: 61
          },
          end: {
            line: 139,
            column: 66
          }
        }],
        line: 139
      },
      "7": {
        loc: {
          start: {
            line: 209,
            column: 28
          },
          end: {
            line: 209,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 209,
            column: 28
          },
          end: {
            line: 209,
            column: 40
          }
        }, {
          start: {
            line: 209,
            column: 44
          },
          end: {
            line: 209,
            column: 53
          }
        }],
        line: 209
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0d9491331babf5d6b8817139ba349f547e4cd653"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_e9mg3q03f = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_e9mg3q03f();
import { useState, useCallback } from 'react';
import { useTenant } from '@/contexts/AppContext';
import { useDashboardData } from '@/hooks/useDashboardData';
import { useScanResults } from '@/hooks/useScanResults';
import { usePerformanceMonitor, useDebounce } from '@/lib/performance';

// Modular dashboard components
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import DashboardStats from '@/components/dashboard/DashboardStats';
import RecentRenewals from '@/components/dashboard/RecentRenewals';
import UpcomingRenewals from '@/components/dashboard/UpcomingRenewals';
import ScanResults from '@/components/dashboard/ScanResults';

// Modal components
import AddRenewalModal from '@/components/modals/AddRenewalModal';

// Services
import { saveRenewal } from '@/lib/services/renewalService';

// Common components
import ErrorBoundary from '@/components/common/ErrorBoundary';
import { LoadingPage } from '@/components/common/LoadingStates';
import { LazySection } from '@/components/common/LazyLoad';

// Types

export default function DashboardPage() {
  /* istanbul ignore next */
  cov_e9mg3q03f().f[0]++;
  const {
    tenant,
    loading: tenantLoading,
    error: tenantError
  } =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[0]++, useTenant());
  const {
    data,
    isLoading,
    error,
    refetch
  } =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[1]++, useDashboardData());
  const {
    results: scanResults,
    isLoading: scanLoading,
    lastScanDate,
    runScan
  } =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[2]++, useScanResults());

  // Performance monitoring
  /* istanbul ignore next */
  cov_e9mg3q03f().s[3]++;
  usePerformanceMonitor('DashboardPage');
  const [searchQuery, setSearchQuery] =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[4]++, useState(''));
  const [isAddRenewalModalOpen, setIsAddRenewalModalOpen] =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[5]++, useState(false));

  // Debounce search to prevent excessive API calls
  const debouncedSearchQuery =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[6]++, useDebounce(searchQuery, 300));

  // Memoized event handlers to prevent unnecessary re-renders
  const handleSearch =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[7]++, useCallback(query => {
    /* istanbul ignore next */
    cov_e9mg3q03f().f[1]++;
    cov_e9mg3q03f().s[8]++;
    setSearchQuery(query);
    // TODO: Implement search functionality
    /* istanbul ignore next */
    cov_e9mg3q03f().s[9]++;
    console.log('Searching for:', query);
  }, []));
  const handleAddRenewal =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[10]++, useCallback(() => {
    /* istanbul ignore next */
    cov_e9mg3q03f().f[2]++;
    cov_e9mg3q03f().s[11]++;
    setIsAddRenewalModalOpen(true);
  }, []));
  const handleCloseAddRenewalModal =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[12]++, useCallback(() => {
    /* istanbul ignore next */
    cov_e9mg3q03f().f[3]++;
    cov_e9mg3q03f().s[13]++;
    setIsAddRenewalModalOpen(false);
  }, []));
  const handleSubmitRenewal =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[14]++, useCallback(async (renewalData, alertsData) => {
    /* istanbul ignore next */
    cov_e9mg3q03f().f[4]++;
    cov_e9mg3q03f().s[15]++;
    try {
      /* istanbul ignore next */
      cov_e9mg3q03f().s[16]++;
      if (!tenant?.clientId) {
        /* istanbul ignore next */
        cov_e9mg3q03f().b[0][0]++;
        cov_e9mg3q03f().s[17]++;
        throw new Error('Tenant information not available');
      } else
      /* istanbul ignore next */
      {
        cov_e9mg3q03f().b[0][1]++;
      }

      // Save renewal and alerts using the service
      const result =
      /* istanbul ignore next */
      (cov_e9mg3q03f().s[18]++, await saveRenewal(tenant.clientId, renewalData, alertsData));
      /* istanbul ignore next */
      cov_e9mg3q03f().s[19]++;
      if (!result.success) {
        /* istanbul ignore next */
        cov_e9mg3q03f().b[1][0]++;
        cov_e9mg3q03f().s[20]++;
        throw new Error(
        /* istanbul ignore next */
        (cov_e9mg3q03f().b[2][0]++, result.message) ||
        /* istanbul ignore next */
        (cov_e9mg3q03f().b[2][1]++, 'Failed to save renewal'));
      } else
      /* istanbul ignore next */
      {
        cov_e9mg3q03f().b[1][1]++;
      }

      // Refresh dashboard data after successful save
      cov_e9mg3q03f().s[21]++;
      await refetch();

      // Show success message (TODO: implement toast notifications)
      /* istanbul ignore next */
      cov_e9mg3q03f().s[22]++;
      console.log('Renewal and alerts saved successfully!', result);
    } catch (error) {
      /* istanbul ignore next */
      cov_e9mg3q03f().s[23]++;
      console.error('Error saving renewal:', error);
      // TODO: Show error message to user
      /* istanbul ignore next */
      cov_e9mg3q03f().s[24]++;
      throw error; // Re-throw to let modal handle the error state
    }
  }, [tenant?.clientId, refetch]));
  const handleRenewalClick =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[25]++, useCallback(renewal => {
    /* istanbul ignore next */
    cov_e9mg3q03f().f[5]++;
    cov_e9mg3q03f().s[26]++;
    // TODO: Navigate to renewal details
    console.log('Renewal clicked:', renewal);
  }, []));
  const handleRunScan =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[27]++, useCallback(async () => {
    /* istanbul ignore next */
    cov_e9mg3q03f().f[6]++;
    cov_e9mg3q03f().s[28]++;
    try {
      /* istanbul ignore next */
      cov_e9mg3q03f().s[29]++;
      await runScan();
    } catch (error) {
      /* istanbul ignore next */
      cov_e9mg3q03f().s[30]++;
      console.error('Failed to run scan:', error);
    }
  }, [runScan]));
  const handleScanResultClick =
  /* istanbul ignore next */
  (cov_e9mg3q03f().s[31]++, useCallback(result => {
    /* istanbul ignore next */
    cov_e9mg3q03f().f[7]++;
    cov_e9mg3q03f().s[32]++;
    // TODO: Handle scan result click
    console.log('Scan result clicked:', result);
  }, []));

  // Show loading state only for initial tenant loading
  /* istanbul ignore next */
  cov_e9mg3q03f().s[33]++;
  if (tenantLoading) {
    /* istanbul ignore next */
    cov_e9mg3q03f().b[3][0]++;
    cov_e9mg3q03f().s[34]++;
    return /* istanbul ignore next */__jsx(LoadingPage,
    /* istanbul ignore next */
    {
      title: "Loading Dashboard...",
      subtitle: "Please wait while we set up your dashboard.",
      icon: "\u23F3",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 124,
        columnNumber: 7
      }
    });
  } else
  /* istanbul ignore next */
  {
    cov_e9mg3q03f().b[3][1]++;
  }

  // Show error state
  cov_e9mg3q03f().s[35]++;
  if (
  /* istanbul ignore next */
  (cov_e9mg3q03f().b[5][0]++, tenantError) ||
  /* istanbul ignore next */
  (cov_e9mg3q03f().b[5][1]++, error)) {
    /* istanbul ignore next */
    cov_e9mg3q03f().b[4][0]++;
    cov_e9mg3q03f().s[36]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "dashboard-container",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 135,
        columnNumber: 7
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "text-center py-8",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 136,
        columnNumber: 9
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "text-4xl mb-4",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 137,
        columnNumber: 11
      }
    }, "\u274C"),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "h3",
    /* istanbul ignore next */
    {
      className: "text-lg font-medium mb-2",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 138,
        columnNumber: 11
      }
    }, "Error Loading Dashboard"),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "p",
    /* istanbul ignore next */
    {
      className: "text-secondary mb-4",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 139,
        columnNumber: 11
      }
    },
    /* istanbul ignore next */
    (cov_e9mg3q03f().b[6][0]++, tenantError) ||
    /* istanbul ignore next */
    (cov_e9mg3q03f().b[6][1]++, error)),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "button",
    /* istanbul ignore next */
    {
      className: "btn btn-primary",
      onClick: () => {
        /* istanbul ignore next */
        cov_e9mg3q03f().f[8]++;
        cov_e9mg3q03f().s[37]++;
        return refetch();
      },
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 140,
        columnNumber: 11
      }
    }, "Retry")));
  } else
  /* istanbul ignore next */
  {
    cov_e9mg3q03f().b[4][1]++;
  }
  cov_e9mg3q03f().s[38]++;
  return /* istanbul ignore next */__jsx(ErrorBoundary,
  /* istanbul ignore next */
  {
    onError: (error, errorInfo) => {
      /* istanbul ignore next */
      cov_e9mg3q03f().f[9]++;
      cov_e9mg3q03f().s[39]++;
      console.error('Dashboard error:', error, errorInfo);
    },
    resetKeys: [tenant?.clientId],
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 152,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "dashboard-container",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 158,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(DashboardHeader,
  /* istanbul ignore next */
  {
    clientName: tenant?.clientName,
    onSearch: handleSearch,
    onAddRenewal: handleAddRenewal,
    searchPlaceholder: "Search renewals...",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 160,
      columnNumber: 9
    }
  }),
  /* istanbul ignore next */
  __jsx(DashboardStats,
  /* istanbul ignore next */
  {
    stats: data.stats,
    isLoading: isLoading,
    className: "mb-6",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 168,
      columnNumber: 9
    }
  }),
  /* istanbul ignore next */
  __jsx(LazySection,
  /* istanbul ignore next */
  {
    placeholder:
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-32 bg-gray-100 animate-pulse rounded-lg mb-6",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 176,
        columnNumber: 24
      }
    }),
    className: "mb-6",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 175,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(UpcomingRenewals,
  /* istanbul ignore next */
  {
    renewals: data.upcomingRenewals,
    isLoading: isLoading,
    onRenewalClick: handleRenewalClick,
    daysThreshold: 30,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 179,
      columnNumber: 11
    }
  })),
  /* istanbul ignore next */
  __jsx(LazySection,
  /* istanbul ignore next */
  {
    placeholder:
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "grid grid-cols-1 lg:grid-cols-2 gap-6",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 190,
        columnNumber: 13
      }
    },
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-64 bg-gray-100 animate-pulse rounded-lg",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 191,
        columnNumber: 15
      }
    }),
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      className: "h-64 bg-gray-100 animate-pulse rounded-lg",
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 192,
        columnNumber: 15
      }
    })),
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 188,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "grid grid-cols-1 lg:grid-cols-2 gap-6",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 196,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(RecentRenewals,
  /* istanbul ignore next */
  {
    renewals: data.recentRenewals,
    isLoading: isLoading,
    onRenewalClick: handleRenewalClick,
    maxItems: 5,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 197,
      columnNumber: 13
    }
  }),
  /* istanbul ignore next */
  __jsx(ScanResults,
  /* istanbul ignore next */
  {
    results: scanResults,
    isLoading: scanLoading,
    onRunScan: handleRunScan,
    onResultClick: handleScanResultClick,
    lastScanDate:
    /* istanbul ignore next */
    (cov_e9mg3q03f().b[7][0]++, lastScanDate) ||
    /* istanbul ignore next */
    (cov_e9mg3q03f().b[7][1]++, undefined),
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 204,
      columnNumber: 13
    }
  }))),
  /* istanbul ignore next */
  __jsx(AddRenewalModal,
  /* istanbul ignore next */
  {
    isOpen: isAddRenewalModalOpen,
    onClose: handleCloseAddRenewalModal,
    onSubmit: handleSubmitRenewal,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 215,
      columnNumber: 9
    }
  })));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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