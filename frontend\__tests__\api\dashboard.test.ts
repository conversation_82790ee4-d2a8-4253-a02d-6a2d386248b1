/**
 * Dashboard API Tests
 * 
 * Tests for dashboard API endpoints including authentication,
 * data validation, and error handling
 */

import { jest } from '@jest/globals'
import { testUtils } from '../utils/test-utils'

// Mock the database
const mockDb = {
  query: jest.fn(),
  end: jest.fn(),
}

jest.mock('@/lib/db', () => ({
  db: mockDb,
}))

// Mock JWT validation
const mockValidateJwtToken = jest.fn()
jest.mock('@/lib/jwt-validator', () => ({
  validateJwtToken: mockValidateJwtToken,
}))

describe('Dashboard API Endpoints', () => {
  let mockRequest: any
  let mockResponse: any

  beforeEach(() => {
    jest.clearAllMocks()

    mockRequest = {
      method: 'GET',
      headers: {
        authorization: 'Bearer valid-token',
      },
      cookies: {},
      query: {},
      body: {},
    }

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      setHeader: jest.fn().mockReturnThis(),
    }

    // Mock successful JWT validation
    mockValidateJwtToken.mockResolvedValue({
      sub: 'test-user-id',
      email: '<EMAIL>',
      'custom:tenant_id': 'test-tenant-id',
    })
  })

  describe('GET /api/dashboard/stats', () => {
    const mockStatsData = {
      total_renewals: 25,
      renewals_due: 5,
      vendors: 12,
      annual_spend: 125000,
    }

    beforeEach(() => {
      mockDb.query.mockResolvedValue({ rows: [mockStatsData] })
    })

    it('should return dashboard stats successfully', async () => {
      // Import the handler dynamically to ensure mocks are applied
      const { default: handler } = await import('@/app/api/dashboard/stats/route')

      await handler(mockRequest)

      expect(mockValidateJwtToken).toHaveBeenCalledWith(
        'valid-token',
        expect.any(String),
        expect.any(String)
      )

      expect(mockDb.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT'),
        ['test-tenant-id']
      )

      expect(mockResponse.status).toHaveBeenCalledWith(200)
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: {
          totalRenewals: 25,
          renewalsDue: 5,
          vendors: 12,
          annualSpend: '$125,000',
        },
      })
    })

    it('should handle missing authorization header', async () => {
      delete mockRequest.headers.authorization

      const { default: handler } = await import('@/app/api/dashboard/stats/route')

      await handler(mockRequest)

      expect(mockResponse.status).toHaveBeenCalledWith(401)
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Authorization token required',
      })
    })

    it('should handle invalid JWT token', async () => {
      mockValidateJwtToken.mockRejectedValue(new Error('Invalid token'))

      const { default: handler } = await import('@/app/api/dashboard/stats/route')

      await handler(mockRequest)

      expect(mockResponse.status).toHaveBeenCalledWith(401)
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Invalid or expired token',
      })
    })

    it('should handle database errors', async () => {
      mockDb.query.mockRejectedValue(new Error('Database connection failed'))

      const { default: handler } = await import('@/app/api/dashboard/stats/route')

      await handler(mockRequest)

      expect(mockResponse.status).toHaveBeenCalledWith(500)
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Internal server error',
      })
    })

    it('should handle missing tenant ID in token', async () => {
      mockValidateJwtToken.mockResolvedValue({
        sub: 'test-user-id',
        email: '<EMAIL>',
        // Missing tenant_id
      })

      const { default: handler } = await import('@/app/api/dashboard/stats/route')

      await handler(mockRequest)

      expect(mockResponse.status).toHaveBeenCalledWith(400)
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Tenant ID not found in token',
      })
    })

    it('should handle empty database results', async () => {
      mockDb.query.mockResolvedValue({ rows: [] })

      const { default: handler } = await import('@/app/api/dashboard/stats/route')

      await handler(mockRequest)

      expect(mockResponse.status).toHaveBeenCalledWith(200)
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: {
          totalRenewals: 0,
          renewalsDue: 0,
          vendors: 0,
          annualSpend: '$0',
        },
      })
    })

    it('should format currency correctly', async () => {
      mockDb.query.mockResolvedValue({
        rows: [{ ...mockStatsData, annual_spend: 1234567.89 }],
      })

      const { default: handler } = await import('@/app/api/dashboard/stats/route')

      await handler(mockRequest)

      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: expect.objectContaining({
          annualSpend: '$1,234,568', // Rounded to nearest dollar
        }),
      })
    })

    it('should handle non-GET methods', async () => {
      mockRequest.method = 'POST'

      const { default: handler } = await import('@/app/api/dashboard/stats/route')

      await handler(mockRequest)

      expect(mockResponse.status).toHaveBeenCalledWith(405)
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Method not allowed',
      })
    })
  })

  describe('GET /api/dashboard/renewals', () => {
    const mockRenewalsData = [
      {
        id: '1',
        name: 'Microsoft Office 365',
        vendor: 'Microsoft',
        renewal_date: '2025-02-15',
        cost: 1200,
        status: 'active',
      },
      {
        id: '2',
        name: 'Adobe Creative Suite',
        vendor: 'Adobe',
        renewal_date: '2025-03-01',
        cost: 2400,
        status: 'pending',
      },
    ]

    beforeEach(() => {
      mockDb.query.mockResolvedValue({ rows: mockRenewalsData })
    })

    it('should return recent renewals successfully', async () => {
      const { default: handler } = await import('@/app/api/dashboard/renewals/route')

      await handler(mockRequest)

      expect(mockDb.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT'),
        ['test-tenant-id']
      )

      expect(mockResponse.status).toHaveBeenCalledWith(200)
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: expect.arrayContaining([
          expect.objectContaining({
            id: '1',
            name: 'Microsoft Office 365',
            vendor: 'Microsoft',
          }),
        ]),
      })
    })

    it('should handle query parameters for filtering', async () => {
      mockRequest.query = { limit: '5', status: 'active' }

      const { default: handler } = await import('@/app/api/dashboard/renewals/route')

      await handler(mockRequest)

      expect(mockDb.query).toHaveBeenCalledWith(
        expect.stringContaining('LIMIT'),
        expect.arrayContaining(['test-tenant-id', 'active', 5])
      )
    })

    it('should validate query parameters', async () => {
      mockRequest.query = { limit: 'invalid' }

      const { default: handler } = await import('@/app/api/dashboard/renewals/route')

      await handler(mockRequest)

      expect(mockResponse.status).toHaveBeenCalledWith(400)
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Invalid query parameters',
      })
    })

    it('should handle date formatting correctly', async () => {
      const { default: handler } = await import('@/app/api/dashboard/renewals/route')

      await handler(mockRequest)

      const responseCall = mockResponse.json.mock.calls[0][0]
      const renewals = responseCall.data

      renewals.forEach((renewal: any) => {
        expect(renewal.renewalDate).toMatch(/^\d{4}-\d{2}-\d{2}T/)
      })
    })
  })

  describe('Authentication Middleware', () => {
    it('should extract token from Authorization header', () => {
      const authHeader = 'Bearer test-token-123'
      const token = authHeader.startsWith('Bearer ') 
        ? authHeader.substring(7) 
        : null

      expect(token).toBe('test-token-123')
    })

    it('should extract token from cookies', () => {
      mockRequest.cookies.idToken = 'cookie-token-123'
      const token = mockRequest.cookies.idToken

      expect(token).toBe('cookie-token-123')
    })

    it('should prioritize Authorization header over cookies', () => {
      mockRequest.headers.authorization = 'Bearer header-token'
      mockRequest.cookies.idToken = 'cookie-token'

      const headerToken = mockRequest.headers.authorization?.substring(7)
      const cookieToken = mockRequest.cookies.idToken
      const token = headerToken || cookieToken

      expect(token).toBe('header-token')
    })

    it('should handle malformed Authorization header', () => {
      mockRequest.headers.authorization = 'InvalidFormat token'
      
      const authHeader = mockRequest.headers.authorization
      const token = authHeader?.startsWith('Bearer ') 
        ? authHeader.substring(7) 
        : null

      expect(token).toBe(null)
    })
  })

  describe('Error Handling', () => {
    it('should handle unexpected errors gracefully', async () => {
      mockValidateJwtToken.mockImplementation(() => {
        throw new Error('Unexpected error')
      })

      const { default: handler } = await import('@/app/api/dashboard/stats/route')

      await handler(mockRequest)

      expect(mockResponse.status).toHaveBeenCalledWith(500)
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Internal server error',
      })
    })

    it('should not expose sensitive error details', async () => {
      mockDb.query.mockRejectedValue(new Error('Database password is wrong'))

      const { default: handler } = await import('@/app/api/dashboard/stats/route')

      await handler(mockRequest)

      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Internal server error', // Generic error message
      })
    })

    it('should log errors for debugging', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      mockDb.query.mockRejectedValue(new Error('Database error'))

      const { default: handler } = await import('@/app/api/dashboard/stats/route')

      await handler(mockRequest)

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('API Error'),
        expect.any(Error)
      )

      consoleSpy.mockRestore()
    })
  })

  describe('Security', () => {
    it('should validate tenant isolation', async () => {
      const { default: handler } = await import('@/app/api/dashboard/stats/route')

      await handler(mockRequest)

      // Ensure query includes tenant_id filter
      expect(mockDb.query).toHaveBeenCalledWith(
        expect.stringContaining('tenant_id = $1'),
        expect.arrayContaining(['test-tenant-id'])
      )
    })

    it('should prevent SQL injection', async () => {
      mockRequest.query = { status: "'; DROP TABLE renewals; --" }

      const { default: handler } = await import('@/app/api/dashboard/renewals/route')

      await handler(mockRequest)

      // Should use parameterized queries
      expect(mockDb.query).toHaveBeenCalledWith(
        expect.any(String),
        expect.arrayContaining([expect.any(String)])
      )
    })

    it('should set security headers', async () => {
      const { default: handler } = await import('@/app/api/dashboard/stats/route')

      await handler(mockRequest)

      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'Cache-Control',
        expect.stringContaining('no-cache')
      )
    })
  })
})
