/**
 * Renewals API Endpoint
 * 
 * Provides access to tenant-specific renewals data
 * GET /api/renewals - Returns renewals for the authenticated user's tenant
 */

import { NextRequest } from 'next/server';
import { requireAuth } from '@/lib/auth-middleware';
import { getClientByEmailDomain } from '@/lib/clients';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  ApiErrorCode, 
  HttpStatus,
  withErrorHandling 
} from '@/lib/api-response';
import { executeQuery, schemaExists } from '@/lib/database';

// Renewal interface matching the database structure
export interface Renewal {
  id: string;
  name: string;
  productName: string;
  version: string;
  vendor: string;
  type: string;
  renewalDate: string;
  cost: number;
  currency: string;
  status: string;
  alerts: number;
  licenseCount: number;
  description: string;
}

// GET /api/renewals - Get renewals for authenticated user's tenant
export const GET = withErrorHandling(async (request: NextRequest) => {
  // Verify authentication
  const authResult = await requireAuth();
  if (!authResult.success) {
    return authResult.response!;
  }

  const session = authResult.session!;

  // Get tenant context using the secure method
  const clientResult = await getClientByEmailDomain(session.email);

  if (!clientResult.success) {
    const statusCode = clientResult.errorCode === 'NOT_FOUND' ? HttpStatus.NOT_FOUND : HttpStatus.INTERNAL_SERVER_ERROR;
    const apiErrorCode = clientResult.errorCode === 'NOT_FOUND' ? ApiErrorCode.NOT_FOUND : ApiErrorCode.DATABASE_ERROR;

    return createErrorResponse(
      clientResult.error || 'Failed to fetch tenant information',
      apiErrorCode,
      statusCode
    );
  }

  const tenant = clientResult.client!;

  // Check if tenant schema exists
  const schemaReady = await schemaExists(tenant.tenantSchema);

  if (!schemaReady) {
    console.log(`Tenant schema ${tenant.tenantSchema} not ready yet`);
    return createSuccessResponse([], 'Tenant schema not ready, returning empty results');
  }

  try {
    // Query tenant schema for renewals data with joins to get type and currency names
    const renewalsQuery = `
      SELECT
        r."RenewalID" as id,
        r."RenewalName" as name,
        r."ProductName" as product_name,
        r."Version" as version,
        r."VendorName" as vendor,
        COALESCE(rt."RenewalTypeName", 'Unknown') as type,
        r."RenewalDate" as renewal_date,
        r."Cost" as cost,
        COALESCE(c."CurrencyID", 'USD') as currency,
        r."Status" as status,
        r."LicenseCount" as license_count,
        r."Description" as description,
        0 as alerts -- TODO: Calculate actual alerts from alerts table
      FROM "${tenant.tenantSchema}"."Renewals" r
      LEFT JOIN metadata."RenewalTypes" rt ON r."RenewalTypeID" = rt."RenewalTypeID"
      LEFT JOIN metadata."Currencies" c ON r."CurrencyID" = c."CurrencyID"
      WHERE r."Active" = true
      ORDER BY r."RenewalDate" ASC, r."RenewalName" ASC
    `;

    const result = await executeQuery(renewalsQuery, [], { schema: tenant.tenantSchema });

    if (!result.success) {
      console.error('Failed to fetch renewals:', result.error);
      return createErrorResponse(
        'Failed to fetch renewals data',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    // Format the results
    const renewals: Renewal[] = (result.data || []).map((row: any) => ({
      id: row.id.toString(),
      name: row.name,
      productName: row.product_name || '',
      version: row.version || '',
      vendor: row.vendor || '',
      type: row.type,
      renewalDate: row.renewal_date ? new Date(row.renewal_date).toISOString().split('T')[0] : '',
      cost: parseFloat(row.cost) || 0,
      currency: row.currency,
      status: row.status,
      alerts: parseInt(row.alerts) || 0,
      licenseCount: parseInt(row.license_count) || 0,
      description: row.description || ''
    }));

    console.log(`Renewals fetched successfully: ${renewals.length} records for tenant ${tenant.tenantSchema}`);

    return createSuccessResponse(
      renewals,
      'Renewals retrieved successfully'
    );

  } catch (error) {
    console.error('Error fetching renewals:', error);
    return createErrorResponse(
      'Failed to fetch renewals data',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

// GET /api/renewals/filters - Get filter options from renewals data
export const POST = withErrorHandling(async (request: NextRequest) => {
  // This endpoint can be used to get unique filter values
  const authResult = await requireAuth();
  if (!authResult.success) {
    return authResult.response!;
  }

  const session = authResult.session!;
  const clientResult = await getClientByEmailDomain(session.email);

  if (!clientResult.success) {
    return createErrorResponse(
      'Failed to fetch tenant information',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  const tenant = clientResult.client!;
  const schemaReady = await schemaExists(tenant.tenantSchema);

  if (!schemaReady) {
    return createSuccessResponse({
      vendors: [],
      types: [],
      statuses: []
    }, 'Tenant schema not ready');
  }

  try {
    // Get unique filter values
    const filtersQuery = `
      SELECT
        ARRAY_AGG(DISTINCT r."VendorName") FILTER (WHERE r."VendorName" IS NOT NULL) as vendors,
        ARRAY_AGG(DISTINCT rt."RenewalTypeName") FILTER (WHERE rt."RenewalTypeName" IS NOT NULL) as types,
        ARRAY_AGG(DISTINCT r."Status") FILTER (WHERE r."Status" IS NOT NULL) as statuses
      FROM "${tenant.tenantSchema}"."Renewals" r
      LEFT JOIN metadata."RenewalTypes" rt ON r."RenewalTypeID" = rt."RenewalTypeID"
      WHERE r."Active" = true
    `;

    const result = await executeQuery(filtersQuery, [], { schema: tenant.tenantSchema });

    if (!result.success || !result.data || result.data.length === 0) {
      return createSuccessResponse({
        vendors: [],
        types: [],
        statuses: []
      }, 'No filter data available');
    }

    const filterData = result.data[0];

    return createSuccessResponse({
      vendors: filterData.vendors || [],
      types: filterData.types || [],
      statuses: filterData.statuses || []
    }, 'Filter options retrieved successfully');

  } catch (error) {
    console.error('Error fetching filter options:', error);
    return createErrorResponse(
      'Failed to fetch filter options',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});
