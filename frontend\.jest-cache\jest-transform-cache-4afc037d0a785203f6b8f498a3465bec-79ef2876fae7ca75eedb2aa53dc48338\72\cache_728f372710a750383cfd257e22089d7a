0135950727b6c557b683b3d68a809460
/* istanbul ignore next */
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.componentCache = exports.cacheUtils = exports.apiCache = exports.AdvancedCache = void 0;
exports.useCache = useCache;
exports.userDataCache = void 0;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
function cov_ck59h46it() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\cache.ts";
  var hash = "c8fed1e1d306d9954a5aa18355920cf695ef0275";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\lib\\cache.ts",
    statementMap: {
      "0": {
        start: {
          line: 39,
          column: 18
        },
        end: {
          line: 39,
          column: 50
        }
      },
      "1": {
        start: {
          line: 41,
          column: 34
        },
        end: {
          line: 47,
          column: 3
        }
      },
      "2": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 61,
          column: 5
        }
      },
      "3": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 62,
          column: 44
        }
      },
      "4": {
        start: {
          line: 65,
          column: 4
        },
        end: {
          line: 65,
          column: 23
        }
      },
      "5": {
        start: {
          line: 70,
          column: 18
        },
        end: {
          line: 70,
          column: 37
        }
      },
      "6": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 75,
          column: 5
        }
      },
      "7": {
        start: {
          line: 73,
          column: 6
        },
        end: {
          line: 73,
          column: 32
        }
      },
      "8": {
        start: {
          line: 74,
          column: 6
        },
        end: {
          line: 74,
          column: 17
        }
      },
      "9": {
        start: {
          line: 78,
          column: 4
        },
        end: {
          line: 82,
          column: 5
        }
      },
      "10": {
        start: {
          line: 79,
          column: 6
        },
        end: {
          line: 79,
          column: 28
        }
      },
      "11": {
        start: {
          line: 80,
          column: 6
        },
        end: {
          line: 80,
          column: 32
        }
      },
      "12": {
        start: {
          line: 81,
          column: 6
        },
        end: {
          line: 81,
          column: 17
        }
      },
      "13": {
        start: {
          line: 85,
          column: 4
        },
        end: {
          line: 85,
          column: 23
        }
      },
      "14": {
        start: {
          line: 86,
          column: 4
        },
        end: {
          line: 86,
          column: 35
        }
      },
      "15": {
        start: {
          line: 88,
          column: 4
        },
        end: {
          line: 88,
          column: 29
        }
      },
      "16": {
        start: {
          line: 89,
          column: 4
        },
        end: {
          line: 89,
          column: 21
        }
      },
      "17": {
        start: {
          line: 95,
          column: 4
        },
        end: {
          line: 97,
          column: 5
        }
      },
      "18": {
        start: {
          line: 96,
          column: 6
        },
        end: {
          line: 96,
          column: 18
        }
      },
      "19": {
        start: {
          line: 99,
          column: 33
        },
        end: {
          line: 106,
          column: 5
        }
      },
      "20": {
        start: {
          line: 108,
          column: 4
        },
        end: {
          line: 108,
          column: 30
        }
      },
      "21": {
        start: {
          line: 109,
          column: 4
        },
        end: {
          line: 109,
          column: 29
        }
      },
      "22": {
        start: {
          line: 114,
          column: 18
        },
        end: {
          line: 114,
          column: 37
        }
      },
      "23": {
        start: {
          line: 115,
          column: 4
        },
        end: {
          line: 115,
          column: 28
        }
      },
      "24": {
        start: {
          line: 115,
          column: 16
        },
        end: {
          line: 115,
          column: 28
        }
      },
      "25": {
        start: {
          line: 117,
          column: 4
        },
        end: {
          line: 120,
          column: 5
        }
      },
      "26": {
        start: {
          line: 118,
          column: 6
        },
        end: {
          line: 118,
          column: 28
        }
      },
      "27": {
        start: {
          line: 119,
          column: 6
        },
        end: {
          line: 119,
          column: 18
        }
      },
      "28": {
        start: {
          line: 122,
          column: 4
        },
        end: {
          line: 122,
          column: 15
        }
      },
      "29": {
        start: {
          line: 127,
          column: 4
        },
        end: {
          line: 127,
          column: 33
        }
      },
      "30": {
        start: {
          line: 132,
          column: 18
        },
        end: {
          line: 132,
          column: 19
        }
      },
      "31": {
        start: {
          line: 133,
          column: 4
        },
        end: {
          line: 138,
          column: 5
        }
      },
      "32": {
        start: {
          line: 134,
          column: 6
        },
        end: {
          line: 137,
          column: 7
        }
      },
      "33": {
        start: {
          line: 134,
          column: 33
        },
        end: {
          line: 134,
          column: 51
        }
      },
      "34": {
        start: {
          line: 135,
          column: 8
        },
        end: {
          line: 135,
          column: 30
        }
      },
      "35": {
        start: {
          line: 136,
          column: 8
        },
        end: {
          line: 136,
          column: 17
        }
      },
      "36": {
        start: {
          line: 139,
          column: 4
        },
        end: {
          line: 139,
          column: 18
        }
      },
      "37": {
        start: {
          line: 144,
          column: 4
        },
        end: {
          line: 144,
          column: 22
        }
      },
      "38": {
        start: {
          line: 145,
          column: 4
        },
        end: {
          line: 145,
          column: 23
        }
      },
      "39": {
        start: {
          line: 150,
          column: 26
        },
        end: {
          line: 150,
          column: 65
        }
      },
      "40": {
        start: {
          line: 151,
          column: 4
        },
        end: {
          line: 155,
          column: 5
        }
      },
      "41": {
        start: {
          line: 160,
          column: 4
        },
        end: {
          line: 160,
          column: 40
        }
      },
      "42": {
        start: {
          line: 165,
          column: 4
        },
        end: {
          line: 165,
          column: 26
        }
      },
      "43": {
        start: {
          line: 170,
          column: 4
        },
        end: {
          line: 170,
          column: 51
        }
      },
      "44": {
        start: {
          line: 175,
          column: 4
        },
        end: {
          line: 175,
          column: 37
        }
      },
      "45": {
        start: {
          line: 175,
          column: 31
        },
        end: {
          line: 175,
          column: 37
        }
      },
      "46": {
        start: {
          line: 177,
          column: 36
        },
        end: {
          line: 177,
          column: 40
        }
      },
      "47": {
        start: {
          line: 179,
          column: 4
        },
        end: {
          line: 192,
          column: 5
        }
      },
      "48": {
        start: {
          line: 181,
          column: 8
        },
        end: {
          line: 181,
          column: 38
        }
      },
      "49": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 182,
          column: 13
        }
      },
      "50": {
        start: {
          line: 184,
          column: 8
        },
        end: {
          line: 184,
          column: 38
        }
      },
      "51": {
        start: {
          line: 185,
          column: 8
        },
        end: {
          line: 185,
          column: 13
        }
      },
      "52": {
        start: {
          line: 187,
          column: 8
        },
        end: {
          line: 187,
          column: 42
        }
      },
      "53": {
        start: {
          line: 188,
          column: 8
        },
        end: {
          line: 188,
          column: 13
        }
      },
      "54": {
        start: {
          line: 190,
          column: 8
        },
        end: {
          line: 190,
          column: 39
        }
      },
      "55": {
        start: {
          line: 191,
          column: 8
        },
        end: {
          line: 191,
          column: 13
        }
      },
      "56": {
        start: {
          line: 194,
          column: 4
        },
        end: {
          line: 197,
          column: 5
        }
      },
      "57": {
        start: {
          line: 195,
          column: 6
        },
        end: {
          line: 195,
          column: 35
        }
      },
      "58": {
        start: {
          line: 196,
          column: 6
        },
        end: {
          line: 196,
          column: 30
        }
      },
      "59": {
        start: {
          line: 202,
          column: 35
        },
        end: {
          line: 202,
          column: 39
        }
      },
      "60": {
        start: {
          line: 203,
          column: 21
        },
        end: {
          line: 203,
          column: 31
        }
      },
      "61": {
        start: {
          line: 205,
          column: 4
        },
        end: {
          line: 210,
          column: 5
        }
      },
      "62": {
        start: {
          line: 206,
          column: 6
        },
        end: {
          line: 209,
          column: 7
        }
      },
      "63": {
        start: {
          line: 207,
          column: 8
        },
        end: {
          line: 207,
          column: 39
        }
      },
      "64": {
        start: {
          line: 208,
          column: 8
        },
        end: {
          line: 208,
          column: 23
        }
      },
      "65": {
        start: {
          line: 212,
          column: 4
        },
        end: {
          line: 212,
          column: 20
        }
      },
      "66": {
        start: {
          line: 217,
          column: 38
        },
        end: {
          line: 217,
          column: 42
        }
      },
      "67": {
        start: {
          line: 218,
          column: 21
        },
        end: {
          line: 218,
          column: 29
        }
      },
      "68": {
        start: {
          line: 220,
          column: 4
        },
        end: {
          line: 225,
          column: 5
        }
      },
      "69": {
        start: {
          line: 221,
          column: 6
        },
        end: {
          line: 224,
          column: 7
        }
      },
      "70": {
        start: {
          line: 222,
          column: 8
        },
        end: {
          line: 222,
          column: 38
        }
      },
      "71": {
        start: {
          line: 223,
          column: 8
        },
        end: {
          line: 223,
          column: 26
        }
      },
      "72": {
        start: {
          line: 227,
          column: 4
        },
        end: {
          line: 227,
          column: 23
        }
      },
      "73": {
        start: {
          line: 232,
          column: 4
        },
        end: {
          line: 236,
          column: 5
        }
      },
      "74": {
        start: {
          line: 233,
          column: 6
        },
        end: {
          line: 235,
          column: 7
        }
      },
      "75": {
        start: {
          line: 234,
          column: 8
        },
        end: {
          line: 234,
          column: 18
        }
      },
      "76": {
        start: {
          line: 237,
          column: 4
        },
        end: {
          line: 237,
          column: 15
        }
      },
      "77": {
        start: {
          line: 242,
          column: 35
        },
        end: {
          line: 242,
          column: 39
        }
      },
      "78": {
        start: {
          line: 243,
          column: 21
        },
        end: {
          line: 243,
          column: 31
        }
      },
      "79": {
        start: {
          line: 245,
          column: 4
        },
        end: {
          line: 250,
          column: 5
        }
      },
      "80": {
        start: {
          line: 246,
          column: 6
        },
        end: {
          line: 249,
          column: 7
        }
      },
      "81": {
        start: {
          line: 247,
          column: 8
        },
        end: {
          line: 247,
          column: 36
        }
      },
      "82": {
        start: {
          line: 248,
          column: 8
        },
        end: {
          line: 248,
          column: 23
        }
      },
      "83": {
        start: {
          line: 252,
          column: 4
        },
        end: {
          line: 252,
          column: 20
        }
      },
      "84": {
        start: {
          line: 257,
          column: 4
        },
        end: {
          line: 257,
          column: 42
        }
      },
      "85": {
        start: {
          line: 257,
          column: 36
        },
        end: {
          line: 257,
          column: 42
        }
      },
      "86": {
        start: {
          line: 259,
          column: 4
        },
        end: {
          line: 269,
          column: 5
        }
      },
      "87": {
        start: {
          line: 261,
          column: 8
        },
        end: {
          line: 261,
          column: 27
        }
      },
      "88": {
        start: {
          line: 262,
          column: 8
        },
        end: {
          line: 262,
          column: 13
        }
      },
      "89": {
        start: {
          line: 264,
          column: 8
        },
        end: {
          line: 264,
          column: 29
        }
      },
      "90": {
        start: {
          line: 265,
          column: 8
        },
        end: {
          line: 265,
          column: 13
        }
      },
      "91": {
        start: {
          line: 268,
          column: 8
        },
        end: {
          line: 268,
          column: 13
        }
      },
      "92": {
        start: {
          line: 274,
          column: 4
        },
        end: {
          line: 280,
          column: 5
        }
      },
      "93": {
        start: {
          line: 285,
          column: 4
        },
        end: {
          line: 287,
          column: 35
        }
      },
      "94": {
        start: {
          line: 286,
          column: 6
        },
        end: {
          line: 286,
          column: 20
        }
      },
      "95": {
        start: {
          line: 292,
          column: 16
        },
        end: {
          line: 292,
          column: 26
        }
      },
      "96": {
        start: {
          line: 293,
          column: 35
        },
        end: {
          line: 293,
          column: 37
        }
      },
      "97": {
        start: {
          line: 295,
          column: 4
        },
        end: {
          line: 299,
          column: 5
        }
      },
      "98": {
        start: {
          line: 296,
          column: 6
        },
        end: {
          line: 298,
          column: 7
        }
      },
      "99": {
        start: {
          line: 297,
          column: 8
        },
        end: {
          line: 297,
          column: 30
        }
      },
      "100": {
        start: {
          line: 301,
          column: 4
        },
        end: {
          line: 301,
          column: 55
        }
      },
      "101": {
        start: {
          line: 301,
          column: 32
        },
        end: {
          line: 301,
          column: 54
        }
      },
      "102": {
        start: {
          line: 306,
          column: 4
        },
        end: {
          line: 308,
          column: 5
        }
      },
      "103": {
        start: {
          line: 307,
          column: 6
        },
        end: {
          line: 307,
          column: 38
        }
      },
      "104": {
        start: {
          line: 309,
          column: 4
        },
        end: {
          line: 309,
          column: 16
        }
      },
      "105": {
        start: {
          line: 314,
          column: 24
        },
        end: {
          line: 318,
          column: 9
        }
      },
      "106": {
        start: {
          line: 320,
          column: 30
        },
        end: {
          line: 324,
          column: 9
        }
      },
      "107": {
        start: {
          line: 326,
          column: 29
        },
        end: {
          line: 330,
          column: 9
        }
      },
      "108": {
        start: {
          line: 333,
          column: 26
        },
        end: {
          line: 380,
          column: 1
        }
      },
      "109": {
        start: {
          line: 336,
          column: 4
        },
        end: {
          line: 336,
          column: 42
        }
      },
      "110": {
        start: {
          line: 341,
          column: 4
        },
        end: {
          line: 350,
          column: 5
        }
      },
      "111": {
        start: {
          line: 342,
          column: 6
        },
        end: {
          line: 347,
          column: 8
        }
      },
      "112": {
        start: {
          line: 343,
          column: 8
        },
        end: {
          line: 345,
          column: 9
        }
      },
      "113": {
        start: {
          line: 344,
          column: 10
        },
        end: {
          line: 344,
          column: 63
        }
      },
      "114": {
        start: {
          line: 346,
          column: 8
        },
        end: {
          line: 346,
          column: 20
        }
      },
      "115": {
        start: {
          line: 349,
          column: 6
        },
        end: {
          line: 349,
          column: 24
        }
      },
      "116": {
        start: {
          line: 355,
          column: 4
        },
        end: {
          line: 364,
          column: 5
        }
      },
      "117": {
        start: {
          line: 356,
          column: 6
        },
        end: {
          line: 361,
          column: 8
        }
      },
      "118": {
        start: {
          line: 357,
          column: 8
        },
        end: {
          line: 359,
          column: 9
        }
      },
      "119": {
        start: {
          line: 358,
          column: 10
        },
        end: {
          line: 358,
          column: 38
        }
      },
      "120": {
        start: {
          line: 360,
          column: 8
        },
        end: {
          line: 360,
          column: 20
        }
      },
      "121": {
        start: {
          line: 363,
          column: 6
        },
        end: {
          line: 363,
          column: 17
        }
      },
      "122": {
        start: {
          line: 368,
          column: 25
        },
        end: {
          line: 372,
          column: 3
        }
      },
      "123": {
        start: {
          line: 376,
          column: 4
        },
        end: {
          line: 376,
          column: 20
        }
      },
      "124": {
        start: {
          line: 377,
          column: 4
        },
        end: {
          line: 377,
          column: 26
        }
      },
      "125": {
        start: {
          line: 378,
          column: 4
        },
        end: {
          line: 378,
          column: 25
        }
      },
      "126": {
        start: {
          line: 386,
          column: 2
        },
        end: {
          line: 394,
          column: 3
        }
      },
      "127": {
        start: {
          line: 387,
          column: 26
        },
        end: {
          line: 387,
          column: 40
        }
      },
      "128": {
        start: {
          line: 389,
          column: 6
        },
        end: {
          line: 389,
          column: 37
        }
      },
      "129": {
        start: {
          line: 390,
          column: 26
        },
        end: {
          line: 390,
          column: 40
        }
      },
      "130": {
        start: {
          line: 391,
          column: 29
        },
        end: {
          line: 391,
          column: 46
        }
      },
      "131": {
        start: {
          line: 392,
          column: 17
        },
        end: {
          line: 392,
          column: 30
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 51,
            column: 2
          },
          end: {
            line: 51,
            column: 3
          }
        },
        loc: {
          start: {
            line: 54,
            column: 4
          },
          end: {
            line: 66,
            column: 3
          }
        },
        line: 54
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 69,
            column: 2
          },
          end: {
            line: 69,
            column: 3
          }
        },
        loc: {
          start: {
            line: 69,
            column: 29
          },
          end: {
            line: 90,
            column: 3
          }
        },
        line: 69
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 93,
            column: 2
          },
          end: {
            line: 93,
            column: 3
          }
        },
        loc: {
          start: {
            line: 93,
            column: 69
          },
          end: {
            line: 110,
            column: 3
          }
        },
        line: 93
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 113,
            column: 2
          },
          end: {
            line: 113,
            column: 3
          }
        },
        loc: {
          start: {
            line: 113,
            column: 28
          },
          end: {
            line: 123,
            column: 3
          }
        },
        line: 113
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 126,
            column: 2
          },
          end: {
            line: 126,
            column: 3
          }
        },
        loc: {
          start: {
            line: 126,
            column: 31
          },
          end: {
            line: 128,
            column: 3
          }
        },
        line: 126
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 131,
            column: 2
          },
          end: {
            line: 131,
            column: 3
          }
        },
        loc: {
          start: {
            line: 131,
            column: 38
          },
          end: {
            line: 140,
            column: 3
          }
        },
        line: 131
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 134,
            column: 26
          },
          end: {
            line: 134,
            column: 27
          }
        },
        loc: {
          start: {
            line: 134,
            column: 33
          },
          end: {
            line: 134,
            column: 51
          }
        },
        line: 134
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 143,
            column: 2
          },
          end: {
            line: 143,
            column: 3
          }
        },
        loc: {
          start: {
            line: 143,
            column: 16
          },
          end: {
            line: 146,
            column: 3
          }
        },
        line: 143
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 149,
            column: 2
          },
          end: {
            line: 149,
            column: 3
          }
        },
        loc: {
          start: {
            line: 149,
            column: 29
          },
          end: {
            line: 156,
            column: 3
          }
        },
        line: 149
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 159,
            column: 2
          },
          end: {
            line: 159,
            column: 3
          }
        },
        loc: {
          start: {
            line: 159,
            column: 19
          },
          end: {
            line: 161,
            column: 3
          }
        },
        line: 159
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 164,
            column: 2
          },
          end: {
            line: 164,
            column: 3
          }
        },
        loc: {
          start: {
            line: 164,
            column: 17
          },
          end: {
            line: 166,
            column: 3
          }
        },
        line: 164
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 169,
            column: 2
          },
          end: {
            line: 169,
            column: 3
          }
        },
        loc: {
          start: {
            line: 169,
            column: 51
          },
          end: {
            line: 171,
            column: 3
          }
        },
        line: 169
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 174,
            column: 2
          },
          end: {
            line: 174,
            column: 3
          }
        },
        loc: {
          start: {
            line: 174,
            column: 24
          },
          end: {
            line: 198,
            column: 3
          }
        },
        line: 174
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 201,
            column: 2
          },
          end: {
            line: 201,
            column: 3
          }
        },
        loc: {
          start: {
            line: 201,
            column: 38
          },
          end: {
            line: 213,
            column: 3
          }
        },
        line: 201
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 216,
            column: 2
          },
          end: {
            line: 216,
            column: 3
          }
        },
        loc: {
          start: {
            line: 216,
            column: 38
          },
          end: {
            line: 228,
            column: 3
          }
        },
        line: 216
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 231,
            column: 2
          },
          end: {
            line: 231,
            column: 3
          }
        },
        loc: {
          start: {
            line: 231,
            column: 42
          },
          end: {
            line: 238,
            column: 3
          }
        },
        line: 231
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 241,
            column: 2
          },
          end: {
            line: 241,
            column: 3
          }
        },
        loc: {
          start: {
            line: 241,
            column: 39
          },
          end: {
            line: 253,
            column: 3
          }
        },
        line: 241
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 256,
            column: 2
          },
          end: {
            line: 256,
            column: 3
          }
        },
        loc: {
          start: {
            line: 256,
            column: 65
          },
          end: {
            line: 270,
            column: 3
          }
        },
        line: 256
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 273,
            column: 2
          },
          end: {
            line: 273,
            column: 3
          }
        },
        loc: {
          start: {
            line: 273,
            column: 31
          },
          end: {
            line: 281,
            column: 3
          }
        },
        line: 273
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 284,
            column: 2
          },
          end: {
            line: 284,
            column: 3
          }
        },
        loc: {
          start: {
            line: 284,
            column: 31
          },
          end: {
            line: 288,
            column: 3
          }
        },
        line: 284
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 285,
            column: 36
          },
          end: {
            line: 285,
            column: 37
          }
        },
        loc: {
          start: {
            line: 285,
            column: 42
          },
          end: {
            line: 287,
            column: 5
          }
        },
        line: 285
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 291,
            column: 2
          },
          end: {
            line: 291,
            column: 3
          }
        },
        loc: {
          start: {
            line: 291,
            column: 26
          },
          end: {
            line: 302,
            column: 3
          }
        },
        line: 291
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 301,
            column: 25
          },
          end: {
            line: 301,
            column: 26
          }
        },
        loc: {
          start: {
            line: 301,
            column: 32
          },
          end: {
            line: 301,
            column: 54
          }
        },
        line: 301
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 305,
            column: 2
          },
          end: {
            line: 305,
            column: 3
          }
        },
        loc: {
          start: {
            line: 305,
            column: 18
          },
          end: {
            line: 310,
            column: 3
          }
        },
        line: 305
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 335,
            column: 13
          },
          end: {
            line: 335,
            column: 14
          }
        },
        loc: {
          start: {
            line: 335,
            column: 83
          },
          end: {
            line: 337,
            column: 3
          }
        },
        line: 335
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 340,
            column: 13
          },
          end: {
            line: 340,
            column: 14
          }
        },
        loc: {
          start: {
            line: 340,
            column: 35
          },
          end: {
            line: 351,
            column: 3
          }
        },
        line: 340
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 342,
            column: 33
          },
          end: {
            line: 342,
            column: 34
          }
        },
        loc: {
          start: {
            line: 342,
            column: 49
          },
          end: {
            line: 347,
            column: 7
          }
        },
        line: 342
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 354,
            column: 15
          },
          end: {
            line: 354,
            column: 16
          }
        },
        loc: {
          start: {
            line: 354,
            column: 45
          },
          end: {
            line: 365,
            column: 3
          }
        },
        line: 354
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 356,
            column: 29
          },
          end: {
            line: 356,
            column: 30
          }
        },
        loc: {
          start: {
            line: 356,
            column: 45
          },
          end: {
            line: 361,
            column: 7
          }
        },
        line: 356
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 368,
            column: 18
          },
          end: {
            line: 368,
            column: 19
          }
        },
        loc: {
          start: {
            line: 368,
            column: 25
          },
          end: {
            line: 372,
            column: 3
          }
        },
        line: 368
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 375,
            column: 12
          },
          end: {
            line: 375,
            column: 13
          }
        },
        loc: {
          start: {
            line: 375,
            column: 18
          },
          end: {
            line: 379,
            column: 3
          }
        },
        line: 375
      },
      "31": {
        name: "useCache",
        decl: {
          start: {
            line: 383,
            column: 16
          },
          end: {
            line: 383,
            column: 24
          }
        },
        loc: {
          start: {
            line: 385,
            column: 2
          },
          end: {
            line: 395,
            column: 1
          }
        },
        line: 385
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 387,
            column: 9
          },
          end: {
            line: 387,
            column: 10
          }
        },
        loc: {
          start: {
            line: 387,
            column: 26
          },
          end: {
            line: 387,
            column: 40
          }
        },
        line: 387
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 388,
            column: 9
          },
          end: {
            line: 388,
            column: 10
          }
        },
        loc: {
          start: {
            line: 389,
            column: 6
          },
          end: {
            line: 389,
            column: 37
          }
        },
        line: 389
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 390,
            column: 9
          },
          end: {
            line: 390,
            column: 10
          }
        },
        loc: {
          start: {
            line: 390,
            column: 26
          },
          end: {
            line: 390,
            column: 40
          }
        },
        line: 390
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 391,
            column: 12
          },
          end: {
            line: 391,
            column: 13
          }
        },
        loc: {
          start: {
            line: 391,
            column: 29
          },
          end: {
            line: 391,
            column: 46
          }
        },
        line: 391
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 392,
            column: 11
          },
          end: {
            line: 392,
            column: 12
          }
        },
        loc: {
          start: {
            line: 392,
            column: 17
          },
          end: {
            line: 392,
            column: 30
          }
        },
        line: 392
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 52,
            column: 4
          },
          end: {
            line: 52,
            column: 37
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 52,
            column: 35
          },
          end: {
            line: 52,
            column: 37
          }
        }],
        line: 52
      },
      "1": {
        loc: {
          start: {
            line: 53,
            column: 4
          },
          end: {
            line: 53,
            column: 46
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 53,
            column: 41
          },
          end: {
            line: 53,
            column: 46
          }
        }],
        line: 53
      },
      "2": {
        loc: {
          start: {
            line: 72,
            column: 4
          },
          end: {
            line: 75,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 4
          },
          end: {
            line: 75,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "3": {
        loc: {
          start: {
            line: 78,
            column: 4
          },
          end: {
            line: 82,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 78,
            column: 4
          },
          end: {
            line: 82,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 78
      },
      "4": {
        loc: {
          start: {
            line: 93,
            column: 42
          },
          end: {
            line: 93,
            column: 61
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 93,
            column: 59
          },
          end: {
            line: 93,
            column: 61
          }
        }],
        line: 93
      },
      "5": {
        loc: {
          start: {
            line: 95,
            column: 4
          },
          end: {
            line: 97,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 95,
            column: 4
          },
          end: {
            line: 97,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 95
      },
      "6": {
        loc: {
          start: {
            line: 102,
            column: 11
          },
          end: {
            line: 102,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 102,
            column: 11
          },
          end: {
            line: 102,
            column: 14
          }
        }, {
          start: {
            line: 102,
            column: 18
          },
          end: {
            line: 102,
            column: 40
          }
        }],
        line: 102
      },
      "7": {
        loc: {
          start: {
            line: 115,
            column: 4
          },
          end: {
            line: 115,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 115,
            column: 4
          },
          end: {
            line: 115,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 115
      },
      "8": {
        loc: {
          start: {
            line: 117,
            column: 4
          },
          end: {
            line: 120,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 117,
            column: 4
          },
          end: {
            line: 120,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 117
      },
      "9": {
        loc: {
          start: {
            line: 134,
            column: 6
          },
          end: {
            line: 137,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 134,
            column: 6
          },
          end: {
            line: 137,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 134
      },
      "10": {
        loc: {
          start: {
            line: 154,
            column: 15
          },
          end: {
            line: 154,
            column: 72
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 154,
            column: 35
          },
          end: {
            line: 154,
            column: 68
          }
        }, {
          start: {
            line: 154,
            column: 71
          },
          end: {
            line: 154,
            column: 72
          }
        }],
        line: 154
      },
      "11": {
        loc: {
          start: {
            line: 175,
            column: 4
          },
          end: {
            line: 175,
            column: 37
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 175,
            column: 4
          },
          end: {
            line: 175,
            column: 37
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 175
      },
      "12": {
        loc: {
          start: {
            line: 179,
            column: 4
          },
          end: {
            line: 192,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 180,
            column: 6
          },
          end: {
            line: 182,
            column: 13
          }
        }, {
          start: {
            line: 183,
            column: 6
          },
          end: {
            line: 185,
            column: 13
          }
        }, {
          start: {
            line: 186,
            column: 6
          },
          end: {
            line: 188,
            column: 13
          }
        }, {
          start: {
            line: 189,
            column: 6
          },
          end: {
            line: 191,
            column: 13
          }
        }],
        line: 179
      },
      "13": {
        loc: {
          start: {
            line: 194,
            column: 4
          },
          end: {
            line: 197,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 194,
            column: 4
          },
          end: {
            line: 197,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 194
      },
      "14": {
        loc: {
          start: {
            line: 206,
            column: 6
          },
          end: {
            line: 209,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 206,
            column: 6
          },
          end: {
            line: 209,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 206
      },
      "15": {
        loc: {
          start: {
            line: 221,
            column: 6
          },
          end: {
            line: 224,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 221,
            column: 6
          },
          end: {
            line: 224,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 221
      },
      "16": {
        loc: {
          start: {
            line: 233,
            column: 6
          },
          end: {
            line: 235,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 233,
            column: 6
          },
          end: {
            line: 235,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 233
      },
      "17": {
        loc: {
          start: {
            line: 246,
            column: 6
          },
          end: {
            line: 249,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 246,
            column: 6
          },
          end: {
            line: 249,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 246
      },
      "18": {
        loc: {
          start: {
            line: 257,
            column: 4
          },
          end: {
            line: 257,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 257,
            column: 4
          },
          end: {
            line: 257,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 257
      },
      "19": {
        loc: {
          start: {
            line: 259,
            column: 4
          },
          end: {
            line: 269,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 260,
            column: 6
          },
          end: {
            line: 262,
            column: 13
          }
        }, {
          start: {
            line: 263,
            column: 6
          },
          end: {
            line: 265,
            column: 13
          }
        }, {
          start: {
            line: 266,
            column: 6
          },
          end: {
            line: 268,
            column: 13
          }
        }],
        line: 259
      },
      "20": {
        loc: {
          start: {
            line: 296,
            column: 6
          },
          end: {
            line: 298,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 296,
            column: 6
          },
          end: {
            line: 298,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 296
      },
      "21": {
        loc: {
          start: {
            line: 306,
            column: 4
          },
          end: {
            line: 308,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 306,
            column: 4
          },
          end: {
            line: 308,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 306
      },
      "22": {
        loc: {
          start: {
            line: 343,
            column: 8
          },
          end: {
            line: 345,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 343,
            column: 8
          },
          end: {
            line: 345,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 343
      },
      "23": {
        loc: {
          start: {
            line: 357,
            column: 8
          },
          end: {
            line: 359,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 357,
            column: 8
          },
          end: {
            line: 359,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 357
      },
      "24": {
        loc: {
          start: {
            line: 357,
            column: 12
          },
          end: {
            line: 357,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 357,
            column: 12
          },
          end: {
            line: 357,
            column: 17
          }
        }, {
          start: {
            line: 357,
            column: 21
          },
          end: {
            line: 357,
            column: 44
          }
        }],
        line: 357
      },
      "25": {
        loc: {
          start: {
            line: 384,
            column: 2
          },
          end: {
            line: 384,
            column: 56
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 384,
            column: 28
          },
          end: {
            line: 384,
            column: 56
          }
        }],
        line: 384
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0, 0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c8fed1e1d306d9954a5aa18355920cf695ef0275"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_ck59h46it = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_ck59h46it();
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Advanced Caching System
 * 
 * This module provides a comprehensive caching solution with multiple strategies,
 * automatic invalidation, and performance optimizations.
 */

// Cache entry interface

// Cache configuration

// Cache metrics

// Cache strategies

class AdvancedCache {
  cache =
  /* istanbul ignore next */
  (cov_ck59h46it().s[0]++, new Map());
  metrics =
  /* istanbul ignore next */
  (cov_ck59h46it().s[1]++, {
    hits: 0,
    misses: 0,
    evictions: 0,
    size: 0,
    hitRate: 0
  });
  constructor(config =
  /* istanbul ignore next */
  (cov_ck59h46it().b[0][0]++, {}), evictionStrategy =
  /* istanbul ignore next */
  (cov_ck59h46it().b[1][0]++, 'lru')) {
    /* istanbul ignore next */
    cov_ck59h46it().f[0]++;
    cov_ck59h46it().s[2]++;
    this.config =
    /* istanbul ignore next */
    _objectSpread({
      maxSize: 100,
      defaultTTL: 5 * 60 * 1000,
      // 5 minutes
      cleanupInterval: 60 * 1000,
      // 1 minute
      enableMetrics: true
    }, config);
    /* istanbul ignore next */
    cov_ck59h46it().s[3]++;
    this.evictionStrategy = evictionStrategy;

    // Start cleanup timer
    /* istanbul ignore next */
    cov_ck59h46it().s[4]++;
    this.startCleanup();
  }

  // Get item from cache
  get(key) {
    /* istanbul ignore next */
    cov_ck59h46it().f[1]++;
    const entry =
    /* istanbul ignore next */
    (cov_ck59h46it().s[5]++, this.cache.get(key));
    /* istanbul ignore next */
    cov_ck59h46it().s[6]++;
    if (!entry) {
      /* istanbul ignore next */
      cov_ck59h46it().b[2][0]++;
      cov_ck59h46it().s[7]++;
      this.updateMetrics('miss');
      /* istanbul ignore next */
      cov_ck59h46it().s[8]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_ck59h46it().b[2][1]++;
    }

    // Check if expired
    cov_ck59h46it().s[9]++;
    if (this.isExpired(entry)) {
      /* istanbul ignore next */
      cov_ck59h46it().b[3][0]++;
      cov_ck59h46it().s[10]++;
      this.cache.delete(key);
      /* istanbul ignore next */
      cov_ck59h46it().s[11]++;
      this.updateMetrics('miss');
      /* istanbul ignore next */
      cov_ck59h46it().s[12]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_ck59h46it().b[3][1]++;
    }

    // Update access information
    cov_ck59h46it().s[13]++;
    entry.accessCount++;
    /* istanbul ignore next */
    cov_ck59h46it().s[14]++;
    entry.lastAccessed = Date.now();
    /* istanbul ignore next */
    cov_ck59h46it().s[15]++;
    this.updateMetrics('hit');
    /* istanbul ignore next */
    cov_ck59h46it().s[16]++;
    return entry.data;
  }

  // Set item in cache
  set(key, data, ttl, tags =
  /* istanbul ignore next */
  (cov_ck59h46it().b[4][0]++, [])) {
    /* istanbul ignore next */
    cov_ck59h46it().f[2]++;
    cov_ck59h46it().s[17]++;
    // Check if we need to evict items
    if (this.cache.size >= this.config.maxSize) {
      /* istanbul ignore next */
      cov_ck59h46it().b[5][0]++;
      cov_ck59h46it().s[18]++;
      this.evict();
    } else
    /* istanbul ignore next */
    {
      cov_ck59h46it().b[5][1]++;
    }
    const entry =
    /* istanbul ignore next */
    (cov_ck59h46it().s[19]++, {
      data,
      timestamp: Date.now(),
      ttl:
      /* istanbul ignore next */
      (cov_ck59h46it().b[6][0]++, ttl) ||
      /* istanbul ignore next */
      (cov_ck59h46it().b[6][1]++, this.config.defaultTTL),
      accessCount: 0,
      lastAccessed: Date.now(),
      tags
    });
    /* istanbul ignore next */
    cov_ck59h46it().s[20]++;
    this.cache.set(key, entry);
    /* istanbul ignore next */
    cov_ck59h46it().s[21]++;
    this.updateMetrics('set');
  }

  // Check if item exists and is valid
  has(key) {
    /* istanbul ignore next */
    cov_ck59h46it().f[3]++;
    const entry =
    /* istanbul ignore next */
    (cov_ck59h46it().s[22]++, this.cache.get(key));
    /* istanbul ignore next */
    cov_ck59h46it().s[23]++;
    if (!entry) {
      /* istanbul ignore next */
      cov_ck59h46it().b[7][0]++;
      cov_ck59h46it().s[24]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_ck59h46it().b[7][1]++;
    }
    cov_ck59h46it().s[25]++;
    if (this.isExpired(entry)) {
      /* istanbul ignore next */
      cov_ck59h46it().b[8][0]++;
      cov_ck59h46it().s[26]++;
      this.cache.delete(key);
      /* istanbul ignore next */
      cov_ck59h46it().s[27]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_ck59h46it().b[8][1]++;
    }
    cov_ck59h46it().s[28]++;
    return true;
  }

  // Delete item from cache
  delete(key) {
    /* istanbul ignore next */
    cov_ck59h46it().f[4]++;
    cov_ck59h46it().s[29]++;
    return this.cache.delete(key);
  }

  // Clear cache by tags
  clearByTags(tags) {
    /* istanbul ignore next */
    cov_ck59h46it().f[5]++;
    let cleared =
    /* istanbul ignore next */
    (cov_ck59h46it().s[30]++, 0);
    /* istanbul ignore next */
    cov_ck59h46it().s[31]++;
    for (const [key, entry] of this.cache.entries()) {
      /* istanbul ignore next */
      cov_ck59h46it().s[32]++;
      if (entry.tags.some(tag => {
        /* istanbul ignore next */
        cov_ck59h46it().f[6]++;
        cov_ck59h46it().s[33]++;
        return tags.includes(tag);
      })) {
        /* istanbul ignore next */
        cov_ck59h46it().b[9][0]++;
        cov_ck59h46it().s[34]++;
        this.cache.delete(key);
        /* istanbul ignore next */
        cov_ck59h46it().s[35]++;
        cleared++;
      } else
      /* istanbul ignore next */
      {
        cov_ck59h46it().b[9][1]++;
      }
    }
    /* istanbul ignore next */
    cov_ck59h46it().s[36]++;
    return cleared;
  }

  // Clear all cache
  clear() {
    /* istanbul ignore next */
    cov_ck59h46it().f[7]++;
    cov_ck59h46it().s[37]++;
    this.cache.clear();
    /* istanbul ignore next */
    cov_ck59h46it().s[38]++;
    this.resetMetrics();
  }

  // Get cache metrics
  getMetrics() {
    /* istanbul ignore next */
    cov_ck59h46it().f[8]++;
    const totalRequests =
    /* istanbul ignore next */
    (cov_ck59h46it().s[39]++, this.metrics.hits + this.metrics.misses);
    /* istanbul ignore next */
    cov_ck59h46it().s[40]++;
    return /* istanbul ignore next */_objectSpread(_objectSpread({}, this.metrics), {}, {
      size: this.cache.size,
      hitRate: totalRequests > 0 ?
      /* istanbul ignore next */
      (cov_ck59h46it().b[10][0]++, this.metrics.hits / totalRequests) :
      /* istanbul ignore next */
      (cov_ck59h46it().b[10][1]++, 0)
    });
  }

  // Get all keys
  keys() {
    /* istanbul ignore next */
    cov_ck59h46it().f[9]++;
    cov_ck59h46it().s[41]++;
    return Array.from(this.cache.keys());
  }

  // Get cache size
  size() {
    /* istanbul ignore next */
    cov_ck59h46it().f[10]++;
    cov_ck59h46it().s[42]++;
    return this.cache.size;
  }

  // Check if entry is expired
  isExpired(entry) {
    /* istanbul ignore next */
    cov_ck59h46it().f[11]++;
    cov_ck59h46it().s[43]++;
    return Date.now() - entry.timestamp > entry.ttl;
  }

  // Evict items based on strategy
  evict() {
    /* istanbul ignore next */
    cov_ck59h46it().f[12]++;
    cov_ck59h46it().s[44]++;
    if (this.cache.size === 0) {
      /* istanbul ignore next */
      cov_ck59h46it().b[11][0]++;
      cov_ck59h46it().s[45]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_ck59h46it().b[11][1]++;
    }
    let keyToEvict =
    /* istanbul ignore next */
    (cov_ck59h46it().s[46]++, null);
    /* istanbul ignore next */
    cov_ck59h46it().s[47]++;
    switch (this.evictionStrategy) {
      case 'lru':
        /* istanbul ignore next */
        cov_ck59h46it().b[12][0]++;
        cov_ck59h46it().s[48]++;
        keyToEvict = this.findLRUKey();
        /* istanbul ignore next */
        cov_ck59h46it().s[49]++;
        break;
      case 'lfu':
        /* istanbul ignore next */
        cov_ck59h46it().b[12][1]++;
        cov_ck59h46it().s[50]++;
        keyToEvict = this.findLFUKey();
        /* istanbul ignore next */
        cov_ck59h46it().s[51]++;
        break;
      case 'ttl':
        /* istanbul ignore next */
        cov_ck59h46it().b[12][2]++;
        cov_ck59h46it().s[52]++;
        keyToEvict = this.findExpiredKey();
        /* istanbul ignore next */
        cov_ck59h46it().s[53]++;
        break;
      case 'fifo':
        /* istanbul ignore next */
        cov_ck59h46it().b[12][3]++;
        cov_ck59h46it().s[54]++;
        keyToEvict = this.findFIFOKey();
        /* istanbul ignore next */
        cov_ck59h46it().s[55]++;
        break;
    }
    /* istanbul ignore next */
    cov_ck59h46it().s[56]++;
    if (keyToEvict) {
      /* istanbul ignore next */
      cov_ck59h46it().b[13][0]++;
      cov_ck59h46it().s[57]++;
      this.cache.delete(keyToEvict);
      /* istanbul ignore next */
      cov_ck59h46it().s[58]++;
      this.metrics.evictions++;
    } else
    /* istanbul ignore next */
    {
      cov_ck59h46it().b[13][1]++;
    }
  }

  // Find least recently used key
  findLRUKey() {
    /* istanbul ignore next */
    cov_ck59h46it().f[13]++;
    let oldestKey =
    /* istanbul ignore next */
    (cov_ck59h46it().s[59]++, null);
    let oldestTime =
    /* istanbul ignore next */
    (cov_ck59h46it().s[60]++, Date.now());
    /* istanbul ignore next */
    cov_ck59h46it().s[61]++;
    for (const [key, entry] of this.cache.entries()) {
      /* istanbul ignore next */
      cov_ck59h46it().s[62]++;
      if (entry.lastAccessed < oldestTime) {
        /* istanbul ignore next */
        cov_ck59h46it().b[14][0]++;
        cov_ck59h46it().s[63]++;
        oldestTime = entry.lastAccessed;
        /* istanbul ignore next */
        cov_ck59h46it().s[64]++;
        oldestKey = key;
      } else
      /* istanbul ignore next */
      {
        cov_ck59h46it().b[14][1]++;
      }
    }
    /* istanbul ignore next */
    cov_ck59h46it().s[65]++;
    return oldestKey;
  }

  // Find least frequently used key
  findLFUKey() {
    /* istanbul ignore next */
    cov_ck59h46it().f[14]++;
    let leastUsedKey =
    /* istanbul ignore next */
    (cov_ck59h46it().s[66]++, null);
    let leastCount =
    /* istanbul ignore next */
    (cov_ck59h46it().s[67]++, Infinity);
    /* istanbul ignore next */
    cov_ck59h46it().s[68]++;
    for (const [key, entry] of this.cache.entries()) {
      /* istanbul ignore next */
      cov_ck59h46it().s[69]++;
      if (entry.accessCount < leastCount) {
        /* istanbul ignore next */
        cov_ck59h46it().b[15][0]++;
        cov_ck59h46it().s[70]++;
        leastCount = entry.accessCount;
        /* istanbul ignore next */
        cov_ck59h46it().s[71]++;
        leastUsedKey = key;
      } else
      /* istanbul ignore next */
      {
        cov_ck59h46it().b[15][1]++;
      }
    }
    /* istanbul ignore next */
    cov_ck59h46it().s[72]++;
    return leastUsedKey;
  }

  // Find expired key
  findExpiredKey() {
    /* istanbul ignore next */
    cov_ck59h46it().f[15]++;
    cov_ck59h46it().s[73]++;
    for (const [key, entry] of this.cache.entries()) {
      /* istanbul ignore next */
      cov_ck59h46it().s[74]++;
      if (this.isExpired(entry)) {
        /* istanbul ignore next */
        cov_ck59h46it().b[16][0]++;
        cov_ck59h46it().s[75]++;
        return key;
      } else
      /* istanbul ignore next */
      {
        cov_ck59h46it().b[16][1]++;
      }
    }
    /* istanbul ignore next */
    cov_ck59h46it().s[76]++;
    return null;
  }

  // Find first in, first out key
  findFIFOKey() {
    /* istanbul ignore next */
    cov_ck59h46it().f[16]++;
    let oldestKey =
    /* istanbul ignore next */
    (cov_ck59h46it().s[77]++, null);
    let oldestTime =
    /* istanbul ignore next */
    (cov_ck59h46it().s[78]++, Date.now());
    /* istanbul ignore next */
    cov_ck59h46it().s[79]++;
    for (const [key, entry] of this.cache.entries()) {
      /* istanbul ignore next */
      cov_ck59h46it().s[80]++;
      if (entry.timestamp < oldestTime) {
        /* istanbul ignore next */
        cov_ck59h46it().b[17][0]++;
        cov_ck59h46it().s[81]++;
        oldestTime = entry.timestamp;
        /* istanbul ignore next */
        cov_ck59h46it().s[82]++;
        oldestKey = key;
      } else
      /* istanbul ignore next */
      {
        cov_ck59h46it().b[17][1]++;
      }
    }
    /* istanbul ignore next */
    cov_ck59h46it().s[83]++;
    return oldestKey;
  }

  // Update metrics
  updateMetrics(operation) {
    /* istanbul ignore next */
    cov_ck59h46it().f[17]++;
    cov_ck59h46it().s[84]++;
    if (!this.config.enableMetrics) {
      /* istanbul ignore next */
      cov_ck59h46it().b[18][0]++;
      cov_ck59h46it().s[85]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_ck59h46it().b[18][1]++;
    }
    cov_ck59h46it().s[86]++;
    switch (operation) {
      case 'hit':
        /* istanbul ignore next */
        cov_ck59h46it().b[19][0]++;
        cov_ck59h46it().s[87]++;
        this.metrics.hits++;
        /* istanbul ignore next */
        cov_ck59h46it().s[88]++;
        break;
      case 'miss':
        /* istanbul ignore next */
        cov_ck59h46it().b[19][1]++;
        cov_ck59h46it().s[89]++;
        this.metrics.misses++;
        /* istanbul ignore next */
        cov_ck59h46it().s[90]++;
        break;
      case 'set':
        /* istanbul ignore next */
        cov_ck59h46it().b[19][2]++;
        cov_ck59h46it().s[91]++;
        // No specific metric for set operations
        break;
    }
  }

  // Reset metrics
  resetMetrics() {
    /* istanbul ignore next */
    cov_ck59h46it().f[18]++;
    cov_ck59h46it().s[92]++;
    this.metrics = {
      hits: 0,
      misses: 0,
      evictions: 0,
      size: 0,
      hitRate: 0
    };
  }

  // Start cleanup timer
  startCleanup() {
    /* istanbul ignore next */
    cov_ck59h46it().f[19]++;
    cov_ck59h46it().s[93]++;
    this.cleanupTimer = setInterval(() => {
      /* istanbul ignore next */
      cov_ck59h46it().f[20]++;
      cov_ck59h46it().s[94]++;
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  // Cleanup expired entries
  cleanup() {
    /* istanbul ignore next */
    cov_ck59h46it().f[21]++;
    const now =
    /* istanbul ignore next */
    (cov_ck59h46it().s[95]++, Date.now());
    const keysToDelete =
    /* istanbul ignore next */
    (cov_ck59h46it().s[96]++, []);
    /* istanbul ignore next */
    cov_ck59h46it().s[97]++;
    for (const [key, entry] of this.cache.entries()) {
      /* istanbul ignore next */
      cov_ck59h46it().s[98]++;
      if (now - entry.timestamp > entry.ttl) {
        /* istanbul ignore next */
        cov_ck59h46it().b[20][0]++;
        cov_ck59h46it().s[99]++;
        keysToDelete.push(key);
      } else
      /* istanbul ignore next */
      {
        cov_ck59h46it().b[20][1]++;
      }
    }
    /* istanbul ignore next */
    cov_ck59h46it().s[100]++;
    keysToDelete.forEach(key => {
      /* istanbul ignore next */
      cov_ck59h46it().f[22]++;
      cov_ck59h46it().s[101]++;
      return this.cache.delete(key);
    });
  }

  // Destroy cache and cleanup
  destroy() {
    /* istanbul ignore next */
    cov_ck59h46it().f[23]++;
    cov_ck59h46it().s[102]++;
    if (this.cleanupTimer) {
      /* istanbul ignore next */
      cov_ck59h46it().b[21][0]++;
      cov_ck59h46it().s[103]++;
      clearInterval(this.cleanupTimer);
    } else
    /* istanbul ignore next */
    {
      cov_ck59h46it().b[21][1]++;
    }
    cov_ck59h46it().s[104]++;
    this.clear();
  }
}

// Global cache instances
/* istanbul ignore next */
exports.AdvancedCache = AdvancedCache;
const apiCache =
/* istanbul ignore next */
exports.apiCache = (cov_ck59h46it().s[105]++, new AdvancedCache({
  maxSize: 200,
  defaultTTL: 5 * 60 * 1000,
  // 5 minutes
  enableMetrics: true
}, 'lru'));
const componentCache =
/* istanbul ignore next */
exports.componentCache = (cov_ck59h46it().s[106]++, new AdvancedCache({
  maxSize: 50,
  defaultTTL: 10 * 60 * 1000,
  // 10 minutes
  enableMetrics: true
}, 'lfu'));
const userDataCache =
/* istanbul ignore next */
exports.userDataCache = (cov_ck59h46it().s[107]++, new AdvancedCache({
  maxSize: 100,
  defaultTTL: 15 * 60 * 1000,
  // 15 minutes
  enableMetrics: true
}, 'lru'));

// Cache utilities
const cacheUtils =
/* istanbul ignore next */
exports.cacheUtils = (cov_ck59h46it().s[108]++, {
  // Create cache key from parameters
  createKey: (prefix, ...params) => {
    /* istanbul ignore next */
    cov_ck59h46it().f[24]++;
    cov_ck59h46it().s[109]++;
    return `${prefix}:${params.join(':')}`;
  },
  // Serialize object for caching
  serialize: obj => {
    /* istanbul ignore next */
    cov_ck59h46it().f[25]++;
    cov_ck59h46it().s[110]++;
    try {
      /* istanbul ignore next */
      cov_ck59h46it().s[111]++;
      return JSON.stringify(obj, (key, value) => {
        /* istanbul ignore next */
        cov_ck59h46it().f[26]++;
        cov_ck59h46it().s[112]++;
        if (value instanceof Date) {
          /* istanbul ignore next */
          cov_ck59h46it().b[22][0]++;
          cov_ck59h46it().s[113]++;
          return {
            __type: 'Date',
            value: value.toISOString()
          };
        } else
        /* istanbul ignore next */
        {
          cov_ck59h46it().b[22][1]++;
        }
        cov_ck59h46it().s[114]++;
        return value;
      });
    } catch {
      /* istanbul ignore next */
      cov_ck59h46it().s[115]++;
      return String(obj);
    }
  },
  // Deserialize object from cache
  deserialize: str => {
    /* istanbul ignore next */
    cov_ck59h46it().f[27]++;
    cov_ck59h46it().s[116]++;
    try {
      /* istanbul ignore next */
      cov_ck59h46it().s[117]++;
      return JSON.parse(str, (key, value) => {
        /* istanbul ignore next */
        cov_ck59h46it().f[28]++;
        cov_ck59h46it().s[118]++;
        if (
        /* istanbul ignore next */
        (cov_ck59h46it().b[24][0]++, value) &&
        /* istanbul ignore next */
        (cov_ck59h46it().b[24][1]++, value.__type === 'Date')) {
          /* istanbul ignore next */
          cov_ck59h46it().b[23][0]++;
          cov_ck59h46it().s[119]++;
          return new Date(value.value);
        } else
        /* istanbul ignore next */
        {
          cov_ck59h46it().b[23][1]++;
        }
        cov_ck59h46it().s[120]++;
        return value;
      });
    } catch {
      /* istanbul ignore next */
      cov_ck59h46it().s[121]++;
      return null;
    }
  },
  // Get cache statistics
  getGlobalStats: () => {
    /* istanbul ignore next */
    cov_ck59h46it().f[29]++;
    cov_ck59h46it().s[122]++;
    return {
      api: apiCache.getMetrics(),
      component: componentCache.getMetrics(),
      userData: userDataCache.getMetrics()
    };
  },
  // Clear all caches
  clearAll: () => {
    /* istanbul ignore next */
    cov_ck59h46it().f[30]++;
    cov_ck59h46it().s[123]++;
    apiCache.clear();
    /* istanbul ignore next */
    cov_ck59h46it().s[124]++;
    componentCache.clear();
    /* istanbul ignore next */
    cov_ck59h46it().s[125]++;
    userDataCache.clear();
  }
});

// React hook for using cache
function useCache(cache =
/* istanbul ignore next */
(cov_ck59h46it().b[25][0]++, apiCache)) {
  /* istanbul ignore next */
  cov_ck59h46it().f[31]++;
  cov_ck59h46it().s[126]++;
  return {
    get: key => {
      /* istanbul ignore next */
      cov_ck59h46it().f[32]++;
      cov_ck59h46it().s[127]++;
      return cache.get(key);
    },
    set: (key, data, ttl, tags) => {
      /* istanbul ignore next */
      cov_ck59h46it().f[33]++;
      cov_ck59h46it().s[128]++;
      return cache.set(key, data, ttl, tags);
    },
    has: key => {
      /* istanbul ignore next */
      cov_ck59h46it().f[34]++;
      cov_ck59h46it().s[129]++;
      return cache.has(key);
    },
    delete: key => {
      /* istanbul ignore next */
      cov_ck59h46it().f[35]++;
      cov_ck59h46it().s[130]++;
      return cache.delete(key);
    },
    clear: () => {
      /* istanbul ignore next */
      cov_ck59h46it().f[36]++;
      cov_ck59h46it().s[131]++;
      return cache.clear();
    },
    metrics: cache.getMetrics()
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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