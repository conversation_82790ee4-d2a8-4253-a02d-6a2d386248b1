/**
 * Admin API endpoint to create synchronization tracking tables
 * These tables track sync batches and handle conflicts in the lazy synchronization pattern
 */

import { NextRequest } from 'next/server';
import { executeQuery } from '@/lib/database';
import { 
  createSuccessResponse, 
  createErrorR<PERSON>ponse, 
  ApiErrorCode, 
  HttpStatus,
  withErrorHandling 
} from '@/lib/api-response';

export const POST = withErrorHandling(async (request: NextRequest) => {
  try {
    console.log('Creating synchronization tracking tables...');
    
    // Create enum types for sync tracking
    await executeQuery(`
      DO $$ BEGIN
          CREATE TYPE metadata.sync_entity_type AS ENUM ('vendor', 'product', 'product_version');
      EXCEPTION
          WHEN duplicate_object THEN null;
      END $$;
    `);
    console.log('✓ sync_entity_type enum created');
    
    await executeQuery(`
      DO $$ BEGIN
          CREATE TYPE metadata.sync_batch_status AS ENUM ('running', 'completed', 'failed', 'partial');
      EXCEPTION
          WHEN duplicate_object THEN null;
      END $$;
    `);
    console.log('✓ sync_batch_status enum created');
    
    await executeQuery(`
      DO $$ BEGIN
          CREATE TYPE metadata.sync_conflict_type AS ENUM ('multiple_matches', 'data_mismatch', 'validation_error');
      EXCEPTION
          WHEN duplicate_object THEN null;
      END $$;
    `);
    console.log('✓ sync_conflict_type enum created');
    
    await executeQuery(`
      DO $$ BEGIN
          CREATE TYPE metadata.sync_resolution_status AS ENUM ('pending', 'resolved', 'rejected');
      EXCEPTION
          WHEN duplicate_object THEN null;
      END $$;
    `);
    console.log('✓ sync_resolution_status enum created');
    
    await executeQuery(`
      DO $$ BEGIN
          CREATE TYPE metadata.sync_resolution_action AS ENUM ('link_existing', 'create_new', 'ignore');
      EXCEPTION
          WHEN duplicate_object THEN null;
      END $$;
    `);
    console.log('✓ sync_resolution_action enum created');
    
    // Create sync_batches table
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS metadata.sync_batches (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          tenant_id UUID NOT NULL, -- References the tenant (could be linked to clients table)
          entity_type metadata.sync_entity_type NOT NULL,
          status metadata.sync_batch_status DEFAULT 'running',
          total_records INTEGER DEFAULT 0,
          processed_records INTEGER DEFAULT 0,
          matched_records INTEGER DEFAULT 0,
          new_records INTEGER DEFAULT 0,
          conflict_records INTEGER DEFAULT 0,
          started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          completed_at TIMESTAMP,
          error_message TEXT, -- Store error details for failed batches
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          
          -- Constraints
          CONSTRAINT sync_batches_records_check CHECK (
              processed_records <= total_records AND
              matched_records + new_records + conflict_records <= processed_records
          ),
          CONSTRAINT sync_batches_completion_check CHECK (
              (status = 'completed' AND completed_at IS NOT NULL) OR
              (status != 'completed')
          )
      )
    `);
    console.log('✓ sync_batches table created');
    
    // Create sync_conflicts table
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS metadata.sync_conflicts (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          batch_id UUID NOT NULL REFERENCES metadata.sync_batches(id) ON DELETE CASCADE,
          tenant_entity_id UUID NOT NULL, -- The tenant record ID (from tenant_vendors, tenant_products, etc.)
          entity_type metadata.sync_entity_type NOT NULL,
          conflict_type metadata.sync_conflict_type NOT NULL,
          suggested_matches JSONB DEFAULT '[]', -- Array of potential global matches with scores
          tenant_data JSONB NOT NULL, -- Snapshot of tenant record at time of conflict
          resolution_status metadata.sync_resolution_status DEFAULT 'pending',
          resolved_by UUID, -- User who resolved the conflict
          resolved_at TIMESTAMP,
          resolution_action metadata.sync_resolution_action,
          resolution_data JSONB, -- Additional data about the resolution
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          
          -- Constraints
          CONSTRAINT sync_conflicts_resolution_check CHECK (
              (resolution_status = 'resolved' AND resolved_by IS NOT NULL AND resolved_at IS NOT NULL AND resolution_action IS NOT NULL) OR
              (resolution_status != 'resolved')
          )
      )
    `);
    console.log('✓ sync_conflicts table created');
    
    // Create indexes for performance
    await executeQuery('CREATE INDEX IF NOT EXISTS idx_sync_batches_tenant_id ON metadata.sync_batches(tenant_id)');
    await executeQuery('CREATE INDEX IF NOT EXISTS idx_sync_batches_entity_type ON metadata.sync_batches(entity_type)');
    await executeQuery('CREATE INDEX IF NOT EXISTS idx_sync_batches_status ON metadata.sync_batches(status)');
    await executeQuery('CREATE INDEX IF NOT EXISTS idx_sync_batches_started_at ON metadata.sync_batches(started_at DESC)');
    
    await executeQuery('CREATE INDEX IF NOT EXISTS idx_sync_conflicts_batch_id ON metadata.sync_conflicts(batch_id)');
    await executeQuery('CREATE INDEX IF NOT EXISTS idx_sync_conflicts_tenant_entity_id ON metadata.sync_conflicts(tenant_entity_id)');
    await executeQuery('CREATE INDEX IF NOT EXISTS idx_sync_conflicts_entity_type ON metadata.sync_conflicts(entity_type)');
    await executeQuery('CREATE INDEX IF NOT EXISTS idx_sync_conflicts_resolution_status ON metadata.sync_conflicts(resolution_status)');
    await executeQuery('CREATE INDEX IF NOT EXISTS idx_sync_conflicts_conflict_type ON metadata.sync_conflicts(conflict_type)');
    await executeQuery('CREATE INDEX IF NOT EXISTS idx_sync_conflicts_created_at ON metadata.sync_conflicts(created_at DESC)');
    console.log('✓ Sync tracking table indexes created');
    
    // Create update triggers for updated_at timestamps
    await executeQuery(`
      CREATE OR REPLACE FUNCTION metadata.update_sync_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = CURRENT_TIMESTAMP;
          RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);
    
    await executeQuery(`
      DROP TRIGGER IF EXISTS update_sync_batches_updated_at ON metadata.sync_batches;
      CREATE TRIGGER update_sync_batches_updated_at
          BEFORE UPDATE ON metadata.sync_batches
          FOR EACH ROW
          EXECUTE FUNCTION metadata.update_sync_updated_at_column();
    `);
    
    await executeQuery(`
      DROP TRIGGER IF EXISTS update_sync_conflicts_updated_at ON metadata.sync_conflicts;
      CREATE TRIGGER update_sync_conflicts_updated_at
          BEFORE UPDATE ON metadata.sync_conflicts
          FOR EACH ROW
          EXECUTE FUNCTION metadata.update_sync_updated_at_column();
    `);
    console.log('✓ Sync tracking update triggers created');
    
    // Create a view for sync statistics
    await executeQuery(`
      CREATE OR REPLACE VIEW metadata.sync_statistics AS
      SELECT 
          tenant_id,
          entity_type,
          COUNT(*) as total_batches,
          COUNT(*) FILTER (WHERE status = 'completed') as completed_batches,
          COUNT(*) FILTER (WHERE status = 'failed') as failed_batches,
          COUNT(*) FILTER (WHERE status = 'running') as running_batches,
          SUM(total_records) as total_records_processed,
          SUM(matched_records) as total_matched,
          SUM(new_records) as total_new,
          SUM(conflict_records) as total_conflicts,
          MAX(started_at) as last_sync_started,
          MAX(completed_at) as last_sync_completed
      FROM metadata.sync_batches
      GROUP BY tenant_id, entity_type
    `);
    console.log('✓ Sync statistics view created');
    
    // Create a view for pending conflicts summary
    await executeQuery(`
      CREATE OR REPLACE VIEW metadata.pending_conflicts_summary AS
      SELECT 
          sb.tenant_id,
          sc.entity_type,
          sc.conflict_type,
          COUNT(*) as conflict_count,
          MIN(sc.created_at) as oldest_conflict,
          MAX(sc.created_at) as newest_conflict
      FROM metadata.sync_conflicts sc
      JOIN metadata.sync_batches sb ON sc.batch_id = sb.id
      WHERE sc.resolution_status = 'pending'
      GROUP BY sb.tenant_id, sc.entity_type, sc.conflict_type
      ORDER BY sb.tenant_id, sc.entity_type, sc.conflict_type
    `);
    console.log('✓ Pending conflicts summary view created');
    
    console.log('✅ All synchronization tracking tables created successfully!');
    
    return createSuccessResponse(
      { 
        message: 'Synchronization tracking tables created successfully',
        tables: ['sync_batches', 'sync_conflicts'],
        views: ['sync_statistics', 'pending_conflicts_summary'],
        enums: ['sync_entity_type', 'sync_batch_status', 'sync_conflict_type', 'sync_resolution_status', 'sync_resolution_action']
      },
      'Synchronization tracking system has been created'
    );
    
  } catch (error) {
    console.error('❌ Error creating synchronization tracking tables:', error);
    return createErrorResponse(
      'Failed to create synchronization tracking tables',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});
