/**
 * Performance Optimization Utilities
 * 
 * This module provides utilities for optimizing React performance including
 * memoization helpers, debouncing, throttling, and performance monitoring.
 */

import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'

// Debounce hook for expensive operations
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// Throttle hook for frequent events
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastRun = useRef(Date.now())

  return useCallback(
    ((...args) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args)
        lastRun.current = Date.now()
      }
    }) as T,
    [callback, delay]
  )
}

// Memoized callback with dependency optimization
export function useOptimizedCallback<T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList
): T {
  return useCallback(callback, deps)
}

// Memoized value with deep comparison
export function useDeepMemo<T>(factory: () => T, deps: React.DependencyList): T {
  const ref = useRef<{ deps: React.DependencyList; value: T }>()

  if (!ref.current || !areEqual(ref.current.deps, deps)) {
    ref.current = { deps, value: factory() }
  }

  return ref.current.value
}

// Deep equality check for dependencies
function areEqual(a: React.DependencyList, b: React.DependencyList): boolean {
  if (a.length !== b.length) return false
  
  for (let i = 0; i < a.length; i++) {
    if (!Object.is(a[i], b[i])) {
      return false
    }
  }
  
  return true
}

// Performance monitoring hook
export function usePerformanceMonitor(name: string) {
  const startTime = useRef<number>()
  const renderCount = useRef(0)

  useEffect(() => {
    renderCount.current += 1
    
    if (process.env.NODE_ENV === 'development') {
      if (!startTime.current) {
        startTime.current = performance.now()
      }
      
      const endTime = performance.now()
      const duration = endTime - startTime.current
      
      if (duration > 16) { // More than one frame (16ms)
        console.warn(`Slow render detected in ${name}: ${duration.toFixed(2)}ms (render #${renderCount.current})`)
      }
      
      startTime.current = endTime
    }
  })

  return { renderCount: renderCount.current }
}

// Intersection Observer hook for lazy loading
export function useIntersectionObserver(
  options: IntersectionObserverInit = {}
): [React.RefObject<HTMLElement>, boolean] {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    const element = ref.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting)
      },
      {
        threshold: 0.1,
        ...options,
      }
    )

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [options])

  return [ref, isIntersecting]
}

// Virtual scrolling hook for large lists
export function useVirtualScrolling<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number
) {
  const [scrollTop, setScrollTop] = useState(0)

  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight)
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    )

    return {
      startIndex,
      endIndex,
      items: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight,
    }
  }, [items, itemHeight, containerHeight, scrollTop])

  const handleScroll = useCallback((e: React.UIEvent<HTMLElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }, [])

  return {
    ...visibleItems,
    handleScroll,
  }
}

// Optimized state updater that batches updates
export function useBatchedState<T>(
  initialState: T
): [T, (updater: (prev: T) => T) => void] {
  const [state, setState] = useState(initialState)
  const pendingUpdates = useRef<Array<(prev: T) => T>>([])
  const timeoutRef = useRef<NodeJS.Timeout>()

  const batchedSetState = useCallback((updater: (prev: T) => T) => {
    pendingUpdates.current.push(updater)

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    timeoutRef.current = setTimeout(() => {
      setState((prevState) => {
        let newState = prevState
        for (const update of pendingUpdates.current) {
          newState = update(newState)
        }
        pendingUpdates.current = []
        return newState
      })
    }, 0)
  }, [])

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return [state, batchedSetState]
}

// Memory usage monitoring
export function useMemoryMonitor(componentName: string) {
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && 'memory' in performance) {
      const checkMemory = () => {
        const memory = (performance as any).memory
        if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
          console.warn(`High memory usage detected in ${componentName}:`, {
            used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
            limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`,
          })
        }
      }

      const interval = setInterval(checkMemory, 5000)
      return () => clearInterval(interval)
    }
  }, [componentName])
}

// Optimized event handler creator
export function createOptimizedEventHandler<T extends Event>(
  handler: (event: T) => void,
  options: { passive?: boolean; capture?: boolean } = {}
) {
  return useCallback(
    (event: T) => {
      if (options.passive !== false) {
        // For passive events, we can optimize by not calling preventDefault
        handler(event)
      } else {
        handler(event)
      }
    },
    [handler, options]
  )
}

// Component render optimization checker
export function useRenderOptimization(componentName: string, props: Record<string, any>) {
  const previousProps = useRef<Record<string, any>>()
  const renderCount = useRef(0)

  useEffect(() => {
    renderCount.current += 1

    if (process.env.NODE_ENV === 'development') {
      if (previousProps.current) {
        const changedProps = Object.keys(props).filter(
          key => !Object.is(props[key], previousProps.current![key])
        )

        if (changedProps.length === 0) {
          console.warn(`Unnecessary re-render in ${componentName} (render #${renderCount.current})`)
        } else if (changedProps.length > 0) {
          console.log(`${componentName} re-rendered due to:`, changedProps)
        }
      }

      previousProps.current = { ...props }
    }
  })

  return renderCount.current
}



// Performance timing utilities
export const performanceUtils = {
  // Mark the start of a performance measurement
  mark: (name: string) => {
    if (typeof performance !== 'undefined' && performance.mark) {
      performance.mark(`${name}-start`)
    }
  },

  // Mark the end and measure performance
  measure: (name: string) => {
    if (typeof performance !== 'undefined' && performance.mark && performance.measure) {
      performance.mark(`${name}-end`)
      performance.measure(name, `${name}-start`, `${name}-end`)
      
      const entries = performance.getEntriesByName(name)
      if (entries.length > 0) {
        const duration = entries[entries.length - 1].duration
        if (duration > 100) { // Log if operation takes more than 100ms
          console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`)
        }
      }
    }
  },

  // Clear performance marks and measures
  clear: (name: string) => {
    if (typeof performance !== 'undefined') {
      performance.clearMarks?.(`${name}-start`)
      performance.clearMarks?.(`${name}-end`)
      performance.clearMeasures?.(name)
    }
  },
}
