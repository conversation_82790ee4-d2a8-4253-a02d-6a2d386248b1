6dd8c9a6283724f0705e9b8d53b0d651
"use strict";

_getJestObj().mock('@/lib/db', () => ({
  db: mockDb
}));

// Mock JWT validation

_getJestObj().mock('@/lib/jwt-validator', () => ({
  validateJwtToken: mockValidateJwtToken
}));
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _globals = require("@jest/globals");
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Dashboard API Tests
 * 
 * Tests for dashboard API endpoints including authentication,
 * data validation, and error handling
 */
// Mock the database
const mockDb = {
  query: _globals.jest.fn(),
  end: _globals.jest.fn()
};
const mockValidateJwtToken = _globals.jest.fn();
describe('Dashboard API Endpoints', () => {
  let mockRequest;
  let mockResponse;
  beforeEach(() => {
    _globals.jest.clearAllMocks();
    mockRequest = {
      method: 'GET',
      headers: {
        authorization: 'Bearer valid-token'
      },
      cookies: {},
      query: {},
      body: {}
    };
    mockResponse = {
      status: _globals.jest.fn().mockReturnThis(),
      json: _globals.jest.fn().mockReturnThis(),
      setHeader: _globals.jest.fn().mockReturnThis()
    };

    // Mock successful JWT validation
    mockValidateJwtToken.mockResolvedValue({
      sub: 'test-user-id',
      email: '<EMAIL>',
      'custom:tenant_id': 'test-tenant-id'
    });
  });
  describe('GET /api/dashboard/stats', () => {
    const mockStatsData = {
      total_renewals: 25,
      renewals_due: 5,
      vendors: 12,
      annual_spend: 125000
    };
    beforeEach(() => {
      mockDb.query.mockResolvedValue({
        rows: [mockStatsData]
      });
    });
    it('should return dashboard stats successfully', async () => {
      // Import the handler dynamically to ensure mocks are applied
      const {
        default: handler
      } = await Promise.resolve().then(() => _interopRequireWildcard(require('@/app/api/dashboard/stats/route')));
      await handler(mockRequest);
      expect(mockValidateJwtToken).toHaveBeenCalledWith('valid-token', expect.any(String), expect.any(String));
      expect(mockDb.query).toHaveBeenCalledWith(expect.stringContaining('SELECT'), ['test-tenant-id']);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: {
          totalRenewals: 25,
          renewalsDue: 5,
          vendors: 12,
          annualSpend: '$125,000'
        }
      });
    });
    it('should handle missing authorization header', async () => {
      delete mockRequest.headers.authorization;
      const {
        default: handler
      } = await Promise.resolve().then(() => _interopRequireWildcard(require('@/app/api/dashboard/stats/route')));
      await handler(mockRequest);
      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Authorization token required'
      });
    });
    it('should handle invalid JWT token', async () => {
      mockValidateJwtToken.mockRejectedValue(new Error('Invalid token'));
      const {
        default: handler
      } = await Promise.resolve().then(() => _interopRequireWildcard(require('@/app/api/dashboard/stats/route')));
      await handler(mockRequest);
      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Invalid or expired token'
      });
    });
    it('should handle database errors', async () => {
      mockDb.query.mockRejectedValue(new Error('Database connection failed'));
      const {
        default: handler
      } = await Promise.resolve().then(() => _interopRequireWildcard(require('@/app/api/dashboard/stats/route')));
      await handler(mockRequest);
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Internal server error'
      });
    });
    it('should handle missing tenant ID in token', async () => {
      mockValidateJwtToken.mockResolvedValue({
        sub: 'test-user-id',
        email: '<EMAIL>'
        // Missing tenant_id
      });
      const {
        default: handler
      } = await Promise.resolve().then(() => _interopRequireWildcard(require('@/app/api/dashboard/stats/route')));
      await handler(mockRequest);
      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Tenant ID not found in token'
      });
    });
    it('should handle empty database results', async () => {
      mockDb.query.mockResolvedValue({
        rows: []
      });
      const {
        default: handler
      } = await Promise.resolve().then(() => _interopRequireWildcard(require('@/app/api/dashboard/stats/route')));
      await handler(mockRequest);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: {
          totalRenewals: 0,
          renewalsDue: 0,
          vendors: 0,
          annualSpend: '$0'
        }
      });
    });
    it('should format currency correctly', async () => {
      mockDb.query.mockResolvedValue({
        rows: [_objectSpread(_objectSpread({}, mockStatsData), {}, {
          annual_spend: 1234567.89
        })]
      });
      const {
        default: handler
      } = await Promise.resolve().then(() => _interopRequireWildcard(require('@/app/api/dashboard/stats/route')));
      await handler(mockRequest);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: expect.objectContaining({
          annualSpend: '$1,234,568' // Rounded to nearest dollar
        })
      });
    });
    it('should handle non-GET methods', async () => {
      mockRequest.method = 'POST';
      const {
        default: handler
      } = await Promise.resolve().then(() => _interopRequireWildcard(require('@/app/api/dashboard/stats/route')));
      await handler(mockRequest);
      expect(mockResponse.status).toHaveBeenCalledWith(405);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Method not allowed'
      });
    });
  });
  describe('GET /api/dashboard/renewals', () => {
    const mockRenewalsData = [{
      id: '1',
      name: 'Microsoft Office 365',
      vendor: 'Microsoft',
      renewal_date: '2025-02-15',
      cost: 1200,
      status: 'active'
    }, {
      id: '2',
      name: 'Adobe Creative Suite',
      vendor: 'Adobe',
      renewal_date: '2025-03-01',
      cost: 2400,
      status: 'pending'
    }];
    beforeEach(() => {
      mockDb.query.mockResolvedValue({
        rows: mockRenewalsData
      });
    });
    it('should return recent renewals successfully', async () => {
      const {
        default: handler
      } = await Promise.resolve().then(() => _interopRequireWildcard(require('@/app/api/dashboard/renewals/route')));
      await handler(mockRequest);
      expect(mockDb.query).toHaveBeenCalledWith(expect.stringContaining('SELECT'), ['test-tenant-id']);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: expect.arrayContaining([expect.objectContaining({
          id: '1',
          name: 'Microsoft Office 365',
          vendor: 'Microsoft'
        })])
      });
    });
    it('should handle query parameters for filtering', async () => {
      mockRequest.query = {
        limit: '5',
        status: 'active'
      };
      const {
        default: handler
      } = await Promise.resolve().then(() => _interopRequireWildcard(require('@/app/api/dashboard/renewals/route')));
      await handler(mockRequest);
      expect(mockDb.query).toHaveBeenCalledWith(expect.stringContaining('LIMIT'), expect.arrayContaining(['test-tenant-id', 'active', 5]));
    });
    it('should validate query parameters', async () => {
      mockRequest.query = {
        limit: 'invalid'
      };
      const {
        default: handler
      } = await Promise.resolve().then(() => _interopRequireWildcard(require('@/app/api/dashboard/renewals/route')));
      await handler(mockRequest);
      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Invalid query parameters'
      });
    });
    it('should handle date formatting correctly', async () => {
      const {
        default: handler
      } = await Promise.resolve().then(() => _interopRequireWildcard(require('@/app/api/dashboard/renewals/route')));
      await handler(mockRequest);
      const responseCall = mockResponse.json.mock.calls[0][0];
      const renewals = responseCall.data;
      renewals.forEach(renewal => {
        expect(renewal.renewalDate).toMatch(/^\d{4}-\d{2}-\d{2}T/);
      });
    });
  });
  describe('Authentication Middleware', () => {
    it('should extract token from Authorization header', () => {
      const authHeader = 'Bearer test-token-123';
      const token = authHeader.startsWith('Bearer ') ? authHeader.substring(7) : null;
      expect(token).toBe('test-token-123');
    });
    it('should extract token from cookies', () => {
      mockRequest.cookies.idToken = 'cookie-token-123';
      const token = mockRequest.cookies.idToken;
      expect(token).toBe('cookie-token-123');
    });
    it('should prioritize Authorization header over cookies', () => {
      mockRequest.headers.authorization = 'Bearer header-token';
      mockRequest.cookies.idToken = 'cookie-token';
      const headerToken = mockRequest.headers.authorization?.substring(7);
      const cookieToken = mockRequest.cookies.idToken;
      const token = headerToken || cookieToken;
      expect(token).toBe('header-token');
    });
    it('should handle malformed Authorization header', () => {
      mockRequest.headers.authorization = 'InvalidFormat token';
      const authHeader = mockRequest.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;
      expect(token).toBe(null);
    });
  });
  describe('Error Handling', () => {
    it('should handle unexpected errors gracefully', async () => {
      mockValidateJwtToken.mockImplementation(() => {
        throw new Error('Unexpected error');
      });
      const {
        default: handler
      } = await Promise.resolve().then(() => _interopRequireWildcard(require('@/app/api/dashboard/stats/route')));
      await handler(mockRequest);
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Internal server error'
      });
    });
    it('should not expose sensitive error details', async () => {
      mockDb.query.mockRejectedValue(new Error('Database password is wrong'));
      const {
        default: handler
      } = await Promise.resolve().then(() => _interopRequireWildcard(require('@/app/api/dashboard/stats/route')));
      await handler(mockRequest);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Internal server error' // Generic error message
      });
    });
    it('should log errors for debugging', async () => {
      const consoleSpy = _globals.jest.spyOn(console, 'error').mockImplementation();
      mockDb.query.mockRejectedValue(new Error('Database error'));
      const {
        default: handler
      } = await Promise.resolve().then(() => _interopRequireWildcard(require('@/app/api/dashboard/stats/route')));
      await handler(mockRequest);
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('API Error'), expect.any(Error));
      consoleSpy.mockRestore();
    });
  });
  describe('Security', () => {
    it('should validate tenant isolation', async () => {
      const {
        default: handler
      } = await Promise.resolve().then(() => _interopRequireWildcard(require('@/app/api/dashboard/stats/route')));
      await handler(mockRequest);

      // Ensure query includes tenant_id filter
      expect(mockDb.query).toHaveBeenCalledWith(expect.stringContaining('tenant_id = $1'), expect.arrayContaining(['test-tenant-id']));
    });
    it('should prevent SQL injection', async () => {
      mockRequest.query = {
        status: "'; DROP TABLE renewals; --"
      };
      const {
        default: handler
      } = await Promise.resolve().then(() => _interopRequireWildcard(require('@/app/api/dashboard/renewals/route')));
      await handler(mockRequest);

      // Should use parameterized queries
      expect(mockDb.query).toHaveBeenCalledWith(expect.any(String), expect.arrayContaining([expect.any(String)]));
    });
    it('should set security headers', async () => {
      const {
        default: handler
      } = await Promise.resolve().then(() => _interopRequireWildcard(require('@/app/api/dashboard/stats/route')));
      await handler(mockRequest);
      expect(mockResponse.setHeader).toHaveBeenCalledWith('Cache-Control', expect.stringContaining('no-cache'));
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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