'use client'

import { ReactNode } from 'react'
import { AuthProvider } from '@/contexts/AuthContext'
import { ClientProvider } from '@/contexts/ClientContext'
import { TenantProvider } from '@/contexts/TenantContext'

export function Providers({ children }: { children: ReactNode }) {
  return (
    <AuthProvider>
      <ClientProvider>
        <TenantProvider>
          {children}
        </TenantProvider>
      </ClientProvider>
    </AuthProvider>
  )
}
