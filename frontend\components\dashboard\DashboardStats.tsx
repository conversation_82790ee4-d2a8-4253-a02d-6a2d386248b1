/**
 * Dashboard Statistics Component
 * 
 * Displays key metrics in a grid layout with proper loading and error states.
 * Focused responsibility: Rendering statistics cards only.
 */

'use client'

import React, { memo } from 'react'
import { DashboardStats as DashboardStatsType } from '@/lib/types'
import { BaseComponentProps } from '@/lib/types'
import { usePerformanceMonitor } from '@/lib/performance'

interface DashboardStatsProps extends BaseComponentProps {
  stats: DashboardStatsType
  isLoading?: boolean
}

interface StatCardProps {
  icon: string
  title: string
  value: string | number
  isLoading?: boolean
}

const StatCard = memo(function StatCard({ icon, title, value, isLoading }: StatCardProps) {
  if (isLoading) {
    return (
      <div className="stat-card">
        <div className="stat-icon animate-pulse">⏳</div>
        <h3>{title}</h3>
        <div className="stat-value">
          <div className="animate-pulse bg-gray-200 h-6 w-16 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="stat-card">
      <div className="stat-icon">{icon}</div>
      <h3>{title}</h3>
      <p className="stat-value">{value}</p>
    </div>
  )
})

const DashboardStats = memo(function DashboardStats({
  stats,
  isLoading = false,
  className = '',
  'data-testid': testId
}: DashboardStatsProps) {
  // Performance monitoring in development
  usePerformanceMonitor('DashboardStats')

  // Memoize stats configuration to prevent recreation on every render
  const statsConfig = React.useMemo(() => [
    {
      icon: '📊',
      title: 'Total Renewals',
      value: stats.totalRenewals,
      key: 'totalRenewals'
    },
    {
      icon: '⚠️',
      title: 'Renewals Due',
      value: stats.renewalsDue,
      key: 'renewalsDue'
    },
    {
      icon: '🏢',
      title: 'Vendors',
      value: stats.vendors,
      key: 'vendors'
    },
    {
      icon: '💰',
      title: 'Annual Spend',
      value: stats.annualSpend,
      key: 'annualSpend'
    }
  ], [stats.totalRenewals, stats.renewalsDue, stats.vendors, stats.annualSpend])

  return (
    <div
      className={`stats-grid ${className}`}
      data-testid={testId}
      role="region"
      aria-label="Dashboard Statistics"
    >
      {statsConfig.map((stat) => (
        <StatCard
          key={stat.key}
          icon={stat.icon}
          title={stat.title}
          value={stat.value}
          isLoading={isLoading}
        />
      ))}
    </div>
  )
})

export default DashboardStats
