/**
 * <PERSON>wals Header Component
 * 
 * Header section for the renewals page with title, search, and action buttons
 */

'use client'

import React, { useState } from 'react'
import { BaseComponentProps } from '@/lib/types'

interface RenewalsHeaderProps extends BaseComponentProps {
  onSearch?: (query: string) => void
  onAddRenewal?: () => void
  onImportCSV?: () => void
  searchPlaceholder?: string
}

const RenewalsHeader = React.memo(function RenewalsHeader({
  onSearch,
  onAddRenewal,
  onImportCSV,
  searchPlaceholder = 'Search renewals...',
  className = '',
  'data-testid': testId
}: RenewalsHeaderProps) {
  const [searchQuery, setSearchQuery] = useState('')

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setSearchQuery(query)
    onSearch?.(query)
  }

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch?.(searchQuery)
  }

  const handleAddRenewal = () => {
    onAddRenewal?.()
  }

  const handleImportCSV = () => {
    onImportCSV?.()
  }

  return (
    <div 
      className={`renewals-header ${className}`}
      data-testid={testId}
    >
      <div className="renewals-title-section">
        <h1 className="renewals-title">
          Renewals Inventory
        </h1>
        <p className="renewals-subtitle">
          Manage your subscriptions, maintenance, support and warranties
        </p>
      </div>
      
      <div className="renewals-actions">
        <form onSubmit={handleSearchSubmit} className="search-container">
          <input
            type="text"
            placeholder={searchPlaceholder}
            className="search-input"
            value={searchQuery}
            onChange={handleSearchChange}
            aria-label="Search renewals"
          />
          <button 
            type="submit"
            className="search-icon"
            aria-label="Submit search"
          >
            🔍
          </button>
        </form>
        
        <div className="action-buttons">
          <button 
            className="btn btn-secondary"
            onClick={handleImportCSV}
            aria-label="Import CSV"
          >
            📄 Import CSV
          </button>
          
          <button 
            className="btn btn-primary"
            onClick={handleAddRenewal}
            aria-label="Add new renewal"
          >
            + Add Renewal
          </button>
        </div>
      </div>
    </div>
  )
})

export default RenewalsHeader
