/**
 * Dashboard Integration Tests
 * 
 * End-to-end integration tests for the dashboard functionality
 * including component interactions, data flow, and user workflows
 */

import React from 'react'
import { render, screen, waitFor, fireEvent } from '../utils/test-utils'
import { jest } from '@jest/globals'
import DashboardPage from '@/app/dashboard/page'
import { testUtils } from '../utils/test-utils'

// Mock the hooks and contexts
const mockUseTenant = jest.fn()
const mockUseDashboardData = jest.fn()

jest.mock('@/contexts/AppContext', () => ({
  useTenant: () => mockUseTenant(),
}))

jest.mock('@/hooks/useDashboardData', () => ({
  useDashboardData: () => mockUseDashboardData(),
}))

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  usePathname: () => '/dashboard',
  useSearchParams: () => new URLSearchParams(),
}))

describe('Dashboard Integration Tests', () => {
  const mockTenant = testUtils.generateTestData.tenant()
  const mockDashboardData = testUtils.generateTestData.dashboardStats()
  const mockRenewals = [
    testUtils.generateTestData.renewal({
      id: '1',
      name: 'Microsoft Office 365',
      renewalDate: new Date('2025-02-15'),
    }),
    testUtils.generateTestData.renewal({
      id: '2',
      name: 'Adobe Creative Suite',
      renewalDate: new Date('2025-03-01'),
    }),
  ]

  beforeEach(() => {
    jest.clearAllMocks()

    // Mock successful tenant
    mockUseTenant.mockReturnValue({
      tenant: mockTenant,
      loading: false,
      error: null,
    })

    // Mock successful dashboard data
    mockUseDashboardData.mockReturnValue({
      data: {
        stats: mockDashboardData,
        recentRenewals: mockRenewals,
        upcomingRenewals: mockRenewals,
      },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    })

    // Mock fetch for API calls
    global.fetch = jest.fn().mockResolvedValue(
      testUtils.mockApiSuccess(mockDashboardData)
    )
  })

  describe('Full Dashboard Workflow', () => {
    it('should render complete dashboard with all components', async () => {
      render(<DashboardPage />)

      // Wait for components to load
      await waitFor(() => {
        expect(screen.getByText('Total Renewals')).toBeInTheDocument()
      })

      // Check header
      expect(screen.getByText(mockTenant.clientName)).toBeInTheDocument()

      // Check stats
      expect(screen.getByText('Total Renewals')).toBeInTheDocument()
      expect(screen.getByText('Renewals Due')).toBeInTheDocument()
      expect(screen.getByText('Vendors')).toBeInTheDocument()
      expect(screen.getByText('Annual Spend')).toBeInTheDocument()

      // Check renewals sections
      expect(screen.getByText('Microsoft Office 365')).toBeInTheDocument()
      expect(screen.getByText('Adobe Creative Suite')).toBeInTheDocument()
    })

    it('should handle loading states correctly', async () => {
      // Start with loading state
      mockUseDashboardData.mockReturnValue({
        data: {
          stats: { totalRenewals: 0, renewalsDue: 0, vendors: 0, annualSpend: '$0' },
          recentRenewals: [],
          upcomingRenewals: [],
        },
        isLoading: true,
        error: null,
        refetch: jest.fn(),
      })

      const { rerender } = render(<DashboardPage />)

      // Should show loading indicators
      expect(screen.getAllByText('⏳')).toHaveLength(4)

      // Update to loaded state
      mockUseDashboardData.mockReturnValue({
        data: {
          stats: mockDashboardData,
          recentRenewals: mockRenewals,
          upcomingRenewals: mockRenewals,
        },
        isLoading: false,
        error: null,
        refetch: jest.fn(),
      })

      rerender(<DashboardPage />)

      // Should show actual data
      await waitFor(() => {
        expect(screen.queryByText('⏳')).not.toBeInTheDocument()
        expect(screen.getByText('Microsoft Office 365')).toBeInTheDocument()
      })
    })

    it('should handle error states gracefully', async () => {
      const errorMessage = 'Failed to load dashboard data'
      
      mockUseDashboardData.mockReturnValue({
        data: {
          stats: { totalRenewals: 0, renewalsDue: 0, vendors: 0, annualSpend: '$0' },
          recentRenewals: [],
          upcomingRenewals: [],
        },
        isLoading: false,
        error: errorMessage,
        refetch: jest.fn(),
      })

      render(<DashboardPage />)

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument()
      })
    })
  })

  describe('User Interactions', () => {
    it('should handle search functionality', async () => {
      const mockRefetch = jest.fn()
      mockUseDashboardData.mockReturnValue({
        data: {
          stats: mockDashboardData,
          recentRenewals: mockRenewals,
          upcomingRenewals: mockRenewals,
        },
        isLoading: false,
        error: null,
        refetch: mockRefetch,
      })

      render(<DashboardPage />)

      const searchInput = screen.getByPlaceholderText('Search renewals...')
      
      fireEvent.change(searchInput, { target: { value: 'Microsoft' } })

      // Should trigger search (debounced)
      await waitFor(() => {
        expect(searchInput).toHaveValue('Microsoft')
      })
    })

    it('should handle add renewal button click', async () => {
      render(<DashboardPage />)

      const addButton = screen.getByText('Add Renewal')
      fireEvent.click(addButton)

      // Should trigger add renewal functionality
      // (Implementation depends on actual navigation/modal logic)
    })

    it('should handle renewal item clicks', async () => {
      render(<DashboardPage />)

      await waitFor(() => {
        expect(screen.getByText('Microsoft Office 365')).toBeInTheDocument()
      })

      const renewalItem = screen.getByText('Microsoft Office 365')
      fireEvent.click(renewalItem)

      // Should trigger renewal details view
      // (Implementation depends on actual navigation logic)
    })

    it('should handle scan results interactions', async () => {
      render(<DashboardPage />)

      await waitFor(() => {
        expect(screen.getByText('Run Scan')).toBeInTheDocument()
      })

      const scanButton = screen.getByText('Run Scan')
      fireEvent.click(scanButton)

      // Should trigger scan functionality
    })
  })

  describe('Data Flow Integration', () => {
    it('should refetch data when tenant changes', async () => {
      const mockRefetch = jest.fn()
      mockUseDashboardData.mockReturnValue({
        data: {
          stats: mockDashboardData,
          recentRenewals: mockRenewals,
          upcomingRenewals: mockRenewals,
        },
        isLoading: false,
        error: null,
        refetch: mockRefetch,
      })

      const { rerender } = render(<DashboardPage />)

      // Change tenant
      const newTenant = testUtils.generateTestData.tenant({
        tenantId: 'new-tenant-id',
        clientName: 'New Client',
      })

      mockUseTenant.mockReturnValue({
        tenant: newTenant,
        loading: false,
        error: null,
      })

      rerender(<DashboardPage />)

      // Should show new tenant name
      await waitFor(() => {
        expect(screen.getByText('New Client')).toBeInTheDocument()
      })
    })

    it('should handle API errors and recovery', async () => {
      const mockRefetch = jest.fn()
      
      // Start with error state
      mockUseDashboardData.mockReturnValue({
        data: {
          stats: { totalRenewals: 0, renewalsDue: 0, vendors: 0, annualSpend: '$0' },
          recentRenewals: [],
          upcomingRenewals: [],
        },
        isLoading: false,
        error: 'Network error',
        refetch: mockRefetch,
      })

      const { rerender } = render(<DashboardPage />)

      expect(screen.getByText('Network error')).toBeInTheDocument()

      // Simulate successful retry
      mockUseDashboardData.mockReturnValue({
        data: {
          stats: mockDashboardData,
          recentRenewals: mockRenewals,
          upcomingRenewals: mockRenewals,
        },
        isLoading: false,
        error: null,
        refetch: mockRefetch,
      })

      rerender(<DashboardPage />)

      await waitFor(() => {
        expect(screen.queryByText('Network error')).not.toBeInTheDocument()
        expect(screen.getByText('Microsoft Office 365')).toBeInTheDocument()
      })
    })
  })

  describe('Performance Integration', () => {
    it('should lazy load sections efficiently', async () => {
      render(<DashboardPage />)

      // Initially should show placeholders for lazy sections
      const placeholders = document.querySelectorAll('.animate-pulse')
      expect(placeholders.length).toBeGreaterThan(0)

      // After intersection, should load actual content
      await waitFor(() => {
        expect(screen.getByText('Microsoft Office 365')).toBeInTheDocument()
      })
    })

    it('should handle rapid state changes without memory leaks', async () => {
      const { rerender, unmount } = render(<DashboardPage />)

      // Rapidly change states
      for (let i = 0; i < 10; i++) {
        mockUseDashboardData.mockReturnValue({
          data: {
            stats: { ...mockDashboardData, totalRenewals: i },
            recentRenewals: mockRenewals,
            upcomingRenewals: mockRenewals,
          },
          isLoading: i % 2 === 0,
          error: null,
          refetch: jest.fn(),
        })

        rerender(<DashboardPage />)
      }

      // Should not throw on unmount
      expect(() => unmount()).not.toThrow()
    })
  })

  describe('Accessibility Integration', () => {
    it('should maintain accessibility throughout interactions', async () => {
      render(<DashboardPage />)

      await waitFor(() => {
        expect(screen.getByRole('region', { name: 'Dashboard Statistics' })).toBeInTheDocument()
      })

      // Check for proper heading structure
      const headings = screen.getAllByRole('heading')
      expect(headings.length).toBeGreaterThan(0)

      // Check for proper button accessibility
      const buttons = screen.getAllByRole('button')
      buttons.forEach(button => {
        expect(button).toBeVisible()
      })
    })

    it('should support keyboard navigation', async () => {
      render(<DashboardPage />)

      await waitFor(() => {
        expect(screen.getByText('Add Renewal')).toBeInTheDocument()
      })

      const addButton = screen.getByText('Add Renewal')
      
      // Should be focusable
      addButton.focus()
      expect(document.activeElement).toBe(addButton)

      // Should respond to keyboard events
      fireEvent.keyDown(addButton, { key: 'Enter' })
      // Should trigger the same action as click
    })
  })

  describe('Error Boundary Integration', () => {
    it('should catch and handle component errors', () => {
      // Mock a component that throws an error
      const ThrowingComponent = () => {
        throw new Error('Test error')
      }

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()

      expect(() => {
        render(<ThrowingComponent />)
      }).not.toThrow()

      consoleSpy.mockRestore()
    })

    it('should recover from errors when props change', async () => {
      // This would test error boundary recovery
      // Implementation depends on actual error boundary setup
      render(<DashboardPage />)

      // Should render without throwing
      await waitFor(() => {
        expect(screen.getByText('Total Renewals')).toBeInTheDocument()
      })
    })
  })
})
