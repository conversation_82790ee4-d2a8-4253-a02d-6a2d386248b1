/**
 * Renewals Table Component
 * 
 * Table display for renewals with sorting, actions, and status indicators
 */

'use client'

import React, { useState, useMemo } from 'react'
import { BaseComponentProps } from '@/lib/types'

interface Renewal {
  id: string
  name: string
  product: string
  vendor: string
  type: 'Subscription' | 'Warranty' | 'Support' | 'License'
  renewalDate: string
  cost: number
  currency: string
  status: 'Active' | 'Expired' | 'Expiring'
  alerts: number
}

interface RenewalsTableProps extends BaseComponentProps {
  renewals: Renewal[]
  isLoading?: boolean
  onRenewalAction?: (renewalId: string, action: string) => void
}

type SortField = keyof Renewal
type SortDirection = 'asc' | 'desc'

const RenewalsTable = React.memo(function RenewalsTable({
  renewals,
  isLoading = false,
  onRenewalAction,
  className = '',
  'data-testid': testId
}: RenewalsTableProps) {
  const [sortField, setSortField] = useState<SortField>('renewalDate')
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')

  // Sort renewals
  const sortedRenewals = useMemo(() => {
    return [...renewals].sort((a, b) => {
      let aValue = a[sortField]
      let bValue = b[sortField]

      // Handle date sorting
      if (sortField === 'renewalDate') {
        aValue = new Date(aValue as string).getTime()
        bValue = new Date(bValue as string).getTime()
      }

      // Handle string sorting
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
      return 0
    })
  }, [renewals, sortField, sortDirection])

  const handleSort = (field: SortField) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const handleAction = (renewalId: string, action: string) => {
    onRenewalAction?.(renewalId, action)
  }

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount / 100) // Assuming amounts are in cents
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      'Active': 'status-badge status-active',
      'Expired': 'status-badge status-expired',
      'Expiring': 'status-badge status-expiring'
    }
    
    return (
      <span className={statusClasses[status as keyof typeof statusClasses] || 'status-badge'}>
        {status}
      </span>
    )
  }

  const getSortIcon = (field: SortField) => {
    if (field !== sortField) return '↕️'
    return sortDirection === 'asc' ? '↑' : '↓'
  }

  if (isLoading) {
    return (
      <div className="renewals-table-container">
        <div className="loading-skeleton">
          <div className="skeleton-header"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="skeleton-row"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div 
      className={`renewals-table-container ${className}`}
      data-testid={testId}
    >
      <div className="table-wrapper">
        <table className="renewals-table">
          <thead>
            <tr>
              <th 
                className="sortable-header"
                onClick={() => handleSort('name')}
              >
                Renewal {getSortIcon('name')}
              </th>
              <th 
                className="sortable-header"
                onClick={() => handleSort('product')}
              >
                Product {getSortIcon('product')}
              </th>
              <th 
                className="sortable-header"
                onClick={() => handleSort('vendor')}
              >
                Vendor {getSortIcon('vendor')}
              </th>
              <th 
                className="sortable-header"
                onClick={() => handleSort('type')}
              >
                Type {getSortIcon('type')}
              </th>
              <th 
                className="sortable-header"
                onClick={() => handleSort('renewalDate')}
              >
                Renewal Date {getSortIcon('renewalDate')}
              </th>
              <th 
                className="sortable-header"
                onClick={() => handleSort('cost')}
              >
                Cost {getSortIcon('cost')}
              </th>
              <th 
                className="sortable-header"
                onClick={() => handleSort('currency')}
              >
                Currency {getSortIcon('currency')}
              </th>
              <th 
                className="sortable-header"
                onClick={() => handleSort('status')}
              >
                Status {getSortIcon('status')}
              </th>
              <th 
                className="sortable-header"
                onClick={() => handleSort('alerts')}
              >
                Alerts {getSortIcon('alerts')}
              </th>
              <th className="actions-header">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {sortedRenewals.map((renewal) => (
              <tr key={renewal.id} className="renewal-row">
                <td className="renewal-name">
                  <div className="name-cell">
                    <span className="renewal-icon">📄</span>
                    {renewal.name}
                  </div>
                </td>
                <td>{renewal.product}</td>
                <td>{renewal.vendor}</td>
                <td>{renewal.type}</td>
                <td>{formatDate(renewal.renewalDate)}</td>
                <td>{formatCurrency(renewal.cost, renewal.currency)}</td>
                <td>{renewal.currency}</td>
                <td>{getStatusBadge(renewal.status)}</td>
                <td>
                  {renewal.alerts > 0 && (
                    <span className="alerts-badge">
                      {renewal.alerts}
                    </span>
                  )}
                </td>
                <td className="actions-cell">
                  <div className="actions-dropdown">
                    <button 
                      className="actions-button"
                      onClick={() => handleAction(renewal.id, 'menu')}
                      aria-label="More actions"
                    >
                      ⋯
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {renewals.length === 0 && !isLoading && (
        <div className="empty-state">
          <div className="empty-icon">📋</div>
          <h3>No renewals found</h3>
          <p>Try adjusting your search or filters to find renewals.</p>
        </div>
      )}
    </div>
  )
})

export default RenewalsTable
