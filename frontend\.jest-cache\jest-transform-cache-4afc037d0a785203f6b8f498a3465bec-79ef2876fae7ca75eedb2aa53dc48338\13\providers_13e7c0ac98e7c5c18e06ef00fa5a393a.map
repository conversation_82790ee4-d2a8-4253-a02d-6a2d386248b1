{"version": 3, "names": ["_jsxFileName", "React", "__jsx", "createElement", "cov_295nwmplfa", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "s", "f", "b", "_coverageSchema", "coverage", "actualCoverage", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Providers", "children", "__self", "__source", "fileName", "lineNumber", "columnNumber"], "sources": ["providers.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { ReactNode } from 'react'\r\nimport { AppProvider } from '@/contexts/AppContext'\r\n\r\nexport function Providers({ children }: { children: ReactNode }) {\r\n  return (\r\n    <AppProvider>\r\n      {children}\r\n    </AppProvider>\r\n  )\r\n}\r\n"], "mappings": "AAAA,YAAY;;AAAA;AAAA,IAAAA,YAAA;AAAA,OAAAC,KAAA;AAAA,IAAAC,KAAA,GAAAD,KAAA,CAAAE,aAAA;AAAA,SAAAC,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;IAAAC,CAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;IAAA;IAAAC,CAAA;IAAAC,eAAA;IAAAlB,IAAA;EAAA;EAAA,IAAAmB,QAAA,GAAAlB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAgB,QAAA,CAAApB,IAAA,KAAAoB,QAAA,CAAApB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAmB,QAAA,CAAApB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAgB,cAAA,GAAAD,QAAA,CAAApB,IAAA;EAAA;IAeA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAsB,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAtB,cAAA;AAZZ,SAASuB,WAAW,QAAQ,uBAAuB;AAEnD,OAAO,SAASC,SAASA,CAAC;EAAEC;AAAkC,CAAC,EAAE;EAAA;EAAAzB,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAiB,CAAA;EAC/D,OACE,0BAAAnB,KAAA,CAACyB,WAAW;EAAA;EAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAhC,YAAA;MAAAiC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACTL,QACU,CAAC;AAElB", "ignoreList": []}