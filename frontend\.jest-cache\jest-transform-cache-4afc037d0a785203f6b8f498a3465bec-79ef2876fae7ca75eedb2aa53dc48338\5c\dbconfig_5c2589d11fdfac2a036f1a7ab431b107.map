{"version": 3, "names": ["cov_1xwmwh2tn6", "actualCoverage", "Signer", "Pool", "getDatabaseConfig", "publicConfig", "pool", "s", "getPoolConfig", "dbConfig", "f", "baseConfig", "user", "password", "host", "b", "port", "database", "name", "ssl", "rejectUnauthorized", "max", "min", "idleTimeoutMillis", "connectionTimeoutMillis", "maxUses", "query_timeout", "statement_timeout", "keepAlive", "keepAliveInitialDelayMillis", "getDbPool", "app", "isProduction", "console", "log", "poolConfig", "on", "err", "error", "dbHost", "url", "dbUser", "process", "env", "DB_IAM_USER", "dbN<PERSON>", "signer", "hostname", "username", "region", "aws", "token", "getAuthToken", "Error"], "sources": ["db-config.ts"], "sourcesContent": ["import { Signer } from \"@aws-sdk/rds-signer\";\nimport { Pool, PoolConfig, PoolClient } from 'pg';\nimport { getDatabaseConfig, publicConfig } from './config';\n\n// Connection pool instance\nlet pool: Pool | null = null;\n\n// Pool configuration with optimized settings\nconst getPoolConfig = (dbConfig: ReturnType<typeof getDatabaseConfig>): PoolConfig => {\n  const baseConfig: PoolConfig = {\n    user: dbConfig.user,\n    password: dbConfig.password,\n    host: dbConfig.host || '127.0.0.1',\n    port: 5432,\n    database: dbConfig.name,\n    ssl: dbConfig.ssl ? { rejectUnauthorized: false } : false,\n\n    // Connection pool optimization\n    max: 20, // Maximum number of clients in the pool\n    min: 2,  // Minimum number of clients in the pool\n    idleTimeoutMillis: 30000, // Close idle clients after 30 seconds\n    connectionTimeoutMillis: 10000, // Return error after 10 seconds if connection could not be established\n    maxUses: 7500, // Close (and replace) a connection after it has been used 7500 times\n\n    // Query timeout\n    query_timeout: 30000, // 30 seconds\n    statement_timeout: 30000, // 30 seconds\n\n    // Keep alive settings\n    keepAlive: true,\n    keepAliveInitialDelayMillis: 10000,\n  };\n\n  return baseConfig;\n};\n\nexport async function getDbPool(): Promise<Pool> {\n  if (pool) return pool;\n\n  const dbConfig = getDatabaseConfig();\n\n  // For local development, use environment variables\n  if (!publicConfig.app.isProduction) {\n    console.log('Database config:', {\n      user: dbConfig.user,\n      host: dbConfig.host || '127.0.0.1',\n      database: dbConfig.name,\n      ssl: dbConfig.ssl\n    });\n\n    const poolConfig = getPoolConfig(dbConfig);\n    pool = new Pool(poolConfig);\n\n    // Add error handling for the pool\n    pool.on('error', (err) => {\n      console.error('Unexpected error on idle client', err);\n    });\n\n    pool.on('connect', () => {\n      console.log('New client connected to database');\n    });\n\n    pool.on('remove', () => {\n      console.log('Client removed from pool');\n    });\n\n    return pool;\n  }\n  \n  // For production, use IAM authentication\n  try {\n    const dbHost = dbConfig.host || dbConfig.url || 'renewtrack-prod-ca-central-1-rds.cpy6ukqgy9s3.ca-central-1.rds.amazonaws.com';\n    const dbUser = process.env.DB_IAM_USER || 'renewtrack_iam_user';\n    const dbName = dbConfig.name || 'renewtrack';\n\n    // Create RDS signer for IAM authentication\n    const signer = new Signer({\n      hostname: dbHost,\n      port: 5432,\n      username: dbUser,\n      region: publicConfig.aws.region\n    });\n\n    // Get temporary auth token\n    const token = await signer.getAuthToken();\n\n    if (!token) {\n      throw new Error('Failed to obtain RDS auth token');\n    }\n    \n    // Create connection pool with IAM token as password\n    pool = new Pool({\n      user: dbUser,\n      password: token,\n      host: dbHost,\n      port: 5432,\n      database: dbName,\n      ssl: { rejectUnauthorized: false }\n    });\n    \n    return pool;\n  } catch (error) {\n    console.error('Error establishing database connection:', error);\n    throw error;\n  }\n}\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ,SAASE,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,IAAI,QAAgC,IAAI;AACjD,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,UAAU;;AAE1D;AACA,IAAIC,IAAiB;AAAA;AAAA,CAAAN,cAAA,GAAAO,CAAA,OAAG,IAAI;;AAE5B;AAAA;AAAAP,cAAA,GAAAO,CAAA;AACA,MAAMC,aAAa,GAAIC,QAA8C,IAAiB;EAAA;EAAAT,cAAA,GAAAU,CAAA;EACpF,MAAMC,UAAsB;EAAA;EAAA,CAAAX,cAAA,GAAAO,CAAA,OAAG;IAC7BK,IAAI,EAAEH,QAAQ,CAACG,IAAI;IACnBC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;IAC3BC,IAAI;IAAE;IAAA,CAAAd,cAAA,GAAAe,CAAA,UAAAN,QAAQ,CAACK,IAAI;IAAA;IAAA,CAAAd,cAAA,GAAAe,CAAA,UAAI,WAAW;IAClCC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAER,QAAQ,CAACS,IAAI;IACvBC,GAAG,EAAEV,QAAQ,CAACU,GAAG;IAAA;IAAA,CAAAnB,cAAA,GAAAe,CAAA,UAAG;MAAEK,kBAAkB,EAAE;IAAM,CAAC;IAAA;IAAA,CAAApB,cAAA,GAAAe,CAAA,UAAG,KAAK;IAEzD;IACAM,GAAG,EAAE,EAAE;IAAE;IACTC,GAAG,EAAE,CAAC;IAAG;IACTC,iBAAiB,EAAE,KAAK;IAAE;IAC1BC,uBAAuB,EAAE,KAAK;IAAE;IAChCC,OAAO,EAAE,IAAI;IAAE;;IAEf;IACAC,aAAa,EAAE,KAAK;IAAE;IACtBC,iBAAiB,EAAE,KAAK;IAAE;;IAE1B;IACAC,SAAS,EAAE,IAAI;IACfC,2BAA2B,EAAE;EAC/B,CAAC;EAAC;EAAA7B,cAAA,GAAAO,CAAA;EAEF,OAAOI,UAAU;AACnB,CAAC;AAED,OAAO,eAAemB,SAASA,CAAA,EAAkB;EAAA;EAAA9B,cAAA,GAAAU,CAAA;EAAAV,cAAA,GAAAO,CAAA;EAC/C,IAAID,IAAI,EAAE;IAAA;IAAAN,cAAA,GAAAe,CAAA;IAAAf,cAAA,GAAAO,CAAA;IAAA,OAAOD,IAAI;EAAA,CAAC;EAAA;EAAA;IAAAN,cAAA,GAAAe,CAAA;EAAA;EAEtB,MAAMN,QAAQ;EAAA;EAAA,CAAAT,cAAA,GAAAO,CAAA,OAAGH,iBAAiB,CAAC,CAAC;;EAEpC;EAAA;EAAAJ,cAAA,GAAAO,CAAA;EACA,IAAI,CAACF,YAAY,CAAC0B,GAAG,CAACC,YAAY,EAAE;IAAA;IAAAhC,cAAA,GAAAe,CAAA;IAAAf,cAAA,GAAAO,CAAA;IAClC0B,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;MAC9BtB,IAAI,EAAEH,QAAQ,CAACG,IAAI;MACnBE,IAAI;MAAE;MAAA,CAAAd,cAAA,GAAAe,CAAA,UAAAN,QAAQ,CAACK,IAAI;MAAA;MAAA,CAAAd,cAAA,GAAAe,CAAA,UAAI,WAAW;MAClCE,QAAQ,EAAER,QAAQ,CAACS,IAAI;MACvBC,GAAG,EAAEV,QAAQ,CAACU;IAChB,CAAC,CAAC;IAEF,MAAMgB,UAAU;IAAA;IAAA,CAAAnC,cAAA,GAAAO,CAAA,OAAGC,aAAa,CAACC,QAAQ,CAAC;IAAC;IAAAT,cAAA,GAAAO,CAAA;IAC3CD,IAAI,GAAG,IAAIH,IAAI,CAACgC,UAAU,CAAC;;IAE3B;IAAA;IAAAnC,cAAA,GAAAO,CAAA;IACAD,IAAI,CAAC8B,EAAE,CAAC,OAAO,EAAGC,GAAG,IAAK;MAAA;MAAArC,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAO,CAAA;MACxB0B,OAAO,CAACK,KAAK,CAAC,iCAAiC,EAAED,GAAG,CAAC;IACvD,CAAC,CAAC;IAAC;IAAArC,cAAA,GAAAO,CAAA;IAEHD,IAAI,CAAC8B,EAAE,CAAC,SAAS,EAAE,MAAM;MAAA;MAAApC,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAO,CAAA;MACvB0B,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IACjD,CAAC,CAAC;IAAC;IAAAlC,cAAA,GAAAO,CAAA;IAEHD,IAAI,CAAC8B,EAAE,CAAC,QAAQ,EAAE,MAAM;MAAA;MAAApC,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAO,CAAA;MACtB0B,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACzC,CAAC,CAAC;IAAC;IAAAlC,cAAA,GAAAO,CAAA;IAEH,OAAOD,IAAI;EACb,CAAC;EAAA;EAAA;IAAAN,cAAA,GAAAe,CAAA;EAAA;;EAED;EAAAf,cAAA,GAAAO,CAAA;EACA,IAAI;IACF,MAAMgC,MAAM;IAAA;IAAA,CAAAvC,cAAA,GAAAO,CAAA;IAAG;IAAA,CAAAP,cAAA,GAAAe,CAAA,UAAAN,QAAQ,CAACK,IAAI;IAAA;IAAA,CAAAd,cAAA,GAAAe,CAAA,UAAIN,QAAQ,CAAC+B,GAAG;IAAA;IAAA,CAAAxC,cAAA,GAAAe,CAAA,UAAI,8EAA8E;IAC9H,MAAM0B,MAAM;IAAA;IAAA,CAAAzC,cAAA,GAAAO,CAAA;IAAG;IAAA,CAAAP,cAAA,GAAAe,CAAA,UAAA2B,OAAO,CAACC,GAAG,CAACC,WAAW;IAAA;IAAA,CAAA5C,cAAA,GAAAe,CAAA,UAAI,qBAAqB;IAC/D,MAAM8B,MAAM;IAAA;IAAA,CAAA7C,cAAA,GAAAO,CAAA;IAAG;IAAA,CAAAP,cAAA,GAAAe,CAAA,UAAAN,QAAQ,CAACS,IAAI;IAAA;IAAA,CAAAlB,cAAA,GAAAe,CAAA,UAAI,YAAY;;IAE5C;IACA,MAAM+B,MAAM;IAAA;IAAA,CAAA9C,cAAA,GAAAO,CAAA,QAAG,IAAIL,MAAM,CAAC;MACxB6C,QAAQ,EAAER,MAAM;MAChBvB,IAAI,EAAE,IAAI;MACVgC,QAAQ,EAAEP,MAAM;MAChBQ,MAAM,EAAE5C,YAAY,CAAC6C,GAAG,CAACD;IAC3B,CAAC,CAAC;;IAEF;IACA,MAAME,KAAK;IAAA;IAAA,CAAAnD,cAAA,GAAAO,CAAA,QAAG,MAAMuC,MAAM,CAACM,YAAY,CAAC,CAAC;IAAC;IAAApD,cAAA,GAAAO,CAAA;IAE1C,IAAI,CAAC4C,KAAK,EAAE;MAAA;MAAAnD,cAAA,GAAAe,CAAA;MAAAf,cAAA,GAAAO,CAAA;MACV,MAAM,IAAI8C,KAAK,CAAC,iCAAiC,CAAC;IACpD,CAAC;IAAA;IAAA;MAAArD,cAAA,GAAAe,CAAA;IAAA;;IAED;IAAAf,cAAA,GAAAO,CAAA;IACAD,IAAI,GAAG,IAAIH,IAAI,CAAC;MACdS,IAAI,EAAE6B,MAAM;MACZ5B,QAAQ,EAAEsC,KAAK;MACfrC,IAAI,EAAEyB,MAAM;MACZvB,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE4B,MAAM;MAChB1B,GAAG,EAAE;QAAEC,kBAAkB,EAAE;MAAM;IACnC,CAAC,CAAC;IAAC;IAAApB,cAAA,GAAAO,CAAA;IAEH,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOgC,KAAK,EAAE;IAAA;IAAAtC,cAAA,GAAAO,CAAA;IACd0B,OAAO,CAACK,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IAAC;IAAAtC,cAAA,GAAAO,CAAA;IAChE,MAAM+B,KAAK;EACb;AACF", "ignoreList": []}