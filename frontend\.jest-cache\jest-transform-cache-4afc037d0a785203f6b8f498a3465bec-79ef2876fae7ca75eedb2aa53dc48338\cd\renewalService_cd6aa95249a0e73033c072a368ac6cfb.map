{"version": 3, "names": ["cov_f7bzsmcq5", "actualCoverage", "saveRenewal", "tenantId", "renewalData", "alertsData", "f", "s", "console", "log", "Promise", "resolve", "setTimeout", "renewalId", "Date", "now", "alertIds", "map", "_", "index", "success", "message", "error", "Error", "b", "updateRenewal", "deleteRenewal", "saveAlerts"], "sources": ["renewalService.ts"], "sourcesContent": ["/**\n * Renewal Service\n * \n * Handles API calls for renewal and alert management\n * Includes placeholder implementations for database operations\n */\n\nimport { RenewalFormData, AlertFormData } from '@/components/modals/AddRenewalModal'\n\nexport interface SaveRenewalRequest {\n  tenantId: string\n  renewalData: RenewalFormData\n  alertsData: AlertFormData[]\n}\n\nexport interface SaveRenewalResponse {\n  renewalId: string\n  alertIds: string[]\n  success: boolean\n  message: string\n}\n\n/**\n * Save a new renewal and its associated alerts\n * \n * @param tenantId - The tenant's client ID for schema routing\n * @param renewalData - The renewal information\n * @param alertsData - Array of alert configurations\n * @returns Promise with the save result\n */\nexport async function saveRenewal(\n  tenantId: string,\n  renewalData: RenewalFormData,\n  alertsData: AlertFormData[]\n): Promise<SaveRenewalResponse> {\n  try {\n    // TODO: Replace with actual API call\n    // This is a placeholder implementation\n    \n    console.log('Saving renewal to tenant schema:', tenantId)\n    console.log('Renewal data:', renewalData)\n    console.log('Alerts data:', alertsData)\n    \n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 1000))\n    \n    // Placeholder for actual database operations:\n    /*\n    const response = await fetch('/api/renewals', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-Tenant-ID': tenantId\n      },\n      body: JSON.stringify({\n        renewal: renewalData,\n        alerts: alertsData\n      })\n    })\n    \n    if (!response.ok) {\n      throw new Error(`Failed to save renewal: ${response.statusText}`)\n    }\n    \n    return await response.json()\n    */\n    \n    // Mock successful response\n    return {\n      renewalId: `renewal_${Date.now()}`,\n      alertIds: alertsData.map((_, index) => `alert_${Date.now()}_${index}`),\n      success: true,\n      message: 'Renewal and alerts saved successfully'\n    }\n    \n  } catch (error) {\n    console.error('Error saving renewal:', error)\n    throw new Error(error instanceof Error ? error.message : 'Failed to save renewal')\n  }\n}\n\n/**\n * Update an existing renewal\n * \n * @param tenantId - The tenant's client ID\n * @param renewalId - The renewal ID to update\n * @param renewalData - Updated renewal data\n * @returns Promise with the update result\n */\nexport async function updateRenewal(\n  tenantId: string,\n  renewalId: string,\n  renewalData: Partial<RenewalFormData>\n): Promise<SaveRenewalResponse> {\n  try {\n    console.log('Updating renewal:', renewalId, 'for tenant:', tenantId)\n    console.log('Update data:', renewalData)\n    \n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 800))\n    \n    // TODO: Implement actual API call\n    /*\n    const response = await fetch(`/api/renewals/${renewalId}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-Tenant-ID': tenantId\n      },\n      body: JSON.stringify(renewalData)\n    })\n    \n    if (!response.ok) {\n      throw new Error(`Failed to update renewal: ${response.statusText}`)\n    }\n    \n    return await response.json()\n    */\n    \n    return {\n      renewalId,\n      alertIds: [],\n      success: true,\n      message: 'Renewal updated successfully'\n    }\n    \n  } catch (error) {\n    console.error('Error updating renewal:', error)\n    throw new Error(error instanceof Error ? error.message : 'Failed to update renewal')\n  }\n}\n\n/**\n * Delete a renewal and its associated alerts\n * \n * @param tenantId - The tenant's client ID\n * @param renewalId - The renewal ID to delete\n * @returns Promise with the deletion result\n */\nexport async function deleteRenewal(\n  tenantId: string,\n  renewalId: string\n): Promise<{ success: boolean; message: string }> {\n  try {\n    console.log('Deleting renewal:', renewalId, 'for tenant:', tenantId)\n    \n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 500))\n    \n    // TODO: Implement actual API call\n    /*\n    const response = await fetch(`/api/renewals/${renewalId}`, {\n      method: 'DELETE',\n      headers: {\n        'X-Tenant-ID': tenantId\n      }\n    })\n    \n    if (!response.ok) {\n      throw new Error(`Failed to delete renewal: ${response.statusText}`)\n    }\n    \n    return await response.json()\n    */\n    \n    return {\n      success: true,\n      message: 'Renewal deleted successfully'\n    }\n    \n  } catch (error) {\n    console.error('Error deleting renewal:', error)\n    throw new Error(error instanceof Error ? error.message : 'Failed to delete renewal')\n  }\n}\n\n/**\n * Save alerts for a renewal\n * \n * @param tenantId - The tenant's client ID\n * @param renewalId - The renewal ID\n * @param alertsData - Array of alert configurations\n * @returns Promise with the save result\n */\nexport async function saveAlerts(\n  tenantId: string,\n  renewalId: string,\n  alertsData: AlertFormData[]\n): Promise<{ alertIds: string[]; success: boolean; message: string }> {\n  try {\n    console.log('Saving alerts for renewal:', renewalId, 'tenant:', tenantId)\n    console.log('Alerts data:', alertsData)\n    \n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 600))\n    \n    // TODO: Implement actual API call\n    /*\n    const response = await fetch(`/api/renewals/${renewalId}/alerts`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-Tenant-ID': tenantId\n      },\n      body: JSON.stringify({ alerts: alertsData })\n    })\n    \n    if (!response.ok) {\n      throw new Error(`Failed to save alerts: ${response.statusText}`)\n    }\n    \n    return await response.json()\n    */\n    \n    return {\n      alertIds: alertsData.map((_, index) => `alert_${Date.now()}_${index}`),\n      success: true,\n      message: 'Alerts saved successfully'\n    }\n    \n  } catch (error) {\n    console.error('Error saving alerts:', error)\n    throw new Error(error instanceof Error ? error.message : 'Failed to save alerts')\n  }\n}\n\n/**\n * Database Schema Information\n * \n * The following tables should exist in each tenant's schema:\n * \n * Renewals Table:\n * - id (UUID, Primary Key)\n * - product_name (VARCHAR)\n * - version (VARCHAR)\n * - vendor (VARCHAR)\n * - type (VARCHAR)\n * - department (VARCHAR)\n * - purchase_type (VARCHAR)\n * - licensed_date (DATE)\n * - renewal_date (DATE)\n * - associated_emails (JSON/TEXT[])\n * - reseller (VARCHAR)\n * - currency (VARCHAR)\n * - cost (DECIMAL)\n * - cost_code (VARCHAR)\n * - license_count (INTEGER)\n * - description (TEXT)\n * - notes (TEXT)\n * - created_at (TIMESTAMP)\n * - updated_at (TIMESTAMP)\n * \n * Alerts Table:\n * - id (UUID, Primary Key)\n * - renewal_id (UUID, Foreign Key to Renewals)\n * - days_before_renewal (INTEGER)\n * - email_recipients (JSON/TEXT[])\n * - custom_message (TEXT)\n * - enabled (BOOLEAN)\n * - created_at (TIMESTAMP)\n * - updated_at (TIMESTAMP)\n */\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAfZ;AACA;AACA;AACA;AACA;AACA;;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,eAAeE,WAAWA,CAC/BC,QAAgB,EAChBC,WAA4B,EAC5BC,UAA2B,EACG;EAAA;EAAAL,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAO,CAAA;EAC9B,IAAI;IAAA;IAAAP,aAAA,GAAAO,CAAA;IACF;IACA;;IAEAC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEN,QAAQ,CAAC;IAAA;IAAAH,aAAA,GAAAO,CAAA;IACzDC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEL,WAAW,CAAC;IAAA;IAAAJ,aAAA,GAAAO,CAAA;IACzCC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEJ,UAAU,CAAC;;IAEvC;IAAA;IAAAL,aAAA,GAAAO,CAAA;IACA,MAAM,IAAIG,OAAO,CAACC,OAAO,IAAI;MAAA;MAAAX,aAAA,GAAAM,CAAA;MAAAN,aAAA,GAAAO,CAAA;MAAA,OAAAK,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;IAAD,CAAC,CAAC;;IAEvD;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEI;IAAA;IAAAX,aAAA,GAAAO,CAAA;IACA,OAAO;MACLM,SAAS,EAAE,WAAWC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MAClCC,QAAQ,EAAEX,UAAU,CAACY,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;QAAA;QAAAnB,aAAA,GAAAM,CAAA;QAAAN,aAAA,GAAAO,CAAA;QAAA,gBAASO,IAAI,CAACC,GAAG,CAAC,CAAC,IAAII,KAAK,EAAE;MAAD,CAAC,CAAC;MACtEC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;IACX,CAAC;EAEH,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA;IAAAtB,aAAA,GAAAO,CAAA;IACdC,OAAO,CAACc,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAAA;IAAAtB,aAAA,GAAAO,CAAA;IAC7C,MAAM,IAAIgB,KAAK,CAACD,KAAK,YAAYC,KAAK;IAAA;IAAA,CAAAvB,aAAA,GAAAwB,CAAA,UAAGF,KAAK,CAACD,OAAO;IAAA;IAAA,CAAArB,aAAA,GAAAwB,CAAA,UAAG,wBAAwB,EAAC;EACpF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,eAAeC,aAAaA,CACjCtB,QAAgB,EAChBU,SAAiB,EACjBT,WAAqC,EACP;EAAA;EAAAJ,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAO,CAAA;EAC9B,IAAI;IAAA;IAAAP,aAAA,GAAAO,CAAA;IACFC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEI,SAAS,EAAE,aAAa,EAAEV,QAAQ,CAAC;IAAA;IAAAH,aAAA,GAAAO,CAAA;IACpEC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEL,WAAW,CAAC;;IAExC;IAAA;IAAAJ,aAAA,GAAAO,CAAA;IACA,MAAM,IAAIG,OAAO,CAACC,OAAO,IAAI;MAAA;MAAAX,aAAA,GAAAM,CAAA;MAAAN,aAAA,GAAAO,CAAA;MAAA,OAAAK,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC;IAAD,CAAC,CAAC;;IAEtD;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAfI;IAAAX,aAAA,GAAAO,CAAA;IAiBA,OAAO;MACLM,SAAS;MACTG,QAAQ,EAAE,EAAE;MACZI,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;IACX,CAAC;EAEH,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA;IAAAtB,aAAA,GAAAO,CAAA;IACdC,OAAO,CAACc,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAAA;IAAAtB,aAAA,GAAAO,CAAA;IAC/C,MAAM,IAAIgB,KAAK,CAACD,KAAK,YAAYC,KAAK;IAAA;IAAA,CAAAvB,aAAA,GAAAwB,CAAA,UAAGF,KAAK,CAACD,OAAO;IAAA;IAAA,CAAArB,aAAA,GAAAwB,CAAA,UAAG,0BAA0B,EAAC;EACtF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,eAAeE,aAAaA,CACjCvB,QAAgB,EAChBU,SAAiB,EAC+B;EAAA;EAAAb,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAO,CAAA;EAChD,IAAI;IAAA;IAAAP,aAAA,GAAAO,CAAA;IACFC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEI,SAAS,EAAE,aAAa,EAAEV,QAAQ,CAAC;;IAEpE;IAAA;IAAAH,aAAA,GAAAO,CAAA;IACA,MAAM,IAAIG,OAAO,CAACC,OAAO,IAAI;MAAA;MAAAX,aAAA,GAAAM,CAAA;MAAAN,aAAA,GAAAO,CAAA;MAAA,OAAAK,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC;IAAD,CAAC,CAAC;;IAEtD;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAbI;IAAAX,aAAA,GAAAO,CAAA;IAeA,OAAO;MACLa,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;IACX,CAAC;EAEH,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA;IAAAtB,aAAA,GAAAO,CAAA;IACdC,OAAO,CAACc,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAAA;IAAAtB,aAAA,GAAAO,CAAA;IAC/C,MAAM,IAAIgB,KAAK,CAACD,KAAK,YAAYC,KAAK;IAAA;IAAA,CAAAvB,aAAA,GAAAwB,CAAA,UAAGF,KAAK,CAACD,OAAO;IAAA;IAAA,CAAArB,aAAA,GAAAwB,CAAA,UAAG,0BAA0B,EAAC;EACtF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,eAAeG,UAAUA,CAC9BxB,QAAgB,EAChBU,SAAiB,EACjBR,UAA2B,EACyC;EAAA;EAAAL,aAAA,GAAAM,CAAA;EAAAN,aAAA,GAAAO,CAAA;EACpE,IAAI;IAAA;IAAAP,aAAA,GAAAO,CAAA;IACFC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,SAAS,EAAE,SAAS,EAAEV,QAAQ,CAAC;IAAA;IAAAH,aAAA,GAAAO,CAAA;IACzEC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEJ,UAAU,CAAC;;IAEvC;IAAA;IAAAL,aAAA,GAAAO,CAAA;IACA,MAAM,IAAIG,OAAO,CAACC,OAAO,IAAI;MAAA;MAAAX,aAAA,GAAAM,CAAA;MAAAN,aAAA,GAAAO,CAAA;MAAA,OAAAK,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC;IAAD,CAAC,CAAC;;IAEtD;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAfI;IAAAX,aAAA,GAAAO,CAAA;IAiBA,OAAO;MACLS,QAAQ,EAAEX,UAAU,CAACY,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;QAAA;QAAAnB,aAAA,GAAAM,CAAA;QAAAN,aAAA,GAAAO,CAAA;QAAA,gBAASO,IAAI,CAACC,GAAG,CAAC,CAAC,IAAII,KAAK,EAAE;MAAD,CAAC,CAAC;MACtEC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;IACX,CAAC;EAEH,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA;IAAAtB,aAAA,GAAAO,CAAA;IACdC,OAAO,CAACc,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAAA;IAAAtB,aAAA,GAAAO,CAAA;IAC5C,MAAM,IAAIgB,KAAK,CAACD,KAAK,YAAYC,KAAK;IAAA;IAAA,CAAAvB,aAAA,GAAAwB,CAAA,UAAGF,KAAK,CAACD,OAAO;IAAA;IAAA,CAAArB,aAAA,GAAAwB,CAAA,UAAG,uBAAuB,EAAC;EACnF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}