6e76ce4241a4e845602b1af8abcb09c2
/**
 * Lazy Loading Components
 * 
 * Provides various lazy loading strategies for optimizing performance
 * including intersection observer, virtual scrolling, and code splitting.
 */

'use client';

/* istanbul ignore next */
import _extends from "@babel/runtime/helpers/esm/extends";
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\LazyLoad.tsx";
var __jsx = React.createElement;
function cov_gd70kbils() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\LazyLoad.tsx";
  var hash = "7e74d4b3d2fe8320c6918a00a44ae135d9defdbc";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\components\\common\\LazyLoad.tsx",
    statementMap: {
      "0": {
        start: {
          line: 42,
          column: 29
        },
        end: {
          line: 70,
          column: 2
        }
      },
      "1": {
        start: {
          line: 50,
          column: 32
        },
        end: {
          line: 53,
          column: 4
        }
      },
      "2": {
        start: {
          line: 55,
          column: 36
        },
        end: {
          line: 55,
          column: 51
        }
      },
      "3": {
        start: {
          line: 57,
          column: 2
        },
        end: {
          line: 61,
          column: 33
        }
      },
      "4": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 60,
          column: 5
        }
      },
      "5": {
        start: {
          line: 59,
          column: 6
        },
        end: {
          line: 59,
          column: 24
        }
      },
      "6": {
        start: {
          line: 63,
          column: 23
        },
        end: {
          line: 63,
          column: 63
        }
      },
      "7": {
        start: {
          line: 65,
          column: 2
        },
        end: {
          line: 69,
          column: 3
        }
      },
      "8": {
        start: {
          line: 73,
          column: 25
        },
        end: {
          line: 131,
          column: 2
        }
      },
      "9": {
        start: {
          line: 81,
          column: 32
        },
        end: {
          line: 84,
          column: 4
        }
      },
      "10": {
        start: {
          line: 86,
          column: 34
        },
        end: {
          line: 86,
          column: 49
        }
      },
      "11": {
        start: {
          line: 87,
          column: 34
        },
        end: {
          line: 87,
          column: 49
        }
      },
      "12": {
        start: {
          line: 88,
          column: 34
        },
        end: {
          line: 88,
          column: 55
        }
      },
      "13": {
        start: {
          line: 90,
          column: 2
        },
        end: {
          line: 107,
          column: 64
        }
      },
      "14": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 106,
          column: 5
        }
      },
      "15": {
        start: {
          line: 92,
          column: 18
        },
        end: {
          line: 92,
          column: 29
        }
      },
      "16": {
        start: {
          line: 94,
          column: 6
        },
        end: {
          line: 98,
          column: 7
        }
      },
      "17": {
        start: {
          line: 95,
          column: 8
        },
        end: {
          line: 95,
          column: 24
        }
      },
      "18": {
        start: {
          line: 96,
          column: 8
        },
        end: {
          line: 96,
          column: 25
        }
      },
      "19": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 97,
          column: 18
        }
      },
      "20": {
        start: {
          line: 100,
          column: 6
        },
        end: {
          line: 103,
          column: 7
        }
      },
      "21": {
        start: {
          line: 101,
          column: 8
        },
        end: {
          line: 101,
          column: 25
        }
      },
      "22": {
        start: {
          line: 102,
          column: 8
        },
        end: {
          line: 102,
          column: 19
        }
      },
      "23": {
        start: {
          line: 105,
          column: 6
        },
        end: {
          line: 105,
          column: 19
        }
      },
      "24": {
        start: {
          line: 109,
          column: 2
        },
        end: {
          line: 130,
          column: 3
        }
      },
      "25": {
        start: {
          line: 142,
          column: 36
        },
        end: {
          line: 142,
          column: 47
        }
      },
      "26": {
        start: {
          line: 143,
          column: 23
        },
        end: {
          line: 143,
          column: 51
        }
      },
      "27": {
        start: {
          line: 145,
          column: 21
        },
        end: {
          line: 145,
          column: 79
        }
      },
      "28": {
        start: {
          line: 146,
          column: 19
        },
        end: {
          line: 149,
          column: 3
        }
      },
      "29": {
        start: {
          line: 151,
          column: 23
        },
        end: {
          line: 151,
          column: 60
        }
      },
      "30": {
        start: {
          line: 152,
          column: 22
        },
        end: {
          line: 152,
          column: 47
        }
      },
      "31": {
        start: {
          line: 153,
          column: 18
        },
        end: {
          line: 153,
          column: 41
        }
      },
      "32": {
        start: {
          line: 155,
          column: 23
        },
        end: {
          line: 157,
          column: 3
        }
      },
      "33": {
        start: {
          line: 156,
          column: 4
        },
        end: {
          line: 156,
          column: 43
        }
      },
      "34": {
        start: {
          line: 159,
          column: 2
        },
        end: {
          line: 179,
          column: 3
        }
      },
      "35": {
        start: {
          line: 169,
          column: 12
        },
        end: {
          line: 174,
          column: 18
        }
      },
      "36": {
        start: {
          line: 187,
          column: 24
        },
        end: {
          line: 187,
          column: 38
        }
      },
      "37": {
        start: {
          line: 189,
          column: 2
        },
        end: {
          line: 195,
          column: 4
        }
      },
      "38": {
        start: {
          line: 190,
          column: 4
        },
        end: {
          line: 194,
          column: 5
        }
      },
      "39": {
        start: {
          line: 199,
          column: 38
        },
        end: {
          line: 229,
          column: 2
        }
      },
      "40": {
        start: {
          line: 210,
          column: 32
        },
        end: {
          line: 210,
          column: 66
        }
      },
      "41": {
        start: {
          line: 212,
          column: 2
        },
        end: {
          line: 222,
          column: 24
        }
      },
      "42": {
        start: {
          line: 213,
          column: 4
        },
        end: {
          line: 221,
          column: 5
        }
      },
      "43": {
        start: {
          line: 214,
          column: 20
        },
        end: {
          line: 216,
          column: 15
        }
      },
      "44": {
        start: {
          line: 215,
          column: 8
        },
        end: {
          line: 215,
          column: 24
        }
      },
      "45": {
        start: {
          line: 218,
          column: 6
        },
        end: {
          line: 218,
          column: 38
        }
      },
      "46": {
        start: {
          line: 218,
          column: 19
        },
        end: {
          line: 218,
          column: 38
        }
      },
      "47": {
        start: {
          line: 219,
          column: 11
        },
        end: {
          line: 221,
          column: 5
        }
      },
      "48": {
        start: {
          line: 220,
          column: 6
        },
        end: {
          line: 220,
          column: 22
        }
      },
      "49": {
        start: {
          line: 224,
          column: 2
        },
        end: {
          line: 226,
          column: 3
        }
      },
      "50": {
        start: {
          line: 225,
          column: 4
        },
        end: {
          line: 225,
          column: 44
        }
      },
      "51": {
        start: {
          line: 228,
          column: 2
        },
        end: {
          line: 228,
          column: 24
        }
      },
      "52": {
        start: {
          line: 232,
          column: 27
        },
        end: {
          line: 271,
          column: 2
        }
      },
      "53": {
        start: {
          line: 245,
          column: 32
        },
        end: {
          line: 248,
          column: 4
        }
      },
      "54": {
        start: {
          line: 250,
          column: 36
        },
        end: {
          line: 250,
          column: 51
        }
      },
      "55": {
        start: {
          line: 252,
          column: 2
        },
        end: {
          line: 256,
          column: 22
        }
      },
      "56": {
        start: {
          line: 253,
          column: 4
        },
        end: {
          line: 255,
          column: 5
        }
      },
      "57": {
        start: {
          line: 254,
          column: 6
        },
        end: {
          line: 254,
          column: 24
        }
      },
      "58": {
        start: {
          line: 258,
          column: 2
        },
        end: {
          line: 270,
          column: 3
        }
      },
      "59": {
        start: {
          line: 274,
          column: 24
        },
        end: {
          line: 329,
          column: 2
        }
      },
      "60": {
        start: {
          line: 289,
          column: 38
        },
        end: {
          line: 289,
          column: 68
        }
      },
      "61": {
        start: {
          line: 291,
          column: 2
        },
        end: {
          line: 293,
          column: 17
        }
      },
      "62": {
        start: {
          line: 292,
          column: 4
        },
        end: {
          line: 292,
          column: 56
        }
      },
      "63": {
        start: {
          line: 292,
          column: 26
        },
        end: {
          line: 292,
          column: 55
        }
      },
      "64": {
        start: {
          line: 295,
          column: 2
        },
        end: {
          line: 328,
          column: 3
        }
      },
      "65": {
        start: {
          line: 299,
          column: 10
        },
        end: {
          line: 309,
          column: 19
        }
      },
      "66": {
        start: {
          line: 301,
          column: 27
        },
        end: {
          line: 301,
          column: 46
        }
      },
      "67": {
        start: {
          line: 315,
          column: 10
        },
        end: {
          line: 324,
          column: 16
        }
      }
    },
    fnMap: {
      "0": {
        name: "LazyComponent",
        decl: {
          start: {
            line: 42,
            column: 43
          },
          end: {
            line: 42,
            column: 56
          }
        },
        loc: {
          start: {
            line: 49,
            column: 23
          },
          end: {
            line: 70,
            column: 1
          }
        },
        line: 49
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 57,
            column: 12
          },
          end: {
            line: 57,
            column: 13
          }
        },
        loc: {
          start: {
            line: 57,
            column: 18
          },
          end: {
            line: 61,
            column: 3
          }
        },
        line: 57
      },
      "2": {
        name: "LazyImage",
        decl: {
          start: {
            line: 73,
            column: 39
          },
          end: {
            line: 73,
            column: 48
          }
        },
        loc: {
          start: {
            line: 80,
            column: 19
          },
          end: {
            line: 131,
            column: 1
          }
        },
        line: 80
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 90,
            column: 12
          },
          end: {
            line: 90,
            column: 13
          }
        },
        loc: {
          start: {
            line: 90,
            column: 18
          },
          end: {
            line: 107,
            column: 3
          }
        },
        line: 90
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 94,
            column: 19
          },
          end: {
            line: 94,
            column: 20
          }
        },
        loc: {
          start: {
            line: 94,
            column: 25
          },
          end: {
            line: 98,
            column: 7
          }
        },
        line: 94
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 100,
            column: 20
          },
          end: {
            line: 100,
            column: 21
          }
        },
        loc: {
          start: {
            line: 100,
            column: 26
          },
          end: {
            line: 103,
            column: 7
          }
        },
        line: 100
      },
      "6": {
        name: "VirtualList",
        decl: {
          start: {
            line: 134,
            column: 16
          },
          end: {
            line: 134,
            column: 27
          }
        },
        loc: {
          start: {
            line: 141,
            column: 24
          },
          end: {
            line: 180,
            column: 1
          }
        },
        line: 141
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 155,
            column: 23
          },
          end: {
            line: 155,
            column: 24
          }
        },
        loc: {
          start: {
            line: 155,
            column: 61
          },
          end: {
            line: 157,
            column: 3
          }
        },
        line: 155
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 168,
            column: 28
          },
          end: {
            line: 168,
            column: 29
          }
        },
        loc: {
          start: {
            line: 169,
            column: 12
          },
          end: {
            line: 174,
            column: 18
          }
        },
        line: 169
      },
      "9": {
        name: "createLazyRoute",
        decl: {
          start: {
            line: 183,
            column: 16
          },
          end: {
            line: 183,
            column: 31
          }
        },
        loc: {
          start: {
            line: 186,
            column: 2
          },
          end: {
            line: 196,
            column: 1
          }
        },
        line: 186
      },
      "10": {
        name: "LazyRoute",
        decl: {
          start: {
            line: 189,
            column: 23
          },
          end: {
            line: 189,
            column: 32
          }
        },
        loc: {
          start: {
            line: 189,
            column: 45
          },
          end: {
            line: 195,
            column: 3
          }
        },
        line: 189
      },
      "11": {
        name: "ProgressiveEnhancement",
        decl: {
          start: {
            line: 199,
            column: 52
          },
          end: {
            line: 199,
            column: 74
          }
        },
        loc: {
          start: {
            line: 209,
            column: 3
          },
          end: {
            line: 229,
            column: 1
          }
        },
        line: 209
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 212,
            column: 12
          },
          end: {
            line: 212,
            column: 13
          }
        },
        loc: {
          start: {
            line: 212,
            column: 18
          },
          end: {
            line: 222,
            column: 3
          }
        },
        line: 212
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 214,
            column: 31
          },
          end: {
            line: 214,
            column: 32
          }
        },
        loc: {
          start: {
            line: 214,
            column: 37
          },
          end: {
            line: 216,
            column: 7
          }
        },
        line: 214
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 218,
            column: 13
          },
          end: {
            line: 218,
            column: 14
          }
        },
        loc: {
          start: {
            line: 218,
            column: 19
          },
          end: {
            line: 218,
            column: 38
          }
        },
        line: 218
      },
      "15": {
        name: "LazySection",
        decl: {
          start: {
            line: 232,
            column: 41
          },
          end: {
            line: 232,
            column: 52
          }
        },
        loc: {
          start: {
            line: 244,
            column: 3
          },
          end: {
            line: 271,
            column: 1
          }
        },
        line: 244
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 252,
            column: 12
          },
          end: {
            line: 252,
            column: 13
          }
        },
        loc: {
          start: {
            line: 252,
            column: 18
          },
          end: {
            line: 256,
            column: 3
          }
        },
        line: 252
      },
      "17": {
        name: "LazyTabs",
        decl: {
          start: {
            line: 274,
            column: 38
          },
          end: {
            line: 274,
            column: 46
          }
        },
        loc: {
          start: {
            line: 288,
            column: 3
          },
          end: {
            line: 329,
            column: 1
          }
        },
        line: 288
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 291,
            column: 12
          },
          end: {
            line: 291,
            column: 13
          }
        },
        loc: {
          start: {
            line: 291,
            column: 18
          },
          end: {
            line: 293,
            column: 3
          }
        },
        line: 291
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 292,
            column: 18
          },
          end: {
            line: 292,
            column: 19
          }
        },
        loc: {
          start: {
            line: 292,
            column: 26
          },
          end: {
            line: 292,
            column: 55
          }
        },
        line: 292
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 298,
            column: 18
          },
          end: {
            line: 298,
            column: 19
          }
        },
        loc: {
          start: {
            line: 299,
            column: 10
          },
          end: {
            line: 309,
            column: 19
          }
        },
        line: 299
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 301,
            column: 21
          },
          end: {
            line: 301,
            column: 22
          }
        },
        loc: {
          start: {
            line: 301,
            column: 27
          },
          end: {
            line: 301,
            column: 46
          }
        },
        line: 301
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 314,
            column: 18
          },
          end: {
            line: 314,
            column: 19
          }
        },
        loc: {
          start: {
            line: 315,
            column: 10
          },
          end: {
            line: 324,
            column: 16
          }
        },
        line: 315
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 44,
            column: 2
          },
          end: {
            line: 44,
            column: 42
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 44,
            column: 13
          },
          end: {
            line: 44,
            column: 42
          }
        }],
        line: 44
      },
      "1": {
        loc: {
          start: {
            line: 45,
            column: 2
          },
          end: {
            line: 45,
            column: 17
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 45,
            column: 14
          },
          end: {
            line: 45,
            column: 17
          }
        }],
        line: 45
      },
      "2": {
        loc: {
          start: {
            line: 46,
            column: 2
          },
          end: {
            line: 46,
            column: 21
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 46,
            column: 15
          },
          end: {
            line: 46,
            column: 21
          }
        }],
        line: 46
      },
      "3": {
        loc: {
          start: {
            line: 47,
            column: 2
          },
          end: {
            line: 47,
            column: 20
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 47,
            column: 16
          },
          end: {
            line: 47,
            column: 20
          }
        }],
        line: 47
      },
      "4": {
        loc: {
          start: {
            line: 48,
            column: 2
          },
          end: {
            line: 48,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 48,
            column: 14
          },
          end: {
            line: 48,
            column: 16
          }
        }],
        line: 48
      },
      "5": {
        loc: {
          start: {
            line: 58,
            column: 4
          },
          end: {
            line: 60,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 58,
            column: 4
          },
          end: {
            line: 60,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 58
      },
      "6": {
        loc: {
          start: {
            line: 58,
            column: 8
          },
          end: {
            line: 58,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 58,
            column: 8
          },
          end: {
            line: 58,
            column: 22
          }
        }, {
          start: {
            line: 58,
            column: 26
          },
          end: {
            line: 58,
            column: 36
          }
        }],
        line: 58
      },
      "7": {
        loc: {
          start: {
            line: 63,
            column: 23
          },
          end: {
            line: 63,
            column: 63
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 63,
            column: 37
          },
          end: {
            line: 63,
            column: 46
          }
        }, {
          start: {
            line: 63,
            column: 49
          },
          end: {
            line: 63,
            column: 63
          }
        }],
        line: 63
      },
      "8": {
        loc: {
          start: {
            line: 67,
            column: 7
          },
          end: {
            line: 67,
            column: 41
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 67,
            column: 22
          },
          end: {
            line: 67,
            column: 30
          }
        }, {
          start: {
            line: 67,
            column: 33
          },
          end: {
            line: 67,
            column: 41
          }
        }],
        line: 67
      },
      "9": {
        loc: {
          start: {
            line: 76,
            column: 2
          },
          end: {
            line: 76,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 76,
            column: 14
          },
          end: {
            line: 76,
            column: 16
          }
        }],
        line: 76
      },
      "10": {
        loc: {
          start: {
            line: 77,
            column: 2
          },
          end: {
            line: 77,
            column: 320
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 77,
            column: 16
          },
          end: {
            line: 77,
            column: 320
          }
        }],
        line: 77
      },
      "11": {
        loc: {
          start: {
            line: 91,
            column: 4
          },
          end: {
            line: 106,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 91,
            column: 4
          },
          end: {
            line: 106,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 91
      },
      "12": {
        loc: {
          start: {
            line: 91,
            column: 8
          },
          end: {
            line: 91,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 91,
            column: 8
          },
          end: {
            line: 91,
            column: 22
          }
        }, {
          start: {
            line: 91,
            column: 26
          },
          end: {
            line: 91,
            column: 35
          }
        }, {
          start: {
            line: 91,
            column: 39
          },
          end: {
            line: 91,
            column: 48
          }
        }],
        line: 91
      },
      "13": {
        loc: {
          start: {
            line: 115,
            column: 10
          },
          end: {
            line: 115,
            column: 49
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 115,
            column: 21
          },
          end: {
            line: 115,
            column: 34
          }
        }, {
          start: {
            line: 115,
            column: 37
          },
          end: {
            line: 115,
            column: 49
          }
        }],
        line: 115
      },
      "14": {
        loc: {
          start: {
            line: 119,
            column: 7
          },
          end: {
            line: 123,
            column: 7
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 119,
            column: 7
          },
          end: {
            line: 119,
            column: 16
          }
        }, {
          start: {
            line: 119,
            column: 20
          },
          end: {
            line: 119,
            column: 29
          }
        }, {
          start: {
            line: 120,
            column: 8
          },
          end: {
            line: 122,
            column: 14
          }
        }],
        line: 119
      },
      "15": {
        loc: {
          start: {
            line: 124,
            column: 7
          },
          end: {
            line: 128,
            column: 7
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 124,
            column: 7
          },
          end: {
            line: 124,
            column: 15
          }
        }, {
          start: {
            line: 125,
            column: 8
          },
          end: {
            line: 127,
            column: 14
          }
        }],
        line: 124
      },
      "16": {
        loc: {
          start: {
            line: 139,
            column: 2
          },
          end: {
            line: 139,
            column: 14
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 139,
            column: 13
          },
          end: {
            line: 139,
            column: 14
          }
        }],
        line: 139
      },
      "17": {
        loc: {
          start: {
            line: 140,
            column: 2
          },
          end: {
            line: 140,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 140,
            column: 14
          },
          end: {
            line: 140,
            column: 16
          }
        }],
        line: 140
      },
      "18": {
        loc: {
          start: {
            line: 191,
            column: 26
          },
          end: {
            line: 191,
            column: 81
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 191,
            column: 37
          },
          end: {
            line: 191,
            column: 49
          }
        }, {
          start: {
            line: 191,
            column: 52
          },
          end: {
            line: 191,
            column: 81
          }
        }],
        line: 191
      },
      "19": {
        loc: {
          start: {
            line: 202,
            column: 2
          },
          end: {
            line: 202,
            column: 18
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 202,
            column: 14
          },
          end: {
            line: 202,
            column: 18
          }
        }],
        line: 202
      },
      "20": {
        loc: {
          start: {
            line: 203,
            column: 2
          },
          end: {
            line: 203,
            column: 11
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 203,
            column: 10
          },
          end: {
            line: 203,
            column: 11
          }
        }],
        line: 203
      },
      "21": {
        loc: {
          start: {
            line: 210,
            column: 41
          },
          end: {
            line: 210,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 210,
            column: 41
          },
          end: {
            line: 210,
            column: 52
          }
        }, {
          start: {
            line: 210,
            column: 56
          },
          end: {
            line: 210,
            column: 65
          }
        }],
        line: 210
      },
      "22": {
        loc: {
          start: {
            line: 213,
            column: 4
          },
          end: {
            line: 221,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 213,
            column: 4
          },
          end: {
            line: 221,
            column: 5
          }
        }, {
          start: {
            line: 219,
            column: 11
          },
          end: {
            line: 221,
            column: 5
          }
        }],
        line: 213
      },
      "23": {
        loc: {
          start: {
            line: 213,
            column: 8
          },
          end: {
            line: 213,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 213,
            column: 8
          },
          end: {
            line: 213,
            column: 17
          }
        }, {
          start: {
            line: 213,
            column: 21
          },
          end: {
            line: 213,
            column: 30
          }
        }],
        line: 213
      },
      "24": {
        loc: {
          start: {
            line: 219,
            column: 11
          },
          end: {
            line: 221,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 219,
            column: 11
          },
          end: {
            line: 221,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 219
      },
      "25": {
        loc: {
          start: {
            line: 224,
            column: 2
          },
          end: {
            line: 226,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 224,
            column: 2
          },
          end: {
            line: 226,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 224
      },
      "26": {
        loc: {
          start: {
            line: 225,
            column: 11
          },
          end: {
            line: 225,
            column: 44
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 225,
            column: 22
          },
          end: {
            line: 225,
            column: 37
          }
        }, {
          start: {
            line: 225,
            column: 40
          },
          end: {
            line: 225,
            column: 44
          }
        }],
        line: 225
      },
      "27": {
        loc: {
          start: {
            line: 235,
            column: 2
          },
          end: {
            line: 235,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 235,
            column: 14
          },
          end: {
            line: 235,
            column: 16
          }
        }],
        line: 235
      },
      "28": {
        loc: {
          start: {
            line: 236,
            column: 2
          },
          end: {
            line: 236,
            column: 17
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 236,
            column: 14
          },
          end: {
            line: 236,
            column: 17
          }
        }],
        line: 236
      },
      "29": {
        loc: {
          start: {
            line: 237,
            column: 2
          },
          end: {
            line: 237,
            column: 22
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 237,
            column: 15
          },
          end: {
            line: 237,
            column: 22
          }
        }],
        line: 237
      },
      "30": {
        loc: {
          start: {
            line: 253,
            column: 4
          },
          end: {
            line: 255,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 253,
            column: 4
          },
          end: {
            line: 255,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 253
      },
      "31": {
        loc: {
          start: {
            line: 260,
            column: 7
          },
          end: {
            line: 268,
            column: 7
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 261,
            column: 8
          },
          end: {
            line: 261,
            column: 16
          }
        }, {
          start: {
            line: 263,
            column: 8
          },
          end: {
            line: 267,
            column: 9
          }
        }],
        line: 260
      },
      "32": {
        loc: {
          start: {
            line: 263,
            column: 8
          },
          end: {
            line: 267,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 263,
            column: 8
          },
          end: {
            line: 263,
            column: 19
          }
        }, {
          start: {
            line: 264,
            column: 10
          },
          end: {
            line: 266,
            column: 16
          }
        }],
        line: 263
      },
      "33": {
        loc: {
          start: {
            line: 278,
            column: 2
          },
          end: {
            line: 278,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 278,
            column: 14
          },
          end: {
            line: 278,
            column: 16
          }
        }],
        line: 278
      },
      "34": {
        loc: {
          start: {
            line: 303,
            column: 14
          },
          end: {
            line: 305,
            column: 53
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 304,
            column: 18
          },
          end: {
            line: 304,
            column: 60
          }
        }, {
          start: {
            line: 305,
            column: 18
          },
          end: {
            line: 305,
            column: 53
          }
        }],
        line: 303
      },
      "35": {
        loc: {
          start: {
            line: 317,
            column: 23
          },
          end: {
            line: 317,
            column: 64
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 317,
            column: 46
          },
          end: {
            line: 317,
            column: 53
          }
        }, {
          start: {
            line: 317,
            column: 56
          },
          end: {
            line: 317,
            column: 64
          }
        }],
        line: 317
      },
      "36": {
        loc: {
          start: {
            line: 319,
            column: 13
          },
          end: {
            line: 323,
            column: 13
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 320,
            column: 14
          },
          end: {
            line: 320,
            column: 25
          }
        }, {
          start: {
            line: 322,
            column: 14
          },
          end: {
            line: 322,
            column: 43
          }
        }],
        line: 319
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0],
      "3": [0],
      "4": [0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0],
      "10": [0],
      "11": [0, 0],
      "12": [0, 0, 0],
      "13": [0, 0],
      "14": [0, 0, 0],
      "15": [0, 0],
      "16": [0],
      "17": [0],
      "18": [0, 0],
      "19": [0],
      "20": [0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0],
      "28": [0],
      "29": [0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "7e74d4b3d2fe8320c6918a00a44ae135d9defdbc"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_gd70kbils = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_gd70kbils();
import React, { Suspense, lazy, memo, useState, useEffect, useRef } from 'react';
import { useIntersectionObserver } from '@/lib/performance';
import { LoadingSkeleton } from './LoadingStates';
// Lazy component that loads when it enters the viewport
export const LazyComponent =
/* istanbul ignore next */
(cov_gd70kbils().s[0]++, /*#__PURE__*/memo(function LazyComponent({
  children,
  fallback =
  /* istanbul ignore next */
  (cov_gd70kbils().b[0][0]++,
  /* istanbul ignore next */
  __jsx(LoadingSkeleton,
  /* istanbul ignore next */
  {
    lines: 3,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 44,
      columnNumber: 14
    }
  })),
  threshold =
  /* istanbul ignore next */
  (cov_gd70kbils().b[1][0]++, 0.1),
  rootMargin =
  /* istanbul ignore next */
  (cov_gd70kbils().b[2][0]++, '50px'),
  triggerOnce =
  /* istanbul ignore next */
  (cov_gd70kbils().b[3][0]++, true),
  className =
  /* istanbul ignore next */
  (cov_gd70kbils().b[4][0]++, '')
}) {
  /* istanbul ignore next */
  cov_gd70kbils().f[0]++;
  const [ref, isIntersecting] =
  /* istanbul ignore next */
  (cov_gd70kbils().s[1]++, useIntersectionObserver({
    threshold,
    rootMargin
  }));
  const [hasLoaded, setHasLoaded] =
  /* istanbul ignore next */
  (cov_gd70kbils().s[2]++, useState(false));
  /* istanbul ignore next */
  cov_gd70kbils().s[3]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_gd70kbils().f[1]++;
    cov_gd70kbils().s[4]++;
    if (
    /* istanbul ignore next */
    (cov_gd70kbils().b[6][0]++, isIntersecting) &&
    /* istanbul ignore next */
    (cov_gd70kbils().b[6][1]++, !hasLoaded)) {
      /* istanbul ignore next */
      cov_gd70kbils().b[5][0]++;
      cov_gd70kbils().s[5]++;
      setHasLoaded(true);
    } else
    /* istanbul ignore next */
    {
      cov_gd70kbils().b[5][1]++;
    }
  }, [isIntersecting, hasLoaded]);
  const shouldRender =
  /* istanbul ignore next */
  (cov_gd70kbils().s[6]++, triggerOnce ?
  /* istanbul ignore next */
  (cov_gd70kbils().b[7][0]++, hasLoaded) :
  /* istanbul ignore next */
  (cov_gd70kbils().b[7][1]++, isIntersecting));
  /* istanbul ignore next */
  cov_gd70kbils().s[7]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    ref: ref,
    className: className,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 66,
      columnNumber: 5
    }
  }, shouldRender ?
  /* istanbul ignore next */
  (cov_gd70kbils().b[8][0]++, children) :
  /* istanbul ignore next */
  (cov_gd70kbils().b[8][1]++, fallback));
}));

// Lazy image with progressive loading
export const LazyImage =
/* istanbul ignore next */
(cov_gd70kbils().s[8]++, /*#__PURE__*/memo(function LazyImage({
  src,
  alt,
  className =
  /* istanbul ignore next */
  (cov_gd70kbils().b[9][0]++, ''),
  placeholder =
  /* istanbul ignore next */
  (cov_gd70kbils().b[10][0]++, 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2VlZSIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1zaXplPSIxOCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iIGZpbGw9IiNhYWEiPkxvYWRpbmc8L3RleHQ+PC9zdmc+'),
  onLoad,
  onError
}) {
  /* istanbul ignore next */
  cov_gd70kbils().f[2]++;
  const [ref, isIntersecting] =
  /* istanbul ignore next */
  (cov_gd70kbils().s[9]++, useIntersectionObserver({
    threshold: 0.1,
    rootMargin: '50px'
  }));
  const [isLoaded, setIsLoaded] =
  /* istanbul ignore next */
  (cov_gd70kbils().s[10]++, useState(false));
  const [hasError, setHasError] =
  /* istanbul ignore next */
  (cov_gd70kbils().s[11]++, useState(false));
  const [imageSrc, setImageSrc] =
  /* istanbul ignore next */
  (cov_gd70kbils().s[12]++, useState(placeholder));
  /* istanbul ignore next */
  cov_gd70kbils().s[13]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_gd70kbils().f[3]++;
    cov_gd70kbils().s[14]++;
    if (
    /* istanbul ignore next */
    (cov_gd70kbils().b[12][0]++, isIntersecting) &&
    /* istanbul ignore next */
    (cov_gd70kbils().b[12][1]++, !isLoaded) &&
    /* istanbul ignore next */
    (cov_gd70kbils().b[12][2]++, !hasError)) {
      /* istanbul ignore next */
      cov_gd70kbils().b[11][0]++;
      const img =
      /* istanbul ignore next */
      (cov_gd70kbils().s[15]++, new Image());
      /* istanbul ignore next */
      cov_gd70kbils().s[16]++;
      img.onload = () => {
        /* istanbul ignore next */
        cov_gd70kbils().f[4]++;
        cov_gd70kbils().s[17]++;
        setImageSrc(src);
        /* istanbul ignore next */
        cov_gd70kbils().s[18]++;
        setIsLoaded(true);
        /* istanbul ignore next */
        cov_gd70kbils().s[19]++;
        onLoad?.();
      };
      /* istanbul ignore next */
      cov_gd70kbils().s[20]++;
      img.onerror = () => {
        /* istanbul ignore next */
        cov_gd70kbils().f[5]++;
        cov_gd70kbils().s[21]++;
        setHasError(true);
        /* istanbul ignore next */
        cov_gd70kbils().s[22]++;
        onError?.();
      };
      /* istanbul ignore next */
      cov_gd70kbils().s[23]++;
      img.src = src;
    } else
    /* istanbul ignore next */
    {
      cov_gd70kbils().b[11][1]++;
    }
  }, [isIntersecting, src, isLoaded, hasError, onLoad, onError]);
  /* istanbul ignore next */
  cov_gd70kbils().s[24]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    ref: ref,
    className: `relative ${className}`,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 110,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "img",
  /* istanbul ignore next */
  {
    src: imageSrc,
    alt: alt,
    className: `transition-opacity duration-300 ${isLoaded ?
    /* istanbul ignore next */
    (cov_gd70kbils().b[13][0]++, 'opacity-100') :
    /* istanbul ignore next */
    (cov_gd70kbils().b[13][1]++, 'opacity-70')} ${className}`,
    loading: "lazy",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 111,
      columnNumber: 7
    }
  }),
  /* istanbul ignore next */
  (cov_gd70kbils().b[14][0]++, !isLoaded) &&
  /* istanbul ignore next */
  (cov_gd70kbils().b[14][1]++, !hasError) &&
  /* istanbul ignore next */
  (cov_gd70kbils().b[14][2]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "absolute inset-0 flex items-center justify-center bg-gray-100",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 120,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "animate-pulse text-gray-400",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 121,
      columnNumber: 11
    }
  }, "Loading..."))),
  /* istanbul ignore next */
  (cov_gd70kbils().b[15][0]++, hasError) &&
  /* istanbul ignore next */
  (cov_gd70kbils().b[15][1]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "absolute inset-0 flex items-center justify-center bg-gray-100",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 125,
      columnNumber: 9
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-gray-400",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 126,
      columnNumber: 11
    }
  }, "Failed to load"))));
}));

// Virtual list for large datasets
export function VirtualList({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan =
  /* istanbul ignore next */
  (cov_gd70kbils().b[16][0]++, 5),
  className =
  /* istanbul ignore next */
  (cov_gd70kbils().b[17][0]++, '')
}) {
  /* istanbul ignore next */
  cov_gd70kbils().f[6]++;
  const [scrollTop, setScrollTop] =
  /* istanbul ignore next */
  (cov_gd70kbils().s[25]++, useState(0));
  const containerRef =
  /* istanbul ignore next */
  (cov_gd70kbils().s[26]++, useRef(null));
  const startIndex =
  /* istanbul ignore next */
  (cov_gd70kbils().s[27]++, Math.max(0, Math.floor(scrollTop / itemHeight) - overscan));
  const endIndex =
  /* istanbul ignore next */
  (cov_gd70kbils().s[28]++, Math.min(items.length - 1, Math.floor((scrollTop + containerHeight) / itemHeight) + overscan));
  const visibleItems =
  /* istanbul ignore next */
  (cov_gd70kbils().s[29]++, items.slice(startIndex, endIndex + 1));
  const totalHeight =
  /* istanbul ignore next */
  (cov_gd70kbils().s[30]++, items.length * itemHeight);
  const offsetY =
  /* istanbul ignore next */
  (cov_gd70kbils().s[31]++, startIndex * itemHeight);
  /* istanbul ignore next */
  cov_gd70kbils().s[32]++;
  const handleScroll = e => {
    /* istanbul ignore next */
    cov_gd70kbils().f[7]++;
    cov_gd70kbils().s[33]++;
    setScrollTop(e.currentTarget.scrollTop);
  };
  /* istanbul ignore next */
  cov_gd70kbils().s[34]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    ref: containerRef,
    className: `overflow-auto ${className}`,
    style: {
      height: containerHeight
    },
    onScroll: handleScroll,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 160,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    style: {
      height: totalHeight,
      position: 'relative'
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 166,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    style: {
      transform: `translateY(${offsetY}px)`
    },
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 167,
      columnNumber: 9
    }
  }, visibleItems.map((item, index) => {
    /* istanbul ignore next */
    cov_gd70kbils().f[8]++;
    cov_gd70kbils().s[35]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      key: startIndex + index,
      style: {
        height: itemHeight
      },
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 169,
        columnNumber: 13
      }
    }, renderItem(item, startIndex + index));
  }))));
}

// Code splitting wrapper
export function createLazyRoute(importFn, fallback) {
  /* istanbul ignore next */
  cov_gd70kbils().f[9]++;
  const LazyComponent =
  /* istanbul ignore next */
  (cov_gd70kbils().s[36]++, /*#__PURE__*/lazy(importFn));
  /* istanbul ignore next */
  cov_gd70kbils().s[37]++;
  return /*#__PURE__*/memo(function LazyRoute(props) {
    /* istanbul ignore next */
    cov_gd70kbils().f[10]++;
    cov_gd70kbils().s[38]++;
    return /* istanbul ignore next */__jsx(Suspense,
    /* istanbul ignore next */
    {
      fallback: fallback ?
      /* istanbul ignore next */
      (cov_gd70kbils().b[18][0]++,
      /* istanbul ignore next */
      __jsx(
      /* istanbul ignore next */
      "fallback",
      /* istanbul ignore next */
      {
        __self: this,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 191,
          columnNumber: 38
        }
      })) :
      /* istanbul ignore next */
      (cov_gd70kbils().b[18][1]++,
      /* istanbul ignore next */
      __jsx(LoadingSkeleton,
      /* istanbul ignore next */
      {
        lines: 5,
        __self: this,
        __source: {
          fileName: _jsxFileName,
          lineNumber: 191,
          columnNumber: 53
        }
      })),
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 191,
        columnNumber: 7
      }
    },
    /* istanbul ignore next */
    __jsx(LazyComponent,
    /* istanbul ignore next */
    _extends({}, props, {
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 192,
        columnNumber: 9
      }
    })));
  });
}

// Progressive enhancement wrapper
export const ProgressiveEnhancement =
/* istanbul ignore next */
(cov_gd70kbils().s[39]++, /*#__PURE__*/memo(function ProgressiveEnhancement({
  children,
  fallback,
  condition =
  /* istanbul ignore next */
  (cov_gd70kbils().b[19][0]++, true),
  delay =
  /* istanbul ignore next */
  (cov_gd70kbils().b[20][0]++, 0)
}) {
  /* istanbul ignore next */
  cov_gd70kbils().f[11]++;
  const [isReady, setIsReady] =
  /* istanbul ignore next */
  (cov_gd70kbils().s[40]++, useState(
  /* istanbul ignore next */
  (cov_gd70kbils().b[21][0]++, delay === 0) &&
  /* istanbul ignore next */
  (cov_gd70kbils().b[21][1]++, condition)));
  /* istanbul ignore next */
  cov_gd70kbils().s[41]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_gd70kbils().f[12]++;
    cov_gd70kbils().s[42]++;
    if (
    /* istanbul ignore next */
    (cov_gd70kbils().b[23][0]++, condition) &&
    /* istanbul ignore next */
    (cov_gd70kbils().b[23][1]++, delay > 0)) {
      /* istanbul ignore next */
      cov_gd70kbils().b[22][0]++;
      const timer =
      /* istanbul ignore next */
      (cov_gd70kbils().s[43]++, setTimeout(() => {
        /* istanbul ignore next */
        cov_gd70kbils().f[13]++;
        cov_gd70kbils().s[44]++;
        setIsReady(true);
      }, delay));
      /* istanbul ignore next */
      cov_gd70kbils().s[45]++;
      return () => {
        /* istanbul ignore next */
        cov_gd70kbils().f[14]++;
        cov_gd70kbils().s[46]++;
        return clearTimeout(timer);
      };
    } else {
      /* istanbul ignore next */
      cov_gd70kbils().b[22][1]++;
      cov_gd70kbils().s[47]++;
      if (condition) {
        /* istanbul ignore next */
        cov_gd70kbils().b[24][0]++;
        cov_gd70kbils().s[48]++;
        setIsReady(true);
      } else
      /* istanbul ignore next */
      {
        cov_gd70kbils().b[24][1]++;
      }
    }
  }, [condition, delay]);
  /* istanbul ignore next */
  cov_gd70kbils().s[49]++;
  if (!isReady) {
    /* istanbul ignore next */
    cov_gd70kbils().b[25][0]++;
    cov_gd70kbils().s[50]++;
    return fallback ?
    /* istanbul ignore next */
    (cov_gd70kbils().b[26][0]++,
    /* istanbul ignore next */
    __jsx(
    /* istanbul ignore next */
    React.Fragment,
    /* istanbul ignore next */
    null, fallback)) :
    /* istanbul ignore next */
    (cov_gd70kbils().b[26][1]++, null);
  } else
  /* istanbul ignore next */
  {
    cov_gd70kbils().b[25][1]++;
  }
  cov_gd70kbils().s[51]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  React.Fragment,
  /* istanbul ignore next */
  null, children);
}));

// Lazy section that loads content when scrolled into view
export const LazySection =
/* istanbul ignore next */
(cov_gd70kbils().s[52]++, /*#__PURE__*/memo(function LazySection({
  children,
  placeholder,
  className =
  /* istanbul ignore next */
  (cov_gd70kbils().b[27][0]++, ''),
  threshold =
  /* istanbul ignore next */
  (cov_gd70kbils().b[28][0]++, 0.1),
  rootMargin =
  /* istanbul ignore next */
  (cov_gd70kbils().b[29][0]++, '100px')
}) {
  /* istanbul ignore next */
  cov_gd70kbils().f[15]++;
  const [ref, isIntersecting] =
  /* istanbul ignore next */
  (cov_gd70kbils().s[53]++, useIntersectionObserver({
    threshold,
    rootMargin
  }));
  const [hasLoaded, setHasLoaded] =
  /* istanbul ignore next */
  (cov_gd70kbils().s[54]++, useState(false));
  /* istanbul ignore next */
  cov_gd70kbils().s[55]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_gd70kbils().f[16]++;
    cov_gd70kbils().s[56]++;
    if (isIntersecting) {
      /* istanbul ignore next */
      cov_gd70kbils().b[30][0]++;
      cov_gd70kbils().s[57]++;
      setHasLoaded(true);
    } else
    /* istanbul ignore next */
    {
      cov_gd70kbils().b[30][1]++;
    }
  }, [isIntersecting]);
  /* istanbul ignore next */
  cov_gd70kbils().s[58]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "section",
  /* istanbul ignore next */
  {
    ref: ref,
    className: className,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 259,
      columnNumber: 5
    }
  }, hasLoaded ?
  /* istanbul ignore next */
  (cov_gd70kbils().b[31][0]++, children) :
  /* istanbul ignore next */
  (cov_gd70kbils().b[31][1]++,
  /* istanbul ignore next */
  (cov_gd70kbils().b[32][0]++, placeholder) ||
  /* istanbul ignore next */
  (cov_gd70kbils().b[32][1]++,
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "py-8",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 264,
      columnNumber: 11
    }
  },
  /* istanbul ignore next */
  __jsx(LoadingSkeleton,
  /* istanbul ignore next */
  {
    lines: 4,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 265,
      columnNumber: 13
    }
  })))));
}));

// Lazy tabs that only render active content
export const LazyTabs =
/* istanbul ignore next */
(cov_gd70kbils().s[59]++, /*#__PURE__*/memo(function LazyTabs({
  tabs,
  activeTab,
  onTabChange,
  className =
  /* istanbul ignore next */
  (cov_gd70kbils().b[33][0]++, '')
}) {
  /* istanbul ignore next */
  cov_gd70kbils().f[17]++;
  const [loadedTabs, setLoadedTabs] =
  /* istanbul ignore next */
  (cov_gd70kbils().s[60]++, useState(new Set([activeTab])));
  /* istanbul ignore next */
  cov_gd70kbils().s[61]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_gd70kbils().f[18]++;
    cov_gd70kbils().s[62]++;
    setLoadedTabs(prev => {
      /* istanbul ignore next */
      cov_gd70kbils().f[19]++;
      cov_gd70kbils().s[63]++;
      return new Set([...prev, activeTab]);
    });
  }, [activeTab]);
  /* istanbul ignore next */
  cov_gd70kbils().s[64]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: className,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 296,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex border-b",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 297,
      columnNumber: 7
    }
  }, tabs.map(tab => {
    /* istanbul ignore next */
    cov_gd70kbils().f[20]++;
    cov_gd70kbils().s[65]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "button",
    /* istanbul ignore next */
    {
      key: tab.id,
      onClick: () => {
        /* istanbul ignore next */
        cov_gd70kbils().f[21]++;
        cov_gd70kbils().s[66]++;
        return onTabChange(tab.id);
      },
      className: `px-4 py-2 font-medium text-sm ${activeTab === tab.id ?
      /* istanbul ignore next */
      (cov_gd70kbils().b[34][0]++, 'border-b-2 border-blue-500 text-blue-600') :
      /* istanbul ignore next */
      (cov_gd70kbils().b[34][1]++, 'text-gray-500 hover:text-gray-700')}`,
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 299,
        columnNumber: 11
      }
    }, tab.label);
  })),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "mt-4",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 313,
      columnNumber: 7
    }
  }, tabs.map(tab => {
    /* istanbul ignore next */
    cov_gd70kbils().f[22]++;
    cov_gd70kbils().s[67]++;
    return /* istanbul ignore next */__jsx(
    /* istanbul ignore next */
    "div",
    /* istanbul ignore next */
    {
      key: tab.id,
      className: activeTab === tab.id ?
      /* istanbul ignore next */
      (cov_gd70kbils().b[35][0]++, 'block') :
      /* istanbul ignore next */
      (cov_gd70kbils().b[35][1]++, 'hidden'),
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 315,
        columnNumber: 11
      }
    }, loadedTabs.has(tab.id) ?
    /* istanbul ignore next */
    (cov_gd70kbils().b[36][0]++, tab.content) :
    /* istanbul ignore next */
    (cov_gd70kbils().b[36][1]++,
    /* istanbul ignore next */
    __jsx(LoadingSkeleton,
    /* istanbul ignore next */
    {
      lines: 3,
      __self: this,
      __source: {
        fileName: _jsxFileName,
        lineNumber: 322,
        columnNumber: 15
      }
    })));
  })));
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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