/**
 * Setup Alerts Step Component
 * 
 * Second step of the Add Renewal modal - configures renewal alerts
 */

'use client'

import React, { useCallback } from 'react'
import { AlertFormData } from '../AddRenewalModal'

interface SetupAlertsStepProps {
  data: AlertFormData[]
  onChange: (data: AlertFormData[]) => void
  onBack: () => void
  onSubmit: () => void
  isSubmitting: boolean
}

const SetupAlertsStep: React.FC<SetupAlertsStepProps> = ({
  data,
  onChange,
  onBack,
  onSubmit,
  isSubmitting
}) => {
  // Handle alert field changes
  const handleAlertChange = useCallback((index: number, field: keyof AlertFormData, value: any) => {
    const updatedAlerts = [...data]
    updatedAlerts[index] = {
      ...updatedAlerts[index],
      [field]: value
    }
    onChange(updatedAlerts)
  }, [data, onChange])

  // Handle email recipients change
  const handleEmailRecipientsChange = useCallback((index: number, emailsString: string) => {
    const emails = emailsString.split(',').map(email => email.trim()).filter(email => email)
    handleAlertChange(index, 'emailRecipients', emails)
  }, [handleAlertChange])

  // Add new alert
  const handleAddAlert = useCallback(() => {
    const newAlert: AlertFormData = {
      daysBeforeRenewal: 30,
      emailRecipients: [],
      customMessage: '',
      enabled: true
    }
    onChange([...data, newAlert])
  }, [data, onChange])

  // Remove alert
  const handleRemoveAlert = useCallback((index: number) => {
    if (data.length > 1) {
      const updatedAlerts = data.filter((_, i) => i !== index)
      onChange(updatedAlerts)
    }
  }, [data, onChange])

  return (
    <div className="setup-alerts-step">
      {data.map((alert, index) => (
        <div key={index} className="alert-config">
          {/* Alert Header */}
          <div className="alert-header">
            <div className="alert-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                <line x1="12" y1="9" x2="12" y2="13"/>
                <line x1="12" y1="17" x2="12.01" y2="17"/>
              </svg>
            </div>
            <div className="alert-title">
              <h3>Set Up Renewal Alert</h3>
              <p>Create an alert to be notified before the renewal date for test.</p>
            </div>
            {data.length > 1 && (
              <button
                type="button"
                className="btn-remove-alert"
                onClick={() => handleRemoveAlert(index)}
                aria-label="Remove alert"
              >
                ×
              </button>
            )}
          </div>

          {/* Days Before Renewal */}
          <div className="form-group">
            <label htmlFor={`daysBeforeRenewal-${index}`} className="form-label">
              Days Before Renewal
            </label>
            <input
              id={`daysBeforeRenewal-${index}`}
              type="number"
              className="form-input"
              min="1"
              max="365"
              value={alert.daysBeforeRenewal}
              onChange={(e) => handleAlertChange(index, 'daysBeforeRenewal', parseInt(e.target.value) || 30)}
            />
            <p className="form-help">
              How many days before the renewal date to receive alerts
            </p>
          </div>

          {/* Email Recipients */}
          <div className="form-group">
            <label htmlFor={`emailRecipients-${index}`} className="form-label">
              Email Recipients
            </label>
            <textarea
              id={`emailRecipients-${index}`}
              className="form-textarea"
              placeholder="Enter email addresses separated by commas"
              rows={3}
              value={alert.emailRecipients.join(', ')}
              onChange={(e) => handleEmailRecipientsChange(index, e.target.value)}
            />
            <p className="form-help">
              Email addresses that should receive alerts (leave empty to use the associated emails)
            </p>
          </div>

          {/* Custom Message */}
          <div className="form-group">
            <label htmlFor={`customMessage-${index}`} className="form-label">
              Custom Message (Optional)
            </label>
            <textarea
              id={`customMessage-${index}`}
              className="form-textarea"
              placeholder="Add a custom message to include in the alert"
              rows={4}
              value={alert.customMessage}
              onChange={(e) => handleAlertChange(index, 'customMessage', e.target.value)}
            />
          </div>

          {/* Enable Alert Checkbox */}
          <div className="form-group checkbox-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                className="checkbox-input"
                checked={alert.enabled}
                onChange={(e) => handleAlertChange(index, 'enabled', e.target.checked)}
              />
              <span className="checkbox-custom"></span>
              <span className="checkbox-text">
                <strong>Enable this alert</strong>
                <br />
                <span className="checkbox-subtext">
                  Uncheck to create the alert but keep it disabled
                </span>
              </span>
            </label>
          </div>
        </div>
      ))}

      {/* Add Another Alert Button */}
      {data.length < 5 && (
        <div className="add-alert-section">
          <button
            type="button"
            className="btn btn-outline add-alert-btn"
            onClick={handleAddAlert}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="10"/>
              <line x1="12" y1="8" x2="12" y2="16"/>
              <line x1="8" y1="12" x2="16" y2="12"/>
            </svg>
            Add Another Alert
          </button>
          <p className="add-alert-help">
            {5 - data.length} more alerts available
          </p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="modal-actions">
        <button
          type="button"
          className="btn btn-secondary"
          onClick={onBack}
          disabled={isSubmitting}
        >
          Back to Details
        </button>
        <button
          type="button"
          className="btn btn-primary"
          onClick={onSubmit}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <span className="spinner"></span>
              Saving...
            </>
          ) : (
            'Save & Finish'
          )}
        </button>
      </div>
    </div>
  )
}

export default SetupAlertsStep
