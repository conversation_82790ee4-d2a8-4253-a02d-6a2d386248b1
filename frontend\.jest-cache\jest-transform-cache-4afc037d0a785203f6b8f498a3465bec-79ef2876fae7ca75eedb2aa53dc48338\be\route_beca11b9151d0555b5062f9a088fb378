9f75c6cd7e4e05b241d5219c88d9d9e8
/* istanbul ignore next */
function cov_2bw1277yma() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\users\\[id]\\route.ts";
  var hash = "c6d899e52d0b69098b6aadedd54cafd54bc7ef20";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\api\\users\\[id]\\route.ts",
    statementMap: {
      "0": {
        start: {
          line: 45,
          column: 19
        },
        end: {
          line: 118,
          column: 2
        }
      },
      "1": {
        start: {
          line: 50,
          column: 21
        },
        end: {
          line: 50,
          column: 40
        }
      },
      "2": {
        start: {
          line: 51,
          column: 2
        },
        end: {
          line: 53,
          column: 3
        }
      },
      "3": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 52,
          column: 32
        }
      },
      "4": {
        start: {
          line: 55,
          column: 18
        },
        end: {
          line: 55,
          column: 37
        }
      },
      "5": {
        start: {
          line: 58,
          column: 26
        },
        end: {
          line: 58,
          column: 67
        }
      },
      "6": {
        start: {
          line: 59,
          column: 2
        },
        end: {
          line: 61,
          column: 3
        }
      },
      "7": {
        start: {
          line: 60,
          column: 4
        },
        end: {
          line: 60,
          column: 36
        }
      },
      "8": {
        start: {
          line: 63,
          column: 26
        },
        end: {
          line: 63,
          column: 46
        }
      },
      "9": {
        start: {
          line: 67,
          column: 18
        },
        end: {
          line: 67,
          column: 91
        }
      },
      "10": {
        start: {
          line: 68,
          column: 20
        },
        end: {
          line: 68,
          column: 41
        }
      },
      "11": {
        start: {
          line: 70,
          column: 2
        },
        end: {
          line: 76,
          column: 3
        }
      },
      "12": {
        start: {
          line: 71,
          column: 4
        },
        end: {
          line: 75,
          column: 6
        }
      },
      "13": {
        start: {
          line: 79,
          column: 16
        },
        end: {
          line: 93,
          column: 3
        }
      },
      "14": {
        start: {
          line: 95,
          column: 17
        },
        end: {
          line: 95,
          column: 60
        }
      },
      "15": {
        start: {
          line: 97,
          column: 2
        },
        end: {
          line: 103,
          column: 3
        }
      },
      "16": {
        start: {
          line: 98,
          column: 4
        },
        end: {
          line: 102,
          column: 6
        }
      },
      "17": {
        start: {
          line: 105,
          column: 2
        },
        end: {
          line: 107,
          column: 3
        }
      },
      "18": {
        start: {
          line: 106,
          column: 4
        },
        end: {
          line: 106,
          column: 42
        }
      },
      "19": {
        start: {
          line: 110,
          column: 19
        },
        end: {
          line: 110,
          column: 30
        }
      },
      "20": {
        start: {
          line: 111,
          column: 2
        },
        end: {
          line: 115,
          column: 3
        }
      },
      "21": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 113,
          column: 35
        }
      },
      "22": {
        start: {
          line: 114,
          column: 4
        },
        end: {
          line: 114,
          column: 40
        }
      },
      "23": {
        start: {
          line: 117,
          column: 2
        },
        end: {
          line: 117,
          column: 72
        }
      },
      "24": {
        start: {
          line: 121,
          column: 19
        },
        end: {
          line: 246,
          column: 2
        }
      },
      "25": {
        start: {
          line: 126,
          column: 21
        },
        end: {
          line: 126,
          column: 65
        }
      },
      "26": {
        start: {
          line: 127,
          column: 2
        },
        end: {
          line: 129,
          column: 3
        }
      },
      "27": {
        start: {
          line: 128,
          column: 4
        },
        end: {
          line: 128,
          column: 32
        }
      },
      "28": {
        start: {
          line: 131,
          column: 18
        },
        end: {
          line: 131,
          column: 37
        }
      },
      "29": {
        start: {
          line: 134,
          column: 26
        },
        end: {
          line: 134,
          column: 67
        }
      },
      "30": {
        start: {
          line: 135,
          column: 2
        },
        end: {
          line: 137,
          column: 3
        }
      },
      "31": {
        start: {
          line: 136,
          column: 4
        },
        end: {
          line: 136,
          column: 36
        }
      },
      "32": {
        start: {
          line: 139,
          column: 26
        },
        end: {
          line: 139,
          column: 46
        }
      },
      "33": {
        start: {
          line: 142,
          column: 25
        },
        end: {
          line: 142,
          column: 77
        }
      },
      "34": {
        start: {
          line: 143,
          column: 2
        },
        end: {
          line: 145,
          column: 3
        }
      },
      "35": {
        start: {
          line: 144,
          column: 4
        },
        end: {
          line: 144,
          column: 35
        }
      },
      "36": {
        start: {
          line: 147,
          column: 37
        },
        end: {
          line: 147,
          column: 56
        }
      },
      "37": {
        start: {
          line: 150,
          column: 27
        },
        end: {
          line: 150,
          column: 72
        }
      },
      "38": {
        start: {
          line: 151,
          column: 22
        },
        end: {
          line: 154,
          column: 3
        }
      },
      "39": {
        start: {
          line: 156,
          column: 2
        },
        end: {
          line: 162,
          column: 3
        }
      },
      "40": {
        start: {
          line: 157,
          column: 4
        },
        end: {
          line: 161,
          column: 6
        }
      },
      "41": {
        start: {
          line: 164,
          column: 2
        },
        end: {
          line: 166,
          column: 3
        }
      },
      "42": {
        start: {
          line: 165,
          column: 4
        },
        end: {
          line: 165,
          column: 42
        }
      },
      "43": {
        start: {
          line: 169,
          column: 33
        },
        end: {
          line: 169,
          column: 35
        }
      },
      "44": {
        start: {
          line: 170,
          column: 24
        },
        end: {
          line: 170,
          column: 26
        }
      },
      "45": {
        start: {
          line: 171,
          column: 19
        },
        end: {
          line: 171,
          column: 20
        }
      },
      "46": {
        start: {
          line: 173,
          column: 2
        },
        end: {
          line: 176,
          column: 3
        }
      },
      "47": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 174,
          column: 49
        }
      },
      "48": {
        start: {
          line: 175,
          column: 4
        },
        end: {
          line: 175,
          column: 33
        }
      },
      "49": {
        start: {
          line: 178,
          column: 2
        },
        end: {
          line: 181,
          column: 3
        }
      },
      "50": {
        start: {
          line: 179,
          column: 4
        },
        end: {
          line: 179,
          column: 55
        }
      },
      "51": {
        start: {
          line: 180,
          column: 4
        },
        end: {
          line: 180,
          column: 39
        }
      },
      "52": {
        start: {
          line: 183,
          column: 2
        },
        end: {
          line: 186,
          column: 3
        }
      },
      "53": {
        start: {
          line: 184,
          column: 4
        },
        end: {
          line: 184,
          column: 56
        }
      },
      "54": {
        start: {
          line: 185,
          column: 4
        },
        end: {
          line: 185,
          column: 40
        }
      },
      "55": {
        start: {
          line: 188,
          column: 2
        },
        end: {
          line: 191,
          column: 3
        }
      },
      "56": {
        start: {
          line: 189,
          column: 4
        },
        end: {
          line: 189,
          column: 50
        }
      },
      "57": {
        start: {
          line: 190,
          column: 4
        },
        end: {
          line: 190,
          column: 50
        }
      },
      "58": {
        start: {
          line: 193,
          column: 2
        },
        end: {
          line: 199,
          column: 3
        }
      },
      "59": {
        start: {
          line: 194,
          column: 4
        },
        end: {
          line: 198,
          column: 6
        }
      },
      "60": {
        start: {
          line: 202,
          column: 2
        },
        end: {
          line: 202,
          column: 54
        }
      },
      "61": {
        start: {
          line: 203,
          column: 2
        },
        end: {
          line: 203,
          column: 45
        }
      },
      "62": {
        start: {
          line: 206,
          column: 2
        },
        end: {
          line: 206,
          column: 44
        }
      },
      "63": {
        start: {
          line: 208,
          column: 22
        },
        end: {
          line: 222,
          column: 3
        }
      },
      "64": {
        start: {
          line: 224,
          column: 23
        },
        end: {
          line: 224,
          column: 74
        }
      },
      "65": {
        start: {
          line: 226,
          column: 2
        },
        end: {
          line: 232,
          column: 3
        }
      },
      "66": {
        start: {
          line: 227,
          column: 4
        },
        end: {
          line: 231,
          column: 6
        }
      },
      "67": {
        start: {
          line: 234,
          column: 2
        },
        end: {
          line: 240,
          column: 3
        }
      },
      "68": {
        start: {
          line: 235,
          column: 4
        },
        end: {
          line: 239,
          column: 6
        }
      },
      "69": {
        start: {
          line: 243,
          column: 2
        },
        end: {
          line: 243,
          column: 69
        }
      },
      "70": {
        start: {
          line: 245,
          column: 2
        },
        end: {
          line: 245,
          column: 79
        }
      },
      "71": {
        start: {
          line: 249,
          column: 22
        },
        end: {
          line: 313,
          column: 2
        }
      },
      "72": {
        start: {
          line: 254,
          column: 21
        },
        end: {
          line: 254,
          column: 49
        }
      },
      "73": {
        start: {
          line: 255,
          column: 2
        },
        end: {
          line: 257,
          column: 3
        }
      },
      "74": {
        start: {
          line: 256,
          column: 4
        },
        end: {
          line: 256,
          column: 32
        }
      },
      "75": {
        start: {
          line: 259,
          column: 18
        },
        end: {
          line: 259,
          column: 37
        }
      },
      "76": {
        start: {
          line: 262,
          column: 26
        },
        end: {
          line: 262,
          column: 67
        }
      },
      "77": {
        start: {
          line: 263,
          column: 2
        },
        end: {
          line: 265,
          column: 3
        }
      },
      "78": {
        start: {
          line: 264,
          column: 4
        },
        end: {
          line: 264,
          column: 36
        }
      },
      "79": {
        start: {
          line: 267,
          column: 26
        },
        end: {
          line: 267,
          column: 46
        }
      },
      "80": {
        start: {
          line: 270,
          column: 2
        },
        end: {
          line: 276,
          column: 3
        }
      },
      "81": {
        start: {
          line: 271,
          column: 4
        },
        end: {
          line: 275,
          column: 6
        }
      },
      "82": {
        start: {
          line: 279,
          column: 22
        },
        end: {
          line: 287,
          column: 3
        }
      },
      "83": {
        start: {
          line: 289,
          column: 23
        },
        end: {
          line: 292,
          column: 3
        }
      },
      "84": {
        start: {
          line: 294,
          column: 2
        },
        end: {
          line: 300,
          column: 3
        }
      },
      "85": {
        start: {
          line: 295,
          column: 4
        },
        end: {
          line: 299,
          column: 6
        }
      },
      "86": {
        start: {
          line: 302,
          column: 2
        },
        end: {
          line: 304,
          column: 3
        }
      },
      "87": {
        start: {
          line: 303,
          column: 4
        },
        end: {
          line: 303,
          column: 42
        }
      },
      "88": {
        start: {
          line: 307,
          column: 2
        },
        end: {
          line: 307,
          column: 78
        }
      },
      "89": {
        start: {
          line: 309,
          column: 2
        },
        end: {
          line: 312,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 45,
            column: 37
          },
          end: {
            line: 45,
            column: 38
          }
        },
        loc: {
          start: {
            line: 48,
            column: 5
          },
          end: {
            line: 118,
            column: 1
          }
        },
        line: 48
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 121,
            column: 37
          },
          end: {
            line: 121,
            column: 38
          }
        },
        loc: {
          start: {
            line: 124,
            column: 5
          },
          end: {
            line: 246,
            column: 1
          }
        },
        line: 124
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 249,
            column: 40
          },
          end: {
            line: 249,
            column: 41
          }
        },
        loc: {
          start: {
            line: 252,
            column: 5
          },
          end: {
            line: 313,
            column: 1
          }
        },
        line: 252
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 51,
            column: 2
          },
          end: {
            line: 53,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 51,
            column: 2
          },
          end: {
            line: 53,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 51
      },
      "1": {
        loc: {
          start: {
            line: 59,
            column: 2
          },
          end: {
            line: 61,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 59,
            column: 2
          },
          end: {
            line: 61,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 59
      },
      "2": {
        loc: {
          start: {
            line: 67,
            column: 18
          },
          end: {
            line: 67,
            column: 91
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 67,
            column: 18
          },
          end: {
            line: 67,
            column: 49
          }
        }, {
          start: {
            line: 67,
            column: 53
          },
          end: {
            line: 67,
            column: 91
          }
        }],
        line: 67
      },
      "3": {
        loc: {
          start: {
            line: 70,
            column: 2
          },
          end: {
            line: 76,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 70,
            column: 2
          },
          end: {
            line: 76,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 70
      },
      "4": {
        loc: {
          start: {
            line: 70,
            column: 6
          },
          end: {
            line: 70,
            column: 28
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 70,
            column: 6
          },
          end: {
            line: 70,
            column: 14
          }
        }, {
          start: {
            line: 70,
            column: 18
          },
          end: {
            line: 70,
            column: 28
          }
        }],
        line: 70
      },
      "5": {
        loc: {
          start: {
            line: 97,
            column: 2
          },
          end: {
            line: 103,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 97,
            column: 2
          },
          end: {
            line: 103,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 97
      },
      "6": {
        loc: {
          start: {
            line: 105,
            column: 2
          },
          end: {
            line: 107,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 105,
            column: 2
          },
          end: {
            line: 107,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 105
      },
      "7": {
        loc: {
          start: {
            line: 111,
            column: 2
          },
          end: {
            line: 115,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 111,
            column: 2
          },
          end: {
            line: 115,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 111
      },
      "8": {
        loc: {
          start: {
            line: 111,
            column: 6
          },
          end: {
            line: 111,
            column: 28
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 111,
            column: 6
          },
          end: {
            line: 111,
            column: 14
          }
        }, {
          start: {
            line: 111,
            column: 18
          },
          end: {
            line: 111,
            column: 28
          }
        }],
        line: 111
      },
      "9": {
        loc: {
          start: {
            line: 127,
            column: 2
          },
          end: {
            line: 129,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 127,
            column: 2
          },
          end: {
            line: 129,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 127
      },
      "10": {
        loc: {
          start: {
            line: 135,
            column: 2
          },
          end: {
            line: 137,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 135,
            column: 2
          },
          end: {
            line: 137,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 135
      },
      "11": {
        loc: {
          start: {
            line: 143,
            column: 2
          },
          end: {
            line: 145,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 143,
            column: 2
          },
          end: {
            line: 145,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 143
      },
      "12": {
        loc: {
          start: {
            line: 156,
            column: 2
          },
          end: {
            line: 162,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 156,
            column: 2
          },
          end: {
            line: 162,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 156
      },
      "13": {
        loc: {
          start: {
            line: 164,
            column: 2
          },
          end: {
            line: 166,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 164,
            column: 2
          },
          end: {
            line: 166,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 164
      },
      "14": {
        loc: {
          start: {
            line: 173,
            column: 2
          },
          end: {
            line: 176,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 173,
            column: 2
          },
          end: {
            line: 176,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 173
      },
      "15": {
        loc: {
          start: {
            line: 178,
            column: 2
          },
          end: {
            line: 181,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 178,
            column: 2
          },
          end: {
            line: 181,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 178
      },
      "16": {
        loc: {
          start: {
            line: 183,
            column: 2
          },
          end: {
            line: 186,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 183,
            column: 2
          },
          end: {
            line: 186,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 183
      },
      "17": {
        loc: {
          start: {
            line: 188,
            column: 2
          },
          end: {
            line: 191,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 188,
            column: 2
          },
          end: {
            line: 191,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 188
      },
      "18": {
        loc: {
          start: {
            line: 193,
            column: 2
          },
          end: {
            line: 199,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 193,
            column: 2
          },
          end: {
            line: 199,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 193
      },
      "19": {
        loc: {
          start: {
            line: 226,
            column: 2
          },
          end: {
            line: 232,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 226,
            column: 2
          },
          end: {
            line: 232,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 226
      },
      "20": {
        loc: {
          start: {
            line: 234,
            column: 2
          },
          end: {
            line: 240,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 234,
            column: 2
          },
          end: {
            line: 240,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 234
      },
      "21": {
        loc: {
          start: {
            line: 255,
            column: 2
          },
          end: {
            line: 257,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 255,
            column: 2
          },
          end: {
            line: 257,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 255
      },
      "22": {
        loc: {
          start: {
            line: 263,
            column: 2
          },
          end: {
            line: 265,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 2
          },
          end: {
            line: 265,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 263
      },
      "23": {
        loc: {
          start: {
            line: 270,
            column: 2
          },
          end: {
            line: 276,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 270,
            column: 2
          },
          end: {
            line: 276,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 270
      },
      "24": {
        loc: {
          start: {
            line: 294,
            column: 2
          },
          end: {
            line: 300,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 294,
            column: 2
          },
          end: {
            line: 300,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 294
      },
      "25": {
        loc: {
          start: {
            line: 302,
            column: 2
          },
          end: {
            line: 304,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 302,
            column: 2
          },
          end: {
            line: 304,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 302
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c6d899e52d0b69098b6aadedd54cafd54bc7ef20"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2bw1277yma = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2bw1277yma();
/**
 * Individual User API Route
 * 
 * This route demonstrates CRUD operations for individual users with:
 * - Path parameter validation
 * - Role-based access control
 * - Optimistic concurrency control
 * - Audit logging
 */

import { requireAuth, requireRole } from '@/lib/auth-middleware';
import { createSuccessResponse, createErrorResponse, createNotFoundResponse, ApiErrorCode, HttpStatus, withErrorHandling } from '@/lib/api-response';
import { validateRequestBody, validatePathParams, userUpdateSchema, idParamSchema } from '@/lib/validation';
import { executeQuerySingle } from '@/lib/database';
// GET /api/users/[id] - Get specific user
export const GET =
/* istanbul ignore next */
(cov_2bw1277yma().s[0]++, withErrorHandling(async (request, {
  params
}) => {
  /* istanbul ignore next */
  cov_2bw1277yma().f[0]++;
  // Verify authentication
  const authResult =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[1]++, await requireAuth());
  /* istanbul ignore next */
  cov_2bw1277yma().s[2]++;
  if (!authResult.success) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[0][0]++;
    cov_2bw1277yma().s[3]++;
    return authResult.response;
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[0][1]++;
  }
  const session =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[4]++, authResult.session);

  // Validate path parameters
  const paramValidation =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[5]++, validatePathParams(params, idParamSchema));
  /* istanbul ignore next */
  cov_2bw1277yma().s[6]++;
  if (!paramValidation.success) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[1][0]++;
    cov_2bw1277yma().s[7]++;
    return paramValidation.response;
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[1][1]++;
  }
  const {
    id
  } =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[8]++, paramValidation.data);

  // Check if user can access this resource
  // Users can access their own data, admins can access any user
  const isAdmin =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[9]++,
  /* istanbul ignore next */
  (cov_2bw1277yma().b[2][0]++, session.roles.includes('admin')) ||
  /* istanbul ignore next */
  (cov_2bw1277yma().b[2][1]++, session.roles.includes('user_manager')));
  const isOwnData =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[10]++, session.userId === id);
  /* istanbul ignore next */
  cov_2bw1277yma().s[11]++;
  if (
  /* istanbul ignore next */
  (cov_2bw1277yma().b[4][0]++, !isAdmin) &&
  /* istanbul ignore next */
  (cov_2bw1277yma().b[4][1]++, !isOwnData)) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[3][0]++;
    cov_2bw1277yma().s[12]++;
    return createErrorResponse('Access denied', ApiErrorCode.FORBIDDEN, HttpStatus.FORBIDDEN);
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[3][1]++;
  }

  // Fetch user data
  const query =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[13]++, `
    SELECT 
      id,
      email,
      name,
      given_name,
      family_name,
      roles,
      created_at,
      updated_at,
      last_login,
      version
    FROM users 
    WHERE id = $1
  `);
  const result =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[14]++, await executeQuerySingle(query, [id]));
  /* istanbul ignore next */
  cov_2bw1277yma().s[15]++;
  if (!result.success) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[5][0]++;
    cov_2bw1277yma().s[16]++;
    return createErrorResponse('Failed to fetch user', ApiErrorCode.DATABASE_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[5][1]++;
  }
  cov_2bw1277yma().s[17]++;
  if (!result.data) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[6][0]++;
    cov_2bw1277yma().s[18]++;
    return createNotFoundResponse('User');
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[6][1]++;
  }

  // Remove sensitive data if not admin and not own data
  const userData =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[19]++, result.data);
  /* istanbul ignore next */
  cov_2bw1277yma().s[20]++;
  if (
  /* istanbul ignore next */
  (cov_2bw1277yma().b[8][0]++, !isAdmin) &&
  /* istanbul ignore next */
  (cov_2bw1277yma().b[8][1]++, !isOwnData)) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[7][0]++;
    cov_2bw1277yma().s[21]++;
    // Remove sensitive fields for non-admin users
    delete userData.roles;
    /* istanbul ignore next */
    cov_2bw1277yma().s[22]++;
    delete userData.last_login;
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[7][1]++;
  }
  cov_2bw1277yma().s[23]++;
  return createSuccessResponse(userData, 'User retrieved successfully');
}));

// PUT /api/users/[id] - Update specific user
export const PUT =
/* istanbul ignore next */
(cov_2bw1277yma().s[24]++, withErrorHandling(async (request, {
  params
}) => {
  /* istanbul ignore next */
  cov_2bw1277yma().f[1]++;
  // Verify authentication and role
  const roleResult =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[25]++, await requireRole(['admin', 'user_manager']));
  /* istanbul ignore next */
  cov_2bw1277yma().s[26]++;
  if (!roleResult.success) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[9][0]++;
    cov_2bw1277yma().s[27]++;
    return roleResult.response;
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[9][1]++;
  }
  const session =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[28]++, roleResult.session);

  // Validate path parameters
  const paramValidation =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[29]++, validatePathParams(params, idParamSchema));
  /* istanbul ignore next */
  cov_2bw1277yma().s[30]++;
  if (!paramValidation.success) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[10][0]++;
    cov_2bw1277yma().s[31]++;
    return paramValidation.response;
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[10][1]++;
  }
  const {
    id
  } =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[32]++, paramValidation.data);

  // Validate request body
  const bodyValidation =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[33]++, await validateRequestBody(request, userUpdateSchema));
  /* istanbul ignore next */
  cov_2bw1277yma().s[34]++;
  if (!bodyValidation.success) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[11][0]++;
    cov_2bw1277yma().s[35]++;
    return bodyValidation.response;
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[11][1]++;
  }
  const updateData =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[36]++, bodyValidation.data);

  // Check if user exists and get current version
  const currentUserQuery =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[37]++, 'SELECT id, version FROM users WHERE id = $1');
  const currentUser =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[38]++, await executeQuerySingle(currentUserQuery, [id]));
  /* istanbul ignore next */
  cov_2bw1277yma().s[39]++;
  if (!currentUser.success) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[12][0]++;
    cov_2bw1277yma().s[40]++;
    return createErrorResponse('Failed to fetch user', ApiErrorCode.DATABASE_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[12][1]++;
  }
  cov_2bw1277yma().s[41]++;
  if (!currentUser.data) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[13][0]++;
    cov_2bw1277yma().s[42]++;
    return createNotFoundResponse('User');
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[13][1]++;
  }

  // Build dynamic update query
  const updateFields =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[43]++, []);
  const values =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[44]++, []);
  let paramIndex =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[45]++, 1);
  /* istanbul ignore next */
  cov_2bw1277yma().s[46]++;
  if (updateData.name !== undefined) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[14][0]++;
    cov_2bw1277yma().s[47]++;
    updateFields.push(`name = $${paramIndex++}`);
    /* istanbul ignore next */
    cov_2bw1277yma().s[48]++;
    values.push(updateData.name);
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[14][1]++;
  }
  cov_2bw1277yma().s[49]++;
  if (updateData.given_name !== undefined) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[15][0]++;
    cov_2bw1277yma().s[50]++;
    updateFields.push(`given_name = $${paramIndex++}`);
    /* istanbul ignore next */
    cov_2bw1277yma().s[51]++;
    values.push(updateData.given_name);
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[15][1]++;
  }
  cov_2bw1277yma().s[52]++;
  if (updateData.family_name !== undefined) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[16][0]++;
    cov_2bw1277yma().s[53]++;
    updateFields.push(`family_name = $${paramIndex++}`);
    /* istanbul ignore next */
    cov_2bw1277yma().s[54]++;
    values.push(updateData.family_name);
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[16][1]++;
  }
  cov_2bw1277yma().s[55]++;
  if (updateData.roles !== undefined) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[17][0]++;
    cov_2bw1277yma().s[56]++;
    updateFields.push(`roles = $${paramIndex++}`);
    /* istanbul ignore next */
    cov_2bw1277yma().s[57]++;
    values.push(JSON.stringify(updateData.roles));
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[17][1]++;
  }
  cov_2bw1277yma().s[58]++;
  if (updateFields.length === 0) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[18][0]++;
    cov_2bw1277yma().s[59]++;
    return createErrorResponse('No valid fields to update', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST);
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[18][1]++;
  }

  // Add updated_at, version increment, and WHERE clause
  cov_2bw1277yma().s[60]++;
  updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
  /* istanbul ignore next */
  cov_2bw1277yma().s[61]++;
  updateFields.push(`version = version + 1`);

  // Add WHERE conditions
  /* istanbul ignore next */
  cov_2bw1277yma().s[62]++;
  values.push(id, currentUser.data.version);
  const updateQuery =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[63]++, `
    UPDATE users 
    SET ${updateFields.join(', ')}
    WHERE id = $${paramIndex++} AND version = $${paramIndex++}
    RETURNING 
      id,
      email,
      name,
      given_name,
      family_name,
      roles,
      created_at,
      updated_at,
      version
  `);
  const updateResult =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[64]++, await executeQuerySingle(updateQuery, values));
  /* istanbul ignore next */
  cov_2bw1277yma().s[65]++;
  if (!updateResult.success) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[19][0]++;
    cov_2bw1277yma().s[66]++;
    return createErrorResponse('Failed to update user', ApiErrorCode.DATABASE_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[19][1]++;
  }
  cov_2bw1277yma().s[67]++;
  if (!updateResult.data) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[20][0]++;
    cov_2bw1277yma().s[68]++;
    return createErrorResponse('User was modified by another process. Please refresh and try again.', ApiErrorCode.VALIDATION_ERROR, HttpStatus.CONFLICT);
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[20][1]++;
  }

  // Log user update for audit
  cov_2bw1277yma().s[69]++;
  console.log(`User updated: ${id} by ${session.email}`, updateData);
  /* istanbul ignore next */
  cov_2bw1277yma().s[70]++;
  return createSuccessResponse(updateResult.data, 'User updated successfully');
}));

// DELETE /api/users/[id] - Delete specific user
export const DELETE =
/* istanbul ignore next */
(cov_2bw1277yma().s[71]++, withErrorHandling(async (request, {
  params
}) => {
  /* istanbul ignore next */
  cov_2bw1277yma().f[2]++;
  // Verify authentication and admin role
  const roleResult =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[72]++, await requireRole(['admin']));
  /* istanbul ignore next */
  cov_2bw1277yma().s[73]++;
  if (!roleResult.success) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[21][0]++;
    cov_2bw1277yma().s[74]++;
    return roleResult.response;
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[21][1]++;
  }
  const session =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[75]++, roleResult.session);

  // Validate path parameters
  const paramValidation =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[76]++, validatePathParams(params, idParamSchema));
  /* istanbul ignore next */
  cov_2bw1277yma().s[77]++;
  if (!paramValidation.success) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[22][0]++;
    cov_2bw1277yma().s[78]++;
    return paramValidation.response;
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[22][1]++;
  }
  const {
    id
  } =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[79]++, paramValidation.data);

  // Prevent self-deletion
  /* istanbul ignore next */
  cov_2bw1277yma().s[80]++;
  if (session.userId === id) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[23][0]++;
    cov_2bw1277yma().s[81]++;
    return createErrorResponse('Cannot delete your own account', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST);
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[23][1]++;
  }

  // Soft delete user (mark as inactive instead of hard delete)
  const deleteQuery =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[82]++, `
    UPDATE users 
    SET 
      status = 'deleted',
      updated_at = CURRENT_TIMESTAMP,
      version = version + 1
    WHERE id = $1
    RETURNING id, email
  `);
  const deleteResult =
  /* istanbul ignore next */
  (cov_2bw1277yma().s[83]++, await executeQuerySingle(deleteQuery, [id]));
  /* istanbul ignore next */
  cov_2bw1277yma().s[84]++;
  if (!deleteResult.success) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[24][0]++;
    cov_2bw1277yma().s[85]++;
    return createErrorResponse('Failed to delete user', ApiErrorCode.DATABASE_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[24][1]++;
  }
  cov_2bw1277yma().s[86]++;
  if (!deleteResult.data) {
    /* istanbul ignore next */
    cov_2bw1277yma().b[25][0]++;
    cov_2bw1277yma().s[87]++;
    return createNotFoundResponse('User');
  } else
  /* istanbul ignore next */
  {
    cov_2bw1277yma().b[25][1]++;
  }

  // Log user deletion for audit
  cov_2bw1277yma().s[88]++;
  console.log(`User deleted: ${deleteResult.data.email} by ${session.email}`);
  /* istanbul ignore next */
  cov_2bw1277yma().s[89]++;
  return createSuccessResponse({
    id: deleteResult.data.id
  }, 'User deleted successfully');
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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