-- Create Renewals table in tenant schema
-- This script should be run for each tenant schema

-- Create the Renewals table
CREATE TABLE IF NOT EXISTS "tenant_0000000000000001"."Renewals" (
    "RenewalID" SERIAL PRIMARY KEY,
    "RenewalName" VARCHAR(255) NOT NULL,
    "ProductName" VARCHAR(255),
    "Version" VARCHAR(100),
    "VendorName" VARCHAR(255),
    "RenewalTypeID" INTEGER REFERENCES metadata."RenewalTypes"("RenewalTypeID"),
    "DepartmentID" INTEGER,
    "PurchaseTypeID" INTEGER REFERENCES metadata."PurchaseTypes"("PurchaseTypeID"),
    "LicensedDate" DATE,
    "RenewalDate" DATE,
    "AssociatedEmails" TEXT[], -- Array of email addresses
    "Reseller" VARCHAR(255),
    "CurrencyID" VARCHAR(3) REFERENCES metadata."Currencies"("CurrencyID"),
    "Cost" DECIMAL(15,2),
    "CostCode" VARCHAR(100),
    "LicenseCount" INTEGER,
    "Description" TEXT,
    "Notes" TEXT,
    "Status" VARCHAR(50) DEFAULT 'Active',
    "Active" BOOLEAN DEFAULT true,
    "CreatedOn" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "CreatedBy" VARCHAR(255),
    "ModifiedOn" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "ModifiedBy" VARCHAR(255)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "idx_renewals_renewal_date" ON "tenant_0000000000000001"."Renewals"("RenewalDate");
CREATE INDEX IF NOT EXISTS "idx_renewals_vendor" ON "tenant_0000000000000001"."Renewals"("VendorName");
CREATE INDEX IF NOT EXISTS "idx_renewals_status" ON "tenant_0000000000000001"."Renewals"("Status");
CREATE INDEX IF NOT EXISTS "idx_renewals_active" ON "tenant_0000000000000001"."Renewals"("Active");

-- Insert some sample data for testing
INSERT INTO "tenant_0000000000000001"."Renewals" (
    "RenewalName", "ProductName", "Version", "VendorName", "RenewalTypeID", 
    "RenewalDate", "CurrencyID", "Cost", "Status", "LicenseCount", "Description"
) VALUES 
    ('Alert Test', 'test', 'v1.0', 'test', 1, '2024-05-09', 'USD', 100000, 'Active', 1, 'Test subscription for alerts'),
    ('ASA400', 'vGat', 'v2.1', 'Cisco', 2, '2024-05-04', 'CAD', 50000, 'Active', 1, 'Cisco ASA400 warranty'),
    ('Case Test', 'v1.0', 'v1.0', 'Case Vendor', 1, '2025-05-06', 'USD', 0, 'Expired', 1, 'Test case subscription'),
    ('Cloud Suite', 'vent', 'v3.0', 'Adobe', 1, '2024-05-04', 'USD', 10000, 'Active', 5, 'Adobe Cloud Suite subscription'),
    ('Diskover', 'v1', 'v1.0', 'Diskover', 1, '2024-05-01', 'USD', 50000, 'Active', 1, 'Diskover data management subscription'),
    ('DL360', 'vG10', 'Gen10', 'HPE', 2, '2024-05-05', 'CAD', 20000, 'Active', 1, 'HPE DL360 server warranty'),
    ('M365', 'v5', 'E5', 'Microsoft', 1, '2025-05-07', 'USD', 200000, 'Expired', 100, 'Microsoft 365 E5 subscription'),
    ('M365', 'vF1', 'F1', 'Microsoft', 1, '2024-05-04', 'CAD', 150000, 'Active', 50, 'Microsoft 365 F1 subscription')
ON CONFLICT DO NOTHING;

-- Update trigger for ModifiedOn
CREATE OR REPLACE FUNCTION "tenant_0000000000000001".update_modified_on()
RETURNS TRIGGER AS $$
BEGIN
    NEW."ModifiedOn" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "tr_renewals_update_modified_on"
    BEFORE UPDATE ON "tenant_0000000000000001"."Renewals"
    FOR EACH ROW
    EXECUTE FUNCTION "tenant_0000000000000001".update_modified_on();
