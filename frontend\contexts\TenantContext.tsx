'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { TenantContext as TenantContextType } from '@/lib/types';

interface TenantContextState {
  tenant: TenantContextType | null;
  loading: boolean;
  error: string | null;
  refreshTenant: () => Promise<void>;
}

const TenantContext = createContext<TenantContextState | undefined>(undefined);

interface TenantProviderProps {
  children: ReactNode;
}

export function TenantProvider({ children }: TenantProviderProps) {
  const [tenant, setTenant] = useState<TenantContextType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTenant = async (retryCount = 0) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/clients/domain');
      const data = await response.json();

      if (!response.ok) {
        // If unauthorized and we haven't retried much, wait and retry
        if (response.status === 401 && retryCount < 3) {
          console.log(`Tenant fetch failed (401), retrying in ${(retryCount + 1) * 1000}ms... (attempt ${retryCount + 1}/3)`);
          setTimeout(() => {
            fetchTenant(retryCount + 1);
          }, (retryCount + 1) * 1000);
          return;
        }
        throw new Error(data.error || 'Failed to fetch tenant information');
      }

      if (data.success && data.data?.client) {
        setTenant(data.data.client);
        console.log('✅ Tenant context loaded successfully:', data.data.client.clientName);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('❌ Tenant fetch error:', errorMessage);
      setError(errorMessage);
      setTenant(null);
    } finally {
      setLoading(false);
    }
  };

  const refreshTenant = async () => {
    await fetchTenant();
  };

  useEffect(() => {
    // Give the authentication system a moment to initialize
    const timer = setTimeout(() => {
      fetchTenant();
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const value: TenantContextState = {
    tenant,
    loading,
    error,
    refreshTenant
  };

  return (
    <TenantContext.Provider value={value}>
      {children}
    </TenantContext.Provider>
  );
}

export function useTenant() {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
}

// Hook for getting tenant schema name for database queries
export function useTenantSchema() {
  const { tenant } = useTenant();
  return tenant?.tenantSchema || null;
}

// Hook for checking if tenant has specific features
export function useTenantFeatures() {
  const { tenant } = useTenant();
  
  const hasFeature = (featureName: string): boolean => {
    return (tenant?.settings?.features as any)?.[featureName] === true;
  };

  const getFeatureConfig = (featureName: string): any => {
    return (tenant?.settings?.features as any)?.[featureName];
  };
  
  return {
    hasFeature,
    getFeatureConfig,
    features: tenant?.settings?.features || {}
  };
}
