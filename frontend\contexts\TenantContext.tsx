'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { TenantContext as TenantContextType } from '@/lib/clients';

interface TenantContextState {
  tenant: TenantContextType | null;
  loading: boolean;
  error: string | null;
  refreshTenant: () => Promise<void>;
}

const TenantContext = createContext<TenantContextState | undefined>(undefined);

interface TenantProviderProps {
  children: ReactNode;
}

export function TenantProvider({ children }: TenantProviderProps) {
  const [tenant, setTenant] = useState<TenantContextType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTenant = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/clients/domain');
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch tenant information');
      }
      
      if (data.success && data.client) {
        setTenant(data.client);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      setTenant(null);
    } finally {
      setLoading(false);
    }
  };

  const refreshTenant = async () => {
    await fetchTenant();
  };

  useEffect(() => {
    fetchTenant();
  }, []);

  const value: TenantContextState = {
    tenant,
    loading,
    error,
    refreshTenant
  };

  return (
    <TenantContext.Provider value={value}>
      {children}
    </TenantContext.Provider>
  );
}

export function useTenant() {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
}

// Hook for getting tenant schema name for database queries
export function useTenantSchema() {
  const { tenant } = useTenant();
  return tenant?.tenantSchema || null;
}

// Hook for checking if tenant has specific features
export function useTenantFeatures() {
  const { tenant } = useTenant();
  
  const hasFeature = (featureName: string): boolean => {
    return tenant?.settings?.features?.[featureName] === true;
  };
  
  const getFeatureConfig = (featureName: string): any => {
    return tenant?.settings?.features?.[featureName];
  };
  
  return {
    hasFeature,
    getFeatureConfig,
    features: tenant?.settings?.features || {}
  };
}
