df36ff4426b3485a81b74d95e059743e
'use client';

/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\TenantContext.tsx";
var __jsx = React.createElement;
function cov_93c1ej79g() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\TenantContext.tsx";
  var hash = "275fe873f610eabb59e730461df1a81797bf6378";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\TenantContext.tsx",
    statementMap: {
      "0": {
        start: {
          line: 13,
          column: 22
        },
        end: {
          line: 13,
          column: 78
        }
      },
      "1": {
        start: {
          line: 20,
          column: 30
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "2": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "3": {
        start: {
          line: 22,
          column: 28
        },
        end: {
          line: 22,
          column: 57
        }
      },
      "4": {
        start: {
          line: 24,
          column: 22
        },
        end: {
          line: 58,
          column: 3
        }
      },
      "5": {
        start: {
          line: 25,
          column: 4
        },
        end: {
          line: 57,
          column: 5
        }
      },
      "6": {
        start: {
          line: 26,
          column: 6
        },
        end: {
          line: 26,
          column: 23
        }
      },
      "7": {
        start: {
          line: 27,
          column: 6
        },
        end: {
          line: 27,
          column: 21
        }
      },
      "8": {
        start: {
          line: 29,
          column: 23
        },
        end: {
          line: 29,
          column: 57
        }
      },
      "9": {
        start: {
          line: 30,
          column: 19
        },
        end: {
          line: 30,
          column: 40
        }
      },
      "10": {
        start: {
          line: 32,
          column: 6
        },
        end: {
          line: 42,
          column: 7
        }
      },
      "11": {
        start: {
          line: 34,
          column: 8
        },
        end: {
          line: 40,
          column: 9
        }
      },
      "12": {
        start: {
          line: 35,
          column: 10
        },
        end: {
          line: 35,
          column: 126
        }
      },
      "13": {
        start: {
          line: 36,
          column: 10
        },
        end: {
          line: 38,
          column: 38
        }
      },
      "14": {
        start: {
          line: 37,
          column: 12
        },
        end: {
          line: 37,
          column: 40
        }
      },
      "15": {
        start: {
          line: 39,
          column: 10
        },
        end: {
          line: 39,
          column: 17
        }
      },
      "16": {
        start: {
          line: 41,
          column: 8
        },
        end: {
          line: 41,
          column: 76
        }
      },
      "17": {
        start: {
          line: 44,
          column: 6
        },
        end: {
          line: 49,
          column: 7
        }
      },
      "18": {
        start: {
          line: 45,
          column: 8
        },
        end: {
          line: 45,
          column: 36
        }
      },
      "19": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 90
        }
      },
      "20": {
        start: {
          line: 48,
          column: 8
        },
        end: {
          line: 48,
          column: 51
        }
      },
      "21": {
        start: {
          line: 51,
          column: 27
        },
        end: {
          line: 51,
          column: 88
        }
      },
      "22": {
        start: {
          line: 52,
          column: 6
        },
        end: {
          line: 52,
          column: 59
        }
      },
      "23": {
        start: {
          line: 53,
          column: 6
        },
        end: {
          line: 53,
          column: 29
        }
      },
      "24": {
        start: {
          line: 54,
          column: 6
        },
        end: {
          line: 54,
          column: 22
        }
      },
      "25": {
        start: {
          line: 56,
          column: 6
        },
        end: {
          line: 56,
          column: 24
        }
      },
      "26": {
        start: {
          line: 60,
          column: 24
        },
        end: {
          line: 62,
          column: 3
        }
      },
      "27": {
        start: {
          line: 61,
          column: 4
        },
        end: {
          line: 61,
          column: 24
        }
      },
      "28": {
        start: {
          line: 64,
          column: 2
        },
        end: {
          line: 71,
          column: 9
        }
      },
      "29": {
        start: {
          line: 66,
          column: 18
        },
        end: {
          line: 68,
          column: 12
        }
      },
      "30": {
        start: {
          line: 67,
          column: 6
        },
        end: {
          line: 67,
          column: 20
        }
      },
      "31": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 70,
          column: 37
        }
      },
      "32": {
        start: {
          line: 70,
          column: 17
        },
        end: {
          line: 70,
          column: 36
        }
      },
      "33": {
        start: {
          line: 73,
          column: 36
        },
        end: {
          line: 78,
          column: 3
        }
      },
      "34": {
        start: {
          line: 80,
          column: 2
        },
        end: {
          line: 84,
          column: 4
        }
      },
      "35": {
        start: {
          line: 88,
          column: 18
        },
        end: {
          line: 88,
          column: 43
        }
      },
      "36": {
        start: {
          line: 89,
          column: 2
        },
        end: {
          line: 91,
          column: 3
        }
      },
      "37": {
        start: {
          line: 90,
          column: 4
        },
        end: {
          line: 90,
          column: 70
        }
      },
      "38": {
        start: {
          line: 92,
          column: 2
        },
        end: {
          line: 92,
          column: 17
        }
      },
      "39": {
        start: {
          line: 97,
          column: 21
        },
        end: {
          line: 97,
          column: 32
        }
      },
      "40": {
        start: {
          line: 98,
          column: 2
        },
        end: {
          line: 98,
          column: 38
        }
      },
      "41": {
        start: {
          line: 103,
          column: 21
        },
        end: {
          line: 103,
          column: 32
        }
      },
      "42": {
        start: {
          line: 105,
          column: 21
        },
        end: {
          line: 107,
          column: 3
        }
      },
      "43": {
        start: {
          line: 106,
          column: 4
        },
        end: {
          line: 106,
          column: 71
        }
      },
      "44": {
        start: {
          line: 109,
          column: 27
        },
        end: {
          line: 111,
          column: 3
        }
      },
      "45": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 110,
          column: 62
        }
      },
      "46": {
        start: {
          line: 113,
          column: 2
        },
        end: {
          line: 117,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "TenantProvider",
        decl: {
          start: {
            line: 19,
            column: 16
          },
          end: {
            line: 19,
            column: 30
          }
        },
        loc: {
          start: {
            line: 19,
            column: 66
          },
          end: {
            line: 85,
            column: 1
          }
        },
        line: 19
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 24,
            column: 22
          },
          end: {
            line: 24,
            column: 23
          }
        },
        loc: {
          start: {
            line: 24,
            column: 48
          },
          end: {
            line: 58,
            column: 3
          }
        },
        line: 24
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 36,
            column: 21
          },
          end: {
            line: 36,
            column: 22
          }
        },
        loc: {
          start: {
            line: 36,
            column: 27
          },
          end: {
            line: 38,
            column: 11
          }
        },
        line: 36
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 60,
            column: 24
          },
          end: {
            line: 60,
            column: 25
          }
        },
        loc: {
          start: {
            line: 60,
            column: 36
          },
          end: {
            line: 62,
            column: 3
          }
        },
        line: 60
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 64,
            column: 12
          },
          end: {
            line: 64,
            column: 13
          }
        },
        loc: {
          start: {
            line: 64,
            column: 18
          },
          end: {
            line: 71,
            column: 3
          }
        },
        line: 64
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 66,
            column: 29
          },
          end: {
            line: 66,
            column: 30
          }
        },
        loc: {
          start: {
            line: 66,
            column: 35
          },
          end: {
            line: 68,
            column: 5
          }
        },
        line: 66
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 70,
            column: 11
          },
          end: {
            line: 70,
            column: 12
          }
        },
        loc: {
          start: {
            line: 70,
            column: 17
          },
          end: {
            line: 70,
            column: 36
          }
        },
        line: 70
      },
      "7": {
        name: "useTenant",
        decl: {
          start: {
            line: 87,
            column: 16
          },
          end: {
            line: 87,
            column: 25
          }
        },
        loc: {
          start: {
            line: 87,
            column: 28
          },
          end: {
            line: 93,
            column: 1
          }
        },
        line: 87
      },
      "8": {
        name: "useTenantSchema",
        decl: {
          start: {
            line: 96,
            column: 16
          },
          end: {
            line: 96,
            column: 31
          }
        },
        loc: {
          start: {
            line: 96,
            column: 34
          },
          end: {
            line: 99,
            column: 1
          }
        },
        line: 96
      },
      "9": {
        name: "useTenantFeatures",
        decl: {
          start: {
            line: 102,
            column: 16
          },
          end: {
            line: 102,
            column: 33
          }
        },
        loc: {
          start: {
            line: 102,
            column: 36
          },
          end: {
            line: 118,
            column: 1
          }
        },
        line: 102
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 105,
            column: 21
          },
          end: {
            line: 105,
            column: 22
          }
        },
        loc: {
          start: {
            line: 105,
            column: 55
          },
          end: {
            line: 107,
            column: 3
          }
        },
        line: 105
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 109,
            column: 27
          },
          end: {
            line: 109,
            column: 28
          }
        },
        loc: {
          start: {
            line: 109,
            column: 57
          },
          end: {
            line: 111,
            column: 3
          }
        },
        line: 109
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 24,
            column: 29
          },
          end: {
            line: 24,
            column: 43
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 24,
            column: 42
          },
          end: {
            line: 24,
            column: 43
          }
        }],
        line: 24
      },
      "1": {
        loc: {
          start: {
            line: 32,
            column: 6
          },
          end: {
            line: 42,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 6
          },
          end: {
            line: 42,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      },
      "2": {
        loc: {
          start: {
            line: 34,
            column: 8
          },
          end: {
            line: 40,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 8
          },
          end: {
            line: 40,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "3": {
        loc: {
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 34,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 34,
            column: 35
          }
        }, {
          start: {
            line: 34,
            column: 39
          },
          end: {
            line: 34,
            column: 53
          }
        }],
        line: 34
      },
      "4": {
        loc: {
          start: {
            line: 41,
            column: 24
          },
          end: {
            line: 41,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 41,
            column: 24
          },
          end: {
            line: 41,
            column: 34
          }
        }, {
          start: {
            line: 41,
            column: 38
          },
          end: {
            line: 41,
            column: 74
          }
        }],
        line: 41
      },
      "5": {
        loc: {
          start: {
            line: 44,
            column: 6
          },
          end: {
            line: 49,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 6
          },
          end: {
            line: 49,
            column: 7
          }
        }, {
          start: {
            line: 47,
            column: 13
          },
          end: {
            line: 49,
            column: 7
          }
        }],
        line: 44
      },
      "6": {
        loc: {
          start: {
            line: 44,
            column: 10
          },
          end: {
            line: 44,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 44,
            column: 10
          },
          end: {
            line: 44,
            column: 22
          }
        }, {
          start: {
            line: 44,
            column: 26
          },
          end: {
            line: 44,
            column: 43
          }
        }],
        line: 44
      },
      "7": {
        loc: {
          start: {
            line: 51,
            column: 27
          },
          end: {
            line: 51,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 51,
            column: 50
          },
          end: {
            line: 51,
            column: 61
          }
        }, {
          start: {
            line: 51,
            column: 64
          },
          end: {
            line: 51,
            column: 88
          }
        }],
        line: 51
      },
      "8": {
        loc: {
          start: {
            line: 89,
            column: 2
          },
          end: {
            line: 91,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 89,
            column: 2
          },
          end: {
            line: 91,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 89
      },
      "9": {
        loc: {
          start: {
            line: 98,
            column: 9
          },
          end: {
            line: 98,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 98,
            column: 9
          },
          end: {
            line: 98,
            column: 29
          }
        }, {
          start: {
            line: 98,
            column: 33
          },
          end: {
            line: 98,
            column: 37
          }
        }],
        line: 98
      },
      "10": {
        loc: {
          start: {
            line: 116,
            column: 14
          },
          end: {
            line: 116,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 116,
            column: 14
          },
          end: {
            line: 116,
            column: 40
          }
        }, {
          start: {
            line: 116,
            column: 44
          },
          end: {
            line: 116,
            column: 46
          }
        }],
        line: 116
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "275fe873f610eabb59e730461df1a81797bf6378"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_93c1ej79g = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_93c1ej79g();
import React, { createContext, useContext, useState, useEffect } from 'react';
const TenantContext =
/* istanbul ignore next */
(cov_93c1ej79g().s[0]++, /*#__PURE__*/createContext(undefined));
export function TenantProvider({
  children
}) {
  /* istanbul ignore next */
  cov_93c1ej79g().f[0]++;
  const [tenant, setTenant] =
  /* istanbul ignore next */
  (cov_93c1ej79g().s[1]++, useState(null));
  const [loading, setLoading] =
  /* istanbul ignore next */
  (cov_93c1ej79g().s[2]++, useState(true));
  const [error, setError] =
  /* istanbul ignore next */
  (cov_93c1ej79g().s[3]++, useState(null));
  /* istanbul ignore next */
  cov_93c1ej79g().s[4]++;
  const fetchTenant = async (retryCount =
  /* istanbul ignore next */
  (cov_93c1ej79g().b[0][0]++, 0)) => {
    /* istanbul ignore next */
    cov_93c1ej79g().f[1]++;
    cov_93c1ej79g().s[5]++;
    try {
      /* istanbul ignore next */
      cov_93c1ej79g().s[6]++;
      setLoading(true);
      /* istanbul ignore next */
      cov_93c1ej79g().s[7]++;
      setError(null);
      const response =
      /* istanbul ignore next */
      (cov_93c1ej79g().s[8]++, await fetch('/api/clients/domain'));
      const data =
      /* istanbul ignore next */
      (cov_93c1ej79g().s[9]++, await response.json());
      /* istanbul ignore next */
      cov_93c1ej79g().s[10]++;
      if (!response.ok) {
        /* istanbul ignore next */
        cov_93c1ej79g().b[1][0]++;
        cov_93c1ej79g().s[11]++;
        // If unauthorized and we haven't retried much, wait and retry
        if (
        /* istanbul ignore next */
        (cov_93c1ej79g().b[3][0]++, response.status === 401) &&
        /* istanbul ignore next */
        (cov_93c1ej79g().b[3][1]++, retryCount < 3)) {
          /* istanbul ignore next */
          cov_93c1ej79g().b[2][0]++;
          cov_93c1ej79g().s[12]++;
          console.log(`Tenant fetch failed (401), retrying in ${(retryCount + 1) * 1000}ms... (attempt ${retryCount + 1}/3)`);
          /* istanbul ignore next */
          cov_93c1ej79g().s[13]++;
          setTimeout(() => {
            /* istanbul ignore next */
            cov_93c1ej79g().f[2]++;
            cov_93c1ej79g().s[14]++;
            fetchTenant(retryCount + 1);
          }, (retryCount + 1) * 1000);
          /* istanbul ignore next */
          cov_93c1ej79g().s[15]++;
          return;
        } else
        /* istanbul ignore next */
        {
          cov_93c1ej79g().b[2][1]++;
        }
        cov_93c1ej79g().s[16]++;
        throw new Error(
        /* istanbul ignore next */
        (cov_93c1ej79g().b[4][0]++, data.error) ||
        /* istanbul ignore next */
        (cov_93c1ej79g().b[4][1]++, 'Failed to fetch tenant information'));
      } else
      /* istanbul ignore next */
      {
        cov_93c1ej79g().b[1][1]++;
      }
      cov_93c1ej79g().s[17]++;
      if (
      /* istanbul ignore next */
      (cov_93c1ej79g().b[6][0]++, data.success) &&
      /* istanbul ignore next */
      (cov_93c1ej79g().b[6][1]++, data.data?.client)) {
        /* istanbul ignore next */
        cov_93c1ej79g().b[5][0]++;
        cov_93c1ej79g().s[18]++;
        setTenant(data.data.client);
        /* istanbul ignore next */
        cov_93c1ej79g().s[19]++;
        console.log('✅ Tenant context loaded successfully:', data.data.client.clientName);
      } else {
        /* istanbul ignore next */
        cov_93c1ej79g().b[5][1]++;
        cov_93c1ej79g().s[20]++;
        throw new Error('Invalid response format');
      }
    } catch (err) {
      const errorMessage =
      /* istanbul ignore next */
      (cov_93c1ej79g().s[21]++, err instanceof Error ?
      /* istanbul ignore next */
      (cov_93c1ej79g().b[7][0]++, err.message) :
      /* istanbul ignore next */
      (cov_93c1ej79g().b[7][1]++, 'Unknown error occurred'));
      /* istanbul ignore next */
      cov_93c1ej79g().s[22]++;
      console.error('❌ Tenant fetch error:', errorMessage);
      /* istanbul ignore next */
      cov_93c1ej79g().s[23]++;
      setError(errorMessage);
      /* istanbul ignore next */
      cov_93c1ej79g().s[24]++;
      setTenant(null);
    } finally {
      /* istanbul ignore next */
      cov_93c1ej79g().s[25]++;
      setLoading(false);
    }
  };
  /* istanbul ignore next */
  cov_93c1ej79g().s[26]++;
  const refreshTenant = async () => {
    /* istanbul ignore next */
    cov_93c1ej79g().f[3]++;
    cov_93c1ej79g().s[27]++;
    await fetchTenant();
  };
  /* istanbul ignore next */
  cov_93c1ej79g().s[28]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_93c1ej79g().f[4]++;
    // Give the authentication system a moment to initialize
    const timer =
    /* istanbul ignore next */
    (cov_93c1ej79g().s[29]++, setTimeout(() => {
      /* istanbul ignore next */
      cov_93c1ej79g().f[5]++;
      cov_93c1ej79g().s[30]++;
      fetchTenant();
    }, 1000));
    /* istanbul ignore next */
    cov_93c1ej79g().s[31]++;
    return () => {
      /* istanbul ignore next */
      cov_93c1ej79g().f[6]++;
      cov_93c1ej79g().s[32]++;
      return clearTimeout(timer);
    };
  }, []);
  const value =
  /* istanbul ignore next */
  (cov_93c1ej79g().s[33]++, {
    tenant,
    loading,
    error,
    refreshTenant
  });
  /* istanbul ignore next */
  cov_93c1ej79g().s[34]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  TenantContext.Provider,
  /* istanbul ignore next */
  {
    value: value,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 81,
      columnNumber: 5
    }
  }, children);
}
export function useTenant() {
  /* istanbul ignore next */
  cov_93c1ej79g().f[7]++;
  const context =
  /* istanbul ignore next */
  (cov_93c1ej79g().s[35]++, useContext(TenantContext));
  /* istanbul ignore next */
  cov_93c1ej79g().s[36]++;
  if (context === undefined) {
    /* istanbul ignore next */
    cov_93c1ej79g().b[8][0]++;
    cov_93c1ej79g().s[37]++;
    throw new Error('useTenant must be used within a TenantProvider');
  } else
  /* istanbul ignore next */
  {
    cov_93c1ej79g().b[8][1]++;
  }
  cov_93c1ej79g().s[38]++;
  return context;
}

// Hook for getting tenant schema name for database queries
export function useTenantSchema() {
  /* istanbul ignore next */
  cov_93c1ej79g().f[8]++;
  const {
    tenant
  } =
  /* istanbul ignore next */
  (cov_93c1ej79g().s[39]++, useTenant());
  /* istanbul ignore next */
  cov_93c1ej79g().s[40]++;
  return /* istanbul ignore next */(cov_93c1ej79g().b[9][0]++, tenant?.tenantSchema) ||
  /* istanbul ignore next */
  (cov_93c1ej79g().b[9][1]++, null);
}

// Hook for checking if tenant has specific features
export function useTenantFeatures() {
  /* istanbul ignore next */
  cov_93c1ej79g().f[9]++;
  const {
    tenant
  } =
  /* istanbul ignore next */
  (cov_93c1ej79g().s[41]++, useTenant());
  /* istanbul ignore next */
  cov_93c1ej79g().s[42]++;
  const hasFeature = featureName => {
    /* istanbul ignore next */
    cov_93c1ej79g().f[10]++;
    cov_93c1ej79g().s[43]++;
    return tenant?.settings?.features?.[featureName] === true;
  };
  /* istanbul ignore next */
  cov_93c1ej79g().s[44]++;
  const getFeatureConfig = featureName => {
    /* istanbul ignore next */
    cov_93c1ej79g().f[11]++;
    cov_93c1ej79g().s[45]++;
    return tenant?.settings?.features?.[featureName];
  };
  /* istanbul ignore next */
  cov_93c1ej79g().s[46]++;
  return {
    hasFeature,
    getFeatureConfig,
    features:
    /* istanbul ignore next */
    (cov_93c1ej79g().b[10][0]++, tenant?.settings?.features) ||
    /* istanbul ignore next */
    (cov_93c1ej79g().b[10][1]++, {})
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfanN4RmlsZU5hbWUiLCJfX2pzeCIsIlJlYWN0IiwiY3JlYXRlRWxlbWVudCIsImNvdl85M2MxZWo3OWciLCJwYXRoIiwiaGFzaCIsImdsb2JhbCIsIkZ1bmN0aW9uIiwiZ2N2IiwiY292ZXJhZ2VEYXRhIiwic3RhdGVtZW50TWFwIiwic3RhcnQiLCJsaW5lIiwiY29sdW1uIiwiZW5kIiwiZm5NYXAiLCJuYW1lIiwiZGVjbCIsImxvYyIsImJyYW5jaE1hcCIsInR5cGUiLCJsb2NhdGlvbnMiLCJ1bmRlZmluZWQiLCJzIiwiZiIsImIiLCJfY292ZXJhZ2VTY2hlbWEiLCJjb3ZlcmFnZSIsImFjdHVhbENvdmVyYWdlIiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIlRlbmFudENvbnRleHQiLCJUZW5hbnRQcm92aWRlciIsImNoaWxkcmVuIiwidGVuYW50Iiwic2V0VGVuYW50IiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwiZmV0Y2hUZW5hbnQiLCJyZXRyeUNvdW50IiwicmVzcG9uc2UiLCJmZXRjaCIsImRhdGEiLCJqc29uIiwib2siLCJzdGF0dXMiLCJjb25zb2xlIiwibG9nIiwic2V0VGltZW91dCIsIkVycm9yIiwic3VjY2VzcyIsImNsaWVudCIsImNsaWVudE5hbWUiLCJlcnIiLCJlcnJvck1lc3NhZ2UiLCJtZXNzYWdlIiwicmVmcmVzaFRlbmFudCIsInRpbWVyIiwiY2xlYXJUaW1lb3V0IiwidmFsdWUiLCJQcm92aWRlciIsIl9fc2VsZiIsIl9fc291cmNlIiwiZmlsZU5hbWUiLCJsaW5lTnVtYmVyIiwiY29sdW1uTnVtYmVyIiwidXNlVGVuYW50IiwiY29udGV4dCIsInVzZVRlbmFudFNjaGVtYSIsInRlbmFudFNjaGVtYSIsInVzZVRlbmFudEZlYXR1cmVzIiwiaGFzRmVhdHVyZSIsImZlYXR1cmVOYW1lIiwic2V0dGluZ3MiLCJmZWF0dXJlcyIsImdldEZlYXR1cmVDb25maWciXSwic291cmNlcyI6WyJUZW5hbnRDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlRWZmZWN0LCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBUZW5hbnRDb250ZXh0IGFzIFRlbmFudENvbnRleHRUeXBlIH0gZnJvbSAnQC9saWIvdHlwZXMnO1xuXG5pbnRlcmZhY2UgVGVuYW50Q29udGV4dFN0YXRlIHtcbiAgdGVuYW50OiBUZW5hbnRDb250ZXh0VHlwZSB8IG51bGw7XG4gIGxvYWRpbmc6IGJvb2xlYW47XG4gIGVycm9yOiBzdHJpbmcgfCBudWxsO1xuICByZWZyZXNoVGVuYW50OiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xufVxuXG5jb25zdCBUZW5hbnRDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxUZW5hbnRDb250ZXh0U3RhdGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XG5cbmludGVyZmFjZSBUZW5hbnRQcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFRlbmFudFByb3ZpZGVyKHsgY2hpbGRyZW4gfTogVGVuYW50UHJvdmlkZXJQcm9wcykge1xuICBjb25zdCBbdGVuYW50LCBzZXRUZW5hbnRdID0gdXNlU3RhdGU8VGVuYW50Q29udGV4dFR5cGUgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG5cbiAgY29uc3QgZmV0Y2hUZW5hbnQgPSBhc3luYyAocmV0cnlDb3VudCA9IDApID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2NsaWVudHMvZG9tYWluJyk7XG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIC8vIElmIHVuYXV0aG9yaXplZCBhbmQgd2UgaGF2ZW4ndCByZXRyaWVkIG11Y2gsIHdhaXQgYW5kIHJldHJ5XG4gICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwMSAmJiByZXRyeUNvdW50IDwgMykge1xuICAgICAgICAgIGNvbnNvbGUubG9nKGBUZW5hbnQgZmV0Y2ggZmFpbGVkICg0MDEpLCByZXRyeWluZyBpbiAkeyhyZXRyeUNvdW50ICsgMSkgKiAxMDAwfW1zLi4uIChhdHRlbXB0ICR7cmV0cnlDb3VudCArIDF9LzMpYCk7XG4gICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICBmZXRjaFRlbmFudChyZXRyeUNvdW50ICsgMSk7XG4gICAgICAgICAgfSwgKHJldHJ5Q291bnQgKyAxKSAqIDEwMDApO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZGF0YS5lcnJvciB8fCAnRmFpbGVkIHRvIGZldGNoIHRlbmFudCBpbmZvcm1hdGlvbicpO1xuICAgICAgfVxuXG4gICAgICBpZiAoZGF0YS5zdWNjZXNzICYmIGRhdGEuZGF0YT8uY2xpZW50KSB7XG4gICAgICAgIHNldFRlbmFudChkYXRhLmRhdGEuY2xpZW50KTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBUZW5hbnQgY29udGV4dCBsb2FkZWQgc3VjY2Vzc2Z1bGx5OicsIGRhdGEuZGF0YS5jbGllbnQuY2xpZW50TmFtZSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgcmVzcG9uc2UgZm9ybWF0Jyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3Igb2NjdXJyZWQnO1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIFRlbmFudCBmZXRjaCBlcnJvcjonLCBlcnJvck1lc3NhZ2UpO1xuICAgICAgc2V0RXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICAgIHNldFRlbmFudChudWxsKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHJlZnJlc2hUZW5hbnQgPSBhc3luYyAoKSA9PiB7XG4gICAgYXdhaXQgZmV0Y2hUZW5hbnQoKTtcbiAgfTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIEdpdmUgdGhlIGF1dGhlbnRpY2F0aW9uIHN5c3RlbSBhIG1vbWVudCB0byBpbml0aWFsaXplXG4gICAgY29uc3QgdGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIGZldGNoVGVuYW50KCk7XG4gICAgfSwgMTAwMCk7XG5cbiAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVyKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IHZhbHVlOiBUZW5hbnRDb250ZXh0U3RhdGUgPSB7XG4gICAgdGVuYW50LFxuICAgIGxvYWRpbmcsXG4gICAgZXJyb3IsXG4gICAgcmVmcmVzaFRlbmFudFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPFRlbmFudENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1RlbmFudENvbnRleHQuUHJvdmlkZXI+XG4gICk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VUZW5hbnQoKSB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KFRlbmFudENvbnRleHQpO1xuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VUZW5hbnQgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIFRlbmFudFByb3ZpZGVyJyk7XG4gIH1cbiAgcmV0dXJuIGNvbnRleHQ7XG59XG5cbi8vIEhvb2sgZm9yIGdldHRpbmcgdGVuYW50IHNjaGVtYSBuYW1lIGZvciBkYXRhYmFzZSBxdWVyaWVzXG5leHBvcnQgZnVuY3Rpb24gdXNlVGVuYW50U2NoZW1hKCkge1xuICBjb25zdCB7IHRlbmFudCB9ID0gdXNlVGVuYW50KCk7XG4gIHJldHVybiB0ZW5hbnQ/LnRlbmFudFNjaGVtYSB8fCBudWxsO1xufVxuXG4vLyBIb29rIGZvciBjaGVja2luZyBpZiB0ZW5hbnQgaGFzIHNwZWNpZmljIGZlYXR1cmVzXG5leHBvcnQgZnVuY3Rpb24gdXNlVGVuYW50RmVhdHVyZXMoKSB7XG4gIGNvbnN0IHsgdGVuYW50IH0gPSB1c2VUZW5hbnQoKTtcbiAgXG4gIGNvbnN0IGhhc0ZlYXR1cmUgPSAoZmVhdHVyZU5hbWU6IHN0cmluZyk6IGJvb2xlYW4gPT4ge1xuICAgIHJldHVybiAodGVuYW50Py5zZXR0aW5ncz8uZmVhdHVyZXMgYXMgYW55KT8uW2ZlYXR1cmVOYW1lXSA9PT0gdHJ1ZTtcbiAgfTtcblxuICBjb25zdCBnZXRGZWF0dXJlQ29uZmlnID0gKGZlYXR1cmVOYW1lOiBzdHJpbmcpOiBhbnkgPT4ge1xuICAgIHJldHVybiAodGVuYW50Py5zZXR0aW5ncz8uZmVhdHVyZXMgYXMgYW55KT8uW2ZlYXR1cmVOYW1lXTtcbiAgfTtcbiAgXG4gIHJldHVybiB7XG4gICAgaGFzRmVhdHVyZSxcbiAgICBnZXRGZWF0dXJlQ29uZmlnLFxuICAgIGZlYXR1cmVzOiB0ZW5hbnQ/LnNldHRpbmdzPy5mZWF0dXJlcyB8fCB7fVxuICB9O1xufVxuIl0sIm1hcHBpbmdzIjoiQUFBQSxZQUFZOztBQUFDO0FBQUEsSUFBQUEsWUFBQTtBQUFBLElBQUFDLEtBQUEsR0FBQUMsS0FBQSxDQUFBQyxhQUFBO0FBQUEsU0FBQUMsY0FBQTtFQUFBLElBQUFDLElBQUE7RUFBQSxJQUFBQyxJQUFBO0VBQUEsSUFBQUMsTUFBQSxPQUFBQyxRQUFBO0VBQUEsSUFBQUMsR0FBQTtFQUFBLElBQUFDLFlBQUE7SUFBQUwsSUFBQTtJQUFBTSxZQUFBO01BQUE7UUFBQUMsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtJQUFBO0lBQUFFLEtBQUE7TUFBQTtRQUFBQyxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7SUFBQTtJQUFBTyxTQUFBO01BQUE7UUFBQUQsR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO0lBQUE7SUFBQVcsQ0FBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7SUFBQTtJQUFBQyxDQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO0lBQUE7SUFBQUMsQ0FBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7SUFBQTtJQUFBQyxlQUFBO0lBQUFyQixJQUFBO0VBQUE7RUFBQSxJQUFBc0IsUUFBQSxHQUFBckIsTUFBQSxDQUFBRSxHQUFBLE1BQUFGLE1BQUEsQ0FBQUUsR0FBQTtFQUFBLEtBQUFtQixRQUFBLENBQUF2QixJQUFBLEtBQUF1QixRQUFBLENBQUF2QixJQUFBLEVBQUFDLElBQUEsS0FBQUEsSUFBQTtJQUFBc0IsUUFBQSxDQUFBdkIsSUFBQSxJQUFBSyxZQUFBO0VBQUE7RUFBQSxJQUFBbUIsY0FBQSxHQUFBRCxRQUFBLENBQUF2QixJQUFBO0VBQUE7SUFlRDtJQUFBRCxhQUFBLFlBQUFBLENBQUE7TUFBQSxPQUFBeUIsY0FBQTtJQUFBO0VBQUE7RUFBQSxPQUFBQSxjQUFBO0FBQUE7QUFBQXpCLGFBQUE7QUFiWixPQUFPRixLQUFLLElBQUk0QixhQUFhLEVBQUVDLFVBQVUsRUFBRUMsUUFBUSxFQUFFQyxTQUFTLFFBQW1CLE9BQU87QUFVeEYsTUFBTUMsYUFBYTtBQUFBO0FBQUEsQ0FBQTlCLGFBQUEsR0FBQW9CLENBQUEsb0JBQUdNLGFBQWEsQ0FBaUNQLFNBQVMsQ0FBQztBQU05RSxPQUFPLFNBQVNZLGNBQWNBLENBQUM7RUFBRUM7QUFBOEIsQ0FBQyxFQUFFO0VBQUE7RUFBQWhDLGFBQUEsR0FBQXFCLENBQUE7RUFDaEUsTUFBTSxDQUFDWSxNQUFNLEVBQUVDLFNBQVMsQ0FBQztFQUFBO0VBQUEsQ0FBQWxDLGFBQUEsR0FBQW9CLENBQUEsT0FBR1EsUUFBUSxDQUEyQixJQUFJLENBQUM7RUFDcEUsTUFBTSxDQUFDTyxPQUFPLEVBQUVDLFVBQVUsQ0FBQztFQUFBO0VBQUEsQ0FBQXBDLGFBQUEsR0FBQW9CLENBQUEsT0FBR1EsUUFBUSxDQUFDLElBQUksQ0FBQztFQUM1QyxNQUFNLENBQUNTLEtBQUssRUFBRUMsUUFBUSxDQUFDO0VBQUE7RUFBQSxDQUFBdEMsYUFBQSxHQUFBb0IsQ0FBQSxPQUFHUSxRQUFRLENBQWdCLElBQUksQ0FBQztFQUFDO0VBQUE1QixhQUFBLEdBQUFvQixDQUFBO0VBRXhELE1BQU1tQixXQUFXLEdBQUcsTUFBQUEsQ0FBT0MsVUFBVTtFQUFBO0VBQUEsQ0FBQXhDLGFBQUEsR0FBQXNCLENBQUEsVUFBRyxDQUFDLE1BQUs7SUFBQTtJQUFBdEIsYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUM1QyxJQUFJO01BQUE7TUFBQXBCLGFBQUEsR0FBQW9CLENBQUE7TUFDRmdCLFVBQVUsQ0FBQyxJQUFJLENBQUM7TUFBQztNQUFBcEMsYUFBQSxHQUFBb0IsQ0FBQTtNQUNqQmtCLFFBQVEsQ0FBQyxJQUFJLENBQUM7TUFFZCxNQUFNRyxRQUFRO01BQUE7TUFBQSxDQUFBekMsYUFBQSxHQUFBb0IsQ0FBQSxPQUFHLE1BQU1zQixLQUFLLENBQUMscUJBQXFCLENBQUM7TUFDbkQsTUFBTUMsSUFBSTtNQUFBO01BQUEsQ0FBQTNDLGFBQUEsR0FBQW9CLENBQUEsT0FBRyxNQUFNcUIsUUFBUSxDQUFDRyxJQUFJLENBQUMsQ0FBQztNQUFDO01BQUE1QyxhQUFBLEdBQUFvQixDQUFBO01BRW5DLElBQUksQ0FBQ3FCLFFBQVEsQ0FBQ0ksRUFBRSxFQUFFO1FBQUE7UUFBQTdDLGFBQUEsR0FBQXNCLENBQUE7UUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7UUFDaEI7UUFDQTtRQUFJO1FBQUEsQ0FBQXBCLGFBQUEsR0FBQXNCLENBQUEsVUFBQW1CLFFBQVEsQ0FBQ0ssTUFBTSxLQUFLLEdBQUc7UUFBQTtRQUFBLENBQUE5QyxhQUFBLEdBQUFzQixDQUFBLFVBQUlrQixVQUFVLEdBQUcsQ0FBQyxHQUFFO1VBQUE7VUFBQXhDLGFBQUEsR0FBQXNCLENBQUE7VUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7VUFDN0MyQixPQUFPLENBQUNDLEdBQUcsQ0FBQywwQ0FBMEMsQ0FBQ1IsVUFBVSxHQUFHLENBQUMsSUFBSSxJQUFJLGtCQUFrQkEsVUFBVSxHQUFHLENBQUMsS0FBSyxDQUFDO1VBQUM7VUFBQXhDLGFBQUEsR0FBQW9CLENBQUE7VUFDcEg2QixVQUFVLENBQUMsTUFBTTtZQUFBO1lBQUFqRCxhQUFBLEdBQUFxQixDQUFBO1lBQUFyQixhQUFBLEdBQUFvQixDQUFBO1lBQ2ZtQixXQUFXLENBQUNDLFVBQVUsR0FBRyxDQUFDLENBQUM7VUFDN0IsQ0FBQyxFQUFFLENBQUNBLFVBQVUsR0FBRyxDQUFDLElBQUksSUFBSSxDQUFDO1VBQUM7VUFBQXhDLGFBQUEsR0FBQW9CLENBQUE7VUFDNUI7UUFDRixDQUFDO1FBQUE7UUFBQTtVQUFBcEIsYUFBQSxHQUFBc0IsQ0FBQTtRQUFBO1FBQUF0QixhQUFBLEdBQUFvQixDQUFBO1FBQ0QsTUFBTSxJQUFJOEIsS0FBSztRQUFDO1FBQUEsQ0FBQWxELGFBQUEsR0FBQXNCLENBQUEsVUFBQXFCLElBQUksQ0FBQ04sS0FBSztRQUFBO1FBQUEsQ0FBQXJDLGFBQUEsR0FBQXNCLENBQUEsVUFBSSxvQ0FBb0MsRUFBQztNQUNyRSxDQUFDO01BQUE7TUFBQTtRQUFBdEIsYUFBQSxHQUFBc0IsQ0FBQTtNQUFBO01BQUF0QixhQUFBLEdBQUFvQixDQUFBO01BRUQ7TUFBSTtNQUFBLENBQUFwQixhQUFBLEdBQUFzQixDQUFBLFVBQUFxQixJQUFJLENBQUNRLE9BQU87TUFBQTtNQUFBLENBQUFuRCxhQUFBLEdBQUFzQixDQUFBLFVBQUlxQixJQUFJLENBQUNBLElBQUksRUFBRVMsTUFBTSxHQUFFO1FBQUE7UUFBQXBELGFBQUEsR0FBQXNCLENBQUE7UUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7UUFDckNjLFNBQVMsQ0FBQ1MsSUFBSSxDQUFDQSxJQUFJLENBQUNTLE1BQU0sQ0FBQztRQUFDO1FBQUFwRCxhQUFBLEdBQUFvQixDQUFBO1FBQzVCMkIsT0FBTyxDQUFDQyxHQUFHLENBQUMsdUNBQXVDLEVBQUVMLElBQUksQ0FBQ0EsSUFBSSxDQUFDUyxNQUFNLENBQUNDLFVBQVUsQ0FBQztNQUNuRixDQUFDLE1BQU07UUFBQTtRQUFBckQsYUFBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtRQUNMLE1BQU0sSUFBSThCLEtBQUssQ0FBQyx5QkFBeUIsQ0FBQztNQUM1QztJQUNGLENBQUMsQ0FBQyxPQUFPSSxHQUFHLEVBQUU7TUFDWixNQUFNQyxZQUFZO01BQUE7TUFBQSxDQUFBdkQsYUFBQSxHQUFBb0IsQ0FBQSxRQUFHa0MsR0FBRyxZQUFZSixLQUFLO01BQUE7TUFBQSxDQUFBbEQsYUFBQSxHQUFBc0IsQ0FBQSxVQUFHZ0MsR0FBRyxDQUFDRSxPQUFPO01BQUE7TUFBQSxDQUFBeEQsYUFBQSxHQUFBc0IsQ0FBQSxVQUFHLHdCQUF3QjtNQUFDO01BQUF0QixhQUFBLEdBQUFvQixDQUFBO01BQ25GMkIsT0FBTyxDQUFDVixLQUFLLENBQUMsdUJBQXVCLEVBQUVrQixZQUFZLENBQUM7TUFBQztNQUFBdkQsYUFBQSxHQUFBb0IsQ0FBQTtNQUNyRGtCLFFBQVEsQ0FBQ2lCLFlBQVksQ0FBQztNQUFDO01BQUF2RCxhQUFBLEdBQUFvQixDQUFBO01BQ3ZCYyxTQUFTLENBQUMsSUFBSSxDQUFDO0lBQ2pCLENBQUMsU0FBUztNQUFBO01BQUFsQyxhQUFBLEdBQUFvQixDQUFBO01BQ1JnQixVQUFVLENBQUMsS0FBSyxDQUFDO0lBQ25CO0VBQ0YsQ0FBQztFQUFDO0VBQUFwQyxhQUFBLEdBQUFvQixDQUFBO0VBRUYsTUFBTXFDLGFBQWEsR0FBRyxNQUFBQSxDQUFBLEtBQVk7SUFBQTtJQUFBekQsYUFBQSxHQUFBcUIsQ0FBQTtJQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtJQUNoQyxNQUFNbUIsV0FBVyxDQUFDLENBQUM7RUFDckIsQ0FBQztFQUFDO0VBQUF2QyxhQUFBLEdBQUFvQixDQUFBO0VBRUZTLFNBQVMsQ0FBQyxNQUFNO0lBQUE7SUFBQTdCLGFBQUEsR0FBQXFCLENBQUE7SUFDZDtJQUNBLE1BQU1xQyxLQUFLO0lBQUE7SUFBQSxDQUFBMUQsYUFBQSxHQUFBb0IsQ0FBQSxRQUFHNkIsVUFBVSxDQUFDLE1BQU07TUFBQTtNQUFBakQsYUFBQSxHQUFBcUIsQ0FBQTtNQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtNQUM3Qm1CLFdBQVcsQ0FBQyxDQUFDO0lBQ2YsQ0FBQyxFQUFFLElBQUksQ0FBQztJQUFDO0lBQUF2QyxhQUFBLEdBQUFvQixDQUFBO0lBRVQsT0FBTyxNQUFNO01BQUE7TUFBQXBCLGFBQUEsR0FBQXFCLENBQUE7TUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7TUFBQSxPQUFBdUMsWUFBWSxDQUFDRCxLQUFLLENBQUM7SUFBRCxDQUFDO0VBQ2xDLENBQUMsRUFBRSxFQUFFLENBQUM7RUFFTixNQUFNRSxLQUF5QjtFQUFBO0VBQUEsQ0FBQTVELGFBQUEsR0FBQW9CLENBQUEsUUFBRztJQUNoQ2EsTUFBTTtJQUNORSxPQUFPO0lBQ1BFLEtBQUs7SUFDTG9CO0VBQ0YsQ0FBQztFQUFDO0VBQUF6RCxhQUFBLEdBQUFvQixDQUFBO0VBRUYsT0FDRSwwQkFBQXZCLEtBQUE7RUFBQTtFQUFDaUMsYUFBYSxDQUFDK0IsUUFBUTtFQUFBO0VBQUE7SUFBQ0QsS0FBSyxFQUFFQSxLQUFNO0lBQUFFLE1BQUE7SUFBQUMsUUFBQTtNQUFBQyxRQUFBLEVBQUFwRSxZQUFBO01BQUFxRSxVQUFBO01BQUFDLFlBQUE7SUFBQTtFQUFBLEdBQ2xDbEMsUUFDcUIsQ0FBQztBQUU3QjtBQUVBLE9BQU8sU0FBU21DLFNBQVNBLENBQUEsRUFBRztFQUFBO0VBQUFuRSxhQUFBLEdBQUFxQixDQUFBO0VBQzFCLE1BQU0rQyxPQUFPO0VBQUE7RUFBQSxDQUFBcEUsYUFBQSxHQUFBb0IsQ0FBQSxRQUFHTyxVQUFVLENBQUNHLGFBQWEsQ0FBQztFQUFDO0VBQUE5QixhQUFBLEdBQUFvQixDQUFBO0VBQzFDLElBQUlnRCxPQUFPLEtBQUtqRCxTQUFTLEVBQUU7SUFBQTtJQUFBbkIsYUFBQSxHQUFBc0IsQ0FBQTtJQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtJQUN6QixNQUFNLElBQUk4QixLQUFLLENBQUMsZ0RBQWdELENBQUM7RUFDbkUsQ0FBQztFQUFBO0VBQUE7SUFBQWxELGFBQUEsR0FBQXNCLENBQUE7RUFBQTtFQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtFQUNELE9BQU9nRCxPQUFPO0FBQ2hCOztBQUVBO0FBQ0EsT0FBTyxTQUFTQyxlQUFlQSxDQUFBLEVBQUc7RUFBQTtFQUFBckUsYUFBQSxHQUFBcUIsQ0FBQTtFQUNoQyxNQUFNO0lBQUVZO0VBQU8sQ0FBQztFQUFBO0VBQUEsQ0FBQWpDLGFBQUEsR0FBQW9CLENBQUEsUUFBRytDLFNBQVMsQ0FBQyxDQUFDO0VBQUM7RUFBQW5FLGFBQUEsR0FBQW9CLENBQUE7RUFDL0IsT0FBTywyQkFBQXBCLGFBQUEsR0FBQXNCLENBQUEsVUFBQVcsTUFBTSxFQUFFcUMsWUFBWTtFQUFBO0VBQUEsQ0FBQXRFLGFBQUEsR0FBQXNCLENBQUEsVUFBSSxJQUFJO0FBQ3JDOztBQUVBO0FBQ0EsT0FBTyxTQUFTaUQsaUJBQWlCQSxDQUFBLEVBQUc7RUFBQTtFQUFBdkUsYUFBQSxHQUFBcUIsQ0FBQTtFQUNsQyxNQUFNO0lBQUVZO0VBQU8sQ0FBQztFQUFBO0VBQUEsQ0FBQWpDLGFBQUEsR0FBQW9CLENBQUEsUUFBRytDLFNBQVMsQ0FBQyxDQUFDO0VBQUM7RUFBQW5FLGFBQUEsR0FBQW9CLENBQUE7RUFFL0IsTUFBTW9ELFVBQVUsR0FBSUMsV0FBbUIsSUFBYztJQUFBO0lBQUF6RSxhQUFBLEdBQUFxQixDQUFBO0lBQUFyQixhQUFBLEdBQUFvQixDQUFBO0lBQ25ELE9BQVFhLE1BQU0sRUFBRXlDLFFBQVEsRUFBRUMsUUFBUSxHQUFXRixXQUFXLENBQUMsS0FBSyxJQUFJO0VBQ3BFLENBQUM7RUFBQztFQUFBekUsYUFBQSxHQUFBb0IsQ0FBQTtFQUVGLE1BQU13RCxnQkFBZ0IsR0FBSUgsV0FBbUIsSUFBVTtJQUFBO0lBQUF6RSxhQUFBLEdBQUFxQixDQUFBO0lBQUFyQixhQUFBLEdBQUFvQixDQUFBO0lBQ3JELE9BQVFhLE1BQU0sRUFBRXlDLFFBQVEsRUFBRUMsUUFBUSxHQUFXRixXQUFXLENBQUM7RUFDM0QsQ0FBQztFQUFDO0VBQUF6RSxhQUFBLEdBQUFvQixDQUFBO0VBRUYsT0FBTztJQUNMb0QsVUFBVTtJQUNWSSxnQkFBZ0I7SUFDaEJELFFBQVE7SUFBRTtJQUFBLENBQUEzRSxhQUFBLEdBQUFzQixDQUFBLFdBQUFXLE1BQU0sRUFBRXlDLFFBQVEsRUFBRUMsUUFBUTtJQUFBO0lBQUEsQ0FBQTNFLGFBQUEsR0FBQXNCLENBQUEsV0FBSSxDQUFDLENBQUM7RUFDNUMsQ0FBQztBQUNIIiwiaWdub3JlTGlzdCI6W119