44c48f0be03fd0ffac371fe61b4bc84c
/**
 * Consolidated Application Context
 * 
 * This module consolidates authentication, user, client, and tenant contexts
 * into a single, efficient context provider to eliminate redundancy and
 * improve performance.
 */

'use client';

/* istanbul ignore next */
import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\AppContext.tsx";
var __jsx = React.createElement;
function cov_21c2hnsjtl() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\AppContext.tsx";
  var hash = "d04b7d361e2984090571d14aa6736d112a3aabae";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\contexts\\AppContext.tsx",
    statementMap: {
      "0": {
        start: {
          line: 62,
          column: 19
        },
        end: {
          line: 62,
          column: 65
        }
      },
      "1": {
        start: {
          line: 70,
          column: 48
        },
        end: {
          line: 70,
          column: 63
        }
      },
      "2": {
        start: {
          line: 71,
          column: 36
        },
        end: {
          line: 71,
          column: 50
        }
      },
      "3": {
        start: {
          line: 72,
          column: 26
        },
        end: {
          line: 72,
          column: 53
        }
      },
      "4": {
        start: {
          line: 75,
          column: 30
        },
        end: {
          line: 75,
          column: 70
        }
      },
      "5": {
        start: {
          line: 76,
          column: 44
        },
        end: {
          line: 76,
          column: 59
        }
      },
      "6": {
        start: {
          line: 77,
          column: 40
        },
        end: {
          line: 77,
          column: 69
        }
      },
      "7": {
        start: {
          line: 80,
          column: 25
        },
        end: {
          line: 106,
          column: 3
        }
      },
      "8": {
        start: {
          line: 81,
          column: 4
        },
        end: {
          line: 105,
          column: 5
        }
      },
      "9": {
        start: {
          line: 82,
          column: 26
        },
        end: {
          line: 82,
          column: 55
        }
      },
      "10": {
        start: {
          line: 83,
          column: 25
        },
        end: {
          line: 83,
          column: 52
        }
      },
      "11": {
        start: {
          line: 85,
          column: 6
        },
        end: {
          line: 101,
          column: 8
        }
      },
      "12": {
        start: {
          line: 103,
          column: 6
        },
        end: {
          line: 103,
          column: 58
        }
      },
      "13": {
        start: {
          line: 104,
          column: 6
        },
        end: {
          line: 104,
          column: 18
        }
      },
      "14": {
        start: {
          line: 109,
          column: 22
        },
        end: {
          line: 143,
          column: 3
        }
      },
      "15": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 142,
          column: 5
        }
      },
      "16": {
        start: {
          line: 111,
          column: 6
        },
        end: {
          line: 111,
          column: 29
        }
      },
      "17": {
        start: {
          line: 112,
          column: 6
        },
        end: {
          line: 112,
          column: 27
        }
      },
      "18": {
        start: {
          line: 114,
          column: 23
        },
        end: {
          line: 114,
          column: 57
        }
      },
      "19": {
        start: {
          line: 115,
          column: 19
        },
        end: {
          line: 115,
          column: 40
        }
      },
      "20": {
        start: {
          line: 117,
          column: 6
        },
        end: {
          line: 127,
          column: 7
        }
      },
      "21": {
        start: {
          line: 119,
          column: 8
        },
        end: {
          line: 125,
          column: 9
        }
      },
      "22": {
        start: {
          line: 120,
          column: 10
        },
        end: {
          line: 120,
          column: 126
        }
      },
      "23": {
        start: {
          line: 121,
          column: 10
        },
        end: {
          line: 123,
          column: 38
        }
      },
      "24": {
        start: {
          line: 122,
          column: 12
        },
        end: {
          line: 122,
          column: 40
        }
      },
      "25": {
        start: {
          line: 124,
          column: 10
        },
        end: {
          line: 124,
          column: 17
        }
      },
      "26": {
        start: {
          line: 126,
          column: 8
        },
        end: {
          line: 126,
          column: 76
        }
      },
      "27": {
        start: {
          line: 129,
          column: 6
        },
        end: {
          line: 134,
          column: 7
        }
      },
      "28": {
        start: {
          line: 130,
          column: 8
        },
        end: {
          line: 130,
          column: 31
        }
      },
      "29": {
        start: {
          line: 131,
          column: 8
        },
        end: {
          line: 131,
          column: 85
        }
      },
      "30": {
        start: {
          line: 133,
          column: 8
        },
        end: {
          line: 133,
          column: 51
        }
      },
      "31": {
        start: {
          line: 136,
          column: 27
        },
        end: {
          line: 136,
          column: 88
        }
      },
      "32": {
        start: {
          line: 137,
          column: 6
        },
        end: {
          line: 137,
          column: 59
        }
      },
      "33": {
        start: {
          line: 138,
          column: 6
        },
        end: {
          line: 138,
          column: 35
        }
      },
      "34": {
        start: {
          line: 139,
          column: 6
        },
        end: {
          line: 139,
          column: 22
        }
      },
      "35": {
        start: {
          line: 141,
          column: 6
        },
        end: {
          line: 141,
          column: 30
        }
      },
      "36": {
        start: {
          line: 146,
          column: 2
        },
        end: {
          line: 219,
          column: 9
        }
      },
      "37": {
        start: {
          line: 147,
          column: 20
        },
        end: {
          line: 147,
          column: 24
        }
      },
      "38": {
        start: {
          line: 149,
          column: 22
        },
        end: {
          line: 212,
          column: 5
        }
      },
      "39": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 150,
          column: 29
        }
      },
      "40": {
        start: {
          line: 150,
          column: 22
        },
        end: {
          line: 150,
          column: 29
        }
      },
      "41": {
        start: {
          line: 152,
          column: 6
        },
        end: {
          line: 211,
          column: 7
        }
      },
      "42": {
        start: {
          line: 153,
          column: 8
        },
        end: {
          line: 153,
          column: 73
        }
      },
      "43": {
        start: {
          line: 156,
          column: 26
        },
        end: {
          line: 156,
          column: 69
        }
      },
      "44": {
        start: {
          line: 157,
          column: 29
        },
        end: {
          line: 157,
          column: 50
        }
      },
      "45": {
        start: {
          line: 159,
          column: 8
        },
        end: {
          line: 164,
          column: 9
        }
      },
      "46": {
        start: {
          line: 160,
          column: 10
        },
        end: {
          line: 160,
          column: 96
        }
      },
      "47": {
        start: {
          line: 161,
          column: 10
        },
        end: {
          line: 161,
          column: 66
        }
      },
      "48": {
        start: {
          line: 161,
          column: 39
        },
        end: {
          line: 161,
          column: 64
        }
      },
      "49": {
        start: {
          line: 163,
          column: 10
        },
        end: {
          line: 163,
          column: 66
        }
      },
      "50": {
        start: {
          line: 163,
          column: 39
        },
        end: {
          line: 163,
          column: 64
        }
      },
      "51": {
        start: {
          line: 167,
          column: 24
        },
        end: {
          line: 167,
          column: 71
        }
      },
      "52": {
        start: {
          line: 168,
          column: 30
        },
        end: {
          line: 168,
          column: 56
        }
      },
      "53": {
        start: {
          line: 170,
          column: 8
        },
        end: {
          line: 175,
          column: 11
        }
      },
      "54": {
        start: {
          line: 177,
          column: 8
        },
        end: {
          line: 199,
          column: 9
        }
      },
      "55": {
        start: {
          line: 178,
          column: 10
        },
        end: {
          line: 178,
          column: 88
        }
      },
      "56": {
        start: {
          line: 180,
          column: 27
        },
        end: {
          line: 180,
          column: 49
        }
      },
      "57": {
        start: {
          line: 181,
          column: 10
        },
        end: {
          line: 181,
          column: 72
        }
      },
      "58": {
        start: {
          line: 183,
          column: 10
        },
        end: {
          line: 191,
          column: 11
        }
      },
      "59": {
        start: {
          line: 184,
          column: 12
        },
        end: {
          line: 184,
          column: 30
        }
      },
      "60": {
        start: {
          line: 185,
          column: 12
        },
        end: {
          line: 185,
          column: 37
        }
      },
      "61": {
        start: {
          line: 188,
          column: 12
        },
        end: {
          line: 190,
          column: 21
        }
      },
      "62": {
        start: {
          line: 189,
          column: 14
        },
        end: {
          line: 189,
          column: 28
        }
      },
      "63": {
        start: {
          line: 193,
          column: 10
        },
        end: {
          line: 193,
          column: 64
        }
      },
      "64": {
        start: {
          line: 194,
          column: 10
        },
        end: {
          line: 198,
          column: 11
        }
      },
      "65": {
        start: {
          line: 195,
          column: 12
        },
        end: {
          line: 195,
          column: 26
        }
      },
      "66": {
        start: {
          line: 196,
          column: 12
        },
        end: {
          line: 196,
          column: 38
        }
      },
      "67": {
        start: {
          line: 197,
          column: 12
        },
        end: {
          line: 197,
          column: 28
        }
      },
      "68": {
        start: {
          line: 201,
          column: 8
        },
        end: {
          line: 201,
          column: 66
        }
      },
      "69": {
        start: {
          line: 202,
          column: 8
        },
        end: {
          line: 206,
          column: 9
        }
      },
      "70": {
        start: {
          line: 203,
          column: 10
        },
        end: {
          line: 203,
          column: 24
        }
      },
      "71": {
        start: {
          line: 204,
          column: 10
        },
        end: {
          line: 204,
          column: 36
        }
      },
      "72": {
        start: {
          line: 205,
          column: 10
        },
        end: {
          line: 205,
          column: 26
        }
      },
      "73": {
        start: {
          line: 208,
          column: 8
        },
        end: {
          line: 210,
          column: 9
        }
      },
      "74": {
        start: {
          line: 209,
          column: 10
        },
        end: {
          line: 209,
          column: 30
        }
      },
      "75": {
        start: {
          line: 214,
          column: 4
        },
        end: {
          line: 214,
          column: 16
        }
      },
      "76": {
        start: {
          line: 216,
          column: 4
        },
        end: {
          line: 218,
          column: 6
        }
      },
      "77": {
        start: {
          line: 217,
          column: 6
        },
        end: {
          line: 217,
          column: 24
        }
      },
      "78": {
        start: {
          line: 222,
          column: 18
        },
        end: {
          line: 248,
          column: 3
        }
      },
      "79": {
        start: {
          line: 223,
          column: 4
        },
        end: {
          line: 247,
          column: 5
        }
      },
      "80": {
        start: {
          line: 224,
          column: 6
        },
        end: {
          line: 224,
          column: 45
        }
      },
      "81": {
        start: {
          line: 227,
          column: 6
        },
        end: {
          line: 227,
          column: 49
        }
      },
      "82": {
        start: {
          line: 230,
          column: 6
        },
        end: {
          line: 230,
          column: 68
        }
      },
      "83": {
        start: {
          line: 233,
          column: 6
        },
        end: {
          line: 233,
          column: 20
        }
      },
      "84": {
        start: {
          line: 234,
          column: 6
        },
        end: {
          line: 234,
          column: 32
        }
      },
      "85": {
        start: {
          line: 235,
          column: 6
        },
        end: {
          line: 235,
          column: 22
        }
      },
      "86": {
        start: {
          line: 237,
          column: 6
        },
        end: {
          line: 237,
          column: 18
        }
      },
      "87": {
        start: {
          line: 239,
          column: 6
        },
        end: {
          line: 239,
          column: 49
        }
      },
      "88": {
        start: {
          line: 242,
          column: 6
        },
        end: {
          line: 242,
          column: 20
        }
      },
      "89": {
        start: {
          line: 243,
          column: 6
        },
        end: {
          line: 243,
          column: 32
        }
      },
      "90": {
        start: {
          line: 244,
          column: 6
        },
        end: {
          line: 244,
          column: 22
        }
      },
      "91": {
        start: {
          line: 246,
          column: 6
        },
        end: {
          line: 246,
          column: 18
        }
      },
      "92": {
        start: {
          line: 251,
          column: 24
        },
        end: {
          line: 253,
          column: 3
        }
      },
      "93": {
        start: {
          line: 252,
          column: 4
        },
        end: {
          line: 252,
          column: 24
        }
      },
      "94": {
        start: {
          line: 256,
          column: 32
        },
        end: {
          line: 272,
          column: 3
        }
      },
      "95": {
        start: {
          line: 257,
          column: 4
        },
        end: {
          line: 257,
          column: 32
        }
      },
      "96": {
        start: {
          line: 257,
          column: 19
        },
        end: {
          line: 257,
          column: 32
        }
      },
      "97": {
        start: {
          line: 259,
          column: 4
        },
        end: {
          line: 271,
          column: 5
        }
      },
      "98": {
        start: {
          line: 261,
          column: 33
        },
        end: {
          line: 261,
          column: 72
        }
      },
      "99": {
        start: {
          line: 262,
          column: 6
        },
        end: {
          line: 262,
          column: 82
        }
      },
      "100": {
        start: {
          line: 262,
          column: 22
        },
        end: {
          line: 262,
          column: 80
        }
      },
      "101": {
        start: {
          line: 265,
          column: 6
        },
        end: {
          line: 265,
          column: 88
        }
      },
      "102": {
        start: {
          line: 267,
          column: 6
        },
        end: {
          line: 267,
          column: 18
        }
      },
      "103": {
        start: {
          line: 269,
          column: 6
        },
        end: {
          line: 269,
          column: 63
        }
      },
      "104": {
        start: {
          line: 270,
          column: 6
        },
        end: {
          line: 270,
          column: 19
        }
      },
      "105": {
        start: {
          line: 275,
          column: 18
        },
        end: {
          line: 277,
          column: 3
        }
      },
      "106": {
        start: {
          line: 276,
          column: 4
        },
        end: {
          line: 276,
          column: 48
        }
      },
      "107": {
        start: {
          line: 280,
          column: 25
        },
        end: {
          line: 296,
          column: 3
        }
      },
      "108": {
        start: {
          line: 281,
          column: 4
        },
        end: {
          line: 281,
          column: 29
        }
      },
      "109": {
        start: {
          line: 281,
          column: 15
        },
        end: {
          line: 281,
          column: 29
        }
      },
      "110": {
        start: {
          line: 283,
          column: 4
        },
        end: {
          line: 285,
          column: 5
        }
      },
      "111": {
        start: {
          line: 284,
          column: 6
        },
        end: {
          line: 284,
          column: 54
        }
      },
      "112": {
        start: {
          line: 287,
          column: 4
        },
        end: {
          line: 289,
          column: 5
        }
      },
      "113": {
        start: {
          line: 288,
          column: 6
        },
        end: {
          line: 288,
          column: 23
        }
      },
      "114": {
        start: {
          line: 291,
          column: 4
        },
        end: {
          line: 293,
          column: 5
        }
      },
      "115": {
        start: {
          line: 292,
          column: 6
        },
        end: {
          line: 292,
          column: 38
        }
      },
      "116": {
        start: {
          line: 295,
          column: 4
        },
        end: {
          line: 295,
          column: 45
        }
      },
      "117": {
        start: {
          line: 298,
          column: 26
        },
        end: {
          line: 310,
          column: 3
        }
      },
      "118": {
        start: {
          line: 312,
          column: 2
        },
        end: {
          line: 316,
          column: 4
        }
      },
      "119": {
        start: {
          line: 320,
          column: 22
        },
        end: {
          line: 326,
          column: 1
        }
      },
      "120": {
        start: {
          line: 321,
          column: 18
        },
        end: {
          line: 321,
          column: 40
        }
      },
      "121": {
        start: {
          line: 322,
          column: 2
        },
        end: {
          line: 324,
          column: 3
        }
      },
      "122": {
        start: {
          line: 323,
          column: 4
        },
        end: {
          line: 323,
          column: 65
        }
      },
      "123": {
        start: {
          line: 325,
          column: 2
        },
        end: {
          line: 325,
          column: 17
        }
      },
      "124": {
        start: {
          line: 329,
          column: 23
        },
        end: {
          line: 332,
          column: 1
        }
      },
      "125": {
        start: {
          line: 330,
          column: 65
        },
        end: {
          line: 330,
          column: 73
        }
      },
      "126": {
        start: {
          line: 331,
          column: 2
        },
        end: {
          line: 331,
          column: 64
        }
      },
      "127": {
        start: {
          line: 334,
          column: 25
        },
        end: {
          line: 337,
          column: 1
        }
      },
      "128": {
        start: {
          line: 335,
          column: 64
        },
        end: {
          line: 335,
          column: 72
        }
      },
      "129": {
        start: {
          line: 336,
          column: 2
        },
        end: {
          line: 336,
          column: 79
        }
      },
      "130": {
        start: {
          line: 339,
          column: 23
        },
        end: {
          line: 342,
          column: 1
        }
      },
      "131": {
        start: {
          line: 340,
          column: 78
        },
        end: {
          line: 340,
          column: 86
        }
      },
      "132": {
        start: {
          line: 341,
          column: 2
        },
        end: {
          line: 341,
          column: 77
        }
      }
    },
    fnMap: {
      "0": {
        name: "AppProvider",
        decl: {
          start: {
            line: 68,
            column: 16
          },
          end: {
            line: 68,
            column: 27
          }
        },
        loc: {
          start: {
            line: 68,
            column: 60
          },
          end: {
            line: 317,
            column: 1
          }
        },
        line: 68
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 80,
            column: 25
          },
          end: {
            line: 80,
            column: 26
          }
        },
        loc: {
          start: {
            line: 80,
            column: 59
          },
          end: {
            line: 106,
            column: 3
          }
        },
        line: 80
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 109,
            column: 22
          },
          end: {
            line: 109,
            column: 23
          }
        },
        loc: {
          start: {
            line: 109,
            column: 63
          },
          end: {
            line: 143,
            column: 3
          }
        },
        line: 109
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 121,
            column: 21
          },
          end: {
            line: 121,
            column: 22
          }
        },
        loc: {
          start: {
            line: 121,
            column: 27
          },
          end: {
            line: 123,
            column: 11
          }
        },
        line: 121
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 146,
            column: 12
          },
          end: {
            line: 146,
            column: 13
          }
        },
        loc: {
          start: {
            line: 146,
            column: 18
          },
          end: {
            line: 219,
            column: 3
          }
        },
        line: 146
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 149,
            column: 22
          },
          end: {
            line: 149,
            column: 23
          }
        },
        loc: {
          start: {
            line: 149,
            column: 34
          },
          end: {
            line: 212,
            column: 5
          }
        },
        line: 149
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 161,
            column: 28
          },
          end: {
            line: 161,
            column: 29
          }
        },
        loc: {
          start: {
            line: 161,
            column: 39
          },
          end: {
            line: 161,
            column: 64
          }
        },
        line: 161
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 163,
            column: 28
          },
          end: {
            line: 163,
            column: 29
          }
        },
        loc: {
          start: {
            line: 163,
            column: 39
          },
          end: {
            line: 163,
            column: 64
          }
        },
        line: 163
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 188,
            column: 23
          },
          end: {
            line: 188,
            column: 24
          }
        },
        loc: {
          start: {
            line: 188,
            column: 29
          },
          end: {
            line: 190,
            column: 13
          }
        },
        line: 188
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 216,
            column: 11
          },
          end: {
            line: 216,
            column: 12
          }
        },
        loc: {
          start: {
            line: 216,
            column: 17
          },
          end: {
            line: 218,
            column: 5
          }
        },
        line: 216
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 222,
            column: 18
          },
          end: {
            line: 222,
            column: 19
          }
        },
        loc: {
          start: {
            line: 222,
            column: 48
          },
          end: {
            line: 248,
            column: 3
          }
        },
        line: 222
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 251,
            column: 24
          },
          end: {
            line: 251,
            column: 25
          }
        },
        loc: {
          start: {
            line: 251,
            column: 51
          },
          end: {
            line: 253,
            column: 3
          }
        },
        line: 251
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 256,
            column: 32
          },
          end: {
            line: 256,
            column: 33
          }
        },
        loc: {
          start: {
            line: 256,
            column: 99
          },
          end: {
            line: 272,
            column: 3
          }
        },
        line: 256
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 262,
            column: 14
          },
          end: {
            line: 262,
            column: 15
          }
        },
        loc: {
          start: {
            line: 262,
            column: 22
          },
          end: {
            line: 262,
            column: 80
          }
        },
        line: 262
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 275,
            column: 18
          },
          end: {
            line: 275,
            column: 19
          }
        },
        loc: {
          start: {
            line: 275,
            column: 45
          },
          end: {
            line: 277,
            column: 3
          }
        },
        line: 275
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 280,
            column: 25
          },
          end: {
            line: 280,
            column: 26
          }
        },
        loc: {
          start: {
            line: 280,
            column: 39
          },
          end: {
            line: 296,
            column: 3
          }
        },
        line: 280
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 320,
            column: 22
          },
          end: {
            line: 320,
            column: 23
          }
        },
        loc: {
          start: {
            line: 320,
            column: 28
          },
          end: {
            line: 326,
            column: 1
          }
        },
        line: 320
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 329,
            column: 23
          },
          end: {
            line: 329,
            column: 24
          }
        },
        loc: {
          start: {
            line: 329,
            column: 29
          },
          end: {
            line: 332,
            column: 1
          }
        },
        line: 329
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 334,
            column: 25
          },
          end: {
            line: 334,
            column: 26
          }
        },
        loc: {
          start: {
            line: 334,
            column: 31
          },
          end: {
            line: 337,
            column: 1
          }
        },
        line: 334
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 339,
            column: 23
          },
          end: {
            line: 339,
            column: 24
          }
        },
        loc: {
          start: {
            line: 339,
            column: 29
          },
          end: {
            line: 342,
            column: 1
          }
        },
        line: 339
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 87,
            column: 15
          },
          end: {
            line: 87,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 87,
            column: 15
          },
          end: {
            line: 87,
            column: 31
          }
        }, {
          start: {
            line: 87,
            column: 35
          },
          end: {
            line: 87,
            column: 37
          }
        }],
        line: 87
      },
      "1": {
        loc: {
          start: {
            line: 91,
            column: 15
          },
          end: {
            line: 91,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 91,
            column: 44
          },
          end: {
            line: 91,
            column: 82
          }
        }, {
          start: {
            line: 91,
            column: 85
          },
          end: {
            line: 91,
            column: 87
          }
        }],
        line: 91
      },
      "2": {
        loc: {
          start: {
            line: 109,
            column: 29
          },
          end: {
            line: 109,
            column: 43
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 109,
            column: 42
          },
          end: {
            line: 109,
            column: 43
          }
        }],
        line: 109
      },
      "3": {
        loc: {
          start: {
            line: 117,
            column: 6
          },
          end: {
            line: 127,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 117,
            column: 6
          },
          end: {
            line: 127,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 117
      },
      "4": {
        loc: {
          start: {
            line: 119,
            column: 8
          },
          end: {
            line: 125,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 119,
            column: 8
          },
          end: {
            line: 125,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 119
      },
      "5": {
        loc: {
          start: {
            line: 119,
            column: 12
          },
          end: {
            line: 119,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 119,
            column: 12
          },
          end: {
            line: 119,
            column: 35
          }
        }, {
          start: {
            line: 119,
            column: 39
          },
          end: {
            line: 119,
            column: 53
          }
        }],
        line: 119
      },
      "6": {
        loc: {
          start: {
            line: 126,
            column: 24
          },
          end: {
            line: 126,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 126,
            column: 24
          },
          end: {
            line: 126,
            column: 34
          }
        }, {
          start: {
            line: 126,
            column: 38
          },
          end: {
            line: 126,
            column: 74
          }
        }],
        line: 126
      },
      "7": {
        loc: {
          start: {
            line: 129,
            column: 6
          },
          end: {
            line: 134,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 129,
            column: 6
          },
          end: {
            line: 134,
            column: 7
          }
        }, {
          start: {
            line: 132,
            column: 13
          },
          end: {
            line: 134,
            column: 7
          }
        }],
        line: 129
      },
      "8": {
        loc: {
          start: {
            line: 129,
            column: 10
          },
          end: {
            line: 129,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 129,
            column: 10
          },
          end: {
            line: 129,
            column: 22
          }
        }, {
          start: {
            line: 129,
            column: 26
          },
          end: {
            line: 129,
            column: 37
          }
        }],
        line: 129
      },
      "9": {
        loc: {
          start: {
            line: 136,
            column: 27
          },
          end: {
            line: 136,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 136,
            column: 50
          },
          end: {
            line: 136,
            column: 61
          }
        }, {
          start: {
            line: 136,
            column: 64
          },
          end: {
            line: 136,
            column: 88
          }
        }],
        line: 136
      },
      "10": {
        loc: {
          start: {
            line: 150,
            column: 6
          },
          end: {
            line: 150,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 150,
            column: 6
          },
          end: {
            line: 150,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 150
      },
      "11": {
        loc: {
          start: {
            line: 159,
            column: 8
          },
          end: {
            line: 164,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 159,
            column: 8
          },
          end: {
            line: 164,
            column: 9
          }
        }, {
          start: {
            line: 162,
            column: 15
          },
          end: {
            line: 164,
            column: 9
          }
        }],
        line: 159
      },
      "12": {
        loc: {
          start: {
            line: 177,
            column: 8
          },
          end: {
            line: 199,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 177,
            column: 8
          },
          end: {
            line: 199,
            column: 9
          }
        }, {
          start: {
            line: 192,
            column: 15
          },
          end: {
            line: 199,
            column: 9
          }
        }],
        line: 177
      },
      "13": {
        loc: {
          start: {
            line: 183,
            column: 10
          },
          end: {
            line: 191,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 183,
            column: 10
          },
          end: {
            line: 191,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 183
      },
      "14": {
        loc: {
          start: {
            line: 183,
            column: 14
          },
          end: {
            line: 183,
            column: 35
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 183,
            column: 14
          },
          end: {
            line: 183,
            column: 23
          }
        }, {
          start: {
            line: 183,
            column: 27
          },
          end: {
            line: 183,
            column: 35
          }
        }],
        line: 183
      },
      "15": {
        loc: {
          start: {
            line: 194,
            column: 10
          },
          end: {
            line: 198,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 194,
            column: 10
          },
          end: {
            line: 198,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 194
      },
      "16": {
        loc: {
          start: {
            line: 202,
            column: 8
          },
          end: {
            line: 206,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 202,
            column: 8
          },
          end: {
            line: 206,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 202
      },
      "17": {
        loc: {
          start: {
            line: 208,
            column: 8
          },
          end: {
            line: 210,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 208,
            column: 8
          },
          end: {
            line: 210,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 208
      },
      "18": {
        loc: {
          start: {
            line: 257,
            column: 4
          },
          end: {
            line: 257,
            column: 32
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 257,
            column: 4
          },
          end: {
            line: 257,
            column: 32
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 257
      },
      "19": {
        loc: {
          start: {
            line: 262,
            column: 22
          },
          end: {
            line: 262,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 262,
            column: 29
          },
          end: {
            line: 262,
            column: 73
          }
        }, {
          start: {
            line: 262,
            column: 76
          },
          end: {
            line: 262,
            column: 80
          }
        }],
        line: 262
      },
      "20": {
        loc: {
          start: {
            line: 276,
            column: 11
          },
          end: {
            line: 276,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 276,
            column: 11
          },
          end: {
            line: 276,
            column: 38
          }
        }, {
          start: {
            line: 276,
            column: 42
          },
          end: {
            line: 276,
            column: 47
          }
        }],
        line: 276
      },
      "21": {
        loc: {
          start: {
            line: 281,
            column: 4
          },
          end: {
            line: 281,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 281,
            column: 4
          },
          end: {
            line: 281,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 281
      },
      "22": {
        loc: {
          start: {
            line: 283,
            column: 4
          },
          end: {
            line: 285,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 283,
            column: 4
          },
          end: {
            line: 285,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 283
      },
      "23": {
        loc: {
          start: {
            line: 283,
            column: 8
          },
          end: {
            line: 283,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 283,
            column: 8
          },
          end: {
            line: 283,
            column: 23
          }
        }, {
          start: {
            line: 283,
            column: 27
          },
          end: {
            line: 283,
            column: 43
          }
        }],
        line: 283
      },
      "24": {
        loc: {
          start: {
            line: 287,
            column: 4
          },
          end: {
            line: 289,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 287,
            column: 4
          },
          end: {
            line: 289,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 287
      },
      "25": {
        loc: {
          start: {
            line: 291,
            column: 4
          },
          end: {
            line: 293,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 291,
            column: 4
          },
          end: {
            line: 293,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 291
      },
      "26": {
        loc: {
          start: {
            line: 322,
            column: 2
          },
          end: {
            line: 324,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 322,
            column: 2
          },
          end: {
            line: 324,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 322
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d04b7d361e2984090571d14aa6736d112a3aabae"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_21c2hnsjtl = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_21c2hnsjtl();
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
import React, { createContext, useContext, useState, useEffect } from 'react';
import { fetchAuthSession, signOut as amplifySignOut, getCurrentUser as getAmplifyCurrentUser, fetchUserAttributes } from 'aws-amplify/auth';

// Consolidated interfaces

const AppContext =
/* istanbul ignore next */
(cov_21c2hnsjtl().s[0]++, /*#__PURE__*/createContext(undefined));
export function AppProvider({
  children
}) {
  /* istanbul ignore next */
  cov_21c2hnsjtl().f[0]++;
  // Authentication state
  const [isAuthenticated, setIsAuthenticated] =
  /* istanbul ignore next */
  (cov_21c2hnsjtl().s[1]++, useState(false));
  const [isLoading, setIsLoading] =
  /* istanbul ignore next */
  (cov_21c2hnsjtl().s[2]++, useState(true));
  const [user, setUser] =
  /* istanbul ignore next */
  (cov_21c2hnsjtl().s[3]++, useState(null));

  // Tenant state
  const [tenant, setTenant] =
  /* istanbul ignore next */
  (cov_21c2hnsjtl().s[4]++, useState(null));
  const [tenantLoading, setTenantLoading] =
  /* istanbul ignore next */
  (cov_21c2hnsjtl().s[5]++, useState(false));
  const [tenantError, setTenantError] =
  /* istanbul ignore next */
  (cov_21c2hnsjtl().s[6]++, useState(null));

  // Get current user from Amplify
  /* istanbul ignore next */
  cov_21c2hnsjtl().s[7]++;
  const getCurrentUser = async () => {
    /* istanbul ignore next */
    cov_21c2hnsjtl().f[1]++;
    cov_21c2hnsjtl().s[8]++;
    try {
      const currentUser =
      /* istanbul ignore next */
      (cov_21c2hnsjtl().s[9]++, await getAmplifyCurrentUser());
      const attributes =
      /* istanbul ignore next */
      (cov_21c2hnsjtl().s[10]++, await fetchUserAttributes());
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[11]++;
      return {
        id: currentUser.userId,
        email:
        /* istanbul ignore next */
        (cov_21c2hnsjtl().b[0][0]++, attributes.email) ||
        /* istanbul ignore next */
        (cov_21c2hnsjtl().b[0][1]++, ''),
        name: attributes.name,
        given_name: attributes.given_name,
        family_name: attributes.family_name,
        roles: attributes['custom:roles'] ?
        /* istanbul ignore next */
        (cov_21c2hnsjtl().b[1][0]++, JSON.parse(attributes['custom:roles'])) :
        /* istanbul ignore next */
        (cov_21c2hnsjtl().b[1][1]++, []),
        preferences: {
          theme: 'system',
          notifications: {
            email: true,
            push: true,
            sms: false
          },
          displayDensity: 'comfortable'
        }
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[12]++;
      console.error('Error getting current user:', error);
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[13]++;
      return null;
    }
  };

  // Fetch tenant information
  /* istanbul ignore next */
  cov_21c2hnsjtl().s[14]++;
  const fetchTenant = async (retryCount =
  /* istanbul ignore next */
  (cov_21c2hnsjtl().b[2][0]++, 0)) => {
    /* istanbul ignore next */
    cov_21c2hnsjtl().f[2]++;
    cov_21c2hnsjtl().s[15]++;
    try {
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[16]++;
      setTenantLoading(true);
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[17]++;
      setTenantError(null);
      const response =
      /* istanbul ignore next */
      (cov_21c2hnsjtl().s[18]++, await fetch('/api/clients/domain'));
      const data =
      /* istanbul ignore next */
      (cov_21c2hnsjtl().s[19]++, await response.json());
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[20]++;
      if (!response.ok) {
        /* istanbul ignore next */
        cov_21c2hnsjtl().b[3][0]++;
        cov_21c2hnsjtl().s[21]++;
        // If unauthorized and we haven't retried much, wait and retry
        if (
        /* istanbul ignore next */
        (cov_21c2hnsjtl().b[5][0]++, response.status === 401) &&
        /* istanbul ignore next */
        (cov_21c2hnsjtl().b[5][1]++, retryCount < 3)) {
          /* istanbul ignore next */
          cov_21c2hnsjtl().b[4][0]++;
          cov_21c2hnsjtl().s[22]++;
          console.log(`Tenant fetch failed (401), retrying in ${(retryCount + 1) * 1000}ms... (attempt ${retryCount + 1}/3)`);
          /* istanbul ignore next */
          cov_21c2hnsjtl().s[23]++;
          setTimeout(() => {
            /* istanbul ignore next */
            cov_21c2hnsjtl().f[3]++;
            cov_21c2hnsjtl().s[24]++;
            fetchTenant(retryCount + 1);
          }, (retryCount + 1) * 1000);
          /* istanbul ignore next */
          cov_21c2hnsjtl().s[25]++;
          return;
        } else
        /* istanbul ignore next */
        {
          cov_21c2hnsjtl().b[4][1]++;
        }
        cov_21c2hnsjtl().s[26]++;
        throw new Error(
        /* istanbul ignore next */
        (cov_21c2hnsjtl().b[6][0]++, data.error) ||
        /* istanbul ignore next */
        (cov_21c2hnsjtl().b[6][1]++, 'Failed to fetch tenant information'));
      } else
      /* istanbul ignore next */
      {
        cov_21c2hnsjtl().b[3][1]++;
      }
      cov_21c2hnsjtl().s[27]++;
      if (
      /* istanbul ignore next */
      (cov_21c2hnsjtl().b[8][0]++, data.success) &&
      /* istanbul ignore next */
      (cov_21c2hnsjtl().b[8][1]++, data.client)) {
        /* istanbul ignore next */
        cov_21c2hnsjtl().b[7][0]++;
        cov_21c2hnsjtl().s[28]++;
        setTenant(data.client);
        /* istanbul ignore next */
        cov_21c2hnsjtl().s[29]++;
        console.log('✅ Tenant context loaded successfully:', data.client.clientName);
      } else {
        /* istanbul ignore next */
        cov_21c2hnsjtl().b[7][1]++;
        cov_21c2hnsjtl().s[30]++;
        throw new Error('Invalid response format');
      }
    } catch (err) {
      const errorMessage =
      /* istanbul ignore next */
      (cov_21c2hnsjtl().s[31]++, err instanceof Error ?
      /* istanbul ignore next */
      (cov_21c2hnsjtl().b[9][0]++, err.message) :
      /* istanbul ignore next */
      (cov_21c2hnsjtl().b[9][1]++, 'Unknown error occurred'));
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[32]++;
      console.error('❌ Tenant fetch error:', errorMessage);
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[33]++;
      setTenantError(errorMessage);
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[34]++;
      setTenant(null);
    } finally {
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[35]++;
      setTenantLoading(false);
    }
  };

  // Authentication check
  /* istanbul ignore next */
  cov_21c2hnsjtl().s[36]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_21c2hnsjtl().f[4]++;
    let isMounted =
    /* istanbul ignore next */
    (cov_21c2hnsjtl().s[37]++, true);
    /* istanbul ignore next */
    cov_21c2hnsjtl().s[38]++;
    const checkAuth = async () => {
      /* istanbul ignore next */
      cov_21c2hnsjtl().f[5]++;
      cov_21c2hnsjtl().s[39]++;
      if (!isMounted) {
        /* istanbul ignore next */
        cov_21c2hnsjtl().b[10][0]++;
        cov_21c2hnsjtl().s[40]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_21c2hnsjtl().b[10][1]++;
      }
      cov_21c2hnsjtl().s[41]++;
      try {
        /* istanbul ignore next */
        cov_21c2hnsjtl().s[42]++;
        console.log('🔍 [APP-CONTEXT] Starting authentication check...');

        // Check if this is an OAuth callback
        const urlParams =
        /* istanbul ignore next */
        (cov_21c2hnsjtl().s[43]++, new URLSearchParams(window.location.search));
        const hasOAuthCode =
        /* istanbul ignore next */
        (cov_21c2hnsjtl().s[44]++, urlParams.has('code'));
        /* istanbul ignore next */
        cov_21c2hnsjtl().s[45]++;
        if (hasOAuthCode) {
          /* istanbul ignore next */
          cov_21c2hnsjtl().b[11][0]++;
          cov_21c2hnsjtl().s[46]++;
          console.log('🔄 [APP-CONTEXT] OAuth callback detected, giving Amplify extra time...');
          /* istanbul ignore next */
          cov_21c2hnsjtl().s[47]++;
          await new Promise(resolve => {
            /* istanbul ignore next */
            cov_21c2hnsjtl().f[6]++;
            cov_21c2hnsjtl().s[48]++;
            return setTimeout(resolve, 5000);
          });
        } else {
          /* istanbul ignore next */
          cov_21c2hnsjtl().b[11][1]++;
          cov_21c2hnsjtl().s[49]++;
          await new Promise(resolve => {
            /* istanbul ignore next */
            cov_21c2hnsjtl().f[7]++;
            cov_21c2hnsjtl().s[50]++;
            return setTimeout(resolve, 1000);
          });
        }

        // Check if authenticated with Amplify
        const session =
        /* istanbul ignore next */
        (cov_21c2hnsjtl().s[51]++, await fetchAuthSession({
          forceRefresh: false
        }));
        const authenticated =
        /* istanbul ignore next */
        (cov_21c2hnsjtl().s[52]++, !!session?.tokens?.idToken);
        /* istanbul ignore next */
        cov_21c2hnsjtl().s[53]++;
        console.log('🔍 [APP-CONTEXT] Session details:', {
          hasSession: !!session,
          hasTokens: !!session?.tokens,
          hasIdToken: !!session?.tokens?.idToken,
          authenticated
        });
        /* istanbul ignore next */
        cov_21c2hnsjtl().s[54]++;
        if (authenticated) {
          /* istanbul ignore next */
          cov_21c2hnsjtl().b[12][0]++;
          cov_21c2hnsjtl().s[55]++;
          console.log('✅ [APP-CONTEXT] Session is authenticated, getting user info...');
          const userInfo =
          /* istanbul ignore next */
          (cov_21c2hnsjtl().s[56]++, await getCurrentUser());
          /* istanbul ignore next */
          cov_21c2hnsjtl().s[57]++;
          console.log('✅ [APP-CONTEXT] User info retrieved:', userInfo);
          /* istanbul ignore next */
          cov_21c2hnsjtl().s[58]++;
          if (
          /* istanbul ignore next */
          (cov_21c2hnsjtl().b[14][0]++, isMounted) &&
          /* istanbul ignore next */
          (cov_21c2hnsjtl().b[14][1]++, userInfo)) {
            /* istanbul ignore next */
            cov_21c2hnsjtl().b[13][0]++;
            cov_21c2hnsjtl().s[59]++;
            setUser(userInfo);
            /* istanbul ignore next */
            cov_21c2hnsjtl().s[60]++;
            setIsAuthenticated(true);

            // Fetch tenant information after authentication
            /* istanbul ignore next */
            cov_21c2hnsjtl().s[61]++;
            setTimeout(() => {
              /* istanbul ignore next */
              cov_21c2hnsjtl().f[8]++;
              cov_21c2hnsjtl().s[62]++;
              fetchTenant();
            }, 1000);
          } else
          /* istanbul ignore next */
          {
            cov_21c2hnsjtl().b[13][1]++;
          }
        } else {
          /* istanbul ignore next */
          cov_21c2hnsjtl().b[12][1]++;
          cov_21c2hnsjtl().s[63]++;
          console.log('❌ [APP-CONTEXT] No valid session found');
          /* istanbul ignore next */
          cov_21c2hnsjtl().s[64]++;
          if (isMounted) {
            /* istanbul ignore next */
            cov_21c2hnsjtl().b[15][0]++;
            cov_21c2hnsjtl().s[65]++;
            setUser(null);
            /* istanbul ignore next */
            cov_21c2hnsjtl().s[66]++;
            setIsAuthenticated(false);
            /* istanbul ignore next */
            cov_21c2hnsjtl().s[67]++;
            setTenant(null);
          } else
          /* istanbul ignore next */
          {
            cov_21c2hnsjtl().b[15][1]++;
          }
        }
      } catch (error) {
        /* istanbul ignore next */
        cov_21c2hnsjtl().s[68]++;
        console.error('❌ [APP-CONTEXT] Auth check error:', error);
        /* istanbul ignore next */
        cov_21c2hnsjtl().s[69]++;
        if (isMounted) {
          /* istanbul ignore next */
          cov_21c2hnsjtl().b[16][0]++;
          cov_21c2hnsjtl().s[70]++;
          setUser(null);
          /* istanbul ignore next */
          cov_21c2hnsjtl().s[71]++;
          setIsAuthenticated(false);
          /* istanbul ignore next */
          cov_21c2hnsjtl().s[72]++;
          setTenant(null);
        } else
        /* istanbul ignore next */
        {
          cov_21c2hnsjtl().b[16][1]++;
        }
      } finally {
        /* istanbul ignore next */
        cov_21c2hnsjtl().s[73]++;
        if (isMounted) {
          /* istanbul ignore next */
          cov_21c2hnsjtl().b[17][0]++;
          cov_21c2hnsjtl().s[74]++;
          setIsLoading(false);
        } else
        /* istanbul ignore next */
        {
          cov_21c2hnsjtl().b[17][1]++;
        }
      }
    };
    /* istanbul ignore next */
    cov_21c2hnsjtl().s[75]++;
    checkAuth();
    /* istanbul ignore next */
    cov_21c2hnsjtl().s[76]++;
    return () => {
      /* istanbul ignore next */
      cov_21c2hnsjtl().f[9]++;
      cov_21c2hnsjtl().s[77]++;
      isMounted = false;
    };
  }, []);

  // Sign out function
  /* istanbul ignore next */
  cov_21c2hnsjtl().s[78]++;
  const signOut = async () => {
    /* istanbul ignore next */
    cov_21c2hnsjtl().f[10]++;
    cov_21c2hnsjtl().s[79]++;
    try {
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[80]++;
      await amplifySignOut({
        global: true
      });

      // Clear localStorage
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[81]++;
      localStorage.removeItem('isAuthenticated');

      // Clear the auth cookie
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[82]++;
      document.cookie = 'idToken=; path=/; max-age=0; SameSite=Lax';

      // Update state
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[83]++;
      setUser(null);
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[84]++;
      setIsAuthenticated(false);
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[85]++;
      setTenant(null);
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[86]++;
      return true;
    } catch (error) {
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[87]++;
      console.error('Error signing out:', error);

      // Clear state anyway
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[88]++;
      setUser(null);
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[89]++;
      setIsAuthenticated(false);
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[90]++;
      setTenant(null);
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[91]++;
      return true;
    }
  };

  // Refresh tenant information
  /* istanbul ignore next */
  cov_21c2hnsjtl().s[92]++;
  const refreshTenant = async () => {
    /* istanbul ignore next */
    cov_21c2hnsjtl().f[11]++;
    cov_21c2hnsjtl().s[93]++;
    await fetchTenant();
  };

  // Update user preferences
  /* istanbul ignore next */
  cov_21c2hnsjtl().s[94]++;
  const updateUserPreferences = async preferences => {
    /* istanbul ignore next */
    cov_21c2hnsjtl().f[12]++;
    cov_21c2hnsjtl().s[95]++;
    if (!user?.id) {
      /* istanbul ignore next */
      cov_21c2hnsjtl().b[18][0]++;
      cov_21c2hnsjtl().s[96]++;
      return false;
    } else
    /* istanbul ignore next */
    {
      cov_21c2hnsjtl().b[18][1]++;
    }
    cov_21c2hnsjtl().s[97]++;
    try {
      // Update in state first for immediate feedback
      const updatedPreferences =
      /* istanbul ignore next */
      (cov_21c2hnsjtl().s[98]++, _objectSpread(_objectSpread({}, user.preferences), preferences));
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[99]++;
      setUser(prev => {
        /* istanbul ignore next */
        cov_21c2hnsjtl().f[13]++;
        cov_21c2hnsjtl().s[100]++;
        return prev ?
        /* istanbul ignore next */
        (cov_21c2hnsjtl().b[19][0]++, _objectSpread(_objectSpread({}, prev), {}, {
          preferences: updatedPreferences
        })) :
        /* istanbul ignore next */
        (cov_21c2hnsjtl().b[19][1]++, null);
      });

      // Cache in localStorage
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[101]++;
      localStorage.setItem(`user_prefs_${user.id}`, JSON.stringify(updatedPreferences));
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[102]++;
      return true;
    } catch (error) {
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[103]++;
      console.error('Error updating user preferences:', error);
      /* istanbul ignore next */
      cov_21c2hnsjtl().s[104]++;
      return false;
    }
  };

  // Role checking utility
  /* istanbul ignore next */
  cov_21c2hnsjtl().s[105]++;
  const hasRole = role => {
    /* istanbul ignore next */
    cov_21c2hnsjtl().f[14]++;
    cov_21c2hnsjtl().s[106]++;
    return /* istanbul ignore next */(cov_21c2hnsjtl().b[20][0]++, user?.roles?.includes(role)) ||
    /* istanbul ignore next */
    (cov_21c2hnsjtl().b[20][1]++, false);
  };

  // Get formatted display name
  /* istanbul ignore next */
  cov_21c2hnsjtl().s[107]++;
  const getDisplayName = () => {
    /* istanbul ignore next */
    cov_21c2hnsjtl().f[15]++;
    cov_21c2hnsjtl().s[108]++;
    if (!user) {
      /* istanbul ignore next */
      cov_21c2hnsjtl().b[21][0]++;
      cov_21c2hnsjtl().s[109]++;
      return 'User';
    } else
    /* istanbul ignore next */
    {
      cov_21c2hnsjtl().b[21][1]++;
    }
    cov_21c2hnsjtl().s[110]++;
    if (
    /* istanbul ignore next */
    (cov_21c2hnsjtl().b[23][0]++, user.given_name) &&
    /* istanbul ignore next */
    (cov_21c2hnsjtl().b[23][1]++, user.family_name)) {
      /* istanbul ignore next */
      cov_21c2hnsjtl().b[22][0]++;
      cov_21c2hnsjtl().s[111]++;
      return `${user.given_name} ${user.family_name}`;
    } else
    /* istanbul ignore next */
    {
      cov_21c2hnsjtl().b[22][1]++;
    }
    cov_21c2hnsjtl().s[112]++;
    if (user.name) {
      /* istanbul ignore next */
      cov_21c2hnsjtl().b[24][0]++;
      cov_21c2hnsjtl().s[113]++;
      return user.name;
    } else
    /* istanbul ignore next */
    {
      cov_21c2hnsjtl().b[24][1]++;
    }
    cov_21c2hnsjtl().s[114]++;
    if (user.email) {
      /* istanbul ignore next */
      cov_21c2hnsjtl().b[25][0]++;
      cov_21c2hnsjtl().s[115]++;
      return user.email.split('@')[0];
    } else
    /* istanbul ignore next */
    {
      cov_21c2hnsjtl().b[25][1]++;
    }
    cov_21c2hnsjtl().s[116]++;
    return `User ${user.id.substring(0, 8)}`;
  };
  const value =
  /* istanbul ignore next */
  (cov_21c2hnsjtl().s[117]++, {
    isAuthenticated,
    isLoading,
    user,
    tenant,
    tenantLoading,
    tenantError,
    signOut,
    refreshTenant,
    updateUserPreferences,
    hasRole,
    getDisplayName
  });
  /* istanbul ignore next */
  cov_21c2hnsjtl().s[118]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  AppContext.Provider,
  /* istanbul ignore next */
  {
    value: value,
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 313,
      columnNumber: 5
    }
  }, children);
}

// Custom hook for using app context
/* istanbul ignore next */
cov_21c2hnsjtl().s[119]++;
export const useApp = () => {
  /* istanbul ignore next */
  cov_21c2hnsjtl().f[16]++;
  const context =
  /* istanbul ignore next */
  (cov_21c2hnsjtl().s[120]++, useContext(AppContext));
  /* istanbul ignore next */
  cov_21c2hnsjtl().s[121]++;
  if (context === undefined) {
    /* istanbul ignore next */
    cov_21c2hnsjtl().b[26][0]++;
    cov_21c2hnsjtl().s[122]++;
    throw new Error('useApp must be used within an AppProvider');
  } else
  /* istanbul ignore next */
  {
    cov_21c2hnsjtl().b[26][1]++;
  }
  cov_21c2hnsjtl().s[123]++;
  return context;
};

// Backward compatibility hooks
/* istanbul ignore next */
cov_21c2hnsjtl().s[124]++;
export const useAuth = () => {
  /* istanbul ignore next */
  cov_21c2hnsjtl().f[17]++;
  const {
    isAuthenticated,
    isLoading,
    user,
    signOut,
    hasRole
  } =
  /* istanbul ignore next */
  (cov_21c2hnsjtl().s[125]++, useApp());
  /* istanbul ignore next */
  cov_21c2hnsjtl().s[126]++;
  return {
    isAuthenticated,
    isLoading,
    user,
    signOut,
    hasRole
  };
};
/* istanbul ignore next */
cov_21c2hnsjtl().s[127]++;
export const useTenant = () => {
  /* istanbul ignore next */
  cov_21c2hnsjtl().f[18]++;
  const {
    tenant,
    tenantLoading,
    tenantError,
    refreshTenant
  } =
  /* istanbul ignore next */
  (cov_21c2hnsjtl().s[128]++, useApp());
  /* istanbul ignore next */
  cov_21c2hnsjtl().s[129]++;
  return {
    tenant,
    loading: tenantLoading,
    error: tenantError,
    refreshTenant
  };
};
/* istanbul ignore next */
cov_21c2hnsjtl().s[130]++;
export const useUser = () => {
  /* istanbul ignore next */
  cov_21c2hnsjtl().f[19]++;
  const {
    user,
    isLoading,
    updateUserPreferences,
    getDisplayName,
    hasRole
  } =
  /* istanbul ignore next */
  (cov_21c2hnsjtl().s[131]++, useApp());
  /* istanbul ignore next */
  cov_21c2hnsjtl().s[132]++;
  return {
    user,
    isLoading,
    updateUserPreferences,
    getDisplayName,
    hasRole
  };
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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