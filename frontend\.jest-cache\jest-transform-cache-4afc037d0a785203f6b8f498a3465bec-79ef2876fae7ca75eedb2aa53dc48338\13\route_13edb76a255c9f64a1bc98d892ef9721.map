{"version": 3, "names": ["cov_1win19t05x", "actualCoverage", "requireAuth", "requireRole", "checkRateLimit", "getRateLimitIdentifier", "createSuccessResponse", "createErrorResponse", "createRateLimitResponse", "ApiErrorCode", "HttpStatus", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validateRequestBody", "validateQueryParams", "userCreateSchema", "paginationSchema", "execute<PERSON>uery", "executeQuerySingle", "GET", "s", "request", "f", "authResult", "success", "b", "rateLimitId", "session", "rateLimit", "allowed", "roleResult", "response", "searchParams", "URL", "url", "queryValidation", "page", "limit", "sortBy", "sortOrder", "data", "offset", "allowedSortFields", "sortField", "includes", "query", "toUpperCase", "result", "timeout", "DATABASE_ERROR", "INTERNAL_SERVER_ERROR", "users", "totalCount", "length", "parseInt", "total_count", "toString", "totalPages", "Math", "ceil", "map", "_ref", "user", "_objectWithoutProperties", "_excluded", "pagination", "hasNext", "has<PERSON>rev", "POST", "bodyValidation", "userData", "existingUserQuery", "existingUser", "email", "VALIDATION_ERROR", "CONFLICT", "insert<PERSON><PERSON>y", "insertResult", "name", "given_name", "family_name", "JSON", "stringify", "roles", "console", "log", "CREATED", "OPTIONS", "Response", "status", "headers"], "sources": ["route.ts"], "sourcesContent": ["/**\n * Users API Route\n * \n * This route demonstrates best practices for API implementation including:\n * - Secure authentication and authorization\n * - Input validation with Zod schemas\n * - Standardized error handling\n * - Proper database operations\n * - Rate limiting\n * - Security headers\n */\n\nimport { NextRequest } from 'next/server';\nimport { requireAuth, requireRole, checkRateLimit, getRateLimitIdentifier } from '@/lib/auth-middleware';\nimport { \n  createSuccessResponse, \n  createErrorResponse, \n  createRateLimitResponse,\n  ApiErrorCode, \n  HttpStatus,\n  withErrorHandling \n} from '@/lib/api-response';\nimport { \n  validateRequestBody, \n  validateQueryParams,\n  userCreateSchema,\n  paginationSchema,\n  PaginationParams,\n  UserCreateData \n} from '@/lib/validation';\nimport { executeQuery, executeQuerySingle } from '@/lib/database';\n\n// User interface\ninterface User {\n  id: string;\n  email: string;\n  name?: string;\n  given_name?: string;\n  family_name?: string;\n  roles: string[];\n  created_at: Date;\n  updated_at?: Date;\n  last_login?: Date;\n}\n\n// GET /api/users - List users with pagination\nexport const GET = withErrorHandling(async (request: NextRequest) => {\n  // Rate limiting\n  const authResult = await requireAuth();\n  if (authResult.success) {\n    const rateLimitId = getRateLimitIdentifier(request, authResult.session);\n    const rateLimit = checkRateLimit(rateLimitId, 100, 60000); // 100 requests per minute\n    \n    if (!rateLimit.allowed) {\n      return createRateLimitResponse();\n    }\n  }\n\n  // Verify authentication and admin role\n  const roleResult = await requireRole(['admin', 'user_manager']);\n  if (!roleResult.success) {\n    return roleResult.response!;\n  }\n\n  const session = roleResult.session!;\n\n  // Validate query parameters\n  const { searchParams } = new URL(request.url);\n  const queryValidation = validateQueryParams(searchParams, paginationSchema);\n  \n  if (!queryValidation.success) {\n    return queryValidation.response;\n  }\n\n  const { page, limit, sortBy, sortOrder }: PaginationParams = queryValidation.data;\n\n  // Calculate offset for pagination\n  const offset = (page - 1) * limit;\n\n  // Build query with proper sorting\n  const allowedSortFields = ['email', 'name', 'created_at', 'last_login'];\n  const sortField = allowedSortFields.includes(sortBy || '') ? sortBy : 'created_at';\n  \n  const query = `\n    SELECT \n      id,\n      email,\n      name,\n      given_name,\n      family_name,\n      roles,\n      created_at,\n      updated_at,\n      last_login,\n      COUNT(*) OVER() as total_count\n    FROM users\n    ORDER BY ${sortField} ${sortOrder.toUpperCase()}\n    LIMIT $1 OFFSET $2\n  `;\n\n  const result = await executeQuery<User & { total_count: number }>(\n    query, \n    [limit, offset],\n    { timeout: 10000 }\n  );\n\n  if (!result.success) {\n    return createErrorResponse(\n      'Failed to fetch users',\n      ApiErrorCode.DATABASE_ERROR,\n      HttpStatus.INTERNAL_SERVER_ERROR\n    );\n  }\n\n  const users = result.data || [];\n  const totalCount = users.length > 0 ? parseInt(users[0].total_count.toString()) : 0;\n  const totalPages = Math.ceil(totalCount / limit);\n\n  return createSuccessResponse({\n    users: users.map(({ total_count, ...user }) => user),\n    pagination: {\n      page,\n      limit,\n      totalCount,\n      totalPages,\n      hasNext: page < totalPages,\n      hasPrev: page > 1\n    }\n  }, 'Users retrieved successfully');\n});\n\n// POST /api/users - Create a new user\nexport const POST = withErrorHandling(async (request: NextRequest) => {\n  // Verify authentication and admin role\n  const roleResult = await requireRole(['admin', 'user_manager']);\n  if (!roleResult.success) {\n    return roleResult.response!;\n  }\n\n  const session = roleResult.session!;\n\n  // Rate limiting for user creation (more restrictive)\n  const rateLimitId = getRateLimitIdentifier(request, session);\n  const rateLimit = checkRateLimit(rateLimitId, 10, 60000); // 10 user creations per minute\n  \n  if (!rateLimit.allowed) {\n    return createRateLimitResponse();\n  }\n\n  // Validate request body\n  const bodyValidation = await validateRequestBody(request, userCreateSchema);\n  \n  if (!bodyValidation.success) {\n    return bodyValidation.response;\n  }\n\n  const userData: UserCreateData = bodyValidation.data;\n\n  // Check if user already exists\n  const existingUserQuery = 'SELECT id FROM users WHERE email = $1';\n  const existingUser = await executeQuerySingle(existingUserQuery, [userData.email]);\n\n  if (existingUser.success && existingUser.data) {\n    return createErrorResponse(\n      'User with this email already exists',\n      ApiErrorCode.VALIDATION_ERROR,\n      HttpStatus.CONFLICT\n    );\n  }\n\n  // Create new user\n  const insertQuery = `\n    INSERT INTO users (\n      email, \n      name, \n      given_name, \n      family_name, \n      roles,\n      created_at\n    ) \n    VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)\n    RETURNING \n      id,\n      email,\n      name,\n      given_name,\n      family_name,\n      roles,\n      created_at\n  `;\n\n  const insertResult = await executeQuerySingle<User>(\n    insertQuery,\n    [\n      userData.email,\n      userData.name || null,\n      userData.given_name || null,\n      userData.family_name || null,\n      JSON.stringify(userData.roles)\n    ]\n  );\n\n  if (!insertResult.success) {\n    return createErrorResponse(\n      'Failed to create user',\n      ApiErrorCode.DATABASE_ERROR,\n      HttpStatus.INTERNAL_SERVER_ERROR\n    );\n  }\n\n  // Log user creation for audit\n  console.log(`User created: ${userData.email} by ${session.email}`);\n\n  return createSuccessResponse(\n    insertResult.data,\n    'User created successfully',\n    HttpStatus.CREATED\n  );\n});\n\n// OPTIONS /api/users - CORS preflight\nexport async function OPTIONS() {\n  return new Response(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n      'Access-Control-Max-Age': '86400',\n    },\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASE,WAAW,EAAEC,WAAW,EAAEC,cAAc,EAAEC,sBAAsB,QAAQ,uBAAuB;AACxG,SACEC,qBAAqB,EACrBC,mBAAmB,EACnBC,uBAAuB,EACvBC,YAAY,EACZC,UAAU,EACVC,iBAAiB,QACZ,oBAAoB;AAC3B,SACEC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB,EAChBC,gBAAgB,QAGX,kBAAkB;AACzB,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,gBAAgB;;AAEjE;;AAaA;AACA,OAAO,MAAMC,GAAG;AAAA;AAAA,CAAAlB,cAAA,GAAAmB,CAAA,OAAGR,iBAAiB,CAAC,MAAOS,OAAoB,IAAK;EAAA;EAAApB,cAAA,GAAAqB,CAAA;EACnE;EACA,MAAMC,UAAU;EAAA;EAAA,CAAAtB,cAAA,GAAAmB,CAAA,OAAG,MAAMjB,WAAW,CAAC,CAAC;EAAC;EAAAF,cAAA,GAAAmB,CAAA;EACvC,IAAIG,UAAU,CAACC,OAAO,EAAE;IAAA;IAAAvB,cAAA,GAAAwB,CAAA;IACtB,MAAMC,WAAW;IAAA;IAAA,CAAAzB,cAAA,GAAAmB,CAAA,OAAGd,sBAAsB,CAACe,OAAO,EAAEE,UAAU,CAACI,OAAO,CAAC;IACvE,MAAMC,SAAS;IAAA;IAAA,CAAA3B,cAAA,GAAAmB,CAAA,OAAGf,cAAc,CAACqB,WAAW,EAAE,GAAG,EAAE,KAAK,CAAC,EAAC,CAAC;IAAA;IAAAzB,cAAA,GAAAmB,CAAA;IAE3D,IAAI,CAACQ,SAAS,CAACC,OAAO,EAAE;MAAA;MAAA5B,cAAA,GAAAwB,CAAA;MAAAxB,cAAA,GAAAmB,CAAA;MACtB,OAAOX,uBAAuB,CAAC,CAAC;IAClC,CAAC;IAAA;IAAA;MAAAR,cAAA,GAAAwB,CAAA;IAAA;EACH,CAAC;EAAA;EAAA;IAAAxB,cAAA,GAAAwB,CAAA;EAAA;;EAED;EACA,MAAMK,UAAU;EAAA;EAAA,CAAA7B,cAAA,GAAAmB,CAAA,OAAG,MAAMhB,WAAW,CAAC,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;EAAC;EAAAH,cAAA,GAAAmB,CAAA;EAChE,IAAI,CAACU,UAAU,CAACN,OAAO,EAAE;IAAA;IAAAvB,cAAA,GAAAwB,CAAA;IAAAxB,cAAA,GAAAmB,CAAA;IACvB,OAAOU,UAAU,CAACC,QAAQ;EAC5B,CAAC;EAAA;EAAA;IAAA9B,cAAA,GAAAwB,CAAA;EAAA;EAED,MAAME,OAAO;EAAA;EAAA,CAAA1B,cAAA,GAAAmB,CAAA,QAAGU,UAAU,CAACH,OAAO,CAAC;;EAEnC;EACA,MAAM;IAAEK;EAAa,CAAC;EAAA;EAAA,CAAA/B,cAAA,GAAAmB,CAAA,QAAG,IAAIa,GAAG,CAACZ,OAAO,CAACa,GAAG,CAAC;EAC7C,MAAMC,eAAe;EAAA;EAAA,CAAAlC,cAAA,GAAAmB,CAAA,QAAGN,mBAAmB,CAACkB,YAAY,EAAEhB,gBAAgB,CAAC;EAAC;EAAAf,cAAA,GAAAmB,CAAA;EAE5E,IAAI,CAACe,eAAe,CAACX,OAAO,EAAE;IAAA;IAAAvB,cAAA,GAAAwB,CAAA;IAAAxB,cAAA,GAAAmB,CAAA;IAC5B,OAAOe,eAAe,CAACJ,QAAQ;EACjC,CAAC;EAAA;EAAA;IAAA9B,cAAA,GAAAwB,CAAA;EAAA;EAED,MAAM;IAAEW,IAAI;IAAEC,KAAK;IAAEC,MAAM;IAAEC;EAA4B,CAAC;EAAA;EAAA,CAAAtC,cAAA,GAAAmB,CAAA,QAAGe,eAAe,CAACK,IAAI;;EAEjF;EACA,MAAMC,MAAM;EAAA;EAAA,CAAAxC,cAAA,GAAAmB,CAAA,QAAG,CAACgB,IAAI,GAAG,CAAC,IAAIC,KAAK;;EAEjC;EACA,MAAMK,iBAAiB;EAAA;EAAA,CAAAzC,cAAA,GAAAmB,CAAA,QAAG,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,CAAC;EACvE,MAAMuB,SAAS;EAAA;EAAA,CAAA1C,cAAA,GAAAmB,CAAA,QAAGsB,iBAAiB,CAACE,QAAQ;EAAC;EAAA,CAAA3C,cAAA,GAAAwB,CAAA,UAAAa,MAAM;EAAA;EAAA,CAAArC,cAAA,GAAAwB,CAAA,UAAI,EAAE,EAAC;EAAA;EAAA,CAAAxB,cAAA,GAAAwB,CAAA,UAAGa,MAAM;EAAA;EAAA,CAAArC,cAAA,GAAAwB,CAAA,UAAG,YAAY;EAElF,MAAMoB,KAAK;EAAA;EAAA,CAAA5C,cAAA,GAAAmB,CAAA,QAAG;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeuB,SAAS,IAAIJ,SAAS,CAACO,WAAW,CAAC,CAAC;AACnD;AACA,GAAG;EAED,MAAMC,MAAM;EAAA;EAAA,CAAA9C,cAAA,GAAAmB,CAAA,QAAG,MAAMH,YAAY,CAC/B4B,KAAK,EACL,CAACR,KAAK,EAAEI,MAAM,CAAC,EACf;IAAEO,OAAO,EAAE;EAAM,CACnB,CAAC;EAAC;EAAA/C,cAAA,GAAAmB,CAAA;EAEF,IAAI,CAAC2B,MAAM,CAACvB,OAAO,EAAE;IAAA;IAAAvB,cAAA,GAAAwB,CAAA;IAAAxB,cAAA,GAAAmB,CAAA;IACnB,OAAOZ,mBAAmB,CACxB,uBAAuB,EACvBE,YAAY,CAACuC,cAAc,EAC3BtC,UAAU,CAACuC,qBACb,CAAC;EACH,CAAC;EAAA;EAAA;IAAAjD,cAAA,GAAAwB,CAAA;EAAA;EAED,MAAM0B,KAAK;EAAA;EAAA,CAAAlD,cAAA,GAAAmB,CAAA;EAAG;EAAA,CAAAnB,cAAA,GAAAwB,CAAA,UAAAsB,MAAM,CAACP,IAAI;EAAA;EAAA,CAAAvC,cAAA,GAAAwB,CAAA,UAAI,EAAE;EAC/B,MAAM2B,UAAU;EAAA;EAAA,CAAAnD,cAAA,GAAAmB,CAAA,QAAG+B,KAAK,CAACE,MAAM,GAAG,CAAC;EAAA;EAAA,CAAApD,cAAA,GAAAwB,CAAA,UAAG6B,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAACI,WAAW,CAACC,QAAQ,CAAC,CAAC,CAAC;EAAA;EAAA,CAAAvD,cAAA,GAAAwB,CAAA,UAAG,CAAC;EACnF,MAAMgC,UAAU;EAAA;EAAA,CAAAxD,cAAA,GAAAmB,CAAA,QAAGsC,IAAI,CAACC,IAAI,CAACP,UAAU,GAAGf,KAAK,CAAC;EAAC;EAAApC,cAAA,GAAAmB,CAAA;EAEjD,OAAOb,qBAAqB,CAAC;IAC3B4C,KAAK,EAAEA,KAAK,CAACS,GAAG;IAAC;IAAAC,IAAA,IAA8BC;MAAAA;MAAAA,GAAA,CAA7BA;UAAEP;QAAqB,CAAC,GAAAM,IAAA;QAANC,IAAI,GAAAC,wBAAA,CAAAF,IAAA,EAAAG,SAAA;MAAA;MAAA/D,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAmB,CAAA;MAAO0C,MAAA,CAAAA,IAAI;IAAD,CAAC,CAAC;IACpDG,UAAU,EAAE;MACV7B,IAAI;MACJC,KAAK;MACLe,UAAU;MACVK,UAAU;MACVS,OAAO,EAAE9B,IAAI,GAAGqB,UAAU;MAC1BU,OAAO,EAAE/B,IAAI,GAAG;IAClB;EACF,CAAC,EAAE,8BAA8B,CAAC;AACpC,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMgC,IAAI;AAAA;AAAA,CAAAnE,cAAA,GAAAmB,CAAA,QAAGR,iBAAiB,CAAC,MAAOS,OAAoB,IAAK;EAAA;EAAApB,cAAA,GAAAqB,CAAA;EACpE;EACA,MAAMQ,UAAU;EAAA;EAAA,CAAA7B,cAAA,GAAAmB,CAAA,QAAG,MAAMhB,WAAW,CAAC,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;EAAC;EAAAH,cAAA,GAAAmB,CAAA;EAChE,IAAI,CAACU,UAAU,CAACN,OAAO,EAAE;IAAA;IAAAvB,cAAA,GAAAwB,CAAA;IAAAxB,cAAA,GAAAmB,CAAA;IACvB,OAAOU,UAAU,CAACC,QAAQ;EAC5B,CAAC;EAAA;EAAA;IAAA9B,cAAA,GAAAwB,CAAA;EAAA;EAED,MAAME,OAAO;EAAA;EAAA,CAAA1B,cAAA,GAAAmB,CAAA,QAAGU,UAAU,CAACH,OAAO,CAAC;;EAEnC;EACA,MAAMD,WAAW;EAAA;EAAA,CAAAzB,cAAA,GAAAmB,CAAA,QAAGd,sBAAsB,CAACe,OAAO,EAAEM,OAAO,CAAC;EAC5D,MAAMC,SAAS;EAAA;EAAA,CAAA3B,cAAA,GAAAmB,CAAA,QAAGf,cAAc,CAACqB,WAAW,EAAE,EAAE,EAAE,KAAK,CAAC,EAAC,CAAC;EAAA;EAAAzB,cAAA,GAAAmB,CAAA;EAE1D,IAAI,CAACQ,SAAS,CAACC,OAAO,EAAE;IAAA;IAAA5B,cAAA,GAAAwB,CAAA;IAAAxB,cAAA,GAAAmB,CAAA;IACtB,OAAOX,uBAAuB,CAAC,CAAC;EAClC,CAAC;EAAA;EAAA;IAAAR,cAAA,GAAAwB,CAAA;EAAA;;EAED;EACA,MAAM4C,cAAc;EAAA;EAAA,CAAApE,cAAA,GAAAmB,CAAA,QAAG,MAAMP,mBAAmB,CAACQ,OAAO,EAAEN,gBAAgB,CAAC;EAAC;EAAAd,cAAA,GAAAmB,CAAA;EAE5E,IAAI,CAACiD,cAAc,CAAC7C,OAAO,EAAE;IAAA;IAAAvB,cAAA,GAAAwB,CAAA;IAAAxB,cAAA,GAAAmB,CAAA;IAC3B,OAAOiD,cAAc,CAACtC,QAAQ;EAChC,CAAC;EAAA;EAAA;IAAA9B,cAAA,GAAAwB,CAAA;EAAA;EAED,MAAM6C,QAAwB;EAAA;EAAA,CAAArE,cAAA,GAAAmB,CAAA,QAAGiD,cAAc,CAAC7B,IAAI;;EAEpD;EACA,MAAM+B,iBAAiB;EAAA;EAAA,CAAAtE,cAAA,GAAAmB,CAAA,QAAG,uCAAuC;EACjE,MAAMoD,YAAY;EAAA;EAAA,CAAAvE,cAAA,GAAAmB,CAAA,QAAG,MAAMF,kBAAkB,CAACqD,iBAAiB,EAAE,CAACD,QAAQ,CAACG,KAAK,CAAC,CAAC;EAAC;EAAAxE,cAAA,GAAAmB,CAAA;EAEnF;EAAI;EAAA,CAAAnB,cAAA,GAAAwB,CAAA,WAAA+C,YAAY,CAAChD,OAAO;EAAA;EAAA,CAAAvB,cAAA,GAAAwB,CAAA,WAAI+C,YAAY,CAAChC,IAAI,GAAE;IAAA;IAAAvC,cAAA,GAAAwB,CAAA;IAAAxB,cAAA,GAAAmB,CAAA;IAC7C,OAAOZ,mBAAmB,CACxB,qCAAqC,EACrCE,YAAY,CAACgE,gBAAgB,EAC7B/D,UAAU,CAACgE,QACb,CAAC;EACH,CAAC;EAAA;EAAA;IAAA1E,cAAA,GAAAwB,CAAA;EAAA;;EAED;EACA,MAAMmD,WAAW;EAAA;EAAA,CAAA3E,cAAA,GAAAmB,CAAA,QAAG;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;EAED,MAAMyD,YAAY;EAAA;EAAA,CAAA5E,cAAA,GAAAmB,CAAA,QAAG,MAAMF,kBAAkB,CAC3C0D,WAAW,EACX,CACEN,QAAQ,CAACG,KAAK;EACd;EAAA,CAAAxE,cAAA,GAAAwB,CAAA,WAAA6C,QAAQ,CAACQ,IAAI;EAAA;EAAA,CAAA7E,cAAA,GAAAwB,CAAA,WAAI,IAAI;EACrB;EAAA,CAAAxB,cAAA,GAAAwB,CAAA,WAAA6C,QAAQ,CAACS,UAAU;EAAA;EAAA,CAAA9E,cAAA,GAAAwB,CAAA,WAAI,IAAI;EAC3B;EAAA,CAAAxB,cAAA,GAAAwB,CAAA,WAAA6C,QAAQ,CAACU,WAAW;EAAA;EAAA,CAAA/E,cAAA,GAAAwB,CAAA,WAAI,IAAI,GAC5BwD,IAAI,CAACC,SAAS,CAACZ,QAAQ,CAACa,KAAK,CAAC,CAElC,CAAC;EAAC;EAAAlF,cAAA,GAAAmB,CAAA;EAEF,IAAI,CAACyD,YAAY,CAACrD,OAAO,EAAE;IAAA;IAAAvB,cAAA,GAAAwB,CAAA;IAAAxB,cAAA,GAAAmB,CAAA;IACzB,OAAOZ,mBAAmB,CACxB,uBAAuB,EACvBE,YAAY,CAACuC,cAAc,EAC3BtC,UAAU,CAACuC,qBACb,CAAC;EACH,CAAC;EAAA;EAAA;IAAAjD,cAAA,GAAAwB,CAAA;EAAA;;EAED;EAAAxB,cAAA,GAAAmB,CAAA;EACAgE,OAAO,CAACC,GAAG,CAAC,iBAAiBf,QAAQ,CAACG,KAAK,OAAO9C,OAAO,CAAC8C,KAAK,EAAE,CAAC;EAAC;EAAAxE,cAAA,GAAAmB,CAAA;EAEnE,OAAOb,qBAAqB,CAC1BsE,YAAY,CAACrC,IAAI,EACjB,2BAA2B,EAC3B7B,UAAU,CAAC2E,OACb,CAAC;AACH,CAAC,CAAC;;AAEF;AACA,OAAO,eAAeC,OAAOA,CAAA,EAAG;EAAA;EAAAtF,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAmB,CAAA;EAC9B,OAAO,IAAIoE,QAAQ,CAAC,IAAI,EAAE;IACxBC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE;MACP,6BAA6B,EAAE,GAAG;MAClC,8BAA8B,EAAE,oBAAoB;MACpD,8BAA8B,EAAE,6BAA6B;MAC7D,wBAAwB,EAAE;IAC5B;EACF,CAAC,CAAC;AACJ", "ignoreList": []}