74937a891dda27eb2a7d0da319fa0e49
/* istanbul ignore next */
function cov_14xni1g51m() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\page.tsx";
  var hash = "cad7d17c5e63e466c2be17c05c9e383d14dbd561";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\page.tsx",
    statementMap: {
      "0": {
        start: {
          line: 5,
          column: 2
        },
        end: {
          line: 5,
          column: 24
        }
      }
    },
    fnMap: {
      "0": {
        name: "Home",
        decl: {
          start: {
            line: 3,
            column: 24
          },
          end: {
            line: 3,
            column: 28
          }
        },
        loc: {
          start: {
            line: 3,
            column: 31
          },
          end: {
            line: 6,
            column: 1
          }
        },
        line: 3
      }
    },
    branchMap: {},
    s: {
      "0": 0
    },
    f: {
      "0": 0
    },
    b: {},
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "cad7d17c5e63e466c2be17c05c9e383d14dbd561"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_14xni1g51m = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_14xni1g51m();
import { redirect } from 'next/navigation';
export default function Home() {
  /* istanbul ignore next */
  cov_14xni1g51m().f[0]++;
  cov_14xni1g51m().s[0]++;
  // Redirect to dashboard directly
  redirect('/dashboard');
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfMTR4bmkxZzUxbSIsImFjdHVhbENvdmVyYWdlIiwicmVkaXJlY3QiLCJIb21lIiwiZiIsInMiXSwic291cmNlcyI6WyJwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZWRpcmVjdCB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgLy8gUmVkaXJlY3QgdG8gZGFzaGJvYXJkIGRpcmVjdGx5XG4gIHJlZGlyZWN0KCcvZGFzaGJvYXJkJylcbn1cblxuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFlWTtJQUFBQSxjQUFBLFlBQUFBLENBQUE7TUFBQSxPQUFBQyxjQUFBO0lBQUE7RUFBQTtFQUFBLE9BQUFBLGNBQUE7QUFBQTtBQUFBRCxjQUFBO0FBZlosU0FBU0UsUUFBUSxRQUFRLGlCQUFpQjtBQUUxQyxlQUFlLFNBQVNDLElBQUlBLENBQUEsRUFBRztFQUFBO0VBQUFILGNBQUEsR0FBQUksQ0FBQTtFQUFBSixjQUFBLEdBQUFLLENBQUE7RUFDN0I7RUFDQUgsUUFBUSxDQUFDLFlBQVksQ0FBQztBQUN4QiIsImlnbm9yZUxpc3QiOltdfQ==