{"version": 3, "names": ["_jsxFileName", "React", "__jsx", "createElement", "cov_2ga3zjmfwz", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "_coverageSchema", "coverage", "actualCoverage", "useEffect", "useState", "useRouter", "useAuth", "Sidebar", "DashboardLayout", "children", "isAuthenticated", "isLoading", "router", "isRedirecting", "setIsRedirecting", "hasInitialized", "setHasInitialized", "console", "log", "timestamp", "Date", "toLocaleTimeString", "urlParams", "URLSearchParams", "window", "location", "search", "hasOAuthCode", "has", "push", "cleanUrl", "pathname", "history", "replaceState", "document", "title", "className", "__self", "__source", "fileName", "lineNumber", "columnNumber"], "sources": ["layout.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useEffect, useState } from 'react'\r\nimport { useRouter } from 'next/navigation'\r\nimport { useAuth } from '@/contexts/AppContext'\r\nimport Sidebar from '@/components/layout/Sidebar'\r\n\r\nexport default function DashboardLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode\r\n}) {\r\n  const { isAuthenticated, isLoading } = useAuth()\r\n  const router = useRouter()\r\n  const [isRedirecting, setIsRedirecting] = useState(false)\r\n  const [hasInitialized, setHasInitialized] = useState(false)\r\n  \r\n  useEffect(() => {\r\n    console.log('🏠 [DASHBOARD-LAYOUT] Auth state changed:', {\r\n      isLoading,\r\n      isAuthenticated,\r\n      isRedirecting,\r\n      hasInitialized,\r\n      timestamp: new Date().toLocaleTimeString()\r\n    });\r\n\r\n    // Check if this is an OAuth callback (has code parameter)\r\n    const urlParams = new URLSearchParams(window.location.search)\r\n    const hasOAuthCode = urlParams.has('code')\r\n\r\n    if (hasOAuthCode) {\r\n      console.log('🔄 [DASHBOARD-LAYOUT] OAuth callback detected, waiting for Amplify to process...')\r\n      // Give Amplify extra time to process OAuth callback\r\n      return\r\n    }\r\n\r\n    // Mark as initialized once we've completed the first auth check\r\n    if (!isLoading && !hasInitialized) {\r\n      setHasInitialized(true)\r\n    }\r\n\r\n    // Only redirect if authentication check is complete and user is not authenticated\r\n    if (!isLoading && !isAuthenticated && !isRedirecting && hasInitialized) {\r\n      setIsRedirecting(true)\r\n      console.log('❌ [DASHBOARD-LAYOUT] User not authenticated, redirecting to login')\r\n      router.push('/login')\r\n    } else if (!isLoading && isAuthenticated && hasInitialized) {\r\n      console.log('✅ [DASHBOARD-LAYOUT] User is authenticated, showing dashboard')\r\n\r\n      // Clean up URL if it has OAuth parameters\r\n      if (hasOAuthCode) {\r\n        const cleanUrl = window.location.pathname\r\n        window.history.replaceState({}, document.title, cleanUrl)\r\n      }\r\n    }\r\n  }, [isLoading, isAuthenticated, router, isRedirecting, hasInitialized])\r\n\r\n  // Show loading state only on initial load or when redirecting\r\n  if ((isLoading && !hasInitialized) || isRedirecting) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-screen\">\r\n        <div className=\"w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin\"></div>\r\n        <p className=\"ml-4 text-lg\">\r\n          {isRedirecting ? 'Redirecting to login...' : 'Loading your dashboard...'}\r\n        </p>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // Don't render dashboard if not authenticated (but don't show loading if already initialized)\r\n  if (!isAuthenticated && hasInitialized) {\r\n    return null // Will redirect via useEffect\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex h-screen\">\r\n      <Sidebar />\r\n      <main className=\"main-content\">\r\n        {children}\r\n      </main>\r\n    </div>\r\n  )\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAAA,YAAY;;AAAA;AAAA,IAAAA,YAAA;AAAA,OAAAC,KAAA;AAAA,IAAAC,KAAA,GAAAD,KAAA,CAAAE,aAAA;AAAA,SAAAC,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,eAAA;IAAArB,IAAA;EAAA;EAAA,IAAAsB,QAAA,GAAArB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAmB,QAAA,CAAAvB,IAAA,KAAAuB,QAAA,CAAAvB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAsB,QAAA,CAAAvB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAmB,cAAA,GAAAD,QAAA,CAAAvB,IAAA;EAAA;IAeA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAyB,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAzB,cAAA;AAbZ,SAAS0B,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,OAAOC,OAAO,MAAM,6BAA6B;AAEjD,eAAe,SAASC,eAAeA,CAAC;EACtCC;AAGF,CAAC,EAAE;EAAA;EAAAhC,cAAA,GAAAqB,CAAA;EACD,MAAM;IAAEY,eAAe;IAAEC;EAAU,CAAC;EAAA;EAAA,CAAAlC,cAAA,GAAAoB,CAAA,OAAGS,OAAO,CAAC,CAAC;EAChD,MAAMM,MAAM;EAAA;EAAA,CAAAnC,cAAA,GAAAoB,CAAA,OAAGQ,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACQ,aAAa,EAAEC,gBAAgB,CAAC;EAAA;EAAA,CAAArC,cAAA,GAAAoB,CAAA,OAAGO,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACW,cAAc,EAAEC,iBAAiB,CAAC;EAAA;EAAA,CAAAvC,cAAA,GAAAoB,CAAA,OAAGO,QAAQ,CAAC,KAAK,CAAC;EAAA;EAAA3B,cAAA,GAAAoB,CAAA;EAE3DM,SAAS,CAAC,MAAM;IAAA;IAAA1B,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAoB,CAAA;IACdoB,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;MACvDP,SAAS;MACTD,eAAe;MACfG,aAAa;MACbE,cAAc;MACdI,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC;IAC3C,CAAC,CAAC;;IAEF;IACA,MAAMC,SAAS;IAAA;IAAA,CAAA7C,cAAA,GAAAoB,CAAA,OAAG,IAAI0B,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,MAAMC,YAAY;IAAA;IAAA,CAAAlD,cAAA,GAAAoB,CAAA,OAAGyB,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC;IAAA;IAAAnD,cAAA,GAAAoB,CAAA;IAE1C,IAAI8B,YAAY,EAAE;MAAA;MAAAlD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAChBoB,OAAO,CAACC,GAAG,CAAC,kFAAkF,CAAC;MAC/F;MAAA;MAAAzC,cAAA,GAAAoB,CAAA;MACA;IACF,CAAC;IAAA;IAAA;MAAApB,cAAA,GAAAsB,CAAA;IAAA;;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAACY,SAAS;IAAA;IAAA,CAAAlC,cAAA,GAAAsB,CAAA,UAAI,CAACgB,cAAc,GAAE;MAAA;MAAAtC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACjCmB,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC;IAAA;IAAA;MAAAvC,cAAA,GAAAsB,CAAA;IAAA;;IAED;IAAAtB,cAAA,GAAAoB,CAAA;IACA;IAAI;IAAA,CAAApB,cAAA,GAAAsB,CAAA,WAACY,SAAS;IAAA;IAAA,CAAAlC,cAAA,GAAAsB,CAAA,UAAI,CAACW,eAAe;IAAA;IAAA,CAAAjC,cAAA,GAAAsB,CAAA,UAAI,CAACc,aAAa;IAAA;IAAA,CAAApC,cAAA,GAAAsB,CAAA,UAAIgB,cAAc,GAAE;MAAA;MAAAtC,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACtEiB,gBAAgB,CAAC,IAAI,CAAC;MAAA;MAAArC,cAAA,GAAAoB,CAAA;MACtBoB,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC;MAAA;MAAAzC,cAAA,GAAAoB,CAAA;MAChFe,MAAM,CAACiB,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC,MAAM;MAAA;MAAApD,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAAA;MAAI;MAAA,CAAApB,cAAA,GAAAsB,CAAA,WAACY,SAAS;MAAA;MAAA,CAAAlC,cAAA,GAAAsB,CAAA,UAAIW,eAAe;MAAA;MAAA,CAAAjC,cAAA,GAAAsB,CAAA,UAAIgB,cAAc,GAAE;QAAA;QAAAtC,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC1DoB,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;;QAE5E;QAAA;QAAAzC,cAAA,GAAAoB,CAAA;QACA,IAAI8B,YAAY,EAAE;UAAA;UAAAlD,cAAA,GAAAsB,CAAA;UAChB,MAAM+B,QAAQ;UAAA;UAAA,CAAArD,cAAA,GAAAoB,CAAA,QAAG2B,MAAM,CAACC,QAAQ,CAACM,QAAQ;UAAA;UAAAtD,cAAA,GAAAoB,CAAA;UACzC2B,MAAM,CAACQ,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,EAAEL,QAAQ,CAAC;QAC3D,CAAC;QAAA;QAAA;UAAArD,cAAA,GAAAsB,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAAtB,cAAA,GAAAsB,CAAA;MAAA;IAAD;EACF,CAAC,EAAE,CAACY,SAAS,EAAED,eAAe,EAAEE,MAAM,EAAEC,aAAa,EAAEE,cAAc,CAAC,CAAC;;EAEvE;EAAA;EAAAtC,cAAA,GAAAoB,CAAA;EACA;EAAK;EAAA,CAAApB,cAAA,GAAAsB,CAAA,UAAAY,SAAS;EAAA;EAAA,CAAAlC,cAAA,GAAAsB,CAAA,UAAI,CAACgB,cAAc;EAAA;EAAA,CAAAtC,cAAA,GAAAsB,CAAA,UAAKc,aAAa,GAAE;IAAA;IAAApC,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACnD,OACE,0BAAAtB,KAAA;IAAA;IAAA;IAAA;IAAA;MAAK6D,SAAS,EAAC,2CAA2C;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAlE,YAAA;QAAAmE,UAAA;QAAAC,YAAA;MAAA;IAAA;IACxD;IAAAlE,KAAA;IAAA;IAAA;IAAA;IAAA;MAAK6D,SAAS,EAAC,6EAA6E;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAlE,YAAA;QAAAmE,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAM,CAAC;IACnG;IAAAlE,KAAA;IAAA;IAAA;IAAA;IAAA;MAAG6D,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAlE,YAAA;QAAAmE,UAAA;QAAAC,YAAA;MAAA;IAAA,GACxB5B,aAAa;IAAA;IAAA,CAAApC,cAAA,GAAAsB,CAAA,WAAG,yBAAyB;IAAA;IAAA,CAAAtB,cAAA,GAAAsB,CAAA,WAAG,2BAA2B,CACvE,CACA,CAAC;EAEV,CAAC;EAAA;EAAA;IAAAtB,cAAA,GAAAsB,CAAA;EAAA;;EAED;EAAAtB,cAAA,GAAAoB,CAAA;EACA;EAAI;EAAA,CAAApB,cAAA,GAAAsB,CAAA,YAACW,eAAe;EAAA;EAAA,CAAAjC,cAAA,GAAAsB,CAAA,WAAIgB,cAAc,GAAE;IAAA;IAAAtC,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACtC,OAAO,IAAI,EAAC;EACd,CAAC;EAAA;EAAA;IAAApB,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EAED,OACE,0BAAAtB,KAAA;EAAA;EAAA;EAAA;EAAA;IAAK6D,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAlE,YAAA;MAAAmE,UAAA;MAAAC,YAAA;IAAA;EAAA;EAC5B;EAAAlE,KAAA,CAACgC,OAAO;EAAA;EAAA;IAAA8B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAlE,YAAA;MAAAmE,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC;EACX;EAAAlE,KAAA;EAAA;EAAA;EAAA;EAAA;IAAM6D,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAlE,YAAA;MAAAmE,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3BhC,QACG,CACH,CAAC;AAEV", "ignoreList": []}