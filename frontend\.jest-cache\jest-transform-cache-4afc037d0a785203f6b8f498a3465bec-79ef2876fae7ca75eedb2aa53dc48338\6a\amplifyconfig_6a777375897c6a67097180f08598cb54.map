{"version": 3, "names": ["cov_2c8mebnvkg", "actualCoverage", "Amplify", "clientConfig", "configureAmplify", "f", "s", "getConfig", "<PERSON><PERSON>", "b", "console", "log", "aws", "auth", "config", "region", "userPoolId", "userPoolClientId", "cognitoDomain", "redirectSignIn", "redirectSignOut", "configure", "Cognito", "loginWith", "o<PERSON>h", "domain", "scopes", "responseType", "ssr", "error", "getAmplifySSR", "_request"], "sources": ["amplify-config.ts"], "sourcesContent": ["import { Amplify } from 'aws-amplify'\r\nimport { clientConfig } from './client-config'\r\n\r\n// Amplify configuration using centralized configuration\r\nexport function configureAmplify() {\r\n  // Only configure once\r\n  if (Amplify.getConfig().Auth) {\r\n    console.log('Amplify already configured, skipping...');\r\n    return;\r\n  }\r\n\r\n  // Get configuration from centralized config\r\n  const { aws, auth } = clientConfig;\r\n  const config = {\r\n    region: aws.region,\r\n    userPoolId: aws.userPoolId,\r\n    userPoolClientId: aws.userPoolClientId,\r\n    cognitoDomain: aws.cognitoDomain,\r\n    redirectSignIn: auth.redirectSignIn,\r\n    redirectSignOut: auth.redirectSignOut\r\n  };\r\n\r\n  console.log('Configuring Amplify with:', {\r\n    region: config.region,\r\n    userPoolId: config.userPoolId,\r\n    userPoolClientId: config.userPoolClientId,\r\n    cognitoDomain: config.cognitoDomain,\r\n    redirectSignIn: config.redirectSignIn,\r\n    redirectSignOut: config.redirectSignOut\r\n  });\r\n\r\n  try {\r\n    Amplify.configure({\r\n      Auth: {\r\n        Cognito: {\r\n          userPoolId: config.userPoolId,\r\n          userPoolClientId: config.userPoolClientId,\r\n          loginWith: {\r\n            oauth: {\r\n              domain: config.cognitoDomain,\r\n              scopes: [\"email\", \"profile\", \"openid\", \"aws.cognito.signin.user.admin\"],\r\n              redirectSignIn: [config.redirectSignIn],\r\n              redirectSignOut: [config.redirectSignOut],\r\n              responseType: \"code\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }, {\r\n      ssr: true\r\n    });\r\n    console.log('Amplify configured successfully with OAuth domain:', config.cognitoDomain);\r\n  } catch (error) {\r\n    console.error('Error configuring Amplify:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Export configured instance for server-side operations\r\nexport function getAmplifySSR(_request: Request) {\r\n  // For Amplify v6, we need to create a new instance with SSR context\r\n  return {\r\n    Auth: Amplify.getConfig().Auth\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ,SAASE,OAAO,QAAQ,aAAa;AACrC,SAASC,YAAY,QAAQ,iBAAiB;;AAE9C;AACA,OAAO,SAASC,gBAAgBA,CAAA,EAAG;EAAA;EAAAJ,cAAA,GAAAK,CAAA;EAAAL,cAAA,GAAAM,CAAA;EACjC;EACA,IAAIJ,OAAO,CAACK,SAAS,CAAC,CAAC,CAACC,IAAI,EAAE;IAAA;IAAAR,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAM,CAAA;IAC5BI,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAAC;IAAAX,cAAA,GAAAM,CAAA;IACvD;EACF,CAAC;EAAA;EAAA;IAAAN,cAAA,GAAAS,CAAA;EAAA;;EAED;EACA,MAAM;IAAEG,GAAG;IAAEC;EAAK,CAAC;EAAA;EAAA,CAAAb,cAAA,GAAAM,CAAA,OAAGH,YAAY;EAClC,MAAMW,MAAM;EAAA;EAAA,CAAAd,cAAA,GAAAM,CAAA,OAAG;IACbS,MAAM,EAAEH,GAAG,CAACG,MAAM;IAClBC,UAAU,EAAEJ,GAAG,CAACI,UAAU;IAC1BC,gBAAgB,EAAEL,GAAG,CAACK,gBAAgB;IACtCC,aAAa,EAAEN,GAAG,CAACM,aAAa;IAChCC,cAAc,EAAEN,IAAI,CAACM,cAAc;IACnCC,eAAe,EAAEP,IAAI,CAACO;EACxB,CAAC;EAAC;EAAApB,cAAA,GAAAM,CAAA;EAEFI,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;IACvCI,MAAM,EAAED,MAAM,CAACC,MAAM;IACrBC,UAAU,EAAEF,MAAM,CAACE,UAAU;IAC7BC,gBAAgB,EAAEH,MAAM,CAACG,gBAAgB;IACzCC,aAAa,EAAEJ,MAAM,CAACI,aAAa;IACnCC,cAAc,EAAEL,MAAM,CAACK,cAAc;IACrCC,eAAe,EAAEN,MAAM,CAACM;EAC1B,CAAC,CAAC;EAAC;EAAApB,cAAA,GAAAM,CAAA;EAEH,IAAI;IAAA;IAAAN,cAAA,GAAAM,CAAA;IACFJ,OAAO,CAACmB,SAAS,CAAC;MAChBb,IAAI,EAAE;QACJc,OAAO,EAAE;UACPN,UAAU,EAAEF,MAAM,CAACE,UAAU;UAC7BC,gBAAgB,EAAEH,MAAM,CAACG,gBAAgB;UACzCM,SAAS,EAAE;YACTC,KAAK,EAAE;cACLC,MAAM,EAAEX,MAAM,CAACI,aAAa;cAC5BQ,MAAM,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,+BAA+B,CAAC;cACvEP,cAAc,EAAE,CAACL,MAAM,CAACK,cAAc,CAAC;cACvCC,eAAe,EAAE,CAACN,MAAM,CAACM,eAAe,CAAC;cACzCO,YAAY,EAAE;YAChB;UACF;QACF;MACF;IACF,CAAC,EAAE;MACDC,GAAG,EAAE;IACP,CAAC,CAAC;IAAC;IAAA5B,cAAA,GAAAM,CAAA;IACHI,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEG,MAAM,CAACI,aAAa,CAAC;EACzF,CAAC,CAAC,OAAOW,KAAK,EAAE;IAAA;IAAA7B,cAAA,GAAAM,CAAA;IACdI,OAAO,CAACmB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAAC;IAAA7B,cAAA,GAAAM,CAAA;IACnD,MAAMuB,KAAK;EACb;AACF;;AAEA;AACA,OAAO,SAASC,aAAaA,CAACC,QAAiB,EAAE;EAAA;EAAA/B,cAAA,GAAAK,CAAA;EAAAL,cAAA,GAAAM,CAAA;EAC/C;EACA,OAAO;IACLE,IAAI,EAAEN,OAAO,CAACK,SAAS,CAAC,CAAC,CAACC;EAC5B,CAAC;AACH", "ignoreList": []}