a8ab0f68683a2149cdb580181f90607e
'use client';

/* istanbul ignore next */
var _jsxFileName = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\signout\\page.tsx";
import React from "react";
var __jsx = React.createElement;
function cov_1zmgs245lc() {
  var path = "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\signout\\page.tsx";
  var hash = "aa98941a236c1cadfbdebe6be84525018e09093a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\OneDrive\\Documents\\RenewTrack\\frontend\\app\\signout\\page.tsx",
    statementMap: {
      "0": {
        start: {
          line: 8,
          column: 17
        },
        end: {
          line: 8,
          column: 28
        }
      },
      "1": {
        start: {
          line: 10,
          column: 2
        },
        end: {
          line: 23,
          column: 14
        }
      },
      "2": {
        start: {
          line: 12,
          column: 6
        },
        end: {
          line: 19,
          column: 7
        }
      },
      "3": {
        start: {
          line: 13,
          column: 8
        },
        end: {
          line: 13,
          column: 23
        }
      },
      "4": {
        start: {
          line: 14,
          column: 8
        },
        end: {
          line: 14,
          column: 29
        }
      },
      "5": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 47
        }
      },
      "6": {
        start: {
          line: 18,
          column: 8
        },
        end: {
          line: 18,
          column: 29
        }
      },
      "7": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 22,
          column: 19
        }
      },
      "8": {
        start: {
          line: 25,
          column: 2
        },
        end: {
          line: 33,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "SignOutPage",
        decl: {
          start: {
            line: 7,
            column: 24
          },
          end: {
            line: 7,
            column: 35
          }
        },
        loc: {
          start: {
            line: 7,
            column: 38
          },
          end: {
            line: 34,
            column: 1
          }
        },
        line: 7
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 10,
            column: 12
          },
          end: {
            line: 10,
            column: 13
          }
        },
        loc: {
          start: {
            line: 10,
            column: 18
          },
          end: {
            line: 23,
            column: 3
          }
        },
        line: 10
      },
      "2": {
        name: "handleSignOut",
        decl: {
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 32
          }
        },
        loc: {
          start: {
            line: 11,
            column: 35
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 11
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    b: {},
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "aa98941a236c1cadfbdebe6be84525018e09093a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1zmgs245lc = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1zmgs245lc();
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { signOut } from '@/lib/auth';
export default function SignOutPage() {
  /* istanbul ignore next */
  cov_1zmgs245lc().f[0]++;
  const router =
  /* istanbul ignore next */
  (cov_1zmgs245lc().s[0]++, useRouter());
  /* istanbul ignore next */
  cov_1zmgs245lc().s[1]++;
  useEffect(() => {
    /* istanbul ignore next */
    cov_1zmgs245lc().f[1]++;
    async function handleSignOut() {
      /* istanbul ignore next */
      cov_1zmgs245lc().f[2]++;
      cov_1zmgs245lc().s[2]++;
      try {
        /* istanbul ignore next */
        cov_1zmgs245lc().s[3]++;
        await signOut();
        /* istanbul ignore next */
        cov_1zmgs245lc().s[4]++;
        router.push('/login');
      } catch (error) {
        /* istanbul ignore next */
        cov_1zmgs245lc().s[5]++;
        console.error('Sign out error:', error);
        // Still redirect to login
        /* istanbul ignore next */
        cov_1zmgs245lc().s[6]++;
        router.push('/login');
      }
    }
    /* istanbul ignore next */
    cov_1zmgs245lc().s[7]++;
    handleSignOut();
  }, [router]);
  /* istanbul ignore next */
  cov_1zmgs245lc().s[8]++;
  return /* istanbul ignore next */__jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "flex items-center justify-center min-h-screen",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 26,
      columnNumber: 5
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "text-center",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 27,
      columnNumber: 7
    }
  },
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "h1",
  /* istanbul ignore next */
  {
    className: "text-2xl font-bold mb-4",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 28,
      columnNumber: 9
    }
  }, "Signing Out"),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "p",
  /* istanbul ignore next */
  {
    className: "mb-4",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 29,
      columnNumber: 9
    }
  }, "Please wait while we sign you out..."),
  /* istanbul ignore next */
  __jsx(
  /* istanbul ignore next */
  "div",
  /* istanbul ignore next */
  {
    className: "w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin mx-auto",
    __self: this,
    __source: {
      fileName: _jsxFileName,
      lineNumber: 30,
      columnNumber: 9
    }
  })));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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