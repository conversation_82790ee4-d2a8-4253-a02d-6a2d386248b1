{"version": 3, "names": ["cov_uj9oms7rd", "actualCoverage", "STSClient", "AssumeRoleCommand", "SSMClient", "GetParameterCommand", "getCredentials", "f", "s", "process", "env", "NODE_ENV", "b", "accessKeyId", "AWS_ACCESS_KEY_ID", "secretAccessKey", "AWS_SECRET_ACCESS_KEY", "region", "NEXT_PUBLIC_AWS_REGION", "stsClient", "command", "RoleArn", "PARAMETER_STORE_ROLE_ARN", "RoleSessionName", "DurationSeconds", "response", "send", "Credentials", "AccessKeyId", "SecretAccess<PERSON>ey", "sessionToken", "SessionToken", "getParameter", "paramName", "credentials", "ssmClient", "Name", "WithDecryption", "Parameter", "Value"], "sources": ["aws-config.ts"], "sourcesContent": ["import { STSClient, AssumeRoleCommand } from \"@aws-sdk/client-sts\";\nimport { SSMClient, GetParameterCommand } from \"@aws-sdk/client-ssm\";\n\n// Function to get temporary credentials by assuming a role\nasync function getCredentials() {\n  if (process.env.NODE_ENV !== 'production') {\n    return {\n      accessKeyId: process.env.AWS_ACCESS_KEY_ID,\n      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,\n      region: process.env.NEXT_PUBLIC_AWS_REGION\n    };\n  }\n\n  const stsClient = new STSClient({ region: process.env.NEXT_PUBLIC_AWS_REGION });\n  \n  const command = new AssumeRoleCommand({\n    RoleArn: process.env.PARAMETER_STORE_ROLE_ARN,\n    RoleSessionName: 'RenewTrackParameterStoreAccess',\n    DurationSeconds: 900 // 15 minutes\n  });\n  \n  const response = await stsClient.send(command);\n  \n  return {\n    accessKeyId: response.Credentials?.AccessKeyId,\n    secretAccessKey: response.Credentials?.SecretAccessKey,\n    sessionToken: response.Credentials?.SessionToken,\n    region: process.env.NEXT_PUBLIC_AWS_REGION\n  };\n}\n\n// Get parameter from SSM Parameter Store with assumed role\nexport async function getParameter(paramName: string) {\n  const credentials = await getCredentials();\n  \n  const ssmClient = new SSMClient({\n    region: process.env.NEXT_PUBLIC_AWS_REGION || 'ca-central-1',\n    credentials: {\n      accessKeyId: credentials.accessKeyId || '',\n      secretAccessKey: credentials.secretAccessKey || '',\n      sessionToken: credentials.sessionToken\n    }\n  });\n  \n  const command = new GetParameterCommand({\n    Name: paramName,\n    WithDecryption: true\n  });\n  \n  const response = await ssmClient.send(command);\n  return response.Parameter?.Value;\n}\n\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAfZ,SAASE,SAAS,EAAEC,iBAAiB,QAAQ,qBAAqB;AAClE,SAASC,SAAS,EAAEC,mBAAmB,QAAQ,qBAAqB;;AAEpE;AACA,eAAeC,cAAcA,CAAA,EAAG;EAAA;EAAAN,aAAA,GAAAO,CAAA;EAAAP,aAAA,GAAAQ,CAAA;EAC9B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IAAA;IAAAX,aAAA,GAAAY,CAAA;IAAAZ,aAAA,GAAAQ,CAAA;IACzC,OAAO;MACLK,WAAW,EAAEJ,OAAO,CAACC,GAAG,CAACI,iBAAiB;MAC1CC,eAAe,EAAEN,OAAO,CAACC,GAAG,CAACM,qBAAqB;MAClDC,MAAM,EAAER,OAAO,CAACC,GAAG,CAACQ;IACtB,CAAC;EACH,CAAC;EAAA;EAAA;IAAAlB,aAAA,GAAAY,CAAA;EAAA;EAED,MAAMO,SAAS;EAAA;EAAA,CAAAnB,aAAA,GAAAQ,CAAA,OAAG,IAAIN,SAAS,CAAC;IAAEe,MAAM,EAAER,OAAO,CAACC,GAAG,CAACQ;EAAuB,CAAC,CAAC;EAE/E,MAAME,OAAO;EAAA;EAAA,CAAApB,aAAA,GAAAQ,CAAA,OAAG,IAAIL,iBAAiB,CAAC;IACpCkB,OAAO,EAAEZ,OAAO,CAACC,GAAG,CAACY,wBAAwB;IAC7CC,eAAe,EAAE,gCAAgC;IACjDC,eAAe,EAAE,GAAG,CAAC;EACvB,CAAC,CAAC;EAEF,MAAMC,QAAQ;EAAA;EAAA,CAAAzB,aAAA,GAAAQ,CAAA,OAAG,MAAMW,SAAS,CAACO,IAAI,CAACN,OAAO,CAAC;EAAC;EAAApB,aAAA,GAAAQ,CAAA;EAE/C,OAAO;IACLK,WAAW,EAAEY,QAAQ,CAACE,WAAW,EAAEC,WAAW;IAC9Cb,eAAe,EAAEU,QAAQ,CAACE,WAAW,EAAEE,eAAe;IACtDC,YAAY,EAAEL,QAAQ,CAACE,WAAW,EAAEI,YAAY;IAChDd,MAAM,EAAER,OAAO,CAACC,GAAG,CAACQ;EACtB,CAAC;AACH;;AAEA;AACA,OAAO,eAAec,YAAYA,CAACC,SAAiB,EAAE;EAAA;EAAAjC,aAAA,GAAAO,CAAA;EACpD,MAAM2B,WAAW;EAAA;EAAA,CAAAlC,aAAA,GAAAQ,CAAA,OAAG,MAAMF,cAAc,CAAC,CAAC;EAE1C,MAAM6B,SAAS;EAAA;EAAA,CAAAnC,aAAA,GAAAQ,CAAA,OAAG,IAAIJ,SAAS,CAAC;IAC9Ba,MAAM;IAAE;IAAA,CAAAjB,aAAA,GAAAY,CAAA,UAAAH,OAAO,CAACC,GAAG,CAACQ,sBAAsB;IAAA;IAAA,CAAAlB,aAAA,GAAAY,CAAA,UAAI,cAAc;IAC5DsB,WAAW,EAAE;MACXrB,WAAW;MAAE;MAAA,CAAAb,aAAA,GAAAY,CAAA,UAAAsB,WAAW,CAACrB,WAAW;MAAA;MAAA,CAAAb,aAAA,GAAAY,CAAA,UAAI,EAAE;MAC1CG,eAAe;MAAE;MAAA,CAAAf,aAAA,GAAAY,CAAA,UAAAsB,WAAW,CAACnB,eAAe;MAAA;MAAA,CAAAf,aAAA,GAAAY,CAAA,UAAI,EAAE;MAClDkB,YAAY,EAAEI,WAAW,CAACJ;IAC5B;EACF,CAAC,CAAC;EAEF,MAAMV,OAAO;EAAA;EAAA,CAAApB,aAAA,GAAAQ,CAAA,OAAG,IAAIH,mBAAmB,CAAC;IACtC+B,IAAI,EAAEH,SAAS;IACfI,cAAc,EAAE;EAClB,CAAC,CAAC;EAEF,MAAMZ,QAAQ;EAAA;EAAA,CAAAzB,aAAA,GAAAQ,CAAA,OAAG,MAAM2B,SAAS,CAACT,IAAI,CAACN,OAAO,CAAC;EAAC;EAAApB,aAAA,GAAAQ,CAAA;EAC/C,OAAOiB,QAAQ,CAACa,SAAS,EAAEC,KAAK;AAClC", "ignoreList": []}