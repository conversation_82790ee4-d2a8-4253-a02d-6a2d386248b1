{"version": 3, "names": ["cov_1emzugtcs7", "actualCoverage", "isUser", "obj", "f", "s", "b", "id", "email", "Array", "isArray", "roles", "includes", "status", "isClient", "name", "domain", "isTenantContext", "clientId", "clientName", "tenantId", "tenantSchema", "domains", "isActive", "isRenewal", "vendor", "isApiResponse", "success", "timestamp", "isPaginatedResponse", "pagination", "page", "limit", "totalCount", "isAuthSession", "isAuth", "userId", "isCognitoJwtPayload", "sub", "exp", "iat", "token_use"], "sources": ["types.ts"], "sourcesContent": ["/**\n * Comprehensive Type Definitions and Type Guards\n * \n * This module provides centralized type definitions and type guard functions\n * to ensure type safety throughout the application.\n */\n\n// Base entity interface\nexport interface BaseEntity {\n  id: string;\n  created_at: Date;\n  updated_at?: Date;\n  version?: number; // For optimistic concurrency control\n}\n\n// User types\nexport interface User extends BaseEntity {\n  email: string;\n  name?: string;\n  given_name?: string;\n  family_name?: string;\n  roles: string[];\n  status: 'active' | 'inactive' | 'suspended' | 'deleted';\n  last_login?: Date;\n  preferences?: UserPreferences;\n}\n\nexport interface UserPreferences {\n  theme: 'light' | 'dark' | 'system';\n  notifications: {\n    email: boolean;\n    push: boolean;\n    sms: boolean;\n  };\n  displayDensity: 'comfortable' | 'compact';\n  language?: string;\n  timezone?: string;\n  [key: string]: any;\n}\n\n// Client/Tenant types\nexport interface Client extends BaseEntity {\n  name: string;\n  domain: string;\n  domains?: string[];\n  status: 'active' | 'inactive' | 'suspended';\n  settings: ClientSettings;\n  industry_id?: string;\n  tenant_id?: string;\n  tenant_schema?: string;\n}\n\nexport interface ClientSettings {\n  schemaReady?: boolean;\n  features?: {\n    renewals?: boolean;\n    vendors?: boolean;\n    reports?: boolean;\n    notifications?: boolean;\n  };\n  branding?: {\n    logo?: string;\n    primaryColor?: string;\n    secondaryColor?: string;\n  };\n  [key: string]: any;\n}\n\nexport interface TenantContext {\n  clientId: string;\n  clientName: string;\n  tenantId: string;\n  tenantSchema: string;\n  domains: string[];\n  isActive: boolean;\n  settings: ClientSettings;\n  createdAt: Date;\n  updatedAt?: Date | null;\n}\n\n// Renewal types\nexport interface Renewal extends BaseEntity {\n  name: string;\n  vendor: string;\n  vendor_id?: string;\n  status: 'active' | 'inactive' | 'pending' | 'expired';\n  due_date?: Date;\n  annual_cost?: number;\n  description?: string;\n  category?: string;\n  priority?: 'low' | 'medium' | 'high' | 'critical';\n  auto_renew?: boolean;\n  notification_days?: number[];\n}\n\n// Vendor types\nexport interface Vendor extends BaseEntity {\n  name: string;\n  contact_email?: string;\n  contact_phone?: string;\n  website?: string;\n  status: 'active' | 'inactive';\n  address?: Address;\n  notes?: string;\n}\n\nexport interface Address {\n  street?: string;\n  city?: string;\n  state?: string;\n  postal_code?: string;\n  country?: string;\n}\n\n// API Response types\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  errorCode?: string;\n  message?: string;\n  timestamp: string;\n}\n\nexport interface PaginatedResponse<T> extends ApiResponse<T[]> {\n  pagination: {\n    page: number;\n    limit: number;\n    totalCount: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n}\n\n// Authentication types\nexport interface AuthSession {\n  isAuth: boolean;\n  userId: string;\n  email: string;\n  roles: string[];\n}\n\nexport interface CognitoJwtPayload {\n  sub: string;\n  email: string;\n  email_verified?: boolean;\n  name?: string;\n  given_name?: string;\n  family_name?: string;\n  'cognito:groups'?: string[];\n  'cognito:username'?: string;\n  token_use: 'id' | 'access';\n  auth_time: number;\n  iat: number;\n  exp: number;\n  aud: string;\n  iss: string;\n}\n\n// Database types\nexport interface DbResult<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  errorCode?: string;\n  rowCount?: number;\n}\n\nexport interface QueryOptions {\n  timeout?: number;\n  retries?: number;\n  schema?: string;\n}\n\n// Form types\nexport interface FormField {\n  name: string;\n  label: string;\n  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'select' | 'textarea' | 'checkbox';\n  required?: boolean;\n  placeholder?: string;\n  options?: { value: string; label: string }[];\n  validation?: {\n    min?: number;\n    max?: number;\n    pattern?: string;\n    custom?: (value: any) => string | null;\n  };\n}\n\nexport interface FormState<T = any> {\n  values: T;\n  errors: Record<string, string>;\n  touched: Record<string, boolean>;\n  isSubmitting: boolean;\n  isValid: boolean;\n}\n\n// Component prop types\nexport interface BaseComponentProps {\n  className?: string;\n  children?: React.ReactNode;\n  'data-testid'?: string;\n}\n\nexport interface LoadingState {\n  isLoading: boolean;\n  error?: string | null;\n}\n\n// Type Guards\nexport function isUser(obj: any): obj is User {\n  return (\n    obj &&\n    typeof obj === 'object' &&\n    typeof obj.id === 'string' &&\n    typeof obj.email === 'string' &&\n    Array.isArray(obj.roles) &&\n    ['active', 'inactive', 'suspended', 'deleted'].includes(obj.status)\n  );\n}\n\nexport function isClient(obj: any): obj is Client {\n  return (\n    obj &&\n    typeof obj === 'object' &&\n    typeof obj.id === 'string' &&\n    typeof obj.name === 'string' &&\n    typeof obj.domain === 'string' &&\n    ['active', 'inactive', 'suspended'].includes(obj.status)\n  );\n}\n\nexport function isTenantContext(obj: any): obj is TenantContext {\n  return (\n    obj &&\n    typeof obj === 'object' &&\n    typeof obj.clientId === 'string' &&\n    typeof obj.clientName === 'string' &&\n    typeof obj.tenantId === 'string' &&\n    typeof obj.tenantSchema === 'string' &&\n    Array.isArray(obj.domains) &&\n    typeof obj.isActive === 'boolean'\n  );\n}\n\nexport function isRenewal(obj: any): obj is Renewal {\n  return (\n    obj &&\n    typeof obj === 'object' &&\n    typeof obj.id === 'string' &&\n    typeof obj.name === 'string' &&\n    typeof obj.vendor === 'string' &&\n    ['active', 'inactive', 'pending', 'expired'].includes(obj.status)\n  );\n}\n\nexport function isApiResponse<T>(obj: any): obj is ApiResponse<T> {\n  return (\n    obj &&\n    typeof obj === 'object' &&\n    typeof obj.success === 'boolean' &&\n    typeof obj.timestamp === 'string'\n  );\n}\n\nexport function isPaginatedResponse<T>(obj: any): obj is PaginatedResponse<T> {\n  return (\n    isApiResponse(obj) &&\n    obj.pagination &&\n    typeof obj.pagination === 'object' &&\n    typeof obj.pagination.page === 'number' &&\n    typeof obj.pagination.limit === 'number' &&\n    typeof obj.pagination.totalCount === 'number'\n  );\n}\n\nexport function isAuthSession(obj: any): obj is AuthSession {\n  return (\n    obj &&\n    typeof obj === 'object' &&\n    typeof obj.isAuth === 'boolean' &&\n    typeof obj.userId === 'string' &&\n    typeof obj.email === 'string' &&\n    Array.isArray(obj.roles)\n  );\n}\n\nexport function isCognitoJwtPayload(obj: any): obj is CognitoJwtPayload {\n  return (\n    obj &&\n    typeof obj === 'object' &&\n    typeof obj.sub === 'string' &&\n    typeof obj.email === 'string' &&\n    typeof obj.exp === 'number' &&\n    typeof obj.iat === 'number' &&\n    ['id', 'access'].includes(obj.token_use)\n  );\n}\n\n// Utility types\nexport type Nullable<T> = T | null;\nexport type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;\nexport type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;\nexport type DeepPartial<T> = {\n  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];\n};\n\n// Event types\nexport interface AppEvent<T = any> {\n  type: string;\n  payload: T;\n  timestamp: Date;\n  userId?: string;\n  sessionId?: string;\n}\n\n// Error types\nexport interface AppError extends Error {\n  code?: string;\n  statusCode?: number;\n  details?: any;\n  timestamp: Date;\n}\n\n// Configuration types\nexport interface AppConfig {\n  aws: {\n    region: string;\n    userPoolId: string;\n    userPoolClientId: string;\n    cognitoDomain: string;\n  };\n  auth: {\n    redirectSignIn: string;\n    redirectSignOut: string;\n  };\n  app: {\n    environment: 'development' | 'production' | 'test';\n    isDevelopment: boolean;\n    isProduction: boolean;\n  };\n}\n\n// Dashboard specific types\nexport interface DashboardStats {\n  totalRenewals: number;\n  renewalsDue: number;\n  vendors: number;\n  annualSpend: string;\n}\n\n// Export all types for easy importing\nexport type {\n  BaseEntity,\n  User,\n  UserPreferences,\n  Client,\n  ClientSettings,\n  TenantContext,\n  Renewal,\n  Vendor,\n  Address,\n  ApiResponse,\n  PaginatedResponse,\n  AuthSession,\n  CognitoJwtPayload,\n  DbResult,\n  QueryOptions,\n  FormField,\n  FormState,\n  BaseComponentProps,\n  LoadingState,\n  AppEvent,\n  AppError,\n  AppConfig,\n  DashboardStats,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAfZ;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAQA;;AAyBA;;AAwCA;;AAeA;;AAmBA;;AAqBA;;AAyBA;;AAeA;;AAwBA;;AAYA;AACA,OAAO,SAASE,MAAMA,CAACC,GAAQ,EAAe;EAAA;EAAAH,cAAA,GAAAI,CAAA;EAAAJ,cAAA,GAAAK,CAAA;EAC5C,OACE,2BAAAL,cAAA,GAAAM,CAAA,UAAAH,GAAG;EAAA;EAAA,CAAAH,cAAA,GAAAM,CAAA,UACH,OAAOH,GAAG,KAAK,QAAQ;EAAA;EAAA,CAAAH,cAAA,GAAAM,CAAA,UACvB,OAAOH,GAAG,CAACI,EAAE,KAAK,QAAQ;EAAA;EAAA,CAAAP,cAAA,GAAAM,CAAA,UAC1B,OAAOH,GAAG,CAACK,KAAK,KAAK,QAAQ;EAAA;EAAA,CAAAR,cAAA,GAAAM,CAAA,UAC7BG,KAAK,CAACC,OAAO,CAACP,GAAG,CAACQ,KAAK,CAAC;EAAA;EAAA,CAAAX,cAAA,GAAAM,CAAA,UACxB,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,CAAC,CAACM,QAAQ,CAACT,GAAG,CAACU,MAAM,CAAC;AAEvE;AAEA,OAAO,SAASC,QAAQA,CAACX,GAAQ,EAAiB;EAAA;EAAAH,cAAA,GAAAI,CAAA;EAAAJ,cAAA,GAAAK,CAAA;EAChD,OACE,2BAAAL,cAAA,GAAAM,CAAA,UAAAH,GAAG;EAAA;EAAA,CAAAH,cAAA,GAAAM,CAAA,UACH,OAAOH,GAAG,KAAK,QAAQ;EAAA;EAAA,CAAAH,cAAA,GAAAM,CAAA,UACvB,OAAOH,GAAG,CAACI,EAAE,KAAK,QAAQ;EAAA;EAAA,CAAAP,cAAA,GAAAM,CAAA,UAC1B,OAAOH,GAAG,CAACY,IAAI,KAAK,QAAQ;EAAA;EAAA,CAAAf,cAAA,GAAAM,CAAA,UAC5B,OAAOH,GAAG,CAACa,MAAM,KAAK,QAAQ;EAAA;EAAA,CAAAhB,cAAA,GAAAM,CAAA,UAC9B,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC,CAACM,QAAQ,CAACT,GAAG,CAACU,MAAM,CAAC;AAE5D;AAEA,OAAO,SAASI,eAAeA,CAACd,GAAQ,EAAwB;EAAA;EAAAH,cAAA,GAAAI,CAAA;EAAAJ,cAAA,GAAAK,CAAA;EAC9D,OACE,2BAAAL,cAAA,GAAAM,CAAA,UAAAH,GAAG;EAAA;EAAA,CAAAH,cAAA,GAAAM,CAAA,UACH,OAAOH,GAAG,KAAK,QAAQ;EAAA;EAAA,CAAAH,cAAA,GAAAM,CAAA,UACvB,OAAOH,GAAG,CAACe,QAAQ,KAAK,QAAQ;EAAA;EAAA,CAAAlB,cAAA,GAAAM,CAAA,UAChC,OAAOH,GAAG,CAACgB,UAAU,KAAK,QAAQ;EAAA;EAAA,CAAAnB,cAAA,GAAAM,CAAA,UAClC,OAAOH,GAAG,CAACiB,QAAQ,KAAK,QAAQ;EAAA;EAAA,CAAApB,cAAA,GAAAM,CAAA,UAChC,OAAOH,GAAG,CAACkB,YAAY,KAAK,QAAQ;EAAA;EAAA,CAAArB,cAAA,GAAAM,CAAA,UACpCG,KAAK,CAACC,OAAO,CAACP,GAAG,CAACmB,OAAO,CAAC;EAAA;EAAA,CAAAtB,cAAA,GAAAM,CAAA,UAC1B,OAAOH,GAAG,CAACoB,QAAQ,KAAK,SAAS;AAErC;AAEA,OAAO,SAASC,SAASA,CAACrB,GAAQ,EAAkB;EAAA;EAAAH,cAAA,GAAAI,CAAA;EAAAJ,cAAA,GAAAK,CAAA;EAClD,OACE,2BAAAL,cAAA,GAAAM,CAAA,UAAAH,GAAG;EAAA;EAAA,CAAAH,cAAA,GAAAM,CAAA,UACH,OAAOH,GAAG,KAAK,QAAQ;EAAA;EAAA,CAAAH,cAAA,GAAAM,CAAA,UACvB,OAAOH,GAAG,CAACI,EAAE,KAAK,QAAQ;EAAA;EAAA,CAAAP,cAAA,GAAAM,CAAA,UAC1B,OAAOH,GAAG,CAACY,IAAI,KAAK,QAAQ;EAAA;EAAA,CAAAf,cAAA,GAAAM,CAAA,UAC5B,OAAOH,GAAG,CAACsB,MAAM,KAAK,QAAQ;EAAA;EAAA,CAAAzB,cAAA,GAAAM,CAAA,UAC9B,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAACM,QAAQ,CAACT,GAAG,CAACU,MAAM,CAAC;AAErE;AAEA,OAAO,SAASa,aAAaA,CAAIvB,GAAQ,EAAyB;EAAA;EAAAH,cAAA,GAAAI,CAAA;EAAAJ,cAAA,GAAAK,CAAA;EAChE,OACE,2BAAAL,cAAA,GAAAM,CAAA,UAAAH,GAAG;EAAA;EAAA,CAAAH,cAAA,GAAAM,CAAA,UACH,OAAOH,GAAG,KAAK,QAAQ;EAAA;EAAA,CAAAH,cAAA,GAAAM,CAAA,UACvB,OAAOH,GAAG,CAACwB,OAAO,KAAK,SAAS;EAAA;EAAA,CAAA3B,cAAA,GAAAM,CAAA,UAChC,OAAOH,GAAG,CAACyB,SAAS,KAAK,QAAQ;AAErC;AAEA,OAAO,SAASC,mBAAmBA,CAAI1B,GAAQ,EAA+B;EAAA;EAAAH,cAAA,GAAAI,CAAA;EAAAJ,cAAA,GAAAK,CAAA;EAC5E,OACE,2BAAAL,cAAA,GAAAM,CAAA,UAAAoB,aAAa,CAACvB,GAAG,CAAC;EAAA;EAAA,CAAAH,cAAA,GAAAM,CAAA,UAClBH,GAAG,CAAC2B,UAAU;EAAA;EAAA,CAAA9B,cAAA,GAAAM,CAAA,UACd,OAAOH,GAAG,CAAC2B,UAAU,KAAK,QAAQ;EAAA;EAAA,CAAA9B,cAAA,GAAAM,CAAA,UAClC,OAAOH,GAAG,CAAC2B,UAAU,CAACC,IAAI,KAAK,QAAQ;EAAA;EAAA,CAAA/B,cAAA,GAAAM,CAAA,UACvC,OAAOH,GAAG,CAAC2B,UAAU,CAACE,KAAK,KAAK,QAAQ;EAAA;EAAA,CAAAhC,cAAA,GAAAM,CAAA,UACxC,OAAOH,GAAG,CAAC2B,UAAU,CAACG,UAAU,KAAK,QAAQ;AAEjD;AAEA,OAAO,SAASC,aAAaA,CAAC/B,GAAQ,EAAsB;EAAA;EAAAH,cAAA,GAAAI,CAAA;EAAAJ,cAAA,GAAAK,CAAA;EAC1D,OACE,2BAAAL,cAAA,GAAAM,CAAA,UAAAH,GAAG;EAAA;EAAA,CAAAH,cAAA,GAAAM,CAAA,UACH,OAAOH,GAAG,KAAK,QAAQ;EAAA;EAAA,CAAAH,cAAA,GAAAM,CAAA,UACvB,OAAOH,GAAG,CAACgC,MAAM,KAAK,SAAS;EAAA;EAAA,CAAAnC,cAAA,GAAAM,CAAA,UAC/B,OAAOH,GAAG,CAACiC,MAAM,KAAK,QAAQ;EAAA;EAAA,CAAApC,cAAA,GAAAM,CAAA,UAC9B,OAAOH,GAAG,CAACK,KAAK,KAAK,QAAQ;EAAA;EAAA,CAAAR,cAAA,GAAAM,CAAA,UAC7BG,KAAK,CAACC,OAAO,CAACP,GAAG,CAACQ,KAAK,CAAC;AAE5B;AAEA,OAAO,SAAS0B,mBAAmBA,CAAClC,GAAQ,EAA4B;EAAA;EAAAH,cAAA,GAAAI,CAAA;EAAAJ,cAAA,GAAAK,CAAA;EACtE,OACE,2BAAAL,cAAA,GAAAM,CAAA,UAAAH,GAAG;EAAA;EAAA,CAAAH,cAAA,GAAAM,CAAA,UACH,OAAOH,GAAG,KAAK,QAAQ;EAAA;EAAA,CAAAH,cAAA,GAAAM,CAAA,UACvB,OAAOH,GAAG,CAACmC,GAAG,KAAK,QAAQ;EAAA;EAAA,CAAAtC,cAAA,GAAAM,CAAA,UAC3B,OAAOH,GAAG,CAACK,KAAK,KAAK,QAAQ;EAAA;EAAA,CAAAR,cAAA,GAAAM,CAAA,UAC7B,OAAOH,GAAG,CAACoC,GAAG,KAAK,QAAQ;EAAA;EAAA,CAAAvC,cAAA,GAAAM,CAAA,UAC3B,OAAOH,GAAG,CAACqC,GAAG,KAAK,QAAQ;EAAA;EAAA,CAAAxC,cAAA,GAAAM,CAAA,UAC3B,CAAC,IAAI,EAAE,QAAQ,CAAC,CAACM,QAAQ,CAACT,GAAG,CAACsC,SAAS,CAAC;AAE5C;;AAEA;;AAQA;;AASA;;AAQA;;AAmBA;;AAQA", "ignoreList": []}