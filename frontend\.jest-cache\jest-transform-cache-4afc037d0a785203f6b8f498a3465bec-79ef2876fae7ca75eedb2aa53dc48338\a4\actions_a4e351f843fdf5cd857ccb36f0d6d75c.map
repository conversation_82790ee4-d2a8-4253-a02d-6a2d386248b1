{"version": 3, "names": ["cov_1re1rugbkv", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "_coverageSchema", "coverage", "actualCoverage", "verifySession", "hasRole", "cookies", "verifyCSRFToken", "formData", "cookieStore", "storedToken", "get", "value", "formToken", "console", "error", "hasStoredToken", "hasFormToken", "Error", "generateCSRFToken", "token", "crypto", "randomBytes", "toString", "set", "httpOnly", "secure", "process", "env", "NODE_ENV", "sameSite", "maxAge", "updateUserProfile", "session", "String", "length", "success"], "sources": ["actions.ts"], "sourcesContent": ["'use server'\n\nimport { verifySession, hasRole } from './dal'\nimport { cookies } from 'next/headers'\n\n// ISSUE: CSRF token implementation could be strengthened\n// RECOMMENDATION: Use double submit cookie pattern with secure, httpOnly flags\n\nconst verifyCSRFToken = async (formData: FormData) => {\n  const cookieStore = cookies();\n  const storedToken = cookieStore.get('csrf-token')?.value;\n  const formToken = formData.get('csrf-token') as string;\n  \n  if (!storedToken || !formToken || storedToken !== formToken) {\n    // RECOMMENDATION: Add logging for security events\n    console.error('CSRF token validation failed', {\n      hasStoredToken: !!storedToken,\n      hasFormToken: !!formToken,\n      // Don't log the actual tokens for security reasons\n    });\n    throw new Error('CSRF token validation failed');\n  }\n  \n  return true;\n}\n\n// RECOMMENDATION: Add a function to generate CSRF tokens\nexport function generateCSRFToken() {\n  const token = crypto.randomBytes(32).toString('hex');\n  \n  // Set the cookie with proper security flags\n  cookies().set('csrf-token', token, {\n    httpOnly: true,\n    secure: process.env.NODE_ENV === 'production',\n    sameSite: 'strict',\n    path: '/',\n    maxAge: 3600 // 1 hour\n  });\n  \n  return token;\n}\n\n// Example of a secure server action\nexport async function updateUserProfile(formData: FormData) {\n  // 1. Verify CSRF token\n  await verifyCSRFToken(formData)\n  \n  // 2. Verify user session\n  const session = await verifySession()\n  if (!session) {\n    throw new Error('Unauthorized')\n  }\n  \n  // 3. Check permissions for this action\n  if (!hasRole(session, 'user')) {\n    throw new Error('Insufficient permissions')\n  }\n  \n  // 4. Sanitize and validate input data\n  const name = String(formData.get('name'))\n  if (!name || name.length < 2) {\n    throw new Error('Invalid name')\n  }\n  \n  // 5. Perform the action\n  try {\n    // Update user profile logic here\n    return { success: true }\n  } catch (error) {\n    console.error('Profile update error:', error)\n    throw new Error('Failed to update profile')\n  }\n}\n\n"], "mappings": "AAAA,YAAY;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,eAAA;IAAArB,IAAA;EAAA;EAAA,IAAAsB,QAAA,GAAArB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAmB,QAAA,CAAAvB,IAAA,KAAAuB,QAAA,CAAAvB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAsB,QAAA,CAAAvB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAmB,cAAA,GAAAD,QAAA,CAAAvB,IAAA;EAAA;IAeA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAyB,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAzB,cAAA;AAbZ,SAAS0B,aAAa,EAAEC,OAAO,QAAQ,OAAO;AAC9C,SAASC,OAAO,QAAQ,cAAc;;AAEtC;AACA;AAAA;AAAA5B,cAAA,GAAAoB,CAAA;AAEA,MAAMS,eAAe,GAAG,MAAOC,QAAkB,IAAK;EAAA;EAAA9B,cAAA,GAAAqB,CAAA;EACpD,MAAMU,WAAW;EAAA;EAAA,CAAA/B,cAAA,GAAAoB,CAAA,OAAGQ,OAAO,CAAC,CAAC;EAC7B,MAAMI,WAAW;EAAA;EAAA,CAAAhC,cAAA,GAAAoB,CAAA,OAAGW,WAAW,CAACE,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK;EACxD,MAAMC,SAAS;EAAA;EAAA,CAAAnC,cAAA,GAAAoB,CAAA,OAAGU,QAAQ,CAACG,GAAG,CAAC,YAAY,CAAC,CAAU;EAAC;EAAAjC,cAAA,GAAAoB,CAAA;EAEvD;EAAI;EAAA,CAAApB,cAAA,GAAAsB,CAAA,WAACU,WAAW;EAAA;EAAA,CAAAhC,cAAA,GAAAsB,CAAA,UAAI,CAACa,SAAS;EAAA;EAAA,CAAAnC,cAAA,GAAAsB,CAAA,UAAIU,WAAW,KAAKG,SAAS,GAAE;IAAA;IAAAnC,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC3D;IACAgB,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAE;MAC5CC,cAAc,EAAE,CAAC,CAACN,WAAW;MAC7BO,YAAY,EAAE,CAAC,CAACJ;MAChB;IACF,CAAC,CAAC;IAAC;IAAAnC,cAAA,GAAAoB,CAAA;IACH,MAAM,IAAIoB,KAAK,CAAC,8BAA8B,CAAC;EACjD,CAAC;EAAA;EAAA;IAAAxC,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EAED,OAAO,IAAI;AACb,CAAC;;AAED;AACA,OAAO,SAASqB,iBAAiBA,CAAA,EAAG;EAAA;EAAAzC,cAAA,GAAAqB,CAAA;EAClC,MAAMqB,KAAK;EAAA;EAAA,CAAA1C,cAAA,GAAAoB,CAAA,OAAGuB,MAAM,CAACC,WAAW,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC;;EAEpD;EAAA;EAAA7C,cAAA,GAAAoB,CAAA;EACAQ,OAAO,CAAC,CAAC,CAACkB,GAAG,CAAC,YAAY,EAAEJ,KAAK,EAAE;IACjCK,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;IAC7CC,QAAQ,EAAE,QAAQ;IAClBnD,IAAI,EAAE,GAAG;IACToD,MAAM,EAAE,IAAI,CAAC;EACf,CAAC,CAAC;EAAC;EAAArD,cAAA,GAAAoB,CAAA;EAEH,OAAOsB,KAAK;AACd;;AAEA;AACA,OAAO,eAAeY,iBAAiBA,CAACxB,QAAkB,EAAE;EAAA;EAAA9B,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EAC1D;EACA,MAAMS,eAAe,CAACC,QAAQ,CAAC;;EAE/B;EACA,MAAMyB,OAAO;EAAA;EAAA,CAAAvD,cAAA,GAAAoB,CAAA,QAAG,MAAMM,aAAa,CAAC,CAAC;EAAA;EAAA1B,cAAA,GAAAoB,CAAA;EACrC,IAAI,CAACmC,OAAO,EAAE;IAAA;IAAAvD,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IACZ,MAAM,IAAIoB,KAAK,CAAC,cAAc,CAAC;EACjC,CAAC;EAAA;EAAA;IAAAxC,cAAA,GAAAsB,CAAA;EAAA;;EAED;EAAAtB,cAAA,GAAAoB,CAAA;EACA,IAAI,CAACO,OAAO,CAAC4B,OAAO,EAAE,MAAM,CAAC,EAAE;IAAA;IAAAvD,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC7B,MAAM,IAAIoB,KAAK,CAAC,0BAA0B,CAAC;EAC7C,CAAC;EAAA;EAAA;IAAAxC,cAAA,GAAAsB,CAAA;EAAA;;EAED;EACA,MAAMT,IAAI;EAAA;EAAA,CAAAb,cAAA,GAAAoB,CAAA,QAAGoC,MAAM,CAAC1B,QAAQ,CAACG,GAAG,CAAC,MAAM,CAAC,CAAC;EAAA;EAAAjC,cAAA,GAAAoB,CAAA;EACzC;EAAI;EAAA,CAAApB,cAAA,GAAAsB,CAAA,WAACT,IAAI;EAAA;EAAA,CAAAb,cAAA,GAAAsB,CAAA,UAAIT,IAAI,CAAC4C,MAAM,GAAG,CAAC,GAAE;IAAA;IAAAzD,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAC5B,MAAM,IAAIoB,KAAK,CAAC,cAAc,CAAC;EACjC,CAAC;EAAA;EAAA;IAAAxC,cAAA,GAAAsB,CAAA;EAAA;;EAED;EAAAtB,cAAA,GAAAoB,CAAA;EACA,IAAI;IAAA;IAAApB,cAAA,GAAAoB,CAAA;IACF;IACA,OAAO;MAAEsC,OAAO,EAAE;IAAK,CAAC;EAC1B,CAAC,CAAC,OAAOrB,KAAK,EAAE;IAAA;IAAArC,cAAA,GAAAoB,CAAA;IACdgB,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAAA;IAAArC,cAAA,GAAAoB,CAAA;IAC7C,MAAM,IAAIoB,KAAK,CAAC,0BAA0B,CAAC;EAC7C;AACF", "ignoreList": []}