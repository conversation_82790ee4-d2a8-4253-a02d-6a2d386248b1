/**
 * Client-side configuration
 * This file provides environment variables that are safe to expose to the client
 */

// These values are injected at build time by Next.js
export const clientConfig = {
  aws: {
    region: process.env.NEXT_PUBLIC_AWS_REGION!,
    userPoolId: process.env.NEXT_PUBLIC_AWS_USER_POOLS_ID!,
    userPoolClientId: process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID!,
    cognitoDomain: process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN!,
  },
  auth: {
    redirectSignIn: process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN!,
    redirectSignOut: process.env.NEXT_PUBLIC_REDIRECT_SIGN_OUT!,
  },
  app: {
    environment: process.env.NODE_ENV || 'development',
    isDevelopment: process.env.NODE_ENV === 'development',
    isProduction: process.env.NODE_ENV === 'production',
  },
} as const;

// Validation function for client config
export const validateClientConfig = () => {
  const requiredVars = [
    'NEXT_PUBLIC_AWS_REGION',
    'NEXT_PUBLIC_AWS_USER_POOLS_ID', 
    'NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID',
    'NEXT_PUBLIC_AWS_COGNITO_DOMAIN',
    'NEXT_PUBLIC_REDIRECT_SIGN_IN',
    'NEXT_PUBLIC_REDIRECT_SIGN_OUT',
  ];

  const missing = requiredVars.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    console.error('Missing required environment variables:', missing);
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  return true;
};

// Export individual getters for convenience
export const getAwsConfig = () => clientConfig.aws;
export const getAuthConfig = () => clientConfig.auth;
export const getAppConfig = () => clientConfig.app;
